# Generated by Django 2.2.28 on 2025-07-25 11:10

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('destinazioni', '0032_auto_20250725_1017'),
    ]

    operations = [
        migrations.AlterField(
            model_name='costoprevisto',
            name='importo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='progetto',
            name='controvalore_effettivo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='progetto',
            name='controvalore_previsto_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='progetto',
            name='importo_effettivo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='progetto',
            name='importo_previsto_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
    ]
