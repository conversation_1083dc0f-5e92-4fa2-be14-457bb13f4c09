import csv
import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils.dateparse import parse_date
from matthaeus.anagrafica.models import Benefattore, Tipologia, TipoComunicazione
from common.anagraficabase.models import Provincia, Stato


class Command(BaseCommand):
    help = 'Aggiorna i benefattori da un file CSV basandosi sul campo id'

    def add_arguments(self, parser):
        parser.add_argument(
            'csv_file',
            type=str,
            help='Percorso del file CSV da importare'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Esegue una simulazione senza salvare le modifiche'
        )

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        dry_run = options['dry_run']

        if not os.path.exists(csv_file):
            raise CommandError(f'Il file {csv_file} non esiste')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('MODALITÀ DRY-RUN: Nessuna modifica verrà salvata')
            )

        # Contatori per il report finale
        aggiornati = 0
        errori = 0
        non_trovati = 0

        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                # Verifica che il campo 'id' sia presente
                if 'id' not in reader.fieldnames:
                    raise CommandError('Il file CSV deve contenere una colonna "id"')

                with transaction.atomic():
                    for row_num, row in enumerate(reader, start=2):  # Start=2 perché la riga 1 è l'header
                        try:
                            benefattore_id = row.get('id', '').strip()
                            if not benefattore_id:
                                self.stdout.write(
                                    self.style.WARNING(f'Riga {row_num}: ID vuoto, saltata')
                                )
                                continue

                            # Cerca il benefattore
                            try:
                                benefattore = Benefattore.objects.get(id=benefattore_id)
                            except Benefattore.DoesNotExist:
                                self.stdout.write(
                                    self.style.WARNING(f'Riga {row_num}: Benefattore con ID {benefattore_id} non trovato')
                                )
                                non_trovati += 1
                                continue

                            # Aggiorna i campi
                            updated_fields = []
                            
                            # Campi semplici
                            simple_fields = [
                                'appellativo', 'ragione_sociale', 'indirizzo', 'citta',
                                'cap', 'telefono', 'fax', 'email', 'pec', 'partita_iva',
                                'codice_fiscale', 'note', 'codice_sdi', 'codice', 'presso', 'regione'
                            ]
                            
                            for field in simple_fields:
                                if field in row and row[field].strip():
                                    old_value = getattr(benefattore, field)
                                    new_value = row[field].strip()
                                    if old_value != new_value:
                                        setattr(benefattore, field, new_value)
                                        updated_fields.append(f'{field}: "{old_value}" -> "{new_value}"')

                            # Campo data_nascita
                            if 'data_nascita' in row and row['data_nascita'].strip():
                                try:
                                    new_date = parse_date(row['data_nascita'].strip())
                                    if new_date and benefattore.data_nascita != new_date:
                                        old_date = benefattore.data_nascita
                                        benefattore.data_nascita = new_date
                                        updated_fields.append(f'data_nascita: "{old_date}" -> "{new_date}"')
                                except ValueError:
                                    self.stdout.write(
                                        self.style.WARNING(f'Riga {row_num}: Formato data non valido per data_nascita: {row["data_nascita"]}')
                                    )

                            # Foreign Key: Provincia
                            if 'provincia' in row and row['provincia'].strip():
                                try:
                                    provincia = Provincia.objects.get(codice=row['provincia'].strip())
                                    if benefattore.provincia != provincia:
                                        old_provincia = benefattore.provincia
                                        benefattore.provincia = provincia
                                        updated_fields.append(f'provincia: "{old_provincia}" -> "{provincia}"')
                                except Provincia.DoesNotExist:
                                    self.stdout.write(
                                        self.style.WARNING(f'Riga {row_num}: Provincia con codice "{row["provincia"]}" non trovata')
                                    )

                            # Foreign Key: Stato
                            if 'stato' in row and row['stato'].strip():
                                try:
                                    stato = Stato.objects.get(descrizione=row['stato'].strip())
                                    if benefattore.stato != stato:
                                        old_stato = benefattore.stato
                                        benefattore.stato = stato
                                        updated_fields.append(f'stato: "{old_stato}" -> "{stato}"')
                                except Stato.DoesNotExist:
                                    self.stdout.write(
                                        self.style.WARNING(f'Riga {row_num}: Stato con descrizione "{row["stato"]}" non trovato')
                                    )

                            # Foreign Key: Tipologia
                            if 'tipologia' in row and row['tipologia'].strip():
                                try:
                                    tipologia = Tipologia.objects.get(nome=row['tipologia'].strip())
                                    if benefattore.tipologia != tipologia:
                                        old_tipologia = benefattore.tipologia
                                        benefattore.tipologia = tipologia
                                        updated_fields.append(f'tipologia: "{old_tipologia}" -> "{tipologia}"')
                                except Tipologia.DoesNotExist:
                                    self.stdout.write(
                                        self.style.WARNING(f'Riga {row_num}: Tipologia con nome "{row["tipologia"]}" non trovata')
                                    )

                            # Foreign Key: TipoComunicazione
                            if 'tipo_comunicazione' in row and row['tipo_comunicazione'].strip():
                                try:
                                    tipo_comunicazione = TipoComunicazione.objects.get(nome=row['tipo_comunicazione'].strip())
                                    if benefattore.tipo_comunicazione != tipo_comunicazione:
                                        old_tipo = benefattore.tipo_comunicazione
                                        benefattore.tipo_comunicazione = tipo_comunicazione
                                        updated_fields.append(f'tipo_comunicazione: "{old_tipo}" -> "{tipo_comunicazione}"')
                                except TipoComunicazione.DoesNotExist:
                                    self.stdout.write(
                                        self.style.WARNING(f'Riga {row_num}: TipoComunicazione con nome "{row["tipo_comunicazione"]}" non trovato')
                                    )

                            # Salva solo se ci sono modifiche
                            if updated_fields:
                                if not dry_run:
                                    benefattore.save()
                                
                                self.stdout.write(
                                    self.style.SUCCESS(f'Riga {row_num}: Benefattore ID {benefattore_id} aggiornato')
                                )
                                for field_change in updated_fields:
                                    self.stdout.write(f'  - {field_change}')
                                aggiornati += 1
                            else:
                                self.stdout.write(
                                    f'Riga {row_num}: Benefattore ID {benefattore_id} - nessuna modifica necessaria'
                                )

                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(f'Riga {row_num}: Errore durante l\'aggiornamento: {str(e)}')
                            )
                            errori += 1

                    # Se è dry-run, annulla la transazione
                    if dry_run:
                        transaction.set_rollback(True)

        except Exception as e:
            raise CommandError(f'Errore durante la lettura del file CSV: {str(e)}')

        # Report finale
        self.stdout.write('\n' + '='*50)
        self.stdout.write('REPORT FINALE:')
        self.stdout.write(f'Benefattori aggiornati: {aggiornati}')
        self.stdout.write(f'Benefattori non trovati: {non_trovati}')
        self.stdout.write(f'Errori: {errori}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nATTENZIONE: Modalità dry-run attiva - nessuna modifica è stata salvata')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nImportazione completata con successo!')
            )
