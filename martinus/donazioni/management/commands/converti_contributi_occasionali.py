import csv
from collections import defaultdict
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils.translation import gettext as _

from martinus.adesioni.models import Adesione
from martinus.destinazioni.models import TipologiaContributoOccasionale, TipologiaContributoPeriodico
from martinus.donazioni.models import Donazione


class Command(BaseCommand):
    help = 'Converte contributi occasionali in contributi periodici basandosi su un file CSV'

    def add_arguments(self, parser):
        parser.add_argument(
            'csv_file',
            type=str,
            help='Percorso del file CSV contenente id_benefattore e descrizione_contributo_occasionale'
        )
        parser.add_argument(
            '--tipo-contributo-periodico',
            type=str,
            required=True,
            help='Nome del TipoContributoPeriodico da assegnare alle adesioni'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Esegue una simulazione senza salvare le modifiche'
        )
        parser.add_argument(
            '--controlla-tutti-duplicati',
            action='store_true',
            help='Controlla e risolve duplicati per tutte le adesioni con stesso benefattore e tipo_contributo_periodico, non solo quelle elaborate dal file'
        )

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        nome_tipo_periodico = options['tipo_contributo_periodico']
        dry_run = options['dry_run']
        controlla_tutti_duplicati = options['controlla_tutti_duplicati']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('MODALITÀ DRY-RUN: Nessuna modifica verrà salvata')
            )

        # Verifica che il TipoContributoPeriodico esista
        try:
            tipo_contributo_periodico = TipologiaContributoPeriodico.objects.get(
                nome=nome_tipo_periodico
            )
        except TipologiaContributoPeriodico.DoesNotExist:
            raise CommandError(
                f'TipologiaContributoPeriodico con nome "{nome_tipo_periodico}" non trovato'
            )

        # Leggi il file CSV
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                # Verifica che le colonne richieste esistano
                required_columns = ['id_benefattore', 'descrizione_contributo_occasionale']
                if not all(col in reader.fieldnames for col in required_columns):
                    raise CommandError(
                        f'Il file CSV deve contenere le colonne: {", ".join(required_columns)}'
                    )

                adesioni_modificate = 0
                adesioni_non_trovate = 0
                errori = []
                adesioni_convertite = []  # Lista per tenere traccia delle adesioni convertite
                duplicati_gestiti = 0

                with transaction.atomic():
                    for row_num, row in enumerate(reader, start=2):  # Start=2 perché la riga 1 è l'header
                        id_benefattore = row['id_benefattore'].strip()
                        descrizione_contributo = row['descrizione_contributo_occasionale'].strip()

                        if not id_benefattore or not descrizione_contributo:
                            errori.append(f'Riga {row_num}: id_benefattore o descrizione_contributo_occasionale vuoti')
                            continue

                        try:
                            # Cerca l'adesione che corrisponde ai criteri
                            adesione = Adesione.objects.select_related(
                                'benefattore', 
                                'tipologia_contributo_occasionale'
                            ).get(
                                benefattore__id=id_benefattore,
                                tipologia_contributo_occasionale__nome=descrizione_contributo
                            )

                            if dry_run:
                                self.stdout.write(
                                    f'[DRY-RUN] Adesione ID {adesione.id} - Benefattore: {adesione.benefattore} - '
                                    f'Da: {adesione.tipologia_contributo_occasionale} -> A: {tipo_contributo_periodico}'
                                )
                            else:
                                # Effettua la conversione
                                adesione.tipologia_contributo_periodico = tipo_contributo_periodico
                                adesione.tipologia_contributo_occasionale = None
                                adesione.save()

                                self.stdout.write(
                                    f'Convertita adesione ID {adesione.id} - Benefattore: {adesione.benefattore}'
                                )

                                # Aggiungi l'adesione alla lista delle convertite
                                adesioni_convertite.append(adesione)

                            adesioni_modificate += 1

                        except Adesione.DoesNotExist:
                            errori.append(
                                f'Riga {row_num}: Adesione non trovata per benefattore ID {id_benefattore} '
                                f'con contributo occasionale "{descrizione_contributo}"'
                            )
                            adesioni_non_trovate += 1
                        except Adesione.MultipleObjectsReturned:
                            errori.append(
                                f'Riga {row_num}: Trovate multiple adesioni per benefattore ID {id_benefattore} '
                                f'con contributo occasionale "{descrizione_contributo}"'
                            )
                        except Exception as e:
                            errori.append(f'Riga {row_num}: Errore imprevisto: {str(e)}')

                    # Gestione duplicati dopo la conversione
                    if controlla_tutti_duplicati:
                        # Controlla tutti i duplicati nel database per questo tipo_contributo_periodico
                        if dry_run:
                            duplicati_gestiti = self._analizza_tutti_duplicati_dry_run(tipo_contributo_periodico)
                            if duplicati_gestiti > 0:
                                self.stdout.write(
                                    self.style.WARNING(f'[DRY-RUN] Sarebbero stati risolti {duplicati_gestiti} duplicati totali nel database')
                                )
                        else:
                            duplicati_gestiti = self._gestisci_tutti_duplicati(tipo_contributo_periodico)
                            if duplicati_gestiti > 0:
                                self.stdout.write(
                                    self.style.SUCCESS(f'Risolti {duplicati_gestiti} duplicati totali nel database')
                                )
                    elif adesioni_convertite:
                        # Controlla solo i duplicati tra le adesioni convertite
                        if dry_run:
                            duplicati_gestiti = self._analizza_duplicati_dry_run(adesioni_convertite, tipo_contributo_periodico)
                            if duplicati_gestiti > 0:
                                self.stdout.write(
                                    self.style.WARNING(f'[DRY-RUN] Sarebbero stati risolti {duplicati_gestiti} duplicati dalle conversioni')
                                )
                        else:
                            duplicati_gestiti = self._gestisci_duplicati(adesioni_convertite, tipo_contributo_periodico)
                            if duplicati_gestiti > 0:
                                self.stdout.write(
                                    self.style.SUCCESS(f'Risolti {duplicati_gestiti} duplicati dalle conversioni')
                                )

                    if dry_run:
                        # In modalità dry-run, annulla la transazione
                        transaction.set_rollback(True)

        except FileNotFoundError:
            raise CommandError(f'File CSV non trovato: {csv_file}')
        except Exception as e:
            raise CommandError(f'Errore nella lettura del file CSV: {str(e)}')

        # Stampa il riepilogo
        self.stdout.write('\n' + '='*50)
        self.stdout.write('RIEPILOGO OPERAZIONE')
        self.stdout.write('='*50)
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'Adesioni che sarebbero state modificate: {adesioni_modificate}')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Adesioni modificate con successo: {adesioni_modificate}')
            )
        
        if adesioni_non_trovate > 0:
            self.stdout.write(
                self.style.WARNING(f'Adesioni non trovate: {adesioni_non_trovate}')
            )

        if duplicati_gestiti > 0:
            tipo_controllo = "totali nel database" if controlla_tutti_duplicati else "dalle conversioni"
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Duplicati che sarebbero stati risolti ({tipo_controllo}): {duplicati_gestiti}')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'Duplicati risolti ({tipo_controllo}): {duplicati_gestiti}')
                )

        if errori:
            self.stdout.write(
                self.style.ERROR(f'Errori riscontrati: {len(errori)}')
            )
            for errore in errori:
                self.stdout.write(self.style.ERROR(f'  - {errore}'))

        if not dry_run:
            if adesioni_modificate > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'\nOperazione completata! {adesioni_modificate} adesioni convertite da contributi '
                        f'occasionali a contributi periodici ({nome_tipo_periodico})'
                    )
                )
            if controlla_tutti_duplicati and duplicati_gestiti > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Controllo duplicati completato! {duplicati_gestiti} duplicati risolti nel database '
                        f'per il tipo contributo periodico "{nome_tipo_periodico}"'
                    )
                )

    def _gestisci_duplicati(self, adesioni_convertite, tipo_contributo_periodico):
        """
        Gestisce i duplicati tra le adesioni convertite.
        Trova adesioni con stesso benefattore e stesso tipo_contributo_periodico,
        sposta tutte le donazioni alla prima adesione e cancella i duplicati.
        """
        duplicati_risolti = 0

        # Raggruppa le adesioni per benefattore e tipo_contributo_periodico
        gruppi_adesioni = defaultdict(list)

        for adesione in adesioni_convertite:
            # Ricarica l'adesione per avere i dati aggiornati
            adesione.refresh_from_db()
            chiave = (adesione.benefattore_id, adesione.tipologia_contributo_periodico_id)
            gruppi_adesioni[chiave].append(adesione)

        # Processa ogni gruppo per trovare duplicati
        for (benefattore_id, tipo_periodico_id), adesioni_gruppo in gruppi_adesioni.items():
            if len(adesioni_gruppo) > 1:
                # Ordina per ID per avere un ordine consistente (la prima sarà quella con ID più basso)
                adesioni_gruppo.sort(key=lambda x: x.id)
                adesione_principale = adesioni_gruppo[0]
                adesioni_duplicate = adesioni_gruppo[1:]

                self.stdout.write(
                    f'Trovati duplicati per benefattore ID {benefattore_id} '
                    f'e tipo contributo periodico ID {tipo_periodico_id}'
                )
                self.stdout.write(
                    f'  Adesione principale: ID {adesione_principale.id}'
                )

                # Sposta tutte le donazioni dalle adesioni duplicate a quella principale
                for adesione_duplicata in adesioni_duplicate:
                    donazioni = adesione_duplicata.donazione_set.all()
                    donazioni_spostate = 0

                    for donazione in donazioni:
                        donazione.adesione = adesione_principale
                        donazione.save()
                        donazioni_spostate += 1

                    self.stdout.write(
                        f'  Adesione duplicata ID {adesione_duplicata.id}: '
                        f'{donazioni_spostate} donazioni spostate'
                    )

                    # Verifica che non ci siano più donazioni associate
                    if adesione_duplicata.donazione_set.count() == 0:
                        adesione_duplicata.delete()
                        self.stdout.write(
                            f'  Adesione duplicata ID {adesione_duplicata.id} eliminata'
                        )
                        duplicati_risolti += 1
                    else:
                        self.stdout.write(
                            self.style.ERROR(
                                f'  ERRORE: Adesione duplicata ID {adesione_duplicata.id} '
                                f'ha ancora donazioni associate!'
                            )
                        )

        return duplicati_risolti

    def _analizza_duplicati_dry_run(self, adesioni_convertite, tipo_contributo_periodico):
        """
        Analizza i duplicati che sarebbero presenti dopo la conversione in modalità dry-run.
        """
        duplicati_trovati = 0

        # Raggruppa le adesioni per benefattore e tipo_contributo_periodico
        gruppi_adesioni = defaultdict(list)

        for adesione in adesioni_convertite:
            # In dry-run, simula la conversione
            chiave = (adesione.benefattore_id, tipo_contributo_periodico.id)
            gruppi_adesioni[chiave].append(adesione)

        # Processa ogni gruppo per trovare duplicati
        for (benefattore_id, tipo_periodico_id), adesioni_gruppo in gruppi_adesioni.items():
            if len(adesioni_gruppo) > 1:
                # Ordina per ID per avere un ordine consistente
                adesioni_gruppo.sort(key=lambda x: x.id)
                adesione_principale = adesioni_gruppo[0]
                adesioni_duplicate = adesioni_gruppo[1:]

                self.stdout.write(
                    f'[DRY-RUN] Duplicati trovati per benefattore ID {benefattore_id} '
                    f'e tipo contributo periodico "{tipo_contributo_periodico.nome}"'
                )
                self.stdout.write(
                    f'[DRY-RUN]   Adesione principale: ID {adesione_principale.id}'
                )

                # Conta le donazioni che sarebbero spostate
                for adesione_duplicata in adesioni_duplicate:
                    donazioni_count = adesione_duplicata.donazione_set.count()
                    self.stdout.write(
                        f'[DRY-RUN]   Adesione duplicata ID {adesione_duplicata.id}: '
                        f'{donazioni_count} donazioni da spostare'
                    )
                    duplicati_trovati += 1

        return duplicati_trovati

    def _gestisci_tutti_duplicati(self, tipo_contributo_periodico):
        """
        Gestisce tutti i duplicati nel database per il tipo_contributo_periodico specificato.
        Trova tutte le adesioni con stesso benefattore e stesso tipo_contributo_periodico,
        sposta tutte le donazioni alla prima adesione e cancella i duplicati.
        """
        duplicati_risolti = 0

        # Trova tutte le adesioni con questo tipo_contributo_periodico
        adesioni_con_tipo = Adesione.objects.filter(
            tipologia_contributo_periodico=tipo_contributo_periodico
        ).select_related('benefattore', 'tipologia_contributo_periodico').order_by('benefattore_id', 'id')

        # Raggruppa per benefattore
        gruppi_adesioni = defaultdict(list)
        for adesione in adesioni_con_tipo:
            gruppi_adesioni[adesione.benefattore_id].append(adesione)

        # Processa ogni gruppo per trovare duplicati
        for benefattore_id, adesioni_gruppo in gruppi_adesioni.items():
            if len(adesioni_gruppo) > 1:
                # La prima adesione (con ID più basso) sarà quella principale
                adesione_principale = adesioni_gruppo[0]
                adesioni_duplicate = adesioni_gruppo[1:]

                self.stdout.write(
                    f'Trovati duplicati per benefattore ID {benefattore_id} '
                    f'e tipo contributo periodico "{tipo_contributo_periodico.nome}"'
                )
                self.stdout.write(
                    f'  Adesione principale: ID {adesione_principale.id}'
                )

                # Sposta tutte le donazioni dalle adesioni duplicate a quella principale
                for adesione_duplicata in adesioni_duplicate:
                    donazioni = adesione_duplicata.donazione_set.all()
                    donazioni_spostate = 0

                    for donazione in donazioni:
                        donazione.adesione = adesione_principale
                        donazione.save()
                        donazioni_spostate += 1

                    self.stdout.write(
                        f'  Adesione duplicata ID {adesione_duplicata.id}: '
                        f'{donazioni_spostate} donazioni spostate'
                    )

                    # Verifica che non ci siano più donazioni associate
                    if adesione_duplicata.donazione_set.count() == 0:
                        adesione_duplicata.delete()
                        self.stdout.write(
                            f'  Adesione duplicata ID {adesione_duplicata.id} eliminata'
                        )
                        duplicati_risolti += 1
                    else:
                        self.stdout.write(
                            self.style.ERROR(
                                f'  ERRORE: Adesione duplicata ID {adesione_duplicata.id} '
                                f'ha ancora donazioni associate!'
                            )
                        )

        return duplicati_risolti

    def _analizza_tutti_duplicati_dry_run(self, tipo_contributo_periodico):
        """
        Analizza tutti i duplicati nel database per il tipo_contributo_periodico specificato in modalità dry-run.
        """
        duplicati_trovati = 0

        # Trova tutte le adesioni con questo tipo_contributo_periodico
        adesioni_con_tipo = Adesione.objects.filter(
            tipologia_contributo_periodico=tipo_contributo_periodico
        ).select_related('benefattore', 'tipologia_contributo_periodico').order_by('benefattore_id', 'id')

        # Raggruppa per benefattore
        gruppi_adesioni = defaultdict(list)
        for adesione in adesioni_con_tipo:
            gruppi_adesioni[adesione.benefattore_id].append(adesione)

        # Processa ogni gruppo per trovare duplicati
        for benefattore_id, adesioni_gruppo in gruppi_adesioni.items():
            if len(adesioni_gruppo) > 1:
                # La prima adesione (con ID più basso) sarà quella principale
                adesione_principale = adesioni_gruppo[0]
                adesioni_duplicate = adesioni_gruppo[1:]

                self.stdout.write(
                    f'[DRY-RUN] Duplicati trovati per benefattore ID {benefattore_id} '
                    f'e tipo contributo periodico "{tipo_contributo_periodico.nome}"'
                )
                self.stdout.write(
                    f'[DRY-RUN]   Adesione principale: ID {adesione_principale.id}'
                )

                # Conta le donazioni che sarebbero spostate
                for adesione_duplicata in adesioni_duplicate:
                    donazioni_count = adesione_duplicata.donazione_set.count()
                    self.stdout.write(
                        f'[DRY-RUN]   Adesione duplicata ID {adesione_duplicata.id}: '
                        f'{donazioni_count} donazioni da spostare'
                    )
                    duplicati_trovati += 1

        return duplicati_trovati
