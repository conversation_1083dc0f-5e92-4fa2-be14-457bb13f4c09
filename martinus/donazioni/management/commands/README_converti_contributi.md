# Management Command: converti_contributi_occasionali

## Descrizione
Questo comando converte contributi occasionali in contributi periodici basandosi su un file CSV di input. Do<PERSON> la conversione, gestisce automaticamente i duplicati che potrebbero essere creati.

## Funzionalità

### Conversione Contributi
1. Legge un file CSV con le colonne `id_benefattore` e `descrizione_contributo_occasionale`
2. Per ogni riga, cerca un'adesione che abbia:
   - Il benefattore con l'ID specificato
   - Una tipologia_contributo_occasionale il cui nome corrisponde alla descrizione nel CSV
3. Se trova l'adesione:
   - Assegna il TipoContributoPeriodico specificato come parametro
   - Mette a `null` il campo `tipologia_contributo_occasionale`

### Gestione Duplicati
Il comando offre due modalità per la gestione dei duplicati:

#### Modalità Standard (solo adesioni convertite)
Dopo la conversione, il comando:
1. Identifica adesioni duplicate tra quelle appena convertite (stesso benefattore + stesso tipo_contributo_periodico)
2. Mantiene la prima adesione (con ID più basso)
3. Sposta tutte le donazioni dalle adesioni duplicate alla prima adesione
4. Elimina le adesioni duplicate

#### Modalità Completa (tutti i duplicati nel database)
Con il parametro `--controlla-tutti-duplicati`, il comando:
1. Cerca TUTTI i duplicati nel database per il tipo_contributo_periodico specificato
2. Non si limita alle adesioni appena convertite
3. Applica la stessa logica di consolidamento a tutti i duplicati trovati
4. Utile per pulire duplicati esistenti nel database

## Utilizzo

### Formato CSV Richiesto
Il file CSV deve contenere le seguenti colonne:
- `id_benefattore`: ID numerico del benefattore
- `descrizione_contributo_occasionale`: Nome esatto del tipo di contributo occasionale

Esempio di file CSV:
```csv
id_benefattore,descrizione_contributo_occasionale
123,Donazione Natale
456,Contributo Emergenza
789,Offerta Speciale
101,Donazione Pasqua
```

### Comandi

#### Modalità Dry-Run (Simulazione)
```bash
./manage.py converti_contributi_occasionali /path/to/file.csv --tipo-contributo-periodico "Nome Contributo Periodico" --dry-run
```

#### Esecuzione Effettiva
```bash
./manage.py converti_contributi_occasionali /path/to/file.csv --tipo-contributo-periodico "Nome Contributo Periodico"
```

#### Controllo Completo Duplicati
```bash
# Solo controllo duplicati (dry-run)
./manage.py converti_contributi_occasionali /path/to/file.csv --tipo-contributo-periodico "Nome Contributo Periodico" --controlla-tutti-duplicati --dry-run

# Conversione + controllo completo duplicati
./manage.py converti_contributi_occasionali /path/to/file.csv --tipo-contributo-periodico "Nome Contributo Periodico" --controlla-tutti-duplicati
```

### Parametri
- `csv_file`: Percorso del file CSV (obbligatorio)
- `--tipo-contributo-periodico`: Nome del TipoContributoPeriodico da assegnare (obbligatorio)
- `--dry-run`: Modalità simulazione - non salva le modifiche (opzionale)
- `--controlla-tutti-duplicati`: Controlla tutti i duplicati nel database per il tipo specificato, non solo quelli dalle conversioni (opzionale)

## Quando Usare --controlla-tutti-duplicati

### Scenario 1: Solo Conversione (Default)
Usa il comando senza `--controlla-tutti-duplicati` quando:
- Vuoi convertire specifici contributi occasionali in periodici
- Vuoi gestire solo i duplicati che potrebbero essere creati dalla conversione
- Hai un dataset pulito e vuoi mantenere il controllo limitato

### Scenario 2: Conversione + Pulizia Completa
Usa il parametro `--controlla-tutti-duplicati` quando:
- Vuoi approfittare della conversione per pulire TUTTI i duplicati esistenti
- Sospetti che ci siano già duplicati nel database per quel tipo di contributo
- Vuoi fare una pulizia completa del database
- Stai migrando dati e vuoi assicurarti della consistenza

### Scenario 3: Solo Pulizia Duplicati
Puoi usare il comando anche solo per pulire duplicati senza conversioni:
- Crea un CSV vuoto (solo header)
- Usa `--controlla-tutti-duplicati`
- Il comando non troverà adesioni da convertire ma pulirà tutti i duplicati

## Caratteristiche di Sicurezza

### Transazioni Atomiche
Tutte le modifiche vengono eseguite in una singola transazione. Se si verifica un errore, tutte le modifiche vengono annullate.

### Modalità Dry-Run
La modalità `--dry-run` permette di:
- Testare il comando senza modificare i dati
- Vedere quante adesioni sarebbero convertite
- Identificare potenziali duplicati
- Verificare errori nel file CSV

### Validazioni
- Verifica l'esistenza del TipoContributoPeriodico prima di iniziare
- Controlla la presenza delle colonne richieste nel CSV
- Gestisce adesioni non trovate e multiple adesioni

## Output del Comando

### Durante l'Esecuzione
- Mostra ogni adesione convertita
- Indica duplicati trovati e risolti
- Segnala errori specifici per ogni riga del CSV

### Riepilogo Finale
- Numero di adesioni modificate con successo
- Numero di adesioni non trovate
- Numero di duplicati risolti
- Lista dettagliata degli errori riscontrati

## Gestione Errori

### Errori Comuni
1. **TipoContributoPeriodico non trovato**: Il nome specificato non esiste nel database
2. **File CSV non trovato**: Il percorso del file non è valido
3. **Colonne mancanti**: Il CSV non contiene le colonne richieste
4. **Adesione non trovata**: Nessuna adesione corrisponde ai criteri specificati
5. **Multiple adesioni**: Più adesioni corrispondono agli stessi criteri

### Recupero da Errori
- Gli errori non bloccano l'elaborazione delle altre righe
- Tutti gli errori vengono raccolti e mostrati nel riepilogo finale
- Le transazioni atomiche garantiscono la consistenza dei dati

## Esempi di Output

### Dry-Run
```
MODALITÀ DRY-RUN: Nessuna modifica verrà salvata
[DRY-RUN] Adesione ID 123 - Benefattore: Mario Rossi - Da: Donazione Natale -> A: Contributo Mensile
[DRY-RUN] Duplicati trovati per benefattore ID 456 e tipo contributo periodico "Contributo Mensile"
[DRY-RUN]   Adesione principale: ID 789
[DRY-RUN]   Adesione duplicata ID 790: 2 donazioni da spostare

==================================================
RIEPILOGO OPERAZIONE
==================================================
Adesioni che sarebbero state modificate: 15
Duplicati che sarebbero stati risolti: 3
```

### Esecuzione Reale (Standard)
```
Convertita adesione ID 123 - Benefattore: Mario Rossi
Trovati duplicati per benefattore ID 456 e tipo contributo periodico "Contributo Mensile"
  Adesione principale: ID 789
  Adesione duplicata ID 790: 2 donazioni spostate
  Adesione duplicata ID 790 eliminata

==================================================
RIEPILOGO OPERAZIONE
==================================================
Adesioni modificate con successo: 15
Duplicati risolti (dalle conversioni): 3

Operazione completata! 15 adesioni convertite da contributi occasionali a contributi periodici (Contributo Mensile)
```

### Esecuzione con Controllo Completo Duplicati
```
Convertita adesione ID 123 - Benefattore: Mario Rossi
Convertita adesione ID 124 - Benefattore: Luigi Verdi

Trovati duplicati per benefattore ID 456 e tipo contributo periodico "Contributo Mensile"
  Adesione principale: ID 789
  Adesione duplicata ID 790: 2 donazioni spostate
  Adesione duplicata ID 790 eliminata
Trovati duplicati per benefattore ID 789 e tipo contributo periodico "Contributo Mensile"
  Adesione principale: ID 100
  Adesione duplicata ID 200: 5 donazioni spostate
  Adesione duplicata ID 200 eliminata

==================================================
RIEPILOGO OPERAZIONE
==================================================
Adesioni modificate con successo: 15
Duplicati risolti (totali nel database): 8

Operazione completata! 15 adesioni convertite da contributi occasionali a contributi periodici (Contributo Mensile)
Controllo duplicati completato! 8 duplicati risolti nel database per il tipo contributo periodico "Contributo Mensile"
```
