# Generated by Django 2.2.28 on 2025-07-25 10:17

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('donazioni', '0016_auto_20250616_1003'),
    ]

    operations = [
        migrations.AlterField(
            model_name='donazione',
            name='controvalore_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='donazione',
            name='importo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
    ]
