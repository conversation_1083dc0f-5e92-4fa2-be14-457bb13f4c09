@charset "UTF-8";
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */


body { font-family: "overpass", Arial, sans-serif; font-size: 1rem; line-height: 1.5; color: #272727; background-color: #f1f1f1; }

.blockquote { padding: 0.5rem 1rem; margin-bottom: 1rem; font-size: 1.25rem; border-left: 0.25rem solid #eceeef; }

.blockquote-reverse { padding-right: 1rem; padding-left: 0; text-align: right; border-right: 0.25rem solid #eceeef; border-left: 0; }

.btn-primary, .submit-row input[type='submit']:not([class*="btn-"]):first-child, .submit-row input[type='button']:not([class*="btn-"]):first-child, .submit-row button:not([class*="btn-"]):first-child, body.login .submit-row input[type='submit'], #changelist #toolbar #changelist-search input[type='submit'], #changelist #changelist-form .actions button, #changelist #changelist-form .paginator input[type='submit'] { color: #fff; background-color: #d6303c; border-color: #e44a48; }

#result_list tbody > tr.selected, .tabular table tbody > tr.selected, table#change-history tbody > tr.selected { background-color: #828a9f; }

#result_list tbody > tr.selected:nth-child(even), .tabular table tbody > tr.selected:nth-child(even), table#change-history tbody > tr.selected:nth-child(even) { background-color: #687086; }

body.suit_layout_horizontal #header { display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; -ms-flex-direction: row; flex-direction: row; -webkit-flex-wrap: wrap; -ms-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; -webkit-justify-content: flex-start; -ms-flex-pack: start; justify-content: flex-start; -webkit-align-content: center; -ms-flex-line-pack: center; align-content: center; -webkit-box-align: center; -webkit-align-items: center; -ms-flex-align: center; align-items: center; background-color: #e44a48; padding-top: 0.8rem; }

body.suit_layout_horizontal #header .header-label { font-size: 1rem; color: #e44a48; text-transform: uppercase; font-weight: bold; }

body.suit_layout_horizontal #header #branding #site-name { font-size: 1.6rem; }

body.suit_layout_horizontal #header #branding {min-width: 800px; padding-left: 1.1875rem; text-align: left;}

body.suit_layout_horizontal #header #user-tools { -webkit-box-flex: 2; -webkit-flex-grow: 2; -ms-flex-positive: 2; flex-grow: 2; padding: 0 1rem; text-align: right; color: #d6303c; }

body.suit_layout_horizontal #header #user-tools a { display: inline-block; margin: 0 2px; color: #BDD5EA; }

body.suit_layout_horizontal #header #site-name { margin: 0; font-weight: bold; }

body.suit_layout_horizontal #header { background-color: #d6303c; }

body.suit_layout_horizontal #suit-nav > ul > li.active a { background-color: #fff; color: #373a3c; font-weight: bold; box-shadow: inset 0 3px 0 #BDD5EA; }

body.suit_layout_horizontal #suit-sub-nav ul > li > a { color: rgb(48, 66, 106); display: block; float: left; font-size: 1rem; padding: 0.80128rem 20px; margin: 2px 0.5rem 0; font-weight: bold;}

body.suit_layout_horizontal #content #content-main { -webkit-box-flex: 1; -webkit-flex-grow: 1; -ms-flex-positive: 1; flex-grow: 1; margin-left:5px;}

body.suit_layout_horizontal body.dashboard .module { margin: 0 2rem 1rem 0; }

body.suit_layout_horizontal body.dashboard .module table caption { caption-side: inherit; font-weight: bold; font-size: 1.15rem; padding: 0; margin-bottom: 5px; margin-top: 5px; }
body.suit_layout_horizontal body.dashboard .module table caption a { caption-side: inherit; font-weight: bold; font-size: 1.15rem; padding: 0; color: #e44a48;}

body.suit_layout_horizontal body.dashboard .module table tr th { width: 216px; background-color: #fff; font-weight: bold;}

body.suit_layout_horizontal #suit-nav { -ms-flex-preferred-size: 100%; flex-basis: 100%; margin-top: 0.6rem; background-color: #383d4b; border-top:3px solid #e44a48; padding-top: 0.5rem;}


.highcharts-data-table table {
    border-collapse: collapse;
    border-spacing: 0;
    background: white;
    min-width: 30%;
    margin-top: 10px;
    font-family: sans-serif;
    font-size: 0.9em;
}
.highcharts-data-table td, .highcharts-data-table th, .highcharts-data-table caption {
    border: 1px solid silver;
    padding: 0.5em;
}
.highcharts-data-table tr:nth-child(even), .highcharts-data-table thead tr {
    background: #f8f8f8;
}
.highcharts-data-table tr:hover {
    background: #eff;
}
.highcharts-data-table caption {
    font-size: 1.1em;
    font-weight: bold;
}
