/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(a){a.jqx.jqxWidget("jqxTimeline","",{});a.extend(a.jqx._jqxTimeline.prototype,{defineInstance:function(){var b={disabled:false,position:"both",collapsible:true,autoWidth:false,horizontal:false,source:null};if(this===a.jqx._jqxTimeline.prototype){return b}a.extend(true,this,b);return b},createInstance:function(f){var g=this;var d=document.createElement("div");g.element.appendChild(d);g.element.classList.add("jqx-widget","jqx-timeline");d.classList.add("jqx-container");if(g.theme){g.element.classList.add("jqx-widget-"+g.theme)}var e=document.createElement("div");e.classList.add("jqx-timeline-near-items");var c=document.createElement("div");c.classList.add("jqx-timeline-track");var b=document.createElement("div");b.classList.add("jqx-timeline-far-items");d.appendChild(e);d.appendChild(c);d.appendChild(b);g.$={};g.$.container=d;g.$.nearItems=e;g.$.track=c;g.$.farItems=b},render:function(){var b=this;b.element.setAttribute("role","navigation");b._render()},refresh:function(){var b=this;b._render()},propertyChangedHandler:function(c,b,f,e){var d=this;switch(b){case"theme":d.element.classList.remove("jqx-widget-"+f);break;case"source":d._render();return}d._render()},_render:function(){var f=this;if(f.theme){f.element.classList.add("jqx-widget-"+f.theme)}if(!f.source){f.$.nearItems.innerHTML="";f.$.farItems.innerHTML="";return}f.$.nearItems.innerHTML="";f.$.farItems.innerHTML="";f.$.track.innerHTML="";f.$.nearItems.style.height="";f.$.nearItems.style.width="";f.$.farItems.style.height="";f.$.farItems.style.width="";var d=[];var g=[];if(f.horizontal){f.element.setAttribute("horizontal","")}else{f.element.removeAttribute("horizontal")}for(var c=0;c<f.source.length;c++){var e=f.source[c];var b=function(k){var l=document.createElement("div");l.classList.add("jqx-timeline-item");if(k.css){l.classList.add(k.css)}if(f.collapsible){l.setAttribute("collapsible","")}l.innerHTML='<div class="jqx-timeline-item-pointer"></div><div class="jqx-timeline-item-content"><div class="jqx-flex jqx-timeline-item-header"><div class="jqx-timeline-item-icon"><span class="'+k.icon+'"></span></div><div><div class="jqx-timeline-item-title">'+k.title+'</div><div class="jqx-timeline-item-subtitle">'+k.subtitle+'</div></div></div><div class="jqx-timeline-item-description">'+k.description+"</div></div>";l.onclick=function(){if(f.collapsible){l.classList.toggle("expanded");if(l.cloneNode){l.cloneNode.classList.toggle("expanded")}f._repositionDots()}};if(!f.collapsible){l.classList.add("expanded")}d.push(l);var j=document.createElement("div");j.classList.add("jqx-timeline-item-date");j.innerHTML=k.date;g.push(j);var i=document.createElement("div");i.timelineItem=l;if(f.position==="near"){f.$.nearItems.appendChild(l);f.$.farItems.appendChild(j)}else{if(f.position==="far"){f.$.farItems.appendChild(l);f.$.nearItems.appendChild(j)}else{f.$.nearItems.appendChild(l);var h=l.cloneNode(true);h.onclick=function(){if(f.collapsible){h.classList.toggle("expanded");l.classList.toggle("expanded");f._repositionDots()}};f.$.farItems.appendChild(h);l.cloneNode=h;if(c%2===0){l.classList.add("jqx-visibility-hidden");f.$.nearItems.appendChild(j);i.timelineItem=h}else{h.classList.add("jqx-visibility-hidden");f.$.farItems.appendChild(j)}}}i.classList.add("jqx-timeline-dot");if(k.dotCSS){i.classList.add(k.dotCSS)}f.$.track.appendChild(i);i.date=g[c]};b(e)}f._timelineItems=d;f._layout()},_layout:function(){var b=this;b._arrange();b._repositionDots()},_arrange:function(){var d=this;var c=d._timelineItems;if(d.horizontal===false){var f=function(h){var j=0;for(var g=0;g<h.children.length;g++){j=Math.max(j,h.children[g].offsetWidth)}return j+20};d.$.nearItems.style.width=f(d.$.nearItems)+"px";d.$.farItems.style.width=f(d.$.farItems)+"px"}else{var f=function(){var h=0;for(var g=0;g<c.length;g++){h+=c[g].offsetWidth}return h};var b=f();if(d.autoWidth){d.element.style.width=b+"px"}else{d.element.style.minWidth=b+"px";var e=(d.element.offsetWidth-b)/c.length;if(e>0){a.each(d.$.farItems.children,function(g){var h=d.$.farItems.children[g];h.style.marginLeft=h.style.marginRight=e/2+"px"});a.each(d.$.nearItems.children,function(g){var h=d.$.nearItems.children[g];h.style.marginLeft=h.style.marginRight=e/2+"px"})}}}},_repositionDots:function(){var d=this;for(var c=0;c<this.$.track.children.length;c++){var b=this.$.track.children[c];var e=d.offset(b.timelineItem);if(d.horizontal){b.style.left=e.left+(b.timelineItem.offsetWidth/2)-16-10+"px";b.date.style.marginLeft=b.date.style.marginRight="0px";b.date.style.left=e.left+b.timelineItem.offsetWidth/2-(b.date.offsetWidth/2)-26+"px"}else{b.style.top=e.top+(b.timelineItem.offsetHeight/2)-30+"px";b.date.style.top=e.top+(b.timelineItem.offsetHeight/2)-30+"px"}}},_offsetTop:function(b){var c=this;if(!b){return 0}return b.offsetTop+c._offsetTop(b.offsetParent)},_offsetLeft:function(b){var c=this;if(!b){return 0}return b.offsetLeft+c._offsetLeft(b.offsetParent)},offset:function(b){return{left:this._offsetLeft(b),top:this._offsetTop(b)}},_resizeHandler:function(){var b=this;b._layout()}})})(jqxBaseFramework)})();

