/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}var a=document.all&&!document.addEventListener;if(!a){(function(bf,I){var s,ap,am=bf.document,bq=bf.location,bv=bf.navigator,az=bf.JQXLite,Z=bf.$,aT=Array.prototype.push,aF=Array.prototype.slice,aC=Array.prototype.indexOf,A=Object.prototype.toString,d=Object.prototype.hasOwnProperty,ay=String.prototype.trim,E=function(bw,bx){return new E.fn.init(bw,bx,s)},aG=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,av=/\S/,ba=/\s+/,U=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,aH=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,g=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,l=/^[\],:{}\s]*$/,v=/(?:^|:|,)(?:\s*\[)+/g,a7=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,M=/"[^"\\\r\n]*"|true|false|null|-?(?:\d\d*\.|)\d+(?:[eE][\-+]?\d+|)/g,aw=/^-ms-/,aU=/-([\da-z])/gi,o=function(bw,bx){return(bx+"").toUpperCase()},a6=function(){if(am.addEventListener){am.removeEventListener("DOMContentLoaded",a6,false);E.ready()}else{if(am.readyState==="complete"){am.detachEvent("onreadystatechange",a6);E.ready()}}},a2={};E.fn=E.prototype={constructor:E,init:function(bw,bz,bA){var by,bB,bx,bC;if(!bw){return this}if(bw.nodeType){this.context=this[0]=bw;this.length=1;return this}if(typeof bw==="string"){if(bw.charAt(0)==="<"&&bw.charAt(bw.length-1)===">"&&bw.length>=3){by=[null,bw,null]}else{by=aH.exec(bw)}if(by&&(by[1]||!bz)){if(by[1]){bz=bz instanceof E?bz[0]:bz;bC=(bz&&bz.nodeType?bz.ownerDocument||bz:am);bw=E.parseHTML(by[1],bC,true);if(g.test(by[1])&&E.isPlainObject(bz)){this.attr.call(bw,bz,true)}return E.merge(this,bw)}else{bB=am.getElementById(by[2]);if(bB&&bB.parentNode){if(bB.id!==by[2]){return bA.find(bw)}this.length=1;this[0]=bB}this.context=am;this.selector=bw;return this}}else{if(!bz||bz.jqx){return(bz||bA).find(bw)}else{return this.constructor(bz).find(bw)}}}else{if(E.isFunction(bw)){return bA.ready(bw)}}if(bw.selector!==I){this.selector=bw.selector;this.context=bw.context}return E.makeArray(bw,this)},selector:"",jqx:"4.5.0",length:0,size:function(){return this.length},toArray:function(){return aF.call(this)},get:function(bw){return bw==null?this.toArray():(bw<0?this[this.length+bw]:this[bw])},pushStack:function(bx,bz,bw){var by=E.merge(this.constructor(),bx);by.prevObject=this;by.context=this.context;if(bz==="find"){by.selector=this.selector+(this.selector?" ":"")+bw}else{if(bz){by.selector=this.selector+"."+bz+"("+bw+")"}}return by},each:function(bx,bw){return E.each(this,bx,bw)},ready:function(bw){E.ready.promise().done(bw);return this},eq:function(bw){bw=+bw;return bw===-1?this.slice(bw):this.slice(bw,bw+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(aF.apply(this,arguments),"slice",aF.call(arguments).join(","))},map:function(bw){return this.pushStack(E.map(this,function(by,bx){return bw.call(by,bx,by)}))},end:function(){return this.prevObject||this.constructor(null)},push:aT,sort:[].sort,splice:[].splice};E.fn.init.prototype=E.fn;E.extend=E.fn.extend=function(){var bF,by,bw,bx,bC,bD,bB=arguments[0]||{},bA=1,bz=arguments.length,bE=false;if(typeof bB==="boolean"){bE=bB;bB=arguments[1]||{};bA=2}if(typeof bB!=="object"&&!E.isFunction(bB)){bB={}}if(bz===bA){bB=this;--bA}for(;bA<bz;bA++){if((bF=arguments[bA])!=null){for(by in bF){bw=bB[by];bx=bF[by];if(bB===bx){continue}if(bE&&bx&&(E.isPlainObject(bx)||(bC=E.isArray(bx)))){if(bC){bC=false;bD=bw&&E.isArray(bw)?bw:[]}else{bD=bw&&E.isPlainObject(bw)?bw:{}}bB[by]=E.extend(bE,bD,bx)}else{if(bx!==I){bB[by]=bx}}}}}return bB};E.extend({noConflict:function(bw){if(bf.$===E){bf.$=Z}if(bw&&bf.JQXLite===E){bf.JQXLite=az}return E},isReady:false,readyWait:1,holdReady:function(bw){if(bw){E.readyWait++}else{E.ready(true)}},ready:function(bw){if(bw===true?--E.readyWait:E.isReady){return}if(!am.body){return setTimeout(E.ready,1)}E.isReady=true;if(bw!==true&&--E.readyWait>0){return}ap.resolveWith(am,[E]);if(E.fn.trigger){E(am).trigger("ready").off("ready")}},isFunction:function(bw){return E.type(bw)==="function"},isArray:Array.isArray||function(bw){return E.type(bw)==="array"},isWindow:function(bw){return bw!=null&&bw==bw.window},isNumeric:function(bw){return !isNaN(parseFloat(bw))&&isFinite(bw)},type:function(bw){return bw==null?String(bw):a2[A.call(bw)]||"object"},isPlainObject:function(by){if(!by||E.type(by)!=="object"||by.nodeType||E.isWindow(by)){return false}try{if(by.constructor&&!d.call(by,"constructor")&&!d.call(by.constructor.prototype,"isPrototypeOf")){return false}}catch(bx){return false}var bw;for(bw in by){}return bw===I||d.call(by,bw)},isEmptyObject:function(bx){var bw;for(bw in bx){return false}return true},error:function(bw){throw new Error(bw)},parseHTML:function(bz,by,bw){var bx;if(!bz||typeof bz!=="string"){return null}if(typeof by==="boolean"){bw=by;by=0}by=by||am;if((bx=g.exec(bz))){return[by.createElement(bx[1])]}bx=E.buildFragment([bz],by,bw?null:[]);return E.merge([],(bx.cacheable?E.clone(bx.fragment):bx.fragment).childNodes)},parseJSON:function(bw){if(!bw||typeof bw!=="string"){return null}bw=E.trim(bw);if(bf.JSON&&bf.JSON.parse){return bf.JSON.parse(bw)}if(l.test(bw.replace(a7,"@").replace(M,"]").replace(v,""))){return(new Function("return "+bw))()}E.error("Invalid JSON: "+bw)},parseXML:function(by){var bw,bx;if(!by||typeof by!=="string"){return null}try{if(bf.DOMParser){bx=new DOMParser();bw=bx.parseFromString(by,"text/xml")}else{bw=new ActiveXObject("Microsoft.XMLDOM");bw.async="false";bw.loadXML(by)}}catch(bz){bw=I}if(!bw||!bw.documentElement||bw.getElementsByTagName("parsererror").length){E.error("Invalid XML: "+by)}return bw},noop:function(){},globalEval:function(bw){if(bw&&av.test(bw)){(bf.execScript||function(bx){bf["eval"].call(bf,bx)})(bw)}},camelCase:function(bw){return bw.replace(aw,"ms-").replace(aU,o)},nodeName:function(bx,bw){return bx.nodeName&&bx.nodeName.toLowerCase()===bw.toLowerCase()},each:function(bB,bC,by){var bx,bz=0,bA=bB.length,bw=bA===I||E.isFunction(bB);if(by){if(bw){for(bx in bB){if(bC.apply(bB[bx],by)===false){break}}}else{for(;bz<bA;){if(bC.apply(bB[bz++],by)===false){break}}}}else{if(bw){for(bx in bB){if(bC.call(bB[bx],bx,bB[bx])===false){break}}}else{for(;bz<bA;){if(bC.call(bB[bz],bz,bB[bz++])===false){break}}}}return bB},trim:ay&&!ay.call("\uFEFF\xA0")?function(bw){return bw==null?"":ay.call(bw)}:function(bw){return bw==null?"":(bw+"").replace(U,"")},makeArray:function(bw,by){var bz,bx=by||[];if(bw!=null){bz=E.type(bw);if(bw.length==null||bz==="string"||bz==="function"||bz==="regexp"||E.isWindow(bw)){aT.call(bx,bw)}else{E.merge(bx,bw)}}return bx},inArray:function(bz,bx,by){var bw;if(bx){if(aC){return aC.call(bx,bz,by)}bw=bx.length;by=by?by<0?Math.max(0,bw+by):by:0;for(;by<bw;by++){if(by in bx&&bx[by]===bz){return by}}}return -1},merge:function(bA,by){var bw=by.length,bz=bA.length,bx=0;if(typeof bw==="number"){for(;bx<bw;bx++){bA[bz++]=by[bx]}}else{while(by[bx]!==I){bA[bz++]=by[bx++]}}bA.length=bz;return bA},grep:function(bx,bC,bw){var bB,by=[],bz=0,bA=bx.length;bw=!!bw;for(;bz<bA;bz++){bB=!!bC(bx[bz],bz);if(bw!==bB){by.push(bx[bz])}}return by},map:function(bw,bD,bE){var bB,bC,bA=[],by=0,bx=bw.length,bz=bw instanceof E||bx!==I&&typeof bx==="number"&&((bx>0&&bw[0]&&bw[bx-1])||bx===0||E.isArray(bw));if(bz){for(;by<bx;by++){bB=bD(bw[by],by,bE);if(bB!=null){bA[bA.length]=bB}}}else{for(bC in bw){bB=bD(bw[bC],bC,bE);if(bB!=null){bA[bA.length]=bB}}}return bA.concat.apply([],bA)},guid:1,proxy:function(bA,bz){var by,bw,bx;if(typeof bz==="string"){by=bA[bz];bz=bA;bA=by}if(!E.isFunction(bA)){return I}bw=aF.call(arguments,2);bx=function(){return bA.apply(bz,bw.concat(aF.call(arguments)))};bx.guid=bA.guid=bA.guid||E.guid++;return bx},access:function(bw,bC,bF,bD,bA,bG,bE){var by,bB=bF==null,bz=0,bx=bw.length;if(bF&&typeof bF==="object"){for(bz in bF){E.access(bw,bC,bz,bF[bz],1,bG,bD)}bA=1}else{if(bD!==I){by=bE===I&&E.isFunction(bD);if(bB){if(by){by=bC;bC=function(bI,bH,bJ){return by.call(E(bI),bJ)}}else{bC.call(bw,bD);bC=null}}if(bC){for(;bz<bx;bz++){bC(bw[bz],bF,by?bD.call(bw[bz],bz,bC(bw[bz],bF)):bD,bE)}}bA=1}}return bA?bw:bB?bC.call(bw):bx?bC(bw[0],bF):bG},now:function(){return(new Date()).getTime()}});E.ready.promise=function(bz){if(!ap){ap=E.Deferred();if(am.readyState==="complete"){setTimeout(E.ready,1)}else{if(am.addEventListener){am.addEventListener("DOMContentLoaded",a6,false);bf.addEventListener("load",E.ready,false)}else{am.attachEvent("onreadystatechange",a6);bf.attachEvent("onload",E.ready);var by=false;try{by=bf.frameElement==null&&am.documentElement}catch(bx){}if(by&&by.doScroll){(function bw(){if(!E.isReady){try{by.doScroll("left")}catch(bA){return setTimeout(bw,50)}E.ready()}})()}}}}return ap.promise(bz)};E.each("Boolean Number String Function Array Date RegExp Object".split(" "),function(bx,bw){a2["[object "+bw+"]"]=bw.toLowerCase()});s=E(am);var aZ={};function D(bx){var bw=aZ[bx]={};E.each(bx.split(ba),function(bz,by){bw[by]=true});return bw}E.Callbacks=function(bG){bG=typeof bG==="string"?(aZ[bG]||D(bG)):E.extend({},bG);var bz,bw,bA,by,bB,bC,bD=[],bE=!bG.once&&[],bx=function(bH){bz=bG.memory&&bH;bw=true;bC=by||0;by=0;bB=bD.length;bA=true;for(;bD&&bC<bB;bC++){if(bD[bC].apply(bH[0],bH[1])===false&&bG.stopOnFalse){bz=false;break}}bA=false;if(bD){if(bE){if(bE.length){bx(bE.shift())}}else{if(bz){bD=[]}else{bF.disable()}}}},bF={add:function(){if(bD){var bI=bD.length;(function bH(bJ){E.each(bJ,function(bL,bK){var bM=E.type(bK);if(bM==="function"){if(!bG.unique||!bF.has(bK)){bD.push(bK)}}else{if(bK&&bK.length&&bM!=="string"){bH(bK)}}})})(arguments);if(bA){bB=bD.length}else{if(bz){by=bI;bx(bz)}}}return this},remove:function(){if(bD){E.each(arguments,function(bJ,bH){var bI;while((bI=E.inArray(bH,bD,bI))>-1){bD.splice(bI,1);if(bA){if(bI<=bB){bB--}if(bI<=bC){bC--}}}})}return this},has:function(bH){return E.inArray(bH,bD)>-1},empty:function(){bD=[];return this},disable:function(){bD=bE=bz=I;return this},disabled:function(){return !bD},lock:function(){bE=I;if(!bz){bF.disable()}return this},locked:function(){return !bE},fireWith:function(bI,bH){bH=bH||[];bH=[bI,bH.slice?bH.slice():bH];if(bD&&(!bw||bE)){if(bA){bE.push(bH)}else{bx(bH)}}return this},fire:function(){bF.fireWith(this,arguments);return this},fired:function(){return !!bw}};return bF};E.extend({Deferred:function(by){var bx=[["resolve","done",E.Callbacks("once memory"),"resolved"],["reject","fail",E.Callbacks("once memory"),"rejected"],["notify","progress",E.Callbacks("memory")]],bz="pending",bA={state:function(){return bz},always:function(){bw.done(arguments).fail(arguments);return this},then:function(){var bB=arguments;return E.Deferred(function(bC){E.each(bx,function(bE,bD){var bG=bD[0],bF=bB[bE];bw[bD[1]](E.isFunction(bF)?function(){var bH=bF.apply(this,arguments);if(bH&&E.isFunction(bH.promise)){bH.promise().done(bC.resolve).fail(bC.reject).progress(bC.notify)}else{bC[bG+"With"](this===bw?bC:this,[bH])}}:bC[bG])});bB=null}).promise()},promise:function(bB){return bB!=null?E.extend(bB,bA):bA}},bw={};bA.pipe=bA.then;E.each(bx,function(bC,bB){var bE=bB[2],bD=bB[3];bA[bB[1]]=bE.add;if(bD){bE.add(function(){bz=bD},bx[bC^1][2].disable,bx[2][2].lock)}bw[bB[0]]=bE.fire;bw[bB[0]+"With"]=bE.fireWith});bA.promise(bw);if(by){by.call(bw,bw)}return bw},when:function(bA){var by=0,bC=aF.call(arguments),bw=bC.length,bx=bw!==1||(bA&&E.isFunction(bA.promise))?bw:0,bF=bx===1?bA:E.Deferred(),bz=function(bH,bI,bG){return function(bJ){bI[bH]=this;bG[bH]=arguments.length>1?aF.call(arguments):bJ;if(bG===bE){bF.notifyWith(bI,bG)}else{if(!(--bx)){bF.resolveWith(bI,bG)}}}},bE,bB,bD;if(bw>1){bE=new Array(bw);bB=new Array(bw);bD=new Array(bw);for(;by<bw;by++){if(bC[by]&&E.isFunction(bC[by].promise)){bC[by].promise().done(bz(by,bD,bC)).fail(bF.reject).progress(bz(by,bB,bE))}else{--bx}}}if(!bx){bF.resolveWith(bD,bC)}return bF.promise()}});E.support=(function(){var bI,bH,bF,bG,bz,bE,bD,bB,bA,by,bw,bx=am.createElement("div");bx.setAttribute("className","t");bx.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";bH=bx.getElementsByTagName("*");bF=bx.getElementsByTagName("a")[0];if(!bH||!bF||!bH.length){return{}}bG=am.createElement("select");bz=bG.appendChild(am.createElement("option"));bE=bx.getElementsByTagName("input")[0];bF.style.cssText="top:1px;float:left;opacity:.5";bI={leadingWhitespace:(bx.firstChild.nodeType===3),tbody:!bx.getElementsByTagName("tbody").length,htmlSerialize:!!bx.getElementsByTagName("link").length,style:/top/.test(bF.getAttribute("style")),hrefNormalized:(bF.getAttribute("href")==="/a"),opacity:/^0.5/.test(bF.style.opacity),cssFloat:!!bF.style.cssFloat,checkOn:(bE.value==="on"),optSelected:bz.selected,getSetAttribute:bx.className!=="t",enctype:!!am.createElement("form").enctype,html5Clone:am.createElement("nav").cloneNode(true).outerHTML!=="<:nav></:nav>",boxModel:(am.compatMode==="CSS1Compat"),submitBubbles:true,changeBubbles:true,focusinBubbles:false,deleteExpando:true,noCloneEvent:true,inlineBlockNeedsLayout:false,shrinkWrapBlocks:false,reliableMarginRight:true,boxSizingReliable:true,pixelPosition:false};bE.checked=true;bI.noCloneChecked=bE.cloneNode(true).checked;bG.disabled=true;bI.optDisabled=!bz.disabled;try{delete bx.test}catch(bC){bI.deleteExpando=false}if(!bx.addEventListener&&bx.attachEvent&&bx.fireEvent){bx.attachEvent("onclick",bw=function(){bI.noCloneEvent=false});bx.cloneNode(true).fireEvent("onclick");bx.detachEvent("onclick",bw)}bE=am.createElement("input");bE.value="t";bE.setAttribute("type","radio");bI.radioValue=bE.value==="t";bE.setAttribute("checked","checked");bE.setAttribute("name","t");bx.appendChild(bE);bD=am.createDocumentFragment();bD.appendChild(bx.lastChild);bI.checkClone=bD.cloneNode(true).cloneNode(true).lastChild.checked;bI.appendChecked=bE.checked;bD.removeChild(bE);bD.appendChild(bx);if(bx.attachEvent){for(bA in {submit:true,change:true,focusin:true}){bB="on"+bA;by=(bB in bx);if(!by){bx.setAttribute(bB,"return;");by=(typeof bx[bB]==="function")}bI[bA+"Bubbles"]=by}}E(function(){var bK,bO,bM,bN,bL="padding:0;margin:0;border:0;display:block;overflow:hidden;",bJ=am.getElementsByTagName("body")[0];if(!bJ){return}bK=am.createElement("div");bK.style.cssText="visibility:hidden;border:0;width:0;height:0;position:static;top:0;margin-top:1px";bJ.insertBefore(bK,bJ.firstChild);bO=am.createElement("div");bK.appendChild(bO);bO.innerHTML="<table><tr><td></td><td>t</td></tr></table>";bM=bO.getElementsByTagName("td");bM[0].style.cssText="padding:0;margin:0;border:0;display:none";by=(bM[0].offsetHeight===0);bM[0].style.display="";bM[1].style.display="none";bI.reliableHiddenOffsets=by&&(bM[0].offsetHeight===0);bO.innerHTML="";bO.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;";bI.boxSizing=(bO.offsetWidth===4);bI.doesNotIncludeMarginInBodyOffset=(bJ.offsetTop!==1);if(bf.getComputedStyle){bI.pixelPosition=(bf.getComputedStyle(bO,null)||{}).top!=="1%";bI.boxSizingReliable=(bf.getComputedStyle(bO,null)||{width:"4px"}).width==="4px";bN=am.createElement("div");bN.style.cssText=bO.style.cssText=bL;bN.style.marginRight=bN.style.width="0";bO.style.width="1px";bO.appendChild(bN);bI.reliableMarginRight=!parseFloat((bf.getComputedStyle(bN,null)||{}).marginRight)}if(typeof bO.style.zoom!=="undefined"){bO.innerHTML="";bO.style.cssText=bL+"width:1px;padding:1px;display:inline;zoom:1";bI.inlineBlockNeedsLayout=(bO.offsetWidth===3);bO.style.display="block";bO.style.overflow="visible";bO.innerHTML="<div></div>";bO.firstChild.style.width="5px";bI.shrinkWrapBlocks=(bO.offsetWidth!==3);bK.style.zoom=1}bJ.removeChild(bK);bK=bO=bM=bN=null});bD.removeChild(bx);bH=bF=bG=bz=bE=bD=bx=null;return bI})();var aM=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,at=/([A-Z])/g;E.extend({cache:{},deletedIds:[],uuid:0,expando:"JQXLite"+(E.fn.jqx+Math.random()).replace(/\D/g,""),noData:{embed:true,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:true},hasData:function(bw){bw=bw.nodeType?E.cache[bw[E.expando]]:bw[E.expando];return !!bw&&!O(bw)},data:function(bz,bx,bB,bA){if(!E.acceptData(bz)){return}var bC,bE,bF=E.expando,bD=typeof bx==="string",bG=bz.nodeType,bw=bG?E.cache:bz,by=bG?bz[bF]:bz[bF]&&bF;if((!by||!bw[by]||(!bA&&!bw[by].data))&&bD&&bB===I){return}if(!by){if(bG){bz[bF]=by=E.deletedIds.pop()||E.guid++}else{by=bF}}if(!bw[by]){bw[by]={};if(!bG){bw[by].toJSON=E.noop}}if(typeof bx==="object"||typeof bx==="function"){if(bA){bw[by]=E.extend(bw[by],bx)}else{bw[by].data=E.extend(bw[by].data,bx)}}bC=bw[by];if(!bA){if(!bC.data){bC.data={}}bC=bC.data}if(bB!==I){bC[E.camelCase(bx)]=bB}if(bD){bE=bC[bx];if(bE==null){bE=bC[E.camelCase(bx)]}}else{bE=bC}return bE},removeData:function(bz,bx,bA){if(!E.acceptData(bz)){return}var bD,bC,bB,bE=bz.nodeType,bw=bE?E.cache:bz,by=bE?bz[E.expando]:E.expando;if(!bw[by]){return}if(bx){bD=bA?bw[by]:bw[by].data;if(bD){if(!E.isArray(bx)){if(bx in bD){bx=[bx]}else{bx=E.camelCase(bx);if(bx in bD){bx=[bx]}else{bx=bx.split(" ")}}}for(bC=0,bB=bx.length;bC<bB;bC++){delete bD[bx[bC]]}if(!(bA?O:E.isEmptyObject)(bD)){return}}}if(!bA){delete bw[by].data;if(!O(bw[by])){return}}if(bE){E.cleanData([bz],true)}else{if(E.support.deleteExpando||bw!=bw.window){delete bw[by]}else{bw[by]=null}}},_data:function(bx,bw,by){return E.data(bx,bw,by,true)},acceptData:function(bx){var bw=bx.nodeName&&E.noData[bx.nodeName.toLowerCase()];return !bw||bw!==true&&bx.getAttribute("classid")===bw}});E.fn.extend({data:function(bF,bE){var bA,bx,bD,bw,bz,by=this[0],bC=0,bB=null;if(bF===I){if(this.length){bB=E.data(by);if(by.nodeType===1&&!E._data(by,"parsedAttrs")){bD=by.attributes;for(bz=bD.length;bC<bz;bC++){bw=bD[bC].name;if(!bw.indexOf("data-")){bw=E.camelCase(bw.substring(5));bb(by,bw,bB[bw])}}E._data(by,"parsedAttrs",true)}}return bB}if(typeof bF==="object"){return this.each(function(){E.data(this,bF)})}bA=bF.split(".",2);bA[1]=bA[1]?"."+bA[1]:"";bx=bA[1]+"!";return E.access(this,function(bG){if(bG===I){bB=this.triggerHandler("getData"+bx,[bA[0]]);if(bB===I&&by){bB=E.data(by,bF);bB=bb(by,bF,bB)}return bB===I&&bA[1]?this.data(bA[0]):bB}bA[1]=bG;this.each(function(){var bH=E(this);bH.triggerHandler("setData"+bx,bA);E.data(this,bF,bG);bH.triggerHandler("changeData"+bx,bA)})},null,bE,arguments.length>1,null,false)},removeData:function(bw){return this.each(function(){E.removeData(this,bw)})}});function bb(by,bx,bz){if(bz===I&&by.nodeType===1){var bw="data-"+bx.replace(at,"-$1").toLowerCase();bz=by.getAttribute(bw);if(typeof bz==="string"){try{bz=bz==="true"?true:bz==="false"?false:bz==="null"?null:+bz+""===bz?+bz:aM.test(bz)?E.parseJSON(bz):bz}catch(bA){}E.data(by,bx,bz)}else{bz=I}}return bz}function O(bx){var bw;for(bw in bx){if(bw==="data"&&E.isEmptyObject(bx[bw])){continue}if(bw!=="toJSON"){return false}}return true}E.extend({queue:function(by,bx,bz){var bw;if(by){bx=(bx||"fx")+"queue";bw=E._data(by,bx);if(bz){if(!bw||E.isArray(bz)){bw=E._data(by,bx,E.makeArray(bz))}else{bw.push(bz)}}return bw||[]}},dequeue:function(bB,bA){bA=bA||"fx";var bx=E.queue(bB,bA),bC=bx.length,bz=bx.shift(),bw=E._queueHooks(bB,bA),by=function(){E.dequeue(bB,bA)};if(bz==="inprogress"){bz=bx.shift();bC--}if(bz){if(bA==="fx"){bx.unshift("inprogress")}delete bw.stop;bz.call(bB,by,bw)}if(!bC&&bw){bw.empty.fire()}},_queueHooks:function(by,bx){var bw=bx+"queueHooks";return E._data(by,bw)||E._data(by,bw,{empty:E.Callbacks("once memory").add(function(){E.removeData(by,bx+"queue",true);E.removeData(by,bw,true)})})}});E.fn.extend({queue:function(bw,bx){var by=2;if(typeof bw!=="string"){bx=bw;bw="fx";by--}if(arguments.length<by){return E.queue(this[0],bw)}return bx===I?this:this.each(function(){var bz=E.queue(this,bw,bx);E._queueHooks(this,bw);if(bw==="fx"&&bz[0]!=="inprogress"){E.dequeue(this,bw)}})},dequeue:function(bw){return this.each(function(){E.dequeue(this,bw)})},delay:function(bx,bw){bx=E.fx?E.fx.speeds[bx]||bx:bx;bw=bw||"fx";return this.queue(bw,function(bz,by){var bA=setTimeout(bz,bx);by.stop=function(){clearTimeout(bA)}})},clearQueue:function(bw){return this.queue(bw||"fx",[])},promise:function(by,bC){var bx,bz=1,bD=E.Deferred(),bB=this,bw=this.length,bA=function(){if(!(--bz)){bD.resolveWith(bB,[bB])}};if(typeof by!=="string"){bC=by;by=I}by=by||"fx";while(bw--){bx=E._data(bB[bw],by+"queueHooks");if(bx&&bx.empty){bz++;bx.empty.add(bA)}}bA();return bD.promise(bC)}});var bj,aV,aA,aK=/[\t\r\n]/g,aR=/\r/g,f=/^(?:button|input)$/i,B=/^(?:button|input|object|select|textarea)$/i,j=/^a(?:rea|)$/i,ag=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,C=E.support.getSetAttribute;E.fn.extend({attr:function(bw,bx){return E.access(this,E.attr,bw,bx,arguments.length>1)},removeAttr:function(bw){return this.each(function(){E.removeAttr(this,bw)})},prop:function(bw,bx){return E.access(this,E.prop,bw,bx,arguments.length>1)},removeProp:function(bw){bw=E.propFix[bw]||bw;return this.each(function(){try{this[bw]=I;delete this[bw]}catch(bx){}})},addClass:function(bA){var bC,by,bx,bz,bB,bD,bw;if(E.isFunction(bA)){return this.each(function(bE){E(this).addClass(bA.call(this,bE,this.className))})}if(bA&&typeof bA==="string"){bC=bA.split(ba);for(by=0,bx=this.length;by<bx;by++){bz=this[by];if(bz.nodeType===1){if(!bz.className&&bC.length===1){bz.className=bA}else{bB=" "+bz.className+" ";for(bD=0,bw=bC.length;bD<bw;bD++){if(bB.indexOf(" "+bC[bD]+" ")<0){bB+=bC[bD]+" "}}bz.className=E.trim(bB)}}}}return this},removeClass:function(bC){var bz,bA,bB,bD,bx,by,bw;if(E.isFunction(bC)){return this.each(function(bE){E(this).removeClass(bC.call(this,bE,this.className))})}if((bC&&typeof bC==="string")||bC===I){bz=(bC||"").split(ba);for(by=0,bw=this.length;by<bw;by++){bB=this[by];if(bB.nodeType===1&&bB.className){bA=(" "+bB.className+" ").replace(aK," ");for(bD=0,bx=bz.length;bD<bx;bD++){while(bA.indexOf(" "+bz[bD]+" ")>=0){bA=bA.replace(" "+bz[bD]+" "," ")}}bB.className=bC?E.trim(bA):""}}}return this},toggleClass:function(bz,bx){var by=typeof bz,bw=typeof bx==="boolean";if(E.isFunction(bz)){return this.each(function(bA){E(this).toggleClass(bz.call(this,bA,this.className,bx),bx)})}return this.each(function(){if(by==="string"){var bC,bB=0,bA=E(this),bD=bx,bE=bz.split(ba);while((bC=bE[bB++])){bD=bw?bD:!bA.hasClass(bC);bA[bD?"addClass":"removeClass"](bC)}}else{if(by==="undefined"||by==="boolean"){if(this.className){E._data(this,"__className__",this.className)}this.className=this.className||bz===false?"":E._data(this,"__className__")||""}}})},hasClass:function(bw){var bz=" "+bw+" ",by=0,bx=this.length;for(;by<bx;by++){if(this[by].nodeType===1&&(" "+this[by].className+" ").replace(aK," ").indexOf(bz)>=0){return true}}return false},val:function(bz){var bw,bx,bA,by=this[0];if(!arguments.length){if(by){bw=E.valHooks[by.type]||E.valHooks[by.nodeName.toLowerCase()];if(bw&&"get" in bw&&(bx=bw.get(by,"value"))!==I){return bx}bx=by.value;return typeof bx==="string"?bx.replace(aR,""):bx==null?"":bx}return}bA=E.isFunction(bz);return this.each(function(bC){var bD,bB=E(this);if(this.nodeType!==1){return}if(bA){bD=bz.call(this,bC,bB.val())}else{bD=bz}if(bD==null){bD=""}else{if(typeof bD==="number"){bD+=""}else{if(E.isArray(bD)){bD=E.map(bD,function(bE){return bE==null?"":bE+""})}}}bw=E.valHooks[this.type]||E.valHooks[this.nodeName.toLowerCase()];if(!bw||!("set" in bw)||bw.set(this,bD,"value")===I){this.value=bD}})}});E.extend({valHooks:{option:{get:function(bw){var bx=bw.attributes.value;return !bx||bx.specified?bw.value:bw.text}},select:{get:function(bw){var bC,by,bE=bw.options,bA=bw.selectedIndex,bz=bw.type==="select-one"||bA<0,bD=bz?null:[],bB=bz?bA+1:bE.length,bx=bA<0?bB:bz?bA:0;for(;bx<bB;bx++){by=bE[bx];if((by.selected||bx===bA)&&(E.support.optDisabled?!by.disabled:by.getAttribute("disabled")===null)&&(!by.parentNode.disabled||!E.nodeName(by.parentNode,"optgroup"))){bC=E(by).val();if(bz){return bC}bD.push(bC)}}return bD},set:function(bx,by){var bw=E.makeArray(by);E(bx).find("option").each(function(){this.selected=E.inArray(E(this).val(),bw)>=0});if(!bw.length){bx.selectedIndex=-1}return bw}}},attrFn:{},attr:function(bC,bz,bD,bB){var by,bw,bA,bx=bC.nodeType;if(!bC||bx===3||bx===8||bx===2){return}if(bB&&E.isFunction(E.fn[bz])){return E(bC)[bz](bD)}if(typeof bC.getAttribute==="undefined"){return E.prop(bC,bz,bD)}bA=bx!==1||!E.isXMLDoc(bC);if(bA){bz=bz.toLowerCase();bw=E.attrHooks[bz]||(ag.test(bz)?aV:bj)}if(bD!==I){if(bD===null){E.removeAttr(bC,bz);return}else{if(bw&&"set" in bw&&bA&&(by=bw.set(bC,bD,bz))!==I){return by}else{bC.setAttribute(bz,bD+"");return bD}}}else{if(bw&&"get" in bw&&bA&&(by=bw.get(bC,bz))!==null){return by}else{by=bC.getAttribute(bz);return by===null?I:by}}},removeAttr:function(bz,bB){var bA,bC,bx,bw,by=0;if(bB&&bz.nodeType===1){bC=bB.split(ba);for(;by<bC.length;by++){bx=bC[by];if(bx){bA=E.propFix[bx]||bx;bw=ag.test(bx);if(!bw){E.attr(bz,bx,"")}bz.removeAttribute(C?bx:bA);if(bw&&bA in bz){bz[bA]=false}}}}},attrHooks:{type:{set:function(bw,bx){if(f.test(bw.nodeName)&&bw.parentNode){E.error("type property can't be changed")}else{if(!E.support.radioValue&&bx==="radio"&&E.nodeName(bw,"input")){var by=bw.value;bw.setAttribute("type",bx);if(by){bw.value=by}return bx}}}},value:{get:function(bx,bw){if(bj&&E.nodeName(bx,"button")){return bj.get(bx,bw)}return bw in bx?bx.value:null},set:function(bx,by,bw){if(bj&&E.nodeName(bx,"button")){return bj.set(bx,by,bw)}bx.value=by}}},propFix:{tabindex:"tabIndex",readonly:"readOnly","for":"htmlFor","class":"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(bB,bz,bC){var by,bw,bA,bx=bB.nodeType;if(!bB||bx===3||bx===8||bx===2){return}bA=bx!==1||!E.isXMLDoc(bB);if(bA){bz=E.propFix[bz]||bz;bw=E.propHooks[bz]}if(bC!==I){if(bw&&"set" in bw&&(by=bw.set(bB,bC,bz))!==I){return by}else{return(bB[bz]=bC)}}else{if(bw&&"get" in bw&&(by=bw.get(bB,bz))!==null){return by}else{return bB[bz]}}},propHooks:{tabIndex:{get:function(bx){var bw=bx.getAttributeNode("tabindex");return bw&&bw.specified?parseInt(bw.value,10):B.test(bx.nodeName)||j.test(bx.nodeName)&&bx.href?0:I}}}});aV={get:function(bx,bw){var bz,by=E.prop(bx,bw);return by===true||typeof by!=="boolean"&&(bz=bx.getAttributeNode(bw))&&bz.nodeValue!==false?bw.toLowerCase():I},set:function(bx,bz,bw){var by;if(bz===false){E.removeAttr(bx,bw)}else{by=E.propFix[bw]||bw;if(by in bx){bx[by]=true}bx.setAttribute(bw,bw.toLowerCase())}return bw}};if(!E.support.enctype){E.propFix.enctype="encoding"}var bh=/^(?:textarea|input|select)$/i,p=/^([^\.]*|)(?:\.(.+)|)$/,H=/(?:^|\s)hover(\.\S+|)\b/,aJ=/^key/,bk=/^(?:mouse|contextmenu)|click/,P=/^(?:focusinfocus|focusoutblur)$/,bu=function(bw){return E.event.special.hover?bw:bw.replace(H,"mouseenter$1 mouseleave$1")};E.event={add:function(bz,bD,bK,bB,bA){var bE,bC,bL,bJ,bI,bG,bw,bH,bx,by,bF;if(bz.nodeType===3||bz.nodeType===8||!bD||!bK||!(bE=E._data(bz))){return}if(bK.handler){bx=bK;bK=bx.handler;bA=bx.selector}if(!bK.guid){bK.guid=E.guid++}bL=bE.events;if(!bL){bE.events=bL={}}bC=bE.handle;if(!bC){bE.handle=bC=function(bM){return typeof E!=="undefined"&&(!bM||E.event.triggered!==bM.type)?E.event.dispatch.apply(bC.elem,arguments):I};bC.elem=bz}bD=E.trim(bu(bD)).split(" ");for(bJ=0;bJ<bD.length;bJ++){bI=p.exec(bD[bJ])||[];bG=bI[1];bw=(bI[2]||"").split(".").sort();bF=E.event.special[bG]||{};bG=(bA?bF.delegateType:bF.bindType)||bG;bF=E.event.special[bG]||{};bH=E.extend({type:bG,origType:bI[1],data:bB,handler:bK,guid:bK.guid,selector:bA,needsContext:bA&&E.expr.match.needsContext.test(bA),namespace:bw.join(".")},bx);by=bL[bG];if(!by){by=bL[bG]=[];by.delegateCount=0;if(!bF.setup||bF.setup.call(bz,bB,bw,bC)===false){if(bz.addEventListener){if(bB&&bB.passive!==I){bz.addEventListener(bG,bC,bB)}else{bz.addEventListener(bG,bC,false)}}else{if(bz.attachEvent){bz.attachEvent("on"+bG,bC)}}}}if(bF.add){bF.add.call(bz,bH);if(!bH.handler.guid){bH.handler.guid=bK.guid}}if(bA){by.splice(by.delegateCount++,0,bH)}else{by.push(bH)}E.event.global[bG]=true}bz=null},global:{},remove:function(bz,bE,bK,bA,bD){var bL,bM,bH,by,bx,bB,bC,bJ,bG,bw,bI,bF=E.hasData(bz)&&E._data(bz);if(!bF||!(bJ=bF.events)){return}bE=E.trim(bu(bE||"")).split(" ");for(bL=0;bL<bE.length;bL++){bM=p.exec(bE[bL])||[];bH=by=bM[1];bx=bM[2];if(!bH){for(bH in bJ){E.event.remove(bz,bH+bE[bL],bK,bA,true)}continue}bG=E.event.special[bH]||{};bH=(bA?bG.delegateType:bG.bindType)||bH;bw=bJ[bH]||[];bB=bw.length;bx=bx?new RegExp("(^|\\.)"+bx.split(".").sort().join("\\.(?:.*\\.|)")+"(\\.|$)"):null;for(bC=0;bC<bw.length;bC++){bI=bw[bC];if((bD||by===bI.origType)&&(!bK||bK.guid===bI.guid)&&(!bx||bx.test(bI.namespace))&&(!bA||bA===bI.selector||bA==="**"&&bI.selector)){bw.splice(bC--,1);if(bI.selector){bw.delegateCount--}if(bG.remove){bG.remove.call(bz,bI)}}}if(bw.length===0&&bB!==bw.length){if(!bG.teardown||bG.teardown.call(bz,bx,bF.handle)===false){E.removeEvent(bz,bH,bF.handle)}delete bJ[bH]}}if(E.isEmptyObject(bJ)){delete bF.handle;E.removeData(bz,"events",true)}},customEvent:{getData:true,setData:true,changeData:true},trigger:function(bx,bE,bC,bL){if(bC&&(bC.nodeType===3||bC.nodeType===8)){return}var bw,bz,bF,bJ,bB,bA,bH,bG,bD,bK,bI=bx.type||bx,by=[];if(P.test(bI+E.event.triggered)){return}if(bI.indexOf("!")>=0){bI=bI.slice(0,-1);bz=true}if(bI.indexOf(".")>=0){by=bI.split(".");bI=by.shift();by.sort()}if((!bC||E.event.customEvent[bI])&&!E.event.global[bI]){return}bx=typeof bx==="object"?bx[E.expando]?bx:new E.Event(bI,bx):new E.Event(bI);bx.type=bI;bx.isTrigger=true;bx.exclusive=bz;bx.namespace=by.join(".");bx.namespace_re=bx.namespace?new RegExp("(^|\\.)"+by.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;bA=bI.indexOf(":")<0?"on"+bI:"";if(!bC){bw=E.cache;for(bF in bw){if(bw[bF].events&&bw[bF].events[bI]){E.event.trigger(bx,bE,bw[bF].handle.elem,true)}}return}bx.result=I;if(!bx.target){bx.target=bC}bE=bE!=null?E.makeArray(bE):[];bE.unshift(bx);bH=E.event.special[bI]||{};if(bH.trigger&&bH.trigger.apply(bC,bE)===false){return}bD=[[bC,bH.bindType||bI]];if(!bL&&!bH.noBubble&&!E.isWindow(bC)){bK=bH.delegateType||bI;bJ=P.test(bK+bI)?bC:bC.parentNode;for(bB=bC;bJ;bJ=bJ.parentNode){bD.push([bJ,bK]);bB=bJ}if(bB===(bC.ownerDocument||am)){bD.push([bB.defaultView||bB.parentWindow||bf,bK])}}for(bF=0;bF<bD.length&&!bx.isPropagationStopped();bF++){bJ=bD[bF][0];bx.type=bD[bF][1];bG=(E._data(bJ,"events")||{})[bx.type]&&E._data(bJ,"handle");if(bG){bG.apply(bJ,bE)}bG=bA&&bJ[bA];if(bG&&E.acceptData(bJ)&&bG.apply&&bG.apply(bJ,bE)===false){bx.preventDefault()}}bx.type=bI;if(!bL&&!bx.isDefaultPrevented()){if((!bH._default||bH._default.apply(bC.ownerDocument,bE)===false)&&!(bI==="click"&&E.nodeName(bC,"a"))&&E.acceptData(bC)){if(bA&&bC[bI]&&((bI!=="focus"&&bI!=="blur")||bx.target.offsetWidth!==0)&&!E.isWindow(bC)){bB=bC[bA];if(bB){bC[bA]=null}E.event.triggered=bI;bC[bI]();E.event.triggered=I;if(bB){bC[bA]=bB}}}}return bx.result},dispatch:function(bw){bw=E.event.fix(bw||bf.event);var bD,bC,bM,bG,bF,bx,bE,bK,bz,bL,bA=((E._data(this,"events")||{})[bw.type]||[]),bB=bA.delegateCount,bI=aF.call(arguments),by=!bw.exclusive&&!bw.namespace,bH=E.event.special[bw.type]||{},bJ=[];bI[0]=bw;bw.delegateTarget=this;if(bH.preDispatch&&bH.preDispatch.call(this,bw)===false){return}if(bB&&!(bw.button&&bw.type==="click")){for(bM=bw.target;bM!=this;bM=bM.parentNode||this){if(bM.disabled!==true||bw.type!=="click"){bF={};bE=[];for(bD=0;bD<bB;bD++){bK=bA[bD];bz=bK.selector;if(bF[bz]===I){bF[bz]=bK.needsContext?E(bz,this).index(bM)>=0:E.find(bz,this,null,[bM]).length}if(bF[bz]){bE.push(bK)}}if(bE.length){bJ.push({elem:bM,matches:bE})}}}}if(bA.length>bB){bJ.push({elem:this,matches:bA.slice(bB)})}for(bD=0;bD<bJ.length&&!bw.isPropagationStopped();bD++){bx=bJ[bD];bw.currentTarget=bx.elem;for(bC=0;bC<bx.matches.length&&!bw.isImmediatePropagationStopped();bC++){bK=bx.matches[bC];if(by||(!bw.namespace&&!bK.namespace)||bw.namespace_re&&bw.namespace_re.test(bK.namespace)){bw.data=bK.data;bw.handleObj=bK;bG=((E.event.special[bK.origType]||{}).handle||bK.handler).apply(bx.elem,bI);if(bG!==I){bw.result=bG;if(bG===false){bw.preventDefault();bw.stopPropagation()}}}}}if(bH.postDispatch){bH.postDispatch.call(this,bw)}return bw.result},props:"attrChange attrName relatedNode srcElement altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(bx,bw){if(bx.which==null){bx.which=bw.charCode!=null?bw.charCode:bw.keyCode}return bx}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(bz,by){var bA,bB,bw,bx=by.button,bC=by.fromElement;if(bz.pageX==null&&by.clientX!=null){bA=bz.target.ownerDocument||am;bB=bA.documentElement;bw=bA.body;bz.pageX=by.clientX+(bB&&bB.scrollLeft||bw&&bw.scrollLeft||0)-(bB&&bB.clientLeft||bw&&bw.clientLeft||0);bz.pageY=by.clientY+(bB&&bB.scrollTop||bw&&bw.scrollTop||0)-(bB&&bB.clientTop||bw&&bw.clientTop||0)}if(!bz.relatedTarget&&bC){bz.relatedTarget=bC===bz.target?by.toElement:bC}if(!bz.which&&bx!==I){bz.which=(bx&1?1:(bx&2?3:(bx&4?2:0)))}return bz}},fix:function(by){if(by[E.expando]){return by}var bx,bB,bw=by,bz=E.event.fixHooks[by.type]||{},bA=bz.props?this.props.concat(bz.props):this.props;by=E.Event(bw);for(bx=bA.length;bx;){bB=bA[--bx];by[bB]=bw[bB]}if(!by.target){by.target=bw.srcElement||am}if(by.target.nodeType===3){by.target=by.target.parentNode}by.metaKey=!!by.metaKey;return bz.filter?bz.filter(by,bw):by},special:{load:{noBubble:true},focus:{delegateType:"focusin"},blur:{delegateType:"focusout"},beforeunload:{setup:function(by,bx,bw){if(E.isWindow(this)){this.onbeforeunload=bw}},teardown:function(bx,bw){if(this.onbeforeunload===bw){this.onbeforeunload=null}}}},simulate:function(bx,bz,by,bw){var bA=E.extend(new E.Event(),by,{type:bx,isSimulated:true,originalEvent:{}});if(bw){E.event.trigger(bA,null,bz)}else{E.event.dispatch.call(bz,bA)}if(bA.isDefaultPrevented()){by.preventDefault()}}};E.event.handle=E.event.dispatch;E.removeEvent=am.removeEventListener?function(bx,bw,by){if(bx.removeEventListener){bx.removeEventListener(bw,by,false)}}:function(by,bx,bz){var bw="on"+bx;if(by.detachEvent){if(typeof by[bw]==="undefined"){by[bw]=null}by.detachEvent(bw,bz)}};E.Event=function(bx,bw){if(!(this instanceof E.Event)){return new E.Event(bx,bw)}if(bx&&bx.type){this.originalEvent=bx;this.type=bx.type;this.isDefaultPrevented=(bx.defaultPrevented||bx.returnValue===false||bx.getPreventDefault&&bx.getPreventDefault())?h:bp}else{this.type=bx}if(bw){E.extend(this,bw)}this.timeStamp=bx&&bx.timeStamp||E.now();this[E.expando]=true};function bp(){return false}function h(){return true}E.Event.prototype={preventDefault:function(){this.isDefaultPrevented=h;var bw=this.originalEvent;if(!bw){return}if(bw.preventDefault){bw.preventDefault()}else{bw.returnValue=false}},stopPropagation:function(){this.isPropagationStopped=h;var bw=this.originalEvent;if(!bw){return}if(bw.stopPropagation){bw.stopPropagation()}bw.cancelBubble=true},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=h;this.stopPropagation()},isDefaultPrevented:bp,isPropagationStopped:bp,isImmediatePropagationStopped:bp};E.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(bx,bw){E.event.special[bx]={delegateType:bw,bindType:bw,handle:function(bB){var bz,bD=this,bC=bB.relatedTarget,bA=bB.handleObj,by=bA.selector;if(!bC||(bC!==bD&&!E.contains(bD,bC))){bB.type=bA.origType;bz=bA.handler.apply(this,arguments);bB.type=bw}return bz}}});E.fn.extend({on:function(by,bw,bB,bA,bx){var bC,bz;if(typeof by==="object"){if(typeof bw!=="string"){bB=bB||bw;bw=I}for(bz in by){this.on(bz,bw,bB,by[bz],bx)}return this}if(bB==null&&bA==null){bA=bw;bB=bw=I}else{if(bA==null){if(typeof bw==="string"){bA=bB;bB=I}else{bA=bB;bB=bw;bw=I}}}if(bA===false){bA=bp}else{if(!bA){return this}}if(bx===1){bC=bA;bA=function(bD){E().off(bD);return bC.apply(this,arguments)};bA.guid=bC.guid||(bC.guid=E.guid++)}return this.each(function(){E.event.add(this,by,bA,bB,bw)})},off:function(by,bw,bA){var bx,bz;if(by&&by.preventDefault&&by.handleObj){bx=by.handleObj;E(by.delegateTarget).off(bx.namespace?bx.origType+"."+bx.namespace:bx.origType,bx.selector,bx.handler);return this}if(typeof by==="object"){for(bz in by){this.off(bz,bw,by[bz])}return this}if(bw===false||typeof bw==="function"){bA=bw;bw=I}if(bA===false){bA=bp}return this.each(function(){E.event.remove(this,by,bA,bw)})},delegate:function(bw,bx,bz,by){return this.on(bx,bw,bz,by)},undelegate:function(bw,bx,by){return arguments.length===1?this.off(bw,"**"):this.off(bx,bw||"**",by)},trigger:function(bw,bx){return this.each(function(){E.event.trigger(bw,bx,this)})},triggerHandler:function(bw,bx){if(this[0]){return E.event.trigger(bw,bx,this[0],true)}},toggle:function(bz){var bx=arguments,bw=bz.guid||E.guid++,by=0,bA=function(bB){var bC=(E._data(this,"lastToggle"+bz.guid)||0)%by;E._data(this,"lastToggle"+bz.guid,bC+1);bB.preventDefault();return bx[bC].apply(this,arguments)||false};bA.guid=bw;while(by<bx.length){bx[by++].guid=bw}return this.click(bA)},hover:function(bw,bx){return this.mouseenter(bw).mouseleave(bx||bw)}});E.each(("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu").split(" "),function(bx,bw){E.fn[bw]=function(bz,by){if(by==null){by=bz;bz=null}return arguments.length>0?this.on(bw,null,bz,by):this.trigger(bw)};if(aJ.test(bw)){E.event.fixHooks[bw]=E.event.keyHooks}if(bk.test(bw)){E.event.fixHooks[bw]=E.event.mouseHooks}});
/*!
             * Sizzle CSS Selector Engine
             * Copyright 2012 JQXLite Foundation and other contributors
             * Released under the MIT license
             * http://sizzlejs.com/
             */
(function(cp,bO){var cu,bH,ci,bx,bT,b7,bK,bN,bJ,cg,bG=true,b1="undefined",cw=("sizcache"+Math.random()).replace(".",""),bB=String,bF=cp.document,bI=bF.documentElement,bY=0,bM=0,cb=[].pop,ct=[].push,bS=[].slice,bV=[].indexOf||function(cG){var cF=0,cE=this.length;for(;cF<cE;cF++){if(this[cF]===cG){return cF}}return -1},cy=function(cE,cF){cE[cw]=cF==null||cF;return cE},cC=function(){var cE={},cF=[];return cy(function(cG,cH){if(cF.push(cG)>ci.cacheLength){delete cE[cF.shift()]}return(cE[cG+" "]=cH)},cE)},cr=cC(),cs=cC(),bU=cC(),b5="[\\x20\\t\\r\\n\\f]",bR="(?:\\\\.|[-\\w]|[^\\x00-\\xa0])+",bP=bR.replace("w","w#"),cB="([*^$|!~]?=)",cm="\\["+b5+"*("+bR+")"+b5+"*(?:"+cB+b5+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+bP+")|)|)"+b5+"*\\]",cD=":("+bR+")(?:\\((?:(['\"])((?:\\\\.|[^\\\\])*?)\\2|([^()[\\]]*|(?:(?:"+cm+")|[^:]|\\\\.)*|.*))\\)|)",b6=":(even|odd|eq|gt|lt|nth|first|last)(?:\\("+b5+"*((?:-\\d)?\\d*)"+b5+"*\\)|)(?=[^-]|$)",cq=new RegExp("^"+b5+"+|((?:^|[^\\\\])(?:\\\\.)*)"+b5+"+$","g"),bC=new RegExp("^"+b5+"*,"+b5+"*"),ce=new RegExp("^"+b5+"*([\\x20\\t\\r\\n\\f>+~])"+b5+"*"),cj=new RegExp(cD),cl=/^(?:#([\w\-]+)|(\w+)|\.([\w\-]+))$/,ca=/^:not/,co=/[\x20\t\r\n\f]*[+~]/,cx=/:not\($/,bZ=/h\d/i,ck=/input|select|textarea|button/i,b0=/\\(?!\\)/g,cd={ID:new RegExp("^#("+bR+")"),CLASS:new RegExp("^\\.("+bR+")"),NAME:new RegExp("^\\[name=['\"]?("+bR+")['\"]?\\]"),TAG:new RegExp("^("+bR.replace("w","w*")+")"),ATTR:new RegExp("^"+cm),PSEUDO:new RegExp("^"+cD),POS:new RegExp(b6,"i"),CHILD:new RegExp("^:(only|nth|first|last)-child(?:\\("+b5+"*(even|odd|(([+-]|)(\\d*)n|)"+b5+"*(?:([+-]|)"+b5+"*(\\d+)|))"+b5+"*\\)|)","i"),needsContext:new RegExp("^"+b5+"*[>+~]|"+b6,"i")},ch=function(cE){var cG=bF.createElement("div");try{return cE(cG)}catch(cF){return false}finally{cG=null}},bE=ch(function(cE){cE.appendChild(bF.createComment(""));return !cE.getElementsByTagName("*").length}),b9=ch(function(cE){cE.innerHTML="<a href='#'></a>";return cE.firstChild&&typeof cE.firstChild.getAttribute!==b1&&cE.firstChild.getAttribute("href")==="#"}),bX=ch(function(cF){cF.innerHTML="<select></select>";var cE=typeof cF.lastChild.getAttribute("multiple");return cE!=="boolean"&&cE!=="string"}),b8=ch(function(cE){cE.innerHTML="<div class='hidden e'></div><div class='hidden'></div>";if(!cE.getElementsByClassName||!cE.getElementsByClassName("e").length){return false}cE.lastChild.className="e";return cE.getElementsByClassName("e").length===2}),bw=ch(function(cF){cF.id=cw+0;cF.innerHTML="<a name='"+cw+"'></a><div name='"+cw+"'></div>";bI.insertBefore(cF,bI.firstChild);var cE=bF.getElementsByName&&bF.getElementsByName(cw).length===2+bF.getElementsByName(cw+0).length;bH=!bF.getElementById(cw);bI.removeChild(cF);return cE});try{bS.call(bI.childNodes,0)[0].nodeType}catch(cA){bS=function(cF){var cG,cE=[];for(;(cG=this[cF]);cF++){cE.push(cG)}return cE}}function cn(cH,cE,cJ,cM){cJ=cJ||[];cE=cE||bF;var cK,cF,cL,cG,cI=cE.nodeType;if(!cH||typeof cH!=="string"){return cJ}if(cI!==1&&cI!==9){return[]}cL=bT(cE);if(!cL&&!cM){if((cK=cl.exec(cH))){if((cG=cK[1])){if(cI===9){cF=cE.getElementById(cG);if(cF&&cF.parentNode){if(cF.id===cG){cJ.push(cF);return cJ}}else{return cJ}}else{if(cE.ownerDocument&&(cF=cE.ownerDocument.getElementById(cG))&&b7(cE,cF)&&cF.id===cG){cJ.push(cF);return cJ}}}else{if(cK[2]){ct.apply(cJ,bS.call(cE.getElementsByTagName(cH),0));return cJ}else{if((cG=cK[3])&&b8&&cE.getElementsByClassName){ct.apply(cJ,bS.call(cE.getElementsByClassName(cG),0));return cJ}}}}}return cv(cH.replace(cq,"$1"),cE,cJ,cM,cL)}cn.matches=function(cF,cE){return cn(cF,null,null,cE)};cn.matchesSelector=function(cE,cF){return cn(cF,null,null,[cE]).length>0};function cf(cE){return function(cG){var cF=cG.nodeName.toLowerCase();return cF==="input"&&cG.type===cE}}function bA(cE){return function(cG){var cF=cG.nodeName.toLowerCase();return(cF==="input"||cF==="button")&&cG.type===cE}}function cc(cE){return cy(function(cF){cF=+cF;return cy(function(cG,cK){var cI,cH=cE([],cG.length,cF),cJ=cH.length;while(cJ--){if(cG[(cI=cH[cJ])]){cG[cI]=!(cK[cI]=cG[cI])}}})})}bx=cn.getText=function(cI){var cH,cF="",cG=0,cE=cI.nodeType;if(cE){if(cE===1||cE===9||cE===11){if(typeof cI.textContent==="string"){return cI.textContent}else{for(cI=cI.firstChild;cI;cI=cI.nextSibling){cF+=bx(cI)}}}else{if(cE===3||cE===4){return cI.nodeValue}}}else{for(;(cH=cI[cG]);cG++){cF+=bx(cH)}}return cF};bT=cn.isXML=function(cE){var cF=cE&&(cE.ownerDocument||cE).documentElement;return cF?cF.nodeName!=="HTML":false};b7=cn.contains=bI.contains?function(cF,cE){var cH=cF.nodeType===9?cF.documentElement:cF,cG=cE&&cE.parentNode;return cF===cG||!!(cG&&cG.nodeType===1&&cH.contains&&cH.contains(cG))}:bI.compareDocumentPosition?function(cF,cE){return cE&&!!(cF.compareDocumentPosition(cE)&16)}:function(cF,cE){while((cE=cE.parentNode)){if(cE===cF){return true}}return false};cn.attr=function(cG,cF){var cH,cE=bT(cG);if(!cE){cF=cF.toLowerCase()}if((cH=ci.attrHandle[cF])){return cH(cG)}if(cE||bX){return cG.getAttribute(cF)}cH=cG.getAttributeNode(cF);return cH?typeof cG[cF]==="boolean"?cG[cF]?cF:null:cH.specified?cH.value:null:null};ci=cn.selectors={cacheLength:50,createPseudo:cy,match:cd,attrHandle:b9?{}:{href:function(cE){return cE.getAttribute("href",2)},type:function(cE){return cE.getAttribute("type")}},find:{ID:bH?function(cH,cG,cF){if(typeof cG.getElementById!==b1&&!cF){var cE=cG.getElementById(cH);return cE&&cE.parentNode?[cE]:[]}}:function(cH,cG,cF){if(typeof cG.getElementById!==b1&&!cF){var cE=cG.getElementById(cH);return cE?cE.id===cH||typeof cE.getAttributeNode!==b1&&cE.getAttributeNode("id").value===cH?[cE]:bO:[]}},TAG:bE?function(cE,cF){if(typeof cF.getElementsByTagName!==b1){return cF.getElementsByTagName(cE)}}:function(cE,cI){var cH=cI.getElementsByTagName(cE);if(cE==="*"){var cJ,cG=[],cF=0;for(;(cJ=cH[cF]);cF++){if(cJ.nodeType===1){cG.push(cJ)}}return cG}return cH},NAME:bw&&function(cE,cF){if(typeof cF.getElementsByName!==b1){return cF.getElementsByName(name)}},CLASS:b8&&function(cG,cF,cE){if(typeof cF.getElementsByClassName!==b1&&!cE){return cF.getElementsByClassName(cG)}}},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(cE){cE[1]=cE[1].replace(b0,"");cE[3]=(cE[4]||cE[5]||"").replace(b0,"");if(cE[2]==="~="){cE[3]=" "+cE[3]+" "}return cE.slice(0,4)},CHILD:function(cE){cE[1]=cE[1].toLowerCase();if(cE[1]==="nth"){if(!cE[2]){cn.error(cE[0])}cE[3]=+(cE[3]?cE[4]+(cE[5]||1):2*(cE[2]==="even"||cE[2]==="odd"));cE[4]=+((cE[6]+cE[7])||cE[2]==="odd")}else{if(cE[2]){cn.error(cE[0])}}return cE},PSEUDO:function(cF){var cG,cE;if(cd.CHILD.test(cF[0])){return null}if(cF[3]){cF[2]=cF[3]}else{if((cG=cF[4])){if(cj.test(cG)&&(cE=by(cG,true))&&(cE=cG.indexOf(")",cG.length-cE)-cG.length)){cG=cG.slice(0,cE);cF[0]=cF[0].slice(0,cE)}cF[2]=cG}}return cF.slice(0,3)}},filter:{ID:bH?function(cE){cE=cE.replace(b0,"");return function(cF){return cF.getAttribute("id")===cE}}:function(cE){cE=cE.replace(b0,"");return function(cG){var cF=typeof cG.getAttributeNode!==b1&&cG.getAttributeNode("id");return cF&&cF.value===cE}},TAG:function(cE){if(cE==="*"){return function(){return true}}cE=cE.replace(b0,"").toLowerCase();return function(cF){return cF.nodeName&&cF.nodeName.toLowerCase()===cE}},CLASS:function(cE){var cF=cr[cw][cE+" "];return cF||(cF=new RegExp("(^|"+b5+")"+cE+"("+b5+"|$)"))&&cr(cE,function(cG){return cF.test(cG.className||(typeof cG.getAttribute!==b1&&cG.getAttribute("class"))||"")})},ATTR:function(cG,cF,cE){return function(cJ,cI){var cH=cn.attr(cJ,cG);if(cH==null){return cF==="!="}if(!cF){return true}cH+="";return cF==="="?cH===cE:cF==="!="?cH!==cE:cF==="^="?cE&&cH.indexOf(cE)===0:cF==="*="?cE&&cH.indexOf(cE)>-1:cF==="$="?cE&&cH.substr(cH.length-cE.length)===cE:cF==="~="?(" "+cH+" ").indexOf(cE)>-1:cF==="|="?cH===cE||cH.substr(0,cE.length+1)===cE+"-":false}},CHILD:function(cE,cG,cH,cF){if(cE==="nth"){return function(cK){var cJ,cL,cI=cK.parentNode;if(cH===1&&cF===0){return true}if(cI){cL=0;for(cJ=cI.firstChild;cJ;cJ=cJ.nextSibling){if(cJ.nodeType===1){cL++;if(cK===cJ){break}}}}cL-=cF;return cL===cH||(cL%cH===0&&cL/cH>=0)}}return function(cJ){var cI=cJ;switch(cE){case"only":case"first":while((cI=cI.previousSibling)){if(cI.nodeType===1){return false}}if(cE==="first"){return true}cI=cJ;case"last":while((cI=cI.nextSibling)){if(cI.nodeType===1){return false}}return true}}},PSEUDO:function(cH,cG){var cE,cF=ci.pseudos[cH]||ci.setFilters[cH.toLowerCase()]||cn.error("unsupported pseudo: "+cH);if(cF[cw]){return cF(cG)}if(cF.length>1){cE=[cH,cH,"",cG];return ci.setFilters.hasOwnProperty(cH.toLowerCase())?cy(function(cK,cM){var cJ,cI=cF(cK,cG),cL=cI.length;while(cL--){cJ=bV.call(cK,cI[cL]);cK[cJ]=!(cM[cJ]=cI[cL])}}):function(cI){return cF(cI,0,cE)}}return cF}},pseudos:{not:cy(function(cE){var cF=[],cG=[],cH=bK(cE.replace(cq,"$1"));return cH[cw]?cy(function(cJ,cO,cM,cK){var cN,cI=cH(cJ,null,cK,[]),cL=cJ.length;while(cL--){if((cN=cI[cL])){cJ[cL]=!(cO[cL]=cN)}}}):function(cK,cJ,cI){cF[0]=cK;cH(cF,null,cI,cG);return !cG.pop()}}),has:cy(function(cE){return function(cF){return cn(cE,cF).length>0}}),contains:cy(function(cE){return function(cF){return(cF.textContent||cF.innerText||bx(cF)).indexOf(cE)>-1}}),enabled:function(cE){return cE.disabled===false},disabled:function(cE){return cE.disabled===true},checked:function(cE){var cF=cE.nodeName.toLowerCase();return(cF==="input"&&!!cE.checked)||(cF==="option"&&!!cE.selected)},selected:function(cE){if(cE.parentNode){cE.parentNode.selectedIndex}return cE.selected===true},parent:function(cE){return !ci.pseudos.empty(cE)},empty:function(cF){var cE;cF=cF.firstChild;while(cF){if(cF.nodeName>"@"||(cE=cF.nodeType)===3||cE===4){return false}cF=cF.nextSibling}return true},header:function(cE){return bZ.test(cE.nodeName)},text:function(cG){var cF,cE;return cG.nodeName.toLowerCase()==="input"&&(cF=cG.type)==="text"&&((cE=cG.getAttribute("type"))==null||cE.toLowerCase()===cF)},radio:cf("radio"),checkbox:cf("checkbox"),file:cf("file"),password:cf("password"),image:cf("image"),submit:bA("submit"),reset:bA("reset"),button:function(cF){var cE=cF.nodeName.toLowerCase();return cE==="input"&&cF.type==="button"||cE==="button"},input:function(cE){return ck.test(cE.nodeName)},focus:function(cE){var cF=cE.ownerDocument;return cE===cF.activeElement&&(!cF.hasFocus||cF.hasFocus())&&!!(cE.type||cE.href||~cE.tabIndex)},active:function(cE){return cE===cE.ownerDocument.activeElement},first:cc(function(){return[0]}),last:cc(function(cE,cF){return[cF-1]}),eq:cc(function(cE,cG,cF){return[cF<0?cF+cG:cF]}),even:cc(function(cE,cG){for(var cF=0;cF<cG;cF+=2){cE.push(cF)}return cE}),odd:cc(function(cE,cG){for(var cF=1;cF<cG;cF+=2){cE.push(cF)}return cE}),lt:cc(function(cE,cH,cG){for(var cF=cG<0?cG+cH:cG;--cF>=0;){cE.push(cF)}return cE}),gt:cc(function(cE,cH,cG){for(var cF=cG<0?cG+cH:cG;++cF<cH;){cE.push(cF)}return cE})}};function bz(cF,cE,cG){if(cF===cE){return cG}var cH=cF.nextSibling;while(cH){if(cH===cE){return -1}cH=cH.nextSibling}return 1}bN=bI.compareDocumentPosition?function(cF,cE){if(cF===cE){bJ=true;return 0}return(!cF.compareDocumentPosition||!cE.compareDocumentPosition?cF.compareDocumentPosition:cF.compareDocumentPosition(cE)&4)?-1:1}:function(cM,cL){if(cM===cL){bJ=true;return 0}else{if(cM.sourceIndex&&cL.sourceIndex){return cM.sourceIndex-cL.sourceIndex}}var cJ,cF,cG=[],cE=[],cI=cM.parentNode,cK=cL.parentNode,cN=cI;if(cI===cK){return bz(cM,cL)}else{if(!cI){return -1}else{if(!cK){return 1}}}while(cN){cG.unshift(cN);cN=cN.parentNode}cN=cK;while(cN){cE.unshift(cN);cN=cN.parentNode}cJ=cG.length;cF=cE.length;for(var cH=0;cH<cJ&&cH<cF;cH++){if(cG[cH]!==cE[cH]){return bz(cG[cH],cE[cH])}}return cH===cJ?bz(cM,cE[cH],-1):bz(cG[cH],cL,1)};[0,0].sort(bN);bG=!bJ;cn.uniqueSort=function(cG){var cH,cI=[],cF=1,cE=0;bJ=bG;cG.sort(bN);if(bJ){for(;(cH=cG[cF]);cF++){if(cH===cG[cF-1]){cE=cI.push(cF)}}while(cE--){cG.splice(cI[cE],1)}}return cG};cn.error=function(cE){throw new Error("Syntax error, unrecognized expression: "+cE)};function by(cI,cN){var cF,cJ,cL,cM,cK,cG,cE,cH=cs[cw][cI+" "];if(cH){return cN?0:cH.slice(0)}cK=cI;cG=[];cE=ci.preFilter;while(cK){if(!cF||(cJ=bC.exec(cK))){if(cJ){cK=cK.slice(cJ[0].length)||cK}cG.push(cL=[])}cF=false;if((cJ=ce.exec(cK))){cL.push(cF=new bB(cJ.shift()));cK=cK.slice(cF.length);cF.type=cJ[0].replace(cq," ")}for(cM in ci.filter){if((cJ=cd[cM].exec(cK))&&(!cE[cM]||(cJ=cE[cM](cJ)))){cL.push(cF=new bB(cJ.shift()));cK=cK.slice(cF.length);cF.type=cM;cF.matches=cJ}}if(!cF){break}}return cN?cK.length:cK?cn.error(cI):cs(cI,cG).slice(0)}function b3(cI,cG,cH){var cE=cG.dir,cJ=cH&&cG.dir==="parentNode",cF=bM++;return cG.first?function(cM,cL,cK){while((cM=cM[cE])){if(cJ||cM.nodeType===1){return cI(cM,cL,cK)}}}:function(cN,cM,cL){if(!cL){var cK,cO=bY+" "+cF+" ",cP=cO+cu;while((cN=cN[cE])){if(cJ||cN.nodeType===1){if((cK=cN[cw])===cP){return cN.sizset}else{if(typeof cK==="string"&&cK.indexOf(cO)===0){if(cN.sizset){return cN}}else{cN[cw]=cP;if(cI(cN,cM,cL)){cN.sizset=true;return cN}cN.sizset=false}}}}}else{while((cN=cN[cE])){if(cJ||cN.nodeType===1){if(cI(cN,cM,cL)){return cN}}}}}}function bL(cE){return cE.length>1?function(cI,cH,cF){var cG=cE.length;while(cG--){if(!cE[cG](cI,cH,cF)){return false}}return true}:cE[0]}function b2(cE,cF,cG,cH,cK){var cI,cN=[],cJ=0,cL=cE.length,cM=cF!=null;for(;cJ<cL;cJ++){if((cI=cE[cJ])){if(!cG||cG(cI,cH,cK)){cN.push(cI);if(cM){cF.push(cJ)}}}}return cN}function cz(cG,cF,cI,cH,cJ,cE){if(cH&&!cH[cw]){cH=cz(cH)}if(cJ&&!cJ[cw]){cJ=cz(cJ,cE)}return cy(function(cU,cR,cM,cT){var cW,cS,cO,cN=[],cV=[],cL=cR.length,cK=cU||bW(cF||"*",cM.nodeType?[cM]:cM,[]),cP=cG&&(cU||!cF)?b2(cK,cN,cG,cM,cT):cK,cQ=cI?cJ||(cU?cG:cL||cH)?[]:cR:cP;if(cI){cI(cP,cQ,cM,cT)}if(cH){cW=b2(cQ,cV);cH(cW,[],cM,cT);cS=cW.length;while(cS--){if((cO=cW[cS])){cQ[cV[cS]]=!(cP[cV[cS]]=cO)}}}if(cU){if(cJ||cG){if(cJ){cW=[];cS=cQ.length;while(cS--){if((cO=cQ[cS])){cW.push((cP[cS]=cO))}}cJ(null,(cQ=[]),cW,cT)}cS=cQ.length;while(cS--){if((cO=cQ[cS])&&(cW=cJ?bV.call(cU,cO):cN[cS])>-1){cU[cW]=!(cR[cW]=cO)}}}}else{cQ=b2(cQ===cR?cQ.splice(cL,cQ.length):cQ);if(cJ){cJ(null,cR,cQ,cT)}else{ct.apply(cR,cQ)}}})}function b4(cK){var cF,cI,cG,cJ=cK.length,cN=ci.relative[cK[0].type],cO=cN||ci.relative[" "],cH=cN?1:0,cL=b3(function(cP){return cP===cF},cO,true),cM=b3(function(cP){return bV.call(cF,cP)>-1},cO,true),cE=[function(cR,cQ,cP){return(!cN&&(cP||cQ!==cg))||((cF=cQ).nodeType?cL(cR,cQ,cP):cM(cR,cQ,cP))}];for(;cH<cJ;cH++){if((cI=ci.relative[cK[cH].type])){cE=[b3(bL(cE),cI)]}else{cI=ci.filter[cK[cH].type].apply(null,cK[cH].matches);if(cI[cw]){cG=++cH;for(;cG<cJ;cG++){if(ci.relative[cK[cG].type]){break}}return cz(cH>1&&bL(cE),cH>1&&cK.slice(0,cH-1).join("").replace(cq,"$1"),cI,cH<cG&&b4(cK.slice(cH,cG)),cG<cJ&&b4((cK=cK.slice(cG))),cG<cJ&&cK.join(""))}cE.push(cI)}}return bL(cE)}function bD(cH,cG){var cE=cG.length>0,cI=cH.length>0,cF=function(cS,cM,cR,cQ,cY){var cN,cO,cT,cX=[],cW=0,cP="0",cJ=cS&&[],cU=cY!=null,cV=cg,cL=cS||cI&&ci.find.TAG("*",cY&&cM.parentNode||cM),cK=(bY+=cV==null?1:Math.E);if(cU){cg=cM!==bF&&cM;cu=cF.el}for(;(cN=cL[cP])!=null;cP++){if(cI&&cN){for(cO=0;(cT=cH[cO]);cO++){if(cT(cN,cM,cR)){cQ.push(cN);break}}if(cU){bY=cK;cu=++cF.el}}if(cE){if((cN=!cT&&cN)){cW--}if(cS){cJ.push(cN)}}}cW+=cP;if(cE&&cP!==cW){for(cO=0;(cT=cG[cO]);cO++){cT(cJ,cX,cM,cR)}if(cS){if(cW>0){while(cP--){if(!(cJ[cP]||cX[cP])){cX[cP]=cb.call(cQ)}}}cX=b2(cX)}ct.apply(cQ,cX);if(cU&&!cS&&cX.length>0&&(cW+cG.length)>1){cn.uniqueSort(cQ)}}if(cU){bY=cK;cg=cV}return cJ};cF.el=0;return cE?cy(cF):cF}bK=cn.compile=function(cE,cJ){var cG,cF=[],cI=[],cH=bU[cw][cE+" "];if(!cH){if(!cJ){cJ=by(cE)}cG=cJ.length;while(cG--){cH=b4(cJ[cG]);if(cH[cw]){cF.push(cH)}else{cI.push(cH)}}cH=bU(cE,bD(cI,cF))}return cH};function bW(cF,cI,cH){var cG=0,cE=cI.length;for(;cG<cE;cG++){cn(cF,cI[cG],cH)}return cH}function cv(cG,cE,cI,cM,cL){var cJ,cP,cF,cO,cN,cK=by(cG),cH=cK.length;if(!cM){if(cK.length===1){cP=cK[0]=cK[0].slice(0);if(cP.length>2&&(cF=cP[0]).type==="ID"&&cE.nodeType===9&&!cL&&ci.relative[cP[1].type]){cE=ci.find.ID(cF.matches[0].replace(b0,""),cE,cL)[0];if(!cE){return cI}cG=cG.slice(cP.shift().length)}for(cJ=cd.POS.test(cG)?-1:cP.length-1;cJ>=0;cJ--){cF=cP[cJ];if(ci.relative[(cO=cF.type)]){break}if((cN=ci.find[cO])){if((cM=cN(cF.matches[0].replace(b0,""),co.test(cP[0].type)&&cE.parentNode||cE,cL))){cP.splice(cJ,1);cG=cM.length&&cP.join("");if(!cG){ct.apply(cI,bS.call(cM,0));return cI}break}}}}}bK(cG,cK)(cM,cE,cL,cI,co.test(cG));return cI}if(bF.querySelectorAll){(function(){var cJ,cK=cv,cI=/'|\\/g,cG=/\=[\x20\t\r\n\f]*([^'"\]]*)[\x20\t\r\n\f]*\]/g,cF=[":focus"],cE=[":active"],cH=bI.matchesSelector||bI.mozMatchesSelector||bI.webkitMatchesSelector||bI.oMatchesSelector||bI.msMatchesSelector;ch(function(cL){cL.innerHTML="<select><option selected=''></option></select>";if(!cL.querySelectorAll("[selected]").length){cF.push("\\["+b5+"*(?:checked|disabled|ismap|multiple|readonly|selected|value)")}if(!cL.querySelectorAll(":checked").length){cF.push(":checked")}});ch(function(cL){cL.innerHTML="<p test=''></p>";if(cL.querySelectorAll("[test^='']").length){cF.push("[*^$]="+b5+"*(?:\"\"|'')")}cL.innerHTML="<input type='hidden'/>";if(!cL.querySelectorAll(":enabled").length){cF.push(":enabled",":disabled")}});cF=new RegExp(cF.join("|"));cv=function(cR,cM,cT,cW,cV){if(!cW&&!cV&&!cF.test(cR)){var cP,cU,cO=true,cL=cw,cN=cM,cS=cM.nodeType===9&&cR;if(cM.nodeType===1&&cM.nodeName.toLowerCase()!=="object"){cP=by(cR);if((cO=cM.getAttribute("id"))){cL=cO.replace(cI,"\\$&")}else{cM.setAttribute("id",cL)}cL="[id='"+cL+"'] ";cU=cP.length;while(cU--){cP[cU]=cL+cP[cU].join("")}cN=co.test(cR)&&cM.parentNode||cM;cS=cP.join(",")}if(cS){try{ct.apply(cT,bS.call(cN.querySelectorAll(cS),0));return cT}catch(cQ){}finally{if(!cO){cM.removeAttribute("id")}}}}return cK(cR,cM,cT,cW,cV)};if(cH){ch(function(cM){cJ=cH.call(cM,"div");try{cH.call(cM,"[test!='']:sizzle");cE.push("!=",cD)}catch(cL){}});cE=new RegExp(cE.join("|"));cn.matchesSelector=function(cM,cO){cO=cO.replace(cG,"='$1']");if(!bT(cM)&&!cE.test(cO)&&!cF.test(cO)){try{var cL=cH.call(cM,cO);if(cL||cJ||cM.document&&cM.document.nodeType!==11){return cL}}catch(cN){}}return cn(cO,null,null,[cM]).length>0}}})()}ci.pseudos.nth=ci.pseudos.eq;function bQ(){}ci.filters=bQ.prototype=ci.pseudos;ci.setFilters=new bQ();cn.attr=E.attr;E.find=cn;E.expr=cn.selectors;E.expr[":"]=E.expr.pseudos;E.unique=cn.uniqueSort;E.text=cn.getText;E.isXMLDoc=cn.isXML;E.contains=cn.contains})(bf);var W=/Until$/,ai=/^(?:parents|prev(?:Until|All))/,bs=/^.[^:#\[\.,]*$/,aS=E.expr.match.needsContext,aq={children:true,contents:true,next:true,prev:true};E.fn.extend({find:function(bw){var bA,bx,bC,bD,bB,bz,by=this;if(typeof bw!=="string"){return E(bw).filter(function(){for(bA=0,bx=by.length;bA<bx;bA++){if(E.contains(by[bA],this)){return true}}})}bz=this.pushStack("","find",bw);for(bA=0,bx=this.length;bA<bx;bA++){bC=bz.length;E.find(bw,this[bA],bz);if(bA>0){for(bD=bC;bD<bz.length;bD++){for(bB=0;bB<bC;bB++){if(bz[bB]===bz[bD]){bz.splice(bD--,1);break}}}}}return bz},has:function(bz){var by,bx=E(bz,this),bw=bx.length;return this.filter(function(){for(by=0;by<bw;by++){if(E.contains(this,bx[by])){return true}}})},not:function(bw){return this.pushStack(aB(this,bw,false),"not",bw)},filter:function(bw){return this.pushStack(aB(this,bw,true),"filter",bw)},is:function(bw){return !!bw&&(typeof bw==="string"?aS.test(bw)?E(bw,this.context).index(this[0])>=0:E.filter(bw,this).length>0:this.filter(bw).length>0)},closest:function(bA,bz){var bB,by=0,bw=this.length,bx=[],bC=aS.test(bA)||typeof bA!=="string"?E(bA,bz||this.context):0;for(;by<bw;by++){bB=this[by];while(bB&&bB.ownerDocument&&bB!==bz&&bB.nodeType!==11){if(bC?bC.index(bB)>-1:E.find.matchesSelector(bB,bA)){bx.push(bB);break}bB=bB.parentNode}}bx=bx.length>1?E.unique(bx):bx;return this.pushStack(bx,"closest",bA)},index:function(bw){if(!bw){return(this[0]&&this[0].parentNode)?this.prevAll().length:-1}if(typeof bw==="string"){return E.inArray(this[0],E(bw))}return E.inArray(bw.jqx?bw[0]:bw,this)},add:function(bw,bx){var bz=typeof bw==="string"?E(bw,bx):E.makeArray(bw&&bw.nodeType?[bw]:bw),by=E.merge(this.get(),bz);return this.pushStack(z(bz[0])||z(by[0])?by:E.unique(by))},addBack:function(bw){return this.add(bw==null?this.prevObject:this.prevObject.filter(bw))}});E.fn.andSelf=E.fn.addBack;function z(bw){return !bw||!bw.parentNode||bw.parentNode.nodeType===11}function aD(bx,bw){do{bx=bx[bw]}while(bx&&bx.nodeType!==1);return bx}E.each({parent:function(bx){var bw=bx.parentNode;return bw&&bw.nodeType!==11?bw:null},parents:function(bw){return E.dir(bw,"parentNode")},parentsUntil:function(bx,bw,by){return E.dir(bx,"parentNode",by)},next:function(bw){return aD(bw,"nextSibling")},prev:function(bw){return aD(bw,"previousSibling")},nextAll:function(bw){return E.dir(bw,"nextSibling")},prevAll:function(bw){return E.dir(bw,"previousSibling")},nextUntil:function(bx,bw,by){return E.dir(bx,"nextSibling",by)},prevUntil:function(bx,bw,by){return E.dir(bx,"previousSibling",by)},siblings:function(bw){return E.sibling((bw.parentNode||{}).firstChild,bw)},children:function(bw){return E.sibling(bw.firstChild)},contents:function(bw){return E.nodeName(bw,"iframe")?bw.contentDocument||bw.contentWindow.document:E.merge([],bw.childNodes)}},function(bw,bx){E.fn[bw]=function(bA,by){var bz=E.map(this,bx,bA);if(!W.test(bw)){by=bA}if(by&&typeof by==="string"){bz=E.filter(by,bz)}bz=this.length>1&&!aq[bw]?E.unique(bz):bz;if(this.length>1&&ai.test(bw)){bz=bz.reverse()}return this.pushStack(bz,bw,aF.call(arguments).join(","))}});E.extend({filter:function(by,bw,bx){if(bx){by=":not("+by+")"}return bw.length===1?E.find.matchesSelector(bw[0],by)?[bw[0]]:[]:E.find.matches(by,bw)},dir:function(by,bx,bA){var bw=[],bz=by[bx];while(bz&&bz.nodeType!==9&&(bA===I||bz.nodeType!==1||!E(bz).is(bA))){if(bz.nodeType===1){bw.push(bz)}bz=bz[bx]}return bw},sibling:function(by,bx){var bw=[];for(;by;by=by.nextSibling){if(by.nodeType===1&&by!==bx){bw.push(by)}}return bw}});function aB(bz,by,bw){by=by||0;if(E.isFunction(by)){return E.grep(bz,function(bB,bA){var bC=!!by.call(bB,bA,bB);return bC===bw})}else{if(by.nodeType){return E.grep(bz,function(bB,bA){return(bB===by)===bw})}else{if(typeof by==="string"){var bx=E.grep(bz,function(bA){return bA.nodeType===1});if(bs.test(by)){return E.filter(by,bx,!bw)}else{by=E.filter(by,bx)}}}}return E.grep(bz,function(bB,bA){return(E.inArray(bB,by)>=0)===bw})}function c(bw){var by=aL.split("|"),bx=bw.createDocumentFragment();if(bx.createElement){while(by.length){bx.createElement(by.pop())}}return bx}var aL="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",ac=/ JQXLite\d+="(?:null|\d+)"/g,aj=/^\s+/,N=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,e=/<([\w:]+)/,x=/<tbody/i,R=/<|&#?\w+;/,Y=/<(?:script|style|link)/i,K=/<(?:script|object|embed|option|style)/i,ae=new RegExp("<(?:"+aL+")[\\s/>]","i"),T=/^(?:checkbox|radio)$/,q=/checked\s*(?:[^=]|=\s*.checked.)/i,br=/\/(java|ecma)script/i,aI=/^\s*<!(?:\[CDATA\[|\-\-)|[\]\-]{2}>\s*$/g,ao={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]},V=c(am),bi=V.appendChild(am.createElement("div"));ao.optgroup=ao.option;ao.tbody=ao.tfoot=ao.colgroup=ao.caption=ao.thead;ao.th=ao.td;if(!E.support.htmlSerialize){ao._default=[1,"X<div>","</div>"]}E.fn.extend({text:function(bw){return E.access(this,function(bx){return bx===I?E.text(this):this.empty().append((this[0]&&this[0].ownerDocument||am).createTextNode(bx))},null,bw,arguments.length)},wrapAll:function(bw){if(E.isFunction(bw)){return this.each(function(by){E(this).wrapAll(bw.call(this,by))})}if(this[0]){var bx=E(bw,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){bx.insertBefore(this[0])}bx.map(function(){var by=this;while(by.firstChild&&by.firstChild.nodeType===1){by=by.firstChild}return by}).append(this)}return this},wrapInner:function(bw){if(E.isFunction(bw)){return this.each(function(bx){E(this).wrapInner(bw.call(this,bx))})}return this.each(function(){var bx=E(this),by=bx.contents();if(by.length){by.wrapAll(bw)}else{bx.append(bw)}})},wrap:function(bw){var bx=E.isFunction(bw);return this.each(function(by){E(this).wrapAll(bx?bw.call(this,by):bw)})},unwrap:function(){return this.parent().each(function(){if(!E.nodeName(this,"body")){E(this).replaceWith(this.childNodes)}}).end()},append:function(){return this.domManip(arguments,true,function(bw){if(this.nodeType===1||this.nodeType===11){this.appendChild(bw)}})},prepend:function(){return this.domManip(arguments,true,function(bw){if(this.nodeType===1||this.nodeType===11){this.insertBefore(bw,this.firstChild)}})},before:function(){if(!z(this[0])){return this.domManip(arguments,false,function(bx){this.parentNode.insertBefore(bx,this)})}if(arguments.length){var bw=E.clean(arguments);return this.pushStack(E.merge(bw,this),"before",this.selector)}},after:function(){if(!z(this[0])){return this.domManip(arguments,false,function(bx){this.parentNode.insertBefore(bx,this.nextSibling)})}if(arguments.length){var bw=E.clean(arguments);return this.pushStack(E.merge(this,bw),"after",this.selector)}},remove:function(bw,bz){var by,bx=0;for(;(by=this[bx])!=null;bx++){if(!bw||E.filter(bw,[by]).length){if(!bz&&by.nodeType===1){E.cleanData(by.getElementsByTagName("*"));E.cleanData([by])}if(by.parentNode){by.parentNode.removeChild(by)}}}return this},empty:function(){var bx,bw=0;for(;(bx=this[bw])!=null;bw++){if(bx.nodeType===1){E.cleanData(bx.getElementsByTagName("*"))}while(bx.firstChild){bx.removeChild(bx.firstChild)}}return this},clone:function(bx,bw){bx=bx==null?false:bx;bw=bw==null?bx:bw;return this.map(function(){return E.clone(this,bx,bw)})},html:function(bw){return E.access(this,function(bA){var bz=this[0]||{},by=0,bx=this.length;if(bA===I){return bz.nodeType===1?bz.innerHTML.replace(ac,""):I}if(typeof bA==="string"&&!Y.test(bA)&&(E.support.htmlSerialize||!ae.test(bA))&&(E.support.leadingWhitespace||!aj.test(bA))&&!ao[(e.exec(bA)||["",""])[1].toLowerCase()]){bA=bA.replace(N,"<$1></$2>");try{for(;by<bx;by++){bz=this[by]||{};if(bz.nodeType===1){E.cleanData(bz.getElementsByTagName("*"));bz.innerHTML=bA}}bz=0}catch(bB){}}if(bz){this.empty().append(bA)}},null,bw,arguments.length)},replaceWith:function(bw){if(!z(this[0])){if(E.isFunction(bw)){return this.each(function(bz){var by=E(this),bx=by.html();by.replaceWith(bw.call(this,bz,bx))})}if(typeof bw!=="string"){bw=E(bw).detach()}return this.each(function(){var by=this.nextSibling,bx=this.parentNode;E(this).remove();if(by){E(by).before(bw)}else{E(bx).append(bw)}})}return this.length?this.pushStack(E(E.isFunction(bw)?bw():bw),"replaceWith",bw):this},detach:function(bw){return this.remove(bw,true)},domManip:function(bC,bG,bF){bC=[].concat.apply([],bC);var by,bA,bB,bE,bz=0,bD=bC[0],bx=[],bw=this.length;if(!E.support.checkClone&&bw>1&&typeof bD==="string"&&q.test(bD)){return this.each(function(){E(this).domManip(bC,bG,bF)})}if(E.isFunction(bD)){return this.each(function(bI){var bH=E(this);bC[0]=bD.call(this,bI,bG?bH.html():I);bH.domManip(bC,bG,bF)})}if(this[0]){by=E.buildFragment(bC,this,bx);bB=by.fragment;bA=bB.firstChild;if(bB.childNodes.length===1){bB=bA}if(bA){bG=bG&&E.nodeName(bA,"tr");for(bE=by.cacheable||bw-1;bz<bw;bz++){bF.call(bG&&E.nodeName(this[bz],"table")?a5(this[bz],"tbody"):this[bz],bz===bE?bB:E.clone(bB,true,true))}}bB=bA=null;if(bx.length){E.each(bx,function(bH,bI){if(bI.src){if(E.ajax){E.ajax({url:bI.src,type:"GET",dataType:"script",async:false,global:false,"throws":true})}else{E.error("no ajax")}}else{E.globalEval((bI.text||bI.textContent||bI.innerHTML||"").replace(aI,""))}if(bI.parentNode){bI.parentNode.removeChild(bI)}})}}return this}});function a5(bx,bw){return bx.getElementsByTagName(bw)[0]||bx.appendChild(bx.ownerDocument.createElement(bw))}function t(bD,bx){if(bx.nodeType!==1||!E.hasData(bD)){return}var bA,bz,bw,bC=E._data(bD),bB=E._data(bx,bC),by=bC.events;if(by){delete bB.handle;bB.events={};for(bA in by){for(bz=0,bw=by[bA].length;bz<bw;bz++){E.event.add(bx,bA,by[bA][bz])}}}if(bB.data){bB.data=E.extend({},bB.data)}}function ad(bx,bw){var by;if(bw.nodeType!==1){return}if(bw.clearAttributes){bw.clearAttributes()}if(bw.mergeAttributes){bw.mergeAttributes(bx)}by=bw.nodeName.toLowerCase();if(by==="object"){if(bw.parentNode){bw.outerHTML=bx.outerHTML}if(E.support.html5Clone&&(bx.innerHTML&&!E.trim(bw.innerHTML))){bw.innerHTML=bx.innerHTML}}else{if(by==="input"&&T.test(bx.type)){bw.defaultChecked=bw.checked=bx.checked;if(bw.value!==bx.value){bw.value=bx.value}}else{if(by==="option"){bw.selected=bx.defaultSelected}else{if(by==="input"||by==="textarea"){bw.defaultValue=bx.defaultValue}else{if(by==="script"&&bw.text!==bx.text){bw.text=bx.text}}}}}bw.removeAttribute(E.expando)}E.buildFragment=function(bz,bA,bx){var by,bw,bB,bC=bz[0];bA=bA||am;bA=!bA.nodeType&&bA[0]||bA;bA=bA.ownerDocument||bA;if(bz.length===1&&typeof bC==="string"&&bC.length<512&&bA===am&&bC.charAt(0)==="<"&&!K.test(bC)&&(E.support.checkClone||!q.test(bC))&&(E.support.html5Clone||!ae.test(bC))){bw=true;by=E.fragments[bC];bB=by!==I}if(!by){by=bA.createDocumentFragment();E.clean(bz,bA,by,bx);if(bw){E.fragments[bC]=bB&&by}}return{fragment:by,cacheable:bw}};E.fragments={};E.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(bw,bx){E.fn[bw]=function(by){var bA,bC=0,bB=[],bE=E(by),bz=bE.length,bD=this.length===1&&this[0].parentNode;if((bD==null||bD&&bD.nodeType===11&&bD.childNodes.length===1)&&bz===1){bE[bx](this[0]);return this}else{for(;bC<bz;bC++){bA=(bC>0?this.clone(true):this).get();E(bE[bC])[bx](bA);bB=bB.concat(bA)}return this.pushStack(bB,bw,bE.selector)}}});function bm(bw){if(typeof bw.getElementsByTagName!=="undefined"){return bw.getElementsByTagName("*")}else{if(typeof bw.querySelectorAll!=="undefined"){return bw.querySelectorAll("*")}else{return[]}}}function an(bw){if(T.test(bw.type)){bw.defaultChecked=bw.checked}}E.extend({clone:function(bA,bC,by){var bw,bx,bz,bB;if(E.support.html5Clone||E.isXMLDoc(bA)||!ae.test("<"+bA.nodeName+">")){bB=bA.cloneNode(true)}else{bi.innerHTML=bA.outerHTML;bi.removeChild(bB=bi.firstChild)}if((!E.support.noCloneEvent||!E.support.noCloneChecked)&&(bA.nodeType===1||bA.nodeType===11)&&!E.isXMLDoc(bA)){ad(bA,bB);bw=bm(bA);bx=bm(bB);for(bz=0;bw[bz];++bz){if(bx[bz]){ad(bw[bz],bx[bz])}}}if(bC){t(bA,bB);if(by){bw=bm(bA);bx=bm(bB);for(bz=0;bw[bz];++bz){t(bw[bz],bx[bz])}}}bw=bx=null;return bB},clean:function(bJ,by,bw,bz){var bG,bF,bI,bN,bC,bM,bD,bA,bx,bH,bL,bE,bB=by===am&&V,bK=[];if(!by||typeof by.createDocumentFragment==="undefined"){by=am}for(bG=0;(bI=bJ[bG])!=null;bG++){if(typeof bI==="number"){bI+=""}if(!bI){continue}if(typeof bI==="string"){if(!R.test(bI)){bI=by.createTextNode(bI)}else{bB=bB||c(by);bD=by.createElement("div");bB.appendChild(bD);bI=bI.replace(N,"<$1></$2>");bN=(e.exec(bI)||["",""])[1].toLowerCase();bC=ao[bN]||ao._default;bM=bC[0];bD.innerHTML=bC[1]+bI+bC[2];while(bM--){bD=bD.lastChild}if(!E.support.tbody){bA=x.test(bI);bx=bN==="table"&&!bA?bD.firstChild&&bD.firstChild.childNodes:bC[1]==="<table>"&&!bA?bD.childNodes:[];for(bF=bx.length-1;bF>=0;--bF){if(E.nodeName(bx[bF],"tbody")&&!bx[bF].childNodes.length){bx[bF].parentNode.removeChild(bx[bF])}}}if(!E.support.leadingWhitespace&&aj.test(bI)){bD.insertBefore(by.createTextNode(aj.exec(bI)[0]),bD.firstChild)}bI=bD.childNodes;bD.parentNode.removeChild(bD)}}if(bI.nodeType){bK.push(bI)}else{E.merge(bK,bI)}}if(bD){bI=bD=bB=null}if(!E.support.appendChecked){for(bG=0;(bI=bK[bG])!=null;bG++){if(E.nodeName(bI,"input")){an(bI)}else{if(typeof bI.getElementsByTagName!=="undefined"){E.grep(bI.getElementsByTagName("input"),an)}}}}if(bw){bL=function(bO){if(!bO.type||br.test(bO.type)){return bz?bz.push(bO.parentNode?bO.parentNode.removeChild(bO):bO):bw.appendChild(bO)}};for(bG=0;(bI=bK[bG])!=null;bG++){if(!(E.nodeName(bI,"script")&&bL(bI))){bw.appendChild(bI);if(typeof bI.getElementsByTagName!=="undefined"){bE=E.grep(E.merge([],bI.getElementsByTagName("script")),bL);bK.splice.apply(bK,[bG+1,0].concat(bE));bG+=bE.length}}}}return bK},cleanData:function(bx,bF){var bA,by,bz,bE,bB=0,bG=E.expando,bw=E.cache,bC=E.support.deleteExpando,bD=E.event.special;for(;(bz=bx[bB])!=null;bB++){if(bF||E.acceptData(bz)){by=bz[bG];bA=by&&bw[by];if(bA){if(bA.events){for(bE in bA.events){if(bD[bE]){E.event.remove(bz,bE)}else{E.removeEvent(bz,bE,bA.handle)}}}if(bw[by]){delete bw[by];if(bC){delete bz[bG]}else{if(bz.removeAttribute){bz.removeAttribute(bG)}else{bz[bG]=null}}E.deletedIds.push(by)}}}}}});(function(){var bw,bx;E.uaMatch=function(bz){bz=bz.toLowerCase();var by=/(chrome)[ \/]([\w.]+)/.exec(bz)||/(webkit)[ \/]([\w.]+)/.exec(bz)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(bz)||/(msie) ([\w.]+)/.exec(bz)||bz.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(bz)||[];return{browser:by[1]||"",version:by[2]||"0"}};bw=E.uaMatch(bv.userAgent);bx={};if(bw.browser){bx[bw.browser]=true;bx.version=bw.version}if(bx.chrome){bx.webkit=true}else{if(bx.webkit){bx.safari=true}}E.browser=bx;E.sub=function(){function bz(bB,bC){return new bz.fn.init(bB,bC)}E.extend(true,bz,this);bz.superclass=this;bz.fn=bz.prototype=this();bz.fn.constructor=bz;bz.sub=this.sub;bz.fn.init=function bA(bB,bC){if(bC&&bC instanceof E&&!(bC instanceof bz)){bC=bz(bC)}return E.fn.init.call(this,bB,bC,by)};bz.fn.init.prototype=bz.fn;var by=bz(am);return bz}})();var S,be,n,af=/alpha\([^)]*\)/i,al=/opacity=([^)]*)/,y=/^(top|right|bottom|left)$/,ak=/^(none|table(?!-c[ea]).+)/,ax=/^margin/,k=new RegExp("^("+aG+")(.*)$","i"),aX=new RegExp("^("+aG+")(?!px)[a-z%]+$","i"),G=new RegExp("^([-+])=("+aG+")","i"),L={BODY:"block"},bc={position:"absolute",visibility:"hidden",display:"block"},aN={letterSpacing:0,fontWeight:400},F=["Top","Right","Bottom","Left"],Q=["Webkit","O","Moz","ms"],bg=E.fn.toggle;function a4(bz,bx){if(bx in bz){return bx}var bA=bx.charAt(0).toUpperCase()+bx.slice(1),bw=bx,by=Q.length;while(by--){bx=Q[by]+bA;if(bx in bz){return bx}}return bw}function ar(bx,bw){bx=bw||bx;return E.css(bx,"display")==="none"||!E.contains(bx.ownerDocument,bx)}function bd(bB,bw){var bA,bC,bx=[],by=0,bz=bB.length;for(;by<bz;by++){bA=bB[by];if(!bA.style){continue}bx[by]=E._data(bA,"olddisplay");if(bw){if(!bx[by]&&bA.style.display==="none"){bA.style.display=""}if(bA.style.display===""&&ar(bA)){bx[by]=E._data(bA,"olddisplay",aP(bA.nodeName))}}else{bC=S(bA,"display");if(!bx[by]&&bC!=="none"){E._data(bA,"olddisplay",bC)}}}for(by=0;by<bz;by++){bA=bB[by];if(!bA.style){continue}if(!bw||bA.style.display==="none"||bA.style.display===""){bA.style.display=bw?bx[by]||"":"none"}}return bB}E.fn.extend({css:function(bw,bx){return E.access(this,function(bz,by,bA){return bA!==I?E.style(bz,by,bA):E.css(bz,by)},bw,bx,arguments.length>1)},show:function(){return bd(this,true)},hide:function(){return bd(this)},toggle:function(by,bx){var bw=typeof by==="boolean";if(E.isFunction(by)&&E.isFunction(bx)){return bg.apply(this,arguments)}return this.each(function(){if(bw?by:ar(this)){E(this).show()}else{E(this).hide()}})}});E.extend({cssHooks:{opacity:{get:function(by,bx){if(bx){var bw=S(by,"opacity");return bw===""?"1":bw}}}},cssNumber:{fillOpacity:true,fontWeight:true,lineHeight:true,opacity:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{"float":E.support.cssFloat?"cssFloat":"styleFloat"},style:function(by,bx,bE,bz){if(!by||by.nodeType===3||by.nodeType===8||!by.style){return}var bC,bD,bF,bA=E.camelCase(bx),bw=by.style;bx=E.cssProps[bA]||(E.cssProps[bA]=a4(bw,bA));bF=E.cssHooks[bx]||E.cssHooks[bA];if(bE!==I){bD=typeof bE;if(bD==="string"&&(bC=G.exec(bE))){bE=(bC[1]+1)*bC[2]+parseFloat(E.css(by,bx));bD="number"}if(bE==null||bD==="number"&&isNaN(bE)){return}if(bD==="number"&&!E.cssNumber[bA]){bE+="px"}if(!bF||!("set" in bF)||(bE=bF.set(by,bE,bz))!==I){try{bw[bx]=bE}catch(bB){}}}else{if(bF&&"get" in bF&&(bC=bF.get(by,false,bz))!==I){return bC}return bw[bx]}},css:function(bC,bA,bB,bx){var bD,bz,bw,by=E.camelCase(bA);bA=E.cssProps[by]||(E.cssProps[by]=a4(bC.style,by));bw=E.cssHooks[bA]||E.cssHooks[by];if(bw&&"get" in bw){bD=bw.get(bC,true,bx)}if(bD===I){bD=S(bC,bA)}if(bD==="normal"&&bA in aN){bD=aN[bA]}if(bB||bx!==I){bz=parseFloat(bD);return bB||E.isNumeric(bz)?bz||0:bD}return bD},swap:function(bA,bz,bB){var by,bx,bw={};for(bx in bz){bw[bx]=bA.style[bx];bA.style[bx]=bz[bx]}by=bB.call(bA);for(bx in bz){bA.style[bx]=bw[bx]}return by}});if(bf.getComputedStyle){S=function(bD,bx){var bw,bA,bz,bC,bB=bf.getComputedStyle(bD,null),by=bD.style;if(bB){bw=bB.getPropertyValue(bx)||bB[bx];if(bw===""&&!E.contains(bD.ownerDocument,bD)){bw=E.style(bD,bx)}if(aX.test(bw)&&ax.test(bx)){bA=by.width;bz=by.minWidth;bC=by.maxWidth;by.minWidth=by.maxWidth=by.width=bw;bw=bB.width;by.width=bA;by.minWidth=bz;by.maxWidth=bC}}return bw}}else{if(am.documentElement.currentStyle){S=function(bA,by){var bB,bw,bx=bA.currentStyle&&bA.currentStyle[by],bz=bA.style;if(bx==null&&bz&&bz[by]){bx=bz[by]}if(aX.test(bx)&&!y.test(by)){bB=bz.left;bw=bA.runtimeStyle&&bA.runtimeStyle.left;if(bw){bA.runtimeStyle.left=bA.currentStyle.left}bz.left=by==="fontSize"?"1em":bx;bx=bz.pixelLeft+"px";bz.left=bB;if(bw){bA.runtimeStyle.left=bw}}return bx===""?"auto":bx}}}function aQ(bw,by,bz){var bx=k.exec(by);return bx?Math.max(0,bx[1]-(bz||0))+(bx[2]||"px"):by}function a1(bz,bx,bw,bB){var by=bw===(bB?"border":"content")?4:bx==="width"?1:0,bA=0;for(;by<4;by+=2){if(bw==="margin"){bA+=E.css(bz,bw+F[by],true)}if(bB){if(bw==="content"){bA-=parseFloat(S(bz,"padding"+F[by]))||0}if(bw!=="margin"){bA-=parseFloat(S(bz,"border"+F[by]+"Width"))||0}}else{bA+=parseFloat(S(bz,"padding"+F[by]))||0;if(bw!=="padding"){bA+=parseFloat(S(bz,"border"+F[by]+"Width"))||0}}}return bA}function aa(bz,bx,bw){var bA=bx==="width"?bz.offsetWidth:bz.offsetHeight,by=true,bB=E.support.boxSizing&&E.css(bz,"boxSizing")==="border-box";if(bA<=0||bA==null){bA=S(bz,bx);if(bA<0||bA==null){bA=bz.style[bx]}if(aX.test(bA)){return bA}by=bB&&(E.support.boxSizingReliable||bA===bz.style[bx]);bA=parseFloat(bA)||0}return(bA+a1(bz,bx,bw||(bB?"border":"content"),by))+"px"}function aP(by){if(L[by]){return L[by]}var bw=E("<"+by+">").appendTo(am.body),bx=bw.css("display");bw.remove();if(bx==="none"||bx===""){be=am.body.appendChild(be||E.extend(am.createElement("iframe"),{frameBorder:0,width:0,height:0}));if(!n||!be.createElement){n=(be.contentWindow||be.contentDocument).document;n.write("<!doctype html><html><body>");n.close()}bw=n.body.appendChild(n.createElement(by));bx=S(bw,"display");am.body.removeChild(be)}L[by]=bx;return bx}E.each(["height","width"],function(bx,bw){E.cssHooks[bw]={get:function(bA,bz,by){if(bz){if(bA.offsetWidth===0&&ak.test(S(bA,"display"))){return E.swap(bA,bc,function(){return aa(bA,bw,by)})}else{return aa(bA,bw,by)}}},set:function(bz,bA,by){return aQ(bz,bA,by?a1(bz,bw,by,E.support.boxSizing&&E.css(bz,"boxSizing")==="border-box"):0)}}});if(!E.support.opacity){E.cssHooks.opacity={get:function(bx,bw){return al.test((bw&&bx.currentStyle?bx.currentStyle.filter:bx.style.filter)||"")?(0.01*parseFloat(RegExp.$1))+"":bw?"1":""},set:function(bA,bB){var bz=bA.style,bx=bA.currentStyle,bw=E.isNumeric(bB)?"alpha(opacity="+bB*100+")":"",by=bx&&bx.filter||bz.filter||"";bz.zoom=1;if(bB>=1&&E.trim(by.replace(af,""))===""&&bz.removeAttribute){bz.removeAttribute("filter");if(bx&&!bx.filter){return}}bz.filter=af.test(by)?by.replace(af,bw):by+" "+bw}}}E(function(){if(!E.support.reliableMarginRight){E.cssHooks.marginRight={get:function(bx,bw){return E.swap(bx,{display:"inline-block"},function(){if(bw){return S(bx,"marginRight")}})}}}if(!E.support.pixelPosition&&E.fn.position){E.each(["top","left"],function(bw,bx){E.cssHooks[bx]={get:function(bA,bz){if(bz){var by=S(bA,bx);return aX.test(by)?E(bA).position()[bx]+"px":by}}}})}});if(E.expr&&E.expr.filters){E.expr.filters.hidden=function(bw){return(bw.offsetWidth===0&&bw.offsetHeight===0)||(!E.support.reliableHiddenOffsets&&((bw.style&&bw.style.display)||S(bw,"display"))==="none")};E.expr.filters.visible=function(bw){return !E.expr.filters.hidden(bw)}}E.each({margin:"",padding:"",border:"Width"},function(bw,bx){E.cssHooks[bw+bx]={expand:function(bA){var bz,bB=typeof bA==="string"?bA.split(" "):[bA],by={};for(bz=0;bz<4;bz++){by[bw+F[bz]+bx]=bB[bz]||bB[bz-2]||bB[0]}return by}};if(!ax.test(bw)){E.cssHooks[bw+bx].set=aQ}});var i=/%20/g,ah=/\[\]$/,bt=/\r?\n/g,aW=/^(?:color|date|datetime|datetime-local|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,r=/^(?:select|textarea)/i;E.fn.extend({serialize:function(){return E.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?E.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||r.test(this.nodeName)||aW.test(this.type))}).map(function(bw,bx){var by=E(this).val();return by==null?null:E.isArray(by)?E.map(by,function(bA,bz){return{name:bx.name,value:bA.replace(bt,"\r\n")}}):{name:bx.name,value:by.replace(bt,"\r\n")}}).get()}});E.param=function(bw,by){var bz,bx=[],bA=function(bB,bC){bC=E.isFunction(bC)?bC():(bC==null?"":bC);bx[bx.length]=encodeURIComponent(bB)+"="+encodeURIComponent(bC)};if(by===I){by=E.ajaxSettings&&E.ajaxSettings.traditional}if(E.isArray(bw)||(bw.jqx&&!E.isPlainObject(bw))){E.each(bw,function(){bA(this.name,this.value)})}else{for(bz in bw){u(bz,bw[bz],by,bA)}}return bx.join("&").replace(i,"+")};function u(by,bA,bx,bz){var bw;if(E.isArray(bA)){E.each(bA,function(bC,bB){if(bx||ah.test(by)){bz(by,bB)}else{u(by+"["+(typeof bB==="object"?bC:"")+"]",bB,bx,bz)}})}else{if(!bx&&E.type(bA)==="object"){for(bw in bA){u(by+"["+bw+"]",bA[bw],bx,bz)}}else{bz(by,bA)}}}if(E.support.ajax){E.ajaxTransport(function(bw){if(!bw.crossDomain||E.support.cors){var bx;return{send:function(bD,by){var bB,bA,bC=bw.xhr();if(bw.username){bC.open(bw.type,bw.url,bw.async,bw.username,bw.password)}else{bC.open(bw.type,bw.url,bw.async)}if(bw.xhrFields){for(bA in bw.xhrFields){bC[bA]=bw.xhrFields[bA]}}if(bw.mimeType&&bC.overrideMimeType){bC.overrideMimeType(bw.mimeType)}if(!bw.crossDomain&&!bD["X-Requested-With"]){bD["X-Requested-With"]="XMLHttpRequest"}try{for(bA in bD){bC.setRequestHeader(bA,bD[bA])}}catch(bz){}bC.send((bw.hasContent&&bw.data)||null);bx=function(bM,bG){var bH,bF,bE,bK,bJ;try{if(bx&&(bG||bC.readyState===4)){bx=I;if(bB){bC.onreadystatechange=E.noop;if(xhrOnUnloadAbort){delete xhrCallbacks[bB]}}if(bG){if(bC.readyState!==4){bC.abort()}}else{bH=bC.status;bE=bC.getAllResponseHeaders();bK={};bJ=bC.responseXML;if(bJ&&bJ.documentElement){bK.xml=bJ}try{bK.text=bC.responseText}catch(bL){}try{bF=bC.statusText}catch(bL){bF=""}if(!bH&&bw.isLocal&&!bw.crossDomain){bH=bK.text?200:404}else{if(bH===1223){bH=204}}}}}catch(bI){if(!bG){by(-1,bI)}}if(bK){by(bH,bF,bK,bE)}};if(!bw.async){bx()}else{if(bC.readyState===4){setTimeout(bx,0)}else{bB=++xhrId;if(xhrOnUnloadAbort){if(!xhrCallbacks){xhrCallbacks={};E(bf).unload(xhrOnUnloadAbort)}xhrCallbacks[bB]=bx}bC.onreadystatechange=bx}}},abort:function(){if(bx){bx(0,1)}}}}})}var a8,a3,au=/^(?:toggle|show|hide)$/,aO=new RegExp("^(?:([-+])=|)("+aG+")([a-z%]*)$","i"),a9=/queueHooks$/,m=[bo],J={"*":[function(bw,bD){var bz,bE,bF=this.createTween(bw,bD),bA=aO.exec(bD),bB=bF.cur(),bx=+bB||0,by=1,bC=20;if(bA){bz=+bA[2];bE=bA[3]||(E.cssNumber[bw]?"":"px");if(bE!=="px"&&bx){bx=E.css(bF.elem,bw,true)||bz||1;do{by=by||".5";bx=bx/by;E.style(bF.elem,bw,bx+bE)}while(by!==(by=bF.cur()/bB)&&by!==1&&--bC)}bF.unit=bE;bF.start=bx;bF.end=bA[1]?bx+(bA[1]+1)*bz:bz}return bF}]};function bn(){setTimeout(function(){a8=I},0);return(a8=E.now())}function ab(bx,bw){E.each(bw,function(bC,bA){var bB=(J[bC]||[]).concat(J["*"]),by=0,bz=bB.length;for(;by<bz;by++){if(bB[by].call(bx,bC,bA)){return}}})}function bl(by,bC,bF){var bG,bB=0,bw=0,bx=m.length,bE=E.Deferred().always(function(){delete bA.elem}),bA=function(){var bM=a8||bn(),bJ=Math.max(0,bz.startTime+bz.duration-bM),bH=bJ/bz.duration||0,bL=1-bH,bI=0,bK=bz.tweens.length;for(;bI<bK;bI++){bz.tweens[bI].run(bL)}bE.notifyWith(by,[bz,bL,bJ]);if(bL<1&&bK){return bJ}else{bE.resolveWith(by,[bz]);return false}},bz=bE.promise({elem:by,props:E.extend({},bC),opts:E.extend(true,{specialEasing:{}},bF),originalProperties:bC,originalOptions:bF,startTime:a8||bn(),duration:bF.duration,tweens:[],createTween:function(bK,bH,bJ){var bI=E.Tween(by,bz.opts,bK,bH,bz.opts.specialEasing[bK]||bz.opts.easing);bz.tweens.push(bI);return bI},stop:function(bI){var bH=0,bJ=bI?bz.tweens.length:0;for(;bH<bJ;bH++){bz.tweens[bH].run(1)}if(bI){bE.resolveWith(by,[bz,bI])}else{bE.rejectWith(by,[bz,bI])}return this}}),bD=bz.props;aY(bD,bz.opts.specialEasing);for(;bB<bx;bB++){bG=m[bB].call(bz,by,bD,bz.opts);if(bG){return bG}}ab(bz,bD);if(E.isFunction(bz.opts.start)){bz.opts.start.call(by,bz)}E.fx.timer(E.extend(bA,{anim:bz,queue:bz.opts.queue,elem:by}));return bz.progress(bz.opts.progress).done(bz.opts.done,bz.opts.complete).fail(bz.opts.fail).always(bz.opts.always)}function aY(bz,bB){var by,bx,bC,bA,bw;for(by in bz){bx=E.camelCase(by);bC=bB[bx];bA=bz[by];if(E.isArray(bA)){bC=bA[1];bA=bz[by]=bA[0]}if(by!==bx){bz[bx]=bA;delete bz[by]}bw=E.cssHooks[bx];if(bw&&"expand" in bw){bA=bw.expand(bA);delete bz[bx];for(by in bA){if(!(by in bz)){bz[by]=bA[by];bB[by]=bC}}}else{bB[bx]=bC}}}E.Animation=E.extend(bl,{tweener:function(bx,bA){if(E.isFunction(bx)){bA=bx;bx=["*"]}else{bx=bx.split(" ")}var bz,bw=0,by=bx.length;for(;bw<by;bw++){bz=bx[bw];J[bz]=J[bz]||[];J[bz].unshift(bA)}},prefilter:function(bx,bw){if(bw){m.unshift(bx)}else{m.push(bx)}}});function bo(bA,bG,bw){var bF,by,bI,bz,bM,bC,bL,bK,bJ,bB=this,bx=bA.style,bH={},bE=[],bD=bA.nodeType&&ar(bA);if(!bw.queue){bK=E._queueHooks(bA,"fx");if(bK.unqueued==null){bK.unqueued=0;bJ=bK.empty.fire;bK.empty.fire=function(){if(!bK.unqueued){bJ()}}}bK.unqueued++;bB.always(function(){bB.always(function(){bK.unqueued--;if(!E.queue(bA,"fx").length){bK.empty.fire()}})})}if(bA.nodeType===1&&("height" in bG||"width" in bG)){bw.overflow=[bx.overflow,bx.overflowX,bx.overflowY];if(E.css(bA,"display")==="inline"&&E.css(bA,"float")==="none"){if(!E.support.inlineBlockNeedsLayout||aP(bA.nodeName)==="inline"){bx.display="inline-block"}else{bx.zoom=1}}}if(bw.overflow){bx.overflow="hidden";if(!E.support.shrinkWrapBlocks){bB.done(function(){bx.overflow=bw.overflow[0];bx.overflowX=bw.overflow[1];bx.overflowY=bw.overflow[2]})}}for(bF in bG){bI=bG[bF];if(au.exec(bI)){delete bG[bF];bC=bC||bI==="toggle";if(bI===(bD?"hide":"show")){continue}bE.push(bF)}}bz=bE.length;if(bz){bM=E._data(bA,"fxshow")||E._data(bA,"fxshow",{});if("hidden" in bM){bD=bM.hidden}if(bC){bM.hidden=!bD}if(bD){E(bA).show()}else{bB.done(function(){E(bA).hide()})}bB.done(function(){var bN;E.removeData(bA,"fxshow",true);for(bN in bH){E.style(bA,bN,bH[bN])}});for(bF=0;bF<bz;bF++){by=bE[bF];bL=bB.createTween(by,bD?bM[by]:0);bH[by]=bM[by]||E.style(bA,by);if(!(by in bM)){bM[by]=bL.start;if(bD){bL.end=bL.start;bL.start=by==="width"||by==="height"?1:0}}}}}function w(by,bx,bA,bw,bz){return new w.prototype.init(by,bx,bA,bw,bz)}E.Tween=w;w.prototype={constructor:w,init:function(bz,bx,bB,bw,bA,by){this.elem=bz;this.prop=bB;this.easing=bA||"swing";this.options=bx;this.start=this.now=this.cur();this.end=bw;this.unit=by||(E.cssNumber[bB]?"":"px")},cur:function(){var bw=w.propHooks[this.prop];return bw&&bw.get?bw.get(this):w.propHooks._default.get(this)},run:function(by){var bx,bw=w.propHooks[this.prop];if(this.options.duration){this.pos=bx=E.easing[this.easing](by,this.options.duration*by,0,1,this.options.duration)}else{this.pos=bx=by}this.now=(this.end-this.start)*bx+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(bw&&bw.set){bw.set(this)}else{w.propHooks._default.set(this)}return this}};w.prototype.init.prototype=w.prototype;w.propHooks={_default:{get:function(bx){var bw;if(bx.elem[bx.prop]!=null&&(!bx.elem.style||bx.elem.style[bx.prop]==null)){return bx.elem[bx.prop]}bw=E.css(bx.elem,bx.prop,false,"");return !bw||bw==="auto"?0:bw},set:function(bw){if(E.fx.step[bw.prop]){E.fx.step[bw.prop](bw)}else{if(bw.elem.style&&(bw.elem.style[E.cssProps[bw.prop]]!=null||E.cssHooks[bw.prop])){E.style(bw.elem,bw.prop,bw.now+bw.unit)}else{bw.elem[bw.prop]=bw.now}}}}};w.propHooks.scrollTop=w.propHooks.scrollLeft={set:function(bw){if(bw.elem.nodeType&&bw.elem.parentNode){bw.elem[bw.prop]=bw.now}}};E.each(["toggle","show","hide"],function(bx,bw){var by=E.fn[bw];E.fn[bw]=function(bz,bB,bA){return bz==null||typeof bz==="boolean"||(!bx&&E.isFunction(bz)&&E.isFunction(bB))?by.apply(this,arguments):this.animate(a0(bw,true),bz,bB,bA)}});E.fn.extend({fadeTo:function(bw,bz,by,bx){return this.filter(ar).css("opacity",0).show().end().animate({opacity:bz},bw,by,bx)},animate:function(bC,bz,bB,bA){var by=E.isEmptyObject(bC),bw=E.speed(bz,bB,bA),bx=function(){var bD=bl(this,E.extend({},bC),bw);if(by){bD.stop(true)}};return by||bw.queue===false?this.each(bx):this.queue(bw.queue,bx)},stop:function(by,bx,bw){var bz=function(bA){var bB=bA.stop;delete bA.stop;bB(bw)};if(typeof by!=="string"){bw=bx;bx=by;by=I}if(bx&&by!==false){this.queue(by||"fx",[])}return this.each(function(){var bD=true,bA=by!=null&&by+"queueHooks",bC=E.timers,bB=E._data(this);if(bA){if(bB[bA]&&bB[bA].stop){bz(bB[bA])}}else{for(bA in bB){if(bB[bA]&&bB[bA].stop&&a9.test(bA)){bz(bB[bA])}}}for(bA=bC.length;bA--;){if(bC[bA].elem===this&&(by==null||bC[bA].queue===by)){bC[bA].anim.stop(bw);bD=false;bC.splice(bA,1)}}if(bD||!bw){E.dequeue(this,by)}})}});function a0(by,bA){var bz,bw={height:by},bx=0;bA=bA?1:0;for(;bx<4;bx+=2-bA){bz=F[bx];bw["margin"+bz]=bw["padding"+bz]=by}if(bA){bw.opacity=bw.width=by}return bw}E.each({slideDown:a0("show"),slideUp:a0("hide"),slideToggle:a0("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(bw,bx){E.fn[bw]=function(by,bA,bz){return this.animate(bx,by,bA,bz)}});E.speed=function(by,bz,bx){var bw=by&&typeof by==="object"?E.extend({},by):{complete:bx||!bx&&bz||E.isFunction(by)&&by,duration:by,easing:bx&&bz||bz&&!E.isFunction(bz)&&bz};bw.duration=E.fx.off?0:typeof bw.duration==="number"?bw.duration:bw.duration in E.fx.speeds?E.fx.speeds[bw.duration]:E.fx.speeds._default;if(bw.queue==null||bw.queue===true){bw.queue="fx"}bw.old=bw.complete;bw.complete=function(){if(E.isFunction(bw.old)){bw.old.call(this)}if(bw.queue){E.dequeue(this,bw.queue)}};return bw};E.easing={linear:function(bw){return bw},swing:function(bw){return 0.5-Math.cos(bw*Math.PI)/2}};E.timers=[];E.fx=w.prototype.init;E.fx.tick=function(){var by,bx=E.timers,bw=0;a8=E.now();for(;bw<bx.length;bw++){by=bx[bw];if(!by()&&bx[bw]===by){bx.splice(bw--,1)}}if(!bx.length){E.fx.stop()}a8=I};E.fx.timer=function(bw){if(bw()&&E.timers.push(bw)&&!a3){a3=setInterval(E.fx.tick,E.fx.interval)}};E.fx.interval=13;E.fx.stop=function(){clearInterval(a3);a3=null};E.fx.speeds={slow:600,fast:200,_default:400};E.fx.step={};if(E.expr&&E.expr.filters){E.expr.filters.animated=function(bw){return E.grep(E.timers,function(bx){return bw===bx.elem}).length}}var X=/^(?:body|html)$/i;E.fn.offset=function(bG){if(arguments.length){return bG===I?this:this.each(function(bH){E.offset.setOffset(this,bG,bH)})}var bx,bC,bD,bA,bE,bw,bz,bB={top:0,left:0},by=this[0],bF=by&&by.ownerDocument;if(!bF){return}if((bC=bF.body)===by){return E.offset.bodyOffset(by)}bx=bF.documentElement;if(!E.contains(bx,by)){return bB}if(typeof by.getBoundingClientRect!=="undefined"){bB=by.getBoundingClientRect()}bD=aE(bF);bA=bx.clientTop||bC.clientTop||0;bE=bx.clientLeft||bC.clientLeft||0;bw=bD.pageYOffset||bx.scrollTop;bz=bD.pageXOffset||bx.scrollLeft;return{top:bB.top+bw-bA,left:bB.left+bz-bE}};E.offset={bodyOffset:function(bw){var by=bw.offsetTop,bx=bw.offsetLeft;if(E.support.doesNotIncludeMarginInBodyOffset){by+=parseFloat(E.css(bw,"marginTop"))||0;bx+=parseFloat(E.css(bw,"marginLeft"))||0}return{top:by,left:bx}},setOffset:function(bz,bI,bC){var bD=E.css(bz,"position");if(bD==="static"){bz.style.position="relative"}var bB=E(bz),bx=bB.offset(),bw=E.css(bz,"top"),bG=E.css(bz,"left"),bH=(bD==="absolute"||bD==="fixed")&&E.inArray("auto",[bw,bG])>-1,bF={},bE={},by,bA;if(bH){bE=bB.position();by=bE.top;bA=bE.left}else{by=parseFloat(bw)||0;bA=parseFloat(bG)||0}if(E.isFunction(bI)){bI=bI.call(bz,bC,bx)}if(bI.top!=null){bF.top=(bI.top-bx.top)+by}if(bI.left!=null){bF.left=(bI.left-bx.left)+bA}if("using" in bI){bI.using.call(bz,bF)}else{bB.css(bF)}}};E.fn.extend({isRendered:function(){var bx=this;var bw=this[0];if(bw.parentNode==null||(bw.offsetWidth===0||bw.offsetHeight===0)){return false}return true},getSizeFromStyle:function(){var bA=this;var bz=null;var bw=null;var by=this[0];var bx;if(by.style.width){bz=by.style.width}if(by.style.height){bw=by.style.height}if(bf.getComputedStyle){bx=getComputedStyle(by,null)}else{bx=by.currentStyle}if(bx){if(bx.width){bz=bx.width}if(bx.height){bw=bx.height}}if(bz==="0px"){bz=0}if(bw==="0px"){bw=0}if(bz===null){bz=0}if(bw===null){bw=0}return{width:bz,height:bw}},initAnimate:function(){},sizeStyleChanged:function(bz){var by=this;var bA;var bw=function(bB){var bC=bA;if(bB&&bB[0]&&bB[0].attributeName==="style"&&bB[0].type==="attributes"){if(bC.element.offsetWidth!==bC.offsetWidth||bC.element.offsetHeight!==bC.offsetHeight){bC.offsetWidth=bC.element.offsetWidth;bC.offsetHeight=bC.element.offsetHeight;if(by.isRendered()){bC.callback()}}}};bA={element:by[0],offsetWidth:by[0].offsetWidth,offsetHeight:by[0].offsetHeight,callback:bz};try{if(!by.elementStyleObserver){by.elementStyleObserver=new MutationObserver(bw);by.elementStyleObserver.observe(by[0],{attributes:true,childList:false,characterData:false})}}catch(bx){}},position:function(){if(!this[0]){return}var by=this[0],bx=this.offsetParent(),bz=this.offset(),bw=X.test(bx[0].nodeName)?{top:0,left:0}:bx.offset();bz.top-=parseFloat(E.css(by,"marginTop"))||0;bz.left-=parseFloat(E.css(by,"marginLeft"))||0;bw.top+=parseFloat(E.css(bx[0],"borderTopWidth"))||0;bw.left+=parseFloat(E.css(bx[0],"borderLeftWidth"))||0;return{top:bz.top-bw.top,left:bz.left-bw.left}},offsetParent:function(){return this.map(function(){var bw=this.offsetParent||am.body;while(bw&&(!X.test(bw.nodeName)&&E.css(bw,"position")==="static")){bw=bw.offsetParent}return bw||am.body})}});E.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(by,bx){var bw=/Y/.test(bx);E.fn[by]=function(bz){return E.access(this,function(bA,bD,bC){var bB=aE(bA);if(bC===I){return bB?(bx in bB)?bB[bx]:bB.document.documentElement[bD]:bA[bD]}if(bB){bB.scrollTo(!bw?bC:E(bB).scrollLeft(),bw?bC:E(bB).scrollTop())}else{bA[bD]=bC}},by,bz,arguments.length,null)}});function aE(bw){return E.isWindow(bw)?bw:bw.nodeType===9?bw.defaultView||bw.parentWindow:false}E.each({Height:"height",Width:"width"},function(bw,bx){E.each({padding:"inner"+bw,content:bx,"":"outer"+bw},function(by,bz){E.fn[bz]=function(bD,bC){var bB=arguments.length&&(by||typeof bD!=="boolean"),bA=by||(bD===true||bC===true?"margin":"border");return E.access(this,function(bF,bE,bG){var bH;if(E.isWindow(bF)){return bF.document.documentElement["client"+bw]}if(bF.nodeType===9){bH=bF.documentElement;return Math.max(bF.body["scroll"+bw],bH["scroll"+bw],bF.body["offset"+bw],bH["offset"+bw],bH["client"+bw])}return bG===I?E.css(bF,bE,bG,bA):E.style(bF,bE,bG,bA)},bx,bB?bD:I,bB,null)}})});bf.JQXLite=bf.jqxHelper=E;if(typeof define==="function"&&define.amd&&define.amd.JQXLite){define("jqx",[],function(){return E})}})(window)}(function(c){if(c.jqxCore){c.$$=c.minQuery=c.JQXLite;if(!c.$){c.$=c.minQuery}return}if(c.jQuery){c.minQuery=c.JQXLite=c.jQuery;return}if(!c.$){c.$=c.minQuery=c.JQXLite}else{c.minQuery=c.JQXLite=c.$}})(window);JQXLite.generateID=function(){var c=function(){return(((1+Math.random())*65536)|0).toString(16).substring(1)};var d="";do{d="jqx"+c()+c()+c()}while($("#"+d).length>0);return d};var b=window.jqxBaseFramework=window.minQuery||window.jQuery;(function(d){d.jqx=d.jqx||{};window.jqx=d.jqx;var c={createInstance:function(e,g,i){if(g=="jqxDataAdapter"){var h=i[0];var f=i[1]||{};return new d.jqx.dataAdapter(h,f)}d(e)[g](i||{});return d(e)[g]("getInstance")}};window.jqwidgets=c;d.jqx.define=function(e,f,g){e[f]=function(){if(this.baseType){this.base=new e[this.baseType]();this.base.defineInstance()}this.defineInstance();this.metaInfo()};e[f].prototype.defineInstance=function(){};e[f].prototype.metaInfo=function(){};e[f].prototype.base=null;e[f].prototype.baseType=undefined;if(g&&e[g]){e[f].prototype.baseType=g}};d.jqx.invoke=function(h,g){if(g.length==0){return}var i=typeof(g)==Array||g.length>0?g[0]:g;var f=typeof(g)==Array||g.length>1?Array.prototype.slice.call(g,1):d({}).toArray();while(h[i]==undefined&&h.base!=null){if(h[i]!=undefined&&d.isFunction(h[i])){return h[i].apply(h,f)}if(typeof i=="string"){var e=i.toLowerCase();if(h[e]!=undefined&&d.isFunction(h[e])){return h[e].apply(h,f)}}h=h.base}if(h[i]!=undefined&&d.isFunction(h[i])){return h[i].apply(h,f)}if(typeof i=="string"){var e=i.toLowerCase();if(h[e]!=undefined&&d.isFunction(h[e])){return h[e].apply(h,f)}}return};d.jqx.getByPriority=function(e){var g=undefined;for(var f=0;f<e.length&&g==undefined;f++){if(g==undefined&&e[f]!=undefined){g=e[f]}}return g};d.jqx.hasProperty=function(f,e){if(typeof(e)=="object"){for(var h in e){var g=f;while(g){if(g.hasOwnProperty(h)){return true}if(g.hasOwnProperty(h.toLowerCase())){return true}g=g.base}return false}}else{while(f){if(f.hasOwnProperty(e)){return true}if(f.hasOwnProperty(e.toLowerCase())){return true}f=f.base}}return false};d.jqx.hasFunction=function(h,g){if(g.length==0){return false}if(h==undefined){return false}var i=typeof(g)==Array||g.length>0?g[0]:g;var f=typeof(g)==Array||g.length>1?Array.prototype.slice.call(g,1):{};while(h[i]==undefined&&h.base!=null){if(h[i]&&d.isFunction(h[i])){return true}if(typeof i=="string"){var e=i.toLowerCase();if(h[e]&&d.isFunction(h[e])){return true}}h=h.base}if(h[i]&&d.isFunction(h[i])){return true}if(typeof i=="string"){var e=i.toLowerCase();if(h[e]&&d.isFunction(h[e])){return true}}return false};d.jqx.isPropertySetter=function(f,e){if(e.length==1&&typeof(e[0])=="object"){return true}if(e.length==2&&typeof(e[0])=="string"&&!d.jqx.hasFunction(f,e)){return true}return false};d.jqx.validatePropertySetter=function(j,g,e){if(!d.jqx.propertySetterValidation){return true}if(g.length==1&&typeof(g[0])=="object"){for(var h in g[0]){var k=j;while(!k.hasOwnProperty(h)&&k.base){k=k.base}if(!k||!k.hasOwnProperty(h)){if(!e){var f=k.hasOwnProperty(h.toString().toLowerCase());if(!f){throw"Invalid property: "+h}else{return true}}return false}}return true}if(g.length!=2){if(!e){throw"Invalid property: "+g.length>=0?g[0]:""}return false}while(!j.hasOwnProperty(g[0])&&j.base){j=j.base}if(!j||!j.hasOwnProperty(g[0])){if(!e){throw"Invalid property: "+g[0]}return false}return true};if(!Object.keys){Object.keys=(function(){var g=Object.prototype.hasOwnProperty,h=!({toString:null}).propertyIsEnumerable("toString"),f=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],e=f.length;return function(l){if(typeof l!=="object"&&(typeof l!=="function"||l===null)){throw new TypeError("Object.keys called on non-object")}var j=[],m,k;for(m in l){if(g.call(l,m)){j.push(m)}}if(h){for(k=0;k<e;k++){if(g.call(l,f[k])){j.push(f[k])}}}return j}}())}d.jqx.set=function(h,k){var f=0;if(k.length==1&&typeof(k[0])=="object"){if(h.isInitialized&&Object.keys&&Object.keys(k[0]).length>1){var i=!h.base?h.element:h.base.element;var e=d.data(i,h.widgetName).initArgs;if(e&&JSON&&JSON.stringify&&k[0]&&e[0]){try{if(JSON.stringify(k[0])==JSON.stringify(e[0])){var j=true;d.each(k[0],function(n,o){if(h[n]!=o){j=false;return false}});if(j){return}}}catch(g){}}h.batchUpdate=k[0];var l={};var m={};d.each(k[0],function(n,o){var p=h;while(!p.hasOwnProperty(n)&&p.base!=null){p=p.base}if(p.hasOwnProperty(n)){if(h[n]!=o){l[n]=h[n];m[n]=o;f++}}else{if(p.hasOwnProperty(n.toLowerCase())){if(h[n.toLowerCase()]!=o){l[n.toLowerCase()]=h[n.toLowerCase()];m[n.toLowerCase()]=o;f++}}}});if(f<2){h.batchUpdate=null}}d.each(k[0],function(n,o){var p=h;while(!p.hasOwnProperty(n)&&p.base!=null){p=p.base}if(p.hasOwnProperty(n)){d.jqx.setvalueraiseevent(p,n,o)}else{if(p.hasOwnProperty(n.toLowerCase())){d.jqx.setvalueraiseevent(p,n.toLowerCase(),o)}else{if(d.jqx.propertySetterValidation){throw"jqxCore: invalid property '"+n+"'"}}}});if(h.batchUpdate!=null){h.batchUpdate=null;if(h.propertiesChangedHandler&&f>1){h.propertiesChangedHandler(h,l,m)}}}else{if(k.length==2){while(!h.hasOwnProperty(k[0])&&h.base){h=h.base}if(h.hasOwnProperty(k[0])){d.jqx.setvalueraiseevent(h,k[0],k[1])}else{if(h.hasOwnProperty(k[0].toLowerCase())){d.jqx.setvalueraiseevent(h,k[0].toLowerCase(),k[1])}else{if(d.jqx.propertySetterValidation){throw"jqxCore: invalid property '"+k[0]+"'"}}}}}};d.jqx.setvalueraiseevent=function(f,g,h){var e=f[g];f[g]=h;if(!f.isInitialized){return}if(f.propertyChangedHandler!=undefined){f.propertyChangedHandler(f,g,e,h)}if(f.propertyChangeMap!=undefined&&f.propertyChangeMap[g]!=undefined){f.propertyChangeMap[g](f,g,e,h)}};d.jqx.get=function(h,g){if(g==undefined||g==null){return undefined}if(h.propertyMap){var f=h.propertyMap(g);if(f!=null){return f}}if(h.hasOwnProperty(g)){return h[g]}if(h.hasOwnProperty(g.toLowerCase())){return h[g.toLowerCase()]}var e=undefined;if(typeof(g)==Array){if(g.length!=1){return undefined}e=g[0]}else{if(typeof(g)=="string"){e=g}}while(!h.hasOwnProperty(e)&&h.base){h=h.base}if(h){return h[e]}return undefined};d.jqx.serialize=function(h){var e="";if(d.isArray(h)){e="[";for(var g=0;g<h.length;g++){if(g>0){e+=", "}e+=d.jqx.serialize(h[g])}e+="]"}else{if(typeof(h)=="object"){e="{";var f=0;for(var g in h){if(f++>0){e+=", "}e+=g+": "+d.jqx.serialize(h[g])}e+="}"}else{e=h.toString()}}return e};d.jqx.propertySetterValidation=true;d.jqx.jqxWidgetProxy=function(j,f,e){var g=d(f);var i=d.data(f,j);if(i==undefined){return undefined}var h=i.instance;if(d.jqx.hasFunction(h,e)){return d.jqx.invoke(h,e)}if(d.jqx.isPropertySetter(h,e)){if(d.jqx.validatePropertySetter(h,e)){d.jqx.set(h,e);return undefined}}else{if(typeof(e)=="object"&&e.length==0){return}else{if(typeof(e)=="object"&&e.length==1&&d.jqx.hasProperty(h,e[0])){return d.jqx.get(h,e[0])}else{if(typeof(e)=="string"&&d.jqx.hasProperty(h,e[0])){return d.jqx.get(h,e)}}}}throw"jqxCore: Invalid parameter '"+d.jqx.serialize(e)+"' does not exist."};d.jqx.applyWidget=function(g,h,n,o){var k=false;try{k=window.MSApp!=undefined}catch(j){}var p=d(g);if(!o){o=new d.jqx["_"+h]()}else{o.host=p;o.element=g}if(g.id==""){g.id=d.jqx.utilities.createId()}var m={host:p,element:g,instance:o,initArgs:n};o.widgetName=h;d.data(g,h,m);d.data(g,"jqxWidget",m.instance);var l=new Array();var o=m.instance;while(o){o.isInitialized=false;l.push(o);o=o.base}l.reverse();l[0].theme=d.jqx.theme||"";d.jqx.jqxWidgetProxy(h,g,n);for(var f in l){o=l[f];if(f==0){o.host=p;o.element=g;o.WinJS=k}if(o!=undefined){if(o.definedInstance){o.definedInstance()}if(o.createInstance!=null){if(k){MSApp.execUnsafeLocalFunction(function(){o.createInstance(n)})}else{o.createInstance(n)}}}}for(var f in l){if(l[f]!=undefined){l[f].isInitialized=true}}if(k){MSApp.execUnsafeLocalFunction(function(){m.instance.refresh(true)})}else{m.instance.refresh(true)}};d.jqx.jqxWidget=function(f,g,i){var l=false;try{var n=Array.prototype.slice.call(i,0)}catch(k){var n=""}try{l=window.MSApp!=undefined}catch(k){}var j=f;var o="";if(g){o="_"+g}d.jqx.define(d.jqx,"_"+j,o);var m=new Array();if(!window[j]){var h=function(p){if(p==null){return""}var e=d.type(p);switch(e){case"string":case"number":case"date":case"boolean":case"bool":if(p===null){return""}return p.toString()}var q="";d.each(p,function(s,t){var v=t;if(s>0){q+=", "}q+="[";var r=0;if(d.type(v)=="object"){for(var u in v){if(r>0){q+=", "}q+="{"+u+":"+v[u]+"}";r++}}else{if(r>0){q+=", "}q+="{"+s+":"+v+"}";r++}q+="]"});return q};c[j]=window[j]=function(e,u){var p=[];if(!u){u={}}p.push(u);var q=e;if(d.type(q)==="object"&&e[0]){q=e[0].id;if(q===""){q=e[0].id=d.jqx.utilities.createId()}}else{if(d.type(e)==="object"&&e&&e.nodeName){q=e.id;if(q===""){q=e.id=d.jqx.utilities.createId()}}}if(window.jqxWidgets&&window.jqxWidgets[q]){if(u){d.each(window.jqxWidgets[q],function(v){var w=d(this.element).data();if(w&&w.jqxWidget){d(this.element)[j](u)}})}if(window.jqxWidgets[q].length==1){var s=d(window.jqxWidgets[q][0].widgetInstance.element).data();if(s&&s.jqxWidget){return window.jqxWidgets[q][0]}}var s=d(window.jqxWidgets[q][0].widgetInstance.element).data();if(s&&s.jqxWidget){return window.jqxWidgets[q]}}var r=d(e);if(r.length===0){r=d("<div></div>");if(j==="jqxInput"||j==="jqxPasswordInput"||j==="jqxMaskedInput"){r=d("<input/>")}if(j==="jqxTextArea"){r=d("<textarea></textarea>")}if(j==="jqxButton"||j==="jqxRepeatButton"||j==="jqxToggleButton"){r=d("<button/>")}if(j==="jqxSplitter"){r=d("<div><div>Panel 1</div><div>Panel 2</div></div>")}if(j==="jqxTabs"){r=d("<div><ul><li>Tab 1</li><li>Tab 2</li></ul><div>Content 1</div><div>Content 2</div></div>")}if(j==="jqxRibbon"){r=d("<div><ul><li>Tab 1</li><li>Tab 2</li></ul><div><div>Content 1</div><div>Content 2</div></div></div>")}if(j==="jqxDocking"){r=d("<div><div><div><div>Title 1</div><div>Content 1</div></div></div></div>")}if(j==="jqxWindow"){r=d("<div><div>Title 1</div><div>Content 1</div></div>")}}var t=[];d.each(r,function(y){var A=r[y];d.jqx.applyWidget(A,j,p,undefined);if(!m[j]){var w=d.data(A,"jqxWidget");var z=d.jqx["_"+j].prototype.defineInstance();var x={};if(d.jqx["_"+j].prototype.metaInfo){x=d.jqx["_"+j].prototype.metaInfo()}if(j=="jqxDockingLayout"){z=d.extend(z,d.jqx._jqxLayout.prototype.defineInstance())}if(j=="jqxToggleButton"||j=="jqxRepeatButton"){z=d.extend(z,d.jqx._jqxButton.prototype.defineInstance())}if(j=="jqxTreeGrid"){z=d.extend(z,d.jqx._jqxDataTable.prototype.defineInstance())}var v=function(C){var B=d.data(C,"jqxWidget");this.widgetInstance=B;var D=d.extend(this,B);D.on=D.addEventListener=function(F,G){D.addHandler(!D.base?D.host:D.base.host,F,G)};D.off=D.removeEventListener=function(F){D.removeHandler(!D.base?D.host:D.base.host,F)};for(var E in B){if(d.type(B[E])=="function"){D[E]=d.proxy(B[E],B)}}return D};m[j]=v;d.each(z,function(C,B){Object.defineProperty(v.prototype,C,{get:function(){if(this.widgetInstance){return this.widgetInstance[C]}return B},set:function(J){if(this.widgetInstance&&(this.widgetInstance[C]!=J||C==="width"||C==="height")){var H=this.widgetInstance[C];var G=J;var F=d.type(H);var D=d.type(G);var I=false;if(F!=D||C==="source"||C==="width"||C==="height"){I=true}if(I||(h(H)!=h(G))){var E={};E[C]=J;if(this.widgetInstance.host){this.widgetInstance.host[j](E)}else{this.widgetInstance.base.host[j](E)}this.widgetInstance[C]=J;if(this.widgetInstance.propertyUpdated){this.widgetInstance.propertyUpdated(C,H,J)}}}}})})}var w=new m[j](A);t.push(w);if(!window.jqxWidgets){window.jqxWidgets=new Array()}if(!window.jqxWidgets[q]){window.jqxWidgets[q]=new Array()}window.jqxWidgets[q].push(w)});if(t.length===1){return t[0]}return t}}d.fn[j]=function(){var e=Array.prototype.slice.call(arguments,0);if(e.length==0||(e.length==1&&typeof(e[0])=="object")){if(this.length==0){if(this.selector){throw new Error("Invalid Selector - "+this.selector+"! Please, check whether the used ID or CSS Class name is correct.")}else{throw new Error("Invalid Selector! Please, check whether the used ID or CSS Class name is correct.")}}return this.each(function(){var s=d(this);var r=this;var t=d.data(r,j);if(t==null){d.jqx.applyWidget(r,j,e,undefined)}else{d.jqx.jqxWidgetProxy(j,this,e)}})}else{if(this.length==0){if(this.selector){throw new Error("Invalid Selector - "+this.selector+"! Please, check whether the used ID or CSS Class name is correct.")}else{throw new Error("Invalid Selector! Please, check whether the used ID or CSS Class name is correct.")}}var q=null;var p=0;this.each(function(){var r=d.jqx.jqxWidgetProxy(j,this,e);if(p==0){q=r;p++}else{if(p==1){var s=[];s.push(q);q=s}q.push(r)}})}return q};try{d.extend(d.jqx["_"+j].prototype,Array.prototype.slice.call(i,0)[0])}catch(k){}d.extend(d.jqx["_"+j].prototype,{toThemeProperty:function(e,p){return d.jqx.toThemeProperty(this,e,p)},isMaterialized:function(){if(!this.theme){return false}if(this.theme==="fluent"){return true}if(this.theme==="light"){return true}if(this.theme==="dark"){return true}if(this.theme==="deepblue"){return true}if(this.theme.indexOf("material")>=0){return true}},isModern:function(){if(!this.theme){return false}if(this.theme.indexOf("light")>=0){return true}if(this.theme==="dark"){return true}},_addBarAndLabel:function(r){var q=this;var e=d("<label></label");e[0].innerHTML=this.placeHolder;e.addClass(q.toThemeProperty("jqx-input-label"));r.after(e);q.label=e;var p=d("<span></span>");r.after(p);p.addClass(q.toThemeProperty("jqx-input-bar"));q.bar=p;q.bar.css("top",this.host.height())}});d.jqx["_"+j].prototype.refresh=function(){if(this.base){this.base.refresh(true)}};d.jqx["_"+j].prototype.createInstance=function(){};d.jqx.isPassiveSupported=function(){var q=this;if(q.supportsPassive!==undefined){return q.supportsPassive}q.supportsPassive=false;try{var p=Object.defineProperty({},"passive",{get:function(){q.supportsPassive=true}});window.addEventListener("testPassive",null,p);window.removeEventListener("testPassive",null,p)}catch(r){}return q.supportsPassive};d.jqx["_"+j].prototype.addEventHandler=function(p,e){if(this.base){this.base.host.on(p,e)}else{this.host.on(p,e)}};d.jqx["_"+j].prototype.removeEventHandler=function(p,e){if(this.base){this.base.host.off(p)}else{this.host.off(p)}};d.jqx["_"+j].prototype.applyTo=function(q,p){if(!(p instanceof Array)){var e=[];e.push(p);p=e}d.jqx.applyWidget(q,j,p,this)};d.jqx["_"+j].prototype.getInstance=function(){return this};d.jqx["_"+j].prototype.propertyChangeMap={};d.jqx["_"+j].prototype.addHandler=function(r,e,p,q){d.jqx.addHandler(d(r),e,p,q)};d.jqx["_"+j].prototype.removeHandler=function(q,e,p){d.jqx.removeHandler(d(q),e,p)};d.jqx["_"+j].prototype.setOptions=function(){if(!this.host||!this.host.length||this.host.length!=1){return}return d.jqx.jqxWidgetProxy(j,this.host[0],arguments)}};d.jqx.toThemeProperty=function(f,g,l){if(f.theme==""){return g}var k=g.split(" ");var e="";for(var j=0;j<k.length;j++){if(j>0){e+=" "}var h=k[j];if(l!=null&&l){e+=h+"-"+f.theme}else{e+=h+" "+h+"-"+f.theme}}return e};d.jqx.addHandler=function(k,l,h,j){var f=l.split(" ");for(var e=0;e<f.length;e++){var g=f[e];if(window.addEventListener&&k[0]){switch(g){case"mousewheel":if(d.jqx.browser.mozilla){k[0].addEventListener("DOMMouseScroll",h,d.jqx.isPassiveSupported()?{passive:false}:false)}else{k[0].addEventListener("mousewheel",h,d.jqx.isPassiveSupported()?{passive:false}:false)}continue;case"mousemove":if(!j){k[0].addEventListener("mousemove",h,false);continue}break;case"touchmove":if(!j){k[0].addEventListener("touchmove",h,false);continue}else{if(j&&j.passive){k[0].addEventListener("touchmove",h,j);continue}}break}}if(j==undefined||j==null){if(k.on){k.on(g,h)}else{k.bind(g,h)}}else{if(k.on){k.on(g,j,h)}else{k.bind(g,j,h)}}}};d.jqx.removeHandler=function(j,k,h){if(!k){if(j.off){j.off()}else{j.unbind()}return}var f=k.split(" ");for(var e=0;e<f.length;e++){var g=f[e];if(window.removeEventListener){switch(g){case"mousewheel":if(d.jqx.browser.mozilla){j[0].removeEventListener("DOMMouseScroll",h,false)}else{j[0].removeEventListener("mousewheel",h,false)}continue;case"mousemove":if(h){j[0].removeEventListener("mousemove",h,false);continue}break;case"touchmove":if(h){j[0].removeEventListener("touchmove",h,false);continue}break}}if(g==undefined){if(j.off){j.off()}else{j.unbind()}continue}if(h==undefined){if(j.off){j.off(g)}else{j.unbind(g)}}else{if(j.off){j.off(g,h)}else{j.unbind(g,h)}}}};d.jqx.credits=d.jqx.credits||"";d.jqx.theme=d.jqx.theme||"";d.jqx.scrollAnimation=d.jqx.scrollAnimation||false;d.jqx.resizeDelay=d.jqx.resizeDelay||10;d.jqx.ready=function(){d(window).trigger("jqxReady")};d.jqx.init=function(){d.each(arguments[0],function(e,f){if(e=="theme"){d.jqx.theme=f}if(e=="scrollBarSize"){d.jqx.utilities.scrollBarSize=f}if(e=="touchScrollBarSize"){d.jqx.utilities.touchScrollBarSize=f}if(e=="scrollBarButtonsVisibility"){d.jqx.utilities.scrollBarButtonsVisibility=f}})};d.jqx.utilities=d.jqx.utilities||{};d.extend(d.jqx.utilities,{scrollBarSize:13,touchScrollBarSize:8,scrollBarButtonsVisibility:"visible",createId:function(){var e=function(){return(((1+Math.random())*65536)|0).toString(16).substring(1)};return"jqxWidget"+e()+e()+e()},setTheme:function(j,k,h){if(typeof h==="undefined"){return}if(!h[0].className.split){return}if(j===undefined){j=""}if(k===undefined){k=""}var l=h[0].className.split(" "),e=[],m=[],g=h.children();for(var f=0;f<l.length;f+=1){if(l[f].indexOf(j)>=0){if(j.length>0){e.push(l[f]);m.push(l[f].replace(j,k))}else{m.push(l[f].replace("-"+k,"")+"-"+k)}}}this._removeOldClasses(e,h);this._addNewClasses(m,h);for(var f=0;f<g.length;f+=1){this.setTheme(j,k,d(g[f]))}},_removeOldClasses:function(g,f){for(var e=0;e<g.length;e+=1){f.removeClass(g[e])}},_addNewClasses:function(g,f){for(var e=0;e<g.length;e+=1){f.addClass(g[e])}},getOffset:function(e){var g=d.jqx.mobile.getLeftPos(e[0]);var f=d.jqx.mobile.getTopPos(e[0]);return{top:f,left:g}},resize:function(k,v,s,r){if(r===undefined){r=true}var o=-1;var n=this;var g=function(x){if(!n.hiddenWidgets){return -1}var y=-1;for(var w=0;w<n.hiddenWidgets.length;w++){if(x.id){if(n.hiddenWidgets[w].id==x.id){y=w;break}}else{if(n.hiddenWidgets[w].id==x[0].id){y=w;break}}}return y};if(this.resizeHandlers){for(var l=0;l<this.resizeHandlers.length;l++){if(k.id){if(this.resizeHandlers[l].id==k.id){o=l;break}}else{if(this.resizeHandlers[l].id==k[0].id){o=l;break}}}if(s===true){if(o!=-1){this.resizeHandlers.splice(o,1);if(this.watchedElementData&&this.watchedElementData.length>0){this.watchedElementData.splice(o,1)}}if(this.resizeHandlers.length==0){var q=d(window);if(q.off){q.off("resize.jqx");q.off("orientationchange.jqx");q.off("orientationchanged.jqx")}else{q.unbind("resize.jqx");q.unbind("orientationchange.jqx");q.unbind("orientationchanged.jqx")}this.resizeHandlers=null}var e=g(k);if(e!=-1&&this.hiddenWidgets){this.hiddenWidgets.splice(e,1)}return}}else{if(s===true){var e=g(k);if(e!=-1&&this.hiddenWidgets){this.hiddenWidgets.splice(e,1)}return}}var n=this;var p=function(y,H){if(!n.resizeHandlers){return}var I=function(L){var i=-1;var M=L.parentNode;while(M){i++;M=M.parentNode}return i};var x=function(N,L){if(!N.widget||!L.widget){return 0}var M=I(N.widget[0]);var i=I(L.widget[0]);try{if(M<i){return -1}if(M>i){return 1}}catch(O){var P=O}return 0};var z=function(L){if(n.hiddenWidgets.length>0){n.hiddenWidgets.sort(x);var i=function(){var N=false;var P=new Array();for(var O=0;O<n.hiddenWidgets.length;O++){var M=n.hiddenWidgets[O];if(d.jqx.isHidden(M.widget)){N=true;P.push(M)}else{if(M.callback){M.callback(H)}}}n.hiddenWidgets=P;if(!N){clearInterval(n.__resizeInterval)}};if(L==false){i();if(n.__resizeInterval){clearInterval(n.__resizeInterval)}return}if(n.__resizeInterval){clearInterval(n.__resizeInterval)}n.__resizeInterval=setInterval(function(){i()},100)}};if(n.hiddenWidgets&&n.hiddenWidgets.length>0){z(false)}n.hiddenWidgets=new Array();n.resizeHandlers.sort(x);for(var E=0;E<n.resizeHandlers.length;E++){var K=n.resizeHandlers[E];var G=K.widget;var D=K.data;if(!D){continue}if(!D.jqxWidget){continue}var w=D.jqxWidget.width;var J=D.jqxWidget.height;if(D.jqxWidget.base){if(w==undefined){w=D.jqxWidget.base.width}if(J==undefined){J=D.jqxWidget.base.height}}if(w===undefined&&J===undefined){w=D.jqxWidget.element.style.width;J=D.jqxWidget.element.style.height}var F=false;if(w!=null&&w.toString().indexOf("%")!=-1){F=true}if(J!=null&&J.toString().indexOf("%")!=-1){F=true}if(d.jqx.isHidden(G)){if(g(G)===-1){if(F||y===true){if(K.data.nestedWidget!==true){n.hiddenWidgets.push(K)}}}}else{if(y===undefined||y!==true){if(F){K.callback(H);if(n.watchedElementData){for(var B=0;B<n.watchedElementData.length;B++){if(n.watchedElementData[B].element==D.jqxWidget.element){n.watchedElementData[B].offsetWidth=D.jqxWidget.element.offsetWidth;n.watchedElementData[B].offsetHeight=D.jqxWidget.element.offsetHeight;break}}}if(n.hiddenWidgets.indexOf(K)>=0){n.hiddenWidgets.splice(n.hiddenWidgets.indexOf(K),1)}}if(D.jqxWidget.element){var A=D.jqxWidget.element.className;if(A.indexOf("dropdownlist")>=0||A.indexOf("datetimeinput")>=0||A.indexOf("combobox")>=0||A.indexOf("menu")>=0){if(D.jqxWidget.isOpened){var C=D.jqxWidget.isOpened();if(C){if(H&&H=="resize"&&d.jqx.mobile.isTouchDevice()){continue}D.jqxWidget.close()}}}}}}}z()};if(!this.resizeHandlers){this.resizeHandlers=new Array();var q=d(window);if(q.on){this._resizeTimer=null;this._initResize=null;q.on("resize.jqx",function(i){if(n._resizeTimer!=undefined){clearTimeout(n._resizeTimer)}if(!n._initResize){n._initResize=true;p(null,"resize")}else{n._resizeTimer=setTimeout(function(){p(null,"resize")},d.jqx.resizeDelay)}});q.on("orientationchange.jqx",function(i){p(null,"orientationchange")});q.on("orientationchanged.jqx",function(i){p(null,"orientationchange")})}else{q.bind("resize.jqx",function(i){p(null,"orientationchange")});q.bind("orientationchange.jqx",function(i){p(null,"orientationchange")});q.bind("orientationchanged.jqx",function(i){p(null,"orientationchange")})}}var h=k.data();if(r){if(o===-1){this.resizeHandlers.push({id:k[0].id,widget:k,callback:v,data:h})}}try{var f=h.jqxWidget.width;var u=h.jqxWidget.height;if(h.jqxWidget.base){if(f==undefined){f=h.jqxWidget.base.width}if(u==undefined){u=h.jqxWidget.base.height}}if(f===undefined&&u===undefined){f=h.jqxWidget.element.style.width;u=h.jqxWidget.element.style.height}var m=false;if(f!=null&&f.toString().indexOf("%")!=-1){m=true}if(u!=null&&u.toString().indexOf("%")!=-1){m=true}if(m){if(!this.watchedElementData){this.watchedElementData=[]}var n=this;var j=function(i){if(n.watchedElementData.forEach){n.watchedElementData.forEach(function(w){if(w.element.offsetWidth!==w.offsetWidth||w.element.offsetHeight!==w.offsetHeight){w.offsetWidth=w.element.offsetWidth;w.offsetHeight=w.element.offsetHeight;if(w.timer){clearTimeout(w.timer)}w.timer=setTimeout(function(){if(!d.jqx.isHidden(d(w.element))){w.callback()}else{w.timer=setInterval(function(){if(!d.jqx.isHidden(d(w.element))){clearInterval(w.timer);w.callback()}},100)}})}})}};n.watchedElementData.push({element:k[0],offsetWidth:k[0].offsetWidth,offsetHeight:k[0].offsetHeight,callback:v});if(!n.observer){n.observer=new MutationObserver(j);n.observer.observe(document.body,{attributes:true,childList:true,characterData:true})}}}catch(t){}if(d.jqx.isHidden(k)&&r===true){p(true)}d.jqx.resize=function(){p(null,"resize")}},parseJSON:function(g){if(!g||typeof g!=="string"){return null}var e=/^[\],:{}\s]*$/,i=/(?:^|:|,)(?:\s*\[)+/g,f=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,h=/"[^"\\\r\n]*"|true|false|null|-?(?:\d\d*\.|)\d+(?:[eE][\-+]?\d+|)/g;g=d.trim(g);if(window.JSON&&window.JSON.parse){return window.JSON.parse(g)}if(e.test(g.replace(f,"@").replace(h,"]").replace(i,""))){return(new Function("return "+g))()}throw new Error("Invalid JSON: "+g)},html:function(f,g){if(!d(f).on||!d.access){return d(f).html(g)}try{return d.access(f,function(u){var h=f[0]||{},o=0,m=f.length;if(u===undefined){return h.nodeType===1?h.innerHTML.replace(rinlinejQuery,""):undefined}var t=/<(?:script|style|link)/i,p="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",k=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,r=/<([\w:]+)/,j=/<(?:script|object|embed|option|style)/i,n=new RegExp("<(?:"+p+")[\\s/>]","i"),s=/^\s+/,v={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]};if(typeof u==="string"&&!t.test(u)&&(d.support.htmlSerialize||!n.test(u))&&(d.support.leadingWhitespace||!s.test(u))&&!v[(r.exec(u)||["",""])[1].toLowerCase()]){u=u.replace(k,"<$1></$2>");try{for(;o<m;o++){h=this[o]||{};if(h.nodeType===1){d.cleanData(h.getElementsByTagName("*"));h.innerHTML=u}}h=0}catch(q){}}if(h){f.empty().append(u)}},null,g,arguments.length)}catch(e){return d(f).html(g)}},hasTransform:function(g){var f="";f=g.css("transform");if(f==""||f=="none"){f=g.parents().css("transform");if(f==""||f=="none"){var e=d.jqx.utilities.getBrowser();if(e.browser=="msie"){f=g.css("-ms-transform");if(f==""||f=="none"){f=g.parents().css("-ms-transform")}}else{if(e.browser=="chrome"){f=g.css("-webkit-transform");if(f==""||f=="none"){f=g.parents().css("-webkit-transform")}}else{if(e.browser=="opera"){f=g.css("-o-transform");if(f==""||f=="none"){f=g.parents().css("-o-transform")}}else{if(e.browser=="mozilla"){f=g.css("-moz-transform");if(f==""||f=="none"){f=g.parents().css("-moz-transform")}}}}}}else{return f!=""&&f!="none"}}if(f==""||f=="none"){f=d(document.body).css("transform")}return f!=""&&f!="none"&&f!=null},getBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=/(chrome)[ \/]([\w.]+)/.exec(f)||/(webkit)[ \/]([\w.]+)/.exec(f)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(f)||/(msie) ([\w.]+)/.exec(f)||f.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(f)||[];var g={browser:e[1]||"",version:e[2]||"0"};if(f.indexOf("rv:11.0")>=0&&f.indexOf(".net4.0c")>=0){g.browser="msie";g.version="11";e[1]="msie"}if(f.indexOf("edge")>=0){g.browser="msie";g.version="12";e[1]="msie"}g[e[1]]=e[1];return g}});d.jqx.browser=d.jqx.utilities.getBrowser();d.jqx.isHidden=function(f){if(!f||!f[0]){return false}var e=f[0].offsetWidth,g=f[0].offsetHeight;if(e===0||g===0){return true}else{return false}};d.jqx.ariaEnabled=true;d.jqx.aria=function(f,h,g){if(!d.jqx.ariaEnabled){return}if(h==undefined){d.each(f.aria,function(j,k){var m=!f.base?f.host.attr(j):f.base.host.attr(j);if(m!=undefined&&!d.isFunction(m)){var l=m;switch(k.type){case"number":l=new Number(m);if(isNaN(l)){l=m}break;case"boolean":l=m=="true"?true:false;break;case"date":l=new Date(m);if(l=="Invalid Date"||isNaN(l)){l=m}break}f[k.name]=l}else{var m=f[k.name];if(d.isFunction(m)){m=f[k.name]()}if(m==undefined){m=""}try{!f.base?f.host.attr(j,m.toString()):f.base.host.attr(j,m.toString())}catch(i){}}})}else{try{if(f.host){if(!f.base){if(f.host){if(f.element.setAttribute){f.element.setAttribute(h,g.toString())}else{f.host.attr(h,g.toString())}}else{f.attr(h,g.toString())}}else{if(f.base.host){f.base.host.attr(h,g.toString())}else{f.attr(h,g.toString())}}}else{if(f.setAttribute){f.setAttribute(h,g.toString())}}}catch(e){}}};if(!Array.prototype.indexOf){Array.prototype.indexOf=function(f){var e=this.length;var g=Number(arguments[1])||0;g=(g<0)?Math.ceil(g):Math.floor(g);if(g<0){g+=e}for(;g<e;g++){if(g in this&&this[g]===f){return g}}return -1}}d.jqx.mobile=d.jqx.mobile||{};d.jqx.position=function(e){var h=parseInt(e.pageX);var g=parseInt(e.pageY);if(d.jqx.mobile.isTouchDevice()){var f=d.jqx.mobile.getTouches(e);var i=f[0];h=parseInt(i.pageX);g=parseInt(i.pageY)}return{left:h,top:g}};d.extend(d.jqx.mobile,{_touchListener:function(k,i){var f=function(l,n){var m=document.createEvent("MouseEvents");m.initMouseEvent(l,n.bubbles,n.cancelable,n.view,n.detail,n.screenX,n.screenY,n.clientX,n.clientY,n.ctrlKey,n.altKey,n.shiftKey,n.metaKey,n.button,n.relatedTarget);m._pageX=n.pageX;m._pageY=n.pageY;return m};var j={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"};var h=f(j[k.type],k);k.target.dispatchEvent(h);var g=k.target["on"+j[k.type]];if(typeof g==="function"){g(k)}},setMobileSimulator:function(f,h){if(this.isTouchDevice()){return}this.simulatetouches=true;if(h==false){this.simulatetouches=false}var g={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"};var e=this;if(window.addEventListener){var i=function(){for(var j in g){if(f.addEventListener){f.removeEventListener(j,e._touchListener);f.addEventListener(j,e._touchListener,false)}}};if(d.jqx.browser.msie){i()}else{i()}}},isTouchDevice:function(){if(this.touchDevice!=undefined){return this.touchDevice}var g="Browser CodeName: "+navigator.appCodeName+"";g+="Browser Name: "+navigator.appName+"";g+="Browser Version: "+navigator.appVersion+"";g+="Platform: "+navigator.platform+"";g+="User-agent header: "+navigator.userAgent+"";if(navigator.maxTouchPoints>1){}if(g.indexOf("Android")!=-1){return true}if(g.indexOf("IEMobile")!=-1){return true}if(g.indexOf("Windows Phone")!=-1){return true}if(g.indexOf("WPDesktop")!=-1){return true}if(g.indexOf("ZuneWP7")!=-1){return true}if(g.indexOf("BlackBerry")!=-1&&g.indexOf("Mobile Safari")!=-1){return true}if(g.indexOf("ipod")!=-1){return true}if(g.indexOf("nokia")!=-1||g.indexOf("Nokia")!=-1){return true}if(g.indexOf("Chrome/17")!=-1){return false}if(g.indexOf("CrOS")!=-1){return false}if(g.indexOf("Opera")!=-1&&g.indexOf("Mobi")==-1&&g.indexOf("Mini")==-1&&g.indexOf("Platform: Win")!=-1){return false}if(g.indexOf("HybridDeviceTouch")!=-1){return true}if(g.indexOf("HybridDeviceMouse")!=-1){return false}if(g.indexOf("Opera")!=-1&&g.indexOf("Mobi")!=-1&&g.indexOf("Opera Mobi")!=-1){return true}if(g.indexOf("Mozilla/5.0 (X11; Linux x86_64)")!=-1){return false}var h={ios:"i(?:Pad|Phone|Pod)(?:.*)CPU(?: iPhone)? OS ",android:"(Android |HTC_|Silk/)",blackberry:"BlackBerry(?:.*)Version/",rimTablet:"RIM Tablet OS ",webos:"(?:webOS|hpwOS)/",bada:"Bada/"};try{if(this.touchDevice!=undefined){return this.touchDevice}this.touchDevice=false;for(var k in h){if(h.hasOwnProperty(k)){var m=h[k];var j=g.match(new RegExp("(?:"+m+")([^\\s;]+)"));if(j){if(k.toString()=="blackberry"){this.touchDevice=false;return false}this.touchDevice=true;return true}}}var l=navigator.userAgent;if(navigator.platform.toLowerCase().indexOf("win")!=-1){if(l.indexOf("Windows Phone")>=0||l.indexOf("WPDesktop")>=0||l.indexOf("IEMobile")>=0||l.indexOf("ZuneWP7")>=0){this.touchDevice=true;return true}else{if(l.indexOf("Touch")>=0){var f=("MSPointerDown" in window)||("pointerdown" in window);if(f){this.touchDevice=true;return true}if(l.indexOf("ARM")>=0){this.touchDevice=true;return true}this.touchDevice=false;return false}}}if(navigator.platform.toLowerCase().indexOf("win")!=-1){this.touchDevice=false;return false}if(("ontouchstart" in window)||window.DocumentTouch&&document instanceof DocumentTouch){this.touchDevice=true}return this.touchDevice}catch(n){this.touchDevice=false;return false}},getLeftPos:function(e){var f=e.offsetLeft;while((e=e.offsetParent)!=null){if(e.tagName!="HTML"){f+=e.offsetLeft;if(document.all){f+=e.clientLeft}}}return f},getTopPos:function(f){var h=f.offsetTop;var e=d(f).coord();while((f=f.offsetParent)!=null){if(f.tagName!="HTML"){h+=(f.offsetTop-f.scrollTop);if(document.all){h+=f.clientTop}}}var g=navigator.userAgent.toLowerCase();var i=(g.indexOf("windows phone")!=-1||g.indexOf("WPDesktop")!=-1||g.indexOf("ZuneWP7")!=-1||g.indexOf("msie 9")!=-1||g.indexOf("msie 11")!=-1||g.indexOf("msie 10")!=-1)&&g.indexOf("touch")!=-1;if(i){return e.top}if(this.isSafariMobileBrowser()){if(this.isSafari4MobileBrowser()&&this.isIPadSafariMobileBrowser()){return h}if(g.indexOf("version/7")!=-1){return e.top}if(g.indexOf("version/6")!=-1||g.indexOf("version/5")!=-1){h=h+d(window).scrollTop()}if(/(Android.*Chrome\/[.0-9]* (!?Mobile))/.exec(navigator.userAgent)){return h}if(/(Android.*Chrome\/[.0-9]* Mobile)/.exec(navigator.userAgent)){return h}return e.top}return h},isChromeMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("android")!=-1;return e},isOperaMiniMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("opera mini")!=-1||f.indexOf("opera mobi")!=-1;return e},isOperaMiniBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("opera mini")!=-1;return e},isNewSafariMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("ipad")!=-1||f.indexOf("iphone")!=-1||f.indexOf("ipod")!=-1;e=e&&(f.indexOf("version/5")!=-1);return e},isSafari4MobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("ipad")!=-1||f.indexOf("iphone")!=-1||f.indexOf("ipod")!=-1;e=e&&(f.indexOf("version/4")!=-1);return e},isWindowsPhone:function(){var f=navigator.userAgent.toLowerCase();var e=(f.indexOf("windows phone")!=-1||f.indexOf("WPDesktop")!=-1||f.indexOf("ZuneWP7")!=-1||f.indexOf("msie 9")!=-1||f.indexOf("msie 11")!=-1||f.indexOf("msie 10")!=-1&&f.indexOf("touch")!=-1);return e},isSafariMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();if(/(Android.*Chrome\/[.0-9]* (!?Mobile))/.exec(navigator.userAgent)){return true}if(/(Android.*Chrome\/[.0-9]* Mobile)/.exec(navigator.userAgent)){return true}var e=f.indexOf("ipad")!=-1||f.indexOf("iphone")!=-1||f.indexOf("ipod")!=-1||f.indexOf("mobile safari")!=-1;return e},isIPadSafariMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("ipad")!=-1;return e},isMobileBrowser:function(){var f=navigator.userAgent.toLowerCase();var e=f.indexOf("ipad")!=-1||f.indexOf("iphone")!=-1||f.indexOf("android")!=-1;return e},getTouches:function(f){if(f.originalEvent){if(f.originalEvent.touches&&f.originalEvent.touches.length){return f.originalEvent.touches}else{if(f.originalEvent.changedTouches&&f.originalEvent.changedTouches.length){return f.originalEvent.changedTouches}}}if(!f.touches){f.touches=new Array();f.touches[0]=f.originalEvent!=undefined?f.originalEvent:f;if(f.originalEvent!=undefined&&f.pageX){f.touches[0]=f}if(f.type=="mousemove"){f.touches[0]=f}}return f.touches},getTouchEventName:function(e){if(this.isWindowsPhone()){var f=navigator.userAgent.toLowerCase();if(f.indexOf("windows phone 7")!=-1){if(e.toLowerCase().indexOf("start")!=-1){return"MSPointerDown"}if(e.toLowerCase().indexOf("move")!=-1){return"MSPointerMove"}if(e.toLowerCase().indexOf("end")!=-1){return"MSPointerUp"}}if(e.toLowerCase().indexOf("start")!=-1){return"pointerdown"}if(e.toLowerCase().indexOf("move")!=-1){return"pointermove"}if(e.toLowerCase().indexOf("end")!=-1){return"pointerup"}}else{return e}},dispatchMouseEvent:function(f,i,h){if(this.simulatetouches){return}var g=document.createEvent("MouseEvent");g.initMouseEvent(f,true,true,i.view,1,i.screenX,i.screenY,i.clientX,i.clientY,false,false,false,false,0,null);if(h!=null){h.dispatchEvent(g)}},getRootNode:function(e){while(e.nodeType!==1){e=e.parentNode}return e},setTouchScroll:function(e,f){if(!this.enableScrolling){this.enableScrolling=[]}this.enableScrolling[f]=e},touchScroll:function(D,O,ab,J,z,p){if(D==null){return}var I=this;var h=0;var t=0;var i=0;var k=0;var v=0;var l=0;if(!this.scrolling){this.scrolling=[]}this.scrolling[J]=false;var m=false;var r=d(D);var T=["select","input","textarea"];var Z=0;var L=0;if(!this.enableScrolling){this.enableScrolling=[]}this.enableScrolling[J]=true;var J=J;var y=this.getTouchEventName("touchstart")+".touchScroll";var F=this.getTouchEventName("touchend")+".touchScroll";var ad=this.getTouchEventName("touchmove")+".touchScroll";var n,Y,B,aj,X,aa,al,S,ac,f,H,af,ah,Q,g,x,w,U,e,G,ai,q;S=O;var al=0;var ac=0;var j=0;var V=0;var ak=0;var aa=z.jqxScrollBar("max");var q=325;function C(ao){if(ao.targetTouches&&(ao.targetTouches.length>=1)){return ao.targetTouches[0].clientY}else{if(ao.originalEvent&&ao.originalEvent.clientY!==undefined){return ao.originalEvent.clientY}else{var an=I.getTouches(ao);return an[0].clientY}}}function ag(ao){if(ao.targetTouches&&(ao.targetTouches.length>=1)){return ao.targetTouches[0].clientX}else{if(ao.originalEvent&&ao.originalEvent.clientX!==undefined){return ao.originalEvent.clientX}else{var an=I.getTouches(ao);return an[0].clientX}}}var K=function(){var ar,ao,at,aq;ar=Date.now();ao=ar-x;x=ar;at=ac-g;var ap=j-aj;g=ac;aj=j;H=true;aq=1000*at/(1+ao);var an=1000*ap/(1+ao);ah=0.8*aq+0.2*ah;Q=0.8*an+0.2*Q};var E=false;var Z=function(ao){if(!I.enableScrolling[J]){return true}if(d.inArray(ao.target.tagName.toLowerCase(),T)!==-1){return}ac=p.jqxScrollBar("value");j=z.jqxScrollBar("value");var ap=I.getTouches(ao);var aq=ap[0];if(ap.length==1){I.dispatchMouseEvent("mousedown",aq,I.getRootNode(aq.target))}aa=z.jqxScrollBar("max");S=p.jqxScrollBar("max");function an(ar){E=false;H=true;f=C(ar);ai=ag(ar);ah=U=Q=0;g=ac;aj=j;x=Date.now();clearInterval(w);w=setInterval(K,100);V=ac;ak=j;if(ac>0&&ac<S&&p[0].style.visibility!="hidden"){}}an(ao);m=false;t=aq.pageY;v=aq.pageX;if(I.simulatetouches){if(aq._pageY!=undefined){t=aq._pageY;v=aq._pageX}}I.scrolling[J]=true;h=0;k=0;return true};if(r.on){r.on(y,Z)}else{r.bind(y,Z)}var ae=function(ao,an){ac=(ao>S)?S:(ao<al)?al:ao;ab(null,ao,0,0,an);return(ao>S)?"max":(ao<al)?"min":"value"};var o=function(ao,an){j=(ao>aa)?aa:(ao<al)?al:ao;ab(ao,null,0,0,an);return(ao>aa)?"max":(ao<al)?"min":"value"};function W(){var an,ao;if(U){an=Date.now()-x;ao=-U*Math.exp(-an/q);if(ao>0.5||ao<-0.5){ae(e+ao);requestAnimationFrame(W)}else{ae(e)}}}function P(){var an,ao;if(U){an=Date.now()-x;ao=-U*Math.exp(-an/q);if(ao>0.5||ao<-0.5){o(G+ao);requestAnimationFrame(P)}else{o(G)}}}var A=function(an){if(!I.enableScrolling[J]){return true}if(!I.scrolling[J]){return true}if(E){an.preventDefault();an.stopPropagation()}var at=I.getTouches(an);if(at.length>1){return true}var ao=at[0].pageY;var aq=at[0].pageX;if(I.simulatetouches){if(at[0]._pageY!=undefined){ao=at[0]._pageY;aq=at[0]._pageX}}var aw=ao-t;var ax=aq-v;L=ao;var av=aq;i=aw-h;l=ax-k;m=true;h=aw;k=ax;var ap=z!=null?z[0].style.visibility!="hidden":true;var au=p!=null?p[0].style.visibility!="hidden":true;function ar(aA){var aC,aB,az;if(H){aC=C(aA);az=ag(aA);aB=f-aC;X=ai-az;var ay="value";if(aB>2||aB<-2){f=aC;ay=ae(ac+aB,aA);K();if(ay=="min"&&V===0){return true}if(ay=="max"&&V===S){return true}if(!au){return true}aA.preventDefault();aA.stopPropagation();E=true;return false}else{if(X>2||X<-2){ai=az;ay=o(j+X,aA);K();if(ay=="min"&&ak===0){return true}if(ay=="max"&&ak===aa){return true}if(!ap){return true}E=true;aA.preventDefault();aA.stopPropagation();return false}}aA.preventDefault()}}if(ap||au){if((ap)||(au)){ar(an)}}};if(r.on){r.on(ad,A)}else{r.bind(ad,A)}var u=function(ao){if(!I.enableScrolling[J]){return true}var ap=I.getTouches(ao)[0];if(!I.scrolling[J]){return true}H=false;clearInterval(w);if(ah>10||ah<-10){U=0.8*ah;e=Math.round(ac+U);x=Date.now();requestAnimationFrame(W)}else{if(Q>10||Q<-10){U=0.8*Q;G=Math.round(j+U);x=Date.now();requestAnimationFrame(P)}else{}}I.scrolling[J]=false;if(m){I.dispatchMouseEvent("mouseup",ap,ao.target)}else{var ap=I.getTouches(ao)[0],an=I.getRootNode(ap.target);I.dispatchMouseEvent("mouseup",ap,an);I.dispatchMouseEvent("click",ap,an);return true}};if(this.simulatetouches){var s=d(window).on!=undefined||d(window).bind;var R=function(an){try{u(an)}catch(ao){}I.scrolling[J]=false};d(window).on!=undefined?d(document).on("mouseup.touchScroll",R):d(document).bind("mouseup.touchScroll",R);if(window.frameElement){if(window.top!=null){var N=function(an){try{u(an)}catch(ao){}I.scrolling[J]=false};if(window.top.document){d(window.top.document).on?d(window.top.document).on("mouseup",N):d(window.top.document).bind("mouseup",N)}}}var am=d(document).on!=undefined||d(document).bind;var M=function(an){if(!I.scrolling[J]){return true}I.scrolling[J]=false;var ap=I.getTouches(an)[0],ao=I.getRootNode(ap.target);I.dispatchMouseEvent("mouseup",ap,ao);I.dispatchMouseEvent("click",ap,ao)};d(document).on!=undefined?d(document).on("touchend",M):d(document).bind("touchend",M)}if(r.on){r.on("dragstart",function(an){an.preventDefault()});r.on("selectstart",function(an){an.preventDefault()})}r.on?r.on(F+" touchcancel.touchScroll",u):r.bind(F+" touchcancel.touchScroll",u)}});d.jqx.cookie=d.jqx.cookie||{};d.extend(d.jqx.cookie,{cookie:function(h,i,f){if(arguments.length>1&&String(i)!=="[object Object]"){f=d.extend({},f);if(i===null||i===undefined){f.expires=-1}if(typeof f.expires==="number"){var k=f.expires,g=f.expires=new Date();g.setDate(g.getDate()+k)}i=String(i);return(document.cookie=[encodeURIComponent(h),"=",f.raw?i:encodeURIComponent(i),f.expires?"; expires="+f.expires.toUTCString():"",f.path?"; path="+f.path:"",f.domain?"; domain="+f.domain:"",f.secure?"; secure":""].join(""))}f=i||{};var e,j=f.raw?function(l){return l}:decodeURIComponent;return(e=new RegExp("(?:^|; )"+encodeURIComponent(h)+"=([^;]*)").exec(document.cookie))?j(e[1]):null}});d.jqx.string=d.jqx.string||{};d.extend(d.jqx.string,{replace:function(i,g,h){if(g===h){return this}var e=i;var f=e.indexOf(g);while(f!=-1){e=e.replace(g,h);f=e.indexOf(g)}return e},contains:function(e,f){if(e==null||f==null){return false}return e.indexOf(f)!=-1},containsIgnoreCase:function(e,f){if(e==null||f==null){return false}return e.toString().toUpperCase().indexOf(f.toString().toUpperCase())!=-1},equals:function(e,f){if(e==null||f==null){return false}e=this.normalize(e);if(f.length==e.length){return e.slice(0,f.length)==f}return false},equalsIgnoreCase:function(e,f){if(e==null||f==null){return false}e=this.normalize(e);if(f.length==e.length){return e.toUpperCase().slice(0,f.length)==f.toUpperCase()}return false},startsWith:function(e,f){if(e==null||f==null){return false}return e.slice(0,f.length)==f},startsWithIgnoreCase:function(e,f){if(e==null||f==null){return false}return e.toUpperCase().slice(0,f.length)==f.toUpperCase()},normalize:function(e){if(e.charCodeAt(e.length-1)==65279){e=e.substring(0,e.length-1)}return e},endsWith:function(e,f){if(e==null||f==null){return false}e=this.normalize(e);return e.slice(-f.length)==f},endsWithIgnoreCase:function(e,f){if(e==null||f==null){return false}e=this.normalize(e);return e.toUpperCase().slice(-f.length)==f.toUpperCase()}});d.extend(d.easing,{easeOutBack:function(f,g,e,j,i,h){if(h==undefined){h=1.70158}return j*((g=g/i-1)*g*((h+1)*g+h)+1)+e},easeInQuad:function(f,g,e,i,h){return i*(g/=h)*g+e},easeInOutCirc:function(f,g,e,i,h){if((g/=h/2)<1){return -i/2*(Math.sqrt(1-g*g)-1)+e}return i/2*(Math.sqrt(1-(g-=2)*g)+1)+e},easeInOutSine:function(f,g,e,i,h){return -i/2*(Math.cos(Math.PI*g/h)-1)+e},easeInCubic:function(f,g,e,i,h){return i*(g/=h)*g*g+e},easeOutCubic:function(f,g,e,i,h){return i*((g=g/h-1)*g*g+1)+e},easeInOutCubic:function(f,g,e,i,h){if((g/=h/2)<1){return i/2*g*g*g+e}return i/2*((g-=2)*g*g+2)+e},easeInSine:function(f,g,e,i,h){return -i*Math.cos(g/h*(Math.PI/2))+i+e},easeOutSine:function(f,g,e,i,h){return i*Math.sin(g/h*(Math.PI/2))+e},easeInOutSine:function(f,g,e,i,h){return -i/2*(Math.cos(Math.PI*g/h)-1)+e}})})(b);(function(d){if(d.event&&d.event.special){d.extend(d.event.special,{close:{noBubble:true},open:{noBubble:true},cellclick:{noBubble:true},rowclick:{noBubble:true},tabclick:{noBubble:true},selected:{noBubble:true},expanded:{noBubble:true},collapsed:{noBubble:true},valuechanged:{noBubble:true},expandedItem:{noBubble:true},collapsedItem:{noBubble:true},expandingItem:{noBubble:true},collapsingItem:{noBubble:true}})}if(d.fn.extend){d.fn.extend({ischildof:function(i){if(!d(this).parents){var e=i.element.contains(this.element);return e}var g=d(this).parents().get();for(var f=0;f<g.length;f++){if(typeof i!="string"){var h=g[f];if(i!==undefined){if(h==i[0]){return true}}}else{if(i!==undefined){if(d(g[f]).is(i)){return true}}}}return false}})}d.fn.jqxProxy=function(){var g=d(this).data().jqxWidget;var e=Array.prototype.slice.call(arguments,0);var f=g.element;if(!f){f=g.base.element}return d.jqx.jqxWidgetProxy(g.widgetName,f,e)};var c=d.originalVal=d.fn.val;d.fn.val=function(f){if(typeof f=="undefined"){if(d(this).hasClass("jqx-widget")||d(this).hasClass("jqx-input-group")){var e=d(this).data().jqxWidget;if(e&&e.val){return e.val()}}if(this[0]&&this[0].tagName.toLowerCase().indexOf("angular")>=0){var e=d(this).find(".jqx-widget").data().jqxWidget;if(e&&e.val){return e.val()}}return c.call(this)}else{if(d(this).hasClass("jqx-widget")||d(this).hasClass("jqx-input-group")){var e=d(this).data().jqxWidget;if(e&&e.val){if(arguments.length!=2){return e.val(f)}else{return e.val(f,arguments[1])}}}if(this[0]&&this[0].tagName.toLowerCase().indexOf("angular")>=0){var e=d(this).find(".jqx-widget").data().jqxWidget;if(e&&e.val){if(arguments.length!=2){return e.val(f)}else{return e.val(f,arguments[1])}}}return c.call(this,f)}};if(d.fn.modal&&d.fn.modal.Constructor){d.fn.modal.Constructor.prototype.enforceFocus=function(){d(document).off("focusin.bs.modal").on("focusin.bs.modal",d.proxy(function(f){if(this.$element[0]!==f.target&&!this.$element.has(f.target).length){if(d(f.target).parents().hasClass("jqx-popup")){return true}this.$element.trigger("focus")}},this))}}d.fn.coord=function(p){var g,l,k={top:0,left:0},h=this[0],n=h&&h.ownerDocument;if(!n){return}g=n.documentElement;if(!d.contains(g,h)){return k}if(typeof h.getBoundingClientRect!==undefined){k=h.getBoundingClientRect()}var f=function(q){return d.isWindow(q)?q:q.nodeType===9?q.defaultView||q.parentWindow:false};l=f(n);var j=0;var e=0;var i=navigator.userAgent.toLowerCase();var o=i.indexOf("ipad")!=-1||i.indexOf("iphone")!=-1;if(o){j=2}if(true==p){if(document.body.style.position!="static"&&document.body.style.position!=""){var m=d(document.body).coord();j=-m.left;e=-m.top}}return{top:e+k.top+(l.pageYOffset||g.scrollTop)-(g.clientTop||0),left:j+k.left+(l.pageXOffset||g.scrollLeft)-(g.clientLeft||0)}};d.jqx.ripplers=[];d.jqx.ripple=function(g,f,p){if(!f){f=g}var j=d(g);var k=false;j.append("<span class='ink'></span>");var q=j.find(".ink");var e=false;for(var h=0;h<d.jqx.ripplers.length;h++){var l=d.jqx.ripplers[h];if(l.element[0]===g[0]){e=true;break}}if(!e){d.jqx.ripplers.push({ink:q,element:g,hostElement:f,hostElementType:p})}if(p==="checkbox"||p==="radiobutton"){var m=Math.max(j.outerWidth(),j.outerHeight());q.css({height:m,width:m});var o=j.width()/2-q.width()/2;var n=j.height()/2-q.height()/2;q.css({top:n+"px",left:o+"px"})}if(d.jqx.ripplers.length===1){d(document).on("mouseup",function(t){d.jqx.ripple.mouseCaptured=false;for(var s=0;s<d.jqx.ripplers.length;s++){var r=d.jqx.ripplers[s];r.ink.removeClass("active");r.element.removeClass("active");if(p!=="checkbox"&&p!=="radiobutton"){if(r.ink.hasClass("animate")){r.ink.removeClass("animate")}}}})}f.off("mousedown.ripple");f.on("mousedown.ripple",function(r){var i=d(g);d.jqx.ripple.mouseCaptured=true;setTimeout(function(){if(i.find(".ink").length==0){i.append("<span class='ink'></span>")}var t=i.find(".ink");t.removeClass("animate");if(!t.height()&&!t.width()){var u=Math.max(i.outerWidth(),i.outerHeight());t.css({height:u,width:u})}if(p==="checkbox"||p==="radiobutton"){if(p==="checkbox"){if(f.jqxCheckBox("disabled")){return}}if(p==="radiobutton"){if(f.jqxRadioButton("disabled")){return}}var s=i.width()/2-t.width()/2;var v=i.height()/2-t.height()/2;t.css({top:v+"px",left:s+"px"}).addClass("animate");t.on("animationend",function(){if(d.jqx.ripple.mouseCaptured){t.removeClass("animate");t.addClass("active");g.addClass("active")}});return}var s=r.pageX-i.offset().left-t.width()/2;var v=r.pageY-i.offset().top-t.height()/2;t.css({top:v+"px",left:s+"px"}).addClass("animate")})})}})(b)})();

