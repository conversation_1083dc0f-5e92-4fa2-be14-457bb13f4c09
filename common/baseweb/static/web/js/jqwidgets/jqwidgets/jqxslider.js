/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(a){a.jqx.jqxWidget("jqxSlider","",{});a.extend(a.jqx._jqxSlider.prototype,{defineInstance:function(){var b={disabled:false,width:300,height:30,step:1,max:10,min:0,int64:false,orientation:"horizontal",showTicks:true,tickMode:"default",tickNumber:10,minorTickNumber:20,niceInterval:false,ticksPosition:"both",ticksFrequency:2,minorTicksFrequency:1,showMinorTicks:false,showButtons:true,buttonsPosition:"both",mode:"default",showRange:true,rangeSlider:false,value:0,values:[0,10],tooltip:false,tooltipFormatFunction:null,tooltipFormatSettings:null,tooltipPosition:"near",tooltipHideDelay:500,sliderButtonSize:14,tickSize:7,minorTickSize:4,showTickLabels:false,tickLabelStyleSettings:null,tickLabelFormatSettings:null,tickLabelFormatFunction:null,template:"",layout:"normal",rtl:false,changeType:null,editableLabels:false,padding:{},_settings:{vertical:{size:"height",oSize:"width",outerOSize:"outerWidth",outerSize:"outerHeight",left:"top",top:"left",start:"_startY",mouse:"_mouseStartY",page:"pageY",opposite:"horizontal"},horizontal:{size:"width",oSize:"height",outerOSize:"outerHeight",outerSize:"outerWidth",left:"left",top:"top",start:"_startX",mouse:"_mouseStartX",page:"pageX",opposite:"vertical"}},_touchEvents:{mousedown:a.jqx.mobile.getTouchEventName("touchstart"),click:a.jqx.mobile.getTouchEventName("touchstart"),mouseup:a.jqx.mobile.getTouchEventName("touchend"),mousemove:a.jqx.mobile.getTouchEventName("touchmove"),mouseenter:"mouseenter",mouseleave:"mouseleave"},_events:["change","slide","slideEnd","slideStart","created"],_invalidArgumentExceptions:{invalidWidth:"Invalid width.",invalidHeight:"Invalid height.",invalidStep:"Invalid step.",invalidMaxValue:"Invalid maximum value.",invalidMinValue:"Invalid minimum value.",invalidTickFrequency:"Invalid tick frequency.",invalidValue:"Invalid value.",invalidValues:"Invalid values.",invalidTicksPosition:"Invalid ticksPosition",invalidButtonsPosition:"Invalid buttonsPosition"},_lastValue:[],_track:null,_leftButton:null,_rightButton:null,_slider:null,_rangeBar:null,_slideEvent:null,_capturedElement:null,_slideStarted:false,_helpers:[],aria:{"aria-valuenow":{name:"value",type:"number"},"aria-valuemin":{name:"min",type:"number"},"aria-valuemax":{name:"max",type:"number"},"aria-disabled":{name:"disabled",type:"boolean"}}};if(this===a.jqx._jqxSlider.prototype){return b}a.extend(true,this,b);return b},_createFromInput:function(c){var h=this,i,f,e,j,b,g;if(h.element.nodeName.toLowerCase()==="input"){h.field=h.element;if(h.field.className){h._className=h.field.className}i={title:h.field.title};if(h.field.value){i.value=h.field.value}if(h.field.id.length){i.id=h.field.id.replace(/[^\w]/g,"_")+"_"+c}else{i.id=a.jqx.utilities.createId()+"_"+c}if(h.field.getAttribute("min")){f=(h.field.getAttribute("min"));h.min=parseFloat(f)}if(h.field.getAttribute("step")){e=(h.field.getAttribute("step"));h.step=parseFloat(e)}if(h.field.getAttribute("max")){j=(h.field.getAttribute("max"));h.max=parseFloat(j)}b=document.createElement("div");if(undefined!==i.id){b.setAttribute("id",i.id)}if(undefined!==i.value){b.setAttribute("value",i.value)}b.style.cssText=h.field.style.cssText;if(!h.width){h.width=h.field.offsetWidth}if(!h.height){h.height=h.field.offsetHeight}h.field.style.display="none";if(h.field.parentNode){h.field.parentNode.insertBefore(b,h.field.nextSibling)}g=h.host.data();h.host=a(b);h.host.data(g);h.element=b;h.element.id=h.field.id;h.field.id=i.id;h._helpers.element=new jqxHelper(h.element);h._helpers.field=new jqxHelper(h.field);if(h._className){h._helpers.element.addClass(h._className);h._helpers.field.removeClass(h._className)}if(h.field.tabIndex){var d=h.field.tabIndex;h.field.tabIndex=-1;h.element.tabIndex=d}}},createInstance:function(b){var e=this;if(!window.jqxHelper){window.jqxHelper=a}e._createFromInput("jqxSlider");e._isTouchDevice=a.jqx.mobile.isTouchDevice();var d="<div role='slider'><div style='width:100%; height: 100%;'></div></div><div><div></div><div></div><div></div></div><div><div style='width:100%; height: 100%;'></div></div>";e.element.innerHTML=d;e._leftButton=e.element.firstChild;e._contentWrapper=e._leftButton.nextSibling;e._rightButton=e._contentWrapper.nextSibling;e.element.className=e.toThemeProperty("jqx-slider jqx-widget");e._topTicks=e._contentWrapper.firstChild;e._track=e._topTicks.nextSibling;e._bottomTicks=e._track.nextSibling;e._leftButton.className=e.toThemeProperty("jqx-slider-left");e._rightButton.className=e.toThemeProperty("jqx-slider-left");e._helpers.leftButton=new jqxHelper(e._leftButton);e._helpers.rightButton=new jqxHelper(e._rightButton);e._helpers.element=new jqxHelper(e.element);e._helpers.track=new jqxHelper(e._track);if(!e.host.jqxRepeatButton){throw new Error("jqxSlider: Missing reference to jqxbuttons.js.")}a.jqx.aria(this);if(e.int64==="s"){if(!a.jqx.longInt){throw new Error("jqxSlider: Missing reference to jqxmath.js")}a.jqx.longInt(e);e._value64=new a.jqx.math().fromString(e.value.toString(),10);e._values64=[new a.jqx.math().fromString(e.values[0].toString(),10),new a.jqx.math().fromString(e.values[1].toString(),10)];e._min64=new a.jqx.math().fromString(e.min.toString(),10);e._max64=new a.jqx.math().fromString(e.max.toString(),10);e._step64=new a.jqx.math().fromString(e.step.toString(),10);e._ticksFrequency64=new a.jqx.math().fromString(e.ticksFrequency.toString(),10);e._minorTicksFrequency64=new a.jqx.math().fromString(e.minorTicksFrequency.toString(),10)}else{if(e.int64==="u"){try{new BigNumber(e.value)}catch(c){throw new Error("jqxSlider: Missing reference to jqxmath.js")}e._value64=new BigNumber(e.value);e._values64=[new BigNumber(e.values[0]),new BigNumber(e.values[1])];e._min64=new BigNumber(e.min);e._max64=new BigNumber(e.max);e._step64=new BigNumber(e.step);e._ticksFrequency64=new BigNumber(e.ticksFrequency);e._minorTicksFrequency64=new BigNumber(e.minorTicksFrequency)}}e._helpers.element.width(e.width);e._helpers.element.height(e.height);if(e._helpers.element.isRendered){if(e._helpers.element.isRendered()){a(e._leftButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});a(e._rightButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});e.render()}else{if(e._helpers.element.sizeChanged){e._helpers.element.sizeChanged(function(){a(e._leftButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});a(e._rightButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});e.render()})}e._helpers.element.sizeStyleChanged(function(){var f=e._helpers.element.getSizeFromStyle();if(f.width){e.width=f.width}if(f.height){e.height=f.height}e.__trackSize=null;e.__thumbSize=null;e._performLayout();e._initialSettings()})}}else{a(e._leftButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});a(e._rightButton).jqxRepeatButton({template:e.template,theme:e.theme,delay:50,width:e.sliderButtonSize,height:e.sliderButtonSize});e.render()}},render:function(){var c=this;c._raiseEvent(4,{value:c.getValue()});c._setPaddingValues();c._rendering=true;c._refresh();c._addInput();var b=c.element.getAttribute("tabindex")==null;if(b){c.element.setAttribute("tabindex",0)}a.jqx.utilities.resize(c.host,function(){c.__trackSize=null;c.__thumbSize=null;c._performLayout();c._initialSettings()});if(c.orientation==="vertical"){c.element.style.minWidth=96+"px"}c._rendering=false},focus:function(){try{this.host.focus()}catch(b){}},destroy:function(){var b=this;b.removeHandler(a(document),"mouseup.arrow"+b.element.id);b.removeHandler(a(document),b._getEvent("mouseup")+"."+b.element.id);b.removeHandler(a(document),b._getEvent("mousemove")+"."+b.element.id);a.jqx.utilities.resize(this.host,null,true);b.host.remove();b._helpers=[]},_addInput:function(){var d=this;var c=d.element.getAttribute("name");var b=document.createElement("input");b.setAttribute("type","hidden");d.element.appendChild(b);if(c){b.setAttribute("name",c)}if(!d.rangeSlider){b.value=d.value.toString()}else{if(d.values){b.value=d.value.rangeStart.toString()+"-"+d.value.rangeEnd.toString()}}d.input=b},_getSetting:function(b){return this._settings[this.orientation][b]},_getEvent:function(b){if(this._isTouchDevice){return this._touchEvents[b]}else{return b}},refresh:function(b){if(!b){this._refresh()}},_refresh:function(){var b=this;b._render();b._performLayout();b._removeEventHandlers();b._addEventHandlers();b._initialSettings()},_render:function(){var b=this;b._addTrack();b._addSliders();b._addTickContainers();b._updateButtonsVisibility();b._addRangeBar()},_addTrack:function(){var c=this;var b=c._track;c._helpers.track.addClass(c.toThemeProperty("jqx-slider-track"));b.setAttribute("style","");c._helpers.track.removeClass(c.toThemeProperty("jqx-slider-track-"+c._getSetting("opposite")));c._helpers.track.addClass(c.toThemeProperty("jqx-slider-track-"+c.orientation));c._helpers.track.addClass(c.toThemeProperty("jqx-fill-state-normal jqx-rc-all"))},_addSliders:function(){var d=this;if(d._slider===null||d._slider.length<1){d._slider={};var b=document.createElement("div");var c=document.createElement("div");b.className=d.toThemeProperty("jqx-slider-slider");c.className=d.toThemeProperty("jqx-slider-slider");d._slider.left=b;d._track.appendChild(b);d._slider.right=c;d._track.appendChild(c);d._helpers.track=new jqxHelper(d._track);d._helpers.left=new jqxHelper(d._slider.left);d._helpers.right=new jqxHelper(d._slider.right);if(d.template){d._helpers.left.addClass(d.toThemeProperty("jqx-"+d.template));d._helpers.right.addClass(d.toThemeProperty("jqx-"+d.template))}}d._helpers.left.removeClass(d.toThemeProperty("jqx-slider-slider-"+d._getSetting("opposite")));d._helpers.left.addClass(d.toThemeProperty("jqx-slider-slider-"+d.orientation));d._helpers.right.removeClass(d.toThemeProperty("jqx-slider-slider-"+d._getSetting("opposite")));d._helpers.right.addClass(d.toThemeProperty("jqx-slider-slider-"+d.orientation));d._helpers.right.addClass(d.toThemeProperty("jqx-fill-state-normal"));d._helpers.left.addClass(d.toThemeProperty("jqx-fill-state-normal"))},_addTickContainers:function(){var c=this;c._bottomTicks.className=c.toThemeProperty("jqx-slider-tickscontainer");c._topTicks.className=c.toThemeProperty("jqx-slider-tickscontainer");var b="visible";if(!c.showTicks){b="hidden"}c._bottomTicks.style.visibility=b;c._topTicks.style.visibility=b},_updateButtonsVisibility:function(){var c=this;var b="block";if(!c.showButtons||c.rangeSlider){b="none"}c._rightButton.style.display=b;c._leftButton.style.display=b},_getNiceInterval:function(f){function u(w){return Math.log(parseFloat(w))/Math.LN10}var k=this,m,v="Width";if(k.orientation==="vertical"){v="Height"}var h=document.createElement("span");h.className=k.toThemeProperty("jqx-widget jqx-slider-label");h.style.position="absolute";h.style.visibility="hidden";if(k.tickLabelStyleSettings){var e=k.tickLabelStyleSettings;h.style.fontSize=e.fontSize;h.style.fontFamily=e.fontFamily;h.style.fontWeight=e.fontWeight;h.style.fontStyle=e.fontStyle}var s,t;s=k._formatLabel(k.min);t=k._formatLabel(k.max);var d=a.jqx.browser.msie?0:1;document.body.appendChild(h);h.innerHTML=s;var r=h["scroll"+v]+d;h.innerHTML=t;var i=h["scroll"+v]+d;h.parentNode.removeChild(h);var c=Math.max(i,r),j=0;if(c>105){j=(c-105)/100}c*=1.5+j;var b=k._getTrackSize();if(b>64&&k.showButtons===false){b-=64}var g=Math.round(b/c),o,q,l,p,n;if(g===0){g=1}if(f===true){g*=4}if(k.int64===false){o=k.max-k.min;q=Math.floor(u(o)-u(g));l=Math.pow(10,q);p=g*l;if(o<2*p){m=1}else{if(o<3*p){m=2}else{if(o<7*p){m=5}else{m=10}}}n=m*l}else{o=new BigNumber(k.max).subtract(new BigNumber(k.min));q=Math.floor(u(o.toString())-u(g));l=new BigNumber(10).pow(new BigNumber(q));p=new BigNumber(g).multiply(l);if(o.compare(new BigNumber(2*p))===-1){m=1}else{if(o.compare(new BigNumber(3*p))===-1){m=2}else{if(o.compare(new BigNumber(7*p))===-1){m=5}else{m=10}}}n=new BigNumber(m).multiply(l);if(n.compare(1)===-1){n=new BigNumber(1)}if(k.int64==="s"){n=new a.jqx.math().fromString(n.toString())}}return n},_formatLabel:function(f,e){var d=this,b=e!==true?d.tickLabelFormatFunction:d.tooltipFormatFunction,g=e!==true?d.tickLabelFormatSettings:d.tooltipFormatSettings,c;if(b){c=b(f)}else{if(g){if(g.radix!==undefined){c=new a.jqx.math().getRadixValue(f,d.int64,g.radix)}else{if(g.outputNotation!==undefined&&g.outputNotation!=="default"&&g.outputNotation!=="decimal"){c=new a.jqx.math().getDecimalNotation(f,g.outputNotation,g.decimalDigits,g.digits)}else{if(g.decimalDigits!==undefined){c=Number(f).toFixed(g.decimalDigits)}else{if(g.digits!==undefined){c=Number(Number(f).toPrecision(g.digits)).toString()}}}}}else{c=f}}return c},_addTicks:function(r,g){var n=this;if(!n.showTicks){return}var z=parseInt(r.style[n._getSetting("size")],10),f,u=(n.layout==="normal"&&n.orientation==="horizontal"&&n.rtl===false)||(n.layout==="reverse"&&n.orientation==="vertical"),e,x,q,l,I,d,o,B,F,b,G,C;var J="";if(n.int64===false){l=n.max-n.min;if(n.tickMode==="default"){if(n.niceInterval){x=n._getNiceInterval();q=n._getNiceInterval(true)}else{x=n.ticksFrequency;q=n.minorTicksFrequency}I=Math.round(l/x);d=Math.round(l/q)}else{if(n.tickMode==="tickNumber"){I=n.tickNumber;d=n.minorTickNumber;x=Math.round(l/I)}}B=n.min;F=n.max}else{if(n.int64==="s"){l=n._max64.subtract(n._min64);if(n.tickMode==="default"){if(n.niceInterval){x=n._getNiceInterval();q=n._getNiceInterval(true)}else{x=n._ticksFrequency64;q=n._minorTicksFrequency64}I=l.div(x).toNumber();d=l.div(q).toNumber()}else{if(n.tickMode==="tickNumber"){I=n.tickNumber;d=n.minorTickNumber;x=l.div(new a.jqx.math().fromNumber(I))}}B=n._min64.toString();F=n._max64.toString()}else{if(n.int64==="u"){l=n._max64.subtract(n._min64);if(n.tickMode==="default"){if(n.niceInterval){x=n._getNiceInterval();q=n._getNiceInterval(true)}else{x=n._ticksFrequency64;q=n._minorTicksFrequency64}I=parseInt(l.divide(x).toString(),10);d=parseInt(l.divide(q).toString(),10)}else{if(n.tickMode==="tickNumber"){I=n.tickNumber;d=n.minorTickNumber;x=l.divide(new BigNumber(I)).intPart()}}B=n._min64.toString();F=n._max64.toString()}}}var j=z/I;o=z/d;r.innerHTML="";if(u){e=n._formatLabel(B)}else{e=n._formatLabel(F)}var E=document.createElement("span");E.style.visibility="hidden";E.className=n.toThemeProperty("jqx-widget jqx-widget-content jqx-slider");if(n.tickLabelStyleSettings){var c=n.tickLabelStyleSettings;E.style.fontSize=c.fontSize;E.style.fontFamily=c.fontFamily;E.style.fontWeight=c.fontWeight;E.style.fontStyle=c.fontStyle}document.body.appendChild(E);E.innerHTML="0";var D={width:E.offsetWidth,height:E.offsetHeight};E.parentNode.removeChild(E);var v=parseInt(r.style[n._getSetting("oSize")],10);var w=n.orientation==="horizontal"?n.padding.left:0;J+=n._addTick(r,w,n.min,v,e,D,false,g);var h=document.createElement("span");h.className=n.toThemeProperty("jqx-widget");h.style.position="absolute";h.style.visibility="hidden";document.body.appendChild(h);h.innerHTML=n.min.toString();b=n.orientation==="horizontal"?h.offsetWidth:h.offsetHeight;var p=0,t=0;if(n.tickMode==="default"&&n.niceInterval===true){var k,H;if(n.int64===false){if(u){k=n.min;H=k-(k%x)+x;p=H-k}else{k=n.max;H=k-(k%x);p=k-H}t=p/x*j}else{var m=new BigNumber(x.toString());if(u){k=new BigNumber(n.min);H=k.subtract(k.mod(m)).add(m);p=H.subtract(k)}else{k=new BigNumber(n.max);H=k.subtract(k.mod(m));p=k.subtract(H)}t=parseFloat(p.divide(m).multiply(j).toString())}var s=true;if(b>=t){s=false}if(H.toString()!==n.max.toString()&&t<z){var y=n._formatLabel(H.toString());J+=n._addTick(r,t+w,H,v,y,D,false,g,s)}}for(G=1;G<I;G++){f=G*j+t;f=Math.floor(f);var A;if(n.int64===false){if(u){A=n.min+x*G+p}else{A=n.max-x*G-p}}else{if(n.int64==="s"){if(u){A=n._min64.add(x.multiply(new a.jqx.math().fromString(G.toString(),10))).add(new a.jqx.math().fromString(p.toString(),10)).toString()}else{A=n._max64.subtract(x.multiply(new a.jqx.math().fromString(G.toString(),10))).subtract(new a.jqx.math().fromString(p.toString(),10)).toString()}}else{if(n.int64==="u"){if(u){A=n._min64.add(x.multiply(G)).add(p).toString()}else{A=n._max64.subtract(x.multiply(G)).subtract(p).toString()}}}}if(A.toString()!==n.max.toString()){e=n._formatLabel(A.toString()),C=true;if(n.tickMode==="default"&&n.niceInterval===true){h.innerHTML=e;b=n.orientation==="horizontal"?h.offsetWidth:h.offsetHeight;if(f+b>=I*j){C=false}}J+=n._addTick(r,f+w,G,v,e,D,false,g,C)}}if(n.showMinorTicks){for(G=1;G<d;G++){f=G*o;f=Math.floor(f);e="";J+=n._addTick(r,f+w,G,v,e,D,true,g)}}if(u){e=n._formatLabel(F)}else{e=n._formatLabel(B)}J+=n._addTick(r,I*j+w,n.max,v,e,D,false,g);r.innerHTML=J;h.parentNode.removeChild(h)},_addTick:function(p,z,t,r,c,v,g,d,u){var k=this;var l="",j;l=k.toThemeProperty("jqx-slider-tick");l+=" "+k.toThemeProperty("jqx-fill-state-pressed");if(k.template){l+=" "+k.toThemeProperty("jqx-"+k.template)}var i;var q=k._getSetting("top");var m="2px";var s=k.tickSize;if(g){s=k.minorTickSize}if(p!==k._bottomTicks){m=r-s-2+"px"}if(k.orientation==="horizontal"){i='<div style="'+q+": "+m+"; "+k._getSetting("oSize")+":  "+s+"px; float: left; position:absolute; left:"+z+'px;" class="'+k.toThemeProperty("jqx-slider-tick-horizontal")+" "+l+'"></div>';if(k.showTickLabels){if(p!==k._bottomTicks){m=r-s-v.height-2+"px"}else{m=2+s+"px"}var n=v.width*c.toString().length;n=n/2;j=z-n;if(u!==false){var e="",y="",o="",f="";if(k.tickLabelStyleSettings){var b=k.tickLabelStyleSettings;if(b.fontSize){e=b.fontSize}if(b.fontFamily){y=b.fontFamily}if(b.fontWeight){o=b.fontWeight}if(b.fontStyle){f=b.fontStyle}}i+='<div class="'+k.toThemeProperty("jqx-slider-label jqx-slider-label-"+d)+'" style="'+q+": "+m+"; float: left; position:absolute; left:"+j+"px; white-space: nowrap; font-size: "+e+"; font-family: "+y+"; font-weight: "+o+"; font-style: "+f+'">'+c+"</div>"}}}else{i='<div style="'+q+": "+m+"; "+k._getSetting("oSize")+":  "+s+"px; float: none; position:absolute; top:"+z+'px;" class="'+k.toThemeProperty("jqx-slider-tick-vertical")+" "+l+'"></div>';if(k.showTickLabels){if(p!==k._bottomTicks){m=r-s-c.toString().length*v.width-6+"px"}else{m=6+s+"px"}var x=v.height;x=x/2;j=z-x;if(u!==false){i+='<div class="'+k.toThemeProperty("jqx-slider-label jqx-slider-label-"+d)+'" style="'+q+": "+m+"; float: none; position:absolute; top:"+j+'px;">'+c+"</div>"}}}return i},_addRangeBar:function(){var b=this;if(b._rangeBar===null||b._rangeBar.length<1){b._rangeBar=document.createElement("div");b._rangeBar.className=b.toThemeProperty("jqx-slider-rangebar jqx-fill-state-pressed jqx-rc-all");if(b.template){b._rangeBar.className+=" "+b.toThemeProperty("jqx-"+b.template)}b._helpers.rangeBar=new jqxHelper(b._rangeBar);b._track.appendChild(b._rangeBar)}if(!b.showRange){b._rangeBar.style.display="none"}else{b._rangeBar.style.display="block"}b._thumbSize=b._slider.left.offsetWidth},_getLeftDisplacement:function(){if(!this.showButtons){return 0}if(this.rangeSlider){return 0}switch(this.buttonsPosition){case"left":return this._leftButton[this._getSetting("outerSize")](true)+this._rightButton[this._getSetting("outerSize")](true);case"right":return 0;default:return this._leftButton[this._getSetting("outerSize")](true)}return 0},_performLayout:function(){var c=this;if(c.width!==null&&c.width.toString().indexOf("px")!==-1){c.element.style.width=parseInt(c.width,10)+"px"}else{if(c.width!==undefined&&!isNaN(c.width)){c.element.style.width=parseInt(c.width,10)+"px"}}if(c.height!==null&&c.height.toString().indexOf("px")!==-1){c.element.style.height=parseInt(c.height,10)+"px"}else{if(c.height!==undefined&&!isNaN(c.height)){c.element.style.height=parseInt(c.height,10)+"px"}}var g=false;if(c.width!==null&&c.width.toString().indexOf("%")!==-1){g=true;c._helpers.element.width(c.width)}if(c.height!==null&&c.height.toString().indexOf("%")!==-1){g=true;c._helpers.element.height(c.height)}var b=c._helpers.element.innerHeight();if(c._getSetting("size")==="width"){b=c._helpers.element.innerWidth()}c._performButtonsLayout();c._performTrackLayout(b-8);c._contentWrapper.style[c._getSetting("size")]=c._track.style[c._getSetting("size")];c._contentWrapper.style[c._getSetting("oSize")]=c.element.style[c._getSetting("oSize")];c._performTicksLayout();c._performRangeBarLayout();var e=c.padding;if(c.orientation==="horizontal"){c._contentWrapper.style.position="absolute";c._contentWrapper.style.left="0px";c._contentWrapper.style.top="0px";if(c.showButtons&&!c.rangeSlider){c._contentWrapper.style.left=4+c._helpers.leftButton.outerWidth(true)+"px";c._leftButton.style.left=e.left+"px";c._rightButton.style.right=e.right+"px";if(c.buttonsPosition==="left"){c._contentWrapper.style.left=2+2*c._helpers.leftButton.innerWidth()+c._helpers.left.innerWidth()/2+"px";c._rightButton.style.left=1+c._helpers.leftButton.innerWidth()+"px"}else{if(c.buttonsPosition==="right"){c._contentWrapper.style.left=c._helpers.left.innerWidth()/2+"px";c._leftButton.style.left="";c._leftButton.style.right=1+e.right+c._helpers.leftButton.innerWidth()+"px";c._rightButton.style.right=c._leftButton.style.right-c._helpers.leftButton.innerWidth()+"px"}}}if(!c.showButtons||c.rangeSlider){var f=(2+Math.ceil(c.sliderButtonSize/2));c._contentWrapper.style.left=f+"px"}}else{c._contentWrapper.style.position="absolute";c._contentWrapper.style.left="0px";c._contentWrapper.style.top="0px";if(c.showButtons&&!c.rangeSlider){c._contentWrapper.style.top=1+c._helpers.leftButton.outerHeight(true)+"px";c._leftButton.style.top="0px";c._rightButton.style.bottom="0px";c._leftButton.style.left="";c._leftButton.style.right="";c._rightButton.style.left="";c._rightButton.style.right="";if(c.buttonsPosition==="left"){c._contentWrapper.style.top=2+2*c._helpers.leftButton.innerHeight()+c._helpers.left.innerHeight()/2+"px";c._rightButton.style.top=1+c._helpers.leftButton.innerHeight()+"px"}else{if(c.buttonsPosition==="right"){c._contentWrapper.style.top=c._helpers.left.innerHeight()/2+"px";c._leftButton.style.top="";c._leftButton.style.bottom=1+c._helpers.leftButton.innerHeight()+"px";c._rightButton.style.bottom=c._leftButton.style.bottom-c._helpers.leftButton.innerHeight()+"px"}}}if(!c.showButtons||c.rangeSlider){var f=(2+Math.ceil(c.sliderButtonSize/2));c._contentWrapper.style.top=f+"px"}}if(c.rangeSlider){c._slider.left.style.visibility="visible"}else{c._slider.left.style.visibility="hidden"}c._refreshRangeBar();if(c.orientation==="vertical"){if(c.showButtons){var d=(c._leftButton.offsetWidth-c._track.offsetWidth)/2;c._track.style.marginLeft=1+"px"}}c._editableLabels()},_performTrackLayout:function(b){var d=this;var c=b;if(d.showButtons&&!d.rangeSlider){if(d.orientation==="horizontal"){c-=(d._helpers.leftButton.innerWidth()+d._helpers.rightButton.innerWidth()+4)}else{c-=(d._helpers.leftButton.innerHeight()+d._helpers.rightButton.innerHeight()+4)}}if(d.rangeSlider||!d.showButtons){var e=(2+Math.ceil(d.sliderButtonSize/2));c=b-2*e}if(d.orientation==="horizontal"){c=c-(d.padding.left+d.padding.right);c-=d._helpers.left.outerWidth()-2}else{c-=d._helpers.left.outerHeight()-2}d._track.style[d._getSetting("size")]=c+"px";d._track.style.left=d.padding.left+"px";d._slider.left.style.left="0px";d._slider.left.style.top="0px";d._slider.right.style.left="0px";d._slider.right.style.top="0px"},_performTicksLayout:function(){var b=this;b._performTicksContainerLayout();b._addTicks(this._topTicks,"top");b._addTicks(this._bottomTicks,"bottom");b._topTicks.style.visibility="hidden";b._bottomTicks.style.visibility="hidden";if((b.ticksPosition==="top"||b.ticksPosition==="both")&&b.showTicks){b._topTicks.style.visibility="visible"}if((b.ticksPosition==="bottom"||b.ticksPosition==="both")&&b.showTicks){b._bottomTicks.style.visibility="visible"}},_performTicksContainerLayout:function(){var c=this;var b;if(c.orientation==="horizontal"){c._topTicks.style.width=c._track.style.width;c._bottomTicks.style.width=c._track.style.width;b=-2+(parseInt(c.element.style.height,10)-c._helpers.track.outerHeight())/2;c._topTicks.style.height=b+"px";c._bottomTicks.style.height=b+"px";c._topTicks.style["float"]="none";c._track.style["float"]="none";c._bottomTicks.style["float"]="none"}else{c._topTicks.style.height=c._track.style.height;c._bottomTicks.style.height=c._track.style.height;b=-2+(parseInt(c.element.style.width,10)-c._helpers.track.outerWidth())/2;c._topTicks.style.width=b+"px";c._bottomTicks.style.width=b+"px";c._topTicks.style["float"]="left";c._track.style["float"]="left";c._bottomTicks.style["float"]="left"}},_performButtonsLayout:function(){this._updateButtonsVisibilityStyles();this._updateButtonsVisibilityClasses();this._updateButtonsVisibilityHover();this._centerElement(this._rightButton);this._centerElement(this._leftButton);this._layoutButtons()},_centerElement:function(c){var d=new jqxHelper(c);c.style.marginLeft="0px";c.style.marginTop="0px";c.style.marginRight="0px";c.style.marginBottom="0px";var b=(parseFloat(this.element.style[this._getSetting("oSize")])-parseFloat(d[this._getSetting("outerOSize")]()))/2;if(this.orientation==="horizontal"){c.style.marginLeft="0px";c.style.marginTop=b+"px"}else{c.style.marginTop="0px;";c.style.marginLeft=b+"px"}return c},_updateButtonsVisibilityStyles:function(){var b=this;b._leftButton.style.backgroundPosition="center";b._rightButton.style.backgroundPosition="center";if(b.orientation==="vertical"){b._leftButton.style["float"]="none";b._rightButton.style["float"]="none"}b._leftButton.style.position="absolute";b._rightButton.style.position="absolute"},_updateButtonsVisibilityClasses:function(){var c=this;var b={prev:"left",next:"right"};if(c.orientation==="vertical"){b={prev:"up",next:"down"}}c._helpers.leftButton.addClass(c.toThemeProperty("jqx-rc-all jqx-slider-button"));c._helpers.rightButton.addClass(c.toThemeProperty("jqx-rc-all jqx-slider-button"));c._leftArrow=c._leftButton.firstChild;c._rightArrow=c._rightButton.firstChild;c._helpers.leftArrow=new jqxHelper(c._leftArrow);c._helpers.rightArrow=new jqxHelper(c._rightArrow);c._helpers.leftArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-left"));c._helpers.rightArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-right"));c._helpers.leftArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-up"));c._helpers.rightArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-down"));c._helpers.leftArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev));c._helpers.rightArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.next))},_updateButtonsVisibilityHover:function(){var c=this,b={prev:"left",next:"right"};if(c.orientation==="vertical"){b={prev:"up",next:"down"}}c.removeHandler(a(document),"mouseup.arrow"+c.element.id);c.addHandler(a(document),"mouseup.arrow"+c.element.id,function(){c._helpers.leftArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev+"-selected"));c._helpers.rightArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.next+"-selected"));if(c.sliderTooltip){if(c.sliderTooltipTimer){clearTimeout(c.sliderTooltipTimer)}c.sliderTooltipTimer=setTimeout(function(){if(!c.isMaterialized()){c.sliderTooltipObj.fadeOut("fast")}a(c.sliderTooltipObj).removeClass("show");a(c.sliderTooltipObj).addClass("hide");c._mouseDown=false},c.tooltipHideDelay)}else{c._mouseDown=false}if(c.isMaterialized()){c._refreshRangeBar();setTimeout(function(){c._refreshRangeBar()},200)}});c.removeHandler(c._leftButton,"mousedown."+c.element.id);c.removeHandler(c._leftButton,"mouseup."+c.element.id);c.removeHandler(c._leftButton,"mouseenter."+c.element.id);c.removeHandler(c._leftButton,"mouseleave."+c.element.id);c.removeHandler(c._rightButton,"mousedown."+c.element.id);c.removeHandler(c._rightButton,"mouseup."+c.element.id);c.removeHandler(c._rightButton,"mouseenter."+c.element.id);c.removeHandler(c._rightButton,"mouseleave."+c.element.id);c.addHandler(c._leftButton,"mousedown."+c.element.id,function(){if(!c.disabled){c._helpers.leftArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev+"-selected"));c._mouseDown=true}});c.addHandler(c._leftButton,"mouseup."+c.element.id,function(){if(!c.disabled){c._helpers.leftArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev+"-selected"))}});c.addHandler(c._leftButton,"mouseenter."+c.element.id,function(){if(!c.disabled){c._helpers.leftArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev+"-hover"))}});c.addHandler(c._leftButton,"mouseleave."+c.element.id,function(){if(!c.disabled){c._helpers.leftArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.prev+"-hover"))}});c.addHandler(c._rightButton,"mousedown."+c.element.id,function(){if(!c.disabled){c._helpers.rightArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.next+"-selected"));c._mouseDown=true}});c.addHandler(c._rightButton,"mouseup."+c.element.id,function(){if(!c.disabled){c._helpers.rightArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.next+"-selected"))}});c.addHandler(c._rightButton,"mouseenter."+c.element.id,function(){if(!c.disabled){c._helpers.rightArrow.addClass(c.toThemeProperty("jqx-icon-arrow-"+b.next+"-hover"))}});c.addHandler(c._rightButton,"mouseleave."+c.element.id,function(){if(!c.disabled){c._helpers.rightArrow.removeClass(c.toThemeProperty("jqx-icon-arrow-"+b.next+"-hover"))}})},_layoutButtons:function(){var b=this;if(b.orientation==="horizontal"){b._horizontalButtonsLayout()}else{b._verticalButtonsLayout()}},_horizontalButtonsLayout:function(){var b=this;var c=(2+Math.ceil(b.sliderButtonSize/2));if(b.buttonsPosition==="left"){b._leftButton.style.marginRight="0px";b._rightButton.style.marginRight=c+"px"}else{if(b.buttonsPosition==="right"){b._leftButton.style.marginLeft=2+c+"px";b._rightButton.style.marginRight="0px"}else{b._leftButton.style.marginRight=c+"px";b._rightButton.style.marginLeft=c+"px"}}},_verticalButtonsLayout:function(){var c=this;var d=(2+Math.ceil(c.sliderButtonSize/2));if(c.buttonsPosition==="left"){c._leftButton.style.marginBottom="0px";c._rightButton.style.marginBottom=d+"px"}else{if(c.buttonsPosition==="right"){c._leftButton.style.marginTop=2+d+"px";c._rightButton.style.marginBottom="0px"}else{c._leftButton.style.marginBottom=d+"px";c._rightButton.style.marginTop=2+d+"px"}}var b=parseInt(c._leftButton.style.marginLeft,10);c._leftButton.style.marginLeft=(b-1)+"px";c._rightButton.style.marginLeft=(b-1)+"px"},_performRangeBarLayout:function(){var b=this;b._rangeBar.style[b._getSetting("oSize")]=b._helpers.track[b._getSetting("oSize")]()+"px";b._rangeBar.style[b._getSetting("size")]=b._helpers.track[b._getSetting("size")]()+"px";b._rangeBar.style.position="absolute";b._rangeBar.style.left="0px";b._rangeBar.style.top="0px"},_raiseEvent:function(g,c){var f=this;var d=f._events[g];var e=new a.Event(d,this.element);if(f._triggerEvents===false){return true}if(f._rendering){return true}e.args=c;if(g===0){e.args.type=f.changeType;f.changeType=null}if(g===1){e.args.cancel=false;f._slideEvent=e}f._lastValue[g]=c.value;e.owner=this;var b=f.host.trigger(e);return b},_initialSettings:function(){var b=this;if(b.int64===false){if(b.rangeSlider){if(typeof b.value!=="number"){b.setValue(b.value)}else{b.setValue(b.values)}}else{if(b.value===undefined){b.value=0}b.setValue(b.value)}}else{if(b.rangeSlider===false||Array.isArray(b._value64)===true){b.setValue(b._value64)}else{b.setValue(b._values64)}}if(b.disabled){b.disable()}},_addEventHandlers:function(){var d=this;d.addHandler(d._slider.right,d._getEvent("mousedown"),d._startDrag,{that:this});d.addHandler(d._slider.left,d._getEvent("mousedown"),d._startDrag,{that:this});d.addHandler(a(document),d._getEvent("mouseup")+"."+d.element.id,function(){d._stopDrag()});try{if(document.referrer!==""||window.frameElement){if(window.top!==null&&window.top!==window.self){var c=function(){d._stopDrag()};var e=null;if(window.parent&&document.referrer){e=document.referrer}if(e&&e.indexOf(document.location.host)!==-1){if(window.top.document){if(window.top.document.addEventListener){window.top.document.addEventListener("mouseup",c,false)}else{if(window.top.document.attachEvent){window.top.document.attachEvent("onmouseup",c)}}}}}}}catch(b){}d.addHandler(a(document),d._getEvent("mousemove")+"."+d.element.id,d._performDrag,{that:this});d.addHandler(d._slider.left,"mouseenter",function(){if(!d.disabled){d._helpers.left.addClass(d.toThemeProperty("jqx-fill-state-hover"))}});d.addHandler(d._slider.right,"mouseenter",function(){if(!d.disabled){d._helpers.right.addClass(d.toThemeProperty("jqx-fill-state-hover"))}});d.addHandler(d._slider.left,"mouseleave",function(){if(!d.disabled){d._helpers.left.removeClass(d.toThemeProperty("jqx-fill-state-hover"))}});d.addHandler(d._slider.right,"mouseleave",function(){if(!d.disabled){d._helpers.right.removeClass(d.toThemeProperty("jqx-fill-state-hover"))}});d.addHandler(d._slider.left,"mousedown",function(){if(!d.disabled){d._helpers.left.addClass(d.toThemeProperty("jqx-fill-state-pressed"))}});d.addHandler(d._slider.right,"mousedown",function(){if(!d.disabled){d._helpers.right.addClass(d.toThemeProperty("jqx-fill-state-pressed"))}});d.addHandler(d._slider.left,"mouseup",function(){if(!d.disabled){d._helpers.left.removeClass(d.toThemeProperty("jqx-fill-state-pressed"))}});d.addHandler(d._slider.right,"mouseup",function(){if(!d.disabled){d._helpers.right.removeClass(d.toThemeProperty("jqx-fill-state-pressed"))}});d.addHandler(d._leftButton,d._getEvent("click"),d._leftButtonHandler,{that:this});d.addHandler(d._rightButton,d._getEvent("click"),d._rightButtonHandler,{that:this});d.addHandler(d._track,d._getEvent("mousedown"),d._trackMouseDownHandler,{that:this});d.addHandler(d.host,"focus",function(){d._helpers.track.addClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.leftButton.addClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.rightButton.addClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.right.addClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.left.addClass(d.toThemeProperty("jqx-fill-state-focus"))});d.addHandler(d.host,"blur",function(){d._helpers.track.removeClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.leftButton.removeClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.rightButton.removeClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.right.removeClass(d.toThemeProperty("jqx-fill-state-focus"));d._helpers.left.removeClass(d.toThemeProperty("jqx-fill-state-focus"))});d.element.onselectstart=function(){return false};d._addMouseWheelListeners();d._addKeyboardListeners()},_addMouseWheelListeners:function(){var b=this;b.addHandler(b.host,"mousewheel",function(d){if(b.disabled){return true}b.changeType="mouse";if(document.activeElement&&!a(document.activeElement).ischildof(b.host)){return true}var c=d.wheelDelta;if(d.originalEvent&&d.originalEvent.wheelDelta){d.wheelDelta=d.originalEvent.wheelDelta}if(!("wheelDelta" in d)){c=d.detail*-40}if(c>0){b.incrementValue()}else{b.decrementValue()}d.preventDefault()})},_addKeyboardListeners:function(){var b=this;b.addHandler(b.host,"keydown",function(c){if(b._editingLabels===true){return}b.changeType="keyboard";switch(c.keyCode){case 40:case 37:if(b.layout==="normal"&&!b.rtl){b.decrementValue()}else{b.incrementValue()}return false;case 38:case 39:if(b.layout==="normal"&&!b.rtl){b.incrementValue()}else{b.decrementValue()}return false;case 36:if(b.rangeSlider){b.setValue([b.values[0],b.max])}else{b.setValue(b.min)}return false;case 35:if(b.rangeSlider){b.setValue([b.min,b.values[1]])}else{b.setValue(b.max)}return false}})},_trackMouseDownHandler:function(b){var g=b.data.that;var f=a.jqx.mobile.getTouches(b);var e=f[0];var c=parseInt(g._slider.left.style[g._getSetting("size")],10);c=g._getSetting("size")==="width"?g._slider.left.offsetWidth:g._slider.left.offsetHeight;if(isNaN(c)){c=0}var h=(g._isTouchDevice)?e:b,j=h[g._getSetting("page")]-c/2,d=g._getClosest(j);var i=g._getValueByPosition(j);g._mouseDown=true;g.changeType="mouse";g._setValue(i,d);if(g.input){a.jqx.aria(g,"aria-valuenow",g.input.value)}},_getClosest:function(b){var c=this;if(!c.rangeSlider){return c._slider.right}else{b=b-c._helpers.track.offset()[c._getSetting("left")]-c._helpers.left[c._getSetting("size")]()/2;if(Math.abs(parseInt(c._slider.left.style[c._getSetting("left")],10)-b)<Math.abs(parseInt(c._slider.right.style[c._getSetting("left")],10)-b)){return c._slider.left}else{return c._slider.right}}},_removeEventHandlers:function(){var b=this;b.removeHandler(b._slider.right,b._getEvent("mousedown"),b._startDrag);b.removeHandler(b._slider.left,b._getEvent("mousedown"),b._startDrag);b.removeHandler(a(document),b._getEvent("mouseup")+"."+b.host.attr("id"),b._stopDrag);b.removeHandler(a(document),b._getEvent("mousemove")+"."+b.host.attr("id"),b._performDrag);b.removeHandler(b._leftButton,b._getEvent("click"),b._leftButtonHandler);b.removeHandler(b._rightButton,b._getEvent("click"),b._rightButtonHandler);b.removeHandler(b._track,b._getEvent("mousedown"),b._trackMouseDownHandler);b.element.onselectstart=null;b.removeHandler(b.host,b._getEvent("mousewheel"));b.removeHandler(b.host,b._getEvent("keydown"))},_rightButtonClick:function(){var b=this;b.changeType="mouse";if(b.orientation==="horizontal"&&!b.rtl){b.incrementValue()}else{b.decrementValue()}},_leftButtonClick:function(){var b=this;b.changeType="mouse";if(b.orientation==="horizontal"&&!b.rtl){b.decrementValue()}else{b.incrementValue()}},_rightButtonHandler:function(c){var b=c.data.that;if(b.layout==="normal"){b._rightButtonClick()}else{b._leftButtonClick()}return false},_leftButtonHandler:function(c){var b=c.data.that;if(b.layout==="normal"){b._leftButtonClick()}else{b._rightButtonClick()}return false},_startDrag:function(d){var c=d.data.that;c.changeType="mouse";c._capturedElement=d.target;var e=new jqxHelper(d.target);var f=e.offset();c._startX=f.left;c._startY=f.top;var b=a.jqx.position(d);c._mouseStartX=b.left;c._mouseStartY=b.top;c._mouseDown=true;d.stopPropagation();if(c.tooltip){c._showTooltip(c._capturedElement,c.value)}if(c._isTouchDevice){return false}},_stopDrag:function(){var b=this;if(b._slideStarted){b._raiseEvent(2,{value:b.getValue()})}if(!b._slideStarted||b._capturedElement===null){b._capturedElement=null;return}if(b.input){a.jqx.aria(this,"aria-valuenow",b.input.value)}b._helpers.left.removeClass(b.toThemeProperty("jqx-fill-state-pressed"));b._helpers.right.removeClass(b.toThemeProperty("jqx-fill-state-pressed"));b._slideStarted=false;b._capturedElement=null;if(b.sliderTooltip){b.sliderTooltipTimer=setTimeout(function(){if(!b.isMaterialized()){b.sliderTooltipObj.fadeOut("fast")}a(b.sliderTooltipObj).removeClass("show");a(b.sliderTooltip).addClass("hide")},b.tooltipHideDelay)}},_performDrag:function(d){var c=d.data.that;if(c._capturedElement!==null){if(d.which===0&&a.jqx.browser.msie&&a.jqx.browser.version<9){c._stopDrag();return false}var b=a.jqx.position(d);var e=c.orientation==="horizontal"?b.left:b.top;c._isDragged(e);if(c._slideStarted||c._isTouchDevice){return c._dragHandler(e)}}},_isDragged:function(b){var c=this;if(Math.abs(b-this[c._getSetting("mouse")])>2&&!c._slideStarted){c._slideStarted=true;if(c._valueChanged(3)){c._raiseEvent(3,{value:c.getValue()})}}else{if(c._capturedElement==null){c._slideStarted=false}}},_dragHandler:function(b){b=(b-this[this._getSetting("mouse")])+this[this._getSetting("start")];var c=this._getValueByPosition(b);if(this.rangeSlider){var d=this._helpers.right,f=this._helpers.left;var e=this._getSetting("left");if(this._capturedElement===f){if(parseFloat(b)>d.offset()[e]){b=d.offset()[e]}}else{if(parseFloat(b)<f.offset()[e]){b=f.offset()[e]}}}this._setValue(c,this._capturedElement,b);return false},_getValueByPosition:function(b){if(this.mode==="default"){return this._getFloatingValueByPosition(b)}else{return this._getFixedValueByPosition(b)}},_getFloatingValueByPosition:function(b){var h=this;var c=b-h._helpers.track.offset()[h._getSetting("left")]+h._slider.left.offsetWidth/2,g=c/h._helpers.track[h._getSetting("size")](),i,d,e,f;if(c<0){c=0}if(h.int64===false){i=(h.max-h.min)*g+h.min}else{if(h.int64==="s"){f=new a.jqx.math().fromNumber(h._helpers.track[h._getSetting("size")](),10);d=h._max64.subtract(h._min64);e=h._divide64(d,f)*c;i=new a.jqx.math().fromNumber(e,10).add(h._min64)}else{if(h.int64==="u"){f=new BigNumber(h._helpers.track[h._getSetting("size")]());d=h._max64.subtract(h._min64);e=h._divide64(d,f)*c;i=new BigNumber(e).add(h._min64)}}}if(h.layout==="normal"){if(h.orientation==="horizontal"&&!h.rtl){return i}else{if(h.int64===false){return(h.max+h.min)-i}else{return(h._max64.add(h._min64)).subtract(i)}}}else{if(h.orientation==="horizontal"){if(h.int64===false){return(h.max+h.min)-i}else{return(h._max64.add(h._min64)).subtract(i)}}else{return i}}},_getThumbSize:function(){if(this.__thumbSize){return this.__thumbSize}var b=this._helpers.left[this._getSetting("size")]();this.__thumbSize=b;return b},_getTrackSize:function(){var c=this;if(c.__trackSize){return c.__trackSize}var b=parseInt(c._helpers.track[c._getSetting("size")](),10);c.__trackSize=b;return b},_getFixedValueByPosition:function(f){var h=this;var k=h._getTrackSize(),d=h._getThumbSize(),c,j,e,n={number:-1,distance:Number.MAX_VALUE},b,i,g,l;if(h.int64===false){b=h.step;g=(h.max-h.min)/b;i=(k)/g;l=h._helpers.track.offset()[h._getSetting("left")]-d/2;j=h.max+h.step;if(h.mode==="fixedRange"){j=h.max}for(e=h.min;e<=j;e+=h.step){if(Math.abs(n.distance-f)>Math.abs(l-f)){n.distance=l;n.number=e}l+=i}}else{if(h.int64==="s"){b=h._step64;g=(h._max64.subtract(h._min64)).div(h._step64);i=h._divide64(new a.jqx.math().fromNumber(k,10),g);l=h._helpers.track.offset()[h._getSetting("left")]-d/2;n={number:new a.jqx.math().fromString(h._min64.toString(),10),distance:l};for(c=new a.jqx.math().fromString(h._min64.toString(),10);h.mode!=="fixedRange"?c.lessThanOrEqual(h._max64.add(h._step64)):c.lessThanOrEqual(h._max64);c=c.add(h._step64)){if(Math.abs(n.distance-f)>Math.abs(l-f)){n.distance=l;n.number=new a.jqx.math().fromString(c.toString(),10)}l+=i}}else{if(h.int64==="u"){b=h._step64;g=(h._max64.subtract(h._min64)).divide(h._step64);i=parseFloat(new BigNumber(k).divide(g).toString());l=h._helpers.track.offset()[h._getSetting("left")]-d/2;n={number:new BigNumber(h._min64.toString()),distance:l};var m=h.mode!=="fixedRange"?h._max64.add(h._step64):h._max64;for(c=new BigNumber(h._min64.toString());c.compare(m)!==1;c=c.add(h._step64)){if(Math.abs(n.distance-f)>Math.abs(l-f)){n.distance=l;n.number=new BigNumber(c.toString())}l+=i}}}}if(h.layout==="normal"){if(h.orientation==="horizontal"&&!h.rtl){return n.number}else{if(h.int64===false){return(h.max+h.min)-n.number}else{return h._max64.add(h._min64).subtract(n.number)}}}else{if(h.orientation==="horizontal"){if(h.int64===false){return(h.max+h.min)-n.number}else{return h._max64.add(h._min64).subtract(n.number)}}else{return n.number}}},_setValue:function(e,d,b){var c=this;if(!c._slideEvent||!c._slideEvent.args.cancel){e=c._handleValue(e,d);c._setSliderPosition(e,d,b);c._fixZIndexes();if(c._valueChanged(1)){c._raiseEvent(1,{value:c.getValue()})}if(c._valueChanged(0)){c._raiseEvent(0,{value:c.getValue()})}if(!c.input){return}if(!c.rangeSlider){c.input.value=c.value.toString()}else{if(c.values&&(c.value.rangeEnd!==undefined&&c.value.rangeStart!==undefined)){c.input.value=(c.value.rangeStart.toString()+"-"+c.value.rangeEnd.toString())}}}},_valueChanged:function(c){var b=this.getValue();return(!this.rangeSlider&&this._lastValue[c]!==b)||(this.rangeSlider&&(typeof this._lastValue[c]!=="object"||parseFloat(this._lastValue[c].rangeEnd)!==parseFloat(b.rangeEnd)||parseFloat(this._lastValue[c].rangeStart)!==parseFloat(b.rangeStart)))},_handleValue:function(d,c){var b=this;d=b._validateValue(d,c);if(c===b._slider.left){if(b.int64===false){b.values[0]=d}else{b.values[0]=d.toString();b._value64[0]=d}}if(c===b._slider.right){if(b.int64===false){b.values[1]=d}else{b.values[1]=d.toString();b._values64[1]=d}}if(b.rangeSlider){b.value={rangeStart:b.values[0],rangeEnd:b.values[1]};if(b.int64!==false){b._value64={rangeStart:b._values64[0],rangeEnd:b._values64[1]}}}else{if(b.int64===false){b.value=d}else{b.value=d.toString();b._value64=d}}return d},_fixZIndexes:function(){if(this.values[1]-this.values[0]<0.5&&this.max-this.values[0]<0.5){this._slider.left.style.zIndex=20;this._slider.right.style.zIndex=15}else{this._slider.left.style.zIndex=15;this._slider.right.style.zIndex=20}},_refreshRangeBar:function(){var f=this._helpers.left;var g=this._helpers.right;var c=this._helpers.track;var b;var h=this._getSetting("left");var d=this._getSetting("size");var e=this.rtl&&this.orientation==="horizontal";if(this.layout==="normal"){b=f.position()[h];var i=a(f[0]).position()[h];if(this.orientation==="vertical"||e){b=g.position()[h]}}else{b=g.position()[h];if(this.orientation==="vertical"){b=f.position()[h]}}if(this.rangeSlider){this._rangeBar.style[h]=b+"px"}else{if(this.orientation==="horizontal"&&(e||this.layout!=="normal")){this._rangeBar.style[h]=b-c.position().left+f.innerWidth()/2+"px"}else{if(this.orientation==="vertical"){this._rangeBar.style[h]=b-c.position().top+f.innerHeight()/2+"px"}}}this._rangeBar.style[d]=Math.abs(g.position()[h]-f.position()[h])+"px"},_validateValue:function(c,b){if(this.int64===false){if(c>this.max){c=this.max}if(c<this.min){c=this.min}if(this.rangeSlider){if(b===this._slider.left){if(c>=this.values[1]){c=this.values[1]}}else{if(c<=this.values[0]){c=this.values[0]}}}}else{if(this.int64==="s"){if(c.greaterThan(this._max64)){c=this._max64}if(c.lessThan(this._min64)){c=this._min64}}else{if(this.int64==="u"){if(c.compare(this._max64)===1){c=this._max64}if(c.compare(this._min64)===-1){c=this._min64}}}}return c},_setSliderPosition:function(k,b,h){var j=parseInt(this._helpers.track[this._getSetting("size")](),10);var i,c,g,f;if(h){h-=this._helpers.track.offset()[this._getSetting("left")]}var d=parseInt(this._helpers.left[this._getSetting("size")](),10);if(isNaN(d)){d=0}if(this.int64==="s"){if(typeof k==="number"){k=new a.jqx.math().fromNumber(k,10)}else{if(typeof k==="string"){k=new a.jqx.math().fromString(k,10)}}if(k.greaterThan(this._max64)){k=new a.jqx.math().fromString(this._max64.toString(),10)}if(k.lessThan(this._min64)){k=new a.jqx.math().fromString(this._min64.toString(),10)}g=this._divide64(k.subtract(this._min64),this._max64.subtract(this._min64));f=1-g;if(this.layout==="normal"){i=g;if(this.orientation!=="horizontal"||(this.orientation==="horizontal"&&this.rtl)){i=f}}else{i=f;if(this.orientation!=="horizontal"){i=g}}c=j*i-d/2;b.style[this._getSetting("left")]=c+"px"}else{if(this.int64==="u"){if(typeof k==="number"||typeof k==="string"){k=new BigNumber(k)}if(k.compare(this._max64)===1){k=new BigNumber(this._max64)}if(k.compare(this._min64)===-1){k=new BigNumber(this._min64)}g=this._divide64(k.subtract(this._min64),this._max64.subtract(this._min64));f=1-g;if(this.layout==="normal"){i=g;if(this.orientation!=="horizontal"||(this.orientation==="horizontal"&&this.rtl)){i=f}}else{i=f;if(this.orientation!=="horizontal"){i=g}}c=j*i-d/2;b.style[this._getSetting("left")]=c+"px"}else{if(this.int64===false){if(this.layout==="normal"){i=(k-this.min)/(this.max-this.min);if(this.orientation!=="horizontal"||(this.orientation==="horizontal"&&this.rtl)){i=1-((k-this.min)/(this.max-this.min))}}else{i=1-((k-this.min)/(this.max-this.min));if(this.orientation!=="horizontal"){i=(k-this.min)/(this.max-this.min)}}c=j*i-d/2;var e=b.style[this._getSetting("left")];b.style[this._getSetting("left")]=c+"px";if(this.tooltip){this._showTooltip(b,this.value)}this._refreshRangeBar()}}}if(this.tooltip){this._showTooltip(b,this.value)}if(this.value>0){a(b).addClass(this.toThemeProperty("jqx-slider-has-value"))}else{a(b).removeClass(this.toThemeProperty("jqx-slider-has-value"))}this._refreshRangeBar()},_divide64:function(e,b){var h,j,c,k,m;h=e.toString();c=b.toString();if(c.length>15){var l=c.length-15;c=c.slice(0,15)+"."+c.slice(15);k=parseFloat(c);if(h.length>l){var g=h.length-l;h=h.slice(0,g)+"."+h.slice(g)}else{if(h.length===l){h="0."+h}else{var f="0.";for(var d=0;d<l-h.length;d++){f+="0"}h=f+""+h}}j=parseFloat(h)}else{if(this.int64==="s"){j=e.toNumber();k=b.toNumber()}else{j=parseFloat(e.toString());k=parseFloat(b.toString())}}m=j/k;return m},_showTooltip:function(s,n){var h=this;if(h._slideStarted||h._capturedElement!=null||h._mouseDown){n=h._formatLabel(n,true);if(!h.toolTipCreated){var c="tooltip"+h.element.id;var g=document.createElement("div");g.style.display="none";g.style.position="absolute";g.style.visibility="hidden";g.style.boxShadow="none";g.style.top="0px";g.style.left="0px";g.style.zIndex=99999;g.setAttribute("id",c);document.body.appendChild(g);var i=document.createElement("div");i.setAttribute("id",c+"Main");g.appendChild(i);var q=document.createElement("div");q.setAttribute("id",c+"Text");i.appendChild(q);var p=document.createElement("div");p.setAttribute("id",c+"Arrow");p.style.top="0px";p.style.left="0px";g.appendChild(p);h.sliderTooltip=g;h.sliderTooltipObj=new jqxHelper(h.sliderTooltip);if(h.sliderTooltipObj.initAnimate){h.sliderTooltipObj.initAnimate()}q.innerHTML="<span>"+n+"</span>";h.sliderTooltip.className=h.toThemeProperty("jqx-slider-tooltip jqx-tooltip jqx-popup "+h.orientation);if(h.rangeSlider){h.sliderTooltip.className+=" range"}if(h.template){h.sliderTooltip.className+=" jqx-"+h.template+"-slider"}i.className=h.toThemeProperty("jqx-widget jqx-fill-state-normal jqx-tooltip-main");q.className=h.toThemeProperty("jqx-widget jqx-fill-state-normal jqx-tooltip-text");p.className=h.toThemeProperty("jqx-widget jqx-fill-state-normal jqx-tooltip-arrow");h.sliderTooltipContent=q;h.sliderTooltipArrow=p;h.sliderTooltipMain=i;h.sliderTooltipArrowObj=new jqxHelper(h.sliderTooltipArrow);h.arrowSize=5;h.toolTipCreated=true;if(h.rangeSlider){h.sliderTooltipArrow.style.visibility="hidden"}}var k=new jqxHelper(s).offset();var f=new jqxHelper(s).outerWidth();h.sliderTooltip.style.display="block";h.sliderTooltip.style.visibility="visible";a(h.sliderTooltip).addClass("init");a(h.sliderTooltip).removeClass("hide");setTimeout(function(){a(h.sliderTooltip).addClass("show")});var l=h.sliderButtonSize+h.tickSize;if(!h.rangeSlider){h.sliderTooltipContent.innerHTML="<span>"+n.toString()+"</span>"}else{var o=h.value?h.value.rangeStart:"";var b=h.value?h.value.rangeEnd:"";if(o!==""){h.sliderTooltipContent.innerHTML="<span>"+o+" - "+b+"</span>"}else{if(!h.isMaterialized()){h.sliderTooltip.style.display="none";h.sliderTooltip.style.visibility="hidden"}a(h.sliderTooltip).removeClass("show");a(h.sliderTooltip).addClass("hide")}}var r=h.sliderTooltip.offsetWidth;var j,e,d;if(h.orientation==="horizontal"){e=1+k.left+f/2-r/2;if(h.rangeSlider){d=(h._helpers.right.offset().left-h._helpers.left.offset().left-h._thumbSize)/2;e=h._helpers.left.offset().left-r/2+d+h._thumbSize}switch(h.tooltipPosition){case"far":j=k.top+l+h.arrowSize+6;h.sliderTooltipObj.offset({top:j,left:e});h.sliderTooltipObj.addClass(h.toThemeProperty("far"));h.sliderTooltipArrowObj.addClass(h.toThemeProperty("jqx-tooltip-arrow-t-b"));h.sliderTooltipArrow.style.borderTopWidth="0px";h.sliderTooltipArrow.style.borderRightWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderBottomWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderLeftWidth=h.arrowSize+"px";h.sliderTooltipArrowObj.offset({top:j-h.arrowSize+1,left:e-h.arrowSize/2-1+r/2});break;case"near":j=k.top-h.arrowSize-h.sliderTooltipObj.innerHeight()-4;h.sliderTooltipObj.offset({top:j,left:e});h.sliderTooltipArrowObj.addClass(h.toThemeProperty("jqx-tooltip-arrow-t-b"));h.sliderTooltipArrow.style.borderTopWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderRightWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderBottomWidth="0px";h.sliderTooltipArrow.style.borderLeftWidth=h.arrowSize+"px";h.sliderTooltipArrowObj.offset({top:j+h.sliderTooltipObj.innerHeight(),left:e-h.arrowSize/2-1+r/2});break}}else{var m=h.sliderTooltipObj.innerHeight();e=k.left-r-h.arrowSize-h.tickSize-2;j=k.top+h._thumbSize/2-m/2-1;if(h.rangeSlider){d=(h._helpers.right.offset().top-h._helpers.left.offset().top-h._thumbSize)/2;j=h._helpers.left.offset().top-m/2+d+h._thumbSize}switch(h.tooltipPosition){case"far":e=k.left+h._thumbSize+h.arrowSize+h.tickSize;h.sliderTooltipObj.offset({top:j,left:e});h.sliderTooltipObj.addClass(h.toThemeProperty("far"));h.sliderTooltipArrowObj.addClass(h.toThemeProperty("jqx-tooltip-arrow-l-r"));h.sliderTooltipArrow.style.borderTopWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderRightWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderBottomWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderLeftWidth="0px";h.sliderTooltipArrowObj.offset({top:j+h.sliderTooltipObj.innerHeight()/2-h.arrowSize/2-2,left:e-h.arrowSize+1});break;case"near":h.sliderTooltipObj.offset({top:j,left:e+2});h.sliderTooltipArrowObj.addClass(h.toThemeProperty("jqx-tooltip-arrow-l-r"));h.sliderTooltipArrow.style.borderTopWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderRightWidth="0px";h.sliderTooltipArrow.style.borderBottomWidth=h.arrowSize+"px";h.sliderTooltipArrow.style.borderLeftWidth=h.arrowSize+"px";h.sliderTooltipArrowObj.offset({top:j+h.sliderTooltipObj.innerHeight()/2-h.arrowSize/2-2,left:e+r+3});break}}a(h.sliderTooltip).removeClass("init")}},propertiesChangedHandler:function(d,b,c){if(c&&c.width&&c.height&&Object.keys(c).length===2){d.__trackSize=null;d.__thumbSize=null;d._performLayout();d._initialSettings()}},propertyChangedHandler:function(c,b,f,e){c.__trackSize=null;c.__thumbSize=null;if(c.batchUpdate&&c.batchUpdate.width&&c.batchUpdate.height&&Object.keys(c.batchUpdate).length===2){return}switch(b){case"template":if(c.template){c._helpers.left.removeClass(c.toThemeProperty("jqx-"+f));c._helpers.right.removeClass(c.toThemeProperty("jqx-"+f));c._helpers.rangeBar.removeClass(c.toThemeProperty("jqx-"+f));c._helpers.left.addClass(c.toThemeProperty("jqx-"+c.template));c._helpers.right.addClass(c.toThemeProperty("jqx-"+c.template));a(c._leftButton).jqxRepeatButton({template:e});a(c._rightButton).jqxRepeatButton({template:e});c._helpers.rangeBar.addClass(c.toThemeProperty("jqx-"+c.template))}break;case"theme":a.jqx.utilities.setTheme(f,e,c.host);a(c._leftButton).jqxRepeatButton({theme:e});a(c._rightButton).jqxRepeatButton({theme:e});break;case"disabled":if(e){c.disabled=true;c.disable()}else{c.disabled=false;c.enable()}break;case"width":case"height":c.__trackSize=null;c.__thumbSize=null;c._performLayout();c._initialSettings();break;case"min":case"max":if(c.int64==="s"){c["_"+b+"64"]=new a.jqx.math().fromString(e.toString(),10)}else{if(c.int64==="u"){c["_"+b+"64"]=new BigNumber(e)}}c._performLayout();c.__trackSize=null;c.__thumbSize=null;c._initialSettings();break;case"showTicks":case"ticksPosition":case"tickSize":case"tickMode":case"tickNumber":case"minorTickNumber":c._performLayout();c._initialSettings();break;case"ticksFrequency":case"minorTicksFrequency":if(c.int64==="s"){c["_"+b+"64"]=new a.jqx.math().fromString(e.toString(),10)}else{if(c.int64==="u"){c["_"+b+"64"]=new BigNumber(e)}}c._performLayout();c._initialSettings();break;case"showRange":case"showButtons":case"orientation":case"rtl":c._render();c._performLayout();c._initialSettings();if(b==="orientation"){if(e==="vertical"){c.element.style.minWidth="96px"}else{c.element.style.minWidth=""}}break;case"buttonsPosition":c._refresh();break;case"rangeSlider":if(!e){c.value=c.value.rangeEnd}else{c.value={rangeEnd:c.value,rangeStart:c.value}}c._render();c._performLayout();c._initialSettings();break;case"value":var g=e;if(c.int64==="s"){g=new a.jqx.math().fromString(e.toString(),10);c._value64=g}else{if(c.int64==="u"){g=new BigNumber(e);c._value64=g}else{if(c.int64===false){if(!c.rangeSlider){c.value=parseFloat(e)}}}}c.setValue(g);break;case"values":var d=e;if(c.int64==="s"){d=[new a.jqx.math().fromString(e[0].toString(),10),new a.jqx.math().fromString(e[1].toString(),10)];c._values64=d}else{if(c.int64==="u"){d=[new BigNumber(e[0]),new BigNumber(e[1])];c._values64=d}}c.setValue(d);break;case"tooltip":break;case"step":if(c.int64==="s"){c._step64=new a.jqx.math().fromString(e.toString(),10)}else{if(c.int64==="u"){c._step64=new BigNumber(e)}}break;case"editableLabels":c._performLayout();c._initialSettings();break;case"tickLabelStyleSettings":c._setPaddingValues(true);c._performLayout();c._initialSettings();break;default:c._refresh()}},incrementValue:function(c){var b=this;var d;if(b.int64===false){if(c===undefined||isNaN(parseFloat(c))){c=b.step}if(b.rangeSlider){if(b.values[1]<b.max){b._setValue(b.values[1]+c,b._slider.right)}}else{if(b.values[1]>=b.min&&b.values[1]<b.max){b._setValue(b.values[1]+c,b._slider.right)}}}else{if(b.int64==="s"){if(c===undefined||isNaN(parseFloat(c))){c=b._step64}else{c=new a.jqx.math().fromString(c.toString(),10)}d=b._values64[1].add(c);if(d.lessThan(b._values64[1])){d=b._max64}if(b.rangeSlider){if(b._values64[1].lessThan(b._max64)){b._setValue(d,b._slider.right)}}else{if(b._values64[1].greaterThanOrEqual(b._min64)&&b._values64[1].lessThan(b._max64)){b._setValue(d,b._slider.right)}}}else{if(b.int64==="u"){if(c===undefined||isNaN(parseFloat(c))){c=b._step64}else{c=new BigNumber(c)}d=b._values64[1].add(c);if(d.compare(b._values64[1])===-1){d=b._max64}if(b.rangeSlider){if(b._values64[1].compare(b._max64)===-1){b._setValue(d,b._slider.right)}}else{if(b._values64[1].compare(b._min64)!==-1&&b._values64[1].compare(b._max64)===-1){b._setValue(d,b._slider.right)}}}}}if(b.input){a.jqx.aria(this,"aria-valuenow",b.input.value)}},decrementValue:function(c){var b=this;var d;if(b.int64===false){if(c===undefined||isNaN(parseFloat(c))){c=b.step}if(b.rangeSlider){if(b.values[0]>b.min){b._setValue(b.values[0]-c,b._slider.left)}}else{if(b.values[1]<=b.max&&b.values[1]>b.min){b._setValue(b.values[1]-c,b._slider.right)}}}else{if(b.int64==="s"){if(c===undefined||isNaN(parseFloat(c))){c=b._step64}else{c=new a.jqx.math().fromString(c.toString(),10)}if(b.rangeSlider){d=b._values64[0].subtract(c);if(d.greaterThan(b._values64[0])){d=b._min64}if(b._values64[0].greaterThan(b._min64)){b._setValue(d,b._slider.left)}}else{d=b._values64[1].subtract(c);if(d.greaterThan(b._values64[1])){d=b._min64}if(b._values64[1].lessThanOrEqual(b._max64)&&b._values64[1].greaterThan(b._min64)){b._setValue(d,b._slider.right)}}}else{if(b.int64==="u"){if(c===undefined||isNaN(parseFloat(c))){c=b._step64}else{c=new BigNumber(c)}if(b.rangeSlider){d=b._values64[0].subtract(c);if(d.compare(b._values64[0])===1){d=b._min64}if(b._values64[0].compare(b._min64)===1){b._setValue(d,b._slider.left)}}else{d=b._values64[1].subtract(c);if(d.compare(b._values64[1])===1){d=b._min64}if(b._values64[1].compare(b._max64)!==1&&b._values64[1].compare(b._min64)===1){b._setValue(d,b._slider.right)}}}}}if(b.input){a.jqx.aria(this,"aria-valuenow",b.input.value)}},val:function(d){var c=this;var b;if(arguments.length===0||(!a.isArray(d)&&typeof(d)==="object")){return c.getValue()}if(c.int64===false){c.setValue(d)}else{if(c.int64==="s"){b=new a.jqx.math().fromString(d.toString(),10);c.setValue(b)}else{if(c.int64==="u"){b=new BigNumber(d);c.setValue(b)}}}},setValue:function(f){var e=this;if(e.int64!==false&&(typeof f==="string"||typeof f==="number")){if(e.int64==="s"){if(typeof f==="string"){f=new a.jqx.math().fromString(f,10)}else{if(typeof f==="number"){f=new a.jqx.math().fromNumber(f,10)}}}else{if(e.int64==="u"){f=new BigNumber(f)}}}if(e.rangeSlider){var c,b;if(arguments.length<2){if(f instanceof Array){c=f[0];b=f[1]}else{if(typeof f==="object"&&typeof f.rangeStart!=="undefined"&&typeof f.rangeEnd!=="undefined"){c=f.rangeStart;b=f.rangeEnd}}}else{c=arguments[0];b=arguments[1]}e._triggerEvents=false;e._setValue(b,e._slider.right);e._triggerEvents=true;e._setValue(c,e._slider.left)}else{e._triggerEvents=false;var d;if(e.int64===false){d=e.min}else{d=e._min64}e._setValue(d,e._slider.left);e._triggerEvents=true;e._setValue(f,e._slider.right)}if(e.input){a.jqx.aria(this,"aria-valuenow",e.input.value)}},getValue:function(){var b=this.value;if(this.int64!==false){b=this._value64.toString()}return b},_enable:function(c){var b=this;if(c){b._addEventHandlers();b.disabled=false;b._helpers.element.removeClass(this.toThemeProperty("jqx-fill-state-disabled"))}else{b._removeEventHandlers();b.disabled=true;b._helpers.element.addClass(this.toThemeProperty("jqx-fill-state-disabled"))}a(b._leftButton).jqxRepeatButton({disabled:this.disabled});a(b._rightButton).jqxRepeatButton({disabled:this.disabled})},disable:function(){this._enable(false);a.jqx.aria(this,"aria-disabled",true)},enable:function(){this._enable(true);a.jqx.aria(this,"aria-disabled",false)},_setPaddingValues:function(h){var f=this,b,j;var i=document.createElement("span");i.className=f.toThemeProperty("jqx-widget jqx-slider-label");i.style.position="absolute";i.visibility="hidden";if(f.tickLabelStyleSettings){var c=f.tickLabelStyleSettings;i.style.fontSize=c.fontSize;i.style.fontFamily=c.fontFamily;i.style.fontWeight=c.fontWeight;i.style.fontStyle=c.fontStyle}if(f.layout==="normal"){b=f._formatLabel(f.min);j=f._formatLabel(f.max)}else{b=f._formatLabel(f.max);j=f._formatLabel(f.min)}document.body.appendChild(i);i.innerHTML=b;var d=f.orientation==="horizontal"?i.offsetWidth:i.offsetHeight;i.innerHTML=j;var e=f.orientation==="horizontal"?i.offsetWidth:i.offsetHeight;i.parentNode.removeChild(i);function g(m){var l,n,k;if(f.showButtons===true){l=27;n=0}else{l=0;n=8}k=Math.ceil(m/2)+1-l;k=Math.max(k,n);return k}if(h===true||(h!==true&&(f.padding===undefined||a.isEmptyObject(f.padding)))){if(f.orientation==="horizontal"){f.padding={left:g(d),right:g(e)}}else{f.padding={bottom:g(d),top:g(e)}}}},_editableLabels:function(){var f=this;function b(w){var u=document.createElement("span");u.className=f.toThemeProperty("jqx-widget jqx-slider-label");u.style.position="absolute";u.style.visibility="hidden";document.body.appendChild(u);u.innerHTML=w;var v={width:u.scrollWidth,height:u.scrollHeight};u.parentNode.removeChild(u);return v}function o(v,w){if(f.disabled){return}var u=b(f._formatLabel(w));p.offset(a(v).offset());h.style.width=(u.width+10)+"px";h.style.height=u.height+"px";h.style.visibility="visible";h.value=w;h.select();f._editingLabels=true}function e(z,x,y,v){if(z===f[x].toString()){return false}if(f.int64==="s"){var w=new a.jqx.math().fromString(z,10);if((x==="min"&&w.compare(f["_"+v+"64"])!==-1)||(x==="max"&&w.compare(f["_"+v+"64"])!==1)){return false}f[y]=w;f[x]=z}else{if(f.int64==="u"){var u=new BigNumber(z);if(u.compare(0)===-1||(x==="min"&&u.compare(f["_"+v+"64"])!==-1)||(x==="max"&&u.compare(f["_"+v+"64"])!==1)){return false}f[y]=u;f[x]=z}else{if((x==="min"&&z>=f[v])||(x==="max"&&z<=f[v])){return false}f[x]=parseFloat(z)}}}if(f.showTickLabels&&f.editableLabels){var j=f.element.id,d=f.element.getElementsByClassName("jqx-slider-label-top"),k=f.element.getElementsByClassName("jqx-slider-label-bottom"),t=f.ticksPosition,r=/^-?\d+\.?\d*$/,h,p;if(t==="both"||t==="top"){var i=d[0],g=d[d.length-1],s,l;if((f.orientation==="horizontal"&&f.layout==="normal"&&f.rtl===false)||(f.orientation==="vertical"&&f.layout==="reverse")){s=i;l=g}else{s=g;l=i}f.addHandler(a(s),"dblclick.jqxSlider"+j,function(){o(this,f.min);f._editedProperty="min"});f.addHandler(a(l),"dblclick.jqxSlider"+j,function(){o(this,f.max);f._editedProperty="max"})}if(t==="both"||t==="bottom"){var q=k[0],m=k[k.length-1],n,c;if((f.orientation==="horizontal"&&f.layout==="normal"&&f.rtl===false)||(f.orientation==="vertical"&&f.layout==="reverse")){n=q;c=m}else{n=m;c=q}f.addHandler(a(n),"dblclick.jqxSlider"+j,function(){o(this,f.min);f._editedProperty="min"});f.addHandler(a(c),"dblclick.jqxSlider"+j,function(){o(this,f.max);f._editedProperty="max"})}if(f._labelInputCreated!==true){h=document.createElement("input");h.className="jqx-slider-label-input";f.element.appendChild(h)}else{h=f.element.querySelector(".jqx-slider-label-input")}p=a(h);if(f._labelInputCreated!==true){f.addHandler(p,"blur.jqxGauge"+f.element.id,function(){var v=this.value,u;h.style.visibility="hidden";if(!r.test(v)){return}if(f._editedProperty==="min"){u=e(v,"min","_min64","max");if(u===false){return}}else{u=e(v,"max","_max64","min");if(u===false){return}}f._refresh();f._editingLabels=false});f._labelInputCreated=true}}}})})(jqxBaseFramework)})();

