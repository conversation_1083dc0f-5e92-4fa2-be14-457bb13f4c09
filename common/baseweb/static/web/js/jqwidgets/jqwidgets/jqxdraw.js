/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(a){a.jqx.jqxWidget("jqxDraw","",{});a.extend(a.jqx._jqxDraw.prototype,{defineInstance:function(){var c={renderEngine:""};a.extend(true,this,c);var d=["clear","on","off","removeElement","attr","getAttr","line","circle","rect","path","pieslice","text","measureText"];for(var b in d){this._addFn(a.jqx._jqxDraw.prototype,d[b])}if(this===a.jqx._jqxDraw.prototype){return c}return c},_addFn:function(c,b){if(c[b]){return}c[b]=function(){return this.renderer[b].apply(this.renderer,arguments)}},createInstance:function(b){},_initRenderer:function(b){return a.jqx.createRenderer(this,b)},_internalRefresh:function(){var b=this;if(a.jqx.isHidden(b.host)){return}if(!b.renderer){b.host.empty();b._initRenderer(b.host)}var d=b.renderer;if(!d){return}var c=d.getRect();b._render({x:1,y:1,width:c.width,height:c.height});if(d instanceof a.jqx.HTML5Renderer){d.refresh()}},_saveAsImage:function(d,e,b,c){return a.jqx._widgetToImage(this,d,e,b,c)},_render:function(c){var b=this;var d=b.renderer;b._plotRect=c},refresh:function(){this._internalRefresh()},getSize:function(){var b=this._plotRect;return{width:b.width,height:b.height}},saveAsPNG:function(d,b,c){return this._saveAsImage("png",d,b,c)},saveAsJPEG:function(d,b,c){return this._saveAsImage("jpeg",d,b,c)}})})(jqxBaseFramework);(function(a){a.jqx.toGreyScale=function(b){if(b.indexOf("#")==-1){return b}var c=a.jqx.cssToRgb(b);c[0]=c[1]=c[2]=Math.round(0.3*c[0]+0.59*c[1]+0.11*c[2]);var d=a.jqx.rgbToHex(c[0],c[1],c[2]);return"#"+d[0]+d[1]+d[2]},a.jqx.adjustColor=function(e,d){if(typeof(e)!="string"){return"#000000"}if(e.indexOf("#")==-1){return e}var f=a.jqx.cssToRgb(e);var b=a.jqx.rgbToHsl(f);b[2]=Math.min(1,b[2]*d);b[1]=Math.min(1,b[1]*d*1.1);f=a.jqx.hslToRgb(b);var e="#";for(var g=0;g<3;g++){var h=Math.round(f[g]);h=a.jqx.decToHex(h);if(h.toString().length==1){e+="0"}e+=h}return e.toUpperCase()};a.jqx.decToHex=function(b){return b.toString(16)};a.jqx.hexToDec=function(b){return parseInt(b,16)};a.jqx.rgbToHex=function(e,d,c){return[a.jqx.decToHex(e),a.jqx.decToHex(d),a.jqx.decToHex(c)]};a.jqx.hexToRgb=function(c,d,b){return[a.jqx.hexToDec(c),a.jqx.hexToDec(d),a.jqx.hexToDec(b)]};a.jqx.cssToRgb=function(b){if(b.indexOf("rgb")<=-1){return a.jqx.hexToRgb(b.substring(1,3),b.substring(3,5),b.substring(5,7))}return b.substring(4,b.length-1).split(",")};a.jqx.hslToRgb=function(m){var i=parseFloat(m[0]);var n=parseFloat(m[1]);var f=parseFloat(m[2]);if(n==0){var c,j,k;c=j=k=f}else{var d=f<0.5?f*(1+n):f+n-f*n;var e=2*f-d;var c=a.jqx.hueToRgb(e,d,i+1/3);var j=a.jqx.hueToRgb(e,d,i);var k=a.jqx.hueToRgb(e,d,i-1/3)}return[c*255,j*255,k*255]};a.jqx.hueToRgb=function(d,c,b){if(b<0){b+=1}if(b>1){b-=1}if(b<1/6){return d+(c-d)*6*b}else{if(b<1/2){return c}else{if(b<2/3){return d+(c-d)*(2/3-b)*6}}}return d};a.jqx.rgbToHsl=function(j){var c=parseFloat(j[0])/255;var i=parseFloat(j[1])/255;var k=parseFloat(j[2])/255;var m=Math.max(c,i,k),e=Math.min(c,i,k);var f,o,d=(m+e)/2;if(m==e){f=o=0}else{var n=m-e;o=d>0.5?n/(2-m-e):n/(m+e);switch(m){case c:f=(i-k)/n+(i<k?6:0);break;case i:f=(k-c)/n+2;break;case k:f=(c-i)/n+4;break}f/=6}return[f,o,d]};a.jqx.swap=function(b,d){var c=b;b=d;d=c};a.jqx.getNum=function(b){if(!a.isArray(b)){if(isNaN(b)){return 0}}else{for(var c=0;c<b.length;c++){if(!isNaN(b[c])){return b[c]}}}return 0};a.jqx._ptdist=function(c,e,b,d){return Math.sqrt((b-c)*(b-c)+(d-e)*(d-e))};a.jqx._ptrnd=function(c){if(!document.createElementNS){if(Math.round(c)==c){return c}return a.jqx._rnd(c,1,false,true)}var b=a.jqx._rnd(c,0.5,false,true);if(Math.abs(b-Math.round(b))!=0.5){return b>c?b-0.5:b+0.5}return b};a.jqx._ptRotate=function(d,i,c,h,f){var b=Math.sqrt(Math.pow(Math.abs(d-c),2)+Math.pow(Math.abs(i-h),2));var e=Math.asin((d-c)/b);var g=e+f;d=c+Math.cos(g)*b;i=h+Math.sin(g)*b;return{x:d,y:i}};a.jqx._rup=function(c){var b=Math.round(c);if(c>b){b++}return b};a.jqx.log=function(c,b){return Math.log(c)/(b?Math.log(b):1)};a.jqx._mod=function(d,c){var e=Math.abs(d>c?c:d);var f=1;if(e!=0){while(e*f<100){f*=10}}d=d*f;c=c*f;return(d%c)/f};a.jqx._rnd=function(d,f,e,c){if(isNaN(d)){return d}if(undefined===c){c=true}var b=d-((c==true)?d%f:a.jqx._mod(d,f));if(d==b){return b}if(e){if(d>b){b+=f}}else{if(b>d){b-=f}}return(f==1)?Math.round(b):b};a.jqx.commonRenderer={pieSlicePath:function(k,j,h,r,A,B,d){if(!r){r=1}var m=Math.abs(A-B);var p=m>180?1:0;if(m>=360){B=A+359.99}var q=A*Math.PI*2/360;var i=B*Math.PI*2/360;var w=k,v=k,f=j,e=j;var n=!isNaN(h)&&h>0;if(n){d=0}if(d+h>0){if(d>0){var l=m/2+A;var z=l*Math.PI*2/360;k+=d*Math.cos(z);j-=d*Math.sin(z)}if(n){var u=h;w=k+u*Math.cos(q);f=j-u*Math.sin(q);v=k+u*Math.cos(i);e=j-u*Math.sin(i)}}var t=k+r*Math.cos(q);var s=k+r*Math.cos(i);var c=j-r*Math.sin(q);var b=j-r*Math.sin(i);var o="";var g=(Math.abs(Math.abs(B-A)-360)>0.02);if(n){o="M "+v+","+e;o+=" a"+h+","+h;o+=" 0 "+p+",1 "+(w-v)+","+(f-e);if(g){o+=" L"+t+","+c}else{o+=" M"+t+","+c}o+=" a"+r+","+r;o+=" 0 "+p+",0 "+(s-t)+","+(b-c);if(g){o+=" Z"}}else{o="M "+s+","+b;o+=" a"+r+","+r;o+=" 0 "+p+",1 "+(t-s)+","+(c-b);if(g){o+=" L"+k+","+j;o+=" Z"}}return o},measureText:function(o,f,g,n,l){var e=l._getTextParts(o,f,g);var i=e.width;var b=e.height;if(false==n){b/=0.6}var c={};if(isNaN(f)){f=0}if(f==0){c={width:a.jqx._rup(i),height:a.jqx._rup(b)}}else{var k=f*Math.PI*2/360;var d=Math.abs(Math.sin(k));var j=Math.abs(Math.cos(k));var h=Math.abs(i*d+b*j);var m=Math.abs(i*j+b*d);c={width:a.jqx._rup(m),height:a.jqx._rup(h)}}if(n){c.textPartsInfo=e}return c},alignTextInRect:function(q,n,b,r,m,o,i,p,e,d){var k=e*Math.PI*2/360;var c=Math.sin(k);var j=Math.cos(k);var l=m*c;var h=m*j;if(i=="center"||i==""||i=="undefined"){q=q+b/2}else{if(i=="right"){q=q+b}}if(p=="center"||p=="middle"||p==""||p=="undefined"){n=n+r/2}else{if(p=="bottom"){n+=r-o/2}else{if(p=="top"){n+=o/2}}}d=d||"";var f="middle";if(d.indexOf("top")!=-1){f="top"}else{if(d.indexOf("bottom")!=-1){f="bottom"}}var g="center";if(d.indexOf("left")!=-1){g="left"}else{if(d.indexOf("right")!=-1){g="right"}}if(g=="center"){q-=h/2;n-=l/2}else{if(g=="right"){q-=h;n-=l}}if(f=="top"){q-=o*c;n+=o*j}else{if(f=="middle"){q-=o*c/2;n+=o*j/2}}q=a.jqx._rup(q);n=a.jqx._rup(n);return{x:q,y:n}}};a.jqx.svgRenderer=function(){};a.jqx.svgRenderer.prototype={_svgns:"http://www.w3.org/2000/svg",init:function(f){var d="<table class=tblChart cellspacing='0' cellpadding='0' border='0' align='left' valign='top'><tr><td colspan=2 class=tdTop></td></tr><tr><td class=tdLeft></td><td><div class='chartContainer' style='position:relative' onselectstart='return false;'></div></td></tr></table>";f.append(d);this.host=f;var b=f.find(".chartContainer");b[0].style.width=f.width()+"px";b[0].style.height=f.height()+"px";var h;try{var c=document.createElementNS(this._svgns,"svg");c.setAttribute("id","svgChart");c.setAttribute("version","1.1");c.setAttribute("width","100%");c.setAttribute("height","100%");c.setAttribute("overflow","hidden");b[0].appendChild(c);this.canvas=c}catch(g){return false}this._id=new Date().getTime();this.clear();this._layout();this._runLayoutFix();return true},getType:function(){return"SVG"},refresh:function(){},_runLayoutFix:function(){var b=this;this._fixLayout()},_fixLayout:function(){var f=this.canvas.getBoundingClientRect();var d=(parseFloat(f.left)==parseInt(f.left));var b=(parseFloat(f.top)==parseInt(f.top));if(a.jqx.browser.msie){var d=true,b=true;var e=this.host;var c=0,g=0;while(e&&e.position&&e[0].parentNode){var h=e.position();c+=parseFloat(h.left)-parseInt(h.left);g+=parseFloat(h.top)-parseInt(h.top);e=e.parent()}d=parseFloat(c)==parseInt(c);b=parseFloat(g)==parseInt(g)}if(!d){this.host.find(".tdLeft")[0].style.width="0.5px"}if(!b){this.host.find(".tdTop")[0].style.height="0.5px"}},_layout:function(){var b=this.host.find(".chartContainer");this._width=Math.max(a.jqx._rup(this.host.width())-1,0);this._height=Math.max(a.jqx._rup(this.host.height())-1,0);b[0].style.width=this._width;b[0].style.height=this._height;this._fixLayout()},getRect:function(){return{x:0,y:0,width:this._width,height:this._height}},getContainer:function(){var b=this.host.find(".chartContainer");return b},clear:function(){while(this.canvas.childElementCount>0){this.removeElement(this.canvas.firstElementChild)}this._defaultParent=undefined;this._defs=document.createElementNS(this._svgns,"defs");this._gradients={};this.canvas.appendChild(this._defs)},removeElement:function(d){if(undefined==d){return}this.removeHandler(d);try{while(d.firstChild){this.removeElement(d.firstChild)}if(d.parentNode){d.parentNode.removeChild(d)}else{this.canvas.removeChild(d)}}catch(c){var b=c}},_openGroups:[],beginGroup:function(){var b=this._activeParent();var c=document.createElementNS(this._svgns,"g");b.appendChild(c);this._openGroups.push(c);return c},endGroup:function(){if(this._openGroups.length==0){return}this._openGroups.pop()},_activeParent:function(){return this._openGroups.length==0?this.canvas:this._openGroups[this._openGroups.length-1]},createClipRect:function(d){var e=document.createElementNS(this._svgns,"clipPath");var b=document.createElementNS(this._svgns,"rect");this.attr(b,{x:d.x,y:d.y,width:d.width,height:d.height,fill:"none"});this._clipId=this._clipId||0;e.id="cl"+this._id+"_"+(++this._clipId).toString();e.appendChild(b);this._defs.appendChild(e);return e},getWindowHref:function(){var c=a.jqx.browser;if(c&&c.browser=="msie"&&c.version<10){return""}var b=window.location.href;if(!b){return b}b=b.replace(/([\('\)])/g,"\\$1");b=b.replace(/#.*$/,"");return b},setClip:function(d,c){var b="url("+this.getWindowHref()+"#"+c.id+")";return this.attr(d,{"clip-path":b})},_clipId:0,addHandler:function(b,d,c){if(a(b).on){a(b).on(d,c)}else{a(b).bind(d,c)}},removeHandler:function(b,d,c){if(a(b).off){a(b).off(d,c)}else{a(b).unbind(d,c)}},on:function(b,d,c){this.addHandler(b,d,c)},off:function(b,d,c){this.removeHandler(b,d,c)},shape:function(b,e){var c=document.createElementNS(this._svgns,b);if(!c){return undefined}for(var d in e){if(e[d]!==undefined&&e[d].toString()==="NaN"){c.setAttribute(d,0)}else{c.setAttribute(d,e[d])}}this._activeParent().appendChild(c);return c},_getTextParts:function(q,g,h){var f={width:0,height:0,parts:[]};if(undefined===q){return f}var m=0.6;var r=q.toString().split("<br>");var o=this._activeParent();var k=document.createElementNS(this._svgns,"text");this.attr(k,h);for(var j=0;j<r.length;j++){var c=r[j];var d=k.ownerDocument.createTextNode(c);k.appendChild(d);o.appendChild(k);var p;try{p=k.getBBox()}catch(n){}var l=a.jqx._rup(p.width);var b=a.jqx._rup(p.height*m);k.removeChild(d);f.width=Math.max(f.width,l);f.height+=b+(j>0?4:0);f.parts.push({width:l,height:b,text:c})}o.removeChild(k);return f},_measureText:function(e,d,c,b){return a.jqx.commonRenderer.measureText(e,d,c,b,this)},measureText:function(d,c,b){return this._measureText(d,c,b,false)},text:function(t,q,p,B,z,H,J,I,s,k,c){var v=this._measureText(t,H,J,true);var j=v.textPartsInfo;var f=j.parts;var A;if(!s){s="center"}if(!k){k="center"}if(f.length>1||I){A=this.beginGroup()}if(I){var g=this.createClipRect({x:a.jqx._rup(q)-1,y:a.jqx._rup(p)-1,width:a.jqx._rup(B)+2,height:a.jqx._rup(z)+2});this.setClip(A,g)}var o=this._activeParent();var L=0,l=0;var b=0.6;L=j.width;l=j.height;if(isNaN(B)||B<=0){B=L}if(isNaN(z)||z<=0){z=l}var r=B||0;var G=z||0;if(!H||H==0){p+=l;if(k=="center"||k=="middle"){p+=(G-l)/2}else{if(k=="bottom"){p+=G-l}}if(!B){B=L}if(!z){z=l}var o=this._activeParent();var n=0;for(var F=f.length-1;F>=0;F--){var u=document.createElementNS(this._svgns,"text");this.attr(u,J);this.attr(u,{cursor:"default"});var E=u.ownerDocument.createTextNode(f[F].text);u.appendChild(E);var M=q;var m=f[F].width;var e=f[F].height;if(s=="center"){M+=(r-m)/2}else{if(s=="right"){M+=(r-m)}}this.attr(u,{x:a.jqx._rup(M),y:a.jqx._rup(p+n),width:a.jqx._rup(m),height:a.jqx._rup(e)});o.appendChild(u);n-=f[F].height+4}if(A){this.endGroup();return A}return u}var C=a.jqx.commonRenderer.alignTextInRect(q,p,B,z,L,l,s,k,H,c);q=C.x;p=C.y;var D=this.shape("g",{transform:"translate("+q+","+p+")"});var d=this.shape("g",{transform:"rotate("+H+")"});D.appendChild(d);var n=0;for(var F=f.length-1;F>=0;F--){var K=document.createElementNS(this._svgns,"text");this.attr(K,J);this.attr(K,{cursor:"default"});var E=K.ownerDocument.createTextNode(f[F].text);K.appendChild(E);var M=0;var m=f[F].width;var e=f[F].height;if(s=="center"){M+=(j.width-m)/2}else{if(s=="right"){M+=(j.width-m)}}this.attr(K,{x:a.jqx._rup(M),y:a.jqx._rup(n),width:a.jqx._rup(m),height:a.jqx._rup(e)});d.appendChild(K);n-=e+4}o.appendChild(D);if(A){this.endGroup()}return D},line:function(d,f,c,e,g){var b=this.shape("line",{x1:d,y1:f,x2:c,y2:e});this.attr(b,g);return b},path:function(c,d){var b=this.shape("path");b.setAttribute("d",c);if(d){this.attr(b,d)}return b},rect:function(b,g,c,e,f){b=a.jqx._ptrnd(b);g=a.jqx._ptrnd(g);c=Math.max(1,a.jqx._rnd(c,1,false));e=Math.max(1,a.jqx._rnd(e,1,false));var d=this.shape("rect",{x:b,y:g,width:c,height:e});if(f){this.attr(d,f)}return d},circle:function(b,f,d,e){var c=this.shape("circle",{cx:b,cy:f,r:d});if(e){this.attr(c,e)}return c},pieSlicePath:function(c,h,g,e,f,d,b){return a.jqx.commonRenderer.pieSlicePath(c,h,g,e,f,d,b)},pieslice:function(j,h,g,d,f,b,i,c){var e=this.pieSlicePath(j,h,g,d,f,b,i);var k=this.shape("path");k.setAttribute("d",e);if(c){this.attr(k,c)}return k},attr:function(b,d){if(!b||!d){return}for(var c in d){if(c=="textContent"){b.textContent=d[c]}else{b.setAttribute(c,d[c])}}},removeAttr:function(b,d){if(!b||!d){return}for(var c in d){if(c=="textContent"){b.textContent=""}else{b.removeAttribute(d[c])}}},getAttr:function(c,b){return c.getAttribute(b)},_gradients:{},_toLinearGradient:function(e,h,j){var c="grd"+this._id+e.replace("#","")+(h?"v":"h");var b="url("+this.getWindowHref()+"#"+c+")";if(this._gradients[b]){return b}var d=document.createElementNS(this._svgns,"linearGradient");this.attr(d,{x1:"0%",y1:"0%",x2:h?"0%":"100%",y2:h?"100%":"0%",id:c});for(var f=0;f<j.length;f++){var g=j[f];var l=document.createElementNS(this._svgns,"stop");var k="stop-color:"+a.jqx.adjustColor(e,g[1]);this.attr(l,{offset:g[0]+"%",style:k});d.appendChild(l)}this._defs.appendChild(d);this._gradients[b]=true;return b},_toRadialGradient:function(e,j,h){var c="grd"+this._id+e.replace("#","")+"r"+(h!=undefined?h.key:"");var b="url("+this.getWindowHref()+"#"+c+")";if(this._gradients[b]){return b}var d=document.createElementNS(this._svgns,"radialGradient");if(h==undefined){this.attr(d,{cx:"50%",cy:"50%",r:"100%",fx:"50%",fy:"50%",id:c})}else{this.attr(d,{cx:h.x,cy:h.y,r:h.outerRadius,id:c,gradientUnits:"userSpaceOnUse"})}for(var f=0;f<j.length;f++){var g=j[f];var l=document.createElementNS(this._svgns,"stop");var k="stop-color:"+a.jqx.adjustColor(e,g[1]);this.attr(l,{offset:g[0]+"%",style:k});d.appendChild(l)}this._defs.appendChild(d);this._gradients[b]=true;return b}};a.jqx.vmlRenderer=function(){};a.jqx.vmlRenderer.prototype={init:function(g){var f="<div class='chartContainer' style=\"position:relative;overflow:hidden;\"><div>";g.append(f);this.host=g;var b=g.find(".chartContainer");b[0].style.width=g.width()+"px";b[0].style.height=g.height()+"px";var d=true;try{for(var c=0;c<document.namespaces.length;c++){if(document.namespaces[c].name=="v"&&document.namespaces[c].urn=="urn:schemas-microsoft-com:vml"){d=false;break}}}catch(h){return false}if(a.jqx.browser.msie&&parseInt(a.jqx.browser.version)<9&&(document.childNodes&&document.childNodes.length>0&&document.childNodes[0].data&&document.childNodes[0].data.indexOf("DOCTYPE")!=-1)){if(d){document.namespaces.add("v","urn:schemas-microsoft-com:vml")}this._ie8mode=true}else{if(d){document.namespaces.add("v","urn:schemas-microsoft-com:vml");document.createStyleSheet().cssText="v\\:* { behavior: url(#default#VML); display: inline-block; }"}}this.canvas=b[0];this._width=Math.max(a.jqx._rup(b.width()),0);this._height=Math.max(a.jqx._rup(b.height()),0);b[0].style.width=this._width+2;b[0].style.height=this._height+2;this._id=new Date().getTime();this.clear();return true},getType:function(){return"VML"},refresh:function(){},getRect:function(){return{x:0,y:0,width:this._width,height:this._height}},getContainer:function(){var b=this.host.find(".chartContainer");return b},clear:function(){while(this.canvas.childElementCount>0){this.removeHandler(this.canvas.firstElementChild);this.canvas.removeChild(this.canvas.firstElementChild)}this._gradients={};this._defaultParent=undefined},removeElement:function(b){if(b!=null){this.removeHandler(b);b.parentNode.removeChild(b)}},_openGroups:[],beginGroup:function(){var b=this._activeParent();var c=document.createElement("v:group");c.style.position="absolute";c.coordorigin="0,0";c.coordsize=this._width+","+this._height;c.style.left=0;c.style.top=0;c.style.width=this._width;c.style.height=this._height;b.appendChild(c);this._openGroups.push(c);return c},endGroup:function(){if(this._openGroups.length==0){return}this._openGroups.pop()},_activeParent:function(){return this._openGroups.length==0?this.canvas:this._openGroups[this._openGroups.length-1]},createClipRect:function(b){var c=document.createElement("div");c.style.height=(b.height+1)+"px";c.style.width=(b.width+1)+"px";c.style.position="absolute";c.style.left=b.x+"px";c.style.top=b.y+"px";c.style.overflow="hidden";this._clipId=this._clipId||0;c.id="cl"+this._id+"_"+(++this._clipId).toString();this._activeParent().appendChild(c);return c},setClip:function(c,b){},_clipId:0,addHandler:function(b,d,c){if(a(b).on){a(b).on(d,c)}else{a(b).bind(d,c)}},removeHandler:function(b,d,c){if(a(b).off){a(b).off(d,c)}else{a(b).unbind(d,c)}},on:function(b,d,c){this.addHandler(b,d,c)},off:function(b,d,c){this.removeHandler(b,d,c)},_getTextParts:function(o,f,g){var e={width:0,height:0,parts:[]};var m=0.6;var p=o.toString().split("<br>");var n=this._activeParent();var j=document.createElement("v:textbox");this.attr(j,g);n.appendChild(j);for(var h=0;h<p.length;h++){var c=p[h];var d=document.createElement("span");d.appendChild(document.createTextNode(c));j.appendChild(d);if(g&&g["class"]){d.className=g["class"]}var l=a(j);var k=a.jqx._rup(l.width());var b=a.jqx._rup(l.height()*m);if(b==0&&a.jqx.browser.msie&&parseInt(a.jqx.browser.version)<9){var q=l.css("font-size");if(q){b=parseInt(q);if(isNaN(b)){b=0}}}j.removeChild(d);e.width=Math.max(e.width,k);e.height+=b+(h>0?2:0);e.parts.push({width:k,height:b,text:c})}n.removeChild(j);return e},_measureText:function(e,d,c,b){if(Math.abs(d)>45){d=90}else{d=0}return a.jqx.commonRenderer.measureText(e,d,c,b,this)},measureText:function(d,c,b){return this._measureText(d,c,b,false)},text:function(r,n,m,A,t,G,I,H,q,g){var B;if(I&&I.stroke){B=I.stroke}if(B==undefined){B="black"}var s=this._measureText(r,G,I,true);var e=s.textPartsInfo;var b=e.parts;var J=s.width;var j=s.height;if(isNaN(A)||A==0){A=J}if(isNaN(t)||t==0){t=j}var v;if(!q){q="center"}if(!g){g="center"}if(b.length>0||H){v=this.beginGroup()}if(H){var c=this.createClipRect({x:a.jqx._rup(n),y:a.jqx._rup(m),width:a.jqx._rup(A),height:a.jqx._rup(t)});this.setClip(v,c)}var l=this._activeParent();var p=A||0;var F=t||0;if(Math.abs(G)>45){G=90}else{G=0}var u=0,E=0;if(q=="center"){u+=(p-J)/2}else{if(q=="right"){u+=(p-J)}}if(g=="center"){E=(F-j)/2}else{if(g=="bottom"){E=F-j}}if(G==0){m+=j+E;n+=u}else{n+=J+u;m+=E}var k=0,K=0;var d;for(var D=b.length-1;D>=0;D--){var z=b[D];var o=(J-z.width)/2;if(G==0&&q=="left"){o=0}else{if(G==0&&q=="right"){o=J-z.width}else{if(G==90){o=(j-z.width)/2}}}var f=k-z.height;E=G==90?o:f;u=G==90?f:o;d=document.createElement("v:textbox");d.style.position="absolute";d.style.left=a.jqx._rup(n+u);d.style.top=a.jqx._rup(m+E);d.style.width=a.jqx._rup(z.width);d.style.height=a.jqx._rup(z.height);if(G==90){d.style.filter="progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";d.style.height=a.jqx._rup(z.height)+5}var C=document.createElement("span");C.appendChild(document.createTextNode(z.text));if(I&&I["class"]){C.className=I["class"]}d.appendChild(C);l.appendChild(d);k-=z.height+(D>0?2:0)}if(v){this.endGroup();return l}return d},shape:function(b,e){var c=document.createElement(this._createElementMarkup(b));if(!c){return undefined}for(var d in e){c.setAttribute(d,e[d])}this._activeParent().appendChild(c);return c},line:function(e,g,d,f,h){var b="M "+e+","+g+" L "+d+","+f+" X E";var c=this.path(b);this.attr(c,h);return c},_createElementMarkup:function(b){var c="<v:"+b+' style=""></v:'+b+">";if(this._ie8mode){c=c.replace('style=""','style="behavior: url(#default#VML);"')}return c},path:function(c,d){var b=document.createElement(this._createElementMarkup("shape"));b.style.position="absolute";b.coordsize=this._width+" "+this._height;b.coordorigin="0 0";b.style.width=parseInt(this._width);b.style.height=parseInt(this._height);b.style.left=0+"px";b.style.top=0+"px";b.setAttribute("path",c);this._activeParent().appendChild(b);if(d){this.attr(b,d)}return b},rect:function(b,g,c,d,f){b=a.jqx._ptrnd(b);g=a.jqx._ptrnd(g);c=a.jqx._rup(c);d=a.jqx._rup(d);var e=this.shape("rect",f);e.style.position="absolute";e.style.left=b;e.style.top=g;e.style.width=c;e.style.height=d;e.strokeweight=0;if(f){this.attr(e,f)}return e},circle:function(b,f,d,e){var c=this.shape("oval");b=a.jqx._ptrnd(b-d);f=a.jqx._ptrnd(f-d);d=a.jqx._rup(d);c.style.position="absolute";c.style.left=b;c.style.top=f;c.style.width=d*2;c.style.height=d*2;if(e){this.attr(c,e)}return c},updateCircle:function(d,b,e,c){if(b==undefined){b=parseFloat(d.style.left)+parseFloat(d.style.width)/2}if(e==undefined){e=parseFloat(d.style.top)+parseFloat(d.style.height)/2}if(c==undefined){c=parseFloat(d.width)/2}b=a.jqx._ptrnd(b-c);e=a.jqx._ptrnd(e-c);c=a.jqx._rup(c);d.style.left=b;d.style.top=e;d.style.width=c*2;d.style.height=c*2},pieSlicePath:function(k,j,h,r,B,C,d){if(!r){r=1}var m=Math.abs(B-C);var p=m>180?1:0;if(m>360){B=0;C=360}var q=B*Math.PI*2/360;var i=C*Math.PI*2/360;var w=k,v=k,f=j,e=j;var n=!isNaN(h)&&h>0;if(n){d=0}if(d>0){var l=m/2+B;var A=l*Math.PI*2/360;k+=d*Math.cos(A);j-=d*Math.sin(A)}if(n){var u=h;w=a.jqx._ptrnd(k+u*Math.cos(q));f=a.jqx._ptrnd(j-u*Math.sin(q));v=a.jqx._ptrnd(k+u*Math.cos(i));e=a.jqx._ptrnd(j-u*Math.sin(i))}var t=a.jqx._ptrnd(k+r*Math.cos(q));var s=a.jqx._ptrnd(k+r*Math.cos(i));var c=a.jqx._ptrnd(j-r*Math.sin(q));var b=a.jqx._ptrnd(j-r*Math.sin(i));r=a.jqx._ptrnd(r);h=a.jqx._ptrnd(h);k=a.jqx._ptrnd(k);j=a.jqx._ptrnd(j);var g=Math.round(B*65535);var z=Math.round((C-B)*65536);if(h<0){h=1}var o="";if(n){o="M"+w+" "+f;o+=" AE "+k+" "+j+" "+h+" "+h+" "+g+" "+z;o+=" L "+s+" "+b;g=Math.round((B-C)*65535);z=Math.round(C*65536);o+=" AE "+k+" "+j+" "+r+" "+r+" "+z+" "+g;o+=" L "+w+" "+f}else{o="M"+k+" "+j;o+=" AE "+k+" "+j+" "+r+" "+r+" "+g+" "+z}o+=" X E";return o},pieslice:function(k,i,h,e,g,b,j,d){var f=this.pieSlicePath(k,i,h,e,g,b,j);var c=this.path(f,d);if(d){this.attr(c,d)}return c},_keymap:[{svg:"fill",vml:"fillcolor"},{svg:"stroke",vml:"strokecolor"},{svg:"stroke-width",vml:"strokeweight"},{svg:"stroke-dasharray",vml:"dashstyle"},{svg:"fill-opacity",vml:"fillopacity"},{svg:"stroke-opacity",vml:"strokeopacity"},{svg:"opacity",vml:"opacity"},{svg:"cx",vml:"style.left"},{svg:"cy",vml:"style.top"},{svg:"height",vml:"style.height"},{svg:"width",vml:"style.width"},{svg:"x",vml:"style.left"},{svg:"y",vml:"style.top"},{svg:"d",vml:"v"},{svg:"display",vml:"style.display"}],_translateParam:function(b){for(var c in this._keymap){if(this._keymap[c].svg==b){return this._keymap[c].vml}}return b},attr:function(c,e){if(!c||!e){return}for(var d in e){var b=this._translateParam(d);if(undefined==e[d]){continue}if(b=="fillcolor"&&e[d].indexOf("grd")!=-1){c.type=e[d]}else{if(b=="fillcolor"&&e[d]=="transparent"){c.style.filter="alpha(opacity=0)";c["-ms-filter"]="progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"}else{if(b=="opacity"||b=="fillopacity"){if(c.fill){c.fill.opacity=e[d]}}else{if(b=="textContent"){c.children[0].innerText=e[d]}else{if(b=="dashstyle"){c.dashstyle=e[d].replace(","," ")}else{if(b.indexOf("style.")==-1){c[b]=e[d]}else{c.style[b.replace("style.","")]=e[d]}}}}}}}},removeAttr:function(b,d){if(!b||!d){return}for(var c in d){b.removeAttribute(d[c])}},getAttr:function(d,c){var b=this._translateParam(c);if(b=="opacity"||b=="fillopacity"){if(d.fill){return d.fill.opacity}else{return 1}}if(b.indexOf("style.")==-1){return d[b]}return d.style[b.replace("style.","")]},_gradients:{},_toRadialGradient:function(b,d,c){return b},_toLinearGradient:function(g,k,l){if(this._ie8mode){return g}var d="grd"+g.replace("#","")+(k?"v":"h");var e="#"+d+"";if(this._gradients[e]){return e}var f=document.createElement(this._createElementMarkup("fill"));f.type="gradient";f.method="linear";f.angle=k?0:90;var c="";for(var h=0;h<l.length;h++){var j=l[h];if(j>0){c+=", "}c+=j[0]+"% "+a.jqx.adjustColor(g,j[1])}f.colors=c;var b=document.createElement(this._createElementMarkup("shapetype"));b.appendChild(f);b.id=d;this.canvas.appendChild(b);return e}};a.jqx.HTML5Renderer=function(){};a.jqx.ptrnd=function(c){if(Math.abs(Math.round(c)-c)==0.5){return c}var b=Math.round(c);if(b<c){b=b-1}return b+0.5};a.jqx.HTML5Renderer.prototype={init:function(b){try{this.host=b;this.host.append("<div class='chartContainer' style='position:relative' onselectstart='return false;'><canvas id='__jqxCanvasWrap' style='width:100%; height: 100%;'/></div>");this.canvas=b.find("#__jqxCanvasWrap");this.canvas[0].width=b.width();this.canvas[0].height=b.height();this.ctx=this.canvas[0].getContext("2d");this._elements={};this._maxId=0;this._gradientId=0;this._gradients={};this._currentPoint={x:0,y:0};this._lastCmd="";this._pos=0}catch(c){return false}return true},getType:function(){return"HTML5"},getContainer:function(){var b=this.host.find(".chartContainer");return b},getRect:function(){return{x:0,y:0,width:this.canvas[0].width-1,height:this.canvas[0].height-1}},beginGroup:function(){},endGroup:function(){},setClip:function(){},createClipRect:function(b){},addHandler:function(b,d,c){},removeHandler:function(b,d,c){},on:function(b,d,c){this.addHandler(b,d,c)},off:function(b,d,c){this.removeHandler(b,d,c)},clear:function(){this._elements={};this._maxId=0;this._renderers._gradients={};this._gradientId=0},removeElement:function(b){if(undefined==b){return}if(this._elements[b.id]){delete this._elements[b.id]}},shape:function(b,e){var c={type:b,id:this._maxId++};for(var d in e){c[d]=e[d]}this._elements[c.id]=c;return c},attr:function(b,d){for(var c in d){b[c]=d[c]}},removeAttr:function(b,d){for(var c in d){delete b[d[c]]}},rect:function(b,g,c,e,f){if(isNaN(b)){throw'Invalid value for "x"'}if(isNaN(g)){throw'Invalid value for "y"'}if(isNaN(c)){throw'Invalid value for "width"'}if(isNaN(e)){throw'Invalid value for "height"'}var d=this.shape("rect",{x:b,y:g,width:c,height:e});if(f){this.attr(d,f)}return d},path:function(b,d){var c=this.shape("path",d);this.attr(c,{d:b});return c},line:function(c,e,b,d,f){return this.path("M "+c+","+e+" L "+b+","+d,f)},circle:function(b,f,d,e){var c=this.shape("circle",{x:b,y:f,r:d});if(e){this.attr(c,e)}return c},pieSlicePath:function(c,h,g,e,f,d,b){return a.jqx.commonRenderer.pieSlicePath(c,h,g,e,f,d,b)},pieslice:function(j,h,g,e,f,b,i,c){var d=this.path(this.pieSlicePath(j,h,g,e,f,b,i),c);this.attr(d,{x:j,y:h,innerRadius:g,outerRadius:e,angleFrom:f,angleTo:b});return d},_getCSSStyle:function(c){var g=document.styleSheets;try{for(var d=0;d<g.length;d++){for(var b=0;g[d].cssRules&&b<g[d].cssRules.length;b++){if(g[d].cssRules[b].selectorText.indexOf(c)!=-1){return g[d].cssRules[b].style}}}}catch(f){}return{}},_getTextParts:function(p,f,g){var l="Arial";var q="10pt";var m="";if(g&&g["class"]){var b=this._getCSSStyle(g["class"]);if(b.fontSize){q=b.fontSize}if(b.fontFamily){l=b.fontFamily}if(b.fontWeight){m=b.fontWeight}}this.ctx.font=m+" "+q+" "+l;var e={width:0,height:0,parts:[]};var k=0.6;var o=p.toString().split("<br>");for(var h=0;h<o.length;h++){var d=o[h];var j=this.ctx.measureText(d).width;var n=document.createElement("span.jqxchart");n.font=this.ctx.font;n.textContent=d;document.body.appendChild(n);var c=n.offsetHeight*k;document.body.removeChild(n);e.width=Math.max(e.width,a.jqx._rup(j));e.height+=c+(h>0?4:0);e.parts.push({width:j,height:c,text:d})}return e},_measureText:function(e,d,c,b){return a.jqx.commonRenderer.measureText(e,d,c,b,this)},measureText:function(d,c,b){return this._measureText(d,c,b,false)},text:function(m,l,j,c,n,f,g,d,h,k,e){var o=this.shape("text",{text:m,x:l,y:j,width:c,height:n,angle:f,clip:d,halign:h,valign:k,rotateAround:e});if(g){this.attr(o,g)}o.fontFamily="Arial";o.fontSize="10pt";o.fontWeight="";o.color="#000000";if(g&&g["class"]){var b=this._getCSSStyle(g["class"]);o.fontFamily=b.fontFamily||o.fontFamily;o.fontSize=b.fontSize||o.fontSize;o.fontWeight=b.fontWeight||o.fontWeight;o.color=b.color||o.color}var i=this._measureText(m,0,g,true);this.attr(o,{textPartsInfo:i.textPartsInfo,textWidth:i.width,textHeight:i.height});if(c<=0||isNaN(c)){this.attr(o,{width:i.width})}if(n<=0||isNaN(n)){this.attr(o,{height:i.height})}return o},_toLinearGradient:function(c,g,f){if(this._renderers._gradients[c]){return c}var b=[];for(var e=0;e<f.length;e++){b.push({percent:f[e][0]/100,color:a.jqx.adjustColor(c,f[e][1])})}var d="gr"+this._gradientId++;this.createGradient(d,g?"vertical":"horizontal",b);return d},_toRadialGradient:function(c,f){if(this._renderers._gradients[c]){return c}var b=[];for(var e=0;e<f.length;e++){b.push({percent:f[e][0]/100,color:a.jqx.adjustColor(c,f[e][1])})}var d="gr"+this._gradientId++;this.createGradient(d,"radial",b);return d},createGradient:function(d,c,b){this._renderers.createGradient(this,d,c,b)},_renderers:{createGradient:function(e,d,c,b){e._gradients[d]={orientation:c,colorStops:b}},setStroke:function(c,d){var b=c.ctx;b.strokeStyle=d.stroke||"transparent";b.lineWidth=d["stroke-width"]||1;if(d["fill-opacity"]!=undefined){b.globalAlpha=d["fill-opacity"]}else{if(d.opacity!=undefined){b.globalAlpha=d.opacity}else{b.globalAlpha=1}}if(b.setLineDash){if(d["stroke-dasharray"]){b.setLineDash(d["stroke-dasharray"].split(","))}else{b.setLineDash([])}}},setFillStyle:function(c,h){var r=c.ctx;r.fillStyle="transparent";if(h["fill-opacity"]!=undefined){r.globalAlpha=h["fill-opacity"]}else{if(h.opacity!=undefined){r.globalAlpha=h.opacity}else{r.globalAlpha=1}}if(h.fill&&h.fill.indexOf("#")==-1&&c._gradients[h.fill]){var p=c._gradients[h.fill].orientation!="horizontal";var k=c._gradients[h.fill].orientation=="radial";var d=a.jqx.ptrnd(h.x);var q=a.jqx.ptrnd(h.y);var b=a.jqx.ptrnd(h.x+(p?0:h.width));var m=a.jqx.ptrnd(h.y+(p?h.height:0));var o;if((h.type=="circle"||h.type=="path"||h.type=="rect")&&k){var n=a.jqx.ptrnd(h.x);var l=a.jqx.ptrnd(h.y);var g=h.innerRadius||0;var f=h.outerRadius||h.r||0;if(h.type=="rect"){n+=h.width/2;l+=h.height/2}o=r.createRadialGradient(n,l,g,n,l,f)}if(!k){if(isNaN(d)||isNaN(b)||isNaN(q)||isNaN(m)){d=0;q=0;b=p?0:r.canvas.width;m=p?r.canvas.height:0}o=r.createLinearGradient(d,q,b,m)}var e=c._gradients[h.fill].colorStops;for(var j=0;j<e.length;j++){o.addColorStop(e[j].percent,e[j].color)}r.fillStyle=o}else{if(h.fill){r.fillStyle=h.fill}}},rect:function(b,c){if(c.width==0||c.height==0){return}b.fillRect(a.jqx.ptrnd(c.x),a.jqx.ptrnd(c.y),c.width,c.height);b.strokeRect(a.jqx.ptrnd(c.x),a.jqx.ptrnd(c.y),c.width,c.height)},circle:function(b,c){if(c.r==0){return}b.beginPath();b.arc(a.jqx.ptrnd(c.x),a.jqx.ptrnd(c.y),c.r,0,Math.PI*2,false);b.closePath();b.fill();b.stroke()},_parsePoint:function(c){var b=this._parseNumber(c);var d=this._parseNumber(c);return({x:b,y:d})},_parseNumber:function(d){var e=false;for(var b=this._pos;b<d.length;b++){if((d[b]>="0"&&d[b]<="9")||d[b]=="."||d[b]=="e"||(d[b]=="-"&&!e)||(d[b]=="-"&&b>=1&&d[b-1]=="e")){e=true;continue}if(!e&&(d[b]==" "||d[b]==",")){this._pos++;continue}break}var c=parseFloat(d.substring(this._pos,b));if(isNaN(c)){return undefined}this._pos=b;return c},_cmds:"mlcazq",_isRelativeCmd:function(b){return a.jqx.string.contains(this._cmds,b)},_parseCmd:function(b){for(var c=this._pos;c<b.length;c++){if(a.jqx.string.containsIgnoreCase(this._cmds,b[c])){this._pos=c+1;this._lastCmd=b[c];return this._lastCmd}if(b[c]==" "){this._pos++;continue}if(b[c]>="0"&&b[c]<="9"){this._pos=c;if(this._lastCmd==""){break}else{return this._lastCmd}}}return undefined},_toAbsolutePoint:function(b){return{x:this._currentPoint.x+b.x,y:this._currentPoint.y+b.y}},path:function(A,J){var x=J.d;this._pos=0;this._lastCmd="";var k=undefined;this._currentPoint={x:0,y:0};A.beginPath();var E=0;while(this._pos<x.length){var D=this._parseCmd(x);if(D==undefined){break}if(D=="M"||D=="m"){var B=this._parsePoint(x);if(B==undefined){break}A.moveTo(B.x,B.y);this._currentPoint=B;if(k==undefined){k=B}continue}if(D=="L"||D=="l"){var B=this._parsePoint(x);if(B==undefined){break}A.lineTo(B.x,B.y);this._currentPoint=B;continue}if(D=="A"||D=="a"){var g=this._parseNumber(x);var f=this._parseNumber(x);var H=this._parseNumber(x)*(Math.PI/180);var L=this._parseNumber(x);var e=this._parseNumber(x);var o=this._parsePoint(x);if(this._isRelativeCmd(D)){o=this._toAbsolutePoint(o)}if(g==0||f==0){continue}var h=this._currentPoint;var G={x:Math.cos(H)*(h.x-o.x)/2+Math.sin(H)*(h.y-o.y)/2,y:-Math.sin(H)*(h.x-o.x)/2+Math.cos(H)*(h.y-o.y)/2};var j=Math.pow(G.x,2)/Math.pow(g,2)+Math.pow(G.y,2)/Math.pow(f,2);if(j>1){g*=Math.sqrt(j);f*=Math.sqrt(j)}var p=(L==e?-1:1)*Math.sqrt(((Math.pow(g,2)*Math.pow(f,2))-(Math.pow(g,2)*Math.pow(G.y,2))-(Math.pow(f,2)*Math.pow(G.x,2)))/(Math.pow(g,2)*Math.pow(G.y,2)+Math.pow(f,2)*Math.pow(G.x,2)));if(isNaN(p)){p=0}var F={x:p*g*G.y/f,y:p*-f*G.x/g};var z={x:(h.x+o.x)/2+Math.cos(H)*F.x-Math.sin(H)*F.y,y:(h.y+o.y)/2+Math.sin(H)*F.x+Math.cos(H)*F.y};var y=function(i){return Math.sqrt(Math.pow(i[0],2)+Math.pow(i[1],2))};var t=function(m,i){return(m[0]*i[0]+m[1]*i[1])/(y(m)*y(i))};var K=function(m,i){return(m[0]*i[1]<m[1]*i[0]?-1:1)*Math.acos(t(m,i))};var C=K([1,0],[(G.x-F.x)/g,(G.y-F.y)/f]);var n=[(G.x-F.x)/g,(G.y-F.y)/f];var l=[(-G.x-F.x)/g,(-G.y-F.y)/f];var I=K(n,l);if(t(n,l)<=-1){I=Math.PI}if(t(n,l)>=1){I=0}if(e==0&&I>0){I=I-2*Math.PI}if(e==1&&I<0){I=I+2*Math.PI}var t=(g>f)?g:f;var w=(g>f)?1:g/f;var q=(g>f)?f/g:1;A.translate(z.x,z.y);A.rotate(H);A.scale(w,q);A.arc(0,0,t,C,C+I,1-e);A.scale(1/w,1/q);A.rotate(-H);A.translate(-z.x,-z.y);continue}if((D=="Z"||D=="z")&&k!=undefined){A.lineTo(k.x,k.y);this._currentPoint=k;continue}if(D=="C"||D=="c"){var d=this._parsePoint(x);var c=this._parsePoint(x);var b=this._parsePoint(x);A.bezierCurveTo(d.x,d.y,c.x,c.y,b.x,b.y);this._currentPoint=b;continue}if(D=="Q"||D=="q"){var d=this._parsePoint(x);var c=this._parsePoint(x);A.quadraticCurveTo(d.x,d.y,c.x,c.y);this._currentPoint=c;continue}}A.fill();A.stroke();A.closePath()},text:function(u,D){var n=a.jqx.ptrnd(D.x);var m=a.jqx.ptrnd(D.y);var s=a.jqx.ptrnd(D.width);var q=a.jqx.ptrnd(D.height);var p=D.halign;var g=D.valign;var A=D.angle;var b=D.rotateAround;var e=D.textPartsInfo;var d=e.parts;var B=D.clip;if(B==undefined){B=true}u.save();if(!p){p="center"}if(!g){g="center"}if(B){u.rect(n,m,s,q);u.clip()}var E=D.textWidth;var j=D.textHeight;var o=s||0;var z=q||0;u.fillStyle=D.color;u.font=D.fontWeight+" "+D.fontSize+" "+D.fontFamily;if(!A||A==0){m+=j;if(g=="center"||g=="middle"){m+=(z-j)/2}else{if(g=="bottom"){m+=z-j}}if(!s){s=E}if(!q){q=j}var l=0;for(var v=d.length-1;v>=0;v--){var r=d[v];var F=n;var k=d[v].width;var c=d[v].height;if(p=="center"){F+=(o-k)/2}else{if(p=="right"){F+=(o-k)}}u.fillText(r.text,F,m+l);l-=r.height+(v>0?4:0)}u.restore();return}var t=a.jqx.commonRenderer.alignTextInRect(n,m,s,q,E,j,p,g,A,b);n=t.x;m=t.y;var f=A*Math.PI*2/360;u.translate(n,m);u.rotate(f);var l=0;var C=e.width;for(var v=d.length-1;v>=0;v--){var F=0;if(p=="center"){F+=(C-d[v].width)/2}else{if(p=="right"){F+=(C-d[v].width)}}u.fillText(d[v].text,F,l);l-=d[v].height+4}u.restore()}},refresh:function(){this.ctx.clearRect(0,0,this.canvas[0].width,this.canvas[0].height);for(var b in this._elements){var c=this._elements[b];this._renderers.setFillStyle(this,c);this._renderers.setStroke(this,c);this._renderers[this._elements[b].type](this.ctx,c)}}};a.jqx.createRenderer=function(b,d){var c=b;var e=c.renderer=null;if(document.createElementNS&&(c.renderEngine!="HTML5"&&c.renderEngine!="VML")){e=new a.jqx.svgRenderer();if(!e.init(d)){if(c.renderEngine=="SVG"){throw"Your browser does not support SVG"}return null}}if(e==null&&c.renderEngine!="HTML5"){e=new a.jqx.vmlRenderer();if(!e.init(d)){if(c.renderEngine=="VML"){throw"Your browser does not support VML"}return null}c._isVML=true}if(e==null&&(c.renderEngine=="HTML5"||c.renderEngine==undefined)){e=new a.jqx.HTML5Renderer();if(!e.init(d)){throw"Your browser does not support HTML5 Canvas"}}c.renderer=e;return e},a.jqx._widgetToImage=function(o,i,d,m,g,f){var k=o;if(!k){return false}if(d==undefined||d==""){d="image."+i}var l=k.renderEngine;var c=k.enableAnimations;k.enableAnimations=false;k.renderEngine="HTML5";if(k.renderEngine!=l){try{k.refresh()}catch(h){k.renderEngine=l;k.refresh();k.enableAnimations=c;return false}}var b=k.renderer.getContainer().find("canvas")[0];var j=true;if(a.isFunction(f)){j=f(o,b)}var n=true;if(j){n=a.jqx.exportImage(b,i,d,m,g)}if(k.renderEngine!=l){k.renderEngine=l;k.refresh();k.enableAnimations=c}return n};a.jqx.getByPriority=function(b){var d=undefined;for(var c=0;c<b.length&&d==undefined;c++){if(d==undefined&&b[c]!=undefined){d=b[c]}}return d};a.jqx.exportImage=function(d,n,f,q,i){if(!d){return false}var k=n.toLowerCase()==="pdf";if(k){n="jpeg"}if(f==undefined||f==""){f="image."+n}if(q==undefined||q==""){throw"Please specifiy export server"}var s=true;try{if(d){var g=d.toDataURL("image/"+n);if(k){if(!a.jqx.pdfExport){a.jqx.pdfExport={orientation:"portrait",paperSize:"a4"}}var h=595;switch(a.jqx.pdfExport.paperSize){case"legal":var h=612;if(a.jqx.pdfExport.orientation!=="portrait"){h=1008}break;case"letter":var h=612;if(a.jqx.pdfExport.orientation!=="portrait"){h=792}break;case"a3":var h=841;if(a.jqx.pdfExport.orientation!=="portrait"){h=1190}break;case"a4":var h=595;if(a.jqx.pdfExport.orientation!=="portrait"){h=842}break;case"a5":var h=420;if(a.jqx.pdfExport.orientation!=="portrait"){h=595}break}var j=a(d).width();var o=j*72/96;if(o>=h-20){o=h-20}var p;try{var p=new window.pdfDataExport(a.jqx.pdfExport.orientation,"pt",a.jqx.pdfExport.paperSize)}catch(m){var p=new window.jqxPdfDataExport(a.jqx.pdfExport.orientation,"pt",a.jqx.pdfExport.paperSize)}p.addImage(g,"JPEG",10,10,o,0);p.save(f);return}g=g.replace("data:image/"+n+";base64,","");if(i){a.ajax({dataType:"string",url:q,type:"POST",data:{content:g,fname:f},async:false,success:function(t,e,u){s=true},error:function(t,e,u){s=false}})}else{var c=document.createElement("form");c.method="POST";c.action=q;c.style.display="none";document.body.appendChild(c);var r=document.createElement("input");r.name="fname";r.value=f;r.style.display="none";var b=document.createElement("input");b.name="content";b.value=g;b.style.display="none";c.appendChild(r);c.appendChild(b);c.submit();document.body.removeChild(c);s=true}}}catch(l){s=false}return s}})(jqxBaseFramework);(function(a){window.jqxPlot=function(){};window.jqxPlot.prototype={get:function(d,b,c){return c!==undefined?d[b][c]:d[b]},min:function(f,d){var c=NaN;for(var b=0;b<f.length;b++){var e=this.get(f,b,d);if(isNaN(c)||e<c){c=e}}return c},max:function(f,d){var b=NaN;for(var c=0;c<f.length;c++){var e=this.get(f,c,d);if(isNaN(b)||e>b){b=e}}return b},sum:function(f,c){var d=0;for(var b=0;b<f.length;b++){var e=this.get(f,b,c);if(!isNaN(e)){d+=e}}return d},count:function(f,c){var d=0;for(var b=0;b<f.length;b++){var e=this.get(f,b,c);if(!isNaN(e)){d++}}return d},avg:function(c,b){return this.sum(c,b)/Math.max(1,this.count(c,b))},filter:function(e,d){if(!d){return e}var b=[];for(var c=0;c<e.length;c++){if(d(e[c])){b.push(e[c])}}return b},scale:function(d,i,j,g){if(isNaN(d)){return NaN}if(d<Math.min(i.min,i.max)||d>Math.max(i.min,i.max)){if(!g||g.ignore_range!==true){return NaN}}var n=NaN;var l=1;if(i.type===undefined||i.type!="logarithmic"){var k=Math.abs(i.max-i.min);if(!k){k=1}l=Math.abs(d-Math.min(i.min,i.max))/k}else{if(i.type==="logarithmic"){var e=i.base;if(isNaN(e)){e=10}var h=Math.min(i.min,i.max);if(h<=0){h=1}var m=Math.max(i.min,i.max);if(m<=0){m=1}var f=a.jqx.log(m,e);m=Math.pow(e,f);var c=a.jqx.log(h,e);h=Math.pow(e,c);var b=a.jqx.log(d,e);l=Math.abs(b-c)/(f-c)}}if(j.type==="logarithmic"){var e=j.base;if(isNaN(e)){e=10}var f=a.jqx.log(j.max,e);var c=a.jqx.log(j.min,e);if(j.flip){l=1-l}var b=Math.min(c,f)+l*Math.abs(f-c);n=Math.pow(e,b)}else{n=Math.min(j.min,j.max)+l*Math.abs(j.max-j.min);if(j.flip){n=Math.max(j.min,j.max)-n+j.min}}return n},axis:function(o,p,k){if(k<=1){return[p,o]}var f=o;var h=p;if(isNaN(k)||k<2){k=2}var b=0;while(Math.round(o)!=o&&Math.round(p)!=p&&b<10){o*=10;p*=10;b++}var l=(p-o)/k;while(b<10&&Math.round(l)!=l){o*=10;p*=10;l*=10;b++}var t=[1,2,5];var g=0;var q=0;while(true){var m=q%t.length;var e=Math.floor(q/t.length);var n=Math.pow(10,e)*t[m];m=(q+1)%t.length;e=Math.floor((q+1)/t.length);var j=Math.pow(10,e)*t[m];if(l>=n&&l<j){break}q++}var d=j;var r=[];var s=a.jqx._rnd(o,d,false);var c=b<=0?1:Math.pow(10,b);while(s<p+d){r.push(s/c);s+=d}return r}}})(jqxBaseFramework)})();

