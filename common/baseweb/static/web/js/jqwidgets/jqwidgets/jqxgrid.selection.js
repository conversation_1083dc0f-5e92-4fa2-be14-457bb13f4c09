/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(a){a.extend(a.jqx._jqxGrid.prototype,{selectallrows:function(){this._trigger=false;var e=this.virtualmode?this.dataview.totalrecords:this.dataview.loadedrecords.length;this.selectedrowindexes=new Array();this.selectedcells=new Array();var f=this.dataview.loadedrecords;for(var d=0;d<e;d++){var g=f[d];if(!g){this.selectedrowindexes[d]=d;continue}var b=this.getboundindex(g);if(b!=undefined){this.selectedrowindexes[d]=b}for(var c=0;c<this.columns.records.length;c++){this.selectedcells[b+"_"+this.columns.records[c].datafield]=true}}if(this.selectionmode=="checkbox"&&!this._checkboxcolumnupdating){if(this._checkboxcolumn){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:true})}}this._renderrows(this.virtualsizeinfo);this._trigger=true;if(this.selectionmode=="checkbox"){this._raiseEvent(2,{rowindex:this.selectedrowindexes})}},unselectallrows:function(){this._trigger=false;var b=this.virtualmode?this.dataview.totalrecords:this.dataview.loadedrecords.length;this.selectedrowindexes=new Array();this.selectedcells=new Array();if(this.selectionmode=="checkbox"&&!this._checkboxcolumnupdating){if(this._checkboxcolumn){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:false})}}this._renderrows(this.virtualsizeinfo);this._trigger=true;if(this.selectionmode=="checkbox"){this._raiseEvent(2,{rowindex:this.selectedrowindexes})}},selectrow:function(b,c){if(this.selectionmode!=="none"){this._applyrowselection(b,true,c);if(c!==false){this._updatecheckboxselection()}}},_updatecheckboxselection:function(){if(this.selectionmode=="checkbox"){var d=this.getrows();if(d&&this._checkboxcolumn){if(d.length===0){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:false});return}var c=d.length;if(this.groupable){c=this.dataview.loadedrecords.length}if(this.virtualmode){c=this.source._source.totalrecords}var b=this.selectedrowindexes.length;if(b===c){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:true})}else{if(b===0){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:false})}else{this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:null})}}}}},unselectrow:function(b,c){this._applyrowselection(b,false,c);if(c!==false){this._updatecheckboxselection()}},selectcell:function(c,b){this._applycellselection(c,b,true)},unselectcell:function(c,b){this._applycellselection(c,b,false)},clearselection:function(c,d){this._trigger=false;this.selectedrowindex=-1;this._oldselectedcell=null;if(d!==false){for(var b=0;b<this.selectedrowindexes.length;b++){this._raiseEvent(3,{rowindex:this.selectedrowindexes[b]})}}this.selectedrowindexes=new Array();this.selectedcells=new Array();this.selectedcell=null;if(this.selectionmode=="checkbox"&&!this._checkboxcolumnupdating){this._checkboxcolumn.checkboxelement.jqxCheckBox({checked:false})}for(var b=0;b<this.columns.records.length;b++){this.columns.records[b].selected=false;this.columns.records[b]._applyStyle()}if(false===c){this._trigger=true;return}this._renderrows(this.virtualsizeinfo);this._trigger=true;if(this.selectionmode=="checkbox"){this._raiseEvent(3,{rowindex:this.selectedrowindexes})}},getselectedrowindex:function(){if(this.selectedrowindex==-1||this.selectedrowindex==undefined){for(var b=0;b<this.selectedrowindexes.length;b++){return this.selectedrowindexes[b]}}return this.selectedrowindex},getselectedrowindexes:function(){return this.selectedrowindexes},getselectedcell:function(){if(!this.selectedcell){return null}var b=this.selectedcell;b.row=this.selectedcell.rowindex;b.column=this.selectedcell.datafield;b.value=this.getcellvalue(b.row,b.column);return b},getselectedcells:function(){var b=new Array();for(var c in this.selectedcells){b[b.length]=this.selectedcells[c]}return b},getselection:function(){return{cells:this.getselectedcells(),rows:this.getselectedrowindexes()}},_getcellsforcopypaste:function(){var e=new Array();if(this.selectionmode.indexOf("cell")==-1){var h=this.selectedrowindexes;for(var d=0;d<h.length;d++){var c=h[d];for(var f=0;f<this.columns.records.length;f++){if(this.columns.records[f].datafield==="_checkboxcolumn"){continue}var g=c+"_"+this.columns.records[f].datafield;var b={rowindex:c,datafield:this.columns.records[f].datafield};e.push(b)}}}return e},deleteselection:function(){var d=this;var f=d.getselectedcells();if(this.selectionmode.indexOf("cell")==-1){f=this._getcellsforcopypaste()}if(f!=null&&f.length>0){for(var e=0;e<f.length;e++){var b=f[e];var g=d.getcolumn(b.datafield);var h=d.getcellvalue(b.rowindex,b.datafield);if(!g){continue}if(h!==""){var c=null;if(g.columntype=="checkbox"){if(!g.threestatecheckbox){c=false}}d._raiseEvent(17,{rowindex:b.rowindex,datafield:b.datafield,value:h});if(e==f.length-1){d.setcellvalue(b.rowindex,b.datafield,c,true);if(g.displayfield!=g.datafield){d.setcellvalue(b.rowindex,g.displayfield,c,true)}}else{d.setcellvalue(b.rowindex,b.datafield,c,false);if(g.displayfield!=g.datafield){d.setcellvalue(b.rowindex,g.displayfield,c,true)}}d._raiseEvent(18,{rowindex:b.rowindex,datafield:b.datafield,oldvalue:h,value:c})}}this.dataview.updateview();this._renderrows(this.virtualsizeinfo)}},copyselection:function(){var n="";var s=this;this.clipboardselection={};this.logicalclipboardselection={};this._clipboardselection=[];var r=s.getselectedcells();if(this.selectionmode.indexOf("cell")==-1){r=this._getcellsforcopypaste()}var b=0;var e=new Array();if(r!=null&&r.length>0){var t=999999999999999;var q=-1;for(var j=0;j<r.length;j++){var l=r[j];var d=s.getcolumn(l.datafield);if(d!=null&&d.clipboard&&(!d.hidden||this.copytoclipboardhiddencolumns)){if(e.indexOf(d.text)==-1){e.push(d.text)}var p=s.getcelltext(l.rowindex,d.displayfield);var h=this.getrowdisplayindex(l.rowindex);if(!this.clipboardselection[h]){this.clipboardselection[h]={}}this.clipboardselection[h][d.displayfield]=p;if(!this.logicalclipboardselection[h]){this.logicalclipboardselection[h]={}}this.logicalclipboardselection[h][d.displayfield]=p;if(d.displayfield!=d.datafield){this.logicalclipboardselection[h][d.datafield]=s.getcellvalue(l.rowindex,d.datafield)}t=Math.min(t,h);q=Math.max(q,h)}}var g=new Array();for(var f=t;f<=q;f++){if(!this.logicalclipboardselection[f]){continue}var o=a.extend({},this.logicalclipboardselection[f]);g.push(o)}this.logicalclipboardselection=g;if(this.copytoclipboardwithheaders){for(var c=0;c<e.length;c++){if(c>0){n+="\t"}n+=e[c]}n+="\r\n"}for(var f=t;f<=q;f++){var k=0;this._clipboardselection[this._clipboardselection.length]=new Array();if(this.clipboardselection[f]!=undefined){a.each(this.clipboardselection[f],function(i,m){if(k>0){n+="\t"}var u=m;if(m==null){u=""}s._clipboardselection[s._clipboardselection.length-1][k]=u;k++;n+=u})}else{continue}if(f<q){n+="\r\n"}}}this.clipboardselectedtext=n;return n},pasteselection:function(){var g=this.getselectedcells();this._oldselectedcell=null;if(this.selectionmode.indexOf("cell")==-1){g=this._getcellsforcopypaste()}if(g!=null&&g.length>0){var h=g[0].rowindex;var z=this.getrowdisplayindex(h);var r=g[0].datafield;var w=this._getcolumnindex(r);var p=0;this.selectedrowindexes=new Array();this.selectedcells=new Array();var l=g.length;var D=0;var d=new Array();var s=[];if(this.copytoclipboardwithheaders){this._clipboardselection.splice(0,1)}if(!this._clipboardselection){this._clipboardselection=[]}for(var B=0;B<this._clipboardselection.length;B++){D+=this._clipboardselection[B].length;d[B]=new Array();for(var A=0;A<this._clipboardselection[B].length;A++){var u=this._clipboardselection[B][A];d[B].push(u)}}if(D<g.length){var o=new Array();for(var B=0;B<g.length;B++){var e=g[B];if(!o[e.rowindex]){o[e.rowindex]=new Array()}o[e.rowindex].push(e)}var C=0;var F=0;for(var B=0;B<o.length;B++){if(!o[B]){continue}for(var A=0;A<o[B].length;A++){var e=o[B][A];var n=e.rowindex;var f=this.getcolumn(e.datafield);if(f.datafield==="_checkboxcolumn"){continue}if(f.hidden){continue}if(f.editable===false){continue}var u="";if(d[C]&&undefined==d[C][F]){F=0}u=""+d[C][F];F++;if(f.cellsformat){if(f.cellsformat.indexOf("p")!=-1||f.cellsformat.indexOf("d")!=-1||f.cellsformat.indexOf("c")!=-1||f.cellsformat.indexOf("n")!=-1||f.cellsformat.indexOf("f")!=-1){if(u.indexOf&&u.indexOf(this.gridlocalization.currencysymbol)>-1){u=u.replace(this.gridlocalization.currencysymbol,"")}var b=function(x,j,t){var c=x;if(j==t){return x}var i=c.indexOf(j);while(i!=-1){c=c.replace(j,t);i=c.indexOf(j)}return c};u=b(u,this.gridlocalization.thousandsseparator,"");u=u.replace(this.gridlocalization.decimalseparator,".");if(u.indexOf(this.gridlocalization.percentsymbol)>-1){u=u.replace(this.gridlocalization.percentsymbol,"")}var G="";for(var v=0;v<u.length;v++){var q=u.substring(v,v+1);if(q==="-"){G+="-"}if(q==="."){G+="."}if(q.match(/^[0-9]+$/)!=null){G+=q}}u=G;u=u.replace(/ /g,"");u=new Number(u);if(isNaN(u)){u=""}}}this._raiseEvent(17,{rowindex:n,datafield:e.datafield,value:u});var m=this.getrowdata(n);s.push({oldvalue:m[e.datafield],value:u,datafield:e.datafield,row:n});this.pushToHistory=true;this.setcellvalue(n,f.displayfield,u,false);this.pushToHistory=false;if(f.displayfield!=f.datafield&&this.logicalclipboardselection){if(this.logicalclipboardselection[n]){var y=this.logicalclipboardselection[n][f.datafield];if(y!=undefined){this.setcellvalue(n,f.datafield,y,false)}}}this._raiseEvent(18,{rowindex:n,datafield:e.datafield,oldvalue:this.getcellvalue(e.rowindex,e.datafield),value:u});this._applycellselection(n,e.datafield,true,false)}C++;F=0;if(!d[C]){C=0}}}else{if(!this._clipboardselection){return}for(var m=0;m<this._clipboardselection.length;m++){for(var E=0;E<this._clipboardselection[m].length;E++){var f=this.getcolumnat(w+E);if(!f){continue}if(f.datafield==="_checkboxcolumn"){continue}if(f.hidden){continue}if(f.editable===false){continue}var n=this.getrowboundindex(z+m);var e=this.getcell(n,f.datafield);var u=null;u=this._clipboardselection[m][E];if(u!=null){if(f.cellsformat){if(f.cellsformat.indexOf("p")!=-1||f.cellsformat.indexOf("d")!=-1||f.cellsformat.indexOf("c")!=-1||f.cellsformat.indexOf("n")!=-1||f.cellsformat.indexOf("f")!=-1){if(u.indexOf(this.gridlocalization.currencysymbol)>-1){u=u.replace(this.gridlocalization.currencysymbol,"")}var b=function(x,j,t){var c=x;if(j==t){return x}var i=c.indexOf(j);while(i!=-1){c=c.replace(j,t);i=c.indexOf(j)}return c};u=b(u,this.gridlocalization.thousandsseparator,"");u=u.replace(this.gridlocalization.decimalseparator,".");if(u.indexOf(this.gridlocalization.percentsymbol)>-1){u=u.replace(this.gridlocalization.percentsymbol,"")}var G="";for(var v=0;v<u.length;v++){var q=u.substring(v,v+1);if(q==="-"){G+="-"}if(q==="."){G+="."}if(q.match(/^[0-9]+$/)!=null){G+=q}}u=G;u=u.replace(/ /g,"");u=new Number(u);if(isNaN(u)){u=""}}}this._raiseEvent(17,{rowindex:n,datafield:e.datafield,value:u});var k=this.getrowdata(n);s.push({oldvalue:k[e.datafield],value:u,datafield:e.datafield,row:n});this.pushToHistory=true;this.setcellvalue(n,f.displayfield,u,false);this.pushToHistory=false;if(f.displayfield!=f.datafield&&this.logicalclipboardselection){var y=this.logicalclipboardselection[m][f.datafield];if(y!=undefined){this.setcellvalue(n,f.datafield,y,false)}}this._raiseEvent(18,{rowindex:n,datafield:e.datafield,oldvalue:this.getcellvalue(e.rowindex,e.datafield),value:u});this._applycellselection(n,e.datafield,true,false)}}}}if(this.selectionmode=="checkbox"){this._updatecheckboxselection()}this.dataview.updateview();this._renderrows(this.virtualsizeinfo)}this._undoRedo.push({action:"paste",data:s});this._undoRedoIndex=-1;if(this.clipboardend){this.clipboardend("paste")}},_applyrowselection:function(f,j,g,i,b){if(f==null){return false}var k=this.selectedrowindex;if(this.selectionmode=="singlerow"){var e=this;setTimeout(function(){if(j){e._raiseEvent(2,{rowindex:f,row:e.getrowdata(f)})}else{e._raiseEvent(3,{rowindex:f,row:e.getrowdata(f)})}e._raiseEvent(3,{rowindex:k})});this.selectedrowindexes=new Array();this.selectedcells=new Array()}if(i==true){this.selectedrowindexes=new Array()}if(this.dataview.filters.length>0){var c=this.getrowdata(f);if(c&&c.dataindex!==undefined){f=c.dataindex}else{if(c&&c.dataindex===undefined){if(c.uid!=undefined){f=this.getrowboundindexbyid(c.uid)}}}}var d=this.selectedrowindexes.indexOf(f);if(j){this.selectedrowindex=f;if(d==-1){this.selectedrowindexes.push(f);if(this.selectionmode!="singlerow"){this._raiseEvent(2,{rowindex:f,row:this.getrowdata(f)})}}else{if(this.selectionmode=="multiplerows"){this.selectedrowindexes.splice(d,1);this._raiseEvent(3,{rowindex:this.selectedrowindex,row:this.getrowdata(f)});this.selectedrowindex=this.selectedrowindexes.length>0?this.selectedrowindexes[this.selectedrowindexes.length-1]:-1}}}else{if(d>=0||this.selectionmode=="singlerow"||this.selectionmode=="multiplerowsextended"||this.selectionmode=="multiplerowsadvanced"){var h=this.selectedrowindexes[d];this.selectedrowindexes.splice(d,1);this._raiseEvent(3,{rowindex:h,row:this.getrowdata(f)});this.selectedrowindex=-1}}if(g==undefined||g){this._rendervisualrows()}return true},_applycellselection:function(f,c,i,g,l){if(f==null){return false}if(c==null){return false}if(this._autofill){this._autofill.remove();a(document).off("pointermove.autofill");a(document).off("pointerup.autofill");this._autofill=null}var m=this.selectedrowindex;if(this.selectionmode=="singlecell"){var e=this.selectedcell;if(e!=null){this._raiseEvent(16,{rowindex:e.rowindex,datafield:e.datafield})}this.selectedcells=new Array()}if(this.selectionmode=="multiplecellsextended"||this.selectionmode=="multiplecellsadvanced"){var e=this.selectedcell;if(e!=null){this._raiseEvent(16,{rowindex:e.rowindex,datafield:e.datafield})}}var h=f+"_"+c;if(this.dataview.filters.length>0){var d=this.getrowdata(f);if(d&&d.dataindex!==undefined){f=d.dataindex;var h=f+"_"+c}else{if(d&&d.dataindex===undefined){if(d.uid){f=this.getrowboundindexbyid(d.uid);var h=f+"_"+c}}}}var k={rowindex:f,datafield:c};if(i){var j=this.selectedcell;this.selectedcell=k;if(!this.selectedcells[h]){this.selectedcells[h]=Object.assign({},k);this.selectedcells.length++;var b=true;if(j&&j.datafield===k.datafield&&k.rowindex===j.rowindex){b=false}if(b&&l!==false){this._raiseEvent(15,k)}}else{if(this.selectionmode=="multiplecells"||this.selectionmode=="multiplecellsextended"||this.selectionmode=="multiplecellsadvanced"){delete this.selectedcells[h];if(this.selectedcells.length>0){this.selectedcells.length--}if(l!==false){this._raiseEvent(16,k)}}}}else{delete this.selectedcells[h];if(this.selectedcells.length>0){this.selectedcells.length--}if(l!==false){this._raiseEvent(16,k)}}if(g==undefined||g){this._rendervisualrows()}return true},_getcellindex:function(b){var c=-1;a.each(this.selectedcells,function(){c++;if(this[b]){return false}});return c},_clearhoverstyle:function(){if(undefined==this.hoveredrow||this.hoveredrow==-1){return}if(this.vScrollInstance.isScrolling()){return}if(this.hScrollInstance.isScrolling()){return}var c=this.table.find(".jqx-grid-cell-hover");if(c.length>0){c.removeClass(this.toTP("jqx-grid-cell-hover"));c.removeClass(this.toTP("jqx-fill-state-hover"))}for(var d=0;d<c.length;d++){var e=c[d].getAttribute("columnindex");if(e){var b=this.columns.records[parseInt(e)];if(b){b._applyCellStyle(c[d])}}}this.hoveredrow=-1},_clearselectstyle:function(){var m=this.table[0].rows.length;var r=this.table[0].rows;var n=this.toTP("jqx-grid-cell-selected");var c=this.toTP("jqx-fill-state-pressed");var o=this.toTP("jqx-grid-cell-hover");var l=this.toTP("jqx-fill-state-hover");for(var k=0;k<m;k++){var b=r[k];var h=b.cells.length;var q=b.cells;for(var g=0;g<h;g++){var e=q[g];var p=a(e);if(e.className.indexOf("jqx-grid-cell-selected")!=-1){p.removeClass(n);p.removeClass(c)}if(e.className.indexOf("jqx-grid-cell-hover")!=-1){p.removeClass(o);p.removeClass(l)}var f=e.getAttribute("columnindex");if(f){var d=this.columns.records[parseInt(f)];if(d){d._applyCellStyle(e)}}}}},_selectpath:function(p,f){var n=this;var g=this;var j=this._lastClickedCell?Math.min(this._lastClickedCell.row,p):0;var m=this._lastClickedCell?Math.max(this._lastClickedCell.row,p):0;var l=null;if(j<=m){var i=this._getcolumnindex(this._lastClickedCell.column||g._lastClickedCell.datafield);var h=this._getcolumnindex(f);var e=Math.min(i,h);var d=Math.max(i,h);this.selectedcells=new Array();var o=this.dataview.loadedrecords;for(var b=j;b<=m;b++){for(var k=e;k<=d;k++){var p=o[b];this._applycellselection(n.getboundindex(p),n._getcolumnat(k).datafield,true,false);l={row:n.getboundindex(p),datafield:n._getcolumnat(k).datafield}}}this._rendervisualrows()}},_selectrowpath:function(g){if(this.selectionmode=="multiplerowsextended"){var c=this;var b=this._lastClickedCell?Math.min(this._lastClickedCell.row,g):0;var h=this._lastClickedCell?Math.max(this._lastClickedCell.row,g):0;var f=this.dataview.loadedrecords;if(b<=h){this.selectedrowindexes=new Array();for(var e=b;e<=h;e++){var g=f[e];var d=this.getrowboundindex(e);this._applyrowselection(d,true,false)}this._rendervisualrows()}}},_selectrowwithmouse:function(q,b,c,f,d,t){var k=b.row;if(k==undefined){return}var l=b.index;if(this.hittestinfo[l]==undefined){return}for(var x=0;x<this.columns.records.length;x++){var w=this.columns.records[x];w.selected=false;if(w.element){w.element.removeAttribute("selected")}w._applyStyle()}var u=this.hittestinfo[l].visualrow;if(this.hittestinfo[l].details){return}var n=u.cells[0].className;if(k.group){return}if(this.selectionmode=="multiplerows"||this.selectionmode=="multiplecells"||this.selectionmode=="checkbox"||(this.selectionmode.indexOf("multiple")!=-1&&(t==true||d==true))){var m=this.getboundindex(k);if(this.dataview.filters.length>0){var y=this.getrowdata(m);if(y){m=y.dataindex;if(m==undefined){var m=this.getboundindex(k)}}}var s=c.indexOf(m)!=-1;var z=this.getboundindex(k)+"_"+f;if(this.selectionmode.indexOf("cell")!=-1){var h=this.selectedcells[z]!=undefined;if(this.selectedcells[z]!=undefined&&h){this._selectcellwithstyle(q,false,l,f,u)}else{this._selectcellwithstyle(q,true,l,f,u)}if(t&&this._lastClickedCell==undefined){var g=this.getselectedcells();if(g&&g.length>0){this._lastClickedCell={row:g[0].rowindex,column:g[0].datafield}}}if(t&&this._lastClickedCell){this._selectpath(k.visibleindex,f);this.mousecaptured=false;if(this.selectionarea.css("visibility")=="visible"){this.selectionarea.css("visibility","hidden")}}}else{if(s){if(d){this._applyrowselection(this.getboundindex(k),false)}else{this._selectrowwithstyle(q,u,false,f)}}else{this._selectrowwithstyle(q,u,true,f)}if(t&&this._lastClickedCell==undefined){var j=this.getselectedrowindexes();if(j&&j.length>0){this._lastClickedCell={row:j[0],column:f}}}if(t&&this._lastClickedCell){this.selectedrowindexes=new Array();var e=this._lastClickedCell?Math.min(this._lastClickedCell.row,k.visibleindex):0;var v=this._lastClickedCell?Math.max(this._lastClickedCell.row,k.visibleindex):0;var o=this.dataview.loadedrecords;for(var p=e;p<=v;p++){var k=o[p];if(k){this._applyrowselection(this.getboundindex(k),true,false,false)}}this._rendervisualrows()}}}else{this._clearselectstyle();this._selectrowwithstyle(q,u,true,f);if(this.selectionmode.indexOf("cell")!=-1){this._selectcellwithstyle(q,true,l,f,u)}}if(!t){this._lastClickedCell={row:k.visibleindex,column:f}}},_selectcellwithstyle:function(e,c,h,g,f){var b=a(f.cells[e._getcolumnindex(g)]);b.removeClass(this.toTP("jqx-grid-cell-hover"));b.removeClass(this.toTP("jqx-fill-state-hover"));if(c){b.addClass(this.toTP("jqx-grid-cell-selected"));b.addClass(this.toTP("jqx-fill-state-pressed"))}else{b.removeClass(this.toTP("jqx-grid-cell-selected"));b.removeClass(this.toTP("jqx-fill-state-pressed"))}var d=this.getcolumn(g);d._applyCellStyle(b)},_selectrowwithstyle:function(k,b,j,f){var h=b.cells.length;var c=0;if(k.rowdetails&&k.showrowdetailscolumn){if(!this.rtl){c=1+this.groups.length}else{h-=1;h-=this.groups.length}}else{if(this.groupable){if(!this.rtl){c=this.groups.length}else{h-=this.groups.length}}}for(var g=c;g<h;g++){var e=b.cells[g];if(j){a(e).removeClass(this.toTP("jqx-grid-cell-hover"));a(e).removeClass(this.toTP("jqx-fill-state-hover"));if(k.selectionmode.indexOf("cell")==-1){a(e).addClass(this.toTP("jqx-grid-cell-selected"));a(e).addClass(this.toTP("jqx-fill-state-pressed"))}}else{a(e).removeClass(this.toTP("jqx-grid-cell-hover"));a(e).removeClass(this.toTP("jqx-grid-cell-selected"));a(e).removeClass(this.toTP("jqx-fill-state-hover"));a(e).removeClass(this.toTP("jqx-fill-state-pressed"))}var f=e.getAttribute("columnindex");if(f){var d=this.columns.records[parseInt(f)];if(d){d._applyCellStyle(e)}}}},_handlemousemoveselection:function(ae,r,X){if(r.hScrollInstance.isScrolling()||r.vScrollInstance.isScrolling()){return false}if((r.selectionmode=="multiplerowsextended"||r.selectionmode=="multiplecellsextended"||r.selectionmode=="multiplecellsadvanced")&&r.mousecaptured){if(r.multipleselectionbegins){var b=r.multipleselectionbegins(ae);if(b===false){return true}}var ad=this.showheader?this.columnsheader.height()+2:0;var K=this._groupsheader()?this.groupsheader.height():0;var M=this.showtoolbar?this.toolbar.height():0;var J=this.showfilterbar?this.toolbarheight:0;K+=M;K+=J;var ac=this.host.coord();if(this.hasTransform){ac=a.jqx.utilities.getOffset(this.host);var ag=this._getBodyOffset();ac.left-=ag.left;ac.top-=ag.top}if(this.host.css("border-top-width")==="0px"){K-=2}var O=ae.pageX;var N=ae.pageY-K;if(ae._pageX){O=ae._pageX;N=ae._pageY-K}if(Math.abs(this.mousecaptureposition.left-O)>3||Math.abs(this.mousecaptureposition.top-N)>3||r.autofill){var g=parseInt(this.columnsheader.coord().top);if(this.hasTransform){g=a.jqx.utilities.getOffset(this.columnsheader).top}if(O<ac.left){O=ac.left}if(O>ac.left+this.host.width()){O=ac.left+this.host.width()}var aa=ac.top+ad;if(N<aa){N=aa+5}var L=parseInt(Math.min(r.mousecaptureposition.left,O));var h=-5+parseInt(Math.min(r.mousecaptureposition.top,N));var I=parseFloat(Math.abs(r.mousecaptureposition.left-O));var R=parseInt(Math.abs(r.mousecaptureposition.top-N));L-=ac.left;h-=ac.top;this.selectionarea.css("visibility","visible");if(r.selectionmode=="multiplecellsadvanced"){var O=L;var u=O+I;var H=O;var o=r.hScrollInstance;var w=o.value;if(this.rtl){if(this.hScrollBar.css("visibility")!="hidden"){w=o.max-o.value}if(this.vScrollBar[0].style.visibility!="hidden"){}}var j=r.table[0].rows[0];var V=0;var C=r.mousecaptureposition.clickedcell;var B=C;var n=false;var s=0;var af=j.cells.length;if(r.mousecaptureposition.left<=ae.pageX){s=C}var c=false;for(var Z=s;Z<af;Z++){var ab=parseFloat(a(this.columnsrow[0].cells[Z]).css("left"));var l=ab-w;if(r.columns.records[Z].pinned&&!r.columns.records[Z].hidden){if(Z==C){c=true}var U=ab+a(this.columnsrow[0].cells[Z]).width();if(r.mousecaptureposition.left>ae.pageX){if(U>=O&&O>=l){B=Z;n=true;break}}else{if(U>=u&&u>=l){B=Z;n=true;break}}continue}if(c){n=true;B--;break}var Q=this._getcolumnat(Z);if(Q!=null&&Q.hidden){continue}if(r.groupable&&r.groups.length>0){if(Z<r.groups.length){continue}}var U=l+a(this.columnsrow[0].cells[Z]).width();if(r.mousecaptureposition.left>ae.pageX){if(U>=O&&O>=l){B=Z;n=true;break}}else{if(U>=u&&u>=l){B=Z;n=true;break}}}if(!n){if(r.mousecaptureposition.left>ae.pageX){a.each(this.columns.records,function(i,k){if(r.groupable&&r.groups.length>0){if(i<r.groups.length){return true}}if(!this.pinned&&!this.hidden){B=i;return false}})}else{if(!r.groupable||(r.groupable&&!r.groups.length>0)){B=j.cells.length-1}}}var P=C;C=Math.min(C,B);B=Math.max(P,B);h+=5;h+=K;var T=r.table[0].rows.indexOf(r.mousecaptureposition.clickedrow);var z=0;var f=-1;var v=-1;var e=0;for(var Z=0;Z<r.table[0].rows.length;Z++){var t=a(r.table[0].rows[Z]);if(Z==0){e=t.coord().top}var G=t.height();var A=e-ac.top;if(f==-1&&A+G>=h){var d=false;for(var S=0;S<r.groups.length;S++){var Y=t[0].cells[S].className;if(Y.indexOf("jqx-grid-group-collapse")!=-1||Y.indexOf("jqx-grid-group-expand")!=-1){d=true;break}}if(d){continue}f=Z}e+=G;if(r.groupable&&r.groups.length>0){var d=false;for(var S=0;S<r.groups.length;S++){var Y=t[0].cells[S].className;if(Y.indexOf("jqx-grid-group-collapse")!=-1||Y.indexOf("jqx-grid-group-expand")!=-1){d=true;break}}if(d){continue}var V=0;for(var W=r.groups.length;W<t[0].cells.length;W++){var F=t[0].cells[W];if(a(F).html()==""){V++}}if(V==t[0].cells.length-r.groups.length){continue}}if(f!=-1){z+=G}if(A+G>h+R){v=Z;break}}if(f!=-1){h=a(r.table[0].rows[f]).coord().top-ac.top-K-2;var E=0;if(this.filterable&&this.showfilterrow){E=this.filterrowheight}if(parseFloat(r.table[0].style.top)<0&&h<this.rowsheight+E){h-=parseFloat(r.table[0].style.top);z+=parseFloat(r.table[0].style.top)}R=z;var m=a(this.columnsrow[0].cells[C]);var D=a(this.columnsrow[0].cells[B]);L=parseFloat(m.css("left"));I=parseFloat(D.css("left"))-parseFloat(L)+D.width()-2;L-=w;if(c){L+=w}if(r.editcell&&r.editable&&r.endcelledit&&(C!=B||f!=v)){if(r.editcell.validated==false){return}r.endcelledit(r.editcell.row,r.editcell.column,true,true)}}}this.selectionarea.width(I);this.selectionarea.css("left",L);if(X!==null){this.selectionarea.css("top",h);this.selectionarea.height(R)}}}},_handlemouseupselection:function(R,k,D){if(!this.selectionarea){return}var E=this;if(this.selectionarea[0].style.visibility!="visible"){k._handlemousemoveselection(R,k);if(D!==false){if(k.__firstcell){var H=k.getselectedcell();if(H){k.__firstcell.index=k.columns.records.indexOf(k.getcolumn(H.datafield));k.__firstcell.row=H.row;k.__firstcell.rowindex=H.rowindex;k._handleAutofill(R,k)}}else{k.__firstcell=k.getselectedcell();if(k.__firstcell){k.__firstcell.index=k.columns.records.indexOf(k.getcolumn(k.__firstcell.datafield));k._handleAutofill(R,k)}}}k.mousecaptured=false;k.selectionarea[0].style.visibility="hidden";return true}if(k.mousecaptured&&(k.selectionmode=="multiplerowsextended"||k.selectionmode=="multiplerowsadvanced"||k.selectionmode=="multiplecellsextended"||k.selectionmode=="multiplecellsadvanced")){k.mousecaptured=false;if(this.selectionarea.css("visibility")=="visible"){this.selectionarea.css("visibility","hidden");var Q=this.showheader?this.columnsheader.height()+2:0;var z=this._groupsheader()?this.groupsheader.height():0;if(this.host.css("border-top-width")==="0px"){z-=2}var C=this.showtoolbar?this.toolbar.height():0;z+=C;var w=this.showfilterbar?this.toolbarheight:0;z+=w;var v=this.selectionarea.coord();var O=this.host.coord();if(this.hasTransform){O=a.jqx.utilities.getOffset(this.host);v=a.jqx.utilities.getOffset(this.selectionarea)}if(this.host.css("border-top-width")==="0px"){z-=2}var G=v.left-O.left;var F=v.top-Q-O.top-z;var K=F;var q=G+this.selectionarea.width();var u=G;var N=new Array();var c=new Array();if(k.selectionmode=="multiplerowsextended"){while(F<K+this.selectionarea.height()){var P=this._hittestrow(G,F);var p=P.row;var A=P.index;if(A!=-1){if(!c[A]){c[A]=true;N[N.length]=P}}F+=20}var K=0;a.each(N,function(){var i=this;var m=this.row;if(k.selectionmode!="none"&&k._selectrowwithmouse){if(R.ctrlKey||R.metaKey){k._applyrowselection(k.getboundindex(m),true,false,false)}else{if(K==0){k._applyrowselection(k.getboundindex(m),true,false,true)}else{k._applyrowselection(k.getboundindex(m),true,false,false)}}K++}})}else{var L=null;var f=null;if(k.selectionmode=="multiplecellsadvanced"){F+=2}var h=k.hScrollInstance;var r=h.value;if(this.rtl){if(this.hScrollBar.css("visibility")!="hidden"){r=h.max-h.value}if(this.vScrollBar[0].style.visibility!="hidden"){r-=this.scrollbarsize+4}}var d=k.table[0].rows[0];var l=k.selectionarea.height();if(!R.ctrlKey&&!R.metaKey&&l>0){k.selectedcells=new Array()}var B=l;var g=parseInt(this.vScrollInstance.value);var e=parseInt(this.hScrollInstance.value);var o=this._gettableheight();var t=this._hostwidth!=undefined?this._hostwidth:this.host.width();var S=this.groupable&&this.groups.length>0?this.groups.length:0;var n=this.columns.records.length-S;var s=this.groupable&&this.groups.length>0;var j=this._getvisualcolumnsindexes(e,t,S,n,s,true,true);var b=j.start;var I=j.end;E.__firstcell=null;while(F<K+B){var P=k._hittestrow(G,F);if(!P){F+=5;continue}var p=P.row;var A=P.index;if(A!=-1){if(!c[A]){c[A]=true;if(!this.enableoptimization){for(var M=0;M<d.cells.length;M++){var e=parseFloat(a(k.columnsrow[0].cells[M]).css("left"))-r;var J=e+a(k.columnsrow[0].cells[M]).width();if((u>=e&&u<=J)||(q>=e&&q<=J)||(e>=u&&e<=q)){k._applycellselection(k.getboundindex(p),k._getcolumnat(M).datafield,true,false);L={x:G,y:F,rowindex:A,row:k.getboundindex(p),index:M,datafield:k._getcolumnat(M).datafield};k.__lastcell=L;if(!f){f=L;k.__firstcell=f}}}}else{for(var M=b;M<I;M++){var e=parseFloat(k.columnsrow[0].cells[M].style.left)-r;var J=e+k.columns.records[M].width;if((u>=e&&u<=J)||(q>=e&&q<=J)||(e>=u&&e<=q)){k._applycellselection(k.getboundindex(p),k._getcolumnat(M).datafield,true,false);L={x:G,y:F,rowindex:A,row:k.getboundindex(p),index:M,datafield:k._getcolumnat(M).datafield};k.__lastcell=L;if(!f){f=L;k.__firstcell=f}}}}}}F+=5}}if(k.autosavestate){if(k.savestate){k.savestate()}}}k._renderrows(k.virtualsizeinfo);if(D!==false){k._handleAutofill(R,k)}}},_handleAutofill:function(u,n){if(!this.autofill){return}var f=this;var v=this.showheader?this.columnsheader.height()+2:0;var o=this._groupsheader()?this.groupsheader.height():0;if(this.host.css("border-top-width")==="0px"){o-=2}var i=new Array();var d=new Array();var A=this.showtoolbar?this.toolbar.height():0;o+=A;var q=this.showfilterbar?this.toolbarheight:0;o+=q;var B=this.selectionarea.coord();var b=this.host.coord();if(this.hasTransform){b=a.jqx.utilities.getOffset(this.host);B=a.jqx.utilities.getOffset(this.selectionarea)}if(this.host.css("border-top-width")==="0px"){o-=2}var j=B.left-b.left;var h=B.top-v-b.top-o;var s=h;var e=j+this.selectionarea.width();var C=j;var k=f.__lastcell;var z=f.__firstcell;if(n.selectionmode=="multiplecellsadvanced"){h+=2}var r=n.hScrollInstance;var t=r.value;if(this.rtl){if(this.hScrollBar.css("visibility")!="hidden"){t=r.max-r.value}if(this.vScrollBar[0].style.visibility!="hidden"){t-=this.scrollbarsize+4}}var p=n.table[0].rows[0];var g=n.selectionarea.height();var c=parseInt(u.pageX);var l=parseInt(u.pageY);var w=g;var f=n;if(f._autofill){f._autofill.remove()}if(z&&!f.getcolumn(z.datafield).autofill){return}if(f.autofill){f._autofill=document.createElement("div");f._autofill.style.width="6px";f._autofill.style.height="6px";f._autofill.style.position="absolute";f._autofill.style.top=B.top-f.host.offset().top+w+1+"px";f._autofill.style.left=e+2+"px";f._autofill.style.cursor="crosshair";f._autofill.style.paddingLeft="0px";f._autofill.style.paddingTop="0px";f._autofill.style.paddingRight="0px";f._autofill.style.paddingBottom="0px";f._autofill.style.borderTopLeftRadius="0px";f._autofill.style.borderTopRightRadius="0px";f._autofill.style.borderBottomLeftRadius="0px";f._autofill.style.borderBottomRightRadius="0px";f._autofill.style.background="transparent";a(f._autofill).addClass(f.toThemeProperty("jqx-widget"));a(f._autofill).addClass(f.toThemeProperty("jqx-button"));a(f._autofill).addClass(f.toThemeProperty("primary"));a(f._autofill).addClass(f.toThemeProperty("jqx-fill-state-pressed"));f._autofill.onpointerdown=function(F){f._handlemouseupselection(F,f,false);var G=F.originalEvent?F.originalEvent:F;f._autofillDragStart=true;f.mousecaptured=true;var I=f.showheader?f.columnsheader.height()+2:0;var H=f._groupsheader()?f.groupsheader.height():0;var L=f.showtoolbar?f.toolbarheight:0;var D=f.showfilterbar?f.toolbarheight:0;H+=L;H+=D;var J=f.host.coord();var R=c-J.left;var P=l-I-J.top-H;if(f.pageable&&!f.autoheight&&f.gotopage){var O=f.pager.coord().top-J.top-H-I;if(P>O){return}}var N=f._hittestrow(R,P);if(!N){return}if(N.details){return}var S=N.row;var K=N.index;var Q=G.target.className;var E=f.table[0].rows[z.rowindex];var M=a(n.columnsrow[0].cells[z.index]).coord().left;var m=a(E).coord().top+1;f.mousecaptured=true;f.mousecaptureposition={x:M,y:m,left:G.pageX,top:G.pageY,clickedrow:E};f.copyselection();G.stopPropagation();G.preventDefault()};a(document).off("pointermove.autofill");a(document).off("pointerup.autofill");a(document).on("pointermove.autofill",function(x){if(f._autofillDragStart){var m=x.originalEvent?x.originalEvent:x;if(f.mousecaptureposition.position){if(f.mousecaptureposition.position==="y"){m._pageX=f.mousecaptureposition.x;m._pageY=m.pageY;f.mousecaptureposition.top=f.mousecaptureposition.y;f._handlemousemoveselection(m,f)}else{m._pageX=m.pageX;m._pageY=f.mousecaptureposition.y;f.mousecaptureposition.clickedcell=z.index;f.mousecaptureposition.left=f.mousecaptureposition.x;f._handlemousemoveselection(m,f,null)}}else{if(Math.abs(f.mousecaptureposition.left-m.pageX)>=5){f.mousecaptureposition.position="x"}else{if(Math.abs(f.mousecaptureposition.top-m.pageY)){f.mousecaptureposition.position="y"}}}}});a(document).on("pointerup.autofill",function(x){var m=x.originalEvent?x.originalEvent:x;if(!f._autofillDragStart){if(f._autofill){f._autofill.remove()}f.clearselection();return}f._handlemouseupselection(m,f);f._autofillDragStart=false;f.mousecaptureposition=null;f.mousecaptured=false;f.pasteselection();f._handlemouseupselection(m,f)});f.wrapper[0].appendChild(f._autofill)}},selectprevcell:function(e,c){var f=this._getcolumnindex(c);var b=this.columns.records.length;var d=this._getprevvisiblecolumn(f);if(d!=null){this.clearselection();this.selectcell(e,d.datafield)}},selectnextcell:function(e,d){var f=this._getcolumnindex(d);var c=this.columns.records.length;var b=this._getnextvisiblecolumn(f);if(b!=null){this.clearselection();this.selectcell(e,b.datafield)}},_getfirstvisiblecolumn:function(){var b=this;var e=this.columns.records.length;for(var c=0;c<e;c++){var d=this.columns.records[c];if(!d.hidden&&d.datafield!=null){return d}}return null},_getlastvisiblecolumn:function(){var b=this;var e=this.columns.records.length;for(var c=e-1;c>=0;c--){var d=this.columns.records[c];if(!d.hidden&&d.datafield!=null){return d}}return null},_handlekeydown:function(P,j){if(j.groupable&&j.groups.length>0){}if(j.disabled){return false}var t=P.charCode?P.charCode:P.keyCode?P.keyCode:0;if(t===32&&(P.metaKey||P.ctrlKey)&&j.selectionmode=="multiplecellsadvanced"){var M=this.getselectedcell();if(P.shiftKey){this.clearselection();this.selectallrows();if(this.columns.records.length<30){for(var N=0;N<this.columns.records.length;N++){var G=this.columns.records[N];if(G.selectable){G.toggleSelection(G,P,true)}}}this._renderrows(this.virtualsizeinfo);return}else{if(M){var G=this.getcolumn(M.datafield);if(G.selectable){this.clearselection();G.toggleSelection(G,P,true);this._renderrows(this.virtualsizeinfo)}}}}if(j.editcell&&j.selectionmode!="multiplecellsadvanced"){return true}else{if(j.editcell&&j.selectionmode=="multiplecellsadvanced"){if(t>=33&&t<=40){if(!P.altKey){if(j._cancelkeydown==undefined||j._cancelkeydown==false){if(j.editmode!=="selectedrow"){j.endcelledit(j.editcell.row,j.editcell.column,false,true);j._cancelkeydown=false;if(j.editcell&&!j.editcell.validated){j._rendervisualrows();j.endcelledit(j.editcell.row,j.editcell.column,false,true);return false}}else{return true}}else{j._cancelkeydown=false;return true}}else{j._cancelkeydown=false;return true}}else{return true}}}if(j.selectionmode=="none"){return true}if(j.showfilterrow&&j.filterable){if(this.filterrow){if(a(P.target).ischildof(j.filterrow)){return true}}}if(j.showeverpresentrow){if(j.addnewrowtop){if(a(P.target).ischildof(j.addnewrowtop)){return true}}if(j.addnewrowbottom){if(a(P.target).ischildof(j.addnewrowbottom)){return true}}}if(P.target.className&&P.target.className.indexOf("jqx-grid-widget")>=0){return true}if(j.pageable){if(a(P.target).ischildof(this.pager)){return true}}if(this.showtoolbar){if(a(P.target).ischildof(this.toolbar)){return true}}if(this.showfilterbar){if(a(P.target).ischildof(this.filterbar)){return true}}if(this.showstatusbar){if(a(P.target).ischildof(this.statusbar)){return true}}var f=false;if(P.altKey){return true}if(P.ctrlKey||P.metaKey){if(this.clipboard){var h=String.fromCharCode(t).toLowerCase();if(h==="z"){if(this._undoRedo){if(this._undoRedoIndex===-1){this._undoRedoIndex=this._undoRedo.length-1}var x=this._undoRedo[this._undoRedoIndex];if(x){if(x.action==="setcellvalue"){this.setcellvalue(x.data.row,x.data.datafield,x.data.oldvalue);this.clearselection();this.selectcell(x.data.row,x.data.datafield);this.ensurecellvisible(x.data.row,x.data.datafield)}else{if(x.action==="paste"){this.clearselection();if(x.data){for(var N=0;N<x.data.length;N++){var p=x.data[N];this.setcellvalue(p.row,p.datafield,p.oldvalue);this._applycellselection(p.row,p.datafield,true,false);if(N===0){this.ensurecellvisible(p.row,p.datafield)}}}this._rendervisualrows()}}}if(this._undoRedoIndex>0){this._undoRedoIndex--}}}if(h==="y"){if(this._undoRedo){if(this._undoRedoIndex===-1){this._undoRedoIndex=this._undoRedo.length-1}var x=this._undoRedo[this._undoRedoIndex];if(x){if(x.action==="setcellvalue"){this.setcellvalue(x.data.row,x.data.datafield,x.data.value);this.clearselection();this.selectcell(x.data.row,x.data.datafield);this.ensurecellvisible(x.data.row,x.data.datafield)}else{if(x.action==="paste"){this.clearselection();for(var N=0;N<x.data.length;N++){var p=x.data[N];this.setcellvalue(p.row,p.datafield,p.value);this._applycellselection(p.row,p.datafield,true,false);if(N===0){this.ensurecellvisible(p.row,p.datafield)}}this._rendervisualrows()}}}if(this._undoRedoIndex<this._undoRedo.length-1){this._undoRedoIndex++}}}if(h==="d"){var o=this.copyselection();var F=this._clipboardselection[0];this._clipboardselection=[this._clipboardselection[0]];this.pasteselection();P.preventDefault();P.stopPropagation()}if(this.clipboardbegin){var m=null;if(h=="c"){m=this.clipboardbegin("copy",this.copyselection())}else{if(h=="x"){m=this.clipboardbegin("cut",this.copyselection())}else{if(h=="v"){m=this.clipboardbegin("paste")}}}if(m===false){return false}}if(h=="c"||h=="x"){var v=this.copyselection();if(h=="c"&&this.clipboardend){this.clipboardend("copy")}if(h=="x"&&this.clipboardend){this.clipboardend("cut")}if(window.clipboardData){window.clipboardData.setData("Text",v)}else{var J=a('<textarea style="position: absolute; left: -1000px; top: -1000px;"/>');J.val(v);a("body").append(J);J.select();setTimeout(function(){document.designMode="off";J.select();J.remove();j.focus()},100)}if(h=="c"&&a.jqx.browser.msie){return false}else{if(h=="c"){return true}}}else{if(h=="v"){if(document.activeElement&&document.activeElement.nodeName==="INPUT"){return true}var O=a('<textarea style="position: absolute; left: -1000px; top: -1000px;"/>');a("body").append(O);O.select();var B=this;setTimeout(function(){B._clipboardselection=new Array();var T=O.val();if(T.length==0&&window.clipboardData){O.val(window.clipboardData.getData("Text"));var T=O.val()}var S=T.split("\n");for(var R=0;R<S.length;R++){if(S[R].split("\t").length>0){var Q=S[R].split("\t");if(Q.length==1&&R==S.length-1&&Q[0]==""){continue}if(Q.length>0){B._clipboardselection.push(Q)}}}B.pasteselection();O.remove();B.focus()},100);return true}}if(h=="x"){this.deleteselection();this.host.focus();return false}}}var C=Math.round(j._gettableheight());var A=Math.round(C/j.rowsheight);var q=j.getdatainformation();switch(j.selectionmode){case"singlecell":case"multiplecells":case"multiplecellsextended":case"multiplecellsadvanced":var M=j.getselectedcell();if(M===null){j.selectcell(0,j.columns.records[0].displayfield)}if(M!=null){var D=this.getrowvisibleindex(M.rowindex);var l=D;var E=M.datafield;var e=j._getcolumnindex(E);var c=j.columns.records.length;var z=function(W,Q,V,U){var i=function(ah,aa){var ac=j.dataview.loadedrecords[ah];if(j.groupable&&j.groups.length>0){var ad=ah;if(U=="up"){ad++}if(U=="down"){ad--}var ac=j.getdisplayrows()[ad];var X=function(ai){if(ai.group){if(j.expandedgroups[ai.uniqueid]){return j.expandedgroups[ai.uniqueid].expanded}}else{return false}};var af=1;var Y=true;while(Y&&af<300){Y=false;if(U=="down"){ac=j.getdisplayrows()[ad+af]}else{if(U=="up"){ac=j.getdisplayrows()[ad-af]}}if(!ac){break}if(ac&&ac.group){Y=true}if(ac&&ac.totalsrow){Y=true}var ag=ac.parentItem;while(ag){if(ag&&!X(ag)){Y=true}ag=ag.parentItem}if(!Y){break}af++}if(af==300){ac=null}if(j.pageable){var ae=false;if(ac){for(var ab=0;ab<j.dataview.rows.length;ab++){if(j.dataview.rows[ab].boundindex==ac.boundindex){ae=true}}if(!ae){ac=null}}}}else{if(j.pageable){var ae=false;if(ac){for(var ab=0;ab<j.dataview.rows.length;ab++){if(j.dataview.rows[ab].boundindex==ac.boundindex){ae=true}}if(!ae){if(j.pagerpageinput&&P.keyCode===9){if(ac.boundindex>j.dataview.rows[j.dataview.rows.length-1].boundindex){j.pagerpageinput.focus();P.preventDefault()}}j.ensurerowvisible(ac)}}}}if(ac!=undefined&&aa!=null&&!ac.totalsrow){if(V||V==undefined){j.clearselection()}var Z=j.getboundindex(ac);j.selectcell(Z,aa);j._oldselectedcell=j.selectedcell;f=true;if(j.groupable){j.ensurecellvisible(Z,aa)}else{j.ensurecellvisible(ah,aa)}return true}return false};if(!i(W,Q)&&!j.groupable){j.ensurecellvisible(W,Q);i(W,Q);if(j.virtualmode){j.host.focus()}}var S=j.groupable&&j.groups.length>0;if(!S){if(P.shiftKey&&P.keyCode!=9){if(j.selectionmode=="multiplecellsextended"||j.selectionmode=="multiplecellsadvanced"){if(j._lastClickedCell){j._selectpath(W,Q);var T=j.dataview.loadedrecords[W];var R=j.getboundindex(T);j.selectedcell={rowindex:R,datafield:Q};return}}}else{if(!P.shiftKey){j._lastClickedCell={row:W,column:Q}}}}};var w=P.shiftKey&&j.selectionmode!="singlecell"&&j.selectionmode!="multiplecells";var k=function(){if(j.pageable){var i=j.dataview.pagenum*j.dataview.pagesize;z(i,E,!w)}else{z(0,E,!w)}};var s=function(){var Q=q.rowscount-1;if(j.pageable){var i=j.dataview.pagenum*j.dataview.pagesize;Q=i+j.dataview.rows.length-1}z(Q,E,!w)};var K=t==9&&!P.shiftKey;var b=t==9&&P.shiftKey;if(j.rtl){var y=K;K=b;b=y}if(K||b){w=false}if(K||b){if(document.activeElement&&document.activeElement.className&&document.activeElement.className.indexOf("jqx-grid-cell-add-new-row")>=0){return true}}var I=P.ctrlKey||P.metaKey;if(I&&t==37){var H=j._getfirstvisiblecolumn(e);if(H!=null){z(l,H.datafield)}}else{if(I&&t==39){var d=j._getlastvisiblecolumn(e);if(d!=null){z(l,d.datafield)}}else{if(t==39||K){var r=j._getnextvisiblecolumn(e);if(r!=null){z(l,r.datafield,!w)}else{if(!K){f=true}else{var u=j._getfirstvisiblecolumn();t=40;E=u.displayfield}}}else{if(t==37||b){var H=j._getprevvisiblecolumn(e);if(H!=null){z(l,H.datafield,!w)}else{if(!b){f=true}else{var n=j._getlastvisiblecolumn();t=38;E=n.displayfield}}}else{if(t==36){k()}else{if(t==35){s()}else{if(t==33){if(l-A>=0){var L=l-A;z(L,E,!w);if(j.pageable&&j.virtualmode){j.gotoprevpage();setTimeout(function(){z(L,E,!w)},25)}}else{k()}}else{if(t==34){if(q.rowscount>l+A){var L=l+A;z(L,E,!w);if(j.pageable&&j.virtualmode){j.gotonextpage();setTimeout(function(){z(L,E,!w)},25)}}else{s()}}}}}}}}}if(t==38){if(I){k()}else{if(l>0){z(l-1,E,!w,"up")}else{f=false}}}if(t==40){if(I){s()}else{if((q.rowscount>l+1)||(j.groupable&&j.groups.length>0)){z(l+1,E,!w,"down")}else{f=true}}}}break;case"singlerow":case"multiplerows":case"multiplerowsextended":case"multiplerowsadvanced":var l=j.getselectedrowindex();if(l==null||l==-1){return true}l=this.getrowvisibleindex(l);var g=function(Q,T,S){var i=function(ab){var aa=j.dataview.loadedrecords[ab];if(j.groupable&&j.groups.length>0){if(S=="up"){ab++}if(S=="down"){ab--}var aa=j.getdisplayrows()[ab];var U=function(af){if(af.group){if(j.expandedgroups[af.uniqueid]){return j.expandedgroups[af.uniqueid].expanded}}else{return false}};var ad=1;var V=true;while(V&&ad<300){V=false;if(S=="down"){aa=j.getdisplayrows()[ab+ad]}else{if(S=="up"){aa=j.getdisplayrows()[ab-ad]}}if(!aa){break}if(aa&&aa.group){V=true}if(aa&&aa.totalsrow){V=true}var ae=aa.parentItem;while(ae){if(ae&&!U(ae)){V=true}ae=ae.parentItem}if(!V){break}ad++}if(ad==300){aa=null}if(j.pageable){var ac=false;if(aa){for(var Z=0;Z<j.dataview.rows.length;Z++){if(j.dataview.rows[Z].boundindex==aa.boundindex){ac=true}}if(!ac){aa=null}}}}if(aa!=undefined){var W=j.getboundindex(aa);var Y=j.selectedrowindex;if(T||T==undefined){j.clearselection()}j.selectedrowindex=Y;j.selectrow(W,false);if(j.groupable&&j.groups.length>0){var X=j.ensurerowvisible(W)}else{var X=j.ensurerowvisible(ab)}if(!X||j.autoheight||j.groupable){j._rendervisualrows()}f=true;return true}return false};if(!i(Q)&&!j.groupable){j.ensurerowvisible(Q);i(Q,T);if(j.virtualmode){setTimeout(function(){i(Q,T)},25)}if(j.virtualmode){j.host.focus()}}var R=j.groupable&&j.groups.length>0;if(!R){if(P.shiftKey&&t!=9){if(j.selectionmode=="multiplerowsextended"){if(j._lastClickedCell){j._selectrowpath(Q);j.selectedrowindex=j.getrowboundindex(Q);return}}}else{if(!P.shiftKey){j._lastClickedCell={row:Q};j.selectedrowindex=j.getrowboundindex(Q)}}}};var w=P.shiftKey&&j.selectionmode!="singlerow"&&j.selectionmode!="multiplerows";var k=function(){if(j.pageable){var i=j.dataview.pagenum*j.dataview.pagesize;g(i,!w)}else{g(0,!w)}};var s=function(){var Q=q.rowscount-1;if(j.pageable){var i=j.dataview.pagenum*j.dataview.pagesize;Q=i+j.dataview.rows.length-1}g(Q,!w)};var I=P.ctrlKey||P.metaKey;if(t==36||(I&&t==38)){k()}else{if(t==35||(I&&t==40)){s()}else{if(t==33){if(l-A>=0){var L=l-A;g(L,!w,"up");if(j.pageable&&j.virtualmode){j.gotoprevpage();setTimeout(function(){g(L,!w)},25)}}else{k()}}else{if(t==34){if(q.rowscount>l+A){var L=l+A;g(L,!w,"down");if(j.pageable&&j.virtualmode){j.gotonextpage();setTimeout(function(){g(L,!w)},25)}}else{s()}}else{if(t==38){if(l>0){g(l-1,!w,"up")}else{f=true}}else{if(t==40){if((q.rowscount>l+1)||(j.groupable&&j.groups.length>0)){g(l+1,!w,"down")}else{f=true}}}}}}}break}if(f){if(j.autosavestate){if(j.savestate){j.savestate()}}return false}return true},_handlemousemove:function(v,p){if(p.vScrollInstance.isScrolling()){return}if(p.hScrollInstance.isScrolling()){return}var z;var q;var f;var n;var m;if(p.enablehover||p.selectionmode=="multiplerows"){z=this.showheader?this.columnsheader.height()+2:0;q=this._groupsheader()?this.groupsheader.height():0;var B=this.showtoolbar?this.toolbarheight:0;var r=this.showfilterbar?this.toolbarheight:0;q+=B;q+=r;f=this.host.coord();if(this.hasTransform){f=a.jqx.utilities.getOffset(this.host);var k=this._getBodyOffset();f.left-=k.left;f.top-=k.top}n=v.pageX-f.left;m=v.pageY-z-f.top-q}if(p.selectionmode=="multiplerowsextended"||p.selectionmode=="multiplecellsextended"||p.selectionmode=="multiplecellsadvanced"){if(p.mousecaptured==true){return}}if(p.enablehover){if(p.disabled){return}if(this.vScrollInstance.isScrolling()||this.hScrollInstance.isScrolling()){return}var c=this._hittestrow(n,m);if(!c){return}var h=c.row;var j=c.index;if(this.hoveredrow!=-1&&j!=-1&&this.hoveredrow==j&&this.selectionmode.indexOf("cell")==-1&&this.selectionmode!="checkbox"){return}this._clearhoverstyle();if(j==-1||h==undefined){return}var s=this.hittestinfo[j].visualrow;if(s==null){return}if(this.hittestinfo[j].details){return}if(v.clientX>a(s).width()+a(s).coord().left){return}var C=0;var o=s.cells.length;if(p.rowdetails&&p.showrowdetailscolumn){if(!this.rtl){C=1+this.groups.length}else{o-=1;o-=this.groups.length}}else{if(this.groupable){if(!this.rtl){C=this.groups.length}else{o-=this.groups.length}}}if(s.cells.length==0){return}var l=s.cells[C].className;if(h.group||(this.selectionmode.indexOf("row")>=0&&l.indexOf("jqx-grid-cell-selected")!=-1)){return}this.hoveredrow=j;if(this.selectionmode.indexOf("cell")!=-1||this.selectionmode=="checkbox"){var e=-1;var t=this.hScrollInstance;var u=t.value;if(this.rtl){if(this.hScrollBar.css("visibility")!="hidden"){u=t.max-t.value}}for(var w=C;w<o;w++){var g=parseInt(this.columnsrow[0].cells[w].style.left)-u;if(this.columns.records[w].pinned&&!this.rtl){g=parseInt(this.columnsrow[0].cells[w].style.left)}var A=g+this.columns.records[w].width;if(A>=n&&n>=g){e=w;break}}if(e!=-1){var b=s.cells[e];if(this.cellhover){this.cellhover(b,v.pageX,v.pageY)}if(b.className.indexOf("jqx-grid-cell-selected")==-1){if(this.editcell){var d=this._getcolumnat(e);if(d){if(this.editcell.row==j&&this.editcell.column==d.datafield){return}}}}a(b).addClass(this.toTP("jqx-grid-cell-hover"));a(b).addClass(this.toTP("jqx-fill-state-hover"));var d=this._getcolumnat(e);d._applyCellStyle(b)}return}for(var w=C;w<o;w++){var b=s.cells[w];a(b).addClass(this.toTP("jqx-grid-cell-hover"));a(b).addClass(this.toTP("jqx-fill-state-hover"));if(this.cellhover){this.cellhover(b,v.pageX,v.pageY)}var d=this._getcolumnat(w);d._applyCellStyle(b)}}else{return true}}})})(jqxBaseFramework)})();

