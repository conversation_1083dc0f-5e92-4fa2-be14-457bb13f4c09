/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(d){var c=(function(){var e={},w,s,l,n,i,j,q,r;function f(E,D,z,C,A,x,y){var B=this;if(!B){B=window.jqx}B.hierarchy=A;B.exportFormat=x;B.filename=y;E.beginFile(y);p(E);m(E);E.endFile(y);return E.getFile()}function p(B){var z=true;d.each(s,function(){if(this.hidden){z=false;return false}});B.beginHeader(z);var y=0;for(var x in s){if(s[x].columnsDataFields){x=s[x].columnsDataFields[y].displayfield}var A=o(x,s[x]);<PERSON>.appendHeaderCell(s[x],x,A,z,y);y++}B.endHeader(z)}function m(A){var z=this;if(!z){z=window.jqx}A.beginBody();if(z.hierarchy){var y=function(C){for(var B=0;B<C.length;B+=1){if(C[B]!==undefined){A.hierarchy=true;A.beginRow(C[B].level);g(A,C[B],B,true);if(C[B].records){A.beginRows(C[B].level);y(C[B].records);A.endRows(C[B].level)}A.endRow(C[B].level)}}};y(w);A.endBody();return}for(var x=0;x<w.length;x+=1){if(w[x]!==undefined){g(A,w[x],x)}}A.endBody()}function g(z,C,A,F){var E=this;if(!E){E=window.jqx}var y;if(F!=true){z.beginRow()}var D=0;for(var B in s){if(s[B].columnsDataFields){B=s[B].columnsDataFields[D].displayfield}y=u(A,B);if(y){if(y.level!=undefined){if(y.index-1>C.level&&y.index-1<y.maxLevel){D++;continue}}if(y.maxLevel!=undefined){if(y.index-1==y.maxLevel){y=d.extend({},y);y.merge=y.maxLevel-C.level-1}}}if(C.level!=undefined&&C.label!=undefined){if(E.exportFormat==="xml"||E.exportFormat==="json"){var x={};x.text="group";z.appendBodyCell(C.label,x,y,C,D,"group");break}}if(C.hasOwnProperty(B)){z.appendBodyCell(C[B],s[B],y,C,D)}else{z.appendBodyCell("",s[B],y,C,D)}D++}if(F!=true){z.endRow()}}function o(y,z){if(z.style){return l[z.style]}var x=v();if(x.length>0){return x[0].style}return null}function v(){if(!i){i=new Array();d.each(l,function(x,y){i[i.length]={name:x,style:y}})}return i}function u(C,B){var D=s[B];if(D){if(D.customCellStyles){var z=D.customCellStyles[C];if(z){return l[z]}}if(D.cellStyle){if(D.cellAltStyle){var y=C%2;if(y==0){return l[D.cellStyle]}return l[D.cellAltStyle]}return l[D.cellStyle]}else{var x=v();if(x.length>0){var y=C%(x.length-1);var A=x[y+1].style;return A}}}return null}function t(A,y,z){var x=document.createElement("input");x.name=y;x.value=A;x.type="hidden";z.appendChild(x);return x}function h(z,x,y){var A=document.createElement("textarea");A.name=x;A.value=z;y.appendChild(A);return A}function k(y,B,A,x,C){var z=document.createElement("form");t(y,"filename",z);t(B,"format",z);h(A,"content",z);if(x==undefined||x==""){if(window&&window.location.toString().indexOf("jqwidgets.com")>=0){x="https://jqwidgets.com/export_server/dataexport.php"}else{x="http://jquerygrid.net/export_server/dataexport.php"}}z.action=x;z.method="post";if(C){z.acceptCharset=C}document.body.appendChild(z);return z}n=function(C,A,z,y,B,x){if(!(this instanceof c)){return new c(C,A,z,B,x)}w=C;s=A;l=z;this.exportTo=function(M,J,I,D){M=M.toString().toLowerCase();var F=e[M];if(typeof F==="undefined"){throw"You can't export to "+M+" format."}if(M==="pdf"&&D==undefined){var O=this.exportTo(M,J,M,"pdf");if(!d.jqx.pdfExport){d.jqx.pdfExport={orientation:"portrait",paperSize:"a4"}}var N=new b(d.jqx.pdfExport.orientation,"pt",d.jqx.pdfExport.paperSize);N.cellInitialize();var L=d(O).find("th");var K=d(O).find("tr");var P=0;N.setFontSize(13*72/96);var H=595;switch(d.jqx.pdfExport.paperSize){case"legal":var H=612;if(d.jqx.pdfExport.orientation!=="portrait"){H=1008}break;case"letter":var H=612;if(d.jqx.pdfExport.orientation!=="portrait"){H=792}break;case"a3":var H=841;if(d.jqx.pdfExport.orientation!=="portrait"){H=1190}break;case"a4":var H=595;if(d.jqx.pdfExport.orientation!=="portrait"){H=842}break;case"a5":var H=420;if(d.jqx.pdfExport.orientation!=="portrait"){H=595}break}H-=20;var G=0;var E=[];d.each(L,function(Q){var R=parseInt(this.style.width);if(isNaN(R)){R=25}var S=R*72/96;E[Q]=S;G+=S});if(L.length===0){d.each(K[0].cells,function(Q){var R=parseInt(this.style.width);if(isNaN(R)){R=H/K[0].cells.length}var S=R*72/96;E[Q]=S;G+=S})}if(G>H){d.each(E,function(Q){E[Q]=(E[Q]/G)*100;E[Q]=E[Q]*H/100})}d.each(L,function(R){var V=E[R];var U=25*72/96;var T=N.getTextDimensions(d(this).html());var S=d(this).html();if(T.w+3>V){var Q=N.splitTextToSize(S,V-3);var W=Q[0];if(W.length>3){S=W.substring(0,W.length-3)+"..."}else{S=W.substring(0,1)+"..."}var Q=N.splitTextToSize(S,V-3);var W=Q[0];if(W!=S){S=W}}N.cell(10,10,V,U,S,P)});P++;d.each(K,function(X){if(X===0){return true}var R=d(this).children();var S=R.length>L.length&&L.length>0;if(S){var aa=R.length-L.length;var ab="";var Z=E[0];var W=25*72/96;for(var T=0;T<=aa;T++){var Q=R[T].innerHTML;if(Q==="+"||Q==="-"){Q=Q+" "}if(Q==="&nbsp;"){Q="   "}ab+=Q}var V=N.getTextDimensions(ab);if(V.w+3>Z){var Y=N.splitTextToSize(ab,Z-3);var U=Y[0];if(U.length>3){ab=U.substring(0,U.length-3)+"..."}else{ab=U.substring(0,1)+"..."}var Y=N.splitTextToSize(ab,Z-3);var U=Y[0];if(U!=ab){ab=U}}N.cell(10,10,Z,W,ab,P);for(var T=aa+1;T<R.length;T++){var X=T-aa;var Z=E[X];var W=25*72/96;var ab=d(R[T]).html();var V=N.getTextDimensions(d(R[T]).html());if(V.w+3>Z){var Y=N.splitTextToSize(ab,Z-3);var U=Y[0];if(U.length>3){ab=U.substring(0,U.length-3)+"..."}else{ab=U.substring(0,1)+"..."}var Y=N.splitTextToSize(ab,Z-3);var U=Y[0];if(U!=ab){ab=U}}N.cell(10,10,Z,W,ab,P)}P++;return true}d.each(R,function(ad){var ah=E[ad];var ag=25*72/96;var af=d(this).html();var ae=N.getTextDimensions(d(this).html());if(ae.w+3>ah){var ac=N.splitTextToSize(af,ah-3);var ai=ac[0];if(ai.length>3){af=ai.substring(0,ai.length-3)+"..."}else{af=ai.substring(0,1)+"..."}var ac=N.splitTextToSize(af,ah-3);var ai=ac[0];if(ai!=af){af=ai}}N.cell(10,10,ah,ag,af,P)});P++});if(d.jqx.browser.msie&&d.jqx.browser.version<10){throw new Error("PDF export requires a browser with HTML5 support");return}return N}return f(F,w,s,l,J,I,D)};this.exportToFile=function(N,D,Q,H,K){if(N==="pdf"){var P=this.exportTo(N,K,N,D);if(!d.jqx.pdfExport){d.jqx.pdfExport={orientation:"portrait",paperSize:"a4"}}var O=new b(d.jqx.pdfExport.orientation,"pt",d.jqx.pdfExport.paperSize);if(H=="utf-8"||H=="UTF-8"){O.setFont("courier","normal")}O.cellInitialize();var M=d(P).find("th");var L=d(P).find("tr");var R=0;O.setFontSize(13*72/96);var I=595;switch(d.jqx.pdfExport.paperSize){case"legal":var I=612;if(d.jqx.pdfExport.orientation!=="portrait"){I=1008}break;case"letter":var I=612;if(d.jqx.pdfExport.orientation!=="portrait"){I=792}break;case"a3":var I=841;if(d.jqx.pdfExport.orientation!=="portrait"){I=1190}break;case"a4":var I=595;if(d.jqx.pdfExport.orientation!=="portrait"){I=842}break;case"a5":var I=420;if(d.jqx.pdfExport.orientation!=="portrait"){I=595}break}I-=20;var G=0;var E=[];d.each(M,function(S){var T=parseInt(this.style.width);if(isNaN(T)){T=25}var U=T*72/96;E[S]=U;G+=U});if(M.length===0){d.each(L[0].cells,function(S){var T=parseInt(this.style.width);if(isNaN(T)){T=I/L[0].cells.length}var U=T*72/96;E[S]=U;G+=U})}if(G>I){d.each(E,function(S){E[S]=(E[S]/G)*100;E[S]=E[S]*I/100})}d.each(M,function(T){var X=E[T];var W=25*72/96;var V=O.getTextDimensions(d(this).html());var U=d(this).html();if(V.w+3>X){var S=O.splitTextToSize(U,X-3);var Y=S[0];if(Y.length>3){U=Y.substring(0,Y.length-3)+"..."}else{U=Y.substring(0,1)+"..."}var S=O.splitTextToSize(U,X-3);var Y=S[0];if(Y!=U){U=Y}}O.cell(10,10,X,W,U,R)});R++;d.each(L,function(Z){if(Z===0){return true}var T=d(this).children();var U=T.length>M.length&&M.length>0;if(U){var ac=T.length-M.length;var ad="";var ab=E[0];var Y=25*72/96;for(var V=0;V<=ac;V++){var S=T[V].innerHTML;if(S==="+"||S==="-"){S=S+" "}if(S==="&nbsp;"){S="   "}ad+=S}var X=O.getTextDimensions(ad);if(X.w+3>ab){var aa=O.splitTextToSize(ad,ab-3);var W=aa[0];if(W.length>3){ad=W.substring(0,W.length-3)+"..."}else{ad=W.substring(0,1)+"..."}var aa=O.splitTextToSize(ad,ab-3);var W=aa[0];if(W!=ad){ad=W}}O.cell(10,10,ab,Y,ad,R);for(var V=ac+1;V<T.length;V++){var Z=V-ac;var ab=E[Z];var Y=25*72/96;var ad=d(T[V]).html();if(ad==="&nbsp;"){ad="   "}var X=O.getTextDimensions(d(T[V]).html());if(X.w+3>ab){var aa=O.splitTextToSize(ad,ab-3);var W=aa[0];if(W.length>3){ad=W.substring(0,W.length-3)+"..."}else{ad=W.substring(0,1)+"..."}var aa=O.splitTextToSize(ad,ab-3);var W=aa[0];if(W!=ad){ad=W}}O.cell(10,10,ab,Y,ad,R)}R++;return true}d.each(T,function(af){var aj=E[af];var ai=25*72/96;var ah=d(this).html();if(ah==="&nbsp;"){ah="   "}var ag=O.getTextDimensions(d(this).html());if(ag.w+3>aj){var ae=O.splitTextToSize(ah,aj-3);var ak=ae[0];if(ak.length>3){ah=ak.substring(0,ak.length-3)+"..."}else{ah=ak.substring(0,1)+"..."}var ae=O.splitTextToSize(ah,aj-3);var ak=ae[0];if(ak!=ah){ah=ak}}O.cell(10,10,aj,ai,ah,R)});R++});if(d.jqx.browser.msie&&d.jqx.browser.version<10){throw new Error("PDF export requires a browser with HTML5 support");return}O.save(D+".pdf");return}var J=this.exportTo(N,K,N,D),F=k(D,N,J,Q,H);F.submit();document.body.removeChild(F)};this.exportToLocalFile=function(H,E,F,D){var G=this.exportTo(H,F,D);document.location.href="data:application/octet-stream;filename="+E+","+encodeURIComponent(G)}};n.extend=function(x,y){if(y instanceof d.jqx.dataAdapter.DataExportModuleBase){e[x]=y}else{throw"The module "+x+" is not instance of DataExportModuleBase."}};return n}());d.jqx.dataAdapter.ArrayExporter=c})(jqxBaseFramework);(function(d){var c=function(){this.formatData=function(h,g,e,j){if(g==="date"){var f="";if(typeof h==="string"){f=d.jqx.dataFormat.tryparsedate(h);h=f}if(h===""||h===null){return""}f=d.jqx.dataFormat.formatdate(h,e,j);if((f&&f.toString()=="NaN")||f==null){return""}h=f}else{if(g==="number"||g==="float"||g==="int"||g=="integer"){if(h===""||h===null){return""}if(!isNaN(new Number(h))){var i=d.jqx.dataFormat.formatnumber(h,e,j);if(i.toString()=="NaN"){return""}else{h=i}}}else{h=h}}if(h===null){return""}return h};this.getFormat=function(h){var e=h?h.formatString:"";var g=h?h.localization:"";var f="string";f=h?h.type:"string";if(f=="number"||f=="float"){if(!e){e="f2"}}if(f=="int"||f=="integer"){if(!e){e="n0"}}if(f=="date"){if(!e){e="d"}}return{type:f,formatString:e,localization:g}};this.beginFile=function(){throw"Not implemented!"};this.beginHeader=function(){throw"Not implemented!"};this.appendHeaderCell=function(){throw"Not implemented!"};this.endHeader=function(){throw"Not implemented!"};this.beginBody=function(){throw"Not implemented!"};this.beginRow=function(){throw"Not implemented!"};this.beginRows=function(){throw"Not implemented!"};this.endRows=function(){throw"Not implemented!"};this.appendBodyCell=function(){throw"Not implemented!"};this.endRow=function(){throw"Not implemented!"};this.endBody=function(){throw"Not implemented!"};this.endFile=function(){throw"Not implemented!"};this.getFile=function(){throw"Not implemented!"}};d.jqx.dataAdapter.DataExportModuleBase=c})(jqxBaseFramework);(function(f){var e=function(l){var g,j,i;var n=0;var k=this;this.beginFile=function(){g=""};this.beginHeader=function(){};this.appendHeaderCell=function(s,t,r,o,p){if(r){if(r.level!=undefined){if(p<r.maxLevel){return}else{if(p===r.maxLevel){if(o){m(s.text)}for(var q=0;q<r.maxLevel;q++){m("")}return}}}}i=o;if(o){m(s.text)}};this.endHeader=function(){this.endRow()};this.beginBody=function(){n=0};this.beginRow=function(){if((n>0)||(n==0&&i)){g+="\n"}n++};this.appendBodyCell=function(s,o,r,t,p){if(r){if(r.maxLevel!=undefined){if(p===r.maxLevel){m(s,o);for(var q=0;q<r.maxLevel-t.level-1;q++){m("",o)}return}}}m(s,o)};this.endRow=function(){g=g.substring(0,g.length-1)};this.endBody=function(){};this.endFile=function(){};this.getFile=function(){return g};function h(o,q){if(q){var p=k.getFormat(q);o=k.formatData(o,p.type,p.formatString,p.localization)}o='"'+o+'"';return o}function m(o,p){o=h(o,p);g+=o+l}};e.prototype=new f.jqx.dataAdapter.DataExportModuleBase();var c=function(){};c.prototype=new e(",");var d=function(){};d.prototype=new e("\t");f.jqx.dataAdapter.ArrayExporter.extend("csv",new c());f.jqx.dataAdapter.ArrayExporter.extend("tsv",new d())})(jqxBaseFramework);(function(f){var c=function(){var k=false;var i;var j;var l=0;this.setPDF=function(){k=true};this.beginFile=function(m){if(k||m==undefined){i='<table style="empty-cells: show;" cellspacing="0" cellpadding="2">'}else{i='<html>\n\t<head>\n\t\t<title></title>\n\t\t<meta http-equiv=Content-type content="text/html; charset=UTF-8">\n\t</head>\n\t<body>\n\t\t<table style="empty-cells: show;" cellspacing="0" cellpadding="2">'}};this.beginHeader=function(){if(k){i+="\n\t<thead><tr>"}else{i+="\n\t\t\t<thead>"}};this.appendHeaderCell=function(o,p,n,m){j=m;if(!m){return}if(k){i+='\n\t\t\t\t<th style="'+h(n)+'">'+o.text+"</th>"}else{if(n.disabled){return}if(n.merge){if(o.width){i+="\n\t\t\t\t<th colspan="+(1+n.merge)+' style="width: '+o.width+"px; "+h(n)+'">'+o.text+"</th>"}else{i+="\n\t\t\t\t<th colspan="+(1+n.merge)+' style="'+h(n)+'">'+o.text+"</th>"}}else{if(o.width){i+='\n\t\t\t\t<th style="width: '+o.width+"px; "+h(n)+'">'+o.text+"</th>"}else{i+='\n\t\t\t\t<th style="'+h(n)+'">'+o.text+"</th>"}}}};this.endHeader=function(){if(k){i+="\n\t</tr></thead>"}else{i+="\n\t\t\t</thead>"}};this.beginBody=function(){if(k){i+="\n\t<tbody>"}else{i+="\n\t\t\t<tbody>"}l=0};this.beginRow=function(){if(k){i+="\n\t<tr>"}else{i+="\n\t\t\t\t<tr>"}l++};this.appendBodyCell=function(n,p,m){var o=this.getFormat(p);if(n===""){n="&nbsp;"}if(k){if(l==1&&!j){i+='\n\t\t\t\t\t<td style="'+h(m)+' border-top-width: 1px;">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}else{i+='\n\t\t\t\t\t<td style="'+h(m)+'">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}}else{if(m.merge){if(l==1&&!j){i+="\n\t\t\t\t\t<td colspan="+(1+m.merge)+' style="'+h(m)+' border-top-width: 1px;">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}else{i+="\n\t\t\t\t\t<td colspan="+(1+m.merge)+' style="'+h(m)+'">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}}else{if(l==1&&!j){i+='\n\t\t\t\t\t<td style="'+h(m)+' border-top-width: 1px;">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}else{i+='\n\t\t\t\t\t<td style="'+h(m)+'">'+this.formatData(n,o.type,o.formatString,o.localization)+"</td>"}}}};this.endRow=function(){if(k){i+="\n\t</tr>"}else{i+="\n\t\t\t\t</tr>"}};this.endBody=function(){if(k){i+="\n\t</tbody>"}else{i+="\n\t\t\t</tbody>"}};this.endFile=function(m){if(k||m==undefined){i+="\n</table>"}else{i+="\n\t\t</table>\n\t</body>\n</html>\n"}};this.getFile=function(){return i};function h(o){var m="";for(var n in o){if(o.hasOwnProperty(n)){if(k&&n=="font-size"){o[n]="100%"}m+=n+":"+o[n]+";"}}return m}};c.prototype=new f.jqx.dataAdapter.DataExportModuleBase();var g=function(){};g.prototype=new c();var e=function(){};e.prototype=new c();var d=new e();f.jqx.dataAdapter.ArrayExporter.extend("html",new g());f.jqx.dataAdapter.ArrayExporter.extend("pdf",d)})(jqxBaseFramework);(function(d){var c=function(){var j,n,f,k,e,l,o={style:"",stylesMap:{font:{color:"Color","font-family":"FontName","font-style":"Italic","font-weight":"Bold"},interior:{"background-color":"Color",background:"Color"},alignment:{left:"Left",center:"Center",right:"Right"}},startStyle:function(r){this.style+='\n\t\t<Style ss:ID="'+r+'" ss:Name="'+r+'">'},buildAlignment:function(s){if(s["text-align"]){var t=this.stylesMap.alignment[s["text-align"]];if(!t){t="Left"}var r='\n\t\t\t<Alignment ss:Vertical="Bottom" ss:Horizontal="'+t+'"/>';this.style+=r}},buildBorder:function(u){if(u["border-color"]){var t="\n\t\t\t<Borders>";var w='\n\t\t\t\t<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="'+u["border-color"]+'"/>';var r='\n\t\t\t\t<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="'+u["border-color"]+'"/>';var s='\n\t\t\t\t<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="'+u["border-color"]+'"/>';var v='\n\t\t\t\t<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="'+u["border-color"]+'"/>';t+=w;t+=r;t+=s;t+=v;t+="\n\t\t\t</Borders>";this.style+=t}},buildFont:function(s){var t=this.stylesMap.font,r="\n\t\t\t<Font ";for(var u in t){if(typeof s[u]!=="undefined"){if(u==="font-style"&&s[u].toString().toLowerCase()==="italic"){r+='ss:Italic="1" '}else{if(u==="font-weight"&&s[u].toString().toLowerCase()==="bold"){r+='ss:Bold="1" '}else{if(u==="color"){r+="ss:"+t[u]+'="'+s[u]+'" '}}}}}r+="/>";this.style+=r},buildInterior:function(s){var t=this.stylesMap.interior,v="\n\t\t\t<Interior ";var r=false;for(var u in t){if(typeof s[u]!=="undefined"){v+="ss:"+t[u]+'="'+s[u]+'" ';r=true}}if(r){v+='ss:Pattern="Solid"'}v+="/>";this.style+=v},buildFormat:function(s){if(s.dataType=="number"||s.dataType=="float"||s.dataType=="int"||s.dataType=="integer"){var r=s.formatString;if(r==""||r.indexOf("n")!=-1||r.indexOf("N")!=-1){this.style+='\n\t\t\t<NumberFormat ss:Format="0"/>'}else{if(r=="f"||r=="F"||r=="D"||r.indexOf("d")!=-1){this.style+='\n\t\t\t<NumberFormat ss:Format="#,##0.00_);[Red](#,##0.00)"/>'}else{if(r.indexOf("p")!=-1||r.indexOf("P")!=-1){this.style+='\n\t\t\t<NumberFormat ss:Format="Percent"/>'}else{if(r.indexOf("c")!=-1||r.indexOf("C")!=-1){if(s.currencysymbol&&parseInt(s.currencysymbol.charCodeAt(0))==8364){this.style+='\n\t\t\t<NumberFormat ss:Format="Euro Currency"/>'}else{this.style+='\n\t\t\t<NumberFormat ss:Format="Currency"/>'}}}}}}else{if(s.dataType=="date"){this.style+='\n\t\t\t<NumberFormat ss:Format="Short Date"/>'}}},closeStyle:function(){this.style+="\n\t\t</Style>"},toString:function(){var r=this.style;this.style="";return r}};this.beginFile=function(){e={};l=0;j='<?xml version="1.0"?>\n\t<?mso-application progid="Excel.Sheet"?> \n\t<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" \n\txmlns:o="urn:schemas-microsoft-com:office:office" \n\txmlns:x="urn:schemas-microsoft-com:office:excel" \n\txmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" \n\txmlns:html="http://www.w3.org/TR/REC-html40"> \n\t<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"> \n\t<Version>12.00</Version> \n\t</DocumentProperties> \n\t<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"> \n\t<WindowHeight>8130</WindowHeight> \n\t<WindowWidth>15135</WindowWidth> \n\t<WindowTopX>120</WindowTopX> \n\t<WindowTopY>45</WindowTopY> \n\t<ProtectStructure>False</ProtectStructure> \n\t<ProtectWindows>False</ProtectWindows> \n\t</ExcelWorkbook> \n\t<Styles>'};this.beginHeader=function(){n='\n\t<Worksheet ss:Name="Sheet1">\n\t\t<Table>';f=[];k=[]};this.appendHeaderCell=function(t,u,s){var r=t.width!=undefined?t.width:t.text.length*10;n+='\n\t\t\t<Column ss:Width="'+r+'"/>';f.push(t);k.push(s)};this.endHeader=function(r){if(r){this.beginRow();for(var s=0;s<f.length;s+=1){if(k[s].disabled){continue}i.call(this,f[s]["text"],null,k[s])}this.endRow()}};this.beginBody=function(){};this.beginRow=function(s){if(s!=undefined){n+="\n\t\t\t";for(var r=0;r<s;r++){n+="\t"}n+="<Row>";return}n+="\n\t\t\t<Row>"};this.beginRows=function(r){n+="\n\t\t\t\t<Rows>"};this.appendBodyCell=function(t,r,s,u){i.call(this,t,r,s,u)};this.endRow=function(s){if(s!=undefined){n+="\n\t\t\t";for(var r=0;r<s;r++){n+="\t"}n+="</Row>";return}n+="\n\t\t\t</Row>"};this.endRows=function(s){if(s!=undefined){n+="\n\t\t\t";for(var r=0;r<s;r++){n+="\t"}n+="</Rows>";return}};this.endBody=function(){n+="\n\t\t</Table>"};this.endFile=function(){n+="\n\t</Worksheet>\n</Workbook>";j+="\n\t</Styles>"};this.getFile=function(){return j+n};function i(v,y,u,x){var t="String";var w=this.getFormat(y);if(v!=null&&v.toString().substring(0,3)=="_AG"){v=v.toString().substring(3);t="String"}else{if(w.type=="date"){var s=v;v=this.formatData(v,w.type,w.formatString,w.localization);if(v===null||v===""){v="";t="String"}else{v=s.toISOString();t="DateTime"}}if(w.type=="string"){if(v===null||v===undefined){v=""}else{if(v.toString().indexOf("&")>=0){v=v.toString().replace(/&/g,"&amp;")}if(v.toString().indexOf(">")>=0){v=v.toString().replace(/>/g,"&gt;")}if(v.toString().indexOf("<")>=0){v=v.toString().replace(/</g,"&lt;")}if(v.toString().indexOf('"')>=0){v=v.toString().replace(/"/g,"&quot;")}if(v.toString().indexOf("'")>=0){v=v.toString().replace(/'/g,"&apos;")}}}if(u.dataType=="number"||u.dataType=="float"||u.dataType=="int"||u.dataType=="integer"){t="Number";v=parseFloat(v);if(v===null||isNaN(v)||v===""){v="";t="String"}if(v&&t!="String"&&v!=""){if(y&&y.formatString&&y.formatString.indexOf("p")>=0){v=v/100}}u.currencysymbol=y.localization.currencysymbol}}var r=h(u);if(u.merge){n+='\n\t\t\t\t<Cell ss:MergeAcross="'+u.merge+'" ss:StyleID="'+r+'"><Data ss:Type="'+t+'">'+v+"</Data></Cell>"}else{n+='\n\t\t\t\t<Cell ss:StyleID="'+r+'"><Data ss:Type="'+t+'">'+v+"</Data></Cell>"}}function p(){l+=1;return"xls-style-"+l}function m(t){for(var r in e){if(q(t,e[r])&&q(e[r],t)){return r}}return undefined}function q(u,r){var t=true;for(var s in u){if(u[s]!==r[s]){t=false}}return t}function g(s,r){o.startStyle(s);o.buildAlignment(r);o.buildBorder(r);o.buildFont(r);o.buildInterior(r);o.buildFormat(r);o.closeStyle();j+=o.toString()}function h(r){if(!r){return""}var s=m(r);if(typeof s==="undefined"){s=p();e[s]=r;g(s,r)}return s}};c.prototype=new d.jqx.dataAdapter.DataExportModuleBase();d.jqx.dataAdapter.ArrayExporter.extend("xls",new c())})(jqxBaseFramework);(function(d){var c=function(){var g,e,f;this.beginFile=function(){g='<?xml version="1.0" encoding="UTF-8" ?>';g+="\n<table>"};this.beginHeader=function(){e=[]};this.appendHeaderCell=function(h,i){e.push(i)};this.endHeader=function(){};this.beginBody=function(i,h){};this.beginRow=function(k){var j=this;if(!j){j=window.jqx}if(k!=undefined){if(j.hierarchy){g+="\n\t";for(var h=0;h<k;h++){g+="\t\t"}g+="<row>";f=0;return}}g+="\n\t<row>";f=0};this.beginRows=function(j){if(j!=undefined){g+="\n\t\t";for(var h=0;h<j;h++){g+="\t\t"}g+="<rows>";f=0;return}g+="\n\t\t<rows>"};this.appendBodyCell=function(k,p,h,q,l,o){var m=this;if(!m){m=window.jqx}var n=this.getFormat(p);k=this.formatData(k,n.type,n.formatString,n.localization);if(n.type=="string"){if(k.toString().indexOf("&")>=0){k=k.toString().replace(/&/g,"&amp;")}if(k.toString().indexOf(">")>=0){k=k.toString().replace(/>/g,"&gt;")}if(k.toString().indexOf("<")>=0){k=k.toString().replace(/</g,"&lt;")}if(k.toString().indexOf('"')>=0){k=k.toString().replace(/"/g,"&quot;")}if(k.toString().indexOf("'")>=0){k=k.toString().replace(/'/g,"&apos;")}}if(q.level!=undefined){if(m.hierarchy){g+="\n\t\t";for(var j=0;j<q.level;j++){g+="\t\t"}if(o===undefined){g+="<"+e[f]+">"+k+"</"+e[f]+">"}else{g+="<"+o+">"+k+"</"+o+">"}}else{if(o!=undefined){g+="\n\t\t<"+o+">"+k+"</"+o+">"}else{g+="\n\t\t<"+e[f]+">"+k+"</"+e[f]+">"}}}else{g+="\n\t\t<"+e[f]+">"+k+"</"+e[f]+">"}f++};this.endRow=function(k){var j=this;if(!j){j=window.jqx}if(k!=undefined){if(j.hierarchy){g+="\n\t";for(var h=0;h<k;h++){g+="\t\t"}g+="</row>";f=0;return}}g+="\n\t</row>";f=0};this.endRows=function(j){if(j!=undefined){g+="\n\t\t";for(var h=0;h<j;h++){g+="\t\t"}g+="</rows>";f=0;return}g+="\n\t\t</rows>"};this.endBody=function(){};this.endFile=function(){g+="\n</table>"};this.getFile=function(){return g}};c.prototype=new d.jqx.dataAdapter.DataExportModuleBase();d.jqx.dataAdapter.ArrayExporter.extend("xml",new c())})(jqxBaseFramework);(function(f){var l=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,n={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};function c(p){return'"'+p.replace(l,function(q){var r=n[q];return typeof r==="string"?r:"\\u"+("0000"+q.charCodeAt(0).toString(16)).slice(-4)})+'"'}function d(p){return p<10?"0"+p:p}function g(q){var p;if(isFinite(q.valueOf())){p=q.getUTCFullYear()+"-"+d(q.getUTCMonth()+1)+"-"+d(q.getUTCDate())+"T"+d(q.getUTCHours())+":"+d(q.getUTCMinutes())+":"+d(q.getUTCSeconds())+'Z"'}else{p="null"}return p}function i(s){var p=s.length,q=[],r;for(r=0;r<p;r++){q.push(j(r,s)||"null")}return"["+q.join(",")+"]"}function o(s){var q=[],r,p;for(r in s){if(Object.prototype.hasOwnProperty.call(s,r)){p=j(r,s);if(p){q.push(c(r)+":"+p)}}}return"{"+q.join(",")+"}"}function k(p){switch(Object.prototype.toString.call(p)){case"[object Date]":return g(p);case"[object Array]":return i(p)}return o(p)}function m(q,p){switch(p){case"string":return c(q);case"number":case"float":case"integer":case"int":return isFinite(q)?q:"null";case"boolean":return q}return"null"}function j(q,p){var s=p[q],r=typeof s;if(s&&typeof s==="object"&&typeof s.toJSON==="function"){s=s.toJSON(q);r=typeof s}if(/(number|float|int|integer|string|boolean)/.test(r)||(!s&&r==="object")){return m(s,r)}else{return k(s)}}function h(p){if(window.JSON&&typeof window.JSON.stringify==="function"){return window.JSON.stringify(p)}return j("",{"":p})}var e=function(){var s=this;this.prepareData=function(v,x){if(x){var w=s.getFormat(x);v=s.formatData(v,w.type,w.formatString,w.localization)}return v};var p,r,t,q=[],u=0;this.beginFile=function(){r=[]};this.beginHeader=function(){};this.appendHeaderCell=function(v){};this.endHeader=function(){};this.beginBody=function(w,v){};this.beginRow=function(){var v=this;if(!v){v=window.jqx}if(v.hierarchy||window.jqx.hierarchy){q[u]={}}else{t={}}};this.beginRows=function(){q[u].rows=[];u++;q[u]={}};this.endRows=function(){u--};this.appendBodyCell=function(x,v){var w=this;if(!w){w=window.jqx}var y=this.prepareData(x,v);if(w.hierarchy||window.jqx.hierarchy){q[u][v.text]=y}else{t[v.text]=y}};this.endRow=function(){var v=this;if(!v){v=window.jqx}if(v.hierarchy||window.jqx.hierarchy){if(u==0){r.push(q[u])}else{q[u-1].rows.push(q[u])}}else{r.push(t)}};this.endBody=function(){};this.endFile=function(){p=h(r)};this.getFile=function(){return p}};e.prototype=new f.jqx.dataAdapter.DataExportModuleBase();f.jqx.dataAdapter.ArrayExporter.extend("json",new e())})(jqxBaseFramework);var b=window.jqxPdfDataExport=(function(){if(typeof btoa==="undefined"){window.btoa=function(o){var k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",u=k.split(""),j,h,g,t,s,q,p,v,n=0,w=0,m="",l=[],f;do{j=o.charCodeAt(n++);h=o.charCodeAt(n++);g=o.charCodeAt(n++);v=j<<16|h<<8|g;t=v>>18&63;s=v>>12&63;q=v>>6&63;p=v&63;l[w++]=u[t]+u[s]+u[q]+u[p]}while(n<o.length);m=l.join("");f=o.length%3;return(f?m.slice(0,f-3):m)+"===".slice(f||3)}}if(typeof atob==="undefined"){window.atob=function(n){var j="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",h,g,f,r,q,p,o,s,m=0,t=0,k="",l=[];if(!n){return n}n+="";do{r=j.indexOf(n.charAt(m++));q=j.indexOf(n.charAt(m++));p=j.indexOf(n.charAt(m++));o=j.indexOf(n.charAt(m++));s=r<<18|q<<12|p<<6|o;h=s>>16&255;g=s>>8&255;f=s&255;if(p===64){l[t++]=String.fromCharCode(h)}else{if(o===64){l[t++]=String.fromCharCode(h,g)}else{l[t++]=String.fromCharCode(h,g,f)}}}while(m<n.length);k=l.join("");return k}}var e=typeof Object.keys==="function"?function(f){return Object.keys(f).length}:function(f){var g=0,h;for(h in f){if(f.hasOwnProperty(h)){g++}}return g},c=function(f){this.topics={};this.context=f;this.publish=function(k,o){if(this.topics[k]){var m=this.topics[k],q=[],p,j,g,h,n=function(){};o=Array.prototype.slice.call(arguments,1);for(j=0,g=m.length;j<g;j++){h=m[j];p=h[0];if(h[1]){h[0]=n;q.push(j)}p.apply(this.context,o)}for(j=0,g=q.length;j<g;j++){m.splice(q[j],1)}}};this.subscribe=function(g,i,h){if(!this.topics[g]){this.topics[g]=[[i,h]]}else{this.topics[g].push([i,h])}return{topic:g,callback:i}};this.unsubscribe=function(k){if(this.topics[k.topic]){var h=this.topics[k.topic],j,g;for(j=0,g=h.length;j<g;j++){if(h[j][0]===k.callback){h.splice(j,1)}}}}};function d(G,ag,O,X){if(typeof G==="undefined"){G="p"}else{G=G.toString().toLowerCase()}if(typeof ag==="undefined"){ag="mm"}if(typeof O==="undefined"){O="a4"}if(typeof X==="undefined"&&typeof zpipe==="undefined"){X=false}var av=O.toString().toLowerCase(),aq="0.9.0rc2",v=[],H=0,ax=X,W="1.3",P={a3:[841.89,1190.55],a4:[595.28,841.89],a5:[420.94,595.28],letter:[612,792],legal:[612,1008]},af="0 g",J="0 G",j=0,h=[],p=2,x=false,F=[],ak={},S={},al=16,f,B=0.200025,D,E,am,Q={title:"",subject:"",author:"",keywords:"",creator:""},T=0,V=0,R={},I=new c(R),ah,at,r=function(i){return i.toFixed(2)},q=function(i){return i.toFixed(3)},C=function(i){var k=(i).toFixed(0);if(i<10){return"0"+k}else{return k}},t=function(i){var k=(i).toFixed(0);if(k.length<10){return new Array(11-k.length).join("0")+k}else{return k}},ad=function(i){if(x){h[j].push(i)}else{v.push(i);H+=i.length+1}},y=function(){p++;F[p]=H;ad(p+" 0 obj");return p},M=function(i){ad("stream");ad(i);ad("endstream")},ao,U,ar,an,ac=function(){ao=E*am;U=D*am;var aD,aC,k,ay,az,aB,aA;for(aD=1;aD<=j;aD++){y();ad("<</Type /Page");ad("/Parent 1 0 R");ad("/Resources 2 0 R");ad("/Contents "+(p+1)+" 0 R>>");ad("endobj");aC=h[aD].join("\n");y();if(ax){k=[];for(az=0;az<aC.length;++az){k[az]=aC.charCodeAt(az)}aA=adler32cs.from(aC);aB=new Deflater(6);aB.append(new Uint8Array(k));aC=aB.flush();k=[new Uint8Array([120,156]),new Uint8Array(aC),new Uint8Array([aA&255,(aA>>8)&255,(aA>>16)&255,(aA>>24)&255])];aC="";for(az in k){if(k.hasOwnProperty(az)){aC+=String.fromCharCode.apply(null,k[az])}}ad("<</Length "+aC.length+" /Filter [/FlateDecode]>>")}else{ad("<</Length "+aC.length+">>")}M(aC);ad("endobj")}F[1]=H;ad("1 0 obj");ad("<</Type /Pages");ar="/Kids [";for(az=0;az<j;az++){ar+=(3+2*az)+" 0 R "}ad(ar+"]");ad("/Count "+j);ad("/MediaBox [0 0 "+r(ao)+" "+r(U)+"]");ad(">>");ad("endobj")},Z=function(i){i.objectNumber=y();ad("<</BaseFont/"+i.PostScriptName+"/Type/Font");if(typeof i.encoding==="string"){ad("/Encoding/"+i.encoding)}ad("/Subtype/Type1>>");ad("endobj")},L=function(){var i;for(i in ak){if(ak.hasOwnProperty(i)){Z(ak[i])}}},N=function(){I.publish("putXobjectDict")},z=function(){ad("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]");ad("/Font <<");var i;for(i in ak){if(ak.hasOwnProperty(i)){ad("/"+i+" "+ak[i].objectNumber+" 0 R")}}ad(">>");ad("/XObject <<");N();ad(">>")},l=function(){L();I.publish("putResources");F[2]=H;ad("2 0 obj");ad("<<");z();ad(">>");ad("endobj");I.publish("postPutResources")},o=function(ay,k,az){var i;if(S[k]===i){S[k]={}}S[k][az]=ay},aw={},w=function(i,az,aB,ay){var aA="F"+(e(ak)+1).toString(10),k=ak[aA]={id:aA,PostScriptName:i,fontName:az,fontStyle:aB,encoding:ay,metadata:{}};o(aA,az,aB);I.publish("addFont",k);return aA},g=function(){var k="helvetica",aI="times",aK="courier",aH="normal",aG="bold",aF="italic",aJ="bolditalic",az="StandardEncoding",aC=[["Helvetica",k,aH],["Helvetica-Bold",k,aG],["Helvetica-Oblique",k,aF],["Helvetica-BoldOblique",k,aJ],["Courier",aK,aH],["Courier-Bold",aK,aG],["Courier-Oblique",aK,aF],["Courier-BoldOblique",aK,aJ],["Times-Roman",aI,aH],["Times-Bold",aI,aG],["Times-Italic",aI,aF],["Times-BoldItalic",aI,aJ]],aE,aA,aD,aB;for(aE=0,aA=aC.length;aE<aA;aE++){var ay=az;aD=w(aC[aE][0],aC[aE][1],aC[aE][2],ay);aB=aC[aE][0].split("-");o(aD,aB[0],aB[1]||"")}I.publish("addFonts",{fonts:ak,dictionary:S})},u=function(aI,az){var aE,aC,aB,aA,aG,aF,ay,aH,k,aD;if(az===aB){az={}}aA=az.sourceEncoding?aA:"Unicode";aF=az.outputEncoding;if((az.autoencode||aF)&&ak[f].metadata&&ak[f].metadata[aA]&&ak[f].metadata[aA].encoding){aG=ak[f].metadata[aA].encoding;if(!aF&&ak[f].encoding){aF=ak[f].encoding}if(!aF&&aG.codePages){aF=aG.codePages[0]}if(typeof aF==="string"){aF=aG[aF]}if(aF){aH=false;ay=[];for(aE=0,aC=aI.length;aE<aC;aE++){k=aF[aI.charCodeAt(aE)];if(k){ay.push(String.fromCharCode(k))}else{ay.push(aI[aE])}if(ay[aE].charCodeAt(0)>>8){aH=true}}aI=ay.join("")}}aE=aI.length;while(aH===aB&&aE!==0){if(aI.charCodeAt(aE-1)>>8){aH=true}aE--}if(!aH){return aI}else{ay=az.noBOM?[]:[254,255];for(aE=0,aC=aI.length;aE<aC;aE++){k=aI.charCodeAt(aE);aD=k>>8;if(aD>>8){throw new Error("Character at position "+aE.toString(10)+" of string '"+aI+"' exceeds 16bits. Cannot be encoded into UCS-2 BE")}ay.push(aD);ay.push(k-(aD<<8))}return String.fromCharCode.apply(aB,ay)}},ab=function(k,i){return u(k,i).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},aa=function(){ad("/Producer (pdfDataExport "+aq+")");if(Q.title){ad("/Title ("+ab(Q.title)+")")}if(Q.subject){ad("/Subject ("+ab(Q.subject)+")")}if(Q.author){ad("/Author ("+ab(Q.author)+")")}if(Q.keywords){ad("/Keywords ("+ab(Q.keywords)+")")}if(Q.creator){ad("/Creator ("+ab(Q.creator)+")")}var i=new Date();ad("/CreationDate (D:"+[i.getFullYear(),C(i.getMonth()+1),C(i.getDate()),C(i.getHours()),C(i.getMinutes()),C(i.getSeconds())].join("")+")")},Y=function(){ad("/Type /Catalog");ad("/Pages 1 0 R");ad("/OpenAction [3 0 R /FitH null]");ad("/PageLayout /OneColumn");I.publish("putCatalog")},n=function(){ad("/Size "+(p+1));ad("/Root "+p+" 0 R");ad("/Info "+(p-1)+" 0 R")},au=function(){j++;x=true;h[j]=[]},aj=function(){au();ad(r(B*am)+" w");ad(J);if(T!==0){ad(T.toString(10)+" J")}if(V!==0){ad(V.toString(10)+" j")}I.publish("addPage",{pageNumber:j})},A=function(ay,aA){var i,k;if(ay===k){ay=ak[f].fontName}if(aA===k){aA=ak[f].fontStyle}try{i=S[ay][aA]}catch(az){i=k}if(!i){throw new Error("Unable to look up font label for font '"+ay+"', '"+aA+"'. Refer to getFontList() for available fonts.")}return i},s=function(){x=false;v=[];F=[];ad("%PDF-"+W);ac();l();y();ad("<<");aa();ad(">>");ad("endobj");y();ad("<<");Y();ad(">>");ad("endobj");var ay=H,k;ad("xref");ad("0 "+(p+1));ad("0000000000 65535 f ");for(k=1;k<=p;k++){ad(t(F[k])+" 00000 n ")}ad("trailer");ad("<<");n();ad(">>");ad("startxref");ad(ay);ad("%%EOF");x=true;return v.join("\n")},ae=function(i){var k="S";if(i==="F"){k="f"}else{if(i==="FD"||i==="DF"){k="B"}}return k},K=function(aB,ay){var aA,aD,aC,aE,az,k;switch(aB){case aA:return s();case"save":if(navigator.getUserMedia){if(window.URL===undefined){return R.output("dataurlnewwindow")}else{if(window.URL.createObjectURL===undefined){return R.output("dataurlnewwindow")}}}aD=s();aC=aD.length;aE=new Uint8Array(new ArrayBuffer(aC));for(az=0;az<aC;az++){aE[az]=aD.charCodeAt(az)}k=new Blob([aE],{type:"application/pdf"});a(k,ay);break;case"datauristring":case"dataurlstring":return"data:application/pdf;base64,"+btoa(s());case"datauri":case"dataurl":document.location.href="data:application/pdf;base64,"+btoa(s());break;case"dataurlnewwindow":window.open("data:application/pdf;base64,"+btoa(s()));break;default:throw new Error('Output type "'+aB+'" is not supported.')}};if(ag==="pt"){am=1}else{if(ag==="mm"){am=72/25.4}else{if(ag==="cm"){am=72/2.54}else{if(ag==="in"){am=72}else{throw ("Invalid unit: "+ag)}}}}if(P.hasOwnProperty(av)){D=P[av][1]/am;E=P[av][0]/am}else{try{D=O[1];E=O[0]}catch(ap){throw ("Invalid format: "+O)}}if(G==="p"||G==="portrait"){G="p";if(E>D){ah=E;E=D;D=ah}}else{if(G==="l"||G==="landscape"){G="l";if(D>E){ah=E;E=D;D=ah}}else{throw ("Invalid orientation: "+G)}}R.internal={pdfEscape:ab,getStyle:ae,getFont:function(){return ak[A.apply(R,arguments)]},getFontSize:function(){return al},btoa:btoa,write:function(i,az,ay,k){ad(arguments.length===1?i:Array.prototype.join.call(arguments," "))},getCoordinateString:function(i){return r(i*am)},getVerticalCoordinateString:function(i){return r((D-i)*am)},collections:{},newObject:y,putStream:M,events:I,scaleFactor:am,pageSize:{width:E,height:D},output:function(k,i){return K(k,i)}};R.addPage=function(){aj();return this};var ai=["","0","00","000","0000"];var m=function(aC,ay){var az=["FEFF"];for(var aB=0,k=aC.length,aA;aB<k;++aB){aA=aC.charCodeAt(aB).toString(16).toUpperCase();az.push(ai[4-aA.length],aA)}return az.join("")};R.text16=function(aH,aG,aE,ay){var az,aC,aB,aF,k,aD,aA;if(typeof aH==="number"){aC=aE;aB=aH;aF=aG;aH=aC;aG=aB;aE=aF}if(typeof aH==="string"&&aH.match(/[\n\r]/)){aH=aH.split(/\r\n|\r|\n/g)}if(typeof ay==="undefined"){ay={noBOM:true,autoencode:true}}else{if(ay.noBOM===az){ay.noBOM=true}if(ay.autoencode===az){ay.autoencode=true}}ay.autoencode=false;if(typeof aH==="string"){aD=m(aH,ay)}else{if(aH instanceof Array){k=aH.concat();for(aA=k.length-1;aA!==-1;aA--){k[aA]=m(k[aA],ay)}aD=k.join("> Tj\nT* <")}else{throw new Error('Type of text must be string or Array. "'+aH+'" is not recognized.')}}ad("BT\n/"+f+" "+al+" Tf\n"+al+" TL\n"+af+"\n"+r(aG*am)+" "+r((D-aE)*am)+" Td\n<"+aD+"> Tj\nET");return this};R.text=function(aH,aG,aE,ay){var az,aC,aB,aF,k,aD,aA;if(typeof aH==="number"){aC=aE;aB=aH;aF=aG;aH=aC;aG=aB;aE=aF}if(typeof aH==="string"&&aH.match(/[\n\r]/)){aH=aH.split(/\r\n|\r|\n/g)}if(typeof ay==="undefined"){ay={noBOM:true,autoencode:true}}else{if(ay.noBOM===az){ay.noBOM=true}if(ay.autoencode===az){ay.autoencode=true}}if(typeof aH==="string"){aD=ab(aH,ay)}else{if(aH instanceof Array){k=aH.concat();for(aA=k.length-1;aA!==-1;aA--){k[aA]=ab(k[aA],ay)}aD=k.join(") Tj\nT* (")}else{throw new Error('Type of text must be string or Array. "'+aH+'" is not recognized.')}}ad("BT\n/"+f+" "+al+" Tf\n"+al+" TL\n"+af+"\n"+r(aG*am)+" "+r((D-aE)*am)+" Td\n("+aD+") Tj\nET");return this};R.line=function(k,az,i,ay){ad(r(k*am)+" "+r((D-az)*am)+" m "+r(i*am)+" "+r((D-ay)*am)+" l S");return this};R.lines=function(k,aH,aG,aQ,aM){var aA,aO,aE,aF,aD,aC,aK,aI,aP,aN,aB,aL,az,aJ,ay;if(typeof k==="number"){aO=aG;aE=k;aF=aH;k=aO;aH=aE;aG=aF}aM=ae(aM);aQ=aQ===aA?[1,1]:aQ;ad(q(aH*am)+" "+q((D-aG)*am)+" m ");aD=aQ[0];aC=aQ[1];aI=k.length;aJ=aH;ay=aG;for(aK=0;aK<aI;aK++){aP=k[aK];if(aP.length===2){aJ=aP[0]*aD+aJ;ay=aP[1]*aC+ay;ad(q(aJ*am)+" "+q((D-ay)*am)+" l")}else{aN=aP[0]*aD+aJ;aB=aP[1]*aC+ay;aL=aP[2]*aD+aJ;az=aP[3]*aC+ay;aJ=aP[4]*aD+aJ;ay=aP[5]*aC+ay;ad(q(aN*am)+" "+q((D-aB)*am)+" "+q(aL*am)+" "+q((D-az)*am)+" "+q(aJ*am)+" "+q((D-ay)*am)+" c")}}ad(aM);return this};R.rect=function(i,aB,k,az,ay){var aA=ae(ay);ad([r(i*am),r((D-aB)*am),r(k*am),r(-az*am),"re",aA].join(" "));return this};R.triangle=function(az,aC,k,aA,i,ay,aB){this.lines([[k-az,aA-aC],[i-k,ay-aA],[az-i,aC-ay]],az,aC,[1,1],aB);return this};R.roundedRect=function(k,aD,ay,aA,aC,aB,az){var i=4/3*(Math.SQRT2-1);this.lines([[(ay-2*aC),0],[(aC*i),0,aC,aB-(aB*i),aC,aB],[0,(aA-2*aB)],[0,(aB*i),-(aC*i),aB,-aC,aB],[(-ay+2*aC),0],[-(aC*i),0,-aC,-(aB*i),-aC,-aB],[0,(-aA+2*aB)],[0,-(aB*i),(aC*i),-aB,aC,-aB]],k+aC,aD,[1,1],az);return this};R.ellipse=function(i,aD,aB,aA,k){var aC=ae(k),az=4/3*(Math.SQRT2-1)*aB,ay=4/3*(Math.SQRT2-1)*aA;ad([r((i+aB)*am),r((D-aD)*am),"m",r((i+aB)*am),r((D-(aD-ay))*am),r((i+az)*am),r((D-(aD-aA))*am),r(i*am),r((D-(aD-aA))*am),"c"].join(" "));ad([r((i-az)*am),r((D-(aD-aA))*am),r((i-aB)*am),r((D-(aD-ay))*am),r((i-aB)*am),r((D-aD)*am),"c"].join(" "));ad([r((i-aB)*am),r((D-(aD+ay))*am),r((i-az)*am),r((D-(aD+aA))*am),r(i*am),r((D-(aD+aA))*am),"c"].join(" "));ad([r((i+az)*am),r((D-(aD+aA))*am),r((i+aB)*am),r((D-(aD+ay))*am),r((i+aB)*am),r((D-aD)*am),"c",aC].join(" "));return this};R.circle=function(i,az,ay,k){return this.ellipse(i,az,ay,ay,k)};R.setProperties=function(i){var k;for(k in Q){if(Q.hasOwnProperty(k)&&i[k]){Q[k]=i[k]}}return this};R.setFontSize=function(i){al=i;return this};R.setFont=function(i,k){f=A(i,k);return this};R.setFontStyle=R.setFontType=function(k){var i;f=A(i,k);return this};R.getFontList=function(){var ay={},k,az,i;for(k in S){if(S.hasOwnProperty(k)){ay[k]=i=[];for(az in S[k]){if(S[k].hasOwnProperty(az)){i.push(az)}}}}return ay};R.setLineWidth=function(i){ad((i*am).toFixed(2)+" w");return this};R.setDrawColor=function(aA,az,ay,i){var k;if(az===undefined||(i===undefined&&aA===az===ay)){if(typeof aA==="string"){k=aA+" G"}else{k=r(aA/255)+" G"}}else{if(i===undefined){if(typeof aA==="string"){k=[aA,az,ay,"RG"].join(" ")}else{k=[r(aA/255),r(az/255),r(ay/255),"RG"].join(" ")}}else{if(typeof aA==="string"){k=[aA,az,ay,i,"K"].join(" ")}else{k=[r(aA),r(az),r(ay),r(i),"K"].join(" ")}}}ad(k);return this};R.setFillColor=function(aA,az,ay,i){var k;if(az===undefined||(i===undefined&&aA===az===ay)){if(typeof aA==="string"){k=aA+" g"}else{k=r(aA/255)+" g"}}else{if(i===undefined){if(typeof aA==="string"){k=[aA,az,ay,"rg"].join(" ")}else{k=[r(aA/255),r(az/255),r(ay/255),"rg"].join(" ")}}else{if(typeof aA==="string"){k=[aA,az,ay,i,"k"].join(" ")}else{k=[r(aA),r(az),r(ay),r(i),"k"].join(" ")}}}ad(k);return this};R.setTextColor=function(ay,k,i){if((ay===0&&k===0&&i===0)||(typeof k==="undefined")){af=q(ay/255)+" g"}else{af=[q(ay/255),q(k/255),q(i/255),"rg"].join(" ")}return this};R.CapJoinStyles={0:0,butt:0,but:0,bevel:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,milter:2};R.setLineCap=function(i){var k=this.CapJoinStyles[i];if(k===undefined){throw new Error("Line cap style of '"+i+"' is not recognized. See or extend .CapJoinStyles property for valid styles")}T=k;ad(k.toString(10)+" J");return this};R.setLineJoin=function(i){var k=this.CapJoinStyles[i];if(k===undefined){throw new Error("Line join style of '"+i+"' is not recognized. See or extend .CapJoinStyles property for valid styles")}V=k;ad(k.toString(10)+" j");return this};R.output=K;R.save=function(i){R.output("save",i)};for(at in d.API){if(d.API.hasOwnProperty(at)){if(at==="events"&&d.API.events.length){(function(az,aB){var aA,ay,k;for(k=aB.length-1;k!==-1;k--){aA=aB[k][0];ay=aB[k][1];az.subscribe.apply(az,[aA].concat(typeof ay==="function"?[ay]:ay))}}(I,d.API.events))}else{R[at]=d.API[at]}}}g();f="F1";aj();I.publish("initialized");return R}d.API={events:[]};return d}());(function(k){var d=0,o=0,c,q,j,e={x:undefined,y:undefined,w:undefined,h:undefined,ln:undefined},h=1,g=false,f=function(r,v,s,t,u){e={x:r,y:v,w:s,h:t,ln:u}},m=function(){return e},l=function(r){d=r},n=function(){return d},p=function(r){o=r},i=function(r){return o};k.getTextDimensions=function(r){c=this.internal.getFont().fontName;q=this.internal.getFontSize();j=this.internal.getFont().fontStyle;var u=0.264583*72/25.4,s,t;t=document.createElement("font");t.id="pdfDataExportCell";t.style.fontStyle=j;t.style.fontName=c;t.style.fontSize=q+"pt";t.innerHTML=r;document.body.appendChild(t);s={w:(t.offsetWidth+1)*u,h:(t.offsetHeight+1)*u};document.body.removeChild(t);return s};k.cellAddPage=function(){this.addPage();f(undefined,undefined,undefined,undefined,undefined);g=true;h+=1;p(1)};k.cellInitialize=function(){d=0;e={x:undefined,y:undefined,w:undefined,h:undefined,ln:undefined};h=1;g=false;p(0)};k.cell=function(B,A,C,u,r,z){this.lnMod=this.lnMod===undefined?0:this.lnMod;if(this.printingHeaderRow!==true&&this.lnMod!==0){z=z+this.lnMod}if((((z*u)+A+(u*2))/h)>=this.internal.pageSize.height&&h===1&&!g){this.cellAddPage();if(this.printHeaders&&this.tableHeaderRow){this.printHeaderRow(z);this.lnMod+=1;z+=1}if(n()===0){l(Math.round((this.internal.pageSize.height-(u*2))/u))}}else{if(g&&m().ln!==z&&i()===n()){this.cellAddPage();if(this.printHeaders&&this.tableHeaderRow){this.printHeaderRow(z);this.lnMod+=1;z+=1}}}var D=m(),t=this.getTextDimensions(r),v=1;if(D.x!==undefined&&D.ln===z){B=D.x+D.w}if(D.y!==undefined&&D.y===A){A=D.y}if(D.h!==undefined&&D.h===u){u=D.h}if(D.ln!==undefined&&D.ln===z){z=D.ln;v=0}if(g){A=u*(i()+v)}else{A=(A+(u*Math.abs(n()*h-z-n())))}this.rect(B,A,C,u);var s=/[а-яА-ЯЁё]/.test(r);if(s){this.text16(r,B+3,A+u-3)}else{this.text(r,B+3,A+u-3)}p(i()+v);f(B,A,C,u,z);return this};k.getKeys=(typeof Object.keys==="function")?function(r){if(!r){return[]}return Object.keys(r)}:function(r){var s=[],t;for(t in r){if(r.hasOwnProperty(t)){s.push(t)}}return s};k.arrayMax=function(w,v){var r=w[0],s,u,t;for(s=0,u=w.length;s<u;s+=1){t=w[s];if(v){if(v(r,t)===-1){r=t}}else{if(t>r){r=t}}}return r};k.table=function(L,t,K){var x=[],r=[],F,B,D,z,G,A,I={},C={},w,u,J=[],E,H=[],v,s,y;this.lnMod=0;if(K){B=K.autoSize||false;D=this.printHeaders=K.printHeaders||true;z=K.autoStretch||true}if(!L){throw"No data for PDF table"}if(t===undefined||(t===null)){x=this.getKeys(L[0])}else{if(t[0]&&(typeof t[0]!=="string")){for(G=0,A=t.length;G<A;G+=1){F=t[G];x.push(F.name);r.push(F.prompt)}}else{x=t}}if(K.autoSize){y=function(M){return M[F]};for(G=0,A=x.length;G<A;G+=1){F=x[G];I[F]=L.map(y);J.push(this.getTextDimensions(r[G]||F).w);u=I[F];for(E=0,A=u.length;E<A;E+=1){w=u[E];J.push(this.getTextDimensions(w).w)}C[F]=k.arrayMax(J)}}if(K.printHeaders){for(G=0,A=x.length;G<A;G+=1){F=x[G];H.push([10,10,C[F],25,String(r.length?r[G]:F)])}this.setTableHeaderRow(H);this.printHeaderRow(1)}for(G=0,A=L.length;G<A;G+=1){v=L[G];for(E=0,s=x.length;E<s;E+=1){F=x[E];this.cell(10,10,C[F],25,String(v[F]),G+2)}}return this};k.setTableHeaderRow=function(r){this.tableHeaderRow=r};k.printHeaderRow=function(r){if(!this.tableHeaderRow){throw"Property tableHeaderRow does not exist."}var s,u,t,v;this.printingHeaderRow=true;for(t=0,v=this.tableHeaderRow.length;t<v;t+=1){s=this.tableHeaderRow[t];u=[].concat(s);this.cell.apply(this,u.concat(r))}this.printingHeaderRow=false}}(b.API));(function(e){var d=e.getCharWidthsArray=function(u,w){if(!w){w={}}var k=w.widths?w.widths:this.internal.getFont().metadata.Unicode.widths,t=k.fof?k.fof:1,p=w.kerning?w.kerning:this.internal.getFont().metadata.Unicode.kerning,r=p.fof?p.fof:1;var o,m,q,n,s=0,v=k[0]||t,j=[];for(o=0,m=u.length;o<m;o++){q=u.charCodeAt(o);j.push((k[q]||v)/t+(p[q]&&p[q][s]||0)/r);s=q}return j};var g=function(l){var k=l.length,j=0;while(k){k--;j+=l[k]}return j};var c=e.getStringUnitWidth=function(j,i){return g(d.call(this,j,i))};var f=function(j,p,k,m){var s=[];var o=0,n=j.length,r=0;while(o!==n&&r+p[o]<k){r+=p[o];o++}s.push(j.slice(0,o));var q=o;r=0;while(o!==n){if(r+p[o]>m){s.push(j.slice(q,o));r=0;q=o}r+=p[o];o++}if(q!==o){s.push(j.slice(q,o))}return s};var h=function(u,n,x){if(!x){x={}}var v=d(" ",x)[0];var t=u.split(" ");var y=[],z=[y],k=x.textIndent||0,w=0,r=0,j,s;var q,o,p;for(q=0,o=t.length;q<o;q++){j=t[q];s=d(j,x);r=g(s);if(k+w+r>n){if(r>n){p=f(j,s,n-(k+w),n);y.push(p.shift());y=[p.pop()];while(p.length){z.push([p.shift()])}r=g(s.slice(j.length-y[0].length))}else{y=[j]}z.push(y);k=r;w=v}else{y.push(j);k+=w+r;w=v}}var m=[];for(q=0,o=z.length;q<o;q++){m.push(z[q].join(" "))}return m};e.splitTextToSize=function(s,o,t){if(!t){t={}}var k=t.fontSize||this.internal.getFontSize(),j=(function(l){var v={0:1},i={};if(!l.widths||!l.kerning){var w=this.internal.getFont(l.fontName,l.fontStyle),u="Unicode";if(w.metadata[u]){return{widths:w.metadata[u].widths||v,kerning:w.metadata[u].kerning||i}}}else{return{widths:l.widths,kerning:l.kerning}}return{widths:v,kerning:i}}).call(this,t);var r;if(s.match(/[\n\r]/)){r=s.split(/\r\n|\r|\n/g)}else{r=[s]}var m=1*this.internal.scaleFactor*o/k;j.textIndent=t.textIndent?t.textIndent*1*this.internal.scaleFactor/k:0;var q,p,n=[];for(q=0,p=r.length;q<p;q++){n=n.concat(h(r[q],m,j))}return n}})(b.API);(function(e){var f="addImage_";var h=function(o){var n,k;if(!o.charCodeAt(0)===255||!o.charCodeAt(1)===216||!o.charCodeAt(2)===255||!o.charCodeAt(3)===224||!o.charCodeAt(6)==="J".charCodeAt(0)||!o.charCodeAt(7)==="F".charCodeAt(0)||!o.charCodeAt(8)==="I".charCodeAt(0)||!o.charCodeAt(9)==="F".charCodeAt(0)||!o.charCodeAt(10)===0){throw new Error("getJpegSize requires a binary jpeg file")}var l=o.charCodeAt(4)*256+o.charCodeAt(5);var m=4,j=o.length;while(m<j){m+=l;if(o.charCodeAt(m)!==255){throw new Error("getJpegSize could not find the size of the image")}if(o.charCodeAt(m+1)===192){k=o.charCodeAt(m+5)*256+o.charCodeAt(m+6);n=o.charCodeAt(m+7)*256+o.charCodeAt(m+8);return[n,k]}else{m+=2;l=o.charCodeAt(m)*256+o.charCodeAt(m+1)}}},d=function(j){var o=this.internal.newObject(),k=this.internal.write,n=this.internal.putStream;j.n=o;k("<</Type /XObject");k("/Subtype /Image");k("/Width "+j.w);k("/Height "+j.h);if(j.cs==="Indexed"){k("/ColorSpace [/Indexed /DeviceRGB "+(j.pal.length/3-1)+" "+(o+1)+" 0 R]")}else{k("/ColorSpace /"+j.cs);if(j.cs==="DeviceCMYK"){k("/Decode [1 0 1 0 1 0 1 0]")}}k("/BitsPerComponent "+j.bpc);if("f" in j){k("/Filter /"+j.f)}if("dp" in j){k("/DecodeParms <<"+j.dp+">>")}if("trns" in j&&j.trns.constructor==Array){var m="";for(var l=0;l<j.trns.length;l++){m+=(j[m][l]+" "+j.trns[l]+" ");k("/Mask ["+m+"]")}}if("smask" in j){k("/SMask "+(o+1)+" 0 R")}k("/Length "+j.data.length+">>");n(j.data);k("endobj")},g=function(){var j=this.internal.collections[f+"images"];for(var k in j){d.call(this,j[k])}},c=function(){var j=this.internal.collections[f+"images"],k=this.internal.write,m;for(var l in j){m=j[l];k("/I"+m.i,m.n,"0","R")}};e.addImage=function(i,t,r,q,u,m){if(typeof i==="object"&&i.nodeType===1){var k=document.createElement("canvas");k.width=i.clientWidth;k.height=i.clientHeight;var v=k.getContext("2d");if(!v){throw ("addImage requires canvas to be supported by browser.")}v.drawImage(i,0,0,k.width,k.height);i=k.toDataURL("image/jpeg");t="JPEG"}if(t.toUpperCase()!=="JPEG"){throw new Error("addImage currently only supports format 'JPEG', not '"+t+"'")}var j,o=this.internal.collections[f+"images"],n=this.internal.getCoordinateString,p=this.internal.getVerticalCoordinateString;if(i.substring(0,23)==="data:image/jpeg;base64,"){i=atob(i.replace("data:image/jpeg;base64,",""))}if(o){j=Object.keys?Object.keys(o).length:(function(y){var w=0;for(var x in y){if(y.hasOwnProperty(x)){w++}}return w})(o)}else{j=0;this.internal.collections[f+"images"]=o={};this.internal.events.subscribe("putResources",g);this.internal.events.subscribe("putXobjectDict",c)}var s=h(i);var l={w:s[0],h:s[1],cs:"DeviceRGB",bpc:8,f:"DCTDecode",i:j,data:i};o[j]=l;if(!u&&!m){u=-96;m=-96}if(u<0){u=(-1)*l.w*72/u/this.internal.scaleFactor}if(m<0){m=(-1)*l.h*72/m/this.internal.scaleFactor}if(u===0){u=m*l.w/l.h}if(m===0){m=u*l.h/l.w}this.internal.write("q",n(u),"0 0",n(m),n(r),p(q+m),"cm /I"+l.i,"Do Q");return this}})(b.API);(function(c){var g=function(s){var y="0123456789abcdef",q="klmnopqrstuvwxyz",k={};for(var t=0;t<q.length;t++){k[q[t]]=y[t]}var r,o={},p=1,v,m=o,j=[],u,n="",w="",x,l=s.length-1,h;t=1;while(t!=l){h=s[t];t+=1;if(h=="'"){if(v){x=v.join("");v=r}else{v=[]}}else{if(v){v.push(h)}else{if(h=="{"){j.push([m,x]);m={};x=r}else{if(h=="}"){u=j.pop();u[0][u[1]]=m;x=r;m=u[0]}else{if(h=="-"){p=-1}else{if(x===r){if(k.hasOwnProperty(h)){n+=k[h];x=parseInt(n,16)*p;p=+1;n=""}else{n+=h}}else{if(k.hasOwnProperty(h)){w+=k[h];m[x]=parseInt(w,16)*p;p=+1;x=r;w=""}else{w+=h}}}}}}}}return o};var f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:g("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},e={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},d={Unicode:{"Courier-Oblique":g("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":g("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":g("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:g("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":g("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":g("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Helvetica:g("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":g("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),"Courier-Bold":g("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":g("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":g("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":g("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};c.events.push(["addFonts",function(k){var h,i,j,m,l="Unicode",n;for(i in k.fonts){if(k.fonts.hasOwnProperty(i)){h=k.fonts[i];j=d[l][h.PostScriptName];if(j){if(h.metadata[l]){m=h.metadata[l]}else{m=h.metadata[l]={}}m.widths=j.widths;m.kerning=j.kerning}n=e[l][h.PostScriptName];if(n){if(h.metadata[l]){m=h.metadata[l]}else{m=h.metadata[l]={}}m.encoding=n;if(n.codePages&&n.codePages.length){h.encoding=n.codePages[0]}}}}}])})(b.API);var a=window.jqxSaveAs=a||(navigator.msSaveBlob&&navigator.msSaveBlob.bind(navigator))||(function(j){var t=j.document,n=function(){return j.URL||j.webkitURL||j},g=j.URL||j.webkitURL||j,p=$("<a></a>")[0],i="download" in p,l=function(v){var u=t.createEvent("MouseEvents");u.initMouseEvent("click",true,false,j,0,0,0,0,0,false,false,false,false,0,null);return v.dispatchEvent(u)},q=j.webkitRequestFileSystem,r=j.requestFileSystem||q||j.mozRequestFileSystem,o=function(u){(j.setImmediate||j.setTimeout)(function(){throw u},0)},e="application/octet-stream",m=0,d=[],k=function(){var v=d.length;while(v--){var u=d[v];if(typeof u==="string"){g.revokeObjectURL(u)}else{u.remove()}}d.length=0},s=function(v,u,y){u=[].concat(u);var x=u.length;while(x--){var z=v["on"+u[x]];if(typeof z==="function"){try{z.call(v,y||v)}catch(w){o(w)}}}},h=function(v,w){var x=this,D=v.type,G=false,z,y,u=function(){var H=n().createObjectURL(v);d.push(H);return H},C=function(){s(x,"writestart progress write writeend".split(" "))},F=function(){if(G||!z){z=u(v)}if(y){y.location.href=z}x.readyState=x.DONE;C()},B=function(H){return function(){if(x.readyState!==x.DONE){return H.apply(this,arguments)}}},A={create:true,exclusive:false},E;x.readyState=x.INIT;if(!w){w="download"}if(i){z=u(v);p.href=z;p.download=w;if(l(p)){x.readyState=x.DONE;C();return}}if(j.chrome&&D&&D!==e){E=v.slice||v.webkitSlice;v=E.call(v,0,v.size,e);G=true}if(q&&w!=="download"){w+=".download"}if(D===e||q){y=j}else{y=j.open()}if(!r){F();return}m+=v.size;r(j.TEMPORARY,m,B(function(H){H.root.getDirectory("saved",A,B(function(I){var J=function(){I.getFile(w,A,B(function(K){K.createWriter(B(function(L){L.onwriteend=function(M){y.location.href=K.toURL();d.push(K);x.readyState=x.DONE;s(x,"writeend",M)};L.onerror=function(){var M=L.error;if(M.code!==M.ABORT_ERR){F()}};"writestart progress write abort".split(" ").forEach(function(M){L["on"+M]=x["on"+M]});L.write(v);x.abort=function(){L.abort();x.readyState=x.DONE};x.readyState=x.WRITING}),F)}),F)};I.getFile(w,{create:false},B(function(K){K.remove();J()}),B(function(K){if(K.code===K.NOT_FOUND_ERR){J()}else{F()}}))}),F)}),F)},f=h.prototype,c=function(u,v){return new h(u,v)};f.abort=function(){var u=this;u.readyState=u.DONE;s(u,"abort")};f.readyState=f.INIT=0;f.WRITING=1;f.DONE=2;f.error=f.onwritestart=f.onprogress=f.onwrite=f.onabort=f.onerror=f.onwriteend=null;if(j.addEventListener){j.addEventListener("unload",k,false)}return c}(self));(function(c){var d="pdfDataExport IE Below 9 Shim plugin";c.output=function(g,f){return this.internal.output(g,f);var e="Output.pdf";switch(g){case"datauristring":case"dataurlstring":case"datauri":case"dataurl":case"dataurlnewwindow":if(console){console.log(d+": Data URIs are not supported on IE6-9.")}break;case"save":e=f;break}}})(b.API)})();

