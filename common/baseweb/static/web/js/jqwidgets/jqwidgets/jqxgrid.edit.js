/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

(function(){if(typeof document==="undefined"){return}(function(a){a.extend(a.jqx._jqxGrid.prototype,{_handledblclick:function(t,n){if(t.target==null){return}if(n.disabled){return}if(a(t.target).ischildof(this.columnsheader)){return}var w;if(t.which){w=(t.which==3)}else{if(t.button){w=(t.button==2)}}if(w){return}var C;if(t.which){C=(t.which==2)}else{if(t.button){C=(t.button==1)}}if(C){return}var v=this.showheader?this.columnsheader.height()+2:0;var o=this._groupsheader()?this.groupsheader.height():0;var B=this.showtoolbar?this.toolbarheight:0;var z=this.showfilterbar?this.toolbarheight:0;o+=B;o+=z;var e=this.host.offset();var m=t.pageX-e.left;var l=t.pageY-v-e.top-o;var b=this._hittestrow(m,l);if(!b){return}var h=b.row;var j=b.index;var q=t.target.className;var p=this.table[0].rows[j];if(p==null){return}n.mousecaptured=true;n.mousecaptureposition={left:t.pageX,top:t.pageY-o};var r=this.hScrollInstance;var s=r.value;var d=0;var k=this.groupable?this.groups.length:0;for(var u=0;u<p.cells.length;u++){var f=parseInt(a(this.columnsrow[0].cells[u]).css("left"));var g=f-s;if(n.columns.records[u].pinned){g=f}var c=this._getcolumnat(u);if(c!=null&&c.hidden){continue}var A=g+a(this.columnsrow[0].cells[u]).width();if(A>=m&&m>=g){d=u;break}}if(h!=null){var c=this._getcolumnat(d);if(!(q.indexOf("jqx-grid-group-expand")!=-1||q.indexOf("jqx-grid-group-collapse")!=-1)){if(h.boundindex!=-1){n.begincelledit(n.getboundindex(h),c.datafield,c.defaulteditorvalue)}}}},_getpreveditablecolumn:function(c){var b=this;while(c>0){c--;var d=b.getcolumnat(c);if(!d){return null}if(!d.editable){continue}if(!d.hidden){return d}}return null},_getnexteditablecolumn:function(c){var b=this;while(c<this.columns.records.length){c++;var d=b.getcolumnat(c);if(!d){return null}if(!d.editable){continue}if(!d.hidden){return d}}return null},_handleeditkeydown:function(V,j){if(j.handlekeyboardnavigation){var N=j.handlekeyboardnavigation(V);if(N==true){return true}}var u=V.charCode?V.charCode:V.keyCode?V.keyCode:0;j.editEvent=V;if(j.showfilterrow&&j.filterable){if(this.filterrow){if(a(V.target).ischildof(this.filterrow)){return true}}}if(j.showfilterbar&&j.filterable){if(this.filterbar){if(a(V.target).ischildof(this.filterbar)){return true}}}if(V.target.className&&V.target.className.indexOf("jqx-grid-widget")>=0){return true}if(j.pageable){if(a(V.target).ischildof(this.pager)){return true}}if(this.showtoolbar){if(a(V.target).ischildof(this.toolbar)){return true}}if(this.showeverpresentrow){if(this.addnewrowtop){if(a(V.target).ischildof(this.addnewrowtop)){return true}}if(this.addnewrowbottom){if(a(V.target).ischildof(this.addnewrowbottom)){return true}}}if(this.showstatusbar){if(a(V.target).ischildof(this.statusbar)){return true}}if(this.rowdetails){if(a(V.target).ischildof(this.content.find("[role='rowgroup']"))){return true}}if(this.editcell){if(this.editmode==="selectedrow"){if(u===13){this.endrowedit(this.editcell.row,false);return false}else{if(u===27){this.endrowedit(this.editcell.row,true);return false}}if(u==32){if(this._currentColumn&&this.getcolumn(this._currentColumn).columntype=="checkbox"){var O=this.getcolumn(this._currentColumn);if(O.editable){var C=!this.getcellvalue(this.editcell.row,O.datafield);var J=this.getrowdata(this.editcell.row);var s=this.editcell.row;var J=j.getrowdata(j.editcell.row);this.pushToHistory=true;this.setcellvalue(this.editcell.row,O.datafield,C,false);this.pushToHistory=false;var k=this._focusedColumn;var d=this._currentColumn;var f=this._currentEditableColumn;this.endrowedit(this.editcell.row,false);this.beginrowedit(s,false);this._currentColumn=d;this._focusedColumn=k;this._currentEditableColumn=f;this._renderrows();this.selectcell(s,O.datafield);this._oldselectedcell=this.selectedcell;if(k){var I=this;setTimeout(function(){I.selectcell(s,O.datafield);I._oldselectedcell=I.selectedcell;a(I._checkboxCells[O.datafield].checkbox).jqxCheckBox("focus")},25)}return false}}}if(u===9){var M=this.editcell.datafield;var e=this._getcolumnindex(M);if(this._currentEditableColumn){e=this._currentEditableColumn}else{this._currentEditableColumn=e}var O=this._getnexteditablecolumn(e);if(V.shiftKey||this.rtl){O=this._getpreveditablecolumn(e)}if(O){var e=this._getcolumnindex(O.datafield);if(this.editcell[O.datafield]){this._currentEditableColumn=e;var b=this.editcell[O.datafield].editor;if(b){if(b.data().jqxWidget&&b.data().jqxWidget.focus){b.data().jqxWidget.focus()}else{b.focus();b[0].onkeyup=function(){if(j.gridcontent[0].scrollTop!=0){j.scrolltop(Math.abs(j.gridcontent[0].scrollTop));j.gridcontent[0].scrollTop=0}if(j.gridcontent[0].scrollLeft!=0){j.gridcontent[0].scrollLeft=0}}}}this._focusedColumn=O.datafield;this._currentColumn=O.datafield;if(this.gridcontent[0].scrollTop!=0){this.scrolltop(Math.abs(this.gridcontent[0].scrollTop));this.gridcontent[0].scrollTop=0}if(this.gridcontent[0].scrollLeft!=0){this.gridcontent[0].scrollLeft=0}}else{if(O.columntype=="checkbox"){this._currentColumn=O.datafield;this._currentEditableColumn=e;this.selectcell(this.editcell.row,O.datafield);this._oldselectedcell=this.selectedcell;if(this._checkboxCells[O.datafield]){a(this._checkboxCells[O.datafield].checkbox).jqxCheckBox("focus")}return false}}}return false}return true}if(this.editcell.columntype==null||this.editcell.columntype=="textbox"||this.editcell.columntype=="numberinput"||this.editcell.columntype=="combobox"||this.editcell.columntype=="datetimeinput"){if(u>=33&&u<=40&&j.selectionmode=="multiplecellsadvanced"){var b=this.editcell.columntype=="textbox"||this.editcell.columntype==null?this.editcell.editor:this.editcell.editor.find("input");var p=j._selection(b);var L=b.val().length;if(p.length>0&&this.editcell.columntype!="datetimeinput"){j._cancelkeydown=true}if(p.start>0&&u==37){j._cancelkeydown=true}if(p.start<L&&u==39&&this.editcell.columntype!="datetimeinput"){j._cancelkeydown=true}if(this.editcell.columntype=="datetimeinput"&&u==39){if(p.start+p.length<L){j._cancelkeydown=true}}}}else{if(this.editcell.columntype=="dropdownlist"){if(u==37||u==39&&j.selectionmode=="multiplecellsadvanced"){j._cancelkeydown=false}}else{if(this.selectionmode=="multiplecellsadvanced"&&this.editcell.columntype!="textbox"&&this.editcell.columntype!="numberinput"){j._cancelkeydown=true}}}if(u==32){if(j.editcell.columntype=="checkbox"){var O=j.getcolumn(j.editcell.datafield);if(O.editable){var C=!j.getcellvalue(j.editcell.row,j.editcell.column);if(O.cellbeginedit){var n=O.cellbeginedit(j.editcell.row,O.datafield,O.columntype,!C);if(n==false){return false}}var J=j.getrowdata(j.editcell.row);j.pushToHistory=true;j.setcellvalue(j.editcell.row,j.editcell.column,C,true);j.pushToHistory=false;j._raiseEvent(18,{rowindex:j.editcell.row,row:J,datafield:j.editcell.column,oldvalue:!C,value:C,columntype:"checkbox"});return false}}}if(u==9){var l=this.editcell.row;var M=this.editcell.column;var i=M;var e=j._getcolumnindex(M);var F=false;var K=j.getrowvisibleindex(l);var P=l;this.editchar="";var t=this.editcell.validated;if(!this.editcell.validated){var t=this.endcelledit(this.editcell.row,this.editcell.column,false,true,false)}if(t!=false){if(V.shiftKey||this.rtl){var O=j._getpreveditablecolumn(e);if(O){M=O.datafield;F=true;if(j.selectionmode.indexOf("cell")!=-1){j.selectprevcell(l,i);j._oldselectedcell=j.selectedcell;setTimeout(function(){j.ensurecellvisible(K,M)},10)}}else{var r=j._getlastvisiblecolumn();F=true;M=r.displayfield;var h=j.getdisplayrows()[K-1];if(h){l=h.dataindex;if(l===undefined){l=h.boundindex}K=j.getrowvisibleindex(l)}var A=true;if(j.pageable){var S=Math.floor(K/this.pagesize);if(this.dataview.pagenum!=S){A=false}}if(A&&j.selectionmode.indexOf("cell")!=-1){j.clearselection();j.selectcell(l,M);j._oldselectedcell=j.selectedcell;setTimeout(function(){j.ensurecellvisible(K,M)},10)}}}else{var O=j._getnexteditablecolumn(e);if(O){M=O.datafield;F=true;if(j.selectionmode.indexOf("cell")!=-1){j.selectnextcell(l,i);j._oldselectedcell=j.selectedcell;setTimeout(function(){j.ensurecellvisible(K,M)},10)}}else{var z=j._getfirstvisiblecolumn();F=true;M=z.displayfield;var h=j.getdisplayrows()[K+1];if(h){l=h.dataindex;if(l===undefined){l=h.boundindex}K=j.getrowvisibleindex(l)}var A=true;if(j.pageable){var S=Math.floor(K/this.pagesize);if(this.dataview.pagenum!=S){A=false}}if(j.selectionmode.indexOf("cell")!=-1){if(A){j.clearselection();j.selectcell(l,M);j._oldselectedcell=j.selectedcell;setTimeout(function(){j.ensurecellvisible(K,M)},10)}}}}if(F){if(j.pageable){var S=Math.floor(K/this.pagesize);if(this.dataview.pagenum!=S){this._renderrows(this.virtualsizeinfo);if(j.selectionmode.indexOf("cell")!=-1){j.clearselection();j.selectcell(P,i);j._oldselectedcell=j.selectedcell}if(S>this.dataview.pagenum){setTimeout(function(){j.pagerpageinput.focus()},25)}return}}j.begincelledit(l,M);if(this.editcell!=null&&this.editcell.columntype=="checkbox"){this._renderrows(this.virtualsizeinfo)}}else{if(this.editcell!=null){j.endcelledit(l,M,false);this._renderrows(this.virtualsizeinfo)}return true}}return false}else{if(u==13){var H=this.selectedcell;if(H){var B=this.getrowvisibleindex(H.rowindex)}this.endcelledit(this.editcell.row,this.editcell.column,false,true);if(this.selectionmode=="multiplecellsadvanced"){var D=j.getselectedcell();if(D!=null){if(j.selectcell){if(this.editcell==null){if(D.rowindex+1<this.dataview.totalrecords){if(this.sortcolumn!=D.datafield){var K=this.getrowvisibleindex(D.rowindex);var q=this.dataview.loadedrecords[K+1];if(q){if(!this.pageable||(this.pageable&&K+1<(this.dataview.pagenum+1)*this.pagesize)){this.clearselection(false);this.selectcell(this.getboundindex(q),D.datafield);var D=this.getselectedcell();this.ensurecellvisible(q.visibleindex,D.datafield)}}}else{if(H!=null){var U=this.dataview.loadedrecords[B+1];if(U){if(!this.pageable||(this.pageable&&B+1<this.pagesize)){this.clearselection(false);this.selectcell(this.getboundindex(U),D.datafield)}else{if(this.pageable&&B+1>=this.pagesize){this.clearselection(false);var U=this.dataview.loadedrecords[B];this.selectcell(this.getboundindex(U),D.datafield)}}}}}}}}}}return false}else{if(u==27){this.endcelledit(this.editcell.row,this.editcell.column,true,true);return false}}}}else{var Q=false;if(u==113){Q=true}if(!V.ctrlKey&&!V.altKey&&!V.metaKey){if(u>=48&&u<=57){this.editchar=String.fromCharCode(u);Q=true}if(u===189){Q=true}if(u>=65&&u<=90){this.editchar=String.fromCharCode(u);var o=false;if(V.shiftKey){o=V.shiftKey}else{if(V.modifiers){o=!!(V.modifiers&4)}}if(this._capsLock){o=!o}if(!o){this.editchar=this.editchar.toLowerCase()}Q=true}else{if(u>=96&&u<=105){this.editchar=u-96;this.editchar=this.editchar.toString();Q=true}}var E=a(".jqx-grid").length;Q=Q&&(E==1||(E>1&&j.focused));var T=a.data(document.body,"jqxgrid.edit");if(T!==undefined&&T!==""){if(u===13||Q){if(T!=j.element.id){return true}}}}if(u==13||Q){if(j.getselectedrowindex){var l=j.getselectedrowindex();if(j.editmode==="selectedrow"){if(l>=0){j.beginrowedit(l)}else{var D=j.getselectedcell();if(D!=null){var O=j._getcolumnbydatafield(D.datafield);j.beginrowedit(D.rowindex)}return false}}switch(j.selectionmode){case"singlerow":case"multiplerows":case"multiplerowsextended":if(l>=0){var M="";for(var R=0;R<j.columns.records.length;R++){var O=j.getcolumnat(R);if(O.editable){M=O.datafield;break}}if(j.editmode==="selectedrow"){j.beginrowedit(l)}else{j.begincelledit(l,M)}}break;case"singlecell":case"multiplecells":case"multiplecellsextended":var D=j.getselectedcell();if(D!=null){var O=j._getcolumnbydatafield(D.datafield);if(j.editmode==="selectedrow"){j.beginrowedit(D.rowindex)}else{if(O.columntype!="checkbox"){j.begincelledit(D.rowindex,D.datafield)}}}break;case"multiplecellsadvanced":var D=j.getselectedcell();if(D!=null){if(u==13){if(j.selectcell){var K=this.getrowvisibleindex(D.rowindex);if(K+1<j.dataview.totalrecords){var q=this.dataview.loadedrecords[K+1];if(q){this.clearselection(false);this.selectcell(this.getboundindex(q),D.datafield);var D=this.getselectedcell();this.ensurecellvisible(q.visibleindex,D.datafield)}}}}else{if(j.editmode!=="selectedrow"){j.begincelledit(D.rowindex,D.datafield,null,false)}}}break}return false}}if(u==46){var w=j.getselectedcells();if(j.selectionmode.indexOf("cell")==-1){if(j._getcellsforcopypaste){w=j._getcellsforcopypaste()}}if(w!=null&&w.length>0){for(var y=0;y<w.length;y++){var D=w[y];if(!D.datafield){continue}var O=j.getcolumn(D.datafield);var G=j.getcellvalue(D.rowindex,D.datafield);if(G!==""&&O.editable&&j.enablekeyboarddelete){var c=null;if(O.columntype=="checkbox"){if(!O.threestatecheckbox){c=false}}if(O.cellbeginedit){var n=O.cellbeginedit(D.rowindex,O.datafield,O.columntype,c);if(n==false){return false}}var J=j.getrowdata(D.rowindex);if(O.validation){var x=O.validation(D,c);var v=typeof x=="boolean"?x:x.result;if(!v){return}}j._raiseEvent(17,{rowindex:D.rowindex,row:J,datafield:D.datafield,value:G});j.pushToHistory=true;if(y==w.length-1){j.setcellvalue(D.rowindex,D.datafield,c,true);if(O.displayfield!=O.datafield){j.setcellvalue(D.rowindex,O.displayfield,c,true)}}else{j.setcellvalue(D.rowindex,D.datafield,c,false);if(O.displayfield!=O.datafield){j.setcellvalue(D.rowindex,O.displayfield,c,true)}}j.pushToHistory=false;if(O.cellendedit){var g=O.cellendedit(D.rowindex,O.datafield,O.columntype,c)}j._raiseEvent(18,{rowindex:D.rowindex,row:J,datafield:D.datafield,oldvalue:G,value:c})}}this.dataview.updateview();this._renderrows(this.virtualsizeinfo);return false}}if(u==32){var D=j.getselectedcell();if(D!=null){var O=j.getcolumn(D.datafield);if(O.columntype=="checkbox"&&O.editable){var C=!j.getcellvalue(D.rowindex,D.datafield);if(O.cellbeginedit){var n=O.cellbeginedit(D.rowindex,O.datafield,O.columntype,!C);if(n==false){return false}}var J=j.getrowdata(D.rowindex);j._raiseEvent(17,{rowindex:D.rowindex,row:J,datafield:D.datafield,value:!C,columntype:"checkbox"});j.pushToHistory=true;j.setcellvalue(D.rowindex,D.datafield,C,true);j.pushToHistory=false;j._raiseEvent(18,{rowindex:D.rowindex,row:J,datafield:D.datafield,oldvalue:!C,value:C,columntype:"checkbox"});return false}}}}return true},begincelledit:function(o,e,l,g,c){var f=this.getcolumn(e);this._cellscache=new Array();if(e==null){return}if(f.columntype=="number"||f.columntype=="button"||f.createwidget){return}if(this.groupable){if(this.groups.indexOf(e)>=0){return}if(this.groups.indexOf(f.displayfield)>=0){return}}if(this.editrow!=undefined){return}if(this.editcell){if(this.editcell.row==o&&this.editcell.column==e){return true}if(this.editmode==="selectedrow"){if(this.editcell.row==o){return}}var d=this.endcelledit(this.editcell.row,this.editcell.column,false,true,false);if(false==d){return}}var i=f.columntype=="checkbox"||f.columntype=="button"||f.createwidget;this.host.removeClass("jqx-disableselect");this.content.removeClass("jqx-disableselect");if(f.editable){if(f.cellbeginedit){var k=this.getcell(o,e);var m=f.cellbeginedit(o,e,f.columntype,k!=null?k.value:null);if(m==false){return}}var j=this.getrowvisibleindex(o);this.editcell=this.getcell(o,e);if(this.editcell){this.editcell.visiblerowindex=j;if(!this.editcell.editing){if(!i){this.editcell.editing=true}this.editcell.columntype=f.columntype;this.editcell.defaultvalue=l;if(f.defaultvalue!=undefined){this.editcell.defaultvalue=f.defaultvalue}this.editcell.init=true;if(f.columntype!="checkbox"&&this.editmode!="selectedrow"){var h=this.getrowdata(o);this._raiseEvent(17,{rowindex:o,row:h,datafield:f.datafield,value:this.editcell.value,columntype:f.columntype})}a.data(document.body,"jqxgrid.edit",this.element.id);if(!i){var b=this.getrowvisibleindex(o);if(g!==false){if(!this.autorowheight&&this.groups.length===0){var n=this.ensurecellvisible(b,f.datafield)}}if(c!==false){this._renderrows(this.virtualsizeinfo)}}if(this.editcell){this.editcell.init=false;return true}}}}else{if(!this.editcell){this.editchar="";return}this.editchar="";this.editcell.editor=null;this.editcell.editing=false;if(c!==false){this._renderrows(this.virtualsizeinfo)}this.editcell=null}},getScrollTop:function(){if(this._py){return this._py}this._py=typeof pageYOffset!="undefined";if(this._py){return pageYOffset}else{var c=document.body;var b=document.documentElement;b=(b.clientHeight)?b:c;return b.scrollTop}},getScrollLeft:function(){if(typeof pageXOffset!="undefined"){return pageXOffset}else{var c=document.body;var b=document.documentElement;b=(b.clientHeight)?b:c;return b.scrollLeft}},endcelledit:function(h,o,k,e,p){if(h==undefined||o==undefined){if(this.editcell){h=this.editcell.row;o=this.editcell.column}if(k==undefined){k=true}}if(!this.editcell){return}var d=this.getcolumn(o);var v=this;if(v.editmode==="selectedrow"){this.endrowedit(h,k);return}var u=function(){if(p!=false){if(v.isTouchDevice()){return}if(!v.isNestedGrid){var w=v.getScrollTop();var y=v.getScrollLeft();try{v.element.focus();v.content.focus();if(w!=v.getScrollTop()){window.scrollTo(y,w)}setTimeout(function(){v.element.focus();v.content.focus();if(w!=v.getScrollTop()){window.scrollTo(y,w)}},10)}catch(x){}}}};if(d.columntype=="checkbox"||d.columntype=="button"||d.createwidget){if(this.editcell){this.editcell.editor=null;this.editcell.editing=false;this.editcell=null}return true}var i=this._geteditorvalue(d);var g=function(x){x._hidecelleditor();if(d.cellendedit){d.cellendedit(h,o,d.columntype,x.editcell.value,i)}x.editchar=null;var z;if(d.displayfield!=d.datafield){var w=x.getcellvalue(x.editcell.row,d.displayfield);var y=x.editcell.value;z={value:y,label:w}}else{z=x.editcell.value}var A=x.getrowdata(h);x._raiseEvent(18,{rowindex:h,row:A,datafield:o,displayfield:d.displayfield,oldvalue:i,value:i,columntype:d.columntype});x.editcell.editor=null;x.editcell.editing=false;x.editcell=null;if(e||e==undefined){x._renderrows(x.virtualsizeinfo)}u();if(!x.enablebrowserselection){x.host.addClass("jqx-disableselect");x.content.addClass("jqx-disableselect")}};if(k){g(this);return false}if(this.validationpopup){this.validationpopup.hide();this.validationpopuparrow.hide()}if(d.cellvaluechanging){var b=d.cellvaluechanging(h,o,d.columntype,this.editcell.value,i);if(b!=undefined){i=b}}if(d.validation){var c=this.getcell(h,o);try{var q=d.validation(c,i);var m=this.gridlocalization.validationstring;if(q.message!=undefined){m=q.message}var n=typeof q=="boolean"?q:q.result;if(!n){if(q.showmessage==undefined||q.showmessage==true){this._showvalidationpopup(h,o,m)}this.editcell.validated=false;return false}}catch(s){this._showvalidationpopup(h,o,this.gridlocalization.validationstring);this.editcell.validated=false;return false}}var j;if(d.displayfield!=d.datafield){var l=this.getcellvalue(this.editcell.row,d.displayfield);var r=this.editcell.value;j={value:r,label:l}}else{j=this.editcell.value}var f=this.getrowdata(h);if(d.cellendedit){var t=d.cellendedit(h,o,d.columntype,this.editcell.value,i);if(t==false){this._raiseEvent(18,{rowindex:h,row:f,datafield:o,displayfield:d.displayfield,oldvalue:j,value:j,columntype:d.columntype});g(this);return false}}this._raiseEvent(18,{rowindex:h,row:f,datafield:o,displayfield:d.displayfield,oldvalue:j,value:i,columntype:d.columntype});this._hidecelleditor(false);if(this.editcell!=undefined){this.editcell.editor=null;this.editcell.editing=false}this.editcell=null;this.editchar=null;this.pushToHistory=true;this.setcellvalue(h,o,i,e);this.pushToHistory=false;if(!this.enablebrowserselection){this.host.addClass("jqx-disableselect");this.content.addClass("jqx-disableselect")}if(!a.jqx.browser.msie){u()}a.data(document.body,"jqxgrid.edit","");if(this.showgroupaggregates&&this.groupable&&this.groups.length&&d.aggregates&&d.aggregates.length){this.dataview.reloaddata();this._renderrows(this.virtualsizeinfo)}return true},beginrowedit:function(f){var e=this;var g=-1;if(f==undefined){return}e._oldselectedrow=f;this._cellscache=new Array();var c=false;if(this.editmode!="selectedrow"){c=true}if(c){var b=this.editmode;this.editmode="selectedrow"}var d=null;if(this._currentEditableColumn){d=this.getcolumnat(this._currentEditableColumn).datafield}a.each(this.columns.records,function(i,k){if(e.editable&&this.editable){if(this.columntype==="checkbox"||this.columntype==="custom"||this.columntype==="widget"||this.columntype==="template"||this.columntype==="button"){return true}if(!d){d=this.datafield}var h=e.getcell(f,this.datafield);e.begincelledit(f,this.datafield,null,false,false);var j=e.getrowdata(f);e._raiseEvent(17,{rowindex:f,row:j,datafield:this.datafield,value:h.value,columntype:this.columntype})}});if(e.editcell){e.editcell.init=true}this._renderrows(this.virtualsizeinfo);if(c){this.editmode=b}if(d&&e.editcell){setTimeout(function(){if(e.editcell&&e.editcell[d]){var h=e.editcell[d].editor;if(h){if(h.data().jqxWidget&&h.data().jqxWidget.focus){h.data().jqxWidget.focus()}else{h.focus()}}}},25)}},endrowedit:function(k,o){var D=this;this._currentEditableColumn=0;this._focusedColumn=null;this._currentColumn=null;this._checkboxCells=[];if(!this.editcell){return false}if(this.editcell.editor==undefined&&this.editcell.columntype!=="checkbox"){return false}if(this._commandColumnEdit){this.editmode=this._commandColumnEditMode;delete this._commandColumnEdit;delete this._commandColumnEditMode;delete this.editrow}var C=function(){if(focus!=false){if(D.isTouchDevice()){return}if(!D.isNestedGrid){var i=D.getScrollTop();var F=D.getScrollLeft();try{D.element.focus();D.content.focus();if(i!=D.getScrollTop()){window.scrollTo(F,i)}setTimeout(function(){D.element.focus();D.content.focus();if(i!=D.getScrollTop()){window.scrollTo(F,i)}},10)}catch(E){}}}};var p=false;if(this.editmode!="selectedrow"){p=true}if(p){var v=this.editmode;this.editmode="selectedrow"}var h=false;var d={};if(this.validationpopup){this.validationpopup.hide();this.validationpopuparrow.hide()}for(var B=0;B<this.columns.records.length;B++){var e=this.columns.records[B];if(!e.editable){continue}if(e.hidden){continue}if(e.createwidget){continue}var l=this._geteditorvalue(e);if(e.columntype=="checkbox"){if(o){l=this.editcell[e.datafield];var n=this.getcellvalue(this.editcell.row,e.displayfield);this.setcellvalue(this.editcell.row,e.displayfield,l)}continue}var g=function(F){F._hidecelleditor();var E=F.getcellvalue(F.editcell.row,e.displayfield);if(e.cellendedit){e.cellendedit(k,u,e.columntype,E,l)}F.editchar=null;var I;if(e.displayfield!=e.datafield){var i=F.getcellvalue(F.editcell.row,e.displayfield);var H=E;I={value:H,label:i}}else{I=E}var G=F.getrowdata(k);F._raiseEvent(18,{rowindex:k,row:G,datafield:u,displayfield:e.displayfield,oldvalue:E,value:E,columntype:e.columntype});F.editcell.editing=false};if(o){g(this);continue}if(e.cellvaluechanging){var n=this.getcellvalue(this.editcell.row,e.displayfield);var b=e.cellvaluechanging(k,u,e.columntype,n,l);if(b!=undefined){l=b}}var u=e.datafield;if(e.validation){var c=this.getcell(k,e.datafield);try{var w=e.validation(c,l);var r=this.gridlocalization.validationstring;if(w.message!=undefined){r=w.message}var t=typeof w=="boolean"?w:w.result;if(!t){if(w.showmessage==undefined||w.showmessage==true){this._showvalidationpopup(k,u,r)}h=true;this.editcell[e.datafield].validated=false;continue}}catch(z){this._showvalidationpopup(k,u,this.gridlocalization.validationstring);this.editcell[e.datafield].validated=false;h=true;continue}}if(e.displayfield!=e.datafield){var q=this.getcellvalue(this.editcell.row,e.displayfield);var x=this.editcell.value;n={value:x,label:q}}else{n=this.getcellvalue(this.editcell.row,e.displayfield)}d[e.datafield]={newvalue:l,oldvalue:n}}var y={};var s={};if(!h){this._hidecelleditor(false);for(var B=0;B<this.columns.records.length;B++){var e=this.columns.records[B];var u=e.datafield;if(e.hidden){continue}if(!e.editable){continue}var f=this.getrowdata(k);if(e.createwidget){continue}if(e.columntype=="checkbox"){var l=this.getcellvalue(k,e.displayfield);this._raiseEvent(18,{rowindex:k,row:f,datafield:e.datafield,displayfield:e.displayfield,oldvalue:l,value:l,columntype:e.columntype});continue}if(!d[e.datafield]){continue}var l=d[e.datafield].newvalue;var n=d[e.datafield].oldvalue;if(e.cellendedit){var A=e.cellendedit(k,u,e.columntype,n,l);if(A==false){this._raiseEvent(18,{rowindex:k,row:f,datafield:u,displayfield:e.displayfield,oldvalue:n,value:n,columntype:e.columntype});g(this);continue}}if(!this.source.updaterow){this._raiseEvent(18,{rowindex:k,row:f,datafield:e.datafield,displayfield:e.displayfield,oldvalue:n,value:l,columntype:e.columntype})}y[e.datafield]=l;s[e.datafield]=n}var j=this.getrowid(k);var f=this.getrowdata(k);a.each(y,function(i,F){if(F&&F.label!=undefined){var E=D.getcolumn(i);f[E.displayfield]=F.label;f[E.datafield]=F.value}else{f[i]=F}});if(!this.enablebrowserselection){this.host.addClass("jqx-disableselect");this.content.addClass("jqx-disableselect")}a.data(document.body,"jqxgrid.edit","");this.editcell=null;this.editchar=null;this.pushToHistory=true;if(this.source.updaterow&&!o){var m=false;var D=this;var t=function(I){var G=D.source.updaterow;D.source.updaterow=null;if(false==I){a.each(s,function(i,K){if(K&&K.label!=undefined){var J=D.getcolumn(i);f[J.displayfield]=K.label;f[J.datafield]=K.value}else{f[i]=K}});D.updaterow(j,f)}else{D.updaterow(j,f)}for(var F=0;F<D.columns.records.length;F++){var H=D.columns.records[F];var E=H.datafield;D._raiseEvent(18,{rowindex:k,datafield:H.datafield,row:f,displayfield:H.displayfield,oldvalue:s[H.datafield],value:f[H.displayfield],columntype:H.columntype})}D.source.updaterow=G};try{m=this.source.updaterow(j,f,t);if(m==undefined){m=true}}catch(z){m=false;return}}else{if(!o){this.updaterow(j,f)}this._renderrows(this.virtualsizeinfo)}}this.pushToHistory=false;if(p){this.editmode=v}return h},_selection:function(b){if("selectionStart" in b[0]){var g=b[0];var h=g.selectionEnd-g.selectionStart;return{start:g.selectionStart,end:g.selectionEnd,length:h,text:g.value}}else{var d=document.selection.createRange();if(d==null){return{start:0,end:g.value.length,length:0}}var c=b[0].createTextRange();var f=c.duplicate();c.moveToBookmark(d.getBookmark());f.setEndPoint("EndToStart",c);var h=d.text.length;return{start:f.text.length,end:f.text.length+d.text.length,length:h,text:d.text}}},_setSelection:function(e,b,d){if("selectionStart" in d[0]){d[0].focus();d[0].setSelectionRange(e,b)}else{var c=d[0].createTextRange();c.collapse(true);c.moveEnd("character",b);c.moveStart("character",e);c.select()}},findRecordIndex:function(g,c,b){var b=b;if(c){var e=b.length;for(var h=0;h<e;h++){var f=b[h];var d=f.label;if(g==d){return h}}}return -1},_destroyeditors:function(){var b=this;if(!this.columns.records){return}a.each(this.columns.records,function(f,j){var c=a.trim(this.datafield).split(" ").join("");switch(this.columntype){case"dropdownlist":var g=b.editors["dropdownlist_"+c];if(g){g.jqxDropDownList("destroy");b.editors["dropdownlist_"+c]=null}break;case"combobox":var k=b.editors["combobox_"+c];if(k){k.jqxComboBox("destroy");b.editors["combobox_"+c]=null}break;case"datetimeinput":var d=b.editors["datetimeinput_"+this.datafield];if(d){d.jqxDateTimeInput("destroy");b.editors["datetimeinput_"+c]=null}break;case"numberinput":var e=b.editors["numberinput_"+c];if(e){e.jqxNumberInput("destroy");b.editors["numberinput_"+c]=null}break;case"custom":case"template":if(b.destroyeditor){if(b.editors["templateeditor_"+c]){b.destroyeditor(b.editors["templateeditor_"+c]);b.editors["templateeditor_"+c]=null}}if(b.destroyeditor){var m=b.getrows.length();for(var l=0;l<m;l++){if(b.editors["customeditor_"+c+"_"+l]){b.destroyeditor(b.editors["customeditor_"+c+"_"+l],l);b.editors["customeditor_"+c+"_"+l]=null}}}break;case"textbox":default:var h=b.editors["textboxeditor_"+c];if(h){b.removeHandler(h,"keydown");b.editors["textbox_"+c]=null}break}});b.editors=new Array()},_showcelleditor:function(r,I,o,M,x){if(o==undefined){return}if(this.editcell==null){return}if(I.columntype=="checkbox"&&I.editable){if(this.editmode=="selectedrow"){if(!this._checkboxCells){this._checkboxCells=[]}this._checkboxCells[I.datafield]=o}return}if(x==undefined){x=true}if(this.editmode=="selectedrow"){this.editchar="";x=false}var F=I.datafield;var h=a(o);var t=this;var d=this.editcell.editor;var K=this.getcellvalue(r,F);var D=this.getcelltext(r,F);var k=this.hScrollInstance;var u=k.value;var j=parseInt(u);var L=this.columns.records.indexOf(I);this.editcell.element=o;if(this.editcell.validated==false){var J="";if(this.validationpopup){J=this.validationpopup.text()}this._showvalidationpopup(r,F,J)}var m=function(e){if(t.hScrollInstance.isScrolling()||t.vScrollInstance.isScrolling()){return}if(!x){return}if(t.isTouchDevice()){return}if(e){e.focus()}if(t.gridcontent[0].scrollTop!=0){t.scrolltop(Math.abs(t.gridcontent[0].scrollTop));t.gridcontent[0].scrollTop=0}if(t.gridcontent[0].scrollLeft!=0){t.gridcontent[0].scrollLeft=0}};switch(I.columntype){case"dropdownlist":if(this.host.jqxDropDownList){o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");var B=a.trim(I.displayfield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}if(B.indexOf(".")!=-1){B=B.replace(".","")}var G=t.theme==="fluent"?2:0;var l=this.editors["dropdownlist_"+E];d=l==undefined?a("<div style='border: none; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='dropdownlisteditor'></div>"):l;d.css("top",G+a(o).parent().position().top);if(this.oldhscroll){d.css("left",G-j+parseInt(a(o).position().left))}else{d.css("left",G+parseInt(a(o).position().left))}if(I.pinned){d.css("left",G+j+parseInt(a(o).position().left))}if(l==undefined){d.prependTo(this.table);d[0].id="dropdownlisteditor"+this.element.id+E;var g=this.source._source?true:false;var y=null;if(!g){y=new a.jqx.dataAdapter(this.source,{autoBind:false,uniqueDataFields:[B],async:false,autoSort:true,autoSortField:B})}else{var q={localdata:JSON.parse(JSON.stringify(this.source.records)),datatype:this.source.datatype,async:false};y=new a.jqx.dataAdapter(q,{autoBind:false,async:false,uniqueDataFields:[B],autoSort:true,autoSortField:B})}var v=!I.createeditor?true:false;d.jqxDropDownList({hint:false,enableBrowserBoundsDetection:true,keyboardSelection:false,source:y,rtl:this.rtl,autoDropDownHeight:v,theme:this.theme,width:h.width()-G,height:h.height()-G,displayMember:B,valueMember:F});this.editors["dropdownlist_"+E]=d;if(I.createeditor){I.createeditor(r,K,d)}}if(I._requirewidthupdate){d.jqxDropDownList({width:h.width()})}var c=d.jqxDropDownList("listBox").visibleItems;if(!I.createeditor){if(c.length<8){d.jqxDropDownList("autoDropDownHeight",true)}else{d.jqxDropDownList("autoDropDownHeight",false)}}var K=this.getcellvalue(r,B);var A=this.findRecordIndex(K,B,c);if(M){if(K!=""){d.jqxDropDownList("selectIndex",A,true)}else{d.jqxDropDownList("selectIndex",-1)}}if(!this.editcell){return}if(this.editcell.defaultvalue!=undefined){d.jqxDropDownList("selectIndex",this.editcell.defaultvalue,true)}if(x){d.jqxDropDownList("focus")}}break;case"combobox":if(this.host.jqxComboBox){o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");var B=a.trim(I.displayfield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}if(B.indexOf(".")!=-1){B=B.replace(".","")}var G=t.theme==="fluent"?2:0;var s=this.editors["combobox_"+E];d=s==undefined?a("<div style='border: none; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='comboboxeditor'></div>"):s;d.css("top",G+a(o).parent().position().top);if(this.oldhscroll){d.css("left",-j+G+parseInt(a(o).position().left))}else{d.css("left",G+parseInt(a(o).position().left))}if(I.pinned){d.css("left",G+j+parseInt(a(o).position().left))}if(s==undefined){d.prependTo(this.table);d[0].id="comboboxeditor"+this.element.id+E;var g=this.source._source?true:false;var y=null;if(!g){y=new a.jqx.dataAdapter(this.source,{autoBind:false,uniqueDataFields:[B],async:false,autoSort:true,autoSortField:B})}else{var q={localdata:JSON.parse(JSON.stringify(this.source.records)),datatype:this.source.datatype,async:false};y=new a.jqx.dataAdapter(q,{autoBind:false,async:false,uniqueDataFields:[B],autoSort:true,autoSortField:B})}var v=!I.createeditor?true:false;d.jqxComboBox({hint:false,enableBrowserBoundsDetection:true,keyboardSelection:false,source:y,rtl:this.rtl,autoDropDownHeight:v,theme:this.theme,width:h.width()-G,height:h.height()-G,displayMember:B,valueMember:F});d.removeAttr("tabindex");d.find("div").removeAttr("tabindex");this.editors["combobox_"+E]=d;if(I.createeditor){I.createeditor(r,K,d)}}if(I._requirewidthupdate){d.jqxComboBox({width:h.width()-2})}var c=d.jqxComboBox("listBox").visibleItems;if(!I.createeditor){if(c.length<8){d.jqxComboBox("autoDropDownHeight",true)}else{d.jqxComboBox("autoDropDownHeight",false)}}var K=this.getcellvalue(r,B);var A=this.findRecordIndex(K,B,c);if(M){if(K!=""){d.jqxComboBox("selectIndex",A,true);d.jqxComboBox("val",K)}else{d.jqxComboBox("selectIndex",-1);d.jqxComboBox("val",K)}}if(!this.editcell){return}if(this.editcell.defaultvalue!=undefined){d.jqxComboBox("selectIndex",this.editcell.defaultvalue,true)}if(this.editchar&&this.editchar.length>0){d.jqxComboBox("input").val(this.editchar)}if(x){setTimeout(function(){m(d.jqxComboBox("input"));if(d){d.jqxComboBox("_setSelection",0,0);if(t.editchar){d.jqxComboBox("_setSelection",1,1);t.editchar=null}else{if(d.jqxComboBox("input")){var e=d.jqxComboBox("input").val();d.jqxComboBox("_setSelection",0,e.length)}}}},10)}}break;case"datetimeinput":if(this.host.jqxDateTimeInput){o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}var G=t.theme==="fluent"?2:0;var w=this.editors["datetimeinput_"+E];d=w==undefined?a("<div style='border: none; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='datetimeeditor'></div>"):w;d.show();d.css("top",G+a(o).parent().position().top);if(this.oldhscroll){d.css("left",G-j+parseInt(a(o).position().left))}else{d.css("left",G+parseInt(a(o).position().left))}if(I.pinned){d.css("left",G+j+parseInt(a(o).position().left))}if(w==undefined){d.prependTo(this.table);d[0].id="datetimeeditor"+this.element.id+E;var H={calendar:this.gridlocalization};d.jqxDateTimeInput({hint:false,firstDayOfWeek:this.gridlocalization.firstDay,enableBrowserBoundsDetection:true,localization:H,_editor:true,theme:this.theme,rtl:this.rtl,width:h.width()-G,height:h.height()-4*G,formatString:I.cellsformat});this.editors["datetimeinput_"+E]=d;if(I.createeditor){I.createeditor(r,K,d)}}if(I._requirewidthupdate){d.jqxDateTimeInput({width:h.width()})}if(M){if(K!=""&&K!=null){var N=new Date(K);if(N=="Invalid Date"){if(this.source.getvaluebytype){N=this.source.getvaluebytype(K,{name:I.datafield,type:"date"})}}d.jqxDateTimeInput("setDate",N)}else{d.jqxDateTimeInput("setDate",null)}if(this.editcell.defaultvalue!=undefined){d.jqxDateTimeInput("setDate",this.editcell.defaultvalue)}}if(x){var P=this.editEvent;setTimeout(function(){m(d.jqxDateTimeInput("dateTimeInput"));setTimeout(function(){d.jqxDateTimeInput("_selectGroup",0);if(t.editchar&&t.editchar.length>0){var T=parseInt(t.editchar);if(!isNaN(T)){if(P){var e=P.charCode?P.charCode:P.keyCode?P.keyCode:0;d.jqxDateTimeInput("_handleKeyPress",P,e)}}}},25)},10)}}break;case"numberinput":if(this.host.jqxNumberInput){o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}var G=t.theme==="fluent"?2:0;var Q=this.editors["numberinput_"+E];d=Q==undefined?a("<div style='border:none; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='numbereditor'></div>"):Q;d.show();d.css("top",G+a(o).parent().position().top);if(this.oldhscroll){d.css("left",G-j+parseInt(a(o).position().left))}else{d.css("left",G+parseInt(a(o).position().left))}if(I.pinned){d.css("left",G+j+parseInt(a(o).position().left))}if(Q==undefined){d.prependTo(this.table);d[0].id="numbereditor"+this.element.id+E;var n="";var z="left";var O=2;if(I.cellsformat){if(I.cellsformat.indexOf("c")!=-1){n=this.gridlocalization.currencysymbol;z=this.gridlocalization.currencysymbolposition;if(z=="before"){z="left"}else{z="right"}if(I.cellsformat.length>1){O=parseInt(I.cellsformat.substring(1),10)}}else{if(I.cellsformat.indexOf("p")!=-1){n=this.gridlocalization.percentsymbol;z="right";if(I.cellsformat.length>1){O=parseInt(I.cellsformat.substring(1),10)}}}}else{O=0}d.jqxNumberInput({decimalSeparator:this.gridlocalization.decimalseparator,decimalDigits:O,inputMode:"simple",theme:this.theme,rtl:this.rtl,width:h.width()-G,height:h.height()-G,spinButtons:true,symbol:n,symbolPosition:z});this.editors["numberinput_"+E]=d;if(I.createeditor){I.createeditor(r,K,d)}}if(I._requirewidthupdate){d.jqxNumberInput({width:h.width()})}if(M){if(K!=""&&K!=null){var S=K;d.jqxNumberInput("setDecimal",S)}else{d.jqxNumberInput("setDecimal",0)}if(this.editcell.defaultvalue!=undefined){d.jqxNumberInput("setDecimal",this.editcell.defaultvalue)}if(this.editchar&&this.editchar.length>0){var p=parseInt(this.editchar);if(!isNaN(p)){d.jqxNumberInput("setDecimal",p)}}if(x){setTimeout(function(){m(d.jqxNumberInput("numberInput"));d.jqxNumberInput("_setSelectionStart",0);if(t.editchar){if(I.cellsformat.length>0&&I.cellsformat.indexOf("c")>=0){d.jqxNumberInput("_setSelectionStart",2)}else{d.jqxNumberInput("_setSelectionStart",1)}t.editchar=null}else{var e=d.jqxNumberInput("spinButtons");if(e){var T=d.jqxNumberInput("numberInput").val();t._setSelection(d.jqxNumberInput("numberInput")[0],T.length,T.length)}else{var T=d.jqxNumberInput("numberInput").val();t._setSelection(d.jqxNumberInput("numberInput")[0],0,T.length)}d.jqxNumberInput("selectAll")}},10)}}}break;case"rating":if(this.host.jqxRating){o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}var R=this.editors["rating_"+E];d=R==undefined?a("<div style='margin: 5px;white-space: nowrap; overflow: auto; border:none; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='ratingEditor'></div>"):R;d.show();d.css("top",a(o).parent().position().top);if(this.oldhscroll){d.css("left",-j+parseInt(a(o).position().left))}else{d.css("left",parseInt(a(o).position().left))}if(I.pinned){d.css("left",j+parseInt(a(o).position().left))}if(R==undefined){d.prependTo(this.table);d[0].id="ratingEditor"+this.element.id+E;d.jqxRating({theme:this.theme,width:h.width(),height:h.height()});this.editors["numberinput_"+E]=d;if(I.createeditor){I.createeditor(r,K,d)}}if(I._requirewidthupdate){d.jqxRating({width:h.width()})}if(M){if(K!=""&&K!=null){var S=K;d.jqxRating("val",S)}else{d.jqxRating("val",0)}if(this.editcell.defaultvalue!=undefined){d.jqxRating("val",this.editcell.defaultvalue)}}}break;case"custom":o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}var C=this.editors["customeditor_"+E+"_"+r];d=C==undefined?a("<div style='overflow: hidden; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='customeditor'></div>"):C;d.show();d.css("top",a(o).parent().position().top);if(this.oldhscroll){d.css("left",-j+parseInt(a(o).position().left))}else{d.css("left",parseInt(a(o).position().left))}if(I.pinned){d.css("left",j+parseInt(a(o).position().left))}if(C==undefined){d.prependTo(this.table);d[0].id="customeditor"+this.element.id+E+"_"+r;this.editors["customeditor_"+E+"_"+r]=d;var b=h.width()-1;var f=h.height()-1;d.width(b);d.height(f);if(I.createeditor){I.createeditor(r,K,d,D,b,f,this.editchar)}}if(I._requirewidthupdate){d.width(h.width())}break;case"template":o.innerHTML="";var E=a.trim(I.datafield).split(" ").join("");if(E.indexOf(".")!=-1){E=E.replace(".","")}var i=this.editors["templateeditor_"+E];d=i==undefined?a("<div style='overflow: hidden; border-radius: 0px; -moz-border-radius: 0px; -webkit-border-radius: 0px; z-index: 799; top: 0px; left: 0px; position: absolute;' id='templateeditor'></div>"):i;d.show();d.css("top",a(o).parent().position().top);if(this.oldhscroll){d.css("left",-j+parseInt(a(o).position().left))}else{d.css("left",parseInt(a(o).position().left))}if(I.pinned){d.css("left",j+parseInt(a(o).position().left))}if(i==undefined){d.prependTo(this.table);d[0].id="templateeditor"+this.element.id+E;this.editors["templateeditor_"+E]=d;var b=h.width()-1;var f=h.height()-1;d.width(b);d.height(f);if(I.createeditor){I.createeditor(r,K,d,D,b,f,this.editchar)}}if(I._requirewidthupdate){d.width(h.width())}break;case"textbox":default:o.innerHTML="";d=this.editors["textboxeditor_"+I.datafield]||a("<input autocomplete='off' style='padding-left: 4px; padding-right:4px; border: none; border-color: transparent;' autocorrect='off' autocapitalize='off' spellcheck='false' type='textbox' id='textboxeditor'/>");d[0].id="textboxeditor"+this.element.id+I.datafield;d.appendTo(h);if(this.rtl){d.css("direction","rtl")}if(M||d[0].className==""){d.addClass(this.toThemeProperty("jqx-input"));d.addClass(this.toThemeProperty("jqx-widget-content"));if(this.editchar&&this.editchar.length>0){d.val(this.editchar)}else{if(I.cellsformat!=""){K=this.getcelltext(r,F)}if(K==undefined){K=""}d.val(K)}if(this.editcell.defaultvalue!=undefined){d.val(this.editcell.defaultvalue)}d.width(h.width()+1);d.height(h.height()+1);if(I.createeditor){I.createeditor(r,K,d)}if(I.cellsformat!=""){if(I.cellsformat.indexOf("p")!=-1||I.cellsformat.indexOf("c")!=-1||I.cellsformat.indexOf("n")!=-1||I.cellsformat.indexOf("f")!=-1){if(!this.editors["textboxeditor_"+I.datafield]){d.keydown(function(T){var Z=T.charCode?T.charCode:T.keyCode?T.keyCode:0;var W=String.fromCharCode(Z);var X=parseInt(W);if(isNaN(X)){return true}if(t._selection(d).length>0){return true}var V="";var U=d.val();if(I.cellsformat.length>1){var Y=parseInt(I.cellsformat.substring(1));if(isNaN(Y)){Y=0}}else{var Y=0}if(Y>0){if(U.indexOf(t.gridlocalization.decimalseparator)!=-1){if(t._selection(d).start>U.indexOf(t.gridlocalization.decimalseparator)){return true}}}for(var aa=0;aa<U.length-Y;aa++){var e=U.substring(aa,aa+1);if(e.match(/^[0-9]+$/)!=null){V+=e}}if(V.length>=11){return false}})}}}}this.editors["textboxeditor_"+I.datafield]=d;if(M){if(x){setTimeout(function(){m(d);if(t.editchar){t._setSelection(d[0],1,1);t.editchar=null}else{t._setSelection(d[0],0,d.val().length)}},25)}}break}if(d){d.addClass("jqx-grid-cell-edit");d[0].style.zIndex=1+parseInt(o.style.zIndex);if(a.jqx.browser.msie&&a.jqx.browser.version<8){d[0].style.zIndex=1+this.columns.records.length+o.style.zIndex}d.css("display","block");this.editcell.editor=d;if(!this.editcell[F]){this.editcell[F]={};this.editcell[F].editor=d}else{this.editcell[F].editor=d}}if(M){if(I.initeditor){I.initeditor(r,K,d,D,this.editchar)}}if(t.isTouchDevice()){return}setTimeout(function(){if(t.content){t.content[0].scrollTop=0;t.content[0].scrollLeft=0}if(t.gridcontent){t.gridcontent[0].scrollLeft=0;t.gridcontent[0].scrollTop=0}},10)},_setSelection:function(d,g,b){try{if("selectionStart" in d){d.setSelectionRange(g,b)}else{var c=d.createTextRange();c.collapse(true);c.moveEnd("character",b);c.moveStart("character",g);c.select()}}catch(e){var f=e}},_hideeditors:function(){if(this.editcells!=null){var b=this;for(var c in this.editcells){b.editcell=b.editcells[c];b._hidecelleditor()}}},_hidecelleditor:function(b){if(!this.editcell){return}if(this.editmode==="selectedrow"){for(var c=0;c<this.columns.records.length;c++){var e=this.columns.records[c];if(this.editcell[e.datafield]&&this.editcell[e.datafield].editor){this.editcell[e.datafield].editor.hide();var d=this.editcell[e.datafield].editor;switch(e.columntype){case"dropdownlist":d.jqxDropDownList({closeDelay:0});d.jqxDropDownList("hideListBox");d.jqxDropDownList({closeDelay:300});break;case"combobox":d.jqxComboBox({closeDelay:0});d.jqxComboBox("hideListBox");d.jqxComboBox({closeDelay:300});break;case"datetimeinput":if(d.jqxDateTimeInput("isOpened")){d.jqxDateTimeInput({closeDelay:0});d.jqxDateTimeInput("hideCalendar");d.jqxDateTimeInput({closeDelay:300})}break}}}if(this.validationpopup){this.validationpopup.hide();this.validationpopuparrow.hide()}return}if(this.editcell.columntype=="checkbox"){return}if(this.editcell.editor){this.editcell.editor.hide();switch(this.editcell.columntype){case"dropdownlist":this.editcell.editor.jqxDropDownList({closeDelay:0});this.editcell.editor.jqxDropDownList("hideListBox");this.editcell.editor.jqxDropDownList({closeDelay:300});break;case"combobox":this.editcell.editor.jqxComboBox({closeDelay:0});this.editcell.editor.jqxComboBox("hideListBox");this.editcell.editor.jqxComboBox({closeDelay:300});break;case"datetimeinput":var f=this.editcell.editor;if(f.jqxDateTimeInput("isOpened")){f.jqxDateTimeInput({closeDelay:0});f.jqxDateTimeInput("hideCalendar");f.jqxDateTimeInput({closeDelay:300})}break}}if(this.validationpopup){this.validationpopup.hide();this.validationpopuparrow.hide()}if(!this.isNestedGrid){if(b!=false){this.element.focus()}}},_geteditorvalue:function(f){var m=new String();if(!this.editcell){return null}var i=this.editcell.editor;if(this.editmode=="selectedrow"){if(this.editcell[f.datafield]){var i=this.editcell[f.datafield].editor}}if(i){switch(f.columntype){case"textbox":default:m=i.val();if(f.cellsformat!=""){var h="string";var s=this.source.datafields||((this.source._source)?this.source._source.datafields:null);if(s){var l="";a.each(s,function(){if(this.name==f.displayfield){if(this.type){l=this.type}return false}});if(l){h=l}}var c=h==="number"||h==="float"||h==="int"||h==="integer";var r=h==="date"||h==="time";if(c||(h==="string"&&(f.cellsformat.indexOf("p")!=-1||f.cellsformat.indexOf("c")!=-1||f.cellsformat.indexOf("n")!=-1||f.cellsformat.indexOf("f")!=-1))){if(m===""&&f.nullable){return""}if(m.indexOf&&m.indexOf(this.gridlocalization.currencysymbol)>-1){m=m.replace(this.gridlocalization.currencysymbol,"")}var b=function(y,w,x){var t=y;if(w==x){return y}var v=t.indexOf(w);while(v!=-1){t=t.replace(w,x);v=t.indexOf(w)}return t};var q=m;q=new Number(q);if(!isNaN(q)){return q}m=b(m,this.gridlocalization.thousandsseparator,"");m=m.replace(this.gridlocalization.decimalseparator,".");if(m.indexOf(this.gridlocalization.percentsymbol)>-1){m=m.replace(this.gridlocalization.percentsymbol,"")}var u="";for(var k=0;k<m.length;k++){var j=m.substring(k,k+1);if(j==="-"){u+="-"}if(j==="."){u+="."}if(j.match(/^[0-9]+$/)!=null){u+=j}}m=u;m=m.replace(/ /g,"");m=new Number(m);if(isNaN(m)){m=""}}if(r||(h==="string"&&(f.cellsformat.indexOf("H")!=-1||f.cellsformat.indexOf("m")!=-1||f.cellsformat.indexOf("M")!=-1||f.cellsformat.indexOf("y")!=-1||f.cellsformat.indexOf("h")!=-1||f.cellsformat.indexOf("d")!=-1))){if(m===""&&f.nullable){return""}var d=m;if(a.jqx.dataFormat){m=a.jqx.dataFormat.tryparsedate(d,this.gridlocalization)}if(m=="Invalid Date"||m==null){m=""}}}if(f.displayfield!=f.datafield){m={label:m,value:m}}break;case"checkbox":if(i.jqxCheckBox){m=i.jqxCheckBox("checked")}break;case"datetimeinput":if(i.jqxDateTimeInput){i.jqxDateTimeInput({isEditing:false});if(i.jqxDateTimeInput("container").css("display")!=="none"){i.jqxDateTimeInput("close")}i.jqxDateTimeInput("_validateValue");m=i.jqxDateTimeInput("getDate");if(m==null){return null}m=new Date(m.toString());if(f.displayfield!=f.datafield){m={label:m,value:m}}}break;case"dropdownlist":if(i.jqxDropDownList){var g=i.jqxDropDownList("selectedIndex");var p=i.jqxDropDownList("listBox").getVisibleItem(g);if(f.displayfield!=f.datafield){if(p){m={label:p.label,value:p.value}}else{m=""}}else{if(p){m=p.label}else{m=""}}if(m==null){m=""}}break;case"combobox":if(i.jqxComboBox){m=i.jqxComboBox("val");if(f.displayfield!=f.datafield){var p=i.jqxComboBox("getSelectedItem");if(p!=null){m={label:p.label,value:p.value}}}if(m==null){m=""}}break;case"numberinput":if(i.jqxNumberInput){if(this.touchdevice){i.jqxNumberInput("_doTouchHandling")}var e=i.jqxNumberInput("getDecimal");m=new Number(e);m=parseFloat(m);if(isNaN(m)){m=0}if(e===null){m=null}var o=i.jqxNumberInput("max");var n=i.jqxNumberInput("min");m=Math.min(o,Math.max(n,m));if(f.displayfield!=f.datafield){m={label:m,value:m}}}break}if(f.geteditorvalue){if(this.editmode=="selectedrow"){m=f.geteditorvalue(this.editcell.row,this.getcellvalue(this.editcell.row,f.datafield),i)}else{m=f.geteditorvalue(this.editcell.row,this.editcell.value,i)}}}return m},hidevalidationpopups:function(){if(this.popups){a.each(this.popups,function(){this.validation.remove();this.validationrow.remove()});this.popups=new Array()}if(this.validationpopup){this.validationpopuparrow.hide();this.validationpopup.hide()}},showvalidationpopup:function(f,j,h){if(h==undefined){var h=this.gridlocalization.validationstring}var g=a("<div style='z-index: 99999; top: 0px; left: 0px; position: absolute;'></div>");var r=a("<div style='width: 20px; height: 20px; z-index: 999999; top: 0px; left: 0px; position: absolute;'></div>");g.html(h);r.addClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));g.addClass(this.toThemeProperty("jqx-grid-validation"));g.addClass(this.toThemeProperty("jqx-rc-all"));g.prependTo(this.table);r.prependTo(this.table);var l=this.hScrollInstance;var n=l.value;var d=parseInt(n);var b=this.getcolumn(j).uielement;var p=null;for(var o=0;o<this.hittestinfo.length;o++){if(f===this.hittestinfo[o].row.visibleindex){p=this.hittestinfo[o]}}if(!p){this.ensurerowvisible(f);var s=this;g.remove();r.remove();setTimeout(function(){var v=null;for(var u=0;u<s.hittestinfo.length;u++){if(f===s.hittestinfo[u].row.visibleindex){v=s.hittestinfo[u]}}if(v){s.showvalidationpopup(f,j,h)}},25);return}var c=a(p.visualrow);g.css("top",parseInt(c.position().top)+30+"px");var k=parseInt(g.css("top"));r.css("top",k-12);r.removeClass();r.addClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));var q=false;if(k>=this._gettableheight()-this.rowsheight){r.removeClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));r.addClass(this.toThemeProperty("jqx-grid-validation-arrow-down"));k=parseInt(c.position().top)-this.rowsheight;if(k<0){k=0;this.validationpopuparrow.removeClass(this.toThemeProperty("jqx-grid-validation-arrow-down"));q=true}g.css("top",k+"px");r.css("top",k+g.outerHeight()-9)}var t=-d+parseInt(a(b).position().left);if(b[0].offsetHeight===0){t=-d+parseInt(b[0].style.left)}r.css("left",d+t+30);var m=g.width();if(m+t>this.host.width()-20){var e=m+t-this.host.width()+40;t-=e}if(!q){g.css("left",d+t)}else{g.css("left",d+parseInt(a(b).position().left)-g.outerWidth())}g.show();r.show();if(!this.popups){this.popups=new Array()}this.popups[this.popups.length]={validation:g,validationrow:r}},_showvalidationpopup:function(p,e,q){var c=this.editcell;var k=this.editcell.editor;if(this.editmode=="selectedrow"){var c=this.editcell[e];if(c&&c.editor){k=c.editor;c.element=k}}if(!k){return}if(this.validationpopup&&a.jqx.isHidden(this.validationpopup)){if(this.validationpopup.remove){this.validationpopup.remove();this.validationpopuparrow.remove()}this.validationpopup=null;this.validationpopuparrow=null;if(e===undefined&&q===undefined&&this.editors&&this.editors.length===0){return}}if(!this.validationpopup){var n=a("<div style='z-index: 99999; top: 0px; left: 0px; position: absolute;'></div>");var m=a("<div style='width: 20px; height: 20px; z-index: 999999; top: 0px; left: 0px; position: absolute;'></div>");n.html(q);m.addClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));n.addClass(this.toThemeProperty("jqx-grid-validation"));n.addClass(this.toThemeProperty("jqx-rc-all"));n.prependTo(this.table);m.prependTo(this.table);this.validationpopup=n;this.validationpopuparrow=m}else{this.validationpopup.html(q)}var h=this.hScrollInstance;var j=h.value;var g=parseInt(j);if(this.editmode=="selectedrow"){if(this.visiblerows&&this.visiblerows[this.editcell.visiblerowindex]){this.validationpopup.css("top",this.visiblerows[this.editcell.visiblerowindex].top+(this.rowsheight+5)+"px")}else{this.validationpopup.css("top",parseInt(a(c.editor).position().top)+(this.rowsheight+5)+"px")}}else{this.validationpopup.css("top",parseInt(a(c.element).parent().position().top)+(this.rowsheight+5)+"px")}var b=parseInt(this.validationpopup.css("top"));this.validationpopuparrow.css("top",b-11);this.validationpopuparrow.removeClass();this.validationpopuparrow.addClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));var o=this._gettableheight();var f=false;if(b>=o){this.validationpopuparrow.removeClass(this.toThemeProperty("jqx-grid-validation-arrow-up"));this.validationpopuparrow.addClass(this.toThemeProperty("jqx-grid-validation-arrow-down"));b=parseInt(a(c.element).parent().position().top)-this.rowsheight-5;if(this.editmode=="selectedrow"){if(this.visiblerows&&this.visiblerows[this.editcell.visiblerowindex]){b=this.visiblerows[this.editcell.visiblerowindex].top-this.rowsheight-5}else{b=parseInt(a(c.editor).position().top)-this.rowsheight-5}}if(b<0){b=0;this.validationpopuparrow.removeClass(this.toThemeProperty("jqx-grid-validation-arrow-down"));f=true}this.validationpopup.css("top",b+"px");this.validationpopuparrow.css("top",b+this.validationpopup.outerHeight()-9)}var l=-g+parseInt(a(c.element).offset().left);this.validationpopuparrow.css("left",g+l+30);var d=this.validationpopup.width();if(d+l>this.host.width()-20){var i=d+l-this.host.width()+40;l-=i}if(!f){this.validationpopup.css("left",g+l)}else{this.validationpopup.css("left",g+parseInt(a(c.element).position().left)-this.validationpopup.outerWidth())}if(this.editcell.editor.css("display")=="none"){this.validationpopup.hide();this.validationpopuparrow.hide()}else{this.validationpopup.show();this.validationpopuparrow.show()}}})})(jqxBaseFramework)})();

