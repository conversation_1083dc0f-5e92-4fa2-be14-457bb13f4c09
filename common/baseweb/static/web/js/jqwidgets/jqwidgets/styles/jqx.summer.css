﻿.jqx-widget-summer{border-color: #cacaca;}
.jqx-fill-state-normal-summer, .jqx-widget-header-summer{ border-color: #cacaca; background-color:#e4e5e5;}
.jqx-fill-state-hover-summer, .jqx-scrollbar-thumb-state-normal-summer, .jqx-scrollbar-thumb-state-normal-horizontal-summer{ border-color:#222;  background-color:#888888; color: #fff;}
.jqx-fill-state-pressed-summer{border-color: #fe5815; background-color: #fe5815; color: #fff; }
.jqx-fill-state-focus-summer { border-color: #555; }

.jqx-scrollbar-state-normal-summer, .jqx-grid-bottomright-summer, .jqx-panel-bottomright-summer, .jqx-listbox-bottomright-summer{background-color: #e4e5e5;}
.jqx-scrollbar-button-state-pressed, .jqx-scrollbar-button-state-hover{background-color: #e4e5e5; border-color: #222;}
/*icons*/
.jqx-menu-item-arrow-up-selected-summer, .jqx-icon-arrow-up-hover-summer, .jqx-icon-arrow-up-selected-summer{background-image:url('images/icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-summer, .jqx-icon-arrow-down-hover-summer, .jqx-icon-arrow-down-selected-summer{background-image:url('images/icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-summer, .jqx-icon-arrow-left-hover-summer, .jqx-icon-arrow-left-selected-summer{background-image:url('images/icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-summer, .jqx-icon-arrow-right-hover-summer, .jqx-icon-arrow-right-selected-summer{background-image:url('images/icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-hover-summer, .jqx-tabs-close-button-selected-summer{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-scrollbar-summer .jqx-icon-arrow-up-selected-summer, .jqx-scrollbar-summer .jqx-icon-arrow-up-hover-summer{background-image:url('images/icon-up.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-summer .jqx-icon-arrow-down-selected-summer, .jqx-scrollbar-summer .jqx-icon-arrow-down-hover-summer{background-image:url('images/icon-down.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-summer .jqx-icon-arrow-left-selected-summer, .jqx-scrollbar-summer .jqx-icon-arrow-left-hover-summer{background-image:url('images/icon-left.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-summer .jqx-icon-arrow-right-selected-summer, .jqx-scrollbar-summer .jqx-icon-arrow-right-hover-summer{background-image:url('images/icon-right.png');background-repeat:no-repeat; background-position:center;}
.jqx-grid-cell-summer.jqx-grid-cell-selected-summer>.jqx-grid-group-expand-summer,
.jqx-grid-cell-summer.jqx-grid-cell-hover-summer>.jqx-grid-group-expand-summer {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-summer.jqx-grid-cell-selected-summer>.jqx-grid-group-collapse-summer,
.jqx-grid-cell-summer.jqx-grid-cell-hover-summer>.jqx-grid-group-collapse-summer {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-summer.jqx-grid-cell-selected-summer>.jqx-grid-group-collapse-rtl-summer,
.jqx-grid-cell-summer.jqx-grid-cell-hover-summer>.jqx-grid-group-collapse-rtl-summer {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-summer.jqx-grid-cell-selected-summer>.jqx-grid-group-expand-rtl-summer,
.jqx-grid-cell-summer.jqx-grid-cell-hover-summer>.jqx-grid-group-expand-rtl-summer {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-summer
{
    background-image: url('images/icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-summer
{
    background-image: url('images/icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-summer
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-summer
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-summer
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-summer
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-hover-summer, .jqx-icon-calendar-pressed-summer {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-layout-summer
{
    background-color: #cacaca;
}