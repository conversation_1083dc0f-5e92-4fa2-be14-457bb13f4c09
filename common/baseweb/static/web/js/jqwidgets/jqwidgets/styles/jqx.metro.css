﻿.jqx-rc-tl-metro
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-metro
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-metro
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-metro
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-metro
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-metro
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-metro
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-metro
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-metro
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-metro {
    font-size: 12px;
    font-family: 'segoe ui', arial, sans-serif;
}
.jqx-widget-content-metro{font-size: 12px; font-family: 'segoe ui', arial, sans-serif; border-color: #e5e5e5; color: #767676; background-color: #fff;}
.jqx-widget-header-metro{font-size: 12px; font-family: 'segoe ui', arial, sans-serif; color: #767676; border-color:#e5e5e5; background-color:#f4f4f4;}
.jqx-fill-state-normal-metro{font-size: 12px; font-family: 'segoe ui', arial, sans-serif; border-color: #e5e5e5; color: #767676; background: #ffffff;}
.jqx-button-metro {border-color: #e5e5e5;}
.jqx-fill-state-hover-metro{border-color:#dedede; color: #000; background-color:#dedede;

}
.jqx-fill-state-focus-metro { border-color: #cdcdcd; }
.jqx-fill-state-pressed-metro{border-color:#1faeff; color: #fff; background-color:#1faeff;

}
.jqx-input-metro {
    border-color: #e5e5e5;
}
.jqx-scrollbar-state-normal-metro, .jqx-grid-bottomright-metro, .jqx-panel-bottomright-metro, .jqx-listbox-bottomright-metro{background-color:#f0f0f0;}
.jqx-widget-metro .jqx-grid-column-header-metro, .jqx-grid-cell-metro, .jqx-widget-metro .jqx-grid-cell-metro, .jqx-widget-metro .jqx-grid-group-cell-metro, .jqx-grid-group-cell-metro{font-size: 12px; font-family: 'segoe ui', arial, sans-serif; border-color:#f0f0f0;}
.jqx-tabs-title-selected-bottom-metro, .jqx-tabs-selection-tracker-bottom-metro, .jqx-tabs-title-selected-top-metro, .jqx-tabs-selection-tracker-top-metro{color: #767676; border-color:#e5e5e5; border-bottom:1px solid #fff; background:#fff}
.jqx-grid-cell-sort-alt-metro, .jqx-grid-cell-filter-alt-metro, .jqx-grid-cell-pinned-metro, .jqx-grid-cell-alt-metro, .jqx-grid-cell-sort-metro{ background-color:#ededed; color: #000;}
.jqx-menu-vertical-metro{background: #fff; border-color: #e5e5e5;}
.jqx-widget-metro .jqx-grid-cell-metro, .jqx-widget-metro .jqx-grid-column-header-metro, .jqx-widget-metro .jqx-grid-group-cell-metro {color: #000; border-color: #e5e5e5;}
.jqx-widget-metro .jqx-grid-column-menubutton-metro, .jqx-widget-metro .jqx-grid-column-sortascbutton-metro, .jqx-widget-metro .jqx-grid-column-sortdescbutton-metro, .jqx-widget-metro .jqx-grid-column-filterbutton-metro {
    background-color: transparent;
    border-color: #e5e5e5;
}
.jqx-window-header-metro, .jqx-input-button-header-metro, .jqx-calendar-title-header-metro, .jqx-grid-metro .jqx-widget-header-metro, .jqx-grid-header-metro, .jqx-grid-column-header-metro {font-size: 12px; font-family: 'segoe ui', arial, sans-serif; border-color: #e5e5e5; color: #767676; background: #f4f4f4;}
.jqx-grid-column-menubutton-metro {
    background-image: url('images/metro-icon-down.png');
 }
.jqx-widget-metro .jqx-grid-cell-selected-metro, .jqx-grid-cell-selected-metro{ background-color:#1faeff; border-color: #1faeff; font-size: 12px;  color:#fff;}
.jqx-grid-cell-hover-metro{ background-color:#dedede;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-metro {
    background-image: url('images/metro-icon-up.png');
 }
.jqx-grid-column-sortdescbutton-metro {
    background-image: url('images/metro-icon-down.png');
}
.jqx-checkbox-hover-metro {
    background-color: #fff;
}
.jqx-radiobutton-hover-metro {
    background-color: #fff;
}
.jqx-scrollbar-thumb-state-normal-horizontal-metro, .jqx-scrollbar-thumb-state-normal-metro {
    background: #cdcdcd; border-color: #cdcdcd;
}
.jqx-scrollbar-thumb-state-hover-horizontal-metro, .jqx-scrollbar-thumb-state-hover-metro {
    background: #a6a6a6; border-color: #a6a6a6;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-metro, .jqx-scrollbar-thumb-state-pressed-metro {
    background: #606060; border-color: #606060;
}
.jqx-scrollbar-button-state-normal-metro
{
    border: 1px solid #f0f0f0; 
    background: #f0f0f0;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-metro
{
    border: 1px solid #dadada;
    background: #dadada;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-metro
{
    border: 1px solid #606060;
    background: #606060;
}

/*icons*/
.jqx-window-collapse-button-metro
{
    background-image: url(./images/metro-icon-up.png);
}
.jqx-window-collapse-button-collapsed-metro {
  background-image: url(./images/metro-icon-down.png);
}
.jqx-icon-arrow-up-metro, .jqx-expander-arrow-bottom-metro, .jqx-menu-item-arrow-up-metro
{
    background-image: url('images/metro-icon-up.png');
}
.jqx-icon-arrow-down-metro, .jqx-expander-arrow-top-metro, .jqx-tree-item-arrow-expand-metro, .jqx-tree-item-arrow-expand-hover-metro, .jqx-menu-item-arrow-down-metro
{
    background-image: url('images/metro-icon-down.png');
}
.jqx-icon-arrow-left-metro, .jqx-menu-item-arrow-left-metro
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-icon-arrow-right-metro, .jqx-menu-item-arrow-right-metro, .jqx-tree-item-arrow-collapse-metro, .jqx-tree-item-arrow-collapse-hover-metro
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-tabs-arrow-left-metro, .jqx-tree-item-arrow-collapse-rtl-metro, .jqx-tree-item-arrow-collapse-hover-rtl-metro
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-tabs-arrow-right-metro
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-menu-item-arrow-up-selected-metro, .jqx-icon-arrow-up-selected-metro{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-metro, .jqx-icon-arrow-down-selected-metro{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-metro, .jqx-icon-arrow-left-selected-metro{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-metro, .jqx-icon-arrow-right-selected-metro{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-metro{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-selected-metro{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-hover-metro{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-scrollbar-metro .jqx-icon-arrow-up-selected-metro{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metro .jqx-icon-arrow-down-selected-metro{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metro .jqx-icon-arrow-left-selected-metro{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metro .jqx-icon-arrow-right-selected-metro{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}
.jqx-slider-slider-metro
{
    border-color:#cdcdcd;
}
.jqx-slider-button-metro
{
    -moz-border-radius: 9px;
    -webkit-border-radius: 9px;
    border-radius: 9px;
    border-color: #cdcdcd;
}
.jqx-input-button-content-metro
{  
    font-size: 10px;
}
.jqx-dropdownlist-state-normal-metro, .jqx-dropdownlist-state-hover-metro, .jqx-dropdownlist-state-selected-metro,
.jqx-scrollbar-button-state-hover-metro, .jqx-scrollbar-button-state-normal-metro, .jqx-scrollbar-button-state-pressed-metro,
.jqx-scrollbar-thumb-state-normal-horizontal-metro, .jqx-scrollbar-thumb-state-hover-horizontal-metro, .jqx-scrollbar-thumb-state-pressed-horizontal-metro,
.jqx-scrollbar-thumb-state-normal-metro, .jqx-scrollbar-thumb-state-pressed-metro, .jqx-button-metro, .jqx-tree-item-hover-metro, .jqx-tree-item-selected-metro,
.jqx-tree-item-metro, .jqx-menu-item-metro, .jqx-menu-item-hover-metro, .jqx-menu-item-selected-metro, .jqx-menu-item-top-metro, .jqx-menu-item-top-hover-metro, 
.jqx-menu-item-top-selected-metro, .jqx-slider-button-metro, .jqx-slider-slider-metro
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-metro {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #a6a6a6;
}
.jqx-switchbutton-thumb-metro {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-metro {
    background: #1faeff;
    color: #1faeff;
}
.jqx-switchbutton-label-off-metro {
    background: #a6a6a6;
    color: #a6a6a6;
}

.jqx-switchbutton-wrapper-metro {
}
.jqx-grid-cell-metro.jqx-grid-cell-selected-metro>.jqx-grid-group-expand-metro {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metro.jqx-grid-cell-selected-metro>.jqx-grid-group-collapse-metro{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metro.jqx-grid-cell-selected-metro>.jqx-grid-group-collapse-rtl-metro {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metro.jqx-grid-cell-selected-metro>.jqx-grid-group-expand-rtl-metro{
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-metro {
    background-image: url(./images/metro-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-metro
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-metro, .jqx-grid-group-expand-rtl-metro
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-metro
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-metro
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-metro
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-metro
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-metro
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-metro
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-collapse-button-metro {
    margin-top: 1px;
}
.jqx-icon-calendar-pressed-metro {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-layout-metro
{
    background-color: #e5e5e5;
}

/*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-hour-container-metro:focus {
    outline: 1px solid rgb(31, 174, 255) !important;
}
.jqx-time-picker .jqx-header .jqx-minute-container-metro:focus {
    outline: 1px solid rgb(31, 174, 255) !important;
}
.jqx-time-picker .jqx-header .jqx-am-container-metro:focus {
    outline: 1px solid rgb(31, 174, 255) !important;
}
.jqx-time-picker .jqx-header .jqx-pm-container-metro:focus {
    outline: 1px solid rgb(31, 174, 255) !important;
}
.jqx-svg-picker-metro {
	border: 1px solid rgb(31, 174, 255);
}
.jqx-svg-picker-metro:focus {
	border: 1px solid rgb(31, 174, 255) !important;
}
.jqx-needle-central-circle-metro {
	fill: rgb(31, 174, 255);
}
.jqx-needle-metro {
	fill: rgb(31, 174, 255);
}
