﻿
.jqx-widget-glacier {
    font-size: 12px;
    font-family: 'segoe ui', arial, sans-serif;
}
.jqx-widget-content-glacier{font-family: 'segoe ui', arial, sans-serif; border-color: #d7d7d7; color: #131619; background-color: #fff;}
.jqx-widget-header-glacier{font-family: 'segoe ui', arial, sans-serif; color: #131619; border-color:#d7d7d7; background-color:#ebebeb;}
.jqx-fill-state-normal-glacier{font-family: 'segoe ui', arial, sans-serif; border-color: #d7d7d7; color: #131619; background: #ffffff;}
.jqx-fill-state-hover-glacier{border-color:#dedede; color: #131619; background-color:#dedede;

}
.jqx-fill-state-focus-glacier { border-color: #cdcdcd; }
.jqx-fill-state-pressed-glacier{border-color:#006aae; color: #fff; background-color:#006aae;}
.jqx-fill-state-pressed-glacier.jqx-calendar-cell-othermonth-glacier {color: #f7f3f2 !important;}
.jqx-fill-state-pressed-glacier.jqx-grid-pager-number-glacier {color: #f7f3f2 !important;}
.jqx-buttongroup-glacier .jqx-fill-state-pressed{border-color:#d7d7d7;}
.jqx-listitem-state-group-glacier {
    color: #006aae;
    padding-left:6px;
}

.jqx-input-glacier {
    border-color: #d7d7d7;
}
.jqx-scrollbar-state-normal-glacier, .jqx-grid-bottomright-glacier, .jqx-panel-bottomright-glacier, .jqx-listbox-bottomright-glacier{background-color:#f4f4f4;}
.jqx-widget-glacier .jqx-grid-column-header-glacier, .jqx-grid-cell-glacier, .jqx-widget-glacier .jqx-grid-cell-glacier, .jqx-widget-glacier .jqx-grid-group-cell-glacier, .jqx-grid-group-cell-glacier{font-family: 'segoe ui', arial, sans-serif; border-color:#d7d7d7;}
.jqx-tabs-title-selected-bottom-glacier, .jqx-tabs-selection-tracker-bottom-glacier, .jqx-tabs-title-selected-top-glacier, .jqx-tabs-selection-tracker-top-glacier{color: #131619; border-color:#d7d7d7; border-bottom:1px solid #fff; background:#fff}
.jqx-grid-cell-sort-alt-glacier, .jqx-grid-cell-filter-alt-glacier, .jqx-grid-cell-pinned-glacier, .jqx-grid-cell-alt-glacier, .jqx-grid-cell-sort-glacier{ background-color:#ededed; color: #131619;}
.jqx-menu-vertical-glacier{background: #fff; border-color: #bcb6b8;}
.jqx-widget-glacier .jqx-grid-cell-glacier, .jqx-widget-glacier .jqx-grid-column-header-glacier, .jqx-widget-glacier .jqx-grid-group-cell-glacier {color: #131619; border-color: #d7d7d7;}
.jqx-widget-glacier .jqx-grid-column-menubutton-glacier, .jqx-widget-glacier .jqx-grid-column-sortascbutton-glacier, .jqx-widget-glacier .jqx-grid-column-sortdescbutton-glacier, .jqx-widget-glacier .jqx-grid-column-filterbutton-glacier {
    background-color: transparent;
    border-color: #d7d7d7;
}
.jqx-window-header-glacier, .jqx-input-button-header-glacier, .jqx-calendar-title-header-glacier, .jqx-grid-glacier .jqx-widget-header-glacier, .jqx-grid-header-glacier, .jqx-grid-column-header-glacier {font-family: 'segoe ui', arial, sans-serif; border-color:#d7d7d7; background-color:#ebebeb; color: #131619;}
.jqx-grid-column-menubutton-glacier {
    background-image: url('images/metro-icon-down.png');
 }
.jqx-widget-glacier .jqx-grid-cell-selected-glacier, .jqx-grid-cell-selected-glacier{ background-color:#006aae; border-color: #006aae;  color:#fff;}
.jqx-grid-cell-hover-glacier{ background-color:#dedede;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-glacier {
    background-image: url('images/metro-icon-up.png');
 }
.jqx-grid-column-sortdescbutton-glacier {
    background-image: url('images/metro-icon-down.png');
}
.jqx-checkbox-hover-glacier {
    background-color: #fff;
}
.jqx-radiobutton-hover-glacier {
    background-color: #fff;
}
.jqx-scrollbar-thumb-state-normal-horizontal-glacier, .jqx-scrollbar-thumb-state-normal-glacier {
    background: #cdcdcd; border-color: #cdcdcd;
}
.jqx-scrollbar-thumb-state-hover-horizontal-glacier, .jqx-scrollbar-thumb-state-hover-glacier {
    background: #a6a6a6; border-color: #a6a6a6;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-glacier, .jqx-scrollbar-thumb-state-pressed-glacier {
    background: #606060; border-color: #606060;
}
.jqx-scrollbar-button-state-normal-glacier
{
    border: 1px solid #f4f4f4; 
    background: #f4f4f4;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-glacier
{
    border: 1px solid #dadada;
    background: #dadada;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-glacier
{
    border: 1px solid #606060;
    background: #606060;
}

/*icons*/
.jqx-window-collapse-button-glacier
{
    background-image: url(./images/metro-icon-up.png);
}
.jqx-window-collapse-button-collapsed-glacier {
  background-image: url(./images/metro-icon-down.png);
}
.jqx-icon-arrow-up-glacier, .jqx-expander-arrow-bottom-glacier, .jqx-menu-item-arrow-up-glacier
{
    background-image: url('images/metro-icon-up.png');
}
.jqx-icon-arrow-down-glacier, .jqx-expander-arrow-top-glacier, .jqx-tree-item-arrow-expand-glacier, .jqx-tree-item-arrow-expand-hover-glacier, .jqx-menu-item-arrow-down-glacier
{
    background-image: url('images/metro-icon-down.png');
}
.jqx-icon-arrow-left-glacier, .jqx-menu-item-arrow-left-glacier
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-icon-arrow-right-glacier, .jqx-menu-item-arrow-right-glacier, .jqx-tree-item-arrow-collapse-glacier, .jqx-tree-item-arrow-collapse-hover-glacier
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-tabs-arrow-left-glacier, .jqx-tree-item-arrow-collapse-rtl-glacier, .jqx-tree-item-arrow-collapse-hover-rtl-glacier
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-tabs-arrow-right-glacier
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-menu-item-arrow-up-selected-glacier, .jqx-icon-arrow-up-selected-glacier{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-glacier, .jqx-icon-arrow-down-selected-glacier{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-glacier, .jqx-icon-arrow-left-selected-glacier{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-glacier, .jqx-icon-arrow-right-selected-glacier{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-glacier{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-selected-glacier{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-hover-glacier{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-scrollbar-glacier .jqx-icon-arrow-up-selected-glacier{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-glacier .jqx-icon-arrow-down-selected-glacier{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-glacier .jqx-icon-arrow-left-selected-glacier{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-glacier .jqx-icon-arrow-right-selected-glacier{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}
.jqx-slider-slider-glacier
{
    border-color:#cdcdcd;
}
.jqx-slider-button-glacier
{
    -moz-border-radius: 9px;
    -webkit-border-radius: 9px;
    border-radius: 9px;
    border-color: #cdcdcd;
}
.jqx-input-button-content-glacier
{  
    font-size: 10px;
}
.jqx-dropdownlist-state-normal-glacier, .jqx-dropdownlist-state-hover-glacier, .jqx-dropdownlist-state-selected-glacier,
.jqx-scrollbar-button-state-hover-glacier, .jqx-scrollbar-button-state-normal-glacier, .jqx-scrollbar-button-state-pressed-glacier,
.jqx-scrollbar-thumb-state-normal-horizontal-glacier, .jqx-scrollbar-thumb-state-hover-horizontal-glacier, .jqx-scrollbar-thumb-state-pressed-horizontal-glacier,
.jqx-scrollbar-thumb-state-normal-glacier, .jqx-scrollbar-thumb-state-pressed-glacier, .jqx-button-glacier, .jqx-tree-item-hover-glacier, .jqx-tree-item-selected-glacier,
.jqx-tree-item-glacier, .jqx-menu-item-glacier, .jqx-menu-item-hover-glacier, .jqx-menu-item-selected-glacier, .jqx-menu-item-top-glacier, .jqx-menu-item-top-hover-glacier, 
.jqx-menu-item-top-selected-glacier, .jqx-slider-button-glacier, .jqx-slider-slider-glacier
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-glacier {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #a6a6a6;
}
.jqx-switchbutton-thumb-glacier {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-glacier {
    background: #006aae;
    color: #006aae;
}
.jqx-switchbutton-label-off-glacier {
    background: #a6a6a6;
    color: #a6a6a6;
}

.jqx-switchbutton-wrapper-glacier {
}
.jqx-grid-cell-glacier.jqx-grid-cell-selected-glacier>.jqx-grid-group-expand-glacier {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-glacier.jqx-grid-cell-selected-glacier>.jqx-grid-group-collapse-glacier{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-glacier.jqx-grid-cell-selected-glacier>.jqx-grid-group-collapse-rtl-glacier {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-glacier.jqx-grid-cell-selected-glacier>.jqx-grid-group-expand-rtl-glacier{
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-glacier {
    background-image: url(./images/metro-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-glacier
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-glacier, .jqx-grid-group-expand-rtl-glacier
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-glacier
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-glacier
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-glacier
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-glacier
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-glacier
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-glacier
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-collapse-button-glacier {
    margin-top: 1px;
}
.jqx-icon-calendar-pressed-glacier {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-pressed-glacier {
    background-image: url('images/icon-time-white.png');
}
 /*applied to the timepicker*/
.jqx-svg-picker-glacier {
	border: 1px solid #4db8ff;
}
.jqx-svg-picker-glacier:hover {
	border: 1px solid #006bb3;
}
.jqx-svg-picker-glacier:focus {
	border: 1px solid #006aae !important;
}
.jqx-label-glacier {
	fill: #006aae;
}
.jqx-needle-glacier {
	fill: #004a73;
}
.jqx-needle-central-circle-glacier {
	fill: #004a73;
}