.jqx-rc-tl-ui-darkness{border-top-left-radius:6px; moz-border-radius-topleft:6px; webkit-border-top-left-radius:6px}
.jqx-rc-tr-ui-darkness{border-top-right-radius:6px; moz-border-radius-topright:6px; webkit-border-top-right-radius:6px}
.jqx-rc-bl-ui-darkness{border-bottom-left-radius:6px; moz-border-radius-bottomleft:6px; webkit-border-bottom-left-radius:6px}
.jqx-rc-br-ui-darkness{border-bottom-right-radius:6px; moz-border-radius-bottomright:6px; webkit-border-bottom-right-radius:6px}
.jqx-rc-t-ui-darkness{border-top-left-radius:6px; border-top-right-radius:6px; moz-border-radius-topleft:6px; moz-border-radius-topright:6px; webkit-border-top-left-radius:6px; webkit-border-top-right-radius:6px}
.jqx-rc-b-ui-darkness{border-bottom-left-radius:6px; border-bottom-right-radius:6px; moz-border-radius-bottomleft:6px; moz-border-radius-bottomright:6px; webkit-border-bottom-left-radius:6px; webkit-border-bottom-right-radius:6px}
.jqx-rc-r-ui-darkness{border-bottom-right-radius:6px; border-top-right-radius:6px; moz-border-radius-bottomright:6px; moz-border-radius-topright:6px; webkit-border-bottom-right-radius:6px; webkit-border-top-right-radius:6px}
.jqx-rc-l-ui-darkness{border-bottom-left-radius:6px; border-top-left-radius:6px; moz-border-radius-bottomleft:6px; moz-border-radius-topleft:6px; webkit-border-bottom-left-radius:6px; webkit-border-top-left-radius:6px}
.jqx-rc-all-ui-darkness{border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-widget-ui-darkness {
    color: inherit;
}
.jqx-grid-column-sortdescbutton-ui-darkness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;background-position:0 -16px; background-image:url(./images/darkness/ui-icons_ffffff_256x240.png);}
.jqx-grid-column-sortascbutton-ui-darkness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_ffffff_256x240.png)}
.jqx-grid-column-menubutton-ui-darkness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_ffffff_256x240.png); border-width:0px}
.jqx-grid-ui-darkness .jqx-widget-header-ui-darkness{border-width:0px !important}
.jqx-tree-item-arrow-expand-ui-darkness, .jqx-tree-item-arrow-expand-hover-ui-darkness{background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-darkness, .jqx-tree-item-arrow-collapse-hover-ui-darkness{background-position:-32px -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-menu-item-arrow-right-ui-darkness, .jqx-menu-item-arrow-right-selected-ui-darkness{background-position:-32px -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-menu-item-arrow-left-ui-darkness, .jqx-menu-item-arrow-left-selected-ui-darkness{background-position:-96px -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-progressbar-ui-darkness .jqx-fill-state-pressed-ui-darkness{background: #626262; border-width:0px; border-right:1px solid #626262; color:#fff; font-weight:bold}
.jqx-progressbar-value-vertical-ui-darkness{border-width:0px !important; border-bottom: 1px solid #626262 !important}
.jqx-tabs-title-ui-darkness{border:1px solid #666; background: #555 url(./images/darkness/ui-bg_glass_20_555555_1x400.png) 50% 50% repeat-x;  color:#fff; font-weight:bold}
.jqx-tabs-header-ui-darkness{margin:2px; border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-tabs-header-bottom-ui-darkness{margin-top:-2px !important; padding-top:2px}
.jqx-tabs-content-ui-darkness{border-width:0px !important}
.jqx-radiobutton-ui-darkness .jqx-fill-state-pressed-ui-darkness{background:#ec8e0c; border:1px solid #fed22f}
.jqx-calendar-cell-ui-darkness{background: #333333 url(./images/darkness/ui-bg_gloss-wave_25_333333_500x100.png) 50% 50% repeat-x; border:1px solid #333; color:#fff; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-darkness{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-darkness{border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-calendar-month-container-ui-darkness{border:none !important}
.jqx-calendar-ui-darkness{padding:2px}
.jqx-calendar-ui-darkness .jqx-icon-arrow-left-ui-darkness{background-image:url(./images/darkness/ui-icons_ffffff_256x240.png); background-position:-80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-darkness .jqx-icon-arrow-right-ui-darkness{background-image:url(./images/darkness/ui-icons_ffffff_256x240.png); background-position:-48px -192px; width:16px; height:16px; right:5px; position:relative}
.jqx-calendar-ui-darkness .jqx-icon-arrow-left-hover-ui-darkness{background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); background-position:-80px -192px}
.jqx-calendar-ui-darkness .jqx-icon-arrow-right-hover-ui-darkness{background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); background-position:-48px -192px}
.jqx-navigationbar-ui-lighness{overflow: auto;}
.jqx-grid-column-header-ui-darkness{border-width:0px !important}

.jqx-icon-arrow-up-ui-darkness, .jqx-menu-item-arrow-up-ui-darkness, .jqx-menu-item-arrow-up-ui-darkness, .jqx-menu-item-arrow-top-up-ui-darkness, .jqx-icon-arrow-up-ui-darkness, .jqx-icon-arrow-up-hover-ui-darkness, .jqx-icon-arrow-up-selected-ui-darkness{background-position:0 -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-down-ui-darkness, .jqx-tree-item-arrow-expand-ui-darkness, .jqx-tree-item-arrow-expand-hover-ui-darkness, .jqx-menu-item-arrow-down-ui-darkness, .jqx-menu-item-arrow-down-ui-darkness, .jqx-menu-item-arrow-down-ui-darkness{background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-left-ui-darkness, .jqx-tabs-arrow-left-ui-darkness, .jqx-menu-item-arrow-left-ui-darkness, .jqx-menu-item-arrow-top-left{background-position:-96px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-right-ui-darkness, .jqx-tree-item-arrow-collapse-ui-darkness, .jqx-tree-item-arrow-collapse-hover-ui-darkness, .jqx-tabs-arrow-right-ui-darkness, .jqx-menu-item-arrow-right-ui-darkness, .jqx-menu-item-arrow-top-right-ui-darkness{background-position:-32px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-up-hover-ui-darkness{background-position:0 -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-down-hover-ui-darkness{background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-left-selected-ui-darkness{background-position:-96px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-right-selected-ui-darkness{background-position:-32px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-up-selected-ui-darkness{background-position:0 -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-down-selected-ui-darkness{background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-left-selected-ui-darkness, .jqx-tree-item-arrow-collapse-rtl-ui-darkness, .jqx-tree-item-arrow-collapse-hover-rtl-ui-darkness{background-position:-96px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-arrow-right-selected-ui-darkness{background-position:-32px -17px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-icon-close-ui-darkness{background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-darkness{background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-window-ui-darkness{padding: 2px;}
.jqx-window-header-ui-darkness{moz-border-radius:6px; border-radius:6px; webkit-border-radius:6px}
.jqx-window-content-ui-darkness{border-width:0px !important}
.jqx-window-close-button-ui-darkness{background-position:-96px -128px; background-image:url(./images/darkness/ui-icons_ffffff_256x240.png);moz-border-radius:6px; border-radius:6px; webkit-border-radius:6px}
.jqx-window-collapse-button-ui-darkness{background-position:0 -16px; background-image:url(./images/darkness/ui-icons_ffffff_256x240.png)}
.jqx-window-collapse-button-hover-ui-darkness{background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); background-color:#fff; border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-window-collapse-button-collapsed-ui-darkness, .jqx-window-collapse-button-collapsed-hover-ui-darkness{background-position:-65px -16px}
.jqx-window-modal-ui-darkness{}
.jqx-window-close-button-hover-ui-darkness{background-color:#fff; background-position:-96px -128px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png); cursor:pointer; width:16px; height:16px}

.jqx-grid-cell-ui-darkness, .jqx-grid-group-cell-ui-darkness
{
   background: #000;
   color: #fff;
}
.jqx-widget-ui-darkness{ line-height: 17px; font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; font-size:12px; font-style:normal;}
.jqx-widget-content-ui-darkness{font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; background: #000000 url(./images/darkness/ui-bg_inset-soft_25_000000_1x100.png) 50% bottom repeat-x; border-color:#666666; color:#fff; font-size:12px}
.jqx-widget-content-ui-darkness a{color:#fff}
.jqx-widget-header-ui-darkness{font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; background: #333333 url(./images/darkness/ui-bg_gloss-wave_25_333333_500x100.png) 50% 50% repeat-x; border-color: #333; color:#fff; font-size:12px}
.jqx-widget-header-ui-darkness a{color:#fff}

.jqx-fill-state-normal-ui-darkness{border-color: #666666; background: #555555 url(./images/darkness/ui-bg_glass_20_555555_1x400.png) 50% 50% repeat-x; color: #eeeeee;}
.jqx-fill-state-normal-ui-darkness a, .jqx-fill-state-normal-ui-darkness a:link, .jqx-fill-state-normal-ui-darkness a:visited{color:#eeeeee; text-decoration:none}
.jqx-fill-state-hover-ui-darkness{border-color:#59b4d4; background: #0078a3 url(./images/darkness/ui-bg_glass_40_0078a3_1x400.png) 50% 50% repeat-x; color: #ffffff;}
.jqx-fill-state-hover-ui-darkness a, .jqx-fill-state-hover-ui-darkness a:hover{color:#fff; text-decoration:none}
.jqx-fill-state-pressed-ui-darkness{border-color: #ffaf0f; background: #f58400 url(./images/darkness/ui-bg_inset-soft_30_f58400_1x100.png) 50% 50% repeat-x; color: #ffffff; }
.jqx-fill-state-pressed-ui-darkness a, .jqx-fill-state-pressed-ui-darkness a:link, .jqx-fill-state-pressed-ui-darkness a:visited{color:#ffffff; text-decoration:none}
.jqx-fill-state-disabled-ui-darkness {cursor: default; color: #fff; opacity: .55; filter:Alpha(Opacity=45);}

.jqx-input-button-content-ui-darkness{font-size:10px}
.jqx-input-icon-ui-darkness{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-darkness{margin-top:0px; background-position:-65px -147px; background-image:url(./images/darkness/ui-icons_cccccc_256x240.png)}
.jqx-grid-cell-sort-ui-darkness, .jqx-grid-cell-filter-ui-darkness, .jqx-grid-cell-pinned-ui-darkness{background-color:#626262;}
.jqx-grid-cell-alt-ui-darkness, .jqx-grid-cell-sort-alt-ui-darkness, .jqx-grid-cell-filter-alt-ui-darkness{background-color:#626262}
.jqx-splitter-collapse-button-horizontal-ui-darkness, .jqx-splitter-collapse-button-vertical-ui-darkness{ background:#ec8e0c; border:1px solid #fdd02e}
.jqx-dropdownlist-content-ui-darkness{ color:#fff}

.jqx-input-ui-darkness, .jqx-input-content-ui-darkness, .jqx-combobox-content-ui-darkness, .jqx-combobox-input-ui-darkness
{
    background: #fff;
    color: #333;
}
.jqx-input-ui-darkness{overflow: hidden;}
.jqx-input-button-header-ui-darkness{background: #f58400;}
.jqx-grid-bottomright-ui-darkness, .jqx-panel-bottomright-ui-darkness, .jqx-listbox-bottomright-ui-darkness, .jqx-scrollbar-state-normal-ui-darkness{background: #000000;}
.jqx-grid-group-expand-ui-darkness{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down-white.png);}
.jqx-grid-group-collapse-ui-darkness{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right-white.png);}
.jqx-scrollbar-thumb-state-normal-ui-darkness, .jqx-scrollbar-thumb-state-normal-horizontal-ui-darkness{ background: #626262;}
.jqx-scrollbar-thumb-state-hover-ui-darkness, .jqx-scrollbar-thumb-state-hover-horizontal-ui-darkness{ background: #0078a3;}
.jqx-scrollbar-thumb-state-pressed-ui-darkness, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-darkness{ background: #f58400;}
.jqx-expander-header-ui-darkness{ border-color: #666; color:#fff; font-weight:bold; border-radius:6px !important; moz-border-radius:6px !important; webkit-border-radius:6px !important}
.jqx-expander-header-hover-ui-darkness{ border-color:  #59b4d4; color:#fff; font-weight:bold}
.jqx-expander-header-expanded-ui-darkness{ border-color:  #ffaf0f; color:#fff; font-weight:bold; border-top-left-radius:6px !important; border-top-right-radius:6px !important; moz-border-radius-topleft:6px !important; moz-border-radius-topright:6px !important; webkit-border-top-left-radius:6px !important; webkit-border-top-right-radius:6px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;}
.jqx-expander-content-bottom-ui-darkness{border-bottom-left-radius:6px !important; border-bottom-right-radius:6px !important; moz-border-radius-bottomleft:6px !important; moz-border-radius-bottomright:6px !important; webkit-border-bottom-left-radius:6px !important; webkit-border-bottom-right-radius:6px !important;}
.jqx-expander-arrow-top-ui-darkness{background-position:-65px -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-expander-arrow-bottom-ui-darkness{background-position:0 -16px; background-image:url(./images/darkness/ui-icons_222222_256x240.png)}
.jqx-tabs-selection-tracker-top-ui-darkness
{
   background: #f69727;
   border-bottom: 1px solid transparent;
}
.jqx-tabs-selection-tracker-bottom-ui-darkness
{
   background: #f69727;
   border-top: 1px solid transparent;
}
.jqx-scrollbar-ui-darkness .jqx-icon-arrow-up-ui-darkness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-darkness .jqx-icon-arrow-down-ui-darkness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-darkness .jqx-icon-arrow-left-ui-darkness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-darkness .jqx-icon-arrow-right-ui-darkness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-icon-arrow-first-ui-darkness
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-ui-darkness
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-darkness>.jqx-grid-group-expand-ui-darkness
{
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-darkness>.jqx-grid-group-collapse-ui-darkness
{
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-darkness>.jqx-grid-group-collapse-rtl-ui-darkness
{
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-darkness>.jqx-grid-group-expand-rtl-ui-darkness {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-ui-darkness
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-ui-darkness, .jqx-icon-calendar-hover-ui-darkness, .jqx-icon-calendar-pressed-ui-darkness {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-ui-darkness, .jqx-icon-time-hover-ui-darkness, .jqx-icon-time-pressed-ui-darkness {
    background-image: url('images/icon-time-white.png');
}
.jqx-menu-minimized-button-ui-darkness {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-ui-darkness {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-ui-darkness
{
    background-color: #333;
}
.jqx-layout-pseudo-window-pin-icon-ui-darkness
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-darkness
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-ui-darkness, .jqx-scheduler-time-column-ui-darkness, .jqx-scheduler-toolbar-ui-darkness
{
    background: #333 !important;
    color: #fff  !important;
}
.jqx-widget-ui-darkness .jqx-scheduler-middle-cell-ui-darkness, .jqx-scheduler-middle-cell-ui-darkness {
    border-bottom-color: #333 !important;
}
.jqx-docking-layout-group-floating-ui-darkness .jqx-window-header-ui-darkness
{
    background-image: none;
}

.jqx-kanban-item-ui-darkness {
    box-shadow:none;
}

 .jqx-grid-column-menubutton-ui-darkness {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
 }

.jqx-item-ui-darkness .jqx-grid-sortasc-icon
 {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-ui-darkness .jqx-grid-sortdesc-icon
 {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the grid menu's sort remove item/*/
.jqx-item-ui-darkness .jqx-grid-sortremove-icon
 {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
/*applied to the timepicker*/
.jqx-label-ui-darkness {
	fill: white;
}
.jqx-main-container-ui-darkness {
	background: none;
}
.jqx-svg-picker-ui-darkness:focus {
	border: 1px solid rgb(73, 68, 55) !important;
}


.jqx-grid-pager-ui-darkness .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-darkness .jqx-icon-arrow-left-ui-darkness{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-darkness .jqx-icon-arrow-right-ui-darkness{
	position: relative;
    top: 6px;
}