﻿$themeName: 'fluent';

$primary: #0078D4;

:root {
  --fluent-black: #000;
  --fluent-white: #fff;

  --fluent-surface: #fff;
  --fluent-surface-rgb: 255, 255, 255;
  --fluent-surface-color: #000;

  --fluent-background: #fff;
  --fluent-background-color: #000;

  --fluent-type-primary: #201F1E;
  --fluent-type-secondary: #323130;
  --fluent-type-disabled: #323130;

  --fluent-body-divider: #EDEBE9;
  --fluent-input-background: #fff;
  --fluent-input-disabled-background: #EDEBE9;
  --fluent-input-border: #8A8886;
  --fluent-input-border-hover: #323130;

  --fluent-alert-color: #201F1E;

  --fluent-theme-primary: #0078D4;
  --fluent-theme-lighter-alt: #EFF6FC;
  --fluent-theme-lighter: #DEECF9;
  --fluent-theme-light: #C7E0F4;
  --fluent-theme-tertiary: #2B88D8;
  --fluent-theme-dark-alt: #106EBE;
  --fluent-theme-dark: #005A9E;
  --fluent-theme-darker: #004578;

  --fluent-btn-primary-color: #fff;
  --fluent-btn-primary-bg: #0078D4;
  --fluent-btn-primary-bg-hover: #106EBE;
  --fluent-btn-primary-bg-active: #005A9E;

  --fluent-btn-secondary-color: #201F1E;
  --fluent-btn-secondary-bg: #fff;
  --fluent-btn-secondary-bg-hover: #F3F2F1;
  --fluent-btn-secondary-bg-active: #EDEBE9;

  --fluent-btn-text-color: #201F1E;
  --fluent-btn-text-bg: #fff;
  --fluent-btn-text-color-hover: #201F1E;
  --fluent-btn-text-bg-hover: #F3F2F1;
  --fluent-btn-text-color-active: #201F1E;
  --fluent-btn-text-bg-active: #EDEBE9;

  --fluent-window-bg: #fff;
  --fluent-tabs-item-bg-hover: #F3F2F1;
  --fluent-list-items-group-bg: #EDEBE9;
  --fluent-tooltip-bg: #fff;
  --fluent-tooltip-color: #201F1E;

  --fluent-tag-bg: #F3F2F1;
  --fluent-tag-color: #201F1E;

  --fluent-breadcrumb-color: #605E5C;
  --fluent-breadcrumb-bg: transparent;
  --fluent-breadcrumb-color-hover: #201F1E;
  --fluent-breadcrumb-bg-hover: #F3F2F1;

  --fluent-accordion-header-color: #201F1E;
  --fluent-accordion-header-bg: #EDEBE9;

  --fluent-pager-color: #0078D4;
  --fluent-pager-bg: #fff;
  --fluent-pager-border: #C8C6C4;

  --fluent-sortable-border: #EDEBE9;

  --fluent-error-bg: #FDE7E9;
  --fluent-error-color: #A80000;

  --fluent-success-bg: #DFF6DD;
  --fluent-success-color: #107C10;

  --fluent-severe-warning-bg: #FED9CC;
  --fluent-severe-warning-color: #D83B01;

  --fluent-warning-bg: #FFF4CE;
  --fluent-warning-color: #797775;

  --fluent-greys-white: #fff;
  --fluent-greys-grey10: #FAF9F8;
  --fluent-greys-grey20: #F3F2F1;
  --fluent-greys-grey30: #EDEBE9;
  --fluent-greys-grey40: #E1DFDD;
  --fluent-greys-grey50: #D2D0CE;
  --fluent-greys-grey60: #C8C6C4;

  --fluent-greys-grey90: #A19F9D;
  --fluent-greys-grey110: #8A8886;
  --fluent-greys-grey130: #605E5C;
  --fluent-greys-grey150: #3B3A39;
  --fluent-greys-grey160: #323130;
  --fluent-greys-grey190: #201F1E;

  --fluent-overlay-light: rgba(red('#fff'), green('#fff'), blue('#fff'), 0.4);
  --fluent-overlay-dark: rgba(red('#000'), green('#000'), blue('#000'), 0.4);

  --fluent-box-shadow-4: 0px 0.3px 0.9px rgba(0, 0, 0, 0.1), 0px 1.6px 3.6px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-8: 0px 0.6px 1.8px rgba(0, 0, 0, 0.1), 0px 3.2px 7.2px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-16: 0px 1.2px 3.6px rgba(0, 0, 0, 0.1), 0px 6.4px 14.4px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-64: 0px 4.8px 14.4px rgba(0, 0, 0, 0.18), 0px 25.6px 57.6px rgba(0, 0, 0, 0.22);

  --jqx-primary-rgb: 0, 120, 212;
  --jqx-primary: rgb(var(--jqx-primary-rgb));
  --jqx-primary-color: #fff;
  --jqx-background: #fff;
  --jqx-background-color: rgba(0, 0, 0, .88);
  --jqx-background-hover: var(--fluent-greys-grey30);
  --jqx-background-color-hover: var(--fluent-greys-grey190);
  --jqx-surface-rgb: var(--fluent-surface-rgb);
  --jqx-surface: rgb(var(--jqx-surface-rgb));
  --jqx-surface-color: rgba(0, 0, 0, .88);
  --jqx-border: var(--fluent-body-divider);
  --jqx-border-radius: 2px;
  --jqx-scrollbar-background: #f5f5f5;
  --jqx-scrollbar-border: #ddd;
  --jqx-scrollbar-thumb-background: #C1C1C1;
  --jqx-scrollbar-thumb-border: #b3b3b3;
  --jqx-scrollbar-thumb-background-hover: #e6e6e6;
  --jqx-scrollbar-thumb-border-hover: #b3b3b3;
  --jqx-scrollbar-thumb-background-pressed: #d9d9d9;
  --jqx-scrollbar-thumb-border-pressed: #b3b3b3;
  --jqx-scrollbar-button-color-hover: #333;
  --jqx-scrollbar-button-background-hover: #f5f5f5;
  --jqx-scrollbar-button-border-hover: #f5f5f5;
  --jqx-scrollbar-button-color-pressed: #333;
  --jqx-scrollbar-button-background-pressed: #f5f5f5;
  --jqx-scrollbar-button-border-pressed: #f5f5f5;
  --jqx-font-size: 14px;
}

body[theme="fluent-dark"] {
  --fluent-surface: #282727;
  --fluent-surface-color: #fff;

  --fluent-background: #282727;
  --fluent-background-color: #fff;

  --fluent-input-background: #282727;

  --fluent-input-disabled-background: #605E5C;

  --fluent-greys-grey190: #fff;

  --fluent-type-primary: #EDEBE9;

  --fluent-input-border: #6c6c6c;
  --fluent-input-border-hover: #EDEBE9;

  --fluent-btn-secondary-color: #EDEBE9;
  --fluent-btn-secondary-bg: #8A8886;
  --fluent-btn-secondary-bg-hover: #605E5C;
  --fluent-btn-secondary-bg-active: #3B3A39;

  --fluent-btn-text-color: #201F1E;
  --fluent-btn-text-bg: #fff;
  --fluent-btn-text-color-hover: #fff;
  --fluent-btn-text-bg-hover: #605E5C;
  --fluent-btn-text-color-active: #fff;
  --fluent-btn-text-bg-active: #3B3A39;

  --fluent-window-bg: #282727;
  --fluent-tabs-item-bg-hover: #605E5C;
  --fluent-list-items-group-bg: #605E5C;

  --fluent-tooltip-bg: #201F1E;
  --fluent-tooltip-color: #fff;

  --fluent-tag-bg: #605E5C;
  --fluent-tag-color: #F3F2F1;

  --fluent-breadcrumb-color: #C8C6C4;
  --fluent-breadcrumb-bg: transparent;
  --fluent-breadcrumb-color-hover: #C8C6C4;
  --fluent-breadcrumb-bg-hover: #3B3A39;

  --fluent-accordion-header-color: #EDEBE9;
  --fluent-accordion-header-bg: #201F1E;

  --fluent-pager-color: #C8C6C4;
  --fluent-pager-bg: #201F1E;
  --fluent-pager-border: #3B3A39;

  --fluent-sortable-border: #3B3A39;
}

.jqx-icon-search-fluent,
.jqx-icon-close-fluent {
  background-image: none;
  font-family: jqx-icons;
}

.jqx-icon-close-fluent:after {
  content: var(--jqx-icon-close-alt);
  font-size: 12px;
}

.jqx-icon-search-fluent:after {
  content: var(--jqx-icon-search);
}

.jqx-calendar-fluent {
  width: 280px !important;
  height: 280px !important;
}

.jqx-fill-state-normal-fluent {
  background: var(--jqx-background);
  color: var(--jqx-background-color);
  border-color: var(--jqx-border);
}

.jqx-fill-state-hover-fluent {
  background: var(--jqx-background-hover);
  color: var(--jqx-background-color-hover);
  border-color: var(--jqx-background-hover);
}

.jqx-fill-state-pressed-fluent {
  background: var(--jqx-primary);
  color: var(--jqx-primary-color);
  border-color: var(--jqx-primary);
}

@font-face {
  font-family: jqx-icons;
  src: local('./font/jqx-icons'), url('./font/jqx-icons.woff2') format('woff2'), url('./font/jqx-icons.woff') format('woff'), url('./font/jqx-icons.ttf') format('truetype'), url('./font/jqx-icons.eot') format('embedded-opentype');
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-weight: normal;
  speak: none;
}

/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-fluent {
  border-top-left-radius: var(--jqx-border-radius);
}

/*top-right rounded Corners*/
.jqx-rc-tr-fluent {
  border-top-right-radius: var(--jqx-border-radius);
}

/*bottom-left rounded Corners*/
.jqx-rc-bl-fluent {
  border-bottom-left-radius: var(--jqx-border-radius);
}

/*bottom-right rounded Corners*/
.jqx-rc-br-fluent {
  border-bottom-right-radius: var(--jqx-border-radius);
}

/*top rounded Corners*/
.jqx-rc-t-fluent {
  border-top-left-radius: var(--jqx-border-radius);
  border-top-right-radius: var(--jqx-border-radius);
}

/*bottom rounded Corners*/
.jqx-rc-b-fluent {
  border-bottom-left-radius: var(--jqx-border-radius);
  border-bottom-right-radius: var(--jqx-border-radius);
}

/*right rounded Corners*/
.jqx-rc-r-fluent {
  border-top-right-radius: var(--jqx-border-radius);
  border-bottom-right-radius: var(--jqx-border-radius);
}

/*left rounded Corners*/
.jqx-rc-l-fluent {
  border-top-left-radius: var(--jqx-border-radius);
  border-bottom-left-radius: var(--jqx-border-radius);
}

/*all rounded Corners*/
.jqx-rc-all-fluent {
  border-radius: var(--jqx-border-radius);
}

.jqx-widget-fluent,
.jqx-widget-header-fluent,
.jqx-fill-state-normal-fluent,
.jqx-widget-content-fluent,
.jqx-fill-state-hover-fluent,
.jqx-fill-state-pressed-fluent {
  font-family: var(--jqx-font-family);
  font-size: var(--jqx-font-size);
}

.jqx-widget-fluent {
  font-family: var(--jqx-font-family);
  font-size: var(--jqx-font-size);
  color: inherit;
  border-color: var(--jqx-border);
}

.jqx-widget-content-fluent {
  font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: var(--jqx-background-color);
  background-color: var(--jqx-background);
  border-color: var(--jqx-border);
}

.jqx-grid-table-fluent.jqx-grid-table-one-cell {
  border-right-color: var(--jqx-border);
}

.jqx-widget-header-fluent {
  background-color: var(--jqx-surface);
  border-color: var(--jqx-border);
  font-family: var(--jqx-font-family);
  font-size: var(--jqx-font-size);
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-grid-column-header-fluent span:not(.jqx-checkbox-check-checked){
  font-size: 12px !important;
  border-color: var(--fluent-greys-grey30);
  font-weight: 600;
}

.jqx-window-header-fluent {
  padding: 10px;
  color: var(--jqx-surface-color);
  background: var(--jqx-surface);
}

.jqx-calendar tr {
  border-bottom-color: var(--jqx-border);
}


.jqx-widget-fluent input::selection,
input.jqx-input-widget-fluent::selection,
.jqx-widget-content-fluent input::selection {
  background: var(--jqx-primary);
  color: var(--jqx-primary-color);
}

.jqx-toolbar-fluent {
  border-color: var(--jqx-border);
}

.jqx-toolbar-fluent {
  height: auto !important;
  display: flex;
  align-items: center;
}


.jqx-button-fluent,
.jqx-button-fluent.jqx-fill-state-normal-fluent {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  outline: none;
}

.jqx-scheduler-edit-dialog-field .jqx-button-fluent {
  padding: 6px 16px;
}

.jqx-button-fluent button,
jqx-button-fluent input {
  background: transparent;
  color: inherit;
  border: none;
  outline: none;
}


.jqx-button-fluent {
  &.jqx-button-#{$themeName} {
    cursor: pointer;

    --jqx-border: var(--fluent-input-border);
    --jqx-background: var(--fluent-btn-secondary-bg);
    --jqx-background-color: var(--fluent-btn-secondary-color);

    &:hover {
      --jqx-background: var(--fluent-btn-secondary-bg-hover);
    }

    &:active {
      --jqx-background: var(--fluent-btn-secondary-bg-active);
    }

    &:not(.jqx-navbar-block) {
      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        outline-offset: 1px;
        outline: 2px solid var(--jqx-border);
      }
    }

    &.jqx-navbar-block {
      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        background: var(--fluent-btn-secondary-bg-active);
      }
    }

    &[disabled] {
      opacity: 1;
      --jqx-border: var(--fluent-greys-grey20);
      --jqx-background: var(--fluent-greys-grey20);
      --jqx-background-color: var(--fluent-greys-grey90);
    }

    &.jqx-primary,
    &.primary {
      --jqx-border: var(--fluent-btn-primary-bg);
      --jqx-background: var(--fluent-btn-primary-bg);
      --jqx-background-color: var(--fluent-btn-primary-color);

      &:hover {
        --jqx-border: var(--fluent-btn-primary-bg-hover);
        --jqx-background: var(--fluent-btn-primary-bg-hover);
      }

      &:active {
        --jqx-background: var(--fluent-btn-primary-bg-active);
      }

      &[disabled] {
        opacity: 1;
        --jqx-border: var(--fluent-greys-grey20);
        --jqx-background: var(--fluent-greys-grey20);
        --jqx-background-color: var(--fluent-greys-grey90);
      }
    }

    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: 1px solid var(--jqx-border);
    border-radius: 2px;
  }
}

.jqx-group-button-normal-fluent {
  box-shadow: none;
  background: var(--jqx-background);
  border-color: var(--jqx-border);
  color: var(--jqx-background-color) !important;
  border-radius: 0px;
}

.jqx-group-button-normal.jqx-fill-state-hover {
  box-shadow: none !important;
}

.jqx-group-button-normal.jqx-fill-state-pressed {
  box-shadow: none !important;
  background: var(--jqx-primary) !important;
  border-color: var(--jqx-primary) !important;
  color: var(--jqx-primary-color) !important;
  border-radius: 0px;
  outline: none !important;
}


.jqx-slider-tooltip-fluent {
  display: none !important;
  opacity: 0 !important;
}

.jqx-slider-fluent {
  --jqx-primary: var(--fluent-greys-grey130);
  --jqx-ui-state-active: var(--fluent-greys-grey130);
  --jqx-disabled: var(--fluent-greys-grey60);
  --jqx-slider-track-size: 4px;
  --jqx-slider-thumb-width: 16px;
  --jqx-slider-thumb-height: 16px;
  border: 1px solid transparent;
  overflow: unset;
  opacity: 1;
  .jqx-track-container {
    border-radius: 2px;
  }

  .jqx-slider-button {
    opacity: 0 !important;
  }

  .jqx-fill-state-focus-fluent {
    .jqx-slider-rangebar-fluent,
    .jqx-slider-slider {
      --jqx-primary: var(--fluent-theme-primary);
      --jqx-ui-state-active: var(--fluent-theme-primary);
    }
  }
  
  .jqx-slider-track {
    background: var(--jqx-disabled);

    .jqx-fill-state-normal {
      width: 16px;
      height: 16px;
      background-color: var(--fluent-greys-white) !important;
      border-width: 2px;
      border-color: var(--jqx-ui-state-active);
    }

    &:hover {
      background: var(--fluent-theme-light);

      .jqx-fill-state-pressed {
        background-color: var(--fluent-theme-primary);
      }

      .jqx-fill-state-normal,
      .jqx-fill-state-hover {
        border-color: var(--fluent-theme-primary);
      }
    }
    overflow: unset;
  }

  &[disabled] {
    opacity: 1;

    .jqx-track {
      --jqx-primary: var(--fluent-greys-grey90);
      background-color: var(--fluent-greys-grey20);
      --jqx-disabled: var(--fluent-greys-grey20);

      .jqx-value {
        background-color: var(--fluent-greys-grey90);
      }
    }
  }
}

.jqx-button-fluent.float {
  border-radius: 100%;
  min-height: 48px;
  min-width: 48px;
  width: 48px;
  height: 48px;
  max-height: 48px;
  max-width: 48px;
}

.jqx-button-fluent.outlined {
  background: transparent;
  color: var(--jqx-primary);
  border-width: 2px;
}

.jqx-button-fluent.flat {
  background: transparent;
  color: var(--jqx-primary);
  border: none;
}

.jqx-fill-state-hover-fluent,
.jqx-fill-state-focus-fluent {
  text-decoration: none;
}

.jqx-expander-header.jqx-fill-state-normal-fluent {
  background: var(--jqx-surface);
  border-color: var(--jqx-border);
  color: var(--jqx-surface-color);
}

.jqx-expander-header.jqx-fill-state-hover-fluent,
.jqx-expander-header.jqx-fill-state-pressed-fluent {
  background: var(--jqx-background-hover);
  border-color: var(--jqx-border);
  color: var(--jqx-background-color-hover);
}

.jqx-expander-header.jqx-fill-state-hover-fluent {
  background: var(--jqx-background-hover);
}

.jqx-expander-content-fluent {
  padding: 0px;
}

.jqx-expander-header-fluent {
  padding: 10px;
}

.jqx-fill-state-disabled-fluent .jqx-action-button-fluent {
  cursor: initial;
}

.jqx-button-fluent.jqx-fill-state-pressed.float {
  box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
}

.jqx-button-fluent.jqx-fill-state-pressed.outlined,
.jqx-button-fluent.jqx-fill-state-pressed.flat {
  background: rgba(179, 229, 252, 0.15);
  box-shadow: none;
  color: var(--jqx-primary);
}

.jqx-button-fluent.jqx-fill-state-focus.outlined,
.jqx-button-fluent.jqx-fill-state-focus.flat {
  box-shadow: none;
  background: rgba(var(--jqx-primary-rgb), 0.15);
  color: var(--jqx-primary);
}

.jqx-dropdownlist-content-fluent {
  display: flex;
  align-items: center;
  height: 100% !important;
  margin-top: 0px !important;
}

.jqx-dropdownlist-content-fluent span {
  top: 0px !important;
}

.jqx-dropdownlist-state-normal-fluent,
.jqx-dropdownlist-state-hover-fluent,
.jqx-dropdownlist-state-selected-fluent,
.jqx-scrollbar-button-state-hover-fluent,
.jqx-scrollbar-button-state-normal-fluent,
.jqx-scrollbar-button-state-pressed-fluent,
.jqx-scrollbar-thumb-state-normal-horizontal-fluent,
.jqx-scrollbar-thumb-state-hover-horizontal-fluent,
.jqx-scrollbar-thumb-state-pressed-horizontal-fluent,
.jqx-scrollbar-thumb-state-normal-fluent,
.jqx-scrollbar-thumb-state-pressed-fluent,
.jqx-tree-item-hover-fluent,
.jqx-tree-item-selected-fluent,
.jqx-tree-item-fluent,
.jqx-menu-item-fluent,
.jqx-menu-item-hover-fluent,
.jqx-menu-item-selected-fluent,
.jqx-menu-item-top-fluent,
.jqx-menu-item-top-hover-fluent,
.jqx-menu-item-top-selected-fluent,
.jqx-slider-button-fluent,
.jqx-slider-slider-fluent {
  -webkit-transition: background-color 100ms linear;
  -moz-transition: background-color 100ms linear;
  -o-transition: background-color 100ms linear;
  -ms-transition: background-color 100ms linear;
  transition: background-color 100ms linear;
}


.jqx-primary-fluent.jqx-input-label-fluent {
  color: var(--jqx-primary) !important;
}

.jqx-primary-fluent.jqx-input-bar-fluent:before {
  background: var(--jqx-primary) !important;
}

.jqx-success-fluent.jqx-input-label-fluent {
  color: #5cb85c !important;
}

.jqx-success-fluent.jqx-input-bar-fluent:before {
  background: #5cb85c !important;
}

.jqx-inverse-fluent.jqx-input-label-fluent {
  color: #666 !important;
}

.jqx-inverse-fluent.jqx-input-bar-fluent:before {
  background: #666 !important;
}

.jqx-danger-fluent.jqx-input-label-fluent {
  color: #d9534f !important;
}

.jqx-danger-fluent.jqx-input-bar-fluent:before {
  background: #d9534f !important;
}

.jqx-warning-fluent.jqx-input-label-fluent {
  color: #f0ad4e !important;
}

.jqx-warning-fluent.jqx-input-bar-fluent:before {
  background: #f0ad4e !important;
}

.jqx-info-fluent.jqx-input-label-fluent {
  color: #5bc0de !important;
}

.jqx-info-fluent.jqx-input-bar-fluent:before {
  background: #5bc0de !important;
}


.jqx-primary-fluent {
  color: var(--jqx-primary) !important;
  background: #fff !important;
  border-color: var(--jqx-primary) !important;
  text-shadow: none !important;
}

.jqx-primary-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-primary-fluent.jqx-slider-button-fluent,
.jqx-primary-fluent.jqx-slider-slider-fluent,
.jqx-primary-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-primary-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-primary-fluent.jqx-action-button-fluent,
.jqx-primary-fluent:hover,
.jqx-primary-fluent:focus,
.jqx-primary-fluent:active,
.jqx-primary-fluent.active,
.jqx-primary-fluent.disabled,
.jqx-primary-fluent[disabled] {
  color: #fff !important;
  background: var(--jqx-primary) !important;
  border-color: var(--jqx-primary) !important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-fluent.jqx-primary-fluent,
.jqx-primary-fluent:active,
.jqx-primary-fluent.active {
  color: #fff !important;
  background-color: var(--jqx-primary) !important;
  border-color: var(--jqx-primary) !important;
  text-shadow: none !important;
}

.jqx-success-fluent {
  color: #5cb85c !important;
  background: #fff !important;
  border-color: #5cb85c !important;
  text-shadow: none !important;
}

.jqx-success-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-success-fluent.jqx-slider-button-fluent,
.jqx-success-fluent.jqx-slider-slider-fluent,
.jqx-success-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-success-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-success-fluent.jqx-action-button-fluent,
.jqx-success-fluent:hover,
.jqx-success-fluent:focus,
.jqx-success-fluent:active,
.jqx-success-fluent.active,
.jqx-success-fluent.disabled,
.jqx-success-fluent[disabled] {
  color: #fff !important;
  background: #5cb85c !important;
  border-color: #5cb85c !important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-fluent.jqx-success-fluent,
.jqx-success-fluent:active,
.jqx-success-fluent.active {
  text-shadow: none !important;
  color: #fff !important;
  background: #5cb85c !important;
  border-color: #5cb85c !important;
}

.jqx-inverse-fluent {
  text-shadow: none !important;
  color: #666 !important;
  background: #fff !important;
  border-color: #cccccc !important;
}

.jqx-inverse-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-inverse-fluent.jqx-slider-button-fluent,
.jqx-inverse-fluent.jqx-slider-slider-fluent,
.jqx-inverse-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-inverse-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-inverse-fluent.jqx-action-button-fluent,
.jqx-inverse-fluent:hover,
.jqx-inverse-fluent:focus,
.jqx-inverse-fluent:active,
.jqx-inverse-fluent.active,
.jqx-inverse-fluent.disabled,
.jqx-inverse-fluent[disabled] {
  text-shadow: none !important;
  color: #666 !important;
  background: #cccccc !important;
  border-color: #cccccc !important;
}

.jqx-fill-state-pressed-fluent.jqx-inverse-fluent,
.jqx-inverse-fluent:active,
.jqx-inverse-fluent.active {
  text-shadow: none !important;
  color: #666 !important;
  background: #cccccc !important;
  border-color: #cccccc !important;
}


.jqx-danger-fluent {
  text-shadow: none !important;
  color: #d9534f !important;
  background: #fff !important;
  border-color: #d9534f !important;
}

.jqx-danger-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-danger-fluent.jqx-slider-button-fluent,
.jqx-danger-fluent.jqx-slider-slider-fluent,
.jqx-danger-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-danger-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-danger-fluent.jqx-action-button-fluent,
.jqx-danger-fluent:hover,
.jqx-danger-fluent:focus,
.jqx-danger-fluent:active,
.jqx-danger-fluent.active,
.jqx-danger-fluent.disabled,
.jqx-danger-fluent[disabled] {
  text-shadow: none !important;
  color: #fff !important;
  background: #d9534f !important;
  border-color: #d9534f !important;
}

.jqx-fill-state-pressed-fluent.jqx-danger-fluent,
.jqx-danger-fluent:active,
.jqx-danger-fluent.active {
  text-shadow: none !important;
  color: #fff !important;
  background: #d9534f !important;
  border-color: #d9534f !important;
}

.jqx-validator-error-label-fluent {
  color: #d9534f !important;
}

.jqx-warning-fluent {
  text-shadow: none !important;
  color: #f0ad4e !important;
  background: #fff !important;
  border-color: #f0ad4e !important;
}

.jqx-warning-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-warning-fluent.jqx-slider-button-fluent,
.jqx-warning-fluent.jqx-slider-slider-fluent,
.jqx-warning-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-warning-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-warning-fluent.jqx-action-button-fluent,
.jqx-warning-fluent:hover,
.jqx-warning-fluent:focus,
.jqx-warning-fluent:active,
.jqx-warning-fluent.active,
.jqx-warning-fluent.disabled,
.jqx-warning-fluent[disabled] {
  text-shadow: none !important;
  color: #fff !important;
  background: #f0ad4e !important;
  border-color: #f0ad4e !important;
}

.jqx-fill-state-pressed-fluent.jqx-warning-fluent,
.jqx-warning-fluent:active,
.jqx-warning-fluent.active {
  text-shadow: none !important;
  color: #fff !important;
  background: #f0ad4e !important;
  border-color: #f0ad4e !important;
}


.jqx-info-fluent {
  text-shadow: none !important;
  color: #5bc0de !important;
  background: #fff !important;
  border-color: #5bc0de !important;
}

.jqx-info-fluent.jqx-dropdownlist-state-normal-fluent,
.jqx-info-fluent.jqx-slider-button-fluent,
.jqx-info-fluent.jqx-slider-slider-fluent,
.jqx-info-fluent.jqx-combobox-arrow-hover-fluent,
.jqx-info-fluent.jqx-combobox-arrow-normal-fluent,
.jqx-info-fluent.jqx-action-button-fluent,
.jqx-info-fluent:hover,
.jqx-info-fluent:focus,
.jqx-info-fluent:active,
.jqx-info-fluent.active,
.jqx-info-fluent.disabled,
.jqx-info-fluent[disabled] {
  color: #fff !important;
  background: #5bc0de !important;
  border-color: #5bc0de !important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-fluent.jqx-info-fluent,
.jqx-info-fluent:active,
.jqx-info-fluent.active {
  text-shadow: none !important;
  color: #fff !important;
  background: #5bc0de !important;
  border-color: #5bc0de !important;
}

.jqx-fill-state-pressed-fluent {
  background-image: none;
  outline: 0;
}

.jqx-grid-group-column-fluent {
  border-color: transparent;
}

.jqx-grid-column-menubutton-fluent {
  border-width: 0px;
}

.jqx-grid-groups-row-fluent>span {
  padding-left: 4px;
}

.jqx-grid-column-filterbutton-fluent,
.jqx-grid-column-menubutton-fluent {
  background-image: none;
  font-family: 'jqx-icons';
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0px;
}

.jqx-grid-column-filterbutton-fluent:after {
  content: var(--jqx-icon-filter);
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-grid-column-menubutton-fluent:after {
  content: var(--jqx-icon-menu) !important;
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
  margin-top:2px;
}

.jqx-datatable-dark .jqx-widget-header-dark .jqx-grid-column-header-dark {
  border-right-color: var(--jqx-border);
}

.jqx-datatable-fluent td.jqx-grid-cell-fluent,
.jqx-treegrid-fluent .jqx-grid-cell-fluent {
  padding-top: 10px;
  padding-bottom: 9px;
  font-size: 14px;
}

.jqx-grid-cell-fluent {
  background: var(--jqx-background);
  color: var(--jqx-background-color);
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}


.jqx-widget-header-fluent.jqx-grid-cell-fluent {
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-grid-fluent:not(.jqx-scheduler) {
  .jqx-grid-cell:not(.jqx-grid-cell-selected) {
    &.jqx-grid-cell-alt-fluent {
      background: #F4F5F933 !important;
      color: var(--jqx-surface-color) !important;

      &.jqx-grid-cell-sort-fluent {
        background: #F4F5F933 !important;
        color: var(--jqx-surface-color) !important;
      }

      &.jqx-grid-cell-hover-fluent {
        background: var(--jqx-background-hover) !important;
        color: var(--jqx-background-color-hover) !important;
      }
    }
  }
}

.jqx-grid-pager-top-fluent .jqx-button-fluent,
.jqx-grid-pager-fluent .jqx-button-fluent {
  color: inherit !important;
  border-color: transparent !important;
  position: relative;
  top: 0px;
  display: flex;
  font-size: 16px;
  justify-content: center;
  align-content: center;
  outline: none !important;
  outline-offset: 0px !important;
  border-radius: 50%;
}

.jqx-grid-pager-input-fluent {
  padding: 0px !important;
}

.jqx-grid-pager-top-fluent .jqx-button-fluent>div,
.jqx-grid-pager-fluent .jqx-button-fluent>div {
  top: 0px;
  position: relative;
  left: 0px;
  display: flex;
  align-items: center;
  margin-left: 0px !important;
}

.jqx-grid-pager-top-fluent .jqx-button-fluent.jqx-fill-state-hover,
.jqx-grid-pager-fluent .jqx-button-fluent.jqx-fill-state-hover {
  color: var(--jqx-background-color-hover);
  background: var(--jqx-background-hover);
  border-color: var(--jqx-background-hover);
  box-shadow: none;
}

.jqx-grid-pager-top-fluent .jqx-button-fluent.jqx-fill-state-pressed,
.jqx-grid-pager-fluent .jqx-button-fluent.jqx-fill-state-pressed {
  background: var(--jqx-primary);
  color: var(--jqx-primary-color) !important;
  border-color: var(--jqx-primary) !important;
}

.jqx-grid-pager-top-fluent .jqx-grid-pager-number-fluent,
.jqx-grid-pager-fluent .jqx-grid-pager-number-fluent {
  background-color: transparent;
  border-color: transparent;
  color: inherit;
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 50%;
  position: relative;
}

.jqx-grid-pager-top-fluent .jqx-grid-pager-number-fluent:hover,
.jqx-grid-pager-fluent .jqx-grid-pager-number-fluent:hover {
  background: var(--jqx-background-hover);
  color: var(--jqx-background-color-hover) !important;
  font-size: var(--jqx-font-size);
}

.jqx-grid-pager-top-fluent .jqx-grid-pager-number-fluent.jqx-fill-state-pressed-fluent,
.jqx-grid-pager-fluent .jqx-grid-pager-number-fluent.jqx-fill-state-pressed-fluent {
   background: var(--jqx-primary);
  color: var(--jqx-background) !important;
}

.jqx-grid-column-menubutton-fluent {
  background-color: transparent;
  border-color: var(--jqx-border) !important;
}

.jqx-cell-fluent {
  font-size: 13px;
}

.jqx-calendar-fluent>div {
  padding: 0px;
  box-sizing: border-box;
}

.jqx-calendar-month-fluent {
  width: 90%;
  position: relative;
  left: 5%;
}

.jqx-calendar-title-navigation-fluent {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  &.jqx-icon-arrow-left-fluent {
    &:after{
      content: var(--jqx-icon-calendar-up) !important;
      font-size: 12px;
    }
  }

  &.jqx-icon-arrow-right-fluent {
    &:after{
      content: var(--jqx-icon-calendar-down) !important;
      font-size: 12px;
    }
  }
  
  &:hover {
    &::after{
      background:var(--jqx-background-hover) !important;
      color: var(--jqx-background-color-hover) !important;
      padding: 5px;
    }
 }
}

.jqx-calendar-row-header-fluent,
.jqx-calendar-top-left-header-fluent {
  background-color: var(--jqx-background);
  border: 0px solid var(--jqx-background);
}

.jqx-calendar-column-header-fluent {
  background-color: var(--jqx-background);
  border-top: 1px solid var(--jqx-background);
  border-bottom: 1px solid var(--jqx-border);
  color: var(--jqx-background-color);
}

.jqx-expander-header-fluent {
  padding-top: 10px;
  padding-bottom: 10px;
}

.jqx-ribbon-header-vertical-fluent,
.jqx-widget-header-vertical-fluent {
  background: var(--jqx-background);
}

.jqx-scrollbar-fluent:not(.jqx-scrollbar-mobile) {
  .jqx-scrollbar-state-normal-fluent {
    background: transparent;
    border-color:transparent;
  }

  .jqx-scrollbar-button-state-normal-fluent {
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .jqx-scrollbar-thumb-state-normal-fluent {
    transform:scaleX(0.3);
    border-radius: 5px;
    transition: transform 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .jqx-scrollbar-thumb-state-normal-horizontal-fluent {
    transform:scaleY(0.3);
    border-radius: 5px;
    transition: transform 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  &:hover,
  &[touched] {
    .jqx-scrollbar-state-normal-fluent {
      background: transparent;
      border-color:transparent;  
    }

    .jqx-scrollbar-button-state-normal-fluent {
       opacity: 1;
    }
     
    .jqx-scrollbar-thumb-state-normal-fluent {
        transform: scaleX(0.5);
        border-radius: 5px;
    }
    .jqx-scrollbar-thumb-state-normal-horizontal-fluent {
        transform: scaleY(0.5);
        border-radius: 5px;
     }
  }

  .jqx-scrollbar-thumb-state-pressed-fluent,
  .jqx-scrollbar-thumb-state-pressed-horizontal-fluent {
    transform: scale(1);
    border-radius: 0px;
  }
}
.jqx-scrollbar-state-normal-fluent {
  background-color: var(--jqx-scrollbar-background);
  border: 1px solid var(--jqx-scrollbar-background);
  border-left-color: var(--jqx-scrollbar-border);
}

.jqx-scrollbar-thumb-state-normal-fluent,
.jqx-scrollbar-thumb-state-normal-horizontal-fluent {
  background: var(--jqx-scrollbar-thumb-background);
  border-color: var(--jqx-scrollbar-thumb-border);
  border-radius: 0px;
}

.jqx-scrollbar-thumb-state-hover-fluent,
.jqx-scrollbar-thumb-state-hover-horizontal-fluent {
  background: var(--jqx-scrollbar-thumb-background-hover);
  border-color: var(--jqx-scrollbar-thumb-border-hover);
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

.jqx-progressbar-fluent {
  background: var(--jqx-background) !important;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.jqx-progressbar-value-fluent,
.jqx-splitter-collapse-button-horizontal-fluent {
  background: var(--jqx-primary);
}

.jqx-splitter-collapse-button-vertical-fluent,
.jqx-progressbar-value-vertical-fluent {
  background: var(--jqx-primary);
}

.jqx-scrollbar-mobile-fluent .jqx-scrollbar-button-state-normal {
  display: none !important;
}

.jqx-scrollbar-button-state-hover-fluent {
  color: var(--jqx-scrollbar-button-color-hover);
  background: var(--jqx-scrollbar-button-background-hover);
  border-color: var(--jqx-scrollbar-button-border-hover);
}


.jqx-scrollbar-button-state-pressed-fluent {
  color: var(--jqx-scrollbar-button-color-pressed);
  background: var(--jqx-scrollbar-button-background-pressed);
  border-color: var(--jqx-scrollbar-button-border-pressed);
}

.jqx-splitter-splitbar-vertical-fluent,
.jqx-splitter-splitbar-horizontal-fluent {
  background: var(--jqx-scrollbar-thumb-background);
  border-color: var(--jqx-scrollbar-thumb-border);
}

.jqx-scrollbar-thumb-state-pressed-fluent,
.jqx-scrollbar-thumb-state-pressed-horizontal-fluent,
.jqx-scrollbar-button-state-pressed-fluent {
  background: var(--jqx-scrollbar-thumb-background-pressed);
  border-color: var(--jqx-scrollbar-thumb-border-pressed);
  box-shadow: none;
}

.jqx-grid-column-sortdescbutton-fluent,
jqx-grid-column-filterbutton-fluent,
.jqx-grid-column-sortascbutton-fluent {
  background-color: transparent;
  border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--jqx-border);
}

.jqx-menu-vertical-fluent {
  background: var(--jqx-background);
  filter: none;
}

.jqx-grid-bottomright-fluent,
.jqx-panel-bottomright-fluent,
.jqx-listbox-bottomright-fluent {
  background-color: var(--jqx-background);
  border-color:var(--jqx-background);
}

.jqx-window-fluent,
.jqx-tooltip-fluent {
  box-shadow: var(--fluent-box-shadow-64) !important;
}
.jqx-layout-group-tabbed-fluent {
  box-shadow: none !important;
}



.jqx-tooltip-fluent {
  --jqx-tooltip-arrow-width: 16px;
  --jqx-tooltip-arrow-translate: 0;
  opacity: 0.9;
  &.dark {
    --fluent-tooltip-bg: #201F1E;
    --fluent-tooltip-color: #fff;
  }

  &.jqx-popup-light,
  .jqx-fill-state-normal-fluent {
    background: var(--fluent-tooltip-bg);
    border-color: var(--fluent-tooltip-bg);
    box-shadow: none;
    color: var(--fluent-tooltip-color);
  }

  box-shadow: var(--fluent-box-shadow-16) !important;
  border-radius: 2px;

  .jqx-tooltip-arrow{
  
  }
  .jqx-tooltip-main {
    border-color: var(--fluent-tooltip-bg);
    background-color: var(--fluent-tooltip-bg);
    color: var(--fluent-tooltip-color);
    border-radius: 2px;   box-shadow: var(--fluent-box-shadow-16) !important;

  }
}


.jqx-rating-image-default,
.jqx-rating-image-hover,
.jqx-rating-image-backward {
  img {
    visibility: hidden;
  }

  &:after {
    content: '';
    top: 0;
    position: absolute;
    display: block;

    background-position: center center;
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
  }
}

.jqx-rating-image-backward {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.8281 12.2461L16.25 20L10 15.1953L3.75 20L6.17188 12.2461L0 7.5H7.65625L10 0L12.3438 7.5H20L13.8281 12.2461ZM13.877 16.6016C13.6296 15.7943 13.3822 14.9935 13.1348 14.1992C12.8874 13.3984 12.6335 12.5944 12.373 11.7871C13.0436 11.2858 13.7044 10.7812 14.3555 10.2734C15.0065 9.76562 15.6641 9.25781 16.3281 8.75H11.4258L10 4.18945L8.57422 8.75H3.67188C4.33594 9.25781 4.99349 9.76562 5.64453 10.2734C6.29557 10.7812 6.95638 11.2858 7.62695 11.7871C7.36654 12.5944 7.11263 13.3984 6.86523 14.1992C6.61784 14.9935 6.37044 15.7943 6.12305 16.6016L10 13.6133L13.877 16.6016Z' fill='%23A19F9D'/%3E%3C/svg%3E");
    background-color: #fff;
  }
}

.jqx-rating-image-hover {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='36' viewBox='0 0 24 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.8281 20.2461L18.25 28L12 23.1953L5.75 28L8.17188 20.2461L2 15.5H9.65625L12 8L14.3438 15.5H22L15.8281 20.2461Z' fill='%230078D4'/%3E%3C/svg%3E");
  }
}

.jqx-rating-image-default {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='36' viewBox='0 0 24 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.8281 20.2461L18.25 28L12 23.1953L5.75 28L8.17188 20.2461L2 15.5H9.65625L12 8L14.3438 15.5H22L15.8281 20.2461Z' fill='%23201F1E'/%3E%3C/svg%3E");
  }
}


.jqx-docking-fluent .jqx-window-fluent {
  box-shadow: none;
}

.jqx-docking-panel-fluent .jqx-window-fluent {
  box-shadow: none;
}

.jqx-checkbox-fluent {
  line-height: 20px;
  overflow: visible;
}

.jqx-radiobutton-fluent {
  overflow: visible;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  background-repeat: no-repeat;
  background: none;
  line-height: 20px;
}

.jqx-radiobutton-fluent-fluent,
.jqx-radiobutton-hover-fluent {
  border-radius: 100%;
  background-repeat: no-repeat;
  transition: background-color ease-in .3s;
}

.jqx-radiobutton-check-checked-fluent {
  filter: none;
  background: var(--jqx-background);
  background-repeat: no-repeat;
  border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-fluent {
  filter: none;
  background: var(--jqx-background);
  border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-fluent {
  filter: none;
  background: var(--jqx-background);
  opacity: 0.7;
  border-radius: 100%;
}

.jqx-checkbox-default-fluent,
.jqx-radiobutton-default-fluent {
  border-width: 1px;
  border-color: var(--fluent-input-border);
  background-color: var(--jqx-background);
  overflow: visible;
}

.jqx-tree-grid-expand-button-fluent,
.jqx-tree-grid-collapse-button-fluent {
  font-size: 16px;
}

.jqx-grid-table-fluent .jqx-grid-cell:first-child {
  padding-left: 10px;
}

.jqx-tree-grid-title-fluent {
  margin-left: 5px;
}

.jqx-tree-fluent .jqx-checkbox-fluent[checked] .jqx-checkbox-default-fluent,
.jqx-checkbox-fluent[checked] .jqx-checkbox-default-fluent,
.jqx-radiobutton-fluent[checked] .jqx-radiobutton-default-fluent {
  color: var(--jqx-primary-color);
  background-color: var(--jqx-primary);
  border-color: var(--jqx-primary);
}

.jqx-datatable-fluent {
  .jqx-tree-grid-checkbox-fluent {
    color:var(--jqx-background-color);
    background-color: var(--jqx-background);
    border-color: var(--jqx-border);

    &:hover {
      border-color: var(--fluent-input-border-hover);
    }

   
    .jqx-checkbox-check-indeterminate,
    &[checked="true"] {
      color: var(--jqx-primary-color) !important;
      background-color: var(--jqx-primary) !important;
      border-color: var(--jqx-primary) !important;  
    }
  }

}

.jqx-listbox-content-element-fluent  {
  .jqx-checkbox-fluent {
    .jqx-checkbox-default-fluent {
      color:var(--jqx-background-color);
      background-color: var(--jqx-background);
      border-color: var(--jqx-border);

      &:hover {
        border-color: var(--fluent-input-border-hover);
      }
    }

    .jqx-checkbox-check-checked {
      color: var(--jqx-primary-color);
      background-color: var(--jqx-primary);
      border-color: var(--jqx-primary);
    }
  }
}
.jqx-menu-item-disabled-fluent {
  color: inherit;
}

.jqx-grid-fluent .jqx-checkbox-default-fluent {
  border-radius: 0px;
}

.jqx-checkbox-check-checked-fluent {
  background: none;
  font-family: jqx-icons;
  display: flex;
  justify-content: center;
}

.jqx-checkbox-check-checked-fluent:after {
  content: var(--jqx-icon-check);
}

.jqx-checkbox-check-indeterminate-fluent {
  width: 14px !important;
  height: 14px !important;
  position: relative;
  top: 1px;
  left: 1px;
  background: var(--jqx-background);
}

.jqx-tree-fluent .jqx-checkbox-check-indeterminate-fluent {
  width: 12px !important;
  height: 12px !important;
  top: 2px;
  left: 2px;
}

.jqx-checkbox-hover-fluent,
.jqx-radiobutton-hover-fluent {
  background-color: var(--fluent-input-background);
  border-color: var(--fluent-input-border-hover);
}

.jqx-tree-item-fluent,
.jqx-tree-item-selected {
  padding: 6px;
  border-radius: 2px;
}

.jqx-grid-pager-input-fluent,
.jqx-listbox-fluent input {
  padding-left: 5px;
  box-sizing: border-box;

  &:hover {
    border: 1px solid var(--fluent-input-border-hover) !important;
  }

  &:focus {
    border: 1px solid var(--jqx-primary) !important;
    outline: 1px solid var(--jqx-primary) !important;
    color: var(--fluent-greys-grey190) !important;
  }
}

.jqx-listbox-content-element {
}
.jqx-listitem-element-fluent .jqx-checkbox-default-fluent {
  border-radius: 0px;
}

.jqx-listitem-state-hover-fluent,
.jqx-listitem-state-selected-fluent,
.jqx-listitem-state-normal-fluent {
  border-radius: 0;
  margin: 0px;
  padding-top: 6px !important;
  padding-bottom:6px !important;
  padding-left: 5px;
  cursor: pointer;
}

.jqx-scheduler-edit-dialog-label-fluent {
  padding-top: 6px;
  padding-bottom: 6px;

}

.jqx-scheduler-edit-dialog-field-fluent {
  padding-top: 6px;
  padding-bottom: 6px;
}

.jqx-scheduler-edit-dialog-label-rtl-fluent {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}

.jqx-scheduler-edit-dialog-field-rtl-fluent {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}

.jqx-menu-horizontal-fluent {
  height: auto !important;
}

.jqx-menu-horizontal-fluent .jqx-menu-item-top-fluent {
  padding: 8px;
}

.jqx-menu-item-top-fluent,
.jqx-menu-item-fluent {
  padding: 8px;
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-fluent,
.jqx-menu-item-hover-fluent,
.jqx-tree-item-hover-fluent,
.jqx-calendar-cell-hover-fluent,
.jqx-grid-cell-hover-fluent,
.jqx-grid-cell-fluent.jqx-fill-state-hover,
.jqx-input-popup-fluent .jqx-fill-state-hover-fluent,
.jqx-input-popup-fluent .jqx-fill-state-pressed-fluent {
  color: var(--jqx-background-color-hover);
  border-color: var(--jqx-background-hover);
  text-decoration: none;
  background-color: var(--jqx-background-hover);
  background-repeat: repeat-x;
  outline: 0;
  background: var(--jqx-background-hover);
  box-shadow: none;
  background-position: 0 0;
}

.jqx-scheduler-cell-hover-fluent {
  border-color: var(--jqx-primary) !important;
  background: var(--jqx-primary) !important;
  color: var(--jqx-background) !important;
}

.jqx-listitem-state-selected-fluent,
.jqx-menu-item-selected-fluent,
.jqx-tree-item-selected-fluent,
.jqx-calendar-cell-selected-fluent,
.jqx-grid-cell-selected-fluent,
.jqx-menu-item-top-selected-fluent,
.jqx-grid-selectionarea-fluent,
.jqx-input-button-header-fluent,
.jqx-input-button-innerHeader-fluent {
  color: var(--jqx-primary-color) !important;
  border-color: var(--jqx-primary) !important;
  background: var(--jqx-primary) !important;
  /* Old browsers */
  box-shadow: none;
}

.jqx-grid-cell-fluent .jqx-button-fluent,
.jqx-grid-cell-fluent .jqx-button-fluent.jqx-fill-state-hover-fluent,
.jqx-grid-cell-fluent .jqx-button-fluent.jqx-fill-state-pressed-fluent {
  box-shadow: none;
  transition: none;
}

.jqx-menu-popup-fluent {
  opacity: 0;
  transform-origin: top left;
  box-shadow: var(--fluent-box-shadow-8) !important;
  background: var(--jqx-background) !important;

  .jqx-popup-fluent {
    box-shadow:none !important;
  }
}

.jqx-menu-popup-fluent.top {
  transform: scaleY(0);
  transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-fluent.horizontal {
  transform: scaleX(0);
  transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-fluent.show {
  transform: scale(1);
  opacity: 1;
}

.jqx-popup-fluent {
  border: 1px solid var(--jqx-border);
  background: var(--jqx-background);
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, .14), 0 3px 14px 3px rgba(0, 0, 0, .12), 0 4px 15px 0 rgba(0, 0, 0, .2);
  box-shadow: var(--fluent-box-shadow-8) !important;
}

.jqx-menu-popup-fluent .jqx-popup-fluent {
  box-shadow: none;
  border: none;
}

.jqx-datatable-fluent .jqx-grid-column-sortdescbutton-fluent,
.jqx-datatable-fluent .jqx-grid-column-sortascbutton-fluent {
  display: flex;
  align-items: center;
}

.jqx-grid-column-sortascbutton-fluent,
.jqx-expander-arrow-bottom-fluent,
.jqx-window-collapse-button-fluent,
.jqx-menu-item-arrow-up-fluent,
.jqx-menu-item-arrow-up-selected-fluent,
.jqx-menu-item-arrow-top-up-fluent,
.jqx-icon-arrow-up-fluent,
.jqx-icon-arrow-up-hover-fluent,
.jqx-icon-arrow-up-selected-fluent {
  background-image: none;
}

.jqx-grid-column-sortascbutton-fluent,
.jqx-expander-arrow-bottom-fluent,
.jqx-window-collapse-button-fluent,
.jqx-menu-item-arrow-up-fluent,
.jqx-menu-item-arrow-up-selected-fluent,
.jqx-menu-item-arrow-top-up-fluent,
.jqx-icon-arrow-up-fluent,
.jqx-icon-arrow-up-hover-fluent,
.jqx-icon-arrow-up-selected-fluent {
  background-image: none;
  font-family: jqx-icons;
}

.jqx-window-collapse-button-fluent {
  position: relative;
  right: 10px;
  font-size: 18px;
  margin-top: 0px;
}

.jqx-grid-column-sortascbutton-fluent:after,
.jqx-expander-arrow-bottom-fluent:after,
.jqx-window-collapse-button-fluent:after,
.jqx-menu-item-arrow-up-fluent:after,
.jqx-menu-item-arrow-up-selected-fluent:after,
.jqx-menu-item-arrow-top-up-fluent:after,
.jqx-icon-arrow-up-fluent:after,
.jqx-icon-arrow-up-hover-fluent:after,
.jqx-icon-arrow-up-selected-fluent:after {
  content: var(--jqx-icon-arrow-up);
}
.jqx-grid-column-sortascbutton-fluent:after,
.jqx-grid-column-sorticon-fluent.jqx-icon-arrow-up-fluent:after {
  content: var(--jqx-icon-sort-up);  
}
.jqx-widget-fluent .jqx-grid-group-expand-fluent,
.jqx-grid-group-expand-fluent,
.jqx-grid-column-sortdescbutton-fluent,
.jqx-expander-arrow-top-fluent,
.jqx-window-collapse-button-collapsed-fluent,
.jqx-menu-item-arrow-down-fluent,
.jqx-menu-item-arrow-down-selected-fluent,
.jqx-menu-item-arrow-down-fluent,
.jqx-icon-arrow-down-fluent,
.jqx-icon-arrow-down-hover-fluent,
.jqx-icon-arrow-down-selected-fluent {
  background-image: none;
  font-family: jqx-icons;
}

.jqx-widget-fluent .jqx-grid-group-expand-fluent:after,
.jqx-grid-group-expand-fluent:after,
.jqx-grid-column-sortdescbutton-fluent:after,
.jqx-expander-arrow-top-fluent:after,
.jqx-window-collapse-button-collapsed-fluent:after,
.jqx-menu-item-arrow-down-fluent:after,
.jqx-menu-item-arrow-down-selected-fluent:after,
.jqx-menu-item-arrow-down-fluent:after,
.jqx-icon-arrow-down-fluent:after,
.jqx-icon-arrow-down-hover-fluent:after,
.jqx-icon-arrow-down-selected-fluent:after {
  content: var(--jqx-icon-arrow-down); 
}
.jqx-grid-column-sortdescbutton-fluent:after,
.jqx-grid-column-sorticon-fluent.jqx-icon-arrow-down-fluent:after  {
  content: var(--jqx-icon-sort-down);  
}


.jqx-grid-column-header-fluent[sort][sort-index]>div>div {
  width: calc(100% - 45px)
}

.jqx-grid-column-header-fluent[sort][sort-index] .sorticon {
  display: flex;
  align-items: center;
  width: 45px;
  flex-direction: row-reverse;
  margin-right: 5px;
}

.jqx-grid-column-header-fluent[sort][sort-index] .sorticon:before {
  content: attr(order);
  font-size: 11px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: var(--jqx-primary);
  color: var(--jqx-primary-color);
  padding: 5px;
  margin-left: 5px;
  height: 8px;
  box-sizing: content-box;
}

.jqx-tabs-arrow-left-fluent,
.jqx-menu-item-arrow-left-selected-fluent,
.jqx-menu-item-arrow-top-left,
.jqx-icon-arrow-left-fluent,
.jqx-icon-arrow-down-left-fluent,
.jqx-icon-arrow-left-selected-fluent {
  background-image: none;
  font-family: jqx-icons;
  background-repeat: no-repeat;
  background-position: center;
}

.jqx-tabs-arrow-left-fluent:after,
.jqx-menu-item-arrow-left-selected-fluent:after,
.jqx-menu-item-arrow-top-left:after,
.jqx-icon-arrow-left-fluent:after,
.jqx-icon-arrow-down-left-fluent:after,
.jqx-icon-arrow-left-selected-fluent:after {
  content: var(--jqx-icon-arrow-left);
}

.jqx-widget-fluent .jqx-grid-group-collapse-fluent,
.jqx-grid-group-collapse-fluent,
.jqx-tabs-arrow-right-fluent,
.jqx-menu-item-arrow-right-selected-fluent,
.jqx-menu-item-arrow-top-right-fluent,
.jqx-icon-arrow-right-fluent,
.jqx-icon-arrow-right-hover-fluent,
.jqx-icon-arrow-right-selected-fluent {
  background-image: none;
  font-family: jqx-icons;
  background-repeat: no-repeat;
  background-position: center;
}

.jqx-widget-fluent .jqx-grid-group-collapse-fluent:after,
.jqx-grid-group-collapse-fluent:after,
.jqx-tabs-arrow-right-fluent:after,
.jqx-menu-item-arrow-right-selected-fluent:after,
.jqx-menu-item-arrow-top-right-fluent:after,
.jqx-icon-arrow-right-fluent:after,
.jqx-icon-arrow-right-hover-fluent:after,
.jqx-icon-arrow-right-selected-fluent:after {
  content: var(--jqx-icon-arrow-right);
}

.jqx-tree-item-arrow-collapse-rtl-fluent,
.jqx-tree-item-arrow-collapse-hover-rtl-fluent {
  background-image: none;
}

.jqx-tree-item-arrow-collapse-rtl-fluent:after,
.jqx-tree-item-arrow-collapse-hover-rtl-fluent:after {
  content: var(--jqx-icon-arrow-left);
}

.jqx-menu-item-arrow-left-selected-fluent {
  background-image: none;
}

.jqx-menu-item-arrow-right-selected-fluent {
  background-image: none;
}

.jqx-input-button-content-fluent {
  font-size: 10px;
}

.jqx-widget .jqx-grid-column-header-cell-fluent {
  padding-top: 8px;
  padding-bottom: 8px;
  height: 30px;
}

.jqx-widget .jqx-grid-row-cell-fluent {
  padding-top: 8px;
  padding-bottom: 8px;
  height: 30px;
}

.jqx-listbox-container-fluent,
.jqx-calendar-container-fluent {
  margin-left: -10px;
}

.jqx-calendar-container-fluent .jqx-popup,
.jqx-calendar-fluent.jqx-popup,
.jqx-listbox-fluent.jqx-popup {
  margin-left: 9px;
}

.jqx-dropdownbutton-popup,
.jqx-calendar-fluent.jqx-popup,
.jqx-listbox-fluent.jqx-popup,
.jqx-grid-menu.jqx-popup {
  transition: transform 0.25s ease-in-out, opacity 0.35s ease-in-out;
  transform: scaleY(0);
  opacity: 0;
  transform-origin: top left;
  display: block !important;
}

.jqx-dropdownbutton-popup.jqx-popup-show,
.jqx-calendar-fluent.jqx-popup-show,
.jqx-listbox-fluent.jqx-popup-show,
.jqx-grid-menu.jqx-popup-show {
  transform: scaleY(1);
  opacity: 1;
}

.jqx-widget-fluent .jqx-grid-cell {
  border-color: var(--jqx-border);
  color: var(--jqx-background-color);
}

.jqx-widget-fluent .jqx-grid-column-header,
.jqx-widget-fluent .jqx-grid-group-cell {
  border-color: var(--jqx-border);
  color: var(--jqx-surface-color);
  background: var(--jqx-surface);
}

.jqx-widget-fluent .jqx-grid-column-header-fluent {
  border-color: var(--jqx-border);
  font-size: 14px;
  color: var(--jqx-surface-color);
}


.jqx-widget-fluent .jqx-widget-header-fluent:hover .jqx-grid-column-header-fluent {
  border-right-color: var(--jqx-border) !important;
  border-bottom-color: var(--jqx-border) !important;
}

.jqx-widget-fluent .jqx-grid-cell-fluent {
  border-color: var(--jqx-border);
}

.jqx-widgets-fluent .jqx-scheduler-cell-selected span {
  color: var(--jqx-background) !important;
}

.jqx-scheduler-time-column-fluent,
.jqx-scheduler-toolbar-fluent {
  background: var(--jqx-surface) !important;
  color: var(--jqx-surface-color) !important;
  border-color: var(--jqx-border) !important;
}

.jqx-scheduler-toolbar-fluent {
  .jqx-widget-fluent {
    border-color: transparent;
    background: transparent;


    .jqx-action-button {
      margin-left: unset !important;
    }

    &:hover {
      background: var(--jqx-background-hover);
      color: var(--jqx-background-color-hover);
    }
  }
}
.jqx-widget-fluent.jqx-scheduler-fluent .jqx-grid-cell-fluent,
.jqx-widget-fluent.jqx-scheduler-fluent .jqx-grid-column-header-fluent {
  border-bottom-color: var(--jqx-border);
}

.jqx-widget-fluent.jqx-scheduler-fluent td.jqx-grid-cell-fluent span {
  font-size: 10px;
  color: var(--jqx-background-color);
}

.jqx-widget-fluent.jqx-scheduler-fluent td.jqx-grid-cell-fluent.jqx-scheduler-cell-hover span,
.jqx-widget-fluent.jqx-scheduler-fluent td.jqx-grid-cell-fluent.jqx-scheduler-cell-selected span {
  color: var(--jqx-primary-color) !important;
  background: var(--jqx-primary) !important;  
}

.jqx-passwordinput-password-icon-fluent,
.jqx-passwordinput-password-icon-rtl-fluent {
  background-image: none !important;
  font-family: jqx-icons;
  color: var(--jqx-background-color);
}

.jqx-passwordinput-password-icon-fluent:after,
.jqx-passwordinput-password-icon-rtl-fluent:after {
  content: var(--jqx-icon-visibility);
}

.jqx-combobox-fluent .jqx-icon-close-fluent {
  background-image: none;
  font-family: jqx-icons;
}

.jqx-combobox-multi-item-fluent {
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.jqx-combobox-multi-item-fluent a {
  margin-right: 10px;
  margin-top: 2px;
}

.jqx-combobox-multi-item-fluent {
  .jqx-icon-close { 
    position: static !important;
    margin-top: 0px !important;
    margin-left: 0px !important; 
  }
}
.jqx-combobox-fluent .jqx-icon-close-fluent:after {
  content: var(--jqx-icon-close-alt);
  font-size: 10px;
}

.jqx-dropdownbutton-fluent,
.jqx-dropdownlist-fluent,
.jqx-combobox-fluent,
.jqx-input-fluent {
  border-color: var(--jqx-border);
  color: var(--jqx-background-color);
  background-color: var(--jqx-background);
}

.jqx-combobox-content-fluent,
.jqx-datetimeinput-content-fluent {
  border-color: transparent;
}


.jqx-combobox-content-focus-fluent,
.jqx-combobox-state-focus-fluent,
.jqx-numberinput-focus-fluent {
  outline: none;
}

.jqx-input-group-fluent {
  position: relative;
  display: inline-block;
  overflow: visible;
  border: none;
  box-shadow: none;
 --jqx-border: var(--fluent-input-border); 

 &.jqx-complex-input-group-fluent {
   min-height: 30px;

    .jqx-formatted-input-spin-button-fluent {
      &:hover {
        background: var(--jqx-background-hover);
        color:var(--jqx-background-color-hover);
      }

      &.jqx-fill-state-pressed {
        color:var(--jqx-primary-color);
        background-color: var(--jqx-primary);
      }
    }
 }

  &.underlined {
    .jqx-input-fluent {
      border: none;
      border-bottom: 1px solid var(--jqx-border);
    }
  }

  .jqx-input-fluent, input, textarea {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
    z-index: 9;
    &.jqx-fill-state-disabled {
      background: var(--fluent-input-disabled-background);
      --jqx-background: var(--fluent-input-disabled-background);
      --jqx-surface: var(--fluent-input-disabled-background);
      pointer-events: none;
    }
    .jqx-action-button {
      margin-left: -3px;
    }
  
    &:hover {
      border: 1px solid var(--fluent-input-border-hover);
    }
    
    &.underlined {
      &:hover {
        border-bottom: 1px solid var(--fluent-input-border-hover);
      }

      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        border-bottom: 1px solid var(--jqx-primary);
        color: var(--fluent-greys-grey190) !important;
      }
    }

    &:not(.underlined) {
      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        border: 1px solid var(--jqx-primary);
        outline: 1px solid var(--jqx-primary) !important;
        color: var(--fluent-greys-grey190) !important;
      }
   }
  }
}

.jqx-input-group-fluent input {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.jqx-input-group-fluent textarea {
  width: 100%;
  height: 100%;
  outline: none;
  resize: none;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom-color: var(--jqx-border);
}

.jqx-numberinput-fluent,
.jqx-maskedinput-fluent {
  position: relative;
}

.jqx-numberinput-fluent input {
  height: 100% !important;
}

.jqx-input-fluent.jqx-validator-error-element {
  border-color: transparent !important;
  border-bottom: 1px solid #df2227 !important;
}

.jqx-input-fluent.underlined input,
.jqx-dropdownlist-state-normal-fluent.underlined,
.jqx-combobox-state-normal-fluent.underlined,
.jqx-numberinput-fluent.underlined,
.jqx-datetimeinput-fluent.underlined {
  background: var(--jqx-surface);
  border-color: var(--jqx-surface);
  border-radius: 0;
  color: var(--jqx-surface-color);
  box-shadow: none;
  border-bottom: 1px solid var(--fluent-body-divider);
  --jqx-border: var(--fluent-body-divider);

  &:not(.jqx-fill-state-focus) {
    &:hover {
      border-bottom-color: var(--fluent-input-border-hover);
    }

    &:focus {
      color: var(--fluent-greys-grey190) !important;
    }
  }
  outline: none;
}

.jqx-numberinput-fluent .jqx-action-button-fluent {
  border-radius: 0;
}

.jqx-numberinput-fluent .jqx-action-button-fluent>div {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jqx-date-time-input-popup-fluent {
  --jqx-font-size: 12px;

  input {
    padding-left: 5px;
    box-sizing: border-box;

    &:hover {
      border: 1px solid var(--fluent-input-border-hover) !important;
    }

    &:focus {
      border: 1px solid var(--jqx-primary) !important;
      outline: 1px solid var(--jqx-primary) !important;
      color: var(--fluent-greys-grey190) !important;
    }
  }

  td {
    &:hover {
      background: var(--jqx-background-hover);
      color: var(--jqx-background-color-hover);
    }
  }
}

.jqx-date-time-input-popup-fluent table td a {
  text-decoration: none;
}

textarea.jqx-input-fluent:not(.underlined),
.jqx-text-area-fluent:not(.underlined),
.jqx-numberinput-fluent:not(.underlined),
.jqx-dropdownbutton-fluent:not(.underlined),
.jqx-dropdownlist-fluent:not(.underlined),
.jqx-combobox-fluent:not(.underlined),
.jqx-datetimeinput-fluent:not(.underlined) {
  --jqx-border: var(--fluent-input-border); 
  background: var(--jqx-background);
  color: var(--jqx-background-color);
  border-color: var(--jqx-background);
  border: 1px solid var(--jqx-border);
  
  &.jqx-numberinput-fluent,
  &.jqx-datetimeinput-fluent {
    padding-top: 3px;
    padding-bottom: 2px;
  }

  &.jqx-fill-state-disabled {
    background: var(--fluent-input-disabled-background);
    --jqx-background: var(--fluent-input-disabled-background);
    --jqx-surface: var(--fluent-input-disabled-background);
    pointer-events: none;
  }
  .jqx-action-button {
    margin-left: -3px;
  }

  &.jqx-numberinput-fluent {
    .jqx-action-button {
      margin-left: -3px !important;
    }
  }
  &:hover {
    border: 1px solid var(--fluent-input-border-hover);
  }

  &.jqx-fill-state-pressed,
  &.jqx-fill-state-focus,
  &:focus {
    border: 1px solid var(--jqx-primary);
    outline: 1px solid var(--jqx-primary) !important;
    color: var(--fluent-greys-grey190) !important;

    .jqx-icon-arrow-up-fluent,
    .jqx-icon-arrow-down-fluent,
    .jqx-icon-time-fluent {
      color: var(--fluent-greys-grey190) !important;
      font-weight: 400;
    }
  }

  &.jqx-numberinput-fluent,
  &.jqx-combobox-fluent {
    .jqx-combobox-arrow-normal-fluent,
    .jqx-action-button-fluent,
    .jqx-action-button-rtl-fluent  {
      &:hover {
        background-color: var(--jqx-background-hover);
        border-color: var(--jqx-background-hover);
        color: var(--jqx-background-color-hover);
      }
    }
  }

  &.underlined {
    border: none;
    border-bottom: 1px solid var(--jqx-border);
  }
}

.jqx-combobox-fluent .jqx-combobox-arrow-normal-fluent,
.jqx-datetimeinput-fluent .jqx-action-button-fluent,
.jqx-datetimeinput-fluent .jqx-action-button-rtl-fluent {
  background-color: var(--jqx-surface);
  border-color: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-datetimeinput-fluent,
.jqx-datetimeinput-fluent>div,
.jqx-numberinput-fluent,
.jqx-numberinput-fluent>div,
.jqx-dropdownlist-state-normal-fluent,
.jqx-dropdownlist-state-normal-fluent>div,
.jqx-dropdownlist-state-normal-fluent>div>div,
.jqx-combobox-state-normal-fluent,
.jqx-combobox-state-normal-fluent>div,
.jqx-combobox-state-normal-fluent>div>div {
  overflow: visible !important;
}

.jqx-input-fluent input:focus {
  border-radius: 0;
  box-shadow: none;
}

.jqx-input-fluent input,
input[type="text"].jqx-input-fluent,
input[type="password"].jqx-input-fluent,
input[type="text"].jqx-widget-content-fluent,
input[type="textarea"].jqx-widget-content-fluent,
textarea.jqx-input-fluent {
  --jqx-border: var(--fluent-input-border);
  font-size: var(--jqx-font-size);
  font-family: var(--jqx-font-family);
  resize: none;
  background: var(--jqx-background);
  color: var(--jqx-background-color);
  border-radius: 0;
  box-sizing: border-box;
  box-shadow: none;
  border: 1px solid var(--jqx-border);

  &.underlined {
    border: none;
    border-bottom: 1px solid var(--jqx-border);
  }
}


input[type="text"].jqx-widget-content-fluent,
input[type="textarea"].jqx-widget-content-fluent {
  height: 100%;
}


.jqx-input-label {
  visibility: inherit;
}

.jqx-input-bar {
  visibility: inherit;
}


input.underlined:focus~.jqx-input-label-fluent,
textarea.underlined:focus~.jqx-input-label-fluent 
.jqx-input-widget-fluent[hint=true].underlined .jqx-input-label,
.jqx-text-area-fluent[hint=true].underlined .jqx-input-label,
.jqx-dropdownlist-state-selected-fluent.underlined .jqx-input-label,
.jqx-dropdownlist-state-normal-fluent[hint=true].underlined .jqx-input-label,
.jqx-combobox-state-normal-fluent[hint=true].underlined .jqx-input-label,
.jqx-combobox-fluent.underlined .jqx-input-label.focused,
.jqx-dropdownlist-fluent.underlined .jqx-input-label.focused,
.jqx-datetimeinput-fluent[hint=true].underlined .jqx-input-label,
.jqx-maskedinput-fluent[hint=true].underlined .jqx-input-label,
.jqx-numberinput-fluent[hint=true].underlined .jqx-input-label,
.jqx-formattedinput-fluent[hint=true].underlined .jqx-input-label {
  top: -15px;
  font-size: 12px;
  color: var(--jqx-primary);
  opacity: 1 !important;
}

.jqx-dropdownlist-fluent[default-placeholder="true"] .jqx-input-label {
  visibility: hidden;
}

.jqx-input-widget-fluent.jqx-rtl>input {
  direction: rtl
}

.jqx-input-label-fluent {
  color: var(--jqx-background-color);
  font-size: 14px;
  font-weight: normal;
  position: absolute;
  pointer-events: none;
  left: 5px;
  top: 10px;
  opacity: 0.5;
  top: calc(50% - 7px);
  transition: 300ms ease all;
}


input:focus~.jqx-input-label-fluent,
textarea:focus~.jqx-input-label-fluent,
.jqx-widget-fluent[hint=true] .jqx-input-label-fluent {
  opacity: 0 !important;
}

.jqx-input-group-fluent.jqx-fill-state-disabled {
  pointer-events: none;
}

.jqx-input-label.initial {
  transition: none;
}

.jqx-input-group-fluent,
.jqx-widget-fluent {
  &.underlined {
    input:focus~.jqx-input-bar:before,
    textarea:focus~.jqx-input-bar:before,
    .jqx-dropdownlist-state-selected-fluent .jqx-input-bar:before,
    .jqx-dropdownlist-fluent .jqx-input-bar.focused:before,
    .jqx-dropdownbutton-fluent .jqx-input-bar.focused:before,
    .jqx-combobox-fluent .jqx-input-bar.focused:before,
    .jqx-input-bar.focused::before,
    .jqx-combobox-state-selected-fluent .jqx-input-bar:before {
      width: 100%;
    }

    .jqx-input-bar-fluent {
      position: relative;
      display: block;
      z-index: 1;
    }

    .jqx-input-bar-fluent:before {
      content: '';
      height: 2px;
      width: 0;
      bottom: 0px;
      position: absolute;
      background: var(--jqx-primary);
      transition: 300ms ease all;
      left: 0%;
    }
  }
}

.jqx-formatted-input-spin-button-fluent,
.jqx-input-group-addon-fluent {
  border-color: var(--jqx-background);
  color: var(--jqx-background-color);
  background: var(--jqx-background);
}

.jqx-dropdownlist-state-selected-fluent,
.jqx-combobox-state-selected-fluent {
  color: var(--jqx-primary);
  background: var(--jqx-primary-color);
  border-color: var(--jqx-primary-color);
}


.jqx-dropdownlist-state-normal-fluent .jqx-icon-arrow-down-fluent,
.jqx-combobox-state-normal-fluent .jqx-icon-arrow-down-fluent,
.sorticon.descending .jqx-grid-column-sorticon-fluent,
.jqx-tree-item-arrow-expand-fluent,
.jqx-expander-header-fluent .jqx-icon-arrow-down {
  transform: rotate(0deg);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease-out;
}

.jqx-expander-header-fluent .jqx-icon-arrow-up {
  transform: rotate(180deg);
  transition: transform 0.2s ease-out;
  font-family: jqx-icons;
  background-image: none;
}

.jqx-expander-header-fluent .jqx-icon-arrow-up:after {
  content: var(--jqx-icon-arrow-down);
  margin-left:3px;
}

.jqx-tree-item-arrow-expand-fluent,
.jqx-tree-item-arrow-collapse-fluent {
  font-size: 16px;
}

.jqx-tree-item-arrow-expand-fluent {
  transform: rotate(180deg);
}

.jqx-tree-item-arrow-expand-fluent:after {
  content: var(--jqx-icon-arrow-up);
  margin-left: 2px;
}

.jqx-tree-item-arrow-collapse-fluent {
  transform: rotate(0deg);
  background-image: none;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease-out;
}

.jqx-dropdownlist-state-selected-fluent .jqx-icon-arrow-down-fluent,
.jqx-combobox-state-selected-fluent .jqx-icon-arrow-down-fluent,
.sorticon.ascending .jqx-grid-column-sorticon-fluent {
  display: flex;
  align-items: center;
  transform: rotate(180deg);
  transition: transform 0.2s ease-out;
 
}

.jqx-combobox-state-selected-fluent .jqx-icon-arrow-down-fluent {
  left: -1px;
}

.jqx-listbox-container {
  margin-top: 1px;
}

input[type="text"].jqx-input-fluent:-moz-placeholder,
input[type="text"].jqx-widget-content-fluent:-moz-placeholder,
input[type="textarea"].jqx-widget-content-fluent:-moz-placeholder,
textarea.jqx-input-fluent:-moz-placeholder {
  color: #999999;
}

input[type="text"].jqx-input-fluent:-webkit-input-placeholder,
input[type="text"].jqx-widget-content-fluent:-webkit-input-placeholder,
input[type="textarea"].jqx-widget-content-fluent:-webkit-input-placeholder,
textarea.jqx-input-fluent:-webkit-input-placeholder {
  color: #999999;
}

input[type="text"].jqx-input-fluent:-ms-input-placeholder,
input[type="text"].jqx-widget-content-fluent:-ms-input-placeholder,
input[type="textarea"].jqx-widget-content-fluent:-ms-input-placeholder,
textarea.jqx-input-fluent:-ms-input-placeholder {
  color: #999999;
}

.jqx-combobox-content-focus-fluent,
.jqx-combobox-state-focus-fluent,
.jqx-fill-state-focus-fluent,
.jqx-numberinput-focus-fluent {
  outline: none;
}

.jqx-popup-fluent.jqx-fill-state-focus-fluent {
  outline: none;
  border-color: var(--jqx-border) !important;
}

.jqx-datetimeinput-content,
.jqx-datetimeinput-container {
  overflow: visible !important;
}

.jqx-text-area-fluent,
.jqx-text-area-fluent>div {
  overflow: visible !important;
}

.jqx-text-area-element-fluent {
  box-sizing: border-box;
}

.jqx-pivotgrid-content-wrapper.jqx-fill-state-normal-fluent {
  border-color: var(--jqx-border);
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-expand-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-collapse-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-collapse-rtl-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-expand-rtl-fluent {
  background-image: none;
}

.jqx-tabs-title-selected-top-fluent,
.jqx-tabs-selection-tracker-top-fluent {
  border-color: transparent;
  filter: none;
  background: inherit;
  color: inherit;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.jqx-grid-cell-filter-row-fluent {
  background-color: var(--jqx-surface);
}

.jqx-tabs-title-fluent,
.jqx-ribbon-item-fluent {
  color: inherit;
}

.jqx-ribbon-item-selected-fluent {
  background: inherit;
}

.jqx-tabs-title-selected-bottom-fluent,
.jqx-tabs-title-selected-top-fluent {
  color: var(--jqx-primary);
  font-weight: 500;
  padding-top: 5px;
  padding-bottom: 5px;
}

.jqx-tabs-title.jqx-fill-state-hover-fluent {
  border-color: transparent;
}

.jqx-tabs {
  border: 0;
  background: transparent;

  .jqx-tabs-header {
    .jqx-item {
      color: var(--fluent-btn-secondary-color);
   
      &.jqx-fill-state-pressed {
        &:hover {
          background: var(--fluent-btn-secondary-bg-hover);
        }
      }

      &.jqx-fill-state-hover {
        border-color: var(--fluent-btn-secondary-bg-hover);
        background: var(--fluent-btn-secondary-bg-hover);
      }
    }
  }
}

.jqx-ribbon-item-fluent {
  cursor: pointer;
}

.jqx-ribbon-item-selected-fluent {
  color: var(--jqx-primary);
  font-weight: 500;
  border-color: transparent;
}

.jqx-ribbon-item-hover-fluent {
  background: var(--jqx-background-hover);
  color: var(--jqx-background-color-hover);
}

.jqx-ribbon-header-top-fluent {
  border-color: transparent;
  border-bottom-color: var(--jqx-border);
}

.jqx-ribbon-header-bottom-fluent {
  border-color: transparent;
  border-top-color: var(--jqx-border);
}

.jqx-ribbon-header-right-fluent {
  border-color: transparent;
  border-left-color: var(--jqx-border);
}

.jqx-ribbon-header-left-fluent {
  border-color: transparent;
  border-right-color: var(--jqx-border);
}

.jqx-tabs-title-selected-bottom-fluent,
.jqx-tabs-selection-tracker-bottom-fluent {
  border-color: transparent;
  border-top: 1px solid var(--jqx-background);
  filter: none;
  background: var(--jqx-background);
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.jqx-tabs-fluent,
.jqx-ribbon-fluent {
  border-color: transparent;
}

.jqx-tabs-header-fluent {
  background: transparent;
}

.jqx-ribbon-header-fluent {
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-tabs-position-bottom .jqx-tabs-header-fluent {
  border-color: transparent;
}

.jqx-layout-fluent .jqx-tabs-header-fluent,
.jqx-layout-fluent .jqx-ribbon-header-fluent {
  background: var(--jqx-background);
  border-color: var(--jqx-border);
}

.jqx-tabs-title-bottom {
  border-color: transparent;
}

.jqx-tabs-title-hover-top-fluent,
.jqx-tabs-title-hover-bottom-fluent,
.jqx-tabs-header-fluent {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-tabs-content-fluent {
  box-sizing: border-box;
  border: 1px solid var(--jqx-border);
  border-top-color: transparent;
  padding: 5px;
}

.jqx-tabs-bar-fluent {
  position: absolute;
  bottom: 0;
  background: var(--jqx-primary);
  height: 2px;
  z-index: 10;
  transition: .5s cubic-bezier(.35, 0, .25, 1);
}

.jqx-tabs-bar-fluent.vertical {
  width: 2px;
}

.jqx-tabs-position-bottom .jqx-tabs-bar-fluent {
  top: 0;
}


.jqx-layout-fluent {
  background-color: var(--jqx-background);
}

.jqx-kanban-column-header-fluent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.jqx-kanban-column-header-collapsed-fluent {
  background: var(--jqx-surface);
  color: var(--jqx-surface-color);
}

.jqx-kanban-column-header-button-fluent {
  width: unset;
  height: unset;
  position:static;
  margin-top:unset;
  top: unset;
  right: unset;

  .jqx-window-collapse-button {
    right: unset;
  }
}
.jqx-kanban-column-header-collapsed {
  .jqx-kanban-column-header-button-fluent {
    position: absolute;
    bottom: 10px;
    right: 50%;
    margin-left: -10px;
  }
}

.jqx-calendar-cell-fluent {
  border-radius: 0px;
  font-size: 12px !important;
}

.jqx-calendar-cell-fluent.jqx-fill-state-hover-fluent {
  outline: 2px var(--fluent-greys-grey30);
  overflow: hidden;
  position: relative;
  border-radius: 0px;
  border-color: var(--fluent-greys-grey30) !important;
  background: var(--fluent-greys-grey30) !important;
  color: var(--fluent-greys-grey190) !important;
}
.jqx-calendar-cell-fluent.jqx-fill-state-pressed {
  border-color: var(--jqx-primary) !important;
  background: var(--jqx-primary) !important;
  color: var(--jqx-primary-color) !important;
  overflow: hidden;
  position: relative;
  border-radius: 50%;
}
.jqx-calendar-cell-today-fluent {
  border-radius: 0px;
  border-color: var(--fluent-greys-grey130) !important;
  background: var(--fluent-greys-grey30) !important;
  color: var(--fluent-greys-grey130) !important;
}
.jqx-calendar-cell-year-fluent,
.jqx-calendar-cell-decade-fluent {
  border-radius: 0px !important;
}

.jqx-calendar-cell-fluent.jqx-fill-state-pressed {
}

.jqx-calendar-cell-fluent.jqx-fill-state-pressed-fluent:after {
  content: '';
  width: calc(100% - 4px);
  position: absolute;
  left: 0px;
  top: 0px;
  height: calc(100% - 4px);
}

.jqx-calendar-cell-year-fluent,
.jqx-calendar-cell-decade-fluent {
  border-radius: 0px;
}

.jqx-calendar-title-container-fluent {
    height: 40px;
    align-items: center;
    display: grid;
    grid-template-columns: 1fr 30px 30px 20px;
    td {
      height: 100%;
    }
    td:first-child {
      order: 1;
    }
    td:last-child{
      order: 2;
    }
}
.jqx-calendar-title-content-fluent {
  font-weight: bold;
  padding-left: 26px !important;
  text-align: left;
  display: flex;
  align-items: center;
  height: 100%;
}

.jqx-calendar-column-cell-fluent {
  color: var(--jqx-background-color);
}

.jqx-icon-time-fluent,
.jqx-icon-time-hover-fluent,
.jqx-icon-time-pressed-fluent {
  background-image: none !important;
  font-family: 'jqx-icons';
  display: flex;
  font-family: 'jqx-icons';
  font-size: 16px;
  align-content: center;
  justify-content: center;
  left: initial !important;
  margin-top: 0px;
  top: 0px;
  left: 0px;
  margin: 0;
  align-items: center;
  width: 100%;
  height: 100%;
}

.jqx-icon-time-fluent:after,
.jqx-icon-time-hover-fluent:after,
.jqx-icon-time-pressed-fluent:after {
  content: var(--jqx-icon-clock);
  font-size: 12px;
}

.jqx-icon-calendar-fluent,
.jqx-icon-calendar-hover-fluent,
.jqx-icon-calendar-pressed-fluent {
  background-image: none !important;
  font-family: 'jqx-icons';
  left: 0;
  top: 0 !important;
  margin: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100% !important;
  height: 100% !important;
}

.jqx-icon-calendar-fluent:after,
.jqx-icon-calendar-hover-fluent:after,
.jqx-icon-calendar-pressed-fluent:after {
  content: var(--jqx-icon-calendar-alt);
}

.jqx-tabs-close-button-fluent,
.jqx-tabs-close-button-selected-fluent,
.jqx-tabs-close-button-hover-fluent {
  background-image: none;
}

.jqx-tabs-close-button-fluent:after {
  content: var(--jqx-icon-close);
}

.jqx-scrollbar-button-state-pressed-fluent .jqx-icon-arrow-up-selected-fluent {
  background-image: none;
}

.jqx-scrollbar-button-state-pressed-fluent .jqx-icon-arrow-down-selected-fluent {
  background-image: none;
}

.jqx-scrollbar-button-state-pressed-fluent .jqx-icon-arrow-left-selected-fluent {
  background-image: none;
}

.jqx-scrollbar-button-state-pressed-fluent .jqx-icon-arrow-right-selected-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-expand-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-collapse-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-collapse-rtl-fluent {
  background-image: none;
}

.jqx-grid-cell-fluent.jqx-grid-cell-selected-fluent>.jqx-grid-group-expand-rtl-fluent {
  background-image: none;
}

.jqx-grid-group-collapse-fluent {
  background-image: none;
}

.jqx-grid-group-collapse-rtl-fluent {
  background-image: none;
}

.jqx-grid-group-expand-fluent,
.jqx-grid-group-expand-rtl-fluent {
  background-image: none;
}

.jqx-icon-arrow-first-fluent,
.jqx-icon-arrow-last-fluent {
  background-image: none;
  font-family: jqx-icons;
}

.jqx-icon-arrow-first-fluent:after {
  content: var(--jqx-icon-first-page);
}

.jqx-icon-arrow-last-fluent:after {
  content: var(--jqx-icon-last-page);
}

/* Ripple effect */
.ripple {
  position: relative;
  transform: translate3d(0, 0, 0);
  overflow: hidden;
}

.ink {
  display: block;
  position: absolute;
  pointer-events: none;
  border-radius: 0%;
  transform: scaleX(0);
  background: rgba(var(--jqx-primary-rgb), 0.5);
  opacity: 0.25;
}

.jqx-scrollbar-fluent .jqx-icon-arrow-up,
.jqx-scrollbar-fluent .jqx-icon-arrow-down,
.jqx-scrollbar-fluent .jqx-icon-arrow-right,
.jqx-scrollbar-fluent .jqx-icon-arrow-left {
  display: flex;
  justify-content: center;
  align-items: center;
}

.outlined .ink,
.flat .ink {
  background: rgba(var(--jqx-primary-rgb), 0.5);
  overflow: hidden;
}

.ink.animate {
  animation: ripple .7s ease;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.jqx-tree-fluent .jqx-checkbox-fluent {
  margin-top: 6px !important;
  border-radius: 0px !important;
}

.jqx-tree-item-arrow-expand-fluent,
.jqx-tree-item-arrow-collapse-fluent {
  margin-top: 6px !important;
}

.jqx-checkbox-fluent .ripple,
.jqx-radiobutton-fluent .ripple {
  overflow: visible;
}

.jqx-checkbox-fluent .ink,
.jqx-radiobutton-fluent .ink {
  transform: scale(0);
  background: var(--jqx-primary);
  border-radius: 50%;
}

.jqx-checkbox-fluent.effect .ink.animate,
.jqx-radiobutton-fluent.effect .ink.animate {
  animation: checkRipple 0.3s ease;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.jqx-checkbox-fluent.effect .ink.active,
.jqx-radiobutton-fluent.effect .ink.active {
  opacity: 0.2;
  transform: scale(2);
}

.jqx-checkbox-default-fluent.active .ink,
.jqx-radiobutton-default-fluent.active .ink {
  opacity: 0.2;
  transform: scale(2);
}


@keyframes checkRipple {
  100% {
      opacity: 0.2;
      transform: scale(2);
      animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.jqx-fill-state-pressed-fluent .jqx-icon-delete-fluent {
  background-image: url('images/icon-delete-white.png');
}

.jqx-fill-state-pressed-fluent .jqx-icon-edit-fluent {
  background-image: url('images/icon-edit-white.png');
}

.jqx-fill-state-pressed-fluent .jqx-icon-save-fluent {
  background-image: url('images/icon-save-white.png');
}

.jqx-fill-state-pressed-fluent .jqx-icon-cancel-fluent {
  background-image: url('images/icon-cancel-white.png');
}

.jqx-fill-state-pressed-fluent .jqx-icon-search-fluent {
  background-image: url('images/search_white.png');
}

.jqx-fill-state-pressed-fluent .jqx-icon-plus-fluent {
  background-image: url('images/plus_white.png');
}

.jqx-menu-minimized-button-fluent {
  padding: 0px !important;
}

.jqx-fill-state-pressed-fluent .jqx-menu-minimized-button-fluent {
  background-image: url('images/icon-menu-minimized-white.png');
}

.jqx-editor-fluent {
  --jqx-border: var(--fluent-body-divider) !important;
}

.jqx-editor-toolbar-icon-fluent {
  background: url('images/html_editor.png') no-repeat;
}

.jqx-fill-state-hover-fluent .jqx-editor-toolbar-icon-fontsize-fluent,
.jqx-fill-state-pressed-fluent .jqx-editor-toolbar-icon-fontsize-fluent,
.jqx-fill-state-hover-fluent .jqx-editor-toolbar-icon-forecolor-fluent,
.jqx-fill-state-pressed-fluent .jqx-editor-toolbar-icon-forecolor-fluent {
  background: url('images/html_editor.png') no-repeat;
}

.jqx-editor-toolbar-button-fluent {
  border-color: var(--jqx-border);
  box-shadow: none !important;
  color: var(--jqx-background-color);
}

.jqx-time-picker .jqx-main-container {
  background: var(--jqx-background);
}

/*applied to the timepicker*/
.jqx-needle-central-circle-fluent {
  fill: var(--jqx-primary);
}

.jqx-time-picker-fluent .jqx-label-fluent {
  fill: var(--jqx-background-color);
}

.jqx-needle-fluent {
  fill: var(--jqx-primary);
}

.jqx-time-picker .jqx-header .jqx-selected-fluent:focus {
  outline: 2px solid var(--jqx-primary);
  box-shadow: 0px 0px 4px 2px rgba(0, 119, 190, 0.125);
}

.jqx-svg-picker-fluent:focus {
  border: 1px solid var(--jqx-primary) !important;
}

.jqx-validator-hint-fluent {
  background: #D94F43;
  border-color: #D94F43;
  padding: 10px;
}

.jqx-validator-hint-fluent img {
  display: none;
}


.jqx-grid-group-expand-fluent:after,
.jqx-grid-group-collapse-fluent:after {
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
  height: 100%;
}

.jqx-datatable-fluent {
  .jqx-grid-group-expand-fluent:after,
  .jqx-grid-group-collapse-fluent:after {
    margin-left: -10px;
  }
}

.jqx-grid-pager-fluent .jqx-dropdownlist-fluent  {
background: var(--jqx-background);
color: var(--jqx-background-color);
}
.jqx-grid-pager-input-fluent {
padding-right: 4px !important;
}
.jqx-grid-cell-selected-fluent {
  background: rgba(var(--jqx-primary-rgb), 0.8) !important;
  }
  .jqx-grid-cell-selected-fluent span {
      color: inherit !important;
  }

  
@keyframes css-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.jqx-loader-fluent {
  background: transparent;
  color: var(--fluent-theme-primary);
  box-shadow: unset !important;
  border: none !important;

  .jqx-loader-icon {
    background-image: unset;
    box-sizing: border-box;
    border-radius: 50%;
    border-width: 1.5px;
    border-style: solid;
    border-color: rgb(0, 120, 212) rgb(199, 224, 244) rgb(199, 224, 244);
    border-image: initial;
    animation-name: css-spinner;
    animation-duration: 1.3s;
    animation-iteration-count: infinite;
    animation-timing-function: cubic-bezier(0.53, 0.21, 0.29, 0.67);
    width: 28px;
    height: 28px;
    position: relative;
    margin: 0 auto 25px auto;
  }
}

/* ALERT */
.jqx-notification-fluent {
  display: flex;
  align-items: center;
  border-radius: 0;
  padding: 3px 10px;
  border: 0;
  font-size: 12px;
  color: var(--fluent-alert-color);

  box-shadow: unset !important;

  > div {
    display: flex;
    align-items: center;
  }

  &.jqx-notification-primary {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-theme-light) !important;
  }

  &.jqx-notification-info {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-greys-grey20) !important;
  }

  &.jqx-notification-success {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-success-bg) !important;
  }

  &.jqx-notification-warning {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-warning-bg) !important;
  }

  &.jqx-notification-severe-warning {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-severe-warning-bg) !important;
  }

  &.jqx-notification-error {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-error-bg) !important;
  }

  .jqx-notification-icon.jqx-notification-icon-info,
  .jqx-notification-icon.jqx-notification-icon-primary,
  .jqx-notification-icon.jqx-notification-icon-warning {
    background-image: url("data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 15C6.80729 15 6.14062 14.9115 5.5 14.7344C4.86458 14.5573 4.26823 14.3073 3.71094 13.9844C3.15365 13.6562 2.64583 13.2656 2.1875 12.8125C1.73438 12.3542 1.34375 11.8464 1.01562 11.2891C0.692708 10.7318 0.442708 10.1354 0.265625 9.5C0.0885417 8.85938 0 8.19271 0 7.5C0 6.80729 0.0885417 6.14323 0.265625 5.50781C0.442708 4.86719 0.692708 4.26823 1.01562 3.71094C1.34375 3.15365 1.73438 2.64844 2.1875 2.19531C2.64583 1.73698 3.15365 1.34635 3.71094 1.02344C4.26823 0.695312 4.86458 0.442708 5.5 0.265625C6.14062 0.0885417 6.80729 0 7.5 0C8.19271 0 8.85677 0.0885417 9.49219 0.265625C10.1328 0.442708 10.7318 0.695312 11.2891 1.02344C11.8464 1.34635 12.3516 1.73698 12.8047 2.19531C13.263 2.64844 13.6536 3.15365 13.9766 3.71094C14.3047 4.26823 14.5573 4.86719 14.7344 5.50781C14.9115 6.14323 15 6.80729 15 7.5C15 8.19271 14.9115 8.85938 14.7344 9.5C14.5573 10.1354 14.3047 10.7318 13.9766 11.2891C13.6536 11.8464 13.263 12.3542 12.8047 12.8125C12.3516 13.2656 11.8464 13.6562 11.2891 13.9844C10.7318 14.3073 10.1328 14.5573 9.49219 14.7344C8.85677 14.9115 8.19271 15 7.5 15ZM7.5 1C6.90104 1 6.32552 1.07812 5.77344 1.23438C5.22135 1.39062 4.70312 1.60938 4.21875 1.89062C3.73958 2.17188 3.30208 2.51042 2.90625 2.90625C2.51042 3.30208 2.17188 3.74219 1.89062 4.22656C1.60938 4.70573 1.39062 5.22396 1.23438 5.78125C1.07812 6.33333 1 6.90625 1 7.5C1 8.09375 1.07812 8.66927 1.23438 9.22656C1.39062 9.77865 1.60938 10.2969 1.89062 10.7812C2.17188 11.2604 2.51042 11.6979 2.90625 12.0938C3.30208 12.4896 3.73958 12.8281 4.21875 13.1094C4.70312 13.3906 5.22135 13.6094 5.77344 13.7656C6.32552 13.9219 6.90104 14 7.5 14C8.09375 14 8.66667 13.9219 9.21875 13.7656C9.77604 13.6094 10.2943 13.3906 10.7734 13.1094C11.2578 12.8281 11.6979 12.4896 12.0938 12.0938C12.4896 11.6979 12.8281 11.2604 13.1094 10.7812C13.3906 10.2969 13.6094 9.77865 13.7656 9.22656C13.9219 8.67448 14 8.09896 14 7.5C14 6.90625 13.9219 6.33333 13.7656 5.78125C13.6094 5.22396 13.3906 4.70573 13.1094 4.22656C12.8281 3.74219 12.4896 3.30208 12.0938 2.90625C11.6979 2.51042 11.2578 2.17188 10.7734 1.89062C10.2943 1.60938 9.77604 1.39062 9.21875 1.23438C8.66667 1.07812 8.09375 1 7.5 1ZM7 6H8V11H7V6ZM7 4H8V5H7V4Z' fill='%23605E5C'/%3E%3C/svg%3E");
    width: 30px;
    height: 25px;
  }

  .jqx-notification-icon.jqx-notification-icon-serve-warning {
    background-image: url("data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 15H0L7.5 0L15 15ZM1.61719 14H13.3828L7.5 2.23438L1.61719 14ZM8 6V11H7V6H8ZM7 12H8V13H7V12Z' fill='%23D83B01'/%3E%3C/svg%3E");
    width: 30px;
    height: 25px;
  }

  .jqx-notification-icon.jqx-notification-icon-success {
    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.6484 4.64844L12.3516 5.35156L6.5 11.2031L3.64844 8.35156L4.35156 7.64844L6.5 9.79688L11.6484 4.64844ZM8 0C8.73438 0 9.44271 0.0963542 10.125 0.289062C10.8073 0.476562 11.4453 0.744792 12.0391 1.09375C12.6328 1.4375 13.1719 1.85417 13.6562 2.34375C14.1458 2.82812 14.5625 3.36719 14.9062 3.96094C15.2552 4.55469 15.5234 5.19271 15.7109 5.875C15.9036 6.55729 16 7.26562 16 8C16 8.73438 15.9036 9.44271 15.7109 10.125C15.5234 10.8073 15.2552 11.4453 14.9062 12.0391C14.5625 12.6328 14.1458 13.1745 13.6562 13.6641C13.1719 14.1484 12.6328 14.5651 12.0391 14.9141C11.4453 15.2578 10.8073 15.526 10.125 15.7188C9.44271 15.9062 8.73438 16 8 16C7.26562 16 6.55729 15.9062 5.875 15.7188C5.19271 15.526 4.55469 15.2578 3.96094 14.9141C3.36719 14.5651 2.82552 14.1484 2.33594 13.6641C1.85156 13.1745 1.4349 12.6328 1.08594 12.0391C0.742188 11.4453 0.473958 10.8099 0.28125 10.1328C0.09375 9.45052 0 8.73958 0 8C0 7.26562 0.09375 6.55729 0.28125 5.875C0.473958 5.19271 0.742188 4.55469 1.08594 3.96094C1.4349 3.36719 1.85156 2.82812 2.33594 2.34375C2.82552 1.85417 3.36719 1.4375 3.96094 1.09375C4.55469 0.744792 5.1901 0.476562 5.86719 0.289062C6.54948 0.0963542 7.26042 0 8 0ZM8 15C8.64062 15 9.25781 14.9167 9.85156 14.75C10.4505 14.5833 11.0078 14.349 11.5234 14.0469C12.0443 13.7396 12.5182 13.3724 12.9453 12.9453C13.3724 12.5182 13.737 12.0469 14.0391 11.5312C14.3464 11.0104 14.5833 10.4531 14.75 9.85938C14.9167 9.26562 15 8.64583 15 8C15 7.35938 14.9167 6.74219 14.75 6.14844C14.5833 5.54948 14.3464 4.99219 14.0391 4.47656C13.737 3.95573 13.3724 3.48177 12.9453 3.05469C12.5182 2.6276 12.0443 2.26302 11.5234 1.96094C11.0078 1.65365 10.4505 1.41667 9.85156 1.25C9.25781 1.08333 8.64062 1 8 1C7.35938 1 6.73958 1.08333 6.14062 1.25C5.54688 1.41667 4.98958 1.65365 4.46875 1.96094C3.95312 2.26302 3.48177 2.6276 3.05469 3.05469C2.6276 3.48177 2.26042 3.95573 1.95312 4.47656C1.65104 4.99219 1.41667 5.54948 1.25 6.14844C1.08333 6.74219 1 7.35938 1 8C1 8.64062 1.08333 9.26042 1.25 9.85938C1.41667 10.4531 1.65104 11.0104 1.95312 11.5312C2.26042 12.0469 2.6276 12.5182 3.05469 12.9453C3.48177 13.3724 3.95312 13.7396 4.46875 14.0469C4.98958 14.349 5.54688 14.5833 6.14062 14.75C6.73438 14.9167 7.35417 15 8 15Z' fill='%23107C10'/%3E%3C/svg%3E");
  }

  .jqx-notification-icon.jqx-notification-icon-error {
    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 0C8.73438 0 9.44271 0.0963542 10.125 0.289062C10.8073 0.476562 11.4427 0.744792 12.0312 1.09375C12.625 1.44271 13.1641 1.86198 13.6484 2.35156C14.138 2.83594 14.5573 3.375 14.9062 3.96875C15.2552 4.55729 15.5234 5.19271 15.7109 5.875C15.9036 6.55729 16 7.26562 16 8C16 8.73438 15.9036 9.44271 15.7109 10.125C15.5234 10.8073 15.2552 11.4453 14.9062 12.0391C14.5573 12.6276 14.138 13.1667 13.6484 13.6562C13.1641 14.1406 12.625 14.5573 12.0312 14.9062C11.4427 15.2552 10.8073 15.526 10.125 15.7188C9.44271 15.9062 8.73438 16 8 16C7.26562 16 6.55729 15.9062 5.875 15.7188C5.19271 15.526 4.55469 15.2552 3.96094 14.9062C3.3724 14.5573 2.83333 14.1406 2.34375 13.6562C1.85938 13.1667 1.44271 12.6276 1.09375 12.0391C0.744792 11.4453 0.473958 10.8073 0.28125 10.125C0.09375 9.44271 0 8.73438 0 8C0 7.26562 0.09375 6.55729 0.28125 5.875C0.473958 5.19271 0.744792 4.55729 1.09375 3.96875C1.44271 3.375 1.85938 2.83594 2.34375 2.35156C2.83333 1.86198 3.3724 1.44271 3.96094 1.09375C4.55469 0.744792 5.19271 0.476562 5.875 0.289062C6.55729 0.0963542 7.26562 0 8 0ZM8 15C8.64583 15 9.26562 14.9167 9.85938 14.75C10.4583 14.5833 11.0156 14.349 11.5312 14.0469C12.0521 13.7396 12.5234 13.375 12.9453 12.9531C13.3724 12.526 13.737 12.0547 14.0391 11.5391C14.3464 11.0182 14.5833 10.4609 14.75 9.86719C14.9167 9.26823 15 8.64583 15 8C15 7.35417 14.9167 6.73438 14.75 6.14062C14.5833 5.54167 14.3464 4.98438 14.0391 4.46875C13.737 3.94792 13.3724 3.47656 12.9453 3.05469C12.5234 2.6276 12.0521 2.26302 11.5312 1.96094C11.0156 1.65365 10.4583 1.41667 9.85938 1.25C9.26562 1.08333 8.64583 1 8 1C7.35417 1 6.73177 1.08333 6.13281 1.25C5.53906 1.41667 4.98177 1.65365 4.46094 1.96094C3.94531 2.26302 3.47396 2.6276 3.04688 3.05469C2.625 3.47656 2.26042 3.94792 1.95312 4.46875C1.65104 4.98438 1.41667 5.54167 1.25 6.14062C1.08333 6.73438 1 7.35417 1 8C1 8.64583 1.08333 9.26823 1.25 9.86719C1.41667 10.4609 1.65104 11.0182 1.95312 11.5391C2.26042 12.0547 2.625 12.526 3.04688 12.9531C3.47396 13.375 3.94531 13.7396 4.46094 14.0469C4.98177 14.349 5.53906 14.5833 6.13281 14.75C6.73177 14.9167 7.35417 15 8 15ZM11.4609 5.24219L8.71094 8L11.4609 10.7578L10.7578 11.4609L8 8.71094L5.24219 11.4609L4.53906 10.7578L7.28906 8L4.53906 5.24219L5.24219 4.53906L8 7.28906L10.7578 4.53906L11.4609 5.24219Z' fill='%23A4262C'/%3E%3C/svg%3E");
  }

  .jqx-notification-close-button {
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='10' viewBox='0 0 10 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.44434 5L9.90723 9.46777L9.46777 9.90723L5 5.44434L0.532227 9.90723L0.0927734 9.46777L4.55566 5L0.0927734 0.532227L0.532227 0.0927734L5 4.55566L9.46777 0.0927734L9.90723 0.532227L5.44434 5Z' fill='%23201F1E'/%3E%3C/svg%3E") !important;
    right: 0 !important;
    top: 0 !important;
    background-position: center center !important;
  }
}

.jqx-editor-fluent {
  .jqx-button-fluent {
    --jqx-border:var(--fluent-body-divider);
    &:hover {
      border-color: var(--fluent-input-border-hover);
    }

    &:focus {
      border-color: var(--jqx-primary);
      outline-color: var(--jqx-primary);
      color: var(--fluent-greys-grey190);
    }
  }
}


.jqx-widget-fluent:not(.jqx-input-group):not(.jqx-form-fluent)
{
  .jqx-widget-fluent:not(.jqx-button-fluent),
  input {
    --jqx-border:var(--fluent-body-divider) !important;

    &.jqx-input {
      &:hover {
        border-color: var(--fluent-input-border-hover) !important;
      }

      &:focus {
        border-color: var(--jqx-primary) !important;
        outline-color: var(--jqx-primary) !important;
        color: var(--fluent-greys-grey190) !important;
      }
    }
  }

  textarea,
  input {
    padding-left: 5px;
    box-sizing: border-box;
  
    &.jqx-grid-cell-edit {
      position: relative;
      left: 1px;
      top: 1px;
      width: calc(100% - 2px) !important;
      height: calc(100% - 2px) !important;
      border-width: 1px !important;
      border-style: solid !important;
      border-color: var(--fluent-input-border) !important;

      &:hover {
        border-color: var(--fluent-input-border-hover) !important;
      }

      &:focus {
        border-color: var(--jqx-primary) !important;
        outline-color: var(--jqx-primary) !important;
        color: var(--fluent-greys-grey190) !important;
      }
    }
   
    &:hover {
      border-color: var(--fluent-input-border-hover);
    }
  
    &.jqx-filter-input {
      &:focus {
        outline: 1px solid var(--jqx-primary);
      }

      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    &:focus {
      border-color: var(--jqx-primary);
      outline-color: var(--jqx-primary);
      color: var(--fluent-greys-grey190);
    }
  }
}

.jqx-form-fluent
{
  .jqx-widget-fluent:not(.jqx-button-fluent),
  input {
    --jqx-border:var(--fluent-input-border);
  }
}
.jqx-switchbutton-fluent {
  border-color: var(--fluent-input-border);
  border-radius: 15px;
  .jqx-switchbutton-label {
       font-size: inherit;
       font-family: inherit;
       text-align: center;
       text-transform: inherit;
       font-weight: normal;
  }

  .jqx-switchbutton-thumb-fluent {
    border-radius: 50%;
    transform: scale(0.7);
    background: var(--fluent-greys-grey190);
    color:var(--fluent-greys-grey190);  
    border-color:var(--fluent-greys-grey190);  
 }

  &.jqx-switchbutton-on {
    background: var(--jqx-primary);
    border-color: var(--jqx-primary); 
    color:var(--jqx-primary-color);

    .jqx-switchbutton-thumb-fluent {
      background: var(--jqx-background);
       border-color: var(--jqx-background);
     color:var(--jqx-background);
    }
  }

 
}