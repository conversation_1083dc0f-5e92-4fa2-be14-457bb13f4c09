﻿.jqx-widget-light {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
    color: #555;
}
.jqx-widget-content-light {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
    color: #555;
}
.jqx-widget-header-light {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
    background: #fff;
    color: #555;
}

/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-light {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-light {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-light {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-light {
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*top rounded Corners*/
.jqx-rc-t-light {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom rounded Corners*/
.jqx-rc-b-light {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*right rounded Corners*/
.jqx-rc-r-light {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*left rounded Corners*/
.jqx-rc-l-light {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*all rounded Corners*/
.jqx-rc-all-light {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.jqx-widget-light, .jqx-widget-header-light, .jqx-fill-state-normal-light,
.jqx-widget-content-light, .jqx-fill-state-hover-light, .jqx-fill-state-pressed-light {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
}

.jqx-widget-content-light {
    background-color: #ffffff;
    border-color: #dddddd;
}
.jqx-widget-header-light {
    color: #555;
   	background-color:#f8f8f8; 
    border-color:#dddddd;
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.jqx-widget-light input::selection, input.jqx-input-widget-light::selection, .jqx-widget-content-light input::selection {
    background: #0077BE;
    color: #fff;
}
.jqx-button-light {
    transition: color .2s ease-in-out,background-color .2s ease-in-out,border-color .2s ease-in-out,box-shadow .2s ease-in-out;
}
.jqx-button-light, .jqx-fill-state-normal-light  {
    color: #555;
  	background:#fafafa;
    border-color: #dddddd;
    *zoom: 1;
}

.jqx-fill-state-hover-light {
    color: #373a3c;
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.3);
    background-color: #f0f0f0;
 }
.jqx-fill-state-pressed-light {
    color: #fff ;
    background-color: #0077BE;
    border-color: #0077BE;
    *background-color: #fff;
}

.jqx-fill-state-hover-light, .jqx-fill-state-focus-light {
    color: #333333;
    text-decoration: none;
}
.jqx-fill-state-focus-light, .jqx-item-light.jqx-fill-state-focus {
    border-color: #0077BE;
}
.jqx-fill-state-pressed-light.jqx-fill-state-hover-light, .jqx-dropdownlist-state-selected-light{
  color: #fff ;
}
.jqx-datetimeinput-light .jqx-action-button-light.jqx-fill-state-hover{
    border-color: #ddd;
}
.jqx-datetimeinput-light.jqx-fill-state-focus .jqx-action-button-light{
    border-color: #0077BE;
}
.jqx-filter-input-light:focus {
    border-color: #0077BE !important;
}

.jqx-button-light  {
    color: #555;
    border-color: #ddd;
    *zoom: 1;
}

.jqx-button-light.jqx-fill-state-hover  {
  
    *zoom: 1;
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-button-light.jqx-fill-state-pressed  {
    color: #fff;
  	background:#0077BE;
    border-color: #0077BE;
    *zoom: 1;
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}

.jqx-dropdownlist-state-normal-light, .jqx-dropdownlist-state-hover-light, .jqx-dropdownlist-state-selected-light,
.jqx-scrollbar-button-state-hover-light, .jqx-scrollbar-button-state-normal-light, .jqx-scrollbar-button-state-pressed-light,
.jqx-scrollbar-thumb-state-normal-horizontal-light, .jqx-scrollbar-thumb-state-hover-horizontal-light, .jqx-scrollbar-thumb-state-pressed-horizontal-light,
.jqx-scrollbar-thumb-state-normal-light, .jqx-scrollbar-thumb-state-pressed-light, .jqx-tree-item-hover-light, .jqx-tree-item-selected-light,
.jqx-tree-item-light, .jqx-menu-item-light, .jqx-menu-item-hover-light, .jqx-menu-item-selected-light, .jqx-menu-item-top-light, .jqx-menu-item-top-hover-light, 
.jqx-menu-item-top-selected-light, .jqx-slider-button-light, .jqx-slider-slider-light
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-primary-light
{
  color: #0077BE  !important;
  background: #fff  !important;
  border-color: #0077BE!important;
  text-shadow: none !important;
}
.jqx-primary-light.jqx-dropdownlist-state-normal-light,
.jqx-primary-light.jqx-slider-button-light,
.jqx-primary-light.jqx-slider-slider-light,
.jqx-primary-light.jqx-combobox-arrow-normal-light,
.jqx-primary-light.jqx-combobox-arrow-hover-light,
.jqx-primary-light.jqx-action-button-light,
.jqx-primary-light:hover,
.jqx-primary-light:focus,
.jqx-primary-light:active,
.jqx-primary-light.active,
.jqx-primary-light.disabled,
.jqx-primary-light[disabled] {
 color: #fff  !important;
  background: #0077BE  !important;
  border-color: #0077BE !important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-light.jqx-primary-light,
.jqx-primary-light:active,
.jqx-primary-light.active {
 color: #fff  !important;
  background-color: #0077BE  !important;
  border-color: #0077BE!important;
  text-shadow: none !important;
}

.jqx-success-light
{
  color: #5cb85c  !important;
  background: #fff  !important;
  border-color: #5cb85c!important;
  text-shadow: none !important;
}
.jqx-success-light.jqx-dropdownlist-state-normal-light,
.jqx-success-light.jqx-slider-button-light,
.jqx-success-light.jqx-slider-slider-light,
.jqx-success-light.jqx-combobox-arrow-normal-light,
.jqx-success-light.jqx-combobox-arrow-hover-light,
.jqx-success-light.jqx-action-button-light,
.jqx-success-light:hover,
.jqx-success-light:focus,
.jqx-success-light:active,
.jqx-success-light.active,
.jqx-success-light.disabled,
.jqx-success-light[disabled] {
 color: #fff  !important;
  background: #5cb85c  !important;
  border-color: #5cb85c!important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-light.jqx-success-light,
.jqx-success-light:active,
.jqx-success-light.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #5cb85c  !important;
  border-color: #5cb85c!important;
}

.jqx-inverse-light
{
  text-shadow: none !important;
  color: #666  !important;
  background: #fff  !important;
  border-color: #cccccc!important;
}
.jqx-inverse-light.jqx-dropdownlist-state-normal-light,
.jqx-inverse-light.jqx-slider-button-light,
.jqx-inverse-light.jqx-slider-slider-light,
.jqx-inverse-light.jqx-combobox-arrow-hover-light,
.jqx-inverse-light.jqx-combobox-arrow-normal-light,
.jqx-inverse-light.jqx-action-button-light,
.jqx-inverse-light:hover,
.jqx-inverse-light:focus,
.jqx-inverse-light:active,
.jqx-inverse-light.active,
.jqx-inverse-light.disabled,
.jqx-inverse-light[disabled] {
  text-shadow: none !important;
 color: #666  !important;
  background: #cccccc  !important;
  border-color: #cccccc!important;
}

.jqx-fill-state-pressed-light.jqx-inverse-light,
.jqx-inverse-light:active,
.jqx-inverse-light.active {
  text-shadow: none !important;
 color: #666  !important;
  background: #cccccc  !important;
  border-color: #cccccc!important;
}


.jqx-danger-light
{
  text-shadow: none !important;
  color: #d9534f  !important;
  background: #fff  !important;
  border-color: #d9534f!important;
}
.jqx-danger-light.jqx-dropdownlist-state-normal-light,
.jqx-danger-light.jqx-slider-button-light,
.jqx-danger-light.jqx-slider-slider-light,
.jqx-danger-light.jqx-combobox-arrow-hover-light,
.jqx-danger-light.jqx-combobox-arrow-normal-light,
.jqx-danger-light.jqx-action-button-light,
.jqx-danger-light:hover,
.jqx-danger-light:focus,
.jqx-danger-light:active,
.jqx-danger-light.active,
.jqx-danger-light.disabled,
.jqx-danger-light[disabled] {
  text-shadow: none !important;
 color: #fff  !important;
  background: #d9534f  !important;
  border-color: #d9534f!important;
}

.jqx-fill-state-pressed-light.jqx-danger-light,
.jqx-danger-light:active,
.jqx-danger-light.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #d9534f  !important;
  border-color: #d9534f!important;
}


.jqx-warning-light
{
  text-shadow: none !important;
  color: #f0ad4e  !important;
  background: #fff  !important;
  border-color: #f0ad4e!important;
}
.jqx-warning-light.jqx-dropdownlist-state-normal-light,
.jqx-warning-light.jqx-slider-button-light,
.jqx-warning-light.jqx-slider-slider-light,
.jqx-warning-light.jqx-combobox-arrow-hover-light,
.jqx-warning-light.jqx-combobox-arrow-normal-light,
.jqx-warning-light.jqx-action-button-light,
.jqx-warning-light:hover,
.jqx-warning-light:focus,
.jqx-warning-light:active,
.jqx-warning-light.active,
.jqx-warning-light.disabled,
.jqx-warning-light[disabled] {
  text-shadow: none !important;
 color: #fff  !important;
  background: #f0ad4e  !important;
  border-color: #f0ad4e!important;
}

.jqx-fill-state-pressed-light.jqx-warning-light,
.jqx-warning-light:active,
.jqx-warning-light.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #f0ad4e  !important;
  border-color: #f0ad4e!important;
}


.jqx-info-light
{
  text-shadow: none !important;
  color: #5bc0de  !important;
  background: #fff  !important;
  border-color: #5bc0de!important;
}
.jqx-info-light.jqx-dropdownlist-state-normal-light,
.jqx-info-light.jqx-slider-button-light,
.jqx-info-light.jqx-slider-slider-light,
.jqx-info-light.jqx-combobox-arrow-hover-light,
.jqx-info-light.jqx-combobox-arrow-normal-light,
.jqx-info-light.jqx-action-button-light,
.jqx-info-light:hover,
.jqx-info-light:focus,
.jqx-info-light:active,
.jqx-info-light.active,
.jqx-info-light.disabled,
.jqx-info-light[disabled] {
 color: #fff  !important;
  background: #5bc0de  !important;
  border-color: #5bc0de!important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-light.jqx-info-light,
.jqx-info-light:active,
.jqx-info-light.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #5bc0de  !important;
  border-color: #5bc0de!important;
}

.jqx-fill-state-pressed-light {
    background-image: none;
    outline: 0;
}

.jqx-grid-cell-light {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-column-menubutton-light {
    background-color: transparent;
    border-color: #ddd !important;
}
.jqx-cell-light {
    font-size: 14px;
}

.jqx-calendar-light > div {
    padding: 10px;
    box-sizing: border-box;
}

.jqx-calendar-light .jqx-widget-header-light
{
    background-color: #fff;
    font-size:12px;
    box-shadow:none;
}
.jqx-calendar-row-header-light, .jqx-calendar-top-left-header-light {
    background-color: #f0f0f0;
    border-color: #f2f2f2;
    box-shadow:none;
}
.jqx-calendar-title-content-light {
    font-weight:bold;
}
.jqx-calendar-column-header-light {
    background-color: #FFF;
    border-top-color: #fff;
    box-shadow:none;
    border-bottom-color: #e9e9e9;
}
.jqx-calendar-light > div {
    padding: 10px;
    box-sizing: border-box;
}
.jqx-expander-header-light {
    padding-top: 10px; padding-bottom: 10px;
}
 .jqx-expander-header.jqx-fill-state-hover-light,
 .jqx-expander-header.jqx-fill-state-normal-light,
 .jqx-expander-header.jqx-fill-state-pressed-light
 {
      background: #fff;
      border-color: #e0e0e0;
      color: #333;
}
.jqx-expander-header.jqx-fill-state-hover-light {
    background: #f6f6f6;
}
.jqx-expander-header.jqx-fill-state-focus-light {
    border-color: #0077BE;
}
.jqx-expander-content.jqx-fill-state-focus-light {
    border-color: #e0e0e0;
    background: #fdfdfd;
}
.jqx-expander-header-light {
    padding:10px;
}

.jqx-ribbon-header-vertical-light, .jqx-widget-header-vertical-light {
	background:#f0f0f0;
}

.jqx-scrollbar-state-normal-light {
    background-color: #f8f8f8;
    border: 1px solid #f8f8f8;
    border-left-color: #ddd;
}

.jqx-scrollbar-thumb-state-normal-light, .jqx-scrollbar-thumb-state-normal-horizontal-light {
    background: #f6f6f6;
    border-color: #b3b3b3;
}

.jqx-scrollbar-thumb-state-hover-light, .jqx-scrollbar-thumb-state-hover-horizontal-light {
    background: #e6e6e6;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-light {
    background: #f7f7f7 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-light, .jqx-splitter-collapse-button-horizontal-light {
    background: #0077BE;
}

.jqx-splitter-collapse-button-vertical-light, .jqx-progressbar-value-vertical-light {
    background: #0077BE;
}


.jqx-scrollbar-thumb-state-pressed-light, .jqx-splitter-splitbar-vertical-light, .jqx-splitter-splitbar-horizontal-light, .jqx-scrollbar-thumb-state-pressed-horizontal-light,
.jqx-scrollbar-button-state-pressed-light
 {
    background: #d9d9d9;
    border-color: #b3b3b3;
     box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-grid-column-sortdescbutton-light, jqx-grid-column-filterbutton-light, .jqx-grid-column-sortascbutton-light {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #ddd;
}

.jqx-menu-vertical-light {
    background: #ffffff;
    filter: none;
}

.jqx-checkbox-check-checked-light {
    background: transparent url(./images/material_check_black.png) center center no-repeat;
}
.jqx-checkbox-check-indeterminate-light {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: #0379BF;
}
.jqx-checkbox-hover {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}
.jqx-combobox-content-light, .jqx-input-light {
}

.jqx-combobox-content-light {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.25);
}

.jqx-grid-bottomright-light, .jqx-panel-bottomright-light, .jqx-listbox-bottomright-light {
    background-color: #f8f8f8;
}

.jqx-window-light, .jqx-tooltip-light {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}

.jqx-docking-light .jqx-window-light {
    box-shadow: none;
}

.jqx-docking-panel-light .jqx-window-light {
    box-shadow: none;
}

.jqx-radiobutton-light {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
}

.jqx-radiobutton-light-light, .jqx-radiobutton-hover-light {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
}

.jqx-radiobutton-check-checked-light {
    filter: none;
    background: #0077BE;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-light {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-light {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-slider-track-horizontal-light, .jqx-slider-track-vertical-light {
    border-color: #ddd;
    background: #f0f0f0;
}

.jqx-slider-button-light {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-color: transparent;
    border-color: transparent !important;
}

.jqx-slider-slider-light {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-has-value-light {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-color: #0077BE;
    border-color: #0077BE !important;
}

.jqx-slider-slider-light:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}

.jqx-slider-tooltip-light, .jqx-slider-tooltip-light .jqx-fill-state-normal-light {
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0077BE; 
    border-color: #0077BE;
    color: #fff;
    font-size:11px;
}

.jqx-slider-tooltip-light .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-light .jqx-tooltip-arrow-l-r {
     background: #0077BE; 
    border-color: #0077BE;
}
.jqx-listitem-state-normal-light,
.jqx-listitem-state-hover-light,
.jqx-listitem-state-selected-light
 {
    padding-top:5px;
    padding-bottom:5px;
    margin:0px;
    border-radius: 0px;
}

.jqx-listitem-state-normal-light.checkboxes,
.jqx-listitem-state-hover-light.checkboxes,
.jqx-listitem-state-selected-light.checkboxes {
    border-radius: 4px;
}

.jqx-listitem-element-light {
 
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-light, .jqx-menu-item-hover-light, .jqx-tree-item-hover-light, .jqx-calendar-cell-hover-light, .jqx-grid-cell-hover-light,
.jqx-menu-vertical-light .jqx-menu-item-top-hover-light, .jqx-input-popup-light .jqx-fill-state-hover-light,
.jqx-input-popup-light .jqx-fill-state-pressed-light {
    color: #0077BE !important;
    border-color: #e1f5fe;
    text-decoration: none;
    background-color: #e1f5fe;
    background-repeat: repeat-x;
    outline: 0;
    background: #e1f5fe; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}
.jqx-scheduler-cell-hover-light {
    border-color: #e3f5fb !important;
    background: #e3f5fb !important;
}
.jqx-grid-table-dark {
    font-size: 14px;
}


.jqx-listitem-state-selected-light, .jqx-menu-item-selected-light, .jqx-tree-item-selected-light, .jqx-calendar-cell-selected-light, .jqx-grid-cell-selected-light,
.jqx-menu-vertical-light .jqx-menu-item-top-selected-light, .jqx-grid-selectionarea-light, .jqx-input-button-header-light, .jqx-input-button-innerHeader-light {
    color: #ffffff !important;
    background-color: #0077BE;
    *background-color: #0077BE;
    background-repeat: repeat-x;
    border-color: #0077BE !important;
    background: #0077BE; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-scheduler-cell-selected-light {
    border-color: #0077BE !important;
    background: #0077BE !important;
}
.jqx-grid-cell-light .jqx-button-light, .jqx-grid-cell-light .jqx-button-light.jqx-fill-state-hover-light, .jqx-grid-cell-light .jqx-button-light.jqx-fill-state-pressed-light {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
   -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.jqx-popup-light {
    border: 1px solid #ddd;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}
.jqx-grid-column-sortascbutton-light, .jqx-expander-arrow-bottom-light, .jqx-window-collapse-button-light, .jqx-menu-item-arrow-up-light, .jqx-menu-item-arrow-up-selected-light, .jqx-menu-item-arrow-top-up-light, .jqx-icon-arrow-up-light, .jqx-icon-arrow-up-hover-light, .jqx-icon-arrow-up-selected-light {
    background-image: url('images/metro-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-light .jqx-grid-group-expand-light, .jqx-grid-group-expand-light, .jqx-grid-column-menubutton-light, .jqx-grid-column-sortdescbutton-light, .jqx-expander-arrow-top-light, .jqx-window-collapse-button-collapsed-light, .jqx-menu-item-arrow-down-light, .jqx-menu-item-arrow-down-selected-light, .jqx-menu-item-arrow-down-light, .jqx-icon-arrow-down-light, .jqx-icon-arrow-down-hover-light, .jqx-icon-arrow-down-selected-light {
    background-image: url('images/metro-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-light, .jqx-menu-item-arrow-left-selected-light, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-light, .jqx-icon-arrow-down-left-light, .jqx-icon-arrow-left-selected-light {
    background-image: url('images/metro-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-light .jqx-grid-group-collapse-light, .jqx-grid-group-collapse-light, .jqx-tabs-arrow-right-light, .jqx-menu-item-arrow-right-selected-light, .jqx-menu-item-arrow-top-right-light, .jqx-icon-arrow-right-light, .jqx-icon-arrow-right-hover-light, .jqx-icon-arrow-right-selected-light {
    background-image: url('images/metro-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tree-item-arrow-collapse-rtl-light, .jqx-tree-item-arrow-collapse-hover-rtl-light {
    background-image: url(./images/metro-icon-left.png);
}

.jqx-menu-item-arrow-left-selected-light {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-light {
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-light {
    font-size: 10px;
}
.jqx-widget .jqx-grid-cell, .jqx-widget .jqx-grid-column-header, .jqx-widget .jqx-grid-group-cell {border-color: #ddd;}
.jqx-grid-groups-row-light > span {
    padding-left: 4px;
}
.jqx-combobox-content-light, .jqx-input-light {
    border-color: #ddd;
    color: #555555;
    background-color: #ffffff;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-light, .jqx-combobox-light, .jqx-combobox-state-normal-light {
    border-color: #ddd;
}

.jqx-combobox-content-focus-light, .jqx-combobox-state-focus-light, .jqx-fill-state-focus-light,
.jqx-numberinput-focus-light {
    outline: none;
    border-color: #959595;
}


input[type="text"].jqx-input-light, input[type="password"].jqx-input-light, input[type="text"].jqx-widget-content-light, input[type="textarea"].jqx-widget-content-light, textarea.jqx-input-light {
    font-size: 14px;
    padding-left:3px;
    padding-right: 3px;
    resize: none;
}

input[type="text"].jqx-input-light:-moz-placeholder, input[type="text"].jqx-widget-content-light:-moz-placeholder, input[type="textarea"].jqx-widget-content-light:-moz-placeholder, textarea.jqx-input-light:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-light:-webkit-input-placeholder, input[type="text"].jqx-widget-content-light:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-light:-webkit-input-placeholder, textarea.jqx-input-light:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-light:-ms-input-placeholder, input[type="text"].jqx-widget-content-light:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-light:-ms-input-placeholder, textarea.jqx-input-light:-ms-input-placeholder {
    color: #999999;
}

.jqx-combobox-content-light, .jqx-input-light {
    border-color: #ddd;
    color: #555555;
    background-color: #ffffff;
}

.jqx-combobox-content-light, .jqx-combobox-light, .jqx-combobox-state-normal-light {
    border-color: #ddd;
}

.jqx-combobox-content-focus-light, .jqx-combobox-state-focus-light, .jqx-fill-state-focus-light,
.jqx-numberinput-focus-light {
    outline: none;
    border-color: #0077BE;
}
.jqx-popup-light.jqx-fill-state-focus-light
{
    outline: none;
    border-color: #ddd !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-slider-rangebar-light {
    border-color: #0077BE;
    background: #0077BE;
}
.jqx-switchbutton-light{
    border-radius: 15px;
}
.jqx-switchbutton-light .jqx-fill-state-normal-light,
.jqx-switchbutton-light .jqx-fill-state-hover-light,
.jqx-switchbutton-light .jqx-fill-state-pressed-light {
    border-color: #0077BE;
    background: #0077BE;
}

.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-expand-light {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-collapse-light{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-collapse-rtl-light{
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-expand-rtl-light {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-title-selected-top-light, .jqx-tabs-selection-tracker-top-light {
    border-color: transparent;
    filter: none;
    background: #fff;
    color: #333;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-tabs-arrow-background-light{
    background: #fff;
    border:none;
    box-shadow:none;
}
.jqx-tabs-title-light, .jqx-ribbon-item-light {
    color: #333;
}
.jqx-tabs-title-selected-bottom-light,
.jqx-tabs-title-selected-top-light
 {
    padding-top:5px;
    padding-bottom:5px;
    color: #1997C6;
    font-weight:500;
}
.jqx-tabs-title.jqx-fill-state-hover-light {
    border-color: transparent;
}
.jqx-ribbon-item-light {
    cursor: pointer;
}
.jqx-ribbon-item-selected-light {
    color: #1997C6;
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-light {
    background: transparent;
    border-color: transparent;
}

.jqx-ribbon-header-top-light {
    border-color: transparent;
    border-bottom-color: #E0E0E0;
}

.jqx-ribbon-header-bottom-light {
    border-color: transparent;
    border-top-color: #E0E0E0;
}

.jqx-ribbon-header-right-light {
    border-color: transparent;
    border-left-color:#E0E0E0;
}

.jqx-ribbon-header-left-light {
    border-color: transparent;
    border-right-color:#E0E0E0;
}

.jqx-tabs-title-selected-bottom-light, .jqx-tabs-selection-tracker-bottom-light {
    border-color: transparent;
    border-top: 1px solid #fff;
    filter: none;
    background: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-light, .jqx-ribbon-light {
    border-color: transparent;
}

.jqx-tabs-header-light, .jqx-ribbon-header-light {
    background: transparent;
}
.jqx-tabs-position-bottom .jqx-tabs-header-light {
    border-color: transparent;
}
.jqx-layout-light .jqx-tabs-header-light, .jqx-layout-light .jqx-ribbon-header-light {
    background: #fff;
    border-color: #E0E0E0;
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-light, .jqx-tabs-title-hover-bottom-light, .jqx-tabs-header-light {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    background: transparent;
}

.jqx-tabs-content-light {
    box-sizing: border-box;
    border: 1px solid #E0E0E0;
    border-top-color: transparent;
    padding:5px;
}

.jqx-tabs-bar-light {
    position: absolute;
    bottom: 0;
    background: #1997C6;
    height: 2px;
    z-index:20;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-light.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-light {
    top: 0;
}

.jqx-window-content-light {
    box-sizing:border-box;
}

.jqx-layout-light
{
    background-color: #cccccc;
}
.jqx-kanban-column-header-collapsed-light {
   background: -moz-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ff3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(248,248,248,1)), color-stop(100%, rgba(234,234,234,1))); /* safari4+,chrome */
    background: -webkit-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* safari5.1+,chrome10+ */
    background: -o-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ie10+ */
    background: linear-gradient(90deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* w3c */
}

.jqx-calendar-light td {
    font-size: 12px;
}
.jqx-grid-column-menubutton-light {
    background-image: url('images/metro-icon-down.png');
 }
.jqx-grid-pager-top-light .jqx-grid-pager-number-light,
.jqx-grid-pager-light .jqx-grid-pager-number-light {

    background-color: transparent;
    border-color: transparent;
    color: rgba(0,0,0,.54) !important;
    font-size:12px;
}

.jqx-grid-pager-top-light .jqx-grid-pager-number-light:hover,
.jqx-grid-pager-light .jqx-grid-pager-number-light:hover {
    font-size:12px;
}

.jqx-grid-pager-top-light .jqx-grid-pager-number-light.jqx-fill-state-pressed-light ,
.jqx-grid-pager-light .jqx-grid-pager-number-light.jqx-fill-state-pressed-light {
    color: #0077BE !important;
    font-weight: bold !important;
}
.jqx-menu-item-arrow-up-selected-light, .jqx-icon-arrow-up-selected-light{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-light, .jqx-icon-arrow-down-selected-light{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-light, .jqx-icon-arrow-left-selected-light{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-light, .jqx-icon-arrow-right-selected-light{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-light{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-selected-light{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-hover-light{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-scrollbar-button-state-pressed-light .jqx-icon-arrow-up-selected-light{background-image:url('images/metro-icon-up.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-light .jqx-icon-arrow-down-selected-light{background-image:url('images/metro-icon-down.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-light .jqx-icon-arrow-left-selected-light{background-image:url('images/metro-icon-left.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-light .jqx-icon-arrow-right-selected-light{background-image:url('images/metro-icon-right.png');background-repeat:no-repeat;background-position:center;}

.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-expand-light {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-collapse-light{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-collapse-rtl-light {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-light.jqx-grid-cell-selected-light>.jqx-grid-group-expand-rtl-light{
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-light {
    background-image: url(./images/metro-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-light
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-light, .jqx-grid-group-expand-rtl-light
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-light
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-light
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-light
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-light
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-light
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-light
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-calendar-pressed-light {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-pressed-light {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-time-light{
    margin-left:1px;
}


.sorticon, .filtericon {
    box-shadow:none;
}
.sorticon.descending .jqx-grid-column-sorticon-light {
    margin-top:-1px;
}
.sorticon.ascending .jqx-grid-column-sorticon-light {
    margin-top:1px;
}

.jqx-dropdownlist-state-normal-light .jqx-icon-arrow-down-light,
.jqx-combobox-state-normal-light .jqx-icon-arrow-down-light,
.sorticon.descending .jqx-grid-column-sorticon-light,
.jqx-tree-item-arrow-expand-light,
 .jqx-expander-header-light .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-light .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
    background-image: url('images/metro-icon-down.png');
}

.jqx-tree-item-arrow-collapse-light
{
    transform: rotate(-90deg);
    background-image: url('images/metro-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-light .jqx-icon-arrow-down-light,
.jqx-combobox-state-selected-light .jqx-icon-arrow-down-light,
.sorticon.ascending .jqx-grid-column-sorticon-light
 {
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
}
.jqx-combobox-state-selected-light .jqx-icon-arrow-down-light{
    left:0px;
}

.jqx-primary .jqx-icon-arrow-down-light, .jqx-warning .jqx-icon-arrow-down-light, .jqx-danger .jqx-icon-arrow-down-light, .jqx-success .jqx-icon-arrow-down-light, .jqx-info .jqx-icon-arrow-down-light {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-selected-light, .jqx-warning .jqx-icon-arrow-down-selected-light, .jqx-danger .jqx-icon-arrow-down-selected-light, .jqx-success .jqx-icon-arrow-down-selected-light, .jqx-info .jqx-icon-arrow-down-selected-light {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-hover-light, .jqx-warning .jqx-icon-arrow-down-hover-light, .jqx-danger .jqx-icon-arrow-down-hover-light, .jqx-success .jqx-icon-arrow-down-hover-light, .jqx-info .jqx-icon-arrow-down-hover-light {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-up-light, .jqx-warning .jqx-icon-arrow-up-light, .jqx-danger .jqx-icon-arrow-up-light, .jqx-success .jqx-icon-arrow-up-light, .jqx-info .jqx-icon-arrow-up-light {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-selected-light, .jqx-warning .jqx-icon-arrow-up-selected-light, .jqx-danger .jqx-icon-arrow-up-selected-light, .jqx-success .jqx-icon-arrow-up-selected-light, .jqx-info .jqx-icon-arrow-up-selected-light {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-hover-light, .jqx-warning .jqx-icon-arrow-up-hover-light, .jqx-danger .jqx-icon-arrow-up-hover-light, .jqx-success .jqx-icon-arrow-up-hover-light, .jqx-info .jqx-icon-arrow-up-hover-light {
  background-image: url('images/metro-icon-up-white.png');
}


.jqx-primary .jqx-icon-arrow-left-light, .jqx-warning .jqx-icon-arrow-left-light, .jqx-danger .jqx-icon-arrow-left-light, .jqx-success .jqx-icon-arrow-left-light, .jqx-info .jqx-icon-arrow-left-light {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-left-selected-light, .jqx-warning .jqx-icon-arrow-left-selected-light, .jqx-danger .jqx-icon-arrow-left-selected-light, .jqx-success .jqx-icon-arrow-left-selected-light, .jqx-info .jqx-icon-arrow-left-selected-light {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-left-hover-light, .jqx-warning .jqx-icon-arrow-left-hover-light, .jqx-danger .jqx-icon-arrow-left-hover-light, .jqx-success .jqx-icon-arrow-left-hover-light, .jqx-info .jqx-icon-arrow-left-hover-light {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-right-light, .jqx-warning .jqx-icon-arrow-right-light, .jqx-danger .jqx-icon-arrow-right-light, .jqx-success .jqx-icon-arrow-right-light, .jqx-info .jqx-icon-arrow-right-light {
  background-image: url('images/metro-icon-right-white.png');
}
.jqx-primary .jqx-icon-arrow-right-selected-light, .jqx-warning .jqx-icon-arrow-right-selected-light, .jqx-danger .jqx-icon-arrow-right-selected-light, .jqx-success .jqx-icon-arrow-right-selected-light, .jqx-info .jqx-icon-arrow-right-selected-light {
  background-image: url('images/metro-icon-right-white.png');
}
.jqx-primary .jqx-icon-arrow-right-hover-light, .jqx-warning .jqx-icon-arrow-right-hover-light, .jqx-danger .jqx-icon-arrow-right-hover-light, .jqx-success .jqx-icon-arrow-right-hover-light, .jqx-info .jqx-icon-arrow-right-hover-light {
  background-image: url('images/metro-icon-right-white.png');
}

.jqx-slider-tooltip-light.jqx-primary-slider, .jqx-slider-tooltip-light.jqx-primary-slider .jqx-fill-state-normal-light {
    border-color: #1ca8dd;
    background: #1ca8dd;
}
.jqx-slider-tooltip-light.jqx-success-slider, .jqx-slider-tooltip-light.jqx-success-slider .jqx-fill-state-normal-light {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-light.jqx-inverse-slider, .jqx-slider-tooltip-light.jqx-inverse-slider .jqx-fill-state-normal-light {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-light.jqx-danger-slider, .jqx-slider-tooltip-light.jqx-danger-slider .jqx-fill-state-normal-light {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-light.jqx-warning-slider, .jqx-slider-tooltip-light.jqx-warning-slider .jqx-fill-state-normal-light {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-light.jqx-info-slider, .jqx-slider-tooltip-light.jqx-info-slider .jqx-fill-state-normal-light {
    border-color: #5bc0de;
    background: #5bc0de;
}

.jqx-fill-state-pressed-light .jqx-icon-delete-light
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-light .jqx-icon-edit-light
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-light .jqx-icon-save-light
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-light .jqx-icon-cancel-light
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-light .jqx-icon-search-light
{
    background-image: url(./images/search_white.png);
}
.jqx-fill-state-pressed-light .jqx-icon-plus-light
{
    background-image: url(./images/plus_white.png);
}
.jqx-fill-state-pressed-light .jqx-menu-minimized-button-light {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-fill-state-pressed-light .jqx-editor-toolbar-icon-light {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-editor-toolbar-button-light{
    border-color: #ddd;
}

/*applied to the timepicker*/
.jqx-needle-central-circle-light {
	fill: rgb(0, 119, 190);
}
.jqx-needle-light {
	fill: rgb(0, 119, 190);
}
.jqx-time-picker .jqx-header .jqx-selected-light:focus {
    outline: 2px solid rgba(0, 119, 190, 0.5);
	box-shadow: 0px 0px 4px 2px rgba(0, 119, 190, 0.125);
}
.jqx-svg-picker-light:focus {
	border: 1px solid rgb(0, 119, 190) !important;
}
.jqx-time-picker[view="portrait"] .jqx-header-light {
	border-top-left-radius: inherit;
	border-top-right-radius: inherit;
}
.jqx-time-picker[view="landscape"] .jqx-header-light {
	border-top-left-radius: inherit;
	border-bottom-left-radius: inherit;
}