.jqx-rc-tl-ui-sunny{border-top-left-radius:8px; moz-border-radius-topleft:8px; webkit-border-top-left-radius:8px}
.jqx-rc-tr-ui-sunny{border-top-right-radius:8px; moz-border-radius-topright:8px; webkit-border-top-right-radius:8px}
.jqx-rc-bl-ui-sunny{border-bottom-left-radius:8px; moz-border-radius-bottomleft:8px; webkit-border-bottom-left-radius:8px}
.jqx-rc-br-ui-sunny{border-bottom-right-radius:8px; moz-border-radius-bottomright:8px; webkit-border-bottom-right-radius:8px}
.jqx-rc-t-ui-sunny{border-top-left-radius:8px; border-top-right-radius:8px; moz-border-radius-topleft:8px; moz-border-radius-topright:8px; webkit-border-top-left-radius:8px; webkit-border-top-right-radius:8px}
.jqx-rc-b-ui-sunny{border-bottom-left-radius:8px; border-bottom-right-radius:8px; moz-border-radius-bottomleft:8px; moz-border-radius-bottomright:8px; webkit-border-bottom-left-radius:8px; webkit-border-bottom-right-radius:8px}
.jqx-rc-r-ui-sunny{border-bottom-right-radius:8px; border-top-right-radius:8px; moz-border-radius-bottomright:8px; moz-border-radius-topright:8px; webkit-border-bottom-right-radius:8px; webkit-border-top-right-radius:8px}
.jqx-rc-l-ui-sunny{border-bottom-left-radius:8px; border-top-left-radius:8px; moz-border-radius-bottomleft:8px; moz-border-radius-topleft:8px; webkit-border-bottom-left-radius:8px; webkit-border-top-left-radius:8px}
.jqx-rc-all-ui-sunny{border-radius:8px; moz-border-radius:8px; webkit-border-radius:8px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-sunny{background-position:-96px -192px; background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-sunny{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -192px; background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png)}
.jqx-grid-column-menubutton-ui-sunny{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -192px; background-image:url(./images/sunny/ui-icons_ffe180_256x240.png); border-width:0px}
/*Tree*/
.jqx-tree-item-arrow-expand-ui-sunny, .jqx-tree-item-arrow-expand-hover-ui-sunny{background-position:-65px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-sunny, .jqx-tree-item-arrow-collapse-hover-ui-sunny{background-position:-32px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-menu-item-arrow-right-ui-sunny, .jqx-menu-item-arrow-right-selected-ui-sunny{background-position:-32px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-menu-item-arrow-left-ui-sunny, .jqx-menu-item-arrow-left-selected-ui-sunny, .jqx-tree-item-arrow-collapse-rtl-ui-sunny, .jqx-tree-item-arrow-collapse-hover-rtl-ui-sunny{background-position:-96px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
/*Tabs*/
.jqx-tabs-title-ui-sunny{font-weight: bold; border-color: #d19405; border-bottom-color: transparent; background: #fece2f url(./images/sunny/ui-bg_gloss-wave_60_fece2f_500x100.png) 50% 50% repeat-x;color: #4c3000; }
.jqx-tabs-title-bottom-ui-sunny{border-bottom-color:#d19405; border-top-color: transparent;}
.jqx-tabs-header-ui-sunny{border-width: 1px; margin:2px; border-radius:8px; moz-border-radius:8px; webkit-border-radius:8px}
.jqx-tabs-header-bottom-ui-sunny{margin-top:-2px !important; padding-bottom: 3px; padding-top:1px}
/*Radio Button*/
.jqx-radiobutton-ui-sunny .jqx-fill-state-pressed-ui-sunny{background:#222; border:1px solid #000}
/*Calendar*/
.jqx-calendar-cell-ui-sunny{font-size: 11px; border-color: #d19405; background: #fece2f url(./images/sunny/ui-bg_gloss-wave_60_fece2f_500x100.png) 50% 50% repeat-x; color: #4c3000; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-sunny{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-sunny{border: 1px solid #aaa; border-bottom-width: 0px; border-radius:8px; moz-border-radius:8px; webkit-border-radius:8px; font-weight: bold;}
.jqx-calendar-month-container-ui-sunny{border:none !important}
.jqx-calendar-ui-sunny{padding:2px}
.jqx-calendar-column-cell-ui-sunny{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-sunny .jqx-icon-arrow-left-ui-sunny{background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-sunny .jqx-icon-arrow-right-ui-sunny{background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:5px; position:relative}

/*Icons*/
.jqx-icon-arrow-up-ui-sunny{background-position:0 -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-down-ui-sunny{background-position:-65px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-left-ui-sunny{background-position:-96px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-right-ui-sunny{background-position:-32px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-up-hover-ui-sunny{background-position:0 -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-down-hover-ui-sunny{background-position:-65px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-left-selected-ui-sunny{background-position:-96px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-right-selected-ui-sunny{background-position:-32px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-up-selected-ui-sunny{background-position:0 -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-down-selected-ui-sunny{background-position:-65px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-left-selected-ui-sunny{background-position:-96px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-arrow-right-selected-ui-sunny{background-position:-32px -17px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-icon-close-ui-sunny{background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-sunny{background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png); background-position:-80px -128px; cursor:pointer}
/*Window*/
.jqx-window-ui-sunny{padding: 2px;}
.jqx-window-header-ui-sunny{border: 1px solid #aaa; font-weight: bold; font-size: 11px; moz-border-radius:8px; border-radius:8px; webkit-border-radius:8px}
.jqx-window-content-ui-sunny{border-width:0px !important}
.jqx-window-close-button-ui-sunny{background-position:-96px -128px; background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png);moz-border-radius:8px; border-radius:8px; webkit-border-radius:8px}
.jqx-window-collapse-button-ui-sunny{background-position:0 -16px; background-image:url(./images/sunny/ui-icons_fadc7a_256x240.png)}
.jqx-window-collapse-button-hover-ui-sunny{background-image:url(./images/sunny/ui-icons_bd7b00_256x240.png); background-color:#fff; border-radius:8px; moz-border-radius:8px; webkit-border-radius:8px}
.jqx-window-collapse-button-collapsed-ui-sunny, .jqx-window-collapse-button-collapsed-hover-ui-sunny{background-position:-65px -16px}
.jqx-window-close-button-hover-ui-sunny{background-color:#fff; background-position:-96px -128px; background-image:url(./images/sunny/ui-icons_bd7b00_256x240.png); cursor:pointer; width:16px; height:16px}

/*Common Settings*/
.jqx-widget-ui-sunny{font-family: Segoe UI, Arial, sans-serif; font-size:12px; font-style:normal;}
.jqx-widget-content-ui-sunny{font-family: Segoe UI, Arial, sans-serif; background: #feeebd url(./images/sunny/ui-bg_highlight-soft_100_feeebd_1x100.png) 50% top repeat-x; color: #383838; font-size:12px}
.jqx-widget-content-ui-sunny a{color:#383838}
.jqx-widget-header-ui-sunny{font-family: Segoe UI, Arial, sans-serif; border-color: #494437; background: #817865 url(./images/sunny/ui-bg_gloss-wave_45_817865_500x100.png) 50% 50% repeat-x; color: #ffffff; font-size:12px}
.jqx-widget-header-ui-sunny a{color:#fff}
.jqx-fill-state-normal-ui-sunny{border-color: #d19405; background: #fece2f url(./images/sunny/ui-bg_gloss-wave_60_fece2f_500x100.png) 50% 50% repeat-x;color: #4c3000;}
.jqx-fill-state-normal-ui-sunny a, .jqx-fill-state-normal-ui-sunny a:link, .jqx-fill-state-normal-ui-sunny a:visited{color:#4c3000; text-decoration:none}
.jqx-fill-state-hover-ui-sunny{border-color: #a45b13; background: #ffdd57 url(./images/sunny/ui-bg_gloss-wave_70_ffdd57_500x100.png) 50% 50% repeat-x; color: #381f00; }
.jqx-fill-state-hover-ui-sunny a, .jqx-fill-state-hover-ui-sunny a:hover{color:#381f00; text-decoration:none}
.jqx-fill-state-focus-ui-sunny {border-color: #494437;}
.jqx-fill-state-pressed-ui-sunny{ border-color:#655e4e; background: #ffffff url(./images/sunny/ui-bg_inset-soft_30_ffffff_1x100.png) 50% 50% repeat-x; color: #0074c7;}
.jqx-fill-state-pressed-ui-sunny a, .jqx-fill-state-pressed-ui-sunny a:link, .jqx-fill-state-pressed-ui-sunny a:visited{color:#0074c7; text-decoration:none}
.jqx-fill-state-disabled-ui-sunny {cursor: default; color: #000; opacity: .55; filter:Alpha(Opacity=45);}

.jqx-input-button-content-ui-sunny{font-size:10px}
.jqx-input-icon-ui-sunny{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-sunny{margin-top:0px; background-position:-65px -147px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-sunny .jqx-fill-state-pressed-ui-sunny{background: #9f998a; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-sunny{background: #9f998a; border-right: none; border-bottom: 1px solid #8e846b;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-sunny, .jqx-scrollbar-thumb-state-normal-horizontal-ui-sunny{ border: 1px solid #8e846b; background: #9f998a;}
.jqx-scrollbar-thumb-state-hover-ui-sunny, .jqx-scrollbar-thumb-state-hover-horizontal-ui-sunny{ background: #dadada;}
.jqx-scrollbar-thumb-state-pressed-ui-sunny, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-sunny{ background: #ffffff;}

.jqx-tabs-title-selected-top-ui-sunny
{
    border-color: #aaa;
    border-bottom: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's title when the tab is selected and the jqxTab's position property is set to 'bottom' .*/
.jqx-tabs-title-selected-bottom-ui-sunny
{
    border-color: #aaa;
    border-top: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'top'.*/
.jqx-tabs-selection-tracker-top-ui-sunny
{
   border-color: #aaa;
   border-bottom: 1px solid #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'bottom'.*/
.jqx-tabs-selection-tracker-bottom-ui-sunny
{
   border-color: #aaa;
   border-top: 1px solid #fff;
}
/*Slider*/
.jqx-slider-ui-sunny .jqx-fill-state-pressed-ui-sunny{background:#9f998a;}
.jqx-slider-track-ui-sunny{border: 1px solid #8e846b; background: #fef8e4;}
/*Grid*/
.jqx-grid-cell-sort-ui-sunny, .jqx-grid-cell-filter-ui-sunny, .jqx-grid-cell-pinned-ui-sunny, .jqx-grid-cell-selected-ui-sunny{background:#fef8e4;}
.jqx-grid-bottomright-ui-sunny, .jqx-panel-bottomright-ui-sunny, .jqx-listbox-bottomright-ui-sunny, .jqx-scrollbar-state-normal-ui-sunny{background: #fef8e4;}
.jqx-grid-group-expand-ui-sunny{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down.png);}
.jqx-grid-group-collapse-ui-sunny{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right.png);}
/*Menu*/
.jqx-menu-dropdown-ui-sunny
{
    -moz-border-radius-bottomleft: 8px;
    -webkit-border-bottom-left-radius: 8px;
    border-bottom-left-radius: 8px;
    -moz-border-radius-topright: 8px;
    -webkit-border-top-right-radius: 8px;
    border-top-right-radius: 8px;
    -moz-border-radius-bottomright: 8px;
    -webkit-border-bottom-right-radius: 8px;
    border-bottom-right-radius: 8px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-sunny{overflow: inherit;}
.jqx-expander-header-ui-sunny{font-weight: bold; margin-bottom:2px; margin-top:2px}
.jqx-expander-header-ui-sunny{background: #fece2f url(./images/sunny/ui-bg_gloss-wave_60_fece2f_500x100.png) 50% 50% repeat-x;color: #4c3000; border:1px solid #d19405; border-radius:8px !important; moz-border-radius:8px !important; webkit-border-radius:8px !important}
.jqx-expander-header-hover-ui-sunny{border:1px solid #a45b13; background: #ffdd57 url(./images/sunny/ui-bg_gloss-wave_70_ffdd57_500x100.png) 50% 50% repeat-x; color: #381f00;}
.jqx-expander-header-expanded-ui-sunny{color: #0074c7; background:#fff; border:1px solid #aaa; border-bottom-width:0px; border-top-left-radius:8px !important; border-top-right-radius:8px !important; moz-border-radius-topleft:8px !important; moz-border-radius-topright:8px !important; webkit-border-top-left-radius:8px !important; webkit-border-top-right-radius:8px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-sunny{border-bottom-left-radius:8px !important; border-bottom-right-radius:8px !important; moz-border-radius-bottomleft:8px !important; moz-border-radius-bottomright:8px !important; webkit-border-bottom-left-radius:8px !important; webkit-border-bottom-right-radius:8px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-sunny{background-position:-65px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-expander-arrow-bottom-ui-sunny{background-position:0 -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-tabs-arrow-right-ui-sunny{background-position:-32px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
.jqx-tabs-arrow-left-ui-sunny{background-position:-96px -16px; background-image:url(./images/sunny/ui-icons_3d3d3d_256x240.png)}
/*Scroll Bar*/
.jqx-scrollbar-ui-sunny .jqx-icon-arrow-up-ui-sunny{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-sunny .jqx-icon-arrow-down-ui-sunny{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-sunny .jqx-icon-arrow-left-ui-sunny{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-sunny .jqx-icon-arrow-right-ui-sunny{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-layout-ui-sunny
{
    background-color: #494437;
    background-image: none;
}
.jqx-layout-pseudo-window-pin-icon-ui-sunny
{
    background-image: url("images/pin-yellow.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-sunny
{
    background-image: url("images/pinned-yellow.png");
}
.jqx-docking-layout-group-floating .jqx-window-header-ui-sunny
{
    background-image: none;
}
/*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-selected-ui-sunny:focus {
    outline: 1px solid rgb(254, 238, 189);
}
.jqx-svg-picker-ui-sunny:focus {
	border: 1px solid rgb(73, 68, 55) !important;
}
.jqx-main-container-ui-sunny {
	background: none;
}

.jqx-grid-pager-ui-sunny .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-sunny .jqx-icon-arrow-left-ui-sunny{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-sunny .jqx-icon-arrow-right-ui-sunny{
	position: relative;
    top: 6px;
}