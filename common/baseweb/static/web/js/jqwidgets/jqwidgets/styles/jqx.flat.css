﻿.jqx-rc-tl-flat
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-flat
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-flat
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-flat
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-flat
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-flat
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-flat
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-flat
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-flat
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-flat {
    font-size: 14px;
    font-family: 'segoe ui', arial, sans-serif;color: #000;
    border-color: #1A1A1A;
}
.jqx-widget-content-flat{color: #000; font-size: 14px; font-family: 'segoe ui', arial, sans-serif; border-color: #1A1A1A;  background-color: #F2F2F2;}
.jqx-widget-header-flat{color: #000;font-size: 14px; font-family: 'segoe ui', arial, sans-serif; border-color:#1A1A1A; background-color:#F2F2F2;}
.jqx-fill-state-normal-flat{color: #000;font-size: 14px; font-family: 'segoe ui', arial, sans-serif; border-color: #1A1A1A;  background: transparent;}
.jqx-button-flat {color: #000;background-color: #A6A6A6; border-color: #A6A6A6;}
.jqx-fill-state-hover-flat{border-color:#1A1A1A; color: #000; background-color:#A6A6A6;

}
.jqx-item.jqx-fill-state-hover-flat {
    border-color: transparent;
    color: #FFBB00;
    background-color: transparent;
}
.jqx-item.jqx-fill-state-pressed-flat {
    border-color: #FFBB00;
    color: #000;
    background-color: #FFBB00;
}
.jqx-grid .jqx-button-flat, .jqx-grid .jqx-input-flat {color: #000;background-color: transparent; border-color: #1A1A1A;}
.jqx-fill-state-focus-flat { border-color: #000; }
.jqx-fill-state-pressed-flat{border-color:#FFBB00; color: #000; background-color:#FFBB00;

}

.jqx-datetimeinput-flat 
{
    border-color: transparent !important;
    background: transparent !important;
}

.jqx-datetimeinput-flat .jqx-input-content-flat{
    border: 1px  solid #1A1A1A !important;
    height: calc(100% - 2px) !important;
    padding-top:0px !important;
    padding-bottom:0px !important;
    padding-left: 2px !important;
    padding-right: 0px !important;
    box-sizing: border-box;
}

.jqx-icon-calendar-flat {
    background-image: url('images/icon-calendar-metro-light.png');
    background-repeat: no-repeat;
    background-position: left top;
    position: relative;
    width: 16px !important;
    height: 21px !important;
    margin-top: -12px !important;
}

.jqx-icon-calendar-pressed-flat {
    background-image: url('images/icon-calendar-metro-light.png') !important;
    background-repeat: no-repeat;
    background-position: left top;
    position: relative;
    width: 16px !important;
    height: 21px !important;
    margin-top: -12px !important;
}

.jqx-datetimeinput-flat:hover .jqx-action-button-flat {
    border-color: transparent !important;
    background-color: transparent !important;
}

.jqx-datetimeinput-flat .jqx-action-button-flat.jqx-fill-state-normal-flat
{
    border-color: transparent !important;
}
.jqx-datetimeinput-flat .jqx-action-button-flat.jqx-fill-state-pressed-flat
{
    background-color: #FFBB00 !important;
}
.jqx-datetimeinput-flat .jqx-action-button-flat:active
{
    background-color: #FFBB00 !important;
}
.jqx-datetimeinput-flat .jqx-action-button-flat:hover.jqx-fill-state-hover-flat .jqx-icon-calendar-flat {
    background-image: url('images/icon-calendar-metro-yellow-light.png') !important;
    background-repeat: no-repeat;
    background-position: left top;
    position: relative;
    width: 16px !important;
    height: 21px !important;
    margin-top: -10px !important;
}
.jqx-datetimeinput-flat .jqx-action-button-flat:hover.jqx-fill-state-pressed-flat .jqx-icon-calendar-flat {
    background-image: url('images/icon-calendar-metro-light.png') !important;
    background-repeat: no-repeat;
    background-position: left top;
    position: relative;
    width: 16px !important;
    height: 21px !important;
    margin-top: -10px !important;
}
.jqx-datetimeinput-flat:hover .jqx-input-content-flat:hover {
    border-color: #FFBB00 !important;
}
.jqx-dropdownlist-state-normal-flat {
    background: #F2F2F2;
}
.jqx-dropdownlist-state-hover-flat {
    border-color: #FFBB00;
    background: #F2F2F2;
}
.jqx-dropdownlist-state-selected-flat {
    border-color: #FFBB00;
    background: #FFBB00;
}
.jqx-input-flat {
    border-color: #A6A6A6;
    background: #F2F2F2;
}
.jqx-scrollbar-state-normal-flat, .jqx-grid-bottomright-flat, .jqx-panel-bottomright-flat, .jqx-listbox-bottomright-flat{background-color:#f0f0f0;}
.jqx-widget-flat .jqx-grid-column-header-flat, .jqx-grid-cell-flat, .jqx-widget-flat .jqx-grid-cell-flat, .jqx-widget-flat .jqx-grid-group-cell-flat, .jqx-grid-group-cell-flat{font-size: 14px; font-family: 'segoe ui', arial, sans-serif; border-color:#f0f0f0;}
.jqx-tabs-title-selected-bottom-flat, .jqx-tabs-selection-tracker-bottom-flat, .jqx-tabs-title-selected-top-flat, .jqx-tabs-selection-tracker-top-flat, .jqx-ribbon-item-selected-flat{color: #000; border-color:#FFBB00; border-bottom:1px solid #FFBB00; background:#FFBB00}
.jqx-tabs-title-flat.jqx-fill-state-hover,  .jqx-ribbon-item-hover-flat{
    color: #FFBB00; 
    border-color: transparent !important;
    border-bottom-color: transparent !important;
    background: transparent;
}

.jqx-ribbon-selection-token-flat {
    border-color: transparent;
}
.jqx-tabs-header-flat{
    border-color: transparent;
}
.jqx-ribbon-item-top.jqx-ribbon-item-hover-flat
{
    border-bottom-color: transparent !important;
}
.jqx-ribbon-item-bottom.jqx-ribbon-item-hover-flat
{
    border-top-color: transparent !important;
}
.jqx-ribbon-item-left.jqx-ribbon-item-hover-flat
{
    border-right-color: transparent !important;
}
.jqx-ribbon-item-right.jqx-ribbon-item-hover-flat
{
    border-left-color: transparent !important;
}

.jqx-grid-cell-flat {
    border-color: transparent !important;
    background-color: #F4F4F4;
}
.jqx-grid-flat .jqx-widget-header-flat, .jqx-grid-column-menubutton-flat {
    border-color: transparent !important;
}
.jqx-grid-column-menubutton-flat {
    background-color: #FFBB00 !important; 
}
.jqx-grid-cell-flat.jqx-fill-state-hover-flat {
    color: #FFBB00 !important;
    background: #F4F4F4 !important;
}
.jqx-grid-cell-sort-alt-flat, .jqx-grid-cell-filter-alt-flat, .jqx-grid-cell-pinned-flat, .jqx-grid-cell-alt-flat, .jqx-grid-cell-sort-flat{ background-color:#F9F9F9; color: #000;}
.jqx-grid-cell-alt-flat.jqx-fill-state-hover-flat, .jqx-grid-cell-filter-alt-flat.jqx-fill-state-hover-flat, .jqx-grid-cell-pinned-flat.jqx-fill-state-hover-flat,.jqx-grid-cell-sort-flat.jqx-fill-state-hover-flat {
    color: #FFBB00 !important;
    background: #F9F9F9 !important;
}
.jqx-menu-vertical-flat{background: #F4F4F4; border-color: #A6A6A6;}
.jqx-widget-flat .jqx-grid-cell-flat, .jqx-widget-flat .jqx-grid-column-header-flat, .jqx-widget-flat .jqx-grid-group-cell-flat {color: #000; border-color: #1A1A1A;}
.jqx-widget-flat .jqx-grid-column-menubutton-flat, .jqx-widget-flat .jqx-grid-column-sortascbutton-flat, .jqx-widget-flat .jqx-grid-column-sortdescbutton-flat, .jqx-widget-flat .jqx-grid-column-filterbutton-flat {
    background-color: transparent;
    border-color: #A6A6A6;
}
.jqx-scrollbar-flat .jqx-scrollbar-thumb-state-normal {
    width: 5px ! important;
    margin-left: 5px;  
}
.jqx-scrollbar-mobile .jqx-scrollbar-thumb-state-normal-horizontal {
    height: 5px !important;
    margin-top: 5px;
}

.jqx-scrollbar-button-state-normal-flat
{
    display: none;
}
.jqx-window-header-flat, .jqx-input-button-header-flat, .jqx-calendar-title-header-flat, .jqx-grid-flat .jqx-widget-header-flat, .jqx-grid-header-flat, .jqx-grid-column-header-flat {font-size: 14px; font-family: 'segoe ui', arial, sans-serif; border-color: #A6A6A6; color: #000; background: #f4f4f4;}
.jqx-grid-column-menubutton-flat {
    background-image: url('images/metro-icon-down.png');
 }
.jqx-calendar-title-header-flat {
    background: #F2F2F2;
}
.jqx-calendar-cell-selected-flat {
    background: #FFBB00;
    color: #000;
    border-color: #FFBB00;
}
.jqx-calendar-flat .jqx-widget-content-flat {
    background: #F2F2F2;
}
.jqx-widget-flat .jqx-grid-cell-selected-flat, .jqx-grid-cell-selected-flat{ background-color:#FFBB00; border-color: #FFBB00; font-size: 14px;  color:#000;}
.jqx-grid-cell-hover-flat{ background-color:#dedede;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-flat {
    background-image: url('images/metro-icon-up.png');
 }
.jqx-grid-column-sortdescbutton-flat {
    background-image: url('images/metro-icon-down.png');
}
.jqx-checkbox-hover-flat {
    background-color: #fff;
}
.jqx-radiobutton-hover-flat {
    background-color: #fff;
}
.jqx-scrollbar-thumb-state-normal-horizontal-flat, .jqx-scrollbar-thumb-state-normal-flat {
    background: #cdcdcd; border-color: #cdcdcd;
}
.jqx-scrollbar-thumb-state-hover-horizontal-flat, .jqx-scrollbar-thumb-state-hover-flat {
    background: #a6a6a6; border-color: #a6a6a6;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-flat, .jqx-scrollbar-thumb-state-pressed-flat {
    background: #606060; border-color: #606060;
}
.jqx-scrollbar-button-state-normal-flat
{
    border: 1px solid #f0f0f0; 
    background: #f0f0f0;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-flat
{
    border: 1px solid #dadada;
    background: #dadada;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-flat
{
    border: 1px solid #606060;
    background: #606060;
}

/*icons*/
.jqx-window-collapse-button-flat
{
    background-image: url(./images/metro-icon-up.png);
}
.jqx-window-collapse-button-collapsed-flat {
  background-image: url(./images/metro-icon-down.png);
}
.jqx-icon-arrow-up-flat, .jqx-expander-arrow-bottom-flat, .jqx-menu-item-arrow-up-flat
{
    background-image: url('images/metro-icon-up.png');
}
.jqx-icon-arrow-down-flat, .jqx-expander-arrow-top-flat, .jqx-tree-item-arrow-expand-flat, .jqx-tree-item-arrow-expand-hover-flat, .jqx-menu-item-arrow-down-flat
{
    background-image: url('images/metro-icon-down.png');
}
.jqx-icon-arrow-left-flat, .jqx-menu-item-arrow-left-flat
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-icon-arrow-right-flat, .jqx-menu-item-arrow-right-flat, .jqx-tree-item-arrow-collapse-flat, .jqx-tree-item-arrow-collapse-hover-flat
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-tabs-arrow-left-flat, .jqx-tree-item-arrow-collapse-rtl-flat, .jqx-tree-item-arrow-collapse-hover-rtl-flat
{
    background-image: url('images/metro-icon-left.png');
}
.jqx-tabs-arrow-right-flat
{
    background-image: url('images/metro-icon-right.png');
}
.jqx-slider-slider-flat
{
    border-color:#cdcdcd;
}
.jqx-slider-button-flat
{
    -moz-border-radius: 9px;
    -webkit-border-radius: 9px;
    border-radius: 9px;
    border-color: #cdcdcd;
}
.jqx-input-button-content-flat
{  
    font-size: 10px;
}
.jqx-dropdownlist-state-normal-flat, .jqx-dropdownlist-state-hover-flat, .jqx-dropdownlist-state-selected-flat,
.jqx-scrollbar-button-state-hover-flat, .jqx-scrollbar-button-state-normal-flat, .jqx-scrollbar-button-state-pressed-flat,
.jqx-scrollbar-thumb-state-normal-horizontal-flat, .jqx-scrollbar-thumb-state-hover-horizontal-flat, .jqx-scrollbar-thumb-state-pressed-horizontal-flat,
.jqx-scrollbar-thumb-state-normal-flat, .jqx-scrollbar-thumb-state-pressed-flat, .jqx-button-flat, .jqx-tree-item-hover-flat, .jqx-tree-item-selected-flat,
.jqx-tree-item-flat, .jqx-menu-item-flat, .jqx-menu-item-hover-flat, .jqx-menu-item-selected-flat, .jqx-menu-item-top-flat, .jqx-menu-item-top-hover-flat, 
.jqx-menu-item-top-selected-flat, .jqx-slider-button-flat, .jqx-slider-slider-flat
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-flat {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #a6a6a6;
}
.jqx-switchbutton-thumb-flat {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-flat {
    background: #FFBB00;
    color: #FFBB00;
}
.jqx-switchbutton-label-off-flat {
    background: #a6a6a6;
    color: #a6a6a6;
}

.jqx-switchbutton-wrapper-flat {
}
.jqx-grid-cell-flat.jqx-grid-cell-selected-flat>.jqx-grid-group-expand-flat {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-flat.jqx-grid-cell-selected-flat>.jqx-grid-group-collapse-flat{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-flat.jqx-grid-cell-selected-flat>.jqx-grid-group-collapse-rtl-flat {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-flat.jqx-grid-cell-selected-flat>.jqx-grid-group-expand-rtl-flat{
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-flat {
    background-image: url(./images/metro-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-flat
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-flat, .jqx-grid-group-expand-rtl-flat
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-flat
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-flat
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-flat
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-flat
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-flat
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-flat
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-collapse-button-flat {
    margin-top: 1px;
}
.jqx-icon-calendar-pressed-flat {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-layout-flat
{
    background-color: #A6A6A6;
}
.jqx-slider-slider-flat {
    border-radius: 0px;
    background: #1A1A1A;
    border-color: #1A1A1A;
    width:14px;
    height: 14px;
}
.jqx-slider-left-flat, .jqx-slider-right-flat {
    background-color: transparent;
    border-color: transparent;
}
.jqx-checkbox-flat {
    overflow:hidden !important;
}
.jqx-checkbox-default-flat, .jqx-radiobutton-default-flat{
    border-color: #A6A6A6;
}
.jqx-checkbox-hover-flat, .jqx-radiobutton-hover-flat {
    border-color: #FFBB00;
}
.jqx-input-flat {
    background: #F2F2F2;
}
.jqx-input-flat:hover {
    border-color: #FFBB00;
}
::selection { background: #FFBB00; }

.jqx-expander-header-flat, .jqx-navigationbar-header-flat  {
    background: transparent !important;
    border-color: transparent !important;
 
}
.jqx-expander-arrow-top-flat{
    background-image: url('images/metro-icon-left.png');
}
.jqx-expander-arrow-bottom-flat {
    background-image: url('images/metro-icon-down.png');
}
.jqx-expander .jqx-widget-content-flat, .jqx-navigationbar .jqx-widget-content-flat {
    background-color: transparent !important;
    border-color: transparent !important;
    border-bottom-color: #1A1A1A !important;
}
.jqx-expander-header-flat:hover {
    color: #FFBB00;
}
.jqx-expander-header-flat {
    border-bottom-color: #1A1A1A !important;
}
.jqx-expander-header-expanded-flat {
    border-bottom-color: transparent!important;
}

