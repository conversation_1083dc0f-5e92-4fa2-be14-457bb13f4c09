﻿.jqx-widget-shinyblack{border-color:#222}
.jqx-widget-content-shinyblack{color:#222; border-color:#262626}
.jqx-fill-state-normal-shinyblack, .jqx-widget-header-shinyblack{color:#fff; border-color:#000; background:#000 url(./images/bg_black.png) left center scroll repeat-x}
.jqx-fill-state-hover-shinyblack{background:transparent url(./images/bg_blackhover.png) left center scroll repeat-x; border-color:#262626; color:#fff}
.jqx-fill-state-pressed-shinyblack, .jqx-menu-item-top-hover-shinyblack{background:transparent url(./images/bg_blackpressed.png) left center scroll repeat-x; border-color:#262626; color:#fff}

.jqx-checkbox-check-checked-shinyblack{background:transparent url(./images/check_white.png) left top no-repeat}
.jqx-checkbox-check-indeterminate-shinyblack{background:transparent url(./images/check_indeterminate_white.png) left top no-repeat}
.jqx-grid-shinyblack, .jqx-grid-header-shinyblack, .jqx-grid-cell-shinyblack{border-color:#262626}
.jqx-widget-shinyblack .jqx-grid-cell-shinyblack, .jqx-widget-shinyblack .jqx-grid-group-cell-shinyblack{border-color:#262626}
.jqx-widget-shinyblack .jqx-grid-column-menubutton-shinyblack, .jqx-widget-shinyblack .jqx-grid-column-sortascbutton-shinyblack, .jqx-widget-shinyblack .jqx-grid-column-sortdescbutton-shinyblack, .jqx-widget-shinyblack .jqx-grid-column-filterbutton-shinyblack{border-color:#262626}
.jqx-widget-shinyblack .jqx-grid-column-header-shinyblack{border-color:#262626}
.jqx-grid-bottomright-shinyblack, .jqx-panel-bottomright-shinyblack, .jqx-listbox-bottomright-shinyblack{background-color:#262626}
.jqx-widget-shinyblack .jqx-grid-column-menubutton-shinyblack, .jqx-menu-vertical-shinyblack{background-color:#262626; border-color:#262626}
 .jqx-grid-selectionarea-shinyblack{background-color:#262626; border:1px solid #262626; opacity:0.5}
.jqx-grid-group-cell-shinyblack{border-color:#262626; background-color:#fff}
.jqx-grid-cell-sort-shinyblack, .jqx-grid-cell-filter-shinyblack, .jqx-grid-cell-pinned-shinyblack{ background-color:#eaf8ff}
.jqx-grid-cell-alt-shinyblack, .jqx-grid-cell-sort-alt-shinyblack, .jqx-grid-cell-filter-alt-shinyblack{ background-color:#deedf5}
.jqx-grid-cell-selected-shinyblack{background:#262626; color:#fff}
.jqx-grid-cell-hover-shinyblack{background:#646464; color:#fff}
.jqx-menu-vertical-shinyblack{background: #222; color:#fff;}
.jqx-scrollbar-state-normal-shinyblack{background:#555; border:1px solid #555}
.jqx-scrollbar-button-state-normal-shinyblack{border:1px solid #555; background:#555}
.jqx-scrollbar-button-state-hover-shinyblack{background:#555 url(./images/bg_blackhover.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-button-state-pressed-shinyblack{background:#555 url(./images/bg_blackpressed.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-normal-horizontal-shinyblack{background:#555 url(./images/bg_black.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-hover-horizontal-shinyblack{ background:#555 url(./images/bg_blackhover.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-pressed-horizontal-shinyblack{background:#555 url(./images/bg_blackpressed.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-normal-shinyblack{background:#555 url(./images/bg_black_horizontal.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-hover-shinyblack{background:#555 url(./images/bg_blackhover_horizontal.png) left top scroll repeat-x; border:1px solid #000}
.jqx-scrollbar-thumb-state-pressed-shinyblack{background:#555 url(./images/bg_blackpressed_horizontal.png) left top scroll repeat-x; border:1px solid #000}
.jqx-splitter-splitbar-horizontal-shinyblack, .jqx-splitter-splitbar-vertical-shinyblack, .jqx-splitter-splitbar-hover-shinyblack, .jqx-splitter-splitbar-hover-horizontal-shinyblack{background: #555;}
.jqx-splitter-collapse-button-horizontal-shinyblack, .jqx-splitter-collapse-button-vertical-shinyblack{background: #7f7f7f;}
.jqx-grid-column-sortascbutton-shinyblack, .jqx-expander-arrow-bottom-shinyblack, .jqx-window-collapse-button-shinyblack, .jqx-menu-item-arrow-up-shinyblack, .jqx-menu-item-arrow-up-selected-shinyblack, .jqx-menu-item-arrow-top-up-shinyblack, .jqx-icon-arrow-up-shinyblack, .jqx-icon-arrow-up-hover-shinyblack, .jqx-icon-arrow-up-selected-shinyblack{background-image:url('images/icon-up-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-grid-column-menubutton-shinyblack, .jqx-grid-column-sortdescbutton-shinyblack, .jqx-expander-arrow-top-shinyblack, .jqx-window-collapse-button-collapsed-shinyblack, .jqx-menu-item-arrow-down-shinyblack, .jqx-menu-item-arrow-down-selected-shinyblack, .jqx-menu-item-arrow-down-shinyblack, .jqx-icon-arrow-down-shinyblack, .jqx-icon-arrow-down-hover-shinyblack, .jqx-icon-arrow-down-selected-shinyblack{background-image:url('images/icon-down-white.png'); background-repeat:no-repeat; background-position:center}

.jqx-icon-arrow-left-shinyblack{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-right-shinyblack{background-image:url('images/icon-right-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-menu-item-arrow-left-shinyblack{background-image:url('images/icon-left.png'); background-repeat:no-repeat; background-position:center}
.jqx-menu-item-arrow-right-shinyblack{background-image:url('images/icon-right.png'); background-repeat:no-repeat; background-position:center}

.jqx-tabs-arrow-left-shinyblack, .jqx-menu-item-arrow-left-selected-shinyblack, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-down-left-shinyblack, .jqx-icon-arrow-left-selected-shinyblack{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-tabs-arrow-right-shinyblack, .jqx-menu-item-arrow-right-selected-shinyblack, .jqx-menu-item-arrow-top-right-shinyblack, .jqx-icon-arrow-right-hover-shinyblack, .jqx-icon-arrow-right-selected-shinyblack{background-image:url('images/icon-right-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-window-close-button-shinyblack, .jqx-icon-close-shinyblack, .jqx-tabs-close-button-shinyblack, .jqx-tabs-close-button-hover-shinyblack, .jqx-tabs-close-button-selected-shinyblack{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}
/*applied to the progressbar's value element*/
.jqx-progressbar-value-shinyblack{background: #555;}
.jqx-progressbar-value-vertical-shinyblack{background: #555;}
.jqx-icon-arrow-first-shinyblack
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-shinyblack
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-group-expand-shinyblack, .jqx-grid-cell-shinyblack>.jqx-grid-group-expand-shinyblack, .jqx-tree-item-arrow-expand-shinyblack {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-shinyblack, .jqx-grid-cell-shinyblack>.jqx-grid-group-collapse-shinyblack, .jqx-tree-item-arrow-collapse-shinyblack {
    background-image: url('images/icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-rtl-shinyblack, .jqx-grid-cell-shinyblack>.jqx-grid-group-collapse-rtl-shinyblack, .jqx-tree-item-arrow-collapse-rtl-shinyblack {
    background-image: url('images/icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-expand-rtl-shinyblack, .jqx-grid-cell-shinyblack>.jqx-grid-group-expand-rtl-shinyblack {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-date-time-input-popup-shinyblack .jqx-icon-arrow-down-shinyblack {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-date-time-input-popup-shinyblack .jqx-icon-arrow-up-shinyblack {
    background-image: url('images/icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-shinyblack.jqx-grid-cell-selected-shinyblack>.jqx-grid-group-expand-shinyblack,
.jqx-grid-cell-shinyblack.jqx-grid-cell-hover-shinyblack>.jqx-grid-group-expand-shinyblack {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-shinyblack.jqx-grid-cell-selected-shinyblack>.jqx-grid-group-collapse-shinyblack,
.jqx-grid-cell-shinyblack.jqx-grid-cell-hover-shinyblack>.jqx-grid-group-collapse-shinyblack {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-shinyblack.jqx-grid-cell-selected-shinyblack>.jqx-grid-group-collapse-rtl-shinyblack,
.jqx-grid-cell-shinyblack.jqx-grid-cell-hover-shinyblack>.jqx-grid-group-collapse-rtl-shinyblack {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-shinyblack.jqx-grid-cell-selected-shinyblack>.jqx-grid-group-expand-rtl-shinyblack,
.jqx-grid-cell-shinyblack.jqx-grid-cell-hover-shinyblack>.jqx-grid-group-expand-rtl-shinyblack {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-shinyblack
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-shinyblack, .jqx-icon-calendar-hover-shinyblack, .jqx-icon-calendar-pressed-shinyblack {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-shinyblack, .jqx-icon-time-hover-shinyblack, .jqx-icon-time-pressed-shinyblack {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-delete-shinyblack
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-shinyblack
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-shinyblack
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-shinyblack
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-shinyblack
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-shinyblack
{
    background-image: url(./images/plus_white.png);
}
.jqx-menu-minimized-button-shinyblack {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-shinyblack {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-shinyblack
{
    background-color: #000;
}
.jqx-layout-pseudo-window-pin-icon-shinyblack
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-shinyblack
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-shinyblack, .jqx-scheduler-time-column-shinyblack, .jqx-scheduler-toolbar-shinyblack
{
    background: #35353A !important;
    color: #fff  !important;
}
.jqx-widget-shinyblack .jqx-scheduler-middle-cell-shinyblack, .jqx-scheduler-middle-cell-shinyblack {
    border-bottom-color: #35353A !important;
}
.jqx-kanban-item-shinyblack {
    box-shadow:none;
}

 .jqx-grid-column-menubutton-shinyblack {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
 }
.jqx-item-shinyblack .jqx-grid-sortasc-icon
 {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-shinyblack .jqx-grid-sortdesc-icon
 {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the grid menu's sort remove item/*/
.jqx-item-shinyblack .jqx-grid-sortremove-icon
 {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
 /*applied to the timepicker*/
.jqx-label-shinyblack {
	fill: white;
}
.jqx-needle-shinyblack {
	fill: rgb(58, 105, 130);
}
.jqx-needle-central-circle-shinyblack:first-of-type {
	fill: rgb(6, 64, 92);
	stroke: rgb(58, 105, 130);
	stroke-width: 1.5px;
}
.jqx-needle-central-circle-shinyblack {
	fill: rgb(6, 64, 92);
	stroke: rgb(58, 105, 130);
	stroke-width: 1.5px;
}
.jqx-svg-picker-shinyblack {
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 37%, rgba(70, 70, 70, 1) 53%, rgba(90, 90, 90, 1) 67%, rgba(80, 80, 80, 1) 73%, rgba(35, 40, 41, 1) 82%);	
}

