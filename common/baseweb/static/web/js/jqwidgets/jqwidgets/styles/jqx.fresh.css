﻿.jqx-widget-content-fresh{border-color: #8e8e97; background-color: #f7f7f7;}
.jqx-fill-state-normal-fresh, .jqx-widget-header-fresh{ border-color: #8e8e97; text-shadow:0 1px 0 #f1f1f5;
background: #ffffff;
background: -moz-linear-gradient(top, #ffffff 0%, #efeff1 1%, #ededf0 4%, #d2d2d8 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(1%,#efeff1), color-stop(4%,#ededf0), color-stop(100%,#d2d2d8));
background: -webkit-linear-gradient(top, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: -o-linear-gradient(top, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: -ms-linear-gradient(top, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: linear-gradient(top, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
}
.jqx-widget-fresh .jqx-grid-cell-fresh, .jqx-widget-fresh .jqx-grid-column-header-fresh, .jqx-widget-fresh .jqx-grid-group-cell-fresh {border-color: #8e8e97; background-color: #f7f7f7;}
.jqx-widget-fresh .jqx-grid-cell-sort-fresh, .jqx-widget-fresh .jqx-grid-cell-filter-fresh, .jqx-widget-fresh .jqx-grid-cell-alt-fresh, .jqx-widget-fresh .jqx-grid-cell-pinned-fresh{background-color:#d7d9df}
.jqx-widget-fresh, .jqx-widget-header-fresh, .jqx-widget-content-fresh{-webkit-background-clip: padding-box; background-clip: padding-box;}
.jqx-fill-state-hover-fresh, .jqx-widget-fresh .jqx-grid-cell-hover-fresh{ border-color:#5a5e64; color:#000; text-shadow:0 1px 0 #aaa;
background: #cacdd5;
background: -moz-linear-gradient(top, #cacdd5 0%, #a3a7b4 93%, #a6aab7 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#cacdd5), color-stop(93%,#a3a7b4), color-stop(100%,#a6aab7));
background: -webkit-linear-gradient(top, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: -o-linear-gradient(top, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: -ms-linear-gradient(top, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: linear-gradient(top, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
}
.jqx-fill-state-pressed-fresh, .jqx-widget-fresh .jqx-grid-cell-selected-fresh{
background: #058cf5;
background: -moz-linear-gradient(top, #058cf5 0%, #0385f3 19%, #0167e9 72%, #015de6 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#058cf5), color-stop(19%,#0385f3), color-stop(72%,#0167e9), color-stop(100%,#015de6));
background: -webkit-linear-gradient(top, #058cf5 0%,#0385f3 19%,#0167e9 72%,#015de6 100%);
background: -o-linear-gradient(top, #058cf5 0%,#0385f3 19%,#0167e9 72%,#015de6 100%);
background: -ms-linear-gradient(top, #058cf5 0%,#0385f3 19%,#0167e9 72%,#015de6 100%);
background: linear-gradient(top, #058cf5 0%,#0385f3 19%,#0167e9 72%,#015de6 100%); border-color:#014ab8; 
 color:white; text-shadow:0 1px 0 #555; border-image:initial;
 }
.jqx-fill-state-focus-fresh { border-color: #555; }
.jqx-fill-state-normal-fresh, .jqx-fill-state-pressed-fresh, .jqx-fill-state-hover-fresh
{
    -webkit-box-shadow: 0 2px 3px rgba(0,0,0,.15);
    -moz-box-shadow   : 0 2px 3px rgba(0,0,0,.15);
    -o-box-shadow     : 0 2px 3px rgba(0,0,0,.15);
    box-shadow        : 0 2px 3px rgba(0,0,0,.15);
}

.jqx-grid-column-menubutton-fresh{ background-color:transparent}
.jqx-calendar-row-header-fresh, .jqx-calendar-top-left-header-fresh{ background-color:#f2f2f2;  border:0px solid #f2f2f2}
.jqx-calendar-column-header-fresh{ background-color:#FFF;  border-top:1px solid #FFF;  border-bottom:1px solid #e9e9e9}
.jqx-scrollbar-state-normal-fresh{ background-color:#efefef;  border:1px solid #efefef}
.jqx-scrollbar-button-state-normal-fresh{ border:1px solid #efefef;  background-color:#ececed}
.jqx-scrollbar-button-state-hover-fresh{ border:1px solid #8e8e97;}
.jqx-scrollbar-button-state-pressed-fresh{ border:1px solid #8e8e97;}
.jqx-scrollbar-thumb-state-normal-fresh{background: #ffffff;                                       
background: -moz-linear-gradient(left, #ffffff 0%, #efeff1 1%, #ededf0 4%, #d2d2d8 100%);
background: -webkit-gradient(linear, left top, right top, color-stop(0%,#ffffff), color-stop(1%,#efeff1), color-stop(4%,#ededf0), color-stop(100%,#d2d2d8));
background: -webkit-linear-gradient(left, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: -o-linear-gradient(left, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: -ms-linear-gradient(left, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);
background: linear-gradient(left, #ffffff 0%,#efeff1 1%,#ededf0 4%,#d2d2d8 100%);  border:1px solid #8e8e97}
.jqx-scrollbar-thumb-state-hover-fresh{background: #cacdd5;
background: -moz-linear-gradient(left, #cacdd5 0%, #a3a7b4 93%, #a6aab7 100%);
background: -webkit-gradient(linear, left top, right top, color-stop(0%,#cacdd5), color-stop(93%,#a3a7b4), color-stop(100%,#a6aab7));
background: -webkit-linear-gradient(left, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: -o-linear-gradient(left, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: -ms-linear-gradient(left, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
background: linear-gradient(left, #cacdd5 0%,#a3a7b4 93%,#a6aab7 100%);
 border:1px solid #8e8e97}
.jqx-scrollbar-thumb-state-pressed-fresh, .jqx-progressbar-value-vertical-fresh{
background: #9cbbed;
background: -moz-linear-gradient(left, #9cbbed 0%, #3a78dc 1%, #1543bb 100%);
background: -webkit-gradient(linear, left top, right top, color-stop(0%,#9cbbed), color-stop(1%,#3a78dc), color-stop(100%,#1543bb));
background: -webkit-linear-gradient(left, #9cbbed 0%,#3a78dc 1%,#1543bb 100%);
background: -o-linear-gradient(left, #9cbbed 0%,#3a78dc 1%,#1543bb 100%);
background: -ms-linear-gradient(left, #9cbbed 0%,#3a78dc 1%,#1543bb 100%);
background: linear-gradient(left, #9cbbed 0%,#3a78dc 1%,#1543bb 100%);
 border:1px solid #014ab8}

.jqx-expander-arrow-expanded-fresh, .jqx-icon-arrow-up-selected-fresh{background-image:url('images/icon-up-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-down-selected-fresh{background-image:url('images/icon-down-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-left-selected-fresh{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-right-selected-fresh{background-image:url('images/icon-right-white.png');background-repeat:no-repeat; background-position:center}
.jqx-slider-track-horizontal-fresh, .jqx-slider-track-vertical-fresh{border-color: #e8e8e8; background: #e8e8e8;}
.jqx-grid-column-sortdescbutton-fresh, jqx-grid-column-filterbutton-fresh, .jqx-grid-column-sortascbutton-fresh{ background-color:transparent;  border-style:solid;  border-width:0px 0px 0px 0px;  border-color:#8e8e97}

.jqx-menu-item-arrow-right-selected-fresh{background-image:url(./images/icon-right-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-down-selected-fresh{background-image:url(./images/icon-down-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-up-selected-fresh{background-image:url(./images/icon-up-white.png);background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-left-selected-fresh{background-image:url(./images/icon-left-white.png); background-position:0 50%; background-repeat:no-repeat}

.jqx-grid-bottomright-fresh, .jqx-panel-bottomright-fresh, .jqx-listbox-bottomright-fresh{background-color: #efefef;}
.jqx-tabs-title-selected-top-fresh, .jqx-tabs-selection-tracker-top-fresh {border-color: #8e8e97; border-bottom: 1px solid #f7f7f7; text-shadow:0 1px 0 #f2f2f2; filter: none; color: #222; background: #f7f7f7;}
.jqx-tabs-title-selected-bottom-fresh, .jqx-tabs-selection-tracker-bottom-fresh {border-color: #8e8e97; border-top: 1px solid #f7f7f7; text-shadow:0 1px 0 #f2f2f2; filter: none; color: #222; background: #f7f7f7;}
.jqx-tabs-title-fresh, .jqx-window-fresh, .jqx-docking-fresh, .jqx-widget-fresh .jqx-window-fresh {-webkit-box-shadow: none;-moz-box-shadow:none;-o-box-shadow: none;box-shadow :none;}
.jqx-splitter-splitbar-horizontal-fresh, .jqx-splitter-splitbar-vertical-fresh, .jqx-splitter-splitbar-hover-fresh, .jqx-splitter-splitbar-hover-horizontal-fresh{background: #a5a9b6;}
.jqx-splitter-collapse-button-horizontal-fresh, .jqx-splitter-collapse-button-vertical-fresh{background: #1847bd;}
.jqx-slider-tick-horizontal-black, .jqx-slider-tick-vertical-black{background: #1847bd; border-color: #1847bd;}
.jqx-menu-vertical-fresh{ background:#d2d2d8; filter: none;}
.jqx-grid-cell-fresh.jqx-grid-cell-selected-fresh>.jqx-grid-group-expand-fresh {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-fresh.jqx-grid-cell-selected-fresh>.jqx-grid-group-collapse-fresh
{
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-fresh.jqx-grid-cell-selected-fresh>.jqx-grid-group-collapse-rtl-fresh
{
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-fresh.jqx-grid-cell-selected-fresh>.jqx-grid-group-expand-rtl-fresh
{
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-fresh
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-fresh
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-pressed-fresh {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-pressed-fresh {
    background-image: url('images/icon-time-white.png');
}
.jqx-layout-fresh
{
    background-color: #8e8e97;
}