﻿/*states, content and header*/
.jqx-widget-orange{border-color:#E48605}
.jqx-widget-content-orange{color:#000000; border-color:#E48605}
.jqx-fill-state-normal-orange, .jqx-widget-header-orange, .jqx-menu-vertical-orange {
    border-color: #E48605;
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #faa732;
    background-image: -moz-linear-gradient(top, #fbb450, #f89406);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fbb450), to(#f89406));
    background-image: -webkit-linear-gradient(top, #fbb450, #f89406);
    background-image: -o-linear-gradient(top, #fbb450, #f89406);
    background-image: linear-gradient(to bottom, #fbb450, #f89406);
    background-repeat: repeat-x;
}
.jqx-fill-state-hover-orange{
    border-color: #E48605;
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #E74615;
    background-image: -moz-linear-gradient(top, #f89406, #E48605);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f89406), to(#E48605));
    background-image: -webkit-linear-gradient(top, #f89406, #E48605);
    background-image: -o-linear-gradient(top, #f89406, #E48605);
    background-image: linear-gradient(to bottom, #f89406, #E48605);
    background-repeat: repeat-x;
}
.jqx-fill-state-pressed-orange, .jqx-menu-item-top-hover-orange{ background: #044062; /* Old browsers */
    border-color: #325da7;
    color: #ffffff;
    background: #4d77c1; /* Old browsers */
    background: -moz-linear-gradient(top, #4d77c1 0%, #325da7 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#4d77c1), color-stop(100%,#325da7)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #4d77c1 0%,#325da7 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #4d77c1 0%,#325da7 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #4d77c1 0%,#325da7 100%); /* IE10+ */
    background: linear-gradient(to bottom, #4d77c1 0%,#325da7 100%); /* W3C */
}
.jqx-fill-state-focus-orange {
  border-color: #325da7;
}
/*checkbox images, grid cells*/
.jqx-checkbox-check-checked-orange{background:transparent url(./images/check_white.png) left top no-repeat}
.jqx-checkbox-check-indeterminate-orange{background:transparent url(./images/check_indeterminate_white.png) left top no-repeat}
.jqx-grid-orange, .jqx-grid-header-orange, .jqx-grid-cell-orange{border-color:#E48605}
.jqx-widget-orange .jqx-grid-cell-orange, .jqx-widget-orange .jqx-grid-group-cell-orange{border-color:#FBB34E}
.jqx-widget-orange .jqx-grid-column-header-orange{border-color:#E48605}
.jqx-grid-bottomright-orange, .jqx-panel-bottomright-orange, .jqx-listbox-bottomright-orange{   background-color:#FBB659}
.jqx-widget-orange .jqx-grid-column-menubutton-orange, .jqx-menu-vertical-orange{border-color:#FBB34E}
 .jqx-grid-selectionarea-orange{background-color:#6eaad3;border:1px solid #6eaad3; opacity:0.5}
.jqx-grid-group-cell-orange{border-color:#FBB34E; background-color:#fff}
.jqx-grid-cell-orange, .jqx-widget-orange .jqx-grid-cell-orange {
border-color:#FBB34E; }
.jqx-grid-cell-sort-orange, .jqx-grid-cell-filter-orange, .jqx-grid-cell-pinned-orange{background-color:#FCC67C}
.jqx-grid-cell-alt-orange, .jqx-grid-cell-sort-alt-orange, .jqx-grid-cell-filter-alt-orange{background-color:#FCC67C}
.jqx-scrollbar-state-normal-orange{background-color:#FBB659; border:1px solid #FBB659;}
.jqx-tabs-title-selected-bottom-orange, .jqx-tabs-selection-tracker-bottom-orange, .jqx-tabs-title-selected-top-orange, .jqx-tabs-selection-tracker-top-orange{text-shadow: none; color: #000; border-color:#E48605; border-bottom:1px solid #ffffff; background:#ffffff}
.jqx-radiobutton-check-checked-orange {
    background: #fff;
    border-color: #fff;
}
/*icons*/
.jqx-icon-arrow-left-orange{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-right-orange{background-image:url('images/icon-right-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-menu-item-arrow-left-orange{background-image:url('images/icon-left.png'); background-repeat:no-repeat; background-position:center}
.jqx-menu-item-arrow-right-orange{background-image:url('images/icon-right.png'); background-repeat:no-repeat; background-position:center}

.jqx-grid-column-sortascbutton-orange, .jqx-expander-arrow-bottom-orange, .jqx-window-collapse-button-orange, .jqx-menu-item-arrow-up-orange, .jqx-menu-item-arrow-up-selected-orange, .jqx-menu-item-arrow-top-up-orange, .jqx-icon-arrow-up-orange, .jqx-icon-arrow-up-hover-orange, .jqx-icon-arrow-up-selected-orange{background-image:url('images/icon-up-white.png');background-repeat:no-repeat;background-position:center}
.jqx-grid-column-menubutton-orange, .jqx-grid-column-sortdescbutton-orange, .jqx-expander-arrow-top-orange, .jqx-window-collapse-button-collapsed-orange, .jqx-menu-item-arrow-down-orange, .jqx-menu-item-arrow-down-selected-orange, .jqx-menu-item-arrow-down-orange, .jqx-icon-arrow-down-orange, .jqx-icon-arrow-down-hover-orange, .jqx-icon-arrow-down-selected-orange{background-image:url('images/icon-down-white.png');background-repeat:no-repeat;background-position:center}
.jqx-tabs-arrow-left-orange, .jqx-menu-item-arrow-left-selected-orange, .jqx-menu-item-arrow-top-left-orange, .jqx-icon-arrow-down-left-orange, .jqx-icon-arrow-left-selected-orange{background-image:url('images/icon-left-white.png');background-repeat:no-repeat;background-position:center}
.jqx-tabs-arrow-right-orange, .jqx-menu-item-arrow-right-selected-orange, .jqx-menu-item-arrow-top-right-orange, .jqx-icon-arrow-right-hover-orange, .jqx-icon-arrow-right-selected-orange{background-image:url('images/icon-right-white.png');background-repeat:no-repeat;background-position:center}
.jqx-window-close-button-orange, .jqx-icon-close-orange, .jqx-tabs-close-button-orange, .jqx-tabs-close-button-hover-orange, .jqx-tabs-close-button-selected-orange{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}

.jqx-scrollbar-thumb-state-normal-horizontal-orange{ background:#F9960A; border:1px solid #E48605}
.jqx-scrollbar-thumb-state-hover-horizontal-orange{ background:#E48605; border:1px solid #E48605}
.jqx-scrollbar-thumb-state-pressed-horizontal-orange{ background:#111086; border:1px solid #070734}
.jqx-scrollbar-thumb-state-normal-orange{ background:#F9960A; border:1px solid #E48605;}
.jqx-scrollbar-thumb-state-hover-orange{ background:#E48605; border:1px solid #E48605}
.jqx-scrollbar-thumb-state-pressed-orange{ background:#111086; border:1px solid #070734}
.jqx-icon-arrow-first-orange
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-orange
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-orange>.jqx-grid-group-expand-orange, .jqx-tree-item-arrow-expand-orange {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange>.jqx-grid-group-collapse-orange, .jqx-tree-item-arrow-collapse-orange {
    background-image: url('images/icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange>.jqx-grid-group-collapse-rtl-orange, .jqx-tree-item-arrow-collapse-rtl-orange {
    background-image: url('images/icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange>.jqx-grid-group-expand-rtl-orange {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-orange.jqx-grid-cell-selected-orange>.jqx-grid-group-expand-orange,
.jqx-grid-cell-orange.jqx-grid-cell-hover-orange>.jqx-grid-group-expand-orange {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange.jqx-grid-cell-selected-orange>.jqx-grid-group-collapse-orange,
.jqx-grid-cell-orange.jqx-grid-cell-hover-orange>.jqx-grid-group-collapse-orange {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange.jqx-grid-cell-selected-orange>.jqx-grid-group-collapse-rtl-orange,
.jqx-grid-cell-orange.jqx-grid-cell-hover-orange>.jqx-grid-group-collapse-rtl-orange {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-orange.jqx-grid-cell-selected-orange>.jqx-grid-group-expand-rtl-orange,
.jqx-grid-cell-orange.jqx-grid-cell-hover-orange>.jqx-grid-group-expand-rtl-orange {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-orange
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-orange, .jqx-icon-calendar-hover-orange, .jqx-icon-calendar-pressed-orange {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-orange, .jqx-icon-time-hover-orange, .jqx-icon-time-pressed-orange {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-delete-orange
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-orange
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-orange
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-orange
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-orange
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-orange
{
    background-image: url(./images/plus_white.png);
}
.jqx-menu-minimized-button-orange {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-orange {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-orange
{
    background-color: #E48605;
}
.jqx-layout-pseudo-window-pin-icon-orange
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-orange
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-orange, .jqx-scheduler-time-column-orange, .jqx-scheduler-toolbar-orange
{
    background: #E48605 !important;
    color: #fff  !important;
}
.jqx-widget-orange .jqx-scheduler-middle-cell-orange, .jqx-scheduler-middle-cell-orange {
    border-bottom-color: #E48605 !important;
}
.jqx-date-time-input-popup-orange .jqx-icon-arrow-down-orange {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-date-time-input-popup-orange .jqx-icon-arrow-up-orange {
    background-image: url('images/icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}
 /*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-hour-container-orange:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-minute-container-orange:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-am-container-orange:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-pm-container-orange:focus {
    outline: 1px solid white !important;
}
.jqx-svg-picker-orange:focus {
	border: 1px solid rgb(77, 119, 193) !important;
}
.jqx-label-orange {
	fill: white;
}
.jqx-needle-orange {
	fill: rgb(77, 119, 193);
}
.jqx-needle-central-circle-orange:first-of-type {
	fill: rgb(77, 119, 193);
	stroke: rgb(77, 119, 193);
	stroke-width: 1.5;
}
.jqx-needle-central-circle-orange {
	fill: rgb(0, 74, 115);
	stroke: rgb(77, 119, 193);
	stroke-width: 1.5;
}

.jqx-split-layout-component-orange .jqx-split-layout {
     --jqx-primary-rgb: 77, 119, 193;
    --jqx-primary: #E74615;
    --jqx-background: #fff;
    --jqx-background-color: #333;
    --jqx-surface:#252526;
    --jqx-surface-color: #969690;
    --jqx-border: #414141;
    --jqx-background-hover-rgb: 60, 60, 60;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(255,255,255,.54);
    --jqx-primary-color: #fff;
    --jqx-background-hover-rgb: 225, 225, 225;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(0,0,0,.54);
    --jqx-surface-rgb: 255, 255, 255;
    --jqx-surface: rgb(var(--jqx-surface-rgb));
    --jqx-surface-color: #333;
    --jqx-border: rgba(98, 0, 238, 1);
	color: #333;
	background: var(--jqx-background);
}