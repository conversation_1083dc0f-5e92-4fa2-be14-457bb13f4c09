﻿.jqx-rc-tl-metrodark
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-metrodark
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-metrodark
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-metrodark
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-metrodark
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-metrodark
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-metrodark
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-metrodark
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-metrodark
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-metrodark {
    font-size: 12px; line-height: 17px;
    font-family: 'segoe ui', arial, sans-serif;
    color: inherit;
}
.jqx-widget-content-metrodark{font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; border-color: #35353A; color: #ffffff; background-color: #252526;}
.jqx-widget-header-metrodark{font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; color: #ffffff; border-color:#35353A; background-color:#3E3E42;}
.jqx-fill-state-normal-metrodark{font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-fill-state-hover-metrodark{font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; border-color:#1C97EA; color: #fff; background-color:#1C97EA;

}
.jqx-fill-state-focus-metrodark { border-color: #000; }
.jqx-fill-state-pressed-metrodark{border-color:#007ACC; color: #fff; background-color:#007ACC;

}
.jqx-fill-state-disabled-metrodark {
    color: #898989;
}
.jqx-input-metrodark {
    border-color: #35353A;
}
.jqx-scrollbar-state-normal-metrodark, .jqx-grid-bottomright-metrodark, .jqx-panel-bottomright-metrodark, .jqx-listbox-bottomright-metrodark{background-color:#3E3E42;}
.jqx-widget-metrodark .jqx-grid-column-header-metrodark, .jqx-grid-cell-metrodark, .jqx-widget-metrodark .jqx-grid-cell-metrodark, .jqx-widget-metrodark .jqx-grid-group-cell-metrodark, .jqx-grid-group-cell-metrodark{font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; border-color: #1C1C1E; background-color: #2A2A2C; color: #fff;}
.jqx-tabs-title-selected-bottom-metrodark, .jqx-tabs-selection-tracker-bottom-metrodark, .jqx-tabs-title-selected-top-metrodark, .jqx-tabs-selection-tracker-top-metrodark{color: #ffffff; border-color:#35353A; border-bottom:1px solid #252526; background:#007ACC}
.jqx-widget-metrodark .jqx-grid-cell-alt-metrodark, .jqx-widget-metrodark .jqx-grid-cell-sort-metrodark, .jqx-widget-metrodark .jqx-grid-cell-pinned-metrodark, .jqx-widget-metrodark .jqx-grid-cell-filter-metrodark, .jqx-grid-cell-sort-alt-metrodark, .jqx-grid-cell-filter-alt-metrodark, .jqx-grid-cell-pinned-metrodark, .jqx-grid-cell-alt-metrodark, .jqx-grid-cell-sort-metrodark{ background-color:#3E3E42; color: #fff;}
.jqx-menu-vertical-metrodark{background: #3E3E42; border-color: #3E3E42;}
.jqx-widget-metrodark .jqx-grid-cell-metrodark, .jqx-widget-metrodark .jqx-grid-column-header-metrodark, .jqx-widget-metrodark .jqx-grid-group-cell-metrodark { border-color: #35353A;}

.jqx-widget-metrodark .jqx-grid-column-menubutton-metrodark, .jqx-widget-metrodark .jqx-grid-column-sortascbutton-metrodark, .jqx-widget-metrodark .jqx-grid-column-sortdescbutton-metrodark, .jqx-widget-metrodark .jqx-grid-column-filterbutton-metrodark {
    background-color: transparent;
    border-color: #35353A;
}
.jqx-window-header-metrodark, .jqx-input-button-header-metrodark, .jqx-calendar-title-header-metrodark, .jqx-grid-metrodark .jqx-widget-header-metrodark, .jqx-grid-header-metrodark, .jqx-grid-column-header-metrodark {font-size: 12px; line-height: 17px; font-family: 'segoe ui', arial, sans-serif; border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-grid-column-menubutton-metrodark {
    background-image: url('images/metro-icon-down-white.png');
 }
.jqx-calendar-cell-today-metrodark {
    color: #35353A;
}

.jqx-widget-metrodark .jqx-grid-cell-selected-metrodark, .jqx-grid-cell-selected-metrodark{ background-color:#007ACC; border-color: #007ACC; font-size: 12px;  color:#fff;}
.jqx-widget-metrodark .jqx-grid-cell-hover-metrodark, .jqx-grid-cell-hover-metrodark{ background-color:#1C97EA; border-color: #1C97EA;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-metrodark {
    background-image: url('images/metro-icon-up-white.png');
 }
.jqx-grid-column-sortdescbutton-metrodark {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-checkbox-check-checked-metrodark{background:transparent url(./images/check_white.png) center center no-repeat}
.jqx-checkbox-check-indeterminate-metrodark{background:transparent url(./images/check_indeterminate_white.png) center center no-repeat}
.jqx-checkbox-hover-metrodark, .jqx-radiobutton-hover-metrodark {
    background-color: #3E3E42;
    border-color: #3E3E42;
}
.jqx-radiobutton-check-checked-metrodark {
    background: #fff;
    border-color: #fff;
}
 .jqx-grid-column-menubutton-metrodark {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
 }
.jqx-item-metrodark .jqx-grid-sortasc-icon
 {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-metrodark .jqx-grid-sortdesc-icon
 {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the grid menu's sort remove item/*/
.jqx-item-metrodark .jqx-grid-sortremove-icon
 {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }

.jqx-scrollbar-thumb-state-normal-horizontal-metrodark, .jqx-scrollbar-thumb-state-normal-metrodark {
    background: #686868; border-color: #686868;
}
.jqx-scrollbar-thumb-state-hover-horizontal-metrodark, .jqx-scrollbar-thumb-state-hover-metrodark {
    background: #9E9E9E; border-color: #9E9E9E;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-metrodark, .jqx-scrollbar-thumb-state-pressed-metrodark {
    background: #ffffff; border-color: #ffffff;
}
.jqx-scrollbar-button-state-normal-metrodark
{
    border: 1px solid #3E3E42; 
    background: #3E3E42;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-metrodark
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-metrodark
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}

/*icons*/
.jqx-window-collapse-button-metrodark
{
    background-image: url(./images/metro-icon-up-white.png);
}
.jqx-window-collapse-button-collapsed-metrodark {
  background-image: url(./images/metro-icon-down-white.png);
}
.jqx-icon-arrow-up-metrodark, .jqx-expander-arrow-bottom-metrodark, .jqx-menu-item-arrow-up-metrodark
{
    background-image: url('images/metro-icon-up-white.png');
}
.jqx-icon-arrow-down-metrodark, .jqx-expander-arrow-top-metrodark, .jqx-tree-item-arrow-expand-metrodark, .jqx-tree-item-arrow-expand-hover-metrodark, .jqx-menu-item-arrow-down-metrodark
{
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-icon-arrow-left-metrodark, .jqx-menu-item-arrow-left-metrodark
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-icon-arrow-right-metrodark, .jqx-menu-item-arrow-right-metrodark, .jqx-tree-item-arrow-collapse-metrodark, .jqx-tree-item-arrow-collapse-hover-metrodark
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-tabs-arrow-left-metrodark, .jqx-tree-item-arrow-collapse-rtl-metrodark, .jqx-tree-item-arrow-collapse-hover-rtl-metrodark
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-tabs-arrow-right-metrodark
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-menu-item-arrow-up-selected-metrodark, .jqx-icon-arrow-up-selected-metrodark{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-metrodark, .jqx-icon-arrow-down-selected-metrodark{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-metrodark, .jqx-icon-arrow-left-selected-metrodark{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-metrodark, .jqx-icon-arrow-right-selected-metrodark{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-window-close-button-metrodark, .jqx-icon-close-metrodark, .jqx-tabs-close-button-metrodark, .jqx-tabs-close-button-hover-metrodark, .jqx-tabs-close-button-selected-metrodark{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}
.jqx-listbox-feedback-metrodark {
    border-top: 1px dashed #fff;
}

.jqx-scrollbar-metrodark .jqx-icon-arrow-up-selected-metrodark{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metrodark .jqx-icon-arrow-down-selected-metrodark{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metrodark .jqx-icon-arrow-left-selected-metrodark{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-metrodark .jqx-icon-arrow-right-selected-metrodark{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}
.jqx-slider-button-metrodark
{
    border-radius: 100%;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
}
.jqx-input-button-content-metrodark
{  
    font-size: 10px;
}
.jqx-dropdownlist-state-normal-metrodark, .jqx-dropdownlist-state-hover-metrodark, .jqx-dropdownlist-state-selected-metrodark,
.jqx-scrollbar-button-state-hover-metrodark, .jqx-scrollbar-button-state-normal-metrodark, .jqx-scrollbar-button-state-pressed-metrodark,
.jqx-scrollbar-thumb-state-normal-horizontal-metrodark, .jqx-scrollbar-thumb-state-hover-horizontal-metrodark, .jqx-scrollbar-thumb-state-pressed-horizontal-metrodark,
.jqx-scrollbar-thumb-state-normal-metrodark, .jqx-scrollbar-thumb-state-pressed-metrodark, .jqx-button-metrodark, .jqx-tree-item-hover-metrodark, .jqx-tree-item-selected-metrodark,
.jqx-tree-item-metrodark, .jqx-menu-item-metrodark, .jqx-menu-item-hover-metrodark, .jqx-menu-item-selected-metrodark, .jqx-menu-item-top-metrodark, .jqx-menu-item-top-hover-metrodark, 
.jqx-menu-item-top-selected-metrodark, .jqx-slider-button-metrodark, .jqx-slider-slider-metrodark
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-metrodark {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #35353A;
}
.jqx-switchbutton-thumb-metrodark {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-metrodark {
    background: #007ACC;
    color: #007ACC;
}
.jqx-switchbutton-label-off-metrodark {
    background: #a6a6a6;
    color: #a6a6a6;
}

.jqx-switchbutton-wrapper-metrodark {
}
.jqx-grid-group-collapse-metrodark {
    background-image: url(./images/metro-icon-right-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-metrodark
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-metrodark, .jqx-grid-group-expand-rtl-metrodark
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-metrodark, .jqx-icon-arrow-first-hover-metrodark, .jqx-icon-arrow-first-selected-metrodark
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-metrodark, .jqx-icon-arrow-last-hover-metrodark, .jqx-icon-arrow-last-selected-metrodark
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metrodark.jqx-grid-cell-selected-metrodark>.jqx-grid-group-expand-metrodark,
.jqx-grid-cell-metrodark.jqx-grid-cell-hover-metrodark>.jqx-grid-group-expand-metrodark {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metrodark.jqx-grid-cell-selected-metrodark>.jqx-grid-group-collapse-metrodark,
.jqx-grid-cell-metrodark.jqx-grid-cell-hover-metrodark>.jqx-grid-group-collapse-metrodark {
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metrodark.jqx-grid-cell-selected-metrodark>.jqx-grid-group-collapse-rtl-metrodark,
.jqx-grid-cell-metrodark.jqx-grid-cell-hover-metrodark>.jqx-grid-group-collapse-rtl-metrodark {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-metrodark.jqx-grid-cell-selected-metrodark>.jqx-grid-group-expand-rtl-metrodark,
.jqx-grid-cell-metrodark.jqx-grid-cell-hover-metrodark>.jqx-grid-group-expand-rtl-metrodark {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-expand-button-metrodark {
    margin-top: 3px;
}
.jqx-icon-search-metrodark
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-passwordinput-password-icon-metrodark, .jqx-passwordinput-password-icon-rtl-metrodark
{
    background-image: url(./images/icon-showpassword-white.png) !important;
    background-repeat: no-repeat !important;
}
.jqx-icon-calendar-metrodark, .jqx-icon-calendar-hover-metrodark, .jqx-icon-calendar-pressed-metrodark {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-metrodark, .jqx-icon-time-hover-metrodark, .jqx-icon-time-pressed-metrodark {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-delete-metrodark
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-metrodark
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-metrodark
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-metrodark
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-metrodark
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-metrodark
{
    background-image: url(./images/plus_white.png);
}
.jqx-menu-minimized-button-metrodark {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-metrodark {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-metrodark
{
    background-color: #35353A;
}
.jqx-layout-pseudo-window-pin-icon-metrodark
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-metrodark
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-metrodark, .jqx-scheduler-time-column-metrodark, .jqx-scheduler-toolbar-metrodark
{
    background: #35353A !important;
    color: #fff  !important;
}
.jqx-widget-metrodark .jqx-scheduler-middle-cell-metrodark, .jqx-scheduler-middle-cell-metrodark {
    border-bottom-color: #35353A !important;
}
.jqx-kanban-item-metrodark {
    box-shadow:none;
}

.jqx-input-metrodark {
    color: #fff;
}

/*applied to the timepicker labels*/
.jqx-label-metrodark {
	fill: darkgray;
}

.jqx-needle-metrodark {
	fill: lightgray;
}

.jqx-needle-central-circle-metrodark {
	fill: lightgray;
}

.jqx-time-picker .jqx-label.jqx-selected-metrodark {
	fill: black !important;
}

.jqx-split-layout-component-metrodark .jqx-split-layout {
     --jqx-primary-rgb: 51, 173, 255;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-background: #252526;
    --jqx-background-color: #E7E7E7;
    --jqx-surface:#252526;
    --jqx-surface-color: #969690;
    --jqx-border: #414141;
    --jqx-background-hover-rgb: 60, 60, 60;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(255,255,255,.54);
    --jqx-primary-color: #fff;
    --jqx-background-hover-rgb: 225, 225, 225;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(0,0,0,.54);
    --jqx-surface-rgb: 33, 33, 33;
    --jqx-surface: rgb(var(--jqx-surface-rgb));
    --jqx-surface-color: #E7E7E7;
    --jqx-border: rgba(98, 0, 238, 1);
	color: #E7E7E7;
	background: var(--jqx-background);
}