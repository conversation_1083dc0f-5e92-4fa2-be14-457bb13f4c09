.jqx-rc-tl-ui-overcast{border-top-left-radius:6px; moz-border-radius-topleft:6px; webkit-border-top-left-radius:6px}
.jqx-rc-tr-ui-overcast{border-top-right-radius:6px; moz-border-radius-topright:6px; webkit-border-top-right-radius:6px}
.jqx-rc-bl-ui-overcast{border-bottom-left-radius:6px; moz-border-radius-bottomleft:6px; webkit-border-bottom-left-radius:6px}
.jqx-rc-br-ui-overcast{border-bottom-right-radius:6px; moz-border-radius-bottomright:6px; webkit-border-bottom-right-radius:6px}
.jqx-rc-t-ui-overcast{border-top-left-radius:6px; border-top-right-radius:6px; moz-border-radius-topleft:6px; moz-border-radius-topright:6px; webkit-border-top-left-radius:6px; webkit-border-top-right-radius:6px}
.jqx-rc-b-ui-overcast{border-bottom-left-radius:6px; border-bottom-right-radius:6px; moz-border-radius-bottomleft:6px; moz-border-radius-bottomright:6px; webkit-border-bottom-left-radius:6px; webkit-border-bottom-right-radius:6px}
.jqx-rc-r-ui-overcast{border-bottom-right-radius:6px; border-top-right-radius:6px; moz-border-radius-bottomright:6px; moz-border-radius-topright:6px; webkit-border-bottom-right-radius:6px; webkit-border-top-right-radius:6px}
.jqx-rc-l-ui-overcast{border-bottom-left-radius:6px; border-top-left-radius:6px; moz-border-radius-bottomleft:6px; moz-border-radius-topleft:6px; webkit-border-bottom-left-radius:6px; webkit-border-top-left-radius:6px}
.jqx-rc-all-ui-overcast{border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-overcast{background-position:-96px -192px; background-image:url(./images/overcast/ui-icons_454545_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-overcast{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-grid-column-menubutton-ui-overcast{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png); border-width:0px}
/*Tree*/
.jqx-tree-item-arrow-expand-ui-overcast, .jqx-tree-item-arrow-expand-hover-ui-overcast{background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_999999_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-overcast, .jqx-tree-item-arrow-collapse-hover-ui-overcast{background-position:-32px -16px; background-image:url(./images/overcast/ui-icons_999999_256x240.png)}
.jqx-menu-item-arrow-right-ui-overcast, .jqx-menu-item-arrow-right-selected-ui-overcast{background-position:-32px -16px; background-image:url(./images/overcast/ui-icons_999999_256x240.png)}
.jqx-menu-item-arrow-left-ui-overcast, .jqx-menu-item-arrow-left-selected-ui-overcast, .jqx-tree-item-arrow-collapse-rtl-ui-overcast, .jqx-tree-item-arrow-collapse-hover-rtl-ui-overcast{background-position:-96px -16px; background-image:url(./images/overcast/ui-icons_999999_256x240.png)}
/*Tabs*/
.jqx-tabs-title-ui-overcast{font-weight: bold; border-color: #ccc; background: #eeeeee ; color: #3383bb;}
.jqx-tabs-header-ui-overcast{border-width: 1px; margin:2px; border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-tabs-header-bottom-ui-overcast{margin-top:-2px !important; padding-bottom: 3px; padding-top:1px}
.jqx-tabs-title-bottom-ui-overcast{border-bottom-color:#ccc; border-top-color: transparent;}

/*Radio Button*/
.jqx-radiobutton-ui-overcast .jqx-fill-state-pressed-ui-overcast{background:#444644; border:1px solid #444644}
/*Calendar*/
.jqx-calendar-cell-ui-overcast{font-weight: bold; font-size: 11px; border-color: #ccc; background: #eeeeee url(./images/overcast/ui-bg_glass_60_eeeeee_1x400.png) 50% 50% repeat-x; color: #3383bb;  padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-overcast{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-overcast{border: 1px solid #bbbbbb; border-bottom-width: 0px; border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px; font-weight: bold;}
.jqx-calendar-month-container-ui-overcast{border:none !important}
.jqx-calendar-ui-overcast{padding:2px}
.jqx-calendar-column-cell-ui-overcast{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-overcast .jqx-icon-arrow-left-ui-overcast{background-image:url(./images/overcast/ui-icons_999999_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:6px; position:relative}
.jqx-calendar-ui-overcast .jqx-icon-arrow-right-ui-overcast{background-image:url(./images/overcast/ui-icons_999999_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:6px; position:relative}

/*Icons*/
.jqx-icon-arrow-up-ui-overcast{background-position:0 -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-down-ui-overcast{background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-left-ui-overcast{background-position:-96px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-right-ui-overcast{background-position:-32px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-up-hover-ui-overcast{background-position:0 -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-down-hover-ui-overcast{background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-left-hover-ui-overcast{background-position:-96px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-right-hover-ui-overcast{background-position:-32px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-left-selected-ui-overcast{background-position:-96px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-right-selected-ui-overcast{background-position:-32px -17px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-up-selected-ui-overcast{background-position:0 -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-arrow-down-selected-ui-overcast{background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-icon-close-ui-overcast{background-image:url(./images/overcast/ui-icons_454545_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-overcast{background-image:url(./images/overcast/ui-icons_454545_256x240.png); background-position:-80px -128px; cursor:pointer}
/*Window*/
.jqx-window-ui-overcast{padding: 2px;}
.jqx-window-header-ui-overcast{border: 1px solid #4297d7; font-weight: bold; font-size: 11px; moz-border-radius:6px; border-radius:6px; webkit-border-radius:6px}
.jqx-window-content-ui-overcast{border-width:0px !important}
.jqx-window-close-button-ui-overcast{background-position:-96px -128px; background-image:url(./images/overcast/ui-icons_454545_256x240.png);moz-border-radius:6px; border-radius:6px; webkit-border-radius:6px}
.jqx-window-close-button-hover-ui-overcast{background-color:#fff; background-position:-96px -128px; background-image:url(./images/overcast/ui-icons_70b2e1_256x240.png); cursor:pointer; width:16px; height:16px}
.jqx-window-collapse-button-ui-overcast{background-position:0 -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-window-collapse-button-hover-ui-overcast{background-image:url(./images/overcast/ui-icons_70b2e1_256x240.png); background-color:#fff; border-radius:6px; moz-border-radius:6px; webkit-border-radius:6px}
.jqx-window-collapse-button-collapsed-ui-overcast, .jqx-window-collapse-button-collapsed-hover-ui-overcast{background-position:-64px -16px}
.jqx-window-modal-ui-overcast{background: #aaaaaa; opacity: .30;filter:Alpha(Opacity=30);}
/*Common Settings*/
.jqx-widget-ui-overcast{line-height: 17px; font-family: Trebuchet MS, Helvetica, Arial, sans-serif; font-size:12px; font-style:normal; webkit-tap-highlight-color:rgba(0,0,0,0)}
.jqx-widget-content-ui-overcast{ font-family: Trebuchet MS, Helvetica, Arial, sans-serif; border-color: #aaaaaa; background: #c9c9c9; color: #333333; font-size:12px}
.jqx-widget-content-ui-overcast a{color:#333333}
.jqx-widget-header-ui-overcast{font-family: Trebuchet MS, Helvetica, Arial, sans-serif; border-color: #bbbbbb; background: #dddddd url(./images/overcast/ui-bg_glass_35_dddddd_1x400.png) 50% 50% repeat-x; color: #444444; font-size:12px}
.jqx-widget-header-ui-overcast a{color:#444444}
.jqx-fill-state-normal-ui-overcast{border-color: #cccccc; background: #eeeeee ; color: #3383bb;}
.jqx-fill-state-normal-ui-overcast a, .jqx-fill-state-normal-ui-overcast a:link, .jqx-fill-state-normal-ui-overcast a:visited{color:#3383bb; text-decoration:none}
.jqx-fill-state-hover-ui-overcast{border-color: #bbbbbb; background: #f8f8f8 url(./images/overcast/ui-bg_glass_100_f8f8f8_1x400.png) 50% 50% repeat-x; color: #599fcf;}
.jqx-fill-state-hover-ui-overcast a, .jqx-fill-state-hover-ui-overcast a:hover{color:#599fcf; text-decoration:none}
.jqx-fill-state-focus-ui-overcast {border-color: #555555;}
.jqx-fill-state-pressed-ui-overcast{border-color: #999999; background: #999999 url(./images/overcast/ui-bg_inset-hard_75_999999_1x100.png) 50% 50% repeat-x; color: #ffffff; }
.jqx-fill-state-pressed-ui-overcast a, .jqx-fill-state-pressed-ui-overcast a:link, .jqx-fill-state-pressed-ui-overcast a:visited{color:#ffffff; text-decoration:none}
.jqx-fill-state-disabled-ui-overcast {cursor: default; color: #000; opacity: .55; filter:Alpha(Opacity=45);}

.jqx-input-button-content-ui-overcast{font-size:10px}
.jqx-input-icon-ui-overcast{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-overcast{margin-top:0px; background-position:-64px -147px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-overcast .jqx-fill-state-pressed-ui-overcast{background: #dddddd; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-overcast{background: #dddddd; border-right: none; border-bottom: 1px solid #cccccc;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-overcast, .jqx-scrollbar-thumb-state-normal-horizontal-ui-overcast{ border: 1px solid #cccccc; background: #eeeeee;}
.jqx-scrollbar-thumb-state-hover-ui-overcast, .jqx-scrollbar-thumb-state-hover-horizontal-ui-overcast{ border: 1px solid #bbbbbb; background: #f8f8f8;}
.jqx-scrollbar-thumb-state-pressed-ui-overcast, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-overcast{ border: 1px solid #999999; background: #999999;}

.jqx-tabs-title-selected-top-ui-overcast
{
    border-color: #cccccc;
    border-bottom: 1px solid #c9c9c9;
    background-color: #fff;
}
/*applied to the tab's title when the tab is selected and the jqxTab's position property is set to 'bottom' .*/
.jqx-tabs-title-selected-bottom-ui-overcast
{
    border-color: #cccccc;
    border-top: 1px solid #c9c9c9;
    background-color: #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'top'.*/
.jqx-tabs-selection-tracker-top-ui-overcast
{
   border-color: #cccccc;
   border-bottom: 1px solid #c9c9c9;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'bottom'.*/
.jqx-tabs-selection-tracker-bottom-ui-overcast
{
   border-color: #cccccc;
   border-top: 1px solid #c9c9c9;
}
/*Slider*/
.jqx-slider-ui-overcast .jqx-fill-state-pressed-ui-overcast{background:#e1e1e1;}
.jqx-slider-track-ui-overcast{border: 1px solid #aaaaaa; background: #d9d9d9;}
/*Grid*/
.jqx-grid-cell-sort-ui-overcast, .jqx-grid-cell-filter-ui-overcast, .jqx-grid-cell-pinned-ui-overcast{background:#e1e1e1;}
.jqx-grid-bottomright-ui-overcast, .jqx-panel-bottomright-ui-overcast, .jqx-listbox-bottomright-ui-overcast, .jqx-scrollbar-state-normal-ui-overcast{background: #e1e1e1;}
.jqx-grid-cell-sort-alt-ui-overcast, .jqx-grid-cell-alt-ui-overcast, .jqx-grid-cell-filter-alt-ui-overcast{background: #e1e1e1;}
.jqx-widget-ui-overcast .jqx-grid-column-header-ui-overcast, .jqx-grid-cell-ui-overcast, .jqx-widget-ui-overcast .jqx-grid-cell-ui-overcast, .jqx-widget-ui-overcast .jqx-grid-group-cell-ui-overcast, .jqx-grid-group-cell-ui-overcast{border-color:#cccccc}
.jqx-grid-column-header-ui-overcast{font-weight: bold;}
.jqx-grid-cell-selected-ui-overcast{border-color: #cccccc; background: #999999 url(./images/overcast/ui-bg_inset-hard_75_999999_1x100.png) 50% 50% repeat-x; color: #ffffff;}
.jqx-grid-group-expand-ui-overcast{background-position: 50% 50%; background-repeat: no-repeat; background-image: url(./images/icon-down.png);}
.jqx-grid-group-collapse-ui-overcast{background-position: 50% 50%; background-repeat: no-repeat; background-image: url(./images/icon-right.png);}
/*Splitter*/
.jqx-splitter-collapse-button-vertical, .jqx-splitter-collapse-button-horizontal
{
    background: #aaaaaa;
}
/*Menu*/
.jqx-menu-item-top-ui-overcast
{
    font-weight: bold;
}
.jqx-menu-dropdown-ui-overcast
{
    -moz-border-radius-bottomleft: 6px;
    -webkit-border-bottom-left-radius: 6px;
    border-bottom-left-radius: 6px;
    -moz-border-radius-topright: 6px;
    -webkit-border-top-right-radius: 6px;
    border-top-right-radius: 6px;
    -moz-border-radius-bottomright: 6px;
    -webkit-border-bottom-right-radius: 6px;
    border-bottom-right-radius: 6px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-overcast{overflow: inherit;}
.jqx-expander-header-ui-overcast{font-weight: bold; margin-bottom:2px; margin-top:2px}
.jqx-expander-header-ui-overcast{background: #eeeeee url(./images/overcast/ui-bg_glass_60_eeeeee_1x400.png) 50% 50% repeat-x; color: #3383bb; border-color: #cccccc; border-radius:6px !important; moz-border-radius:6px !important; webkit-border-radius:6px !important}
.jqx-expander-header-hover-ui-overcast{background: #f8f8f8 url(./images/overcast/ui-bg_glass_100_f8f8f8_1x400.png) 50% 50% repeat-x; color: #599fcf; border:1px solid #bbbbbb;}
.jqx-expander-header-expanded-ui-overcast{background: #999999 url(./images/overcast/ui-bg_inset-hard_75_999999_1x100.png) 50% 50% repeat-x; color: #ffffff; border:1px solid #999999; border-bottom-width:0px; border-top-left-radius:6px !important; border-top-right-radius:6px !important; moz-border-radius-topleft:6px !important; moz-border-radius-topright:6px !important; webkit-border-top-left-radius:6px !important; webkit-border-top-right-radius:6px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-overcast{border-bottom-left-radius:6px !important; border-bottom-right-radius:6px !important; moz-border-radius-bottomleft:6px !important; moz-border-radius-bottomright:6px !important; webkit-border-bottom-left-radius:6px !important; webkit-border-bottom-right-radius:6px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-overcast{width: 16px; height: 16px; position: relative; background-position:-64px -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
.jqx-expander-arrow-bottom-ui-overcast{width: 16px; height: 16px; position: relative;  background-position:0 -16px; background-image:url(./images/overcast/ui-icons_454545_256x240.png)}
/*Scroll Bar*/
.jqx-scrollbar-ui-overcast .jqx-icon-arrow-up-ui-overcast{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-overcast .jqx-icon-arrow-down-ui-overcast{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-overcast .jqx-icon-arrow-left-ui-overcast{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-overcast .jqx-icon-arrow-right-ui-overcast{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-layout-ui-overcast
{
    background-color: #bbbbbb;
}
.jqx-layout-pseudo-window-pin-icon-ui-overcast
{
    background-image: url("images/pin-black.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-overcast
{
    background-image: url("images/pinned-black.png");
}
.jqx-docking-layout-group-floating .jqx-window-header-ui-overcast
{
    background-image: none;
}


.jqx-grid-pager-ui-overcast .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-overcast .jqx-icon-arrow-left-ui-overcast{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-overcast .jqx-icon-arrow-right-ui-overcast{
	position: relative;
    top: 6px;
}