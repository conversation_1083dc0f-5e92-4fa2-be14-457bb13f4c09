﻿.jqx-widget-content-highcontrast{color: #000; border-color: #000; background-color: #fff;}
.jqx-fill-state-normal-highcontrast {color: #000;background-color: #fff;}
.jqx-widget-header-highcontrast{color: #fff; border-color:#000; background-color:#000}
.jqx-fill-state-hover-highcontrast{border-color:#000; color: #fff; background-color:#000}
.jqx-fill-state-pressed-highcontrast{border-color:#37006e; color: #fff; background-color:#37006e}
.jqx-fill-state-focus-highcontrast { border-color: #37006e; }
.jqx-scrollbar-state-normal-highcontrast, .jqx-grid-bottomright-highcontrast, .jqx-panel-bottomright-highcontrast, .jqx-listbox-bottomright-highcontrast{background-color:#000}
.jqx-widget-highcontrast .jqx-grid-column-header-highcontrast, .jqx-grid-cell-highcontrast, .jqx-widget-highcontrast .jqx-grid-cell-highcontrast, .jqx-widget-highcontrast .jqx-grid-group-cell-highcontrast, .jqx-grid-group-cell-highcontrast{border-color:#000}
.jqx-tabs-title-selected-bottom-highcontrast, .jqx-tabs-selection-tracker-bottom-highcontrast, .jqx-tabs-title-selected-top-highcontrast, .jqx-tabs-selection-tracker-top-highcontrast{color: #000; border-color:#000; border-bottom:1px solid #fff; background:#fff}
.jqx-grid-cell-sort-alt-highcontrast, .jqx-grid-cell-filter-alt-highcontrast, .jqx-grid-cell-pinned-highcontrast, .jqx-grid-cell-alt-highcontrast, .jqx-grid-cell-sort-highcontrast{ background-color:#000; color: #fff;}
.jqx-grid-cell-selected-highcontrast{ background-color:#37006e;  color:#fff}
.jqx-grid-cell-hover-highcontrast{ background-color:#000; color: #fff;}
.jqx-menu-vertical-highcontrast{background: #000; color: #fff;}
.jqx-checkbox-hover-highcontrast {
    background-color: #fff;
}
.jqx-radiobutton-hover-highcontrast {
    background-color: #fff;
}
.jqx-scrollbar-thumb-state-hover-horizontal-highcontrast, .jqx-scrollbar-thumb-state-hover-highcontrast {
    background: #fff;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-highcontrast, .jqx-scrollbar-thumb-state-pressed-highcontrast {
    background: #fff;
}
.jqx-icon-arrow-up-hover-highcontrast, .jqx-icon-arrow-up-selected-highcontrast
{
    background-image: url('images/icon-up-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-down-hover-highcontrast, .jqx-icon-arrow-down-selected-highcontrast

{
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-left-hover-highcontrast, .jqx-icon-arrow-left-selected-highcontrast
{
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-right-hover-highcontrast, .jqx-icon-arrow-right-selected-highcontrast
{
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-highcontrast, .jqx-icon-arrow-last-selected-highcontrast
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-highcontrast, .jqx-icon-arrow-last-selected-highcontrast
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-hover-highcontrast, .jqx-icon-calendar-pressed-highcontrast {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-highcontrast, .jqx-icon-time-hover-highcontrast, .jqx-icon-time-pressed-highcontrast {
    background-image: url('images/icon-time-white.png');
}
.jqx-grid-cell-highcontrast.jqx-grid-cell-selected-highcontrast>.jqx-grid-group-expand-highcontrast,
.jqx-grid-cell-highcontrast.jqx-grid-cell-hover-highcontrast>.jqx-grid-group-expand-highcontrast {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-highcontrast.jqx-grid-cell-selected-highcontrast>.jqx-grid-group-collapse-highcontrast,
.jqx-grid-cell-highcontrast.jqx-grid-cell-hover-highcontrast>.jqx-grid-group-collapse-highcontrast {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-highcontrast.jqx-grid-cell-selected-highcontrast>.jqx-grid-group-collapse-rtl-highcontrast,
.jqx-grid-cell-highcontrast.jqx-grid-cell-hover-highcontrast>.jqx-grid-group-collapse-rtl-highcontrast {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-highcontrast.jqx-grid-cell-selected-highcontrast>.jqx-grid-group-expand-rtl-highcontrast,
.jqx-grid-cell-highcontrast.jqx-grid-cell-hover-highcontrast>.jqx-grid-group-expand-rtl-highcontrast {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-menu-item-arrow-right-hover-highcontrast, .jqx-menu-item-arrow-right-selected-highcontrast{background-image:url(./images/icon-right-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-down-hover-highcontrast, .jqx-menu-item-arrow-down-selected-highcontrast{background-image:url(./images/icon-down-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-up-hover-highcontrast, .jqx-menu-item-arrow-up-selected-highcontrast{background-image:url(./images/icon-up-white.png);background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-left-hover-highcontrast, .jqx-menu-item-arrow-left-selected-highcontrast{background-image:url(./images/icon-left-white.png); background-position:0 50%; background-repeat:no-repeat}

.jqx-icon-delete-highcontrast
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-highcontrast
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-highcontrast
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-highcontrast
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-highcontrast
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-highcontrast
{
    background-image: url(./images/plus_white.png);
}
.jqx-layout-highcontrast
{
    background-color: #000;
}
 /*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-selected-highcontrast:focus {
    outline: 1px solid white;
}
.jqx-svg-picker-highcontrast {
	border: 1px solid black;
}
.jqx-svg-picker-highcontrast:focus {
	border: 1px solid rgb(55, 0, 110) !important;
}