﻿/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-material-purple {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-material-purple {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-material-purple {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-material-purple {
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*top rounded Corners*/
.jqx-rc-t-material-purple {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom rounded Corners*/
.jqx-rc-b-material-purple {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*right rounded Corners*/
.jqx-rc-r-material-purple {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*left rounded Corners*/
.jqx-rc-l-material-purple {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*all rounded Corners*/
.jqx-rc-all-material-purple {
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

.jqx-widget-material-purple, .jqx-widget-header-material-purple, .jqx-fill-state-normal-material-purple,
.jqx-widget-content-material-purple, .jqx-fill-state-hover-material-purple, .jqx-fill-state-pressed-material-purple {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
}

.jqx-widget-material-purple {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #555;
}

.jqx-widget-content-material-purple {
      font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #555;
    background-color: #ffffff;
    border-color: #E0E0E0;
}

.jqx-widget-header-material-purple {
    background-color: #fff;
    border-color: #E0E0E0;
    color: #757575;
    font-weight: 500;
    *zoom: 1;
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background: #fff;
    color: #757575;
    font-size: 14px;
}

.jqx-fill-state-normal-material-purple {
    border-color: #6200EE;
    *zoom: 1;
    background: #fafafa;
}
.jqx-widget-material-purple input::selection, input.jqx-input-widget-material-purple::selection, .jqx-widget-content-material-purple input::selection {
    background: #6200EE;
    color: #fff;
}
.jqx-toolbar-material-purple{
     border-color: #e0e0e0;
}

.jqx-button-material-purple, jqx-button-material-purple.jqx-fill-state-normal-material-purple {
    color: #fff;
    background: #6200EE;
    border-color: #6200EE;
    *zoom: 1;
    outline: none;
    transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
}
.jqx-button-material-purple button, jqx-button-material-purple input {
    background: transparent;
    color: inherit;
    border:none;
    outline: none;
}

.jqx-slider-button-material-purple {
    padding:3px;
    background: transparent;
    border:transparent;
}
    .jqx-button-material-purple.float {
        border-radius: 100%;
        min-height: 48px;
        min-width: 48px;
        width: 48px;
        height: 48px;
        max-height: 48px;
        max-width:48px;
    }

    .jqx-button-material-purple.outlined {
        background: transparent;
        color: #6200EE;
        border-width: 2px;
    }

    .jqx-button-material-purple.flat {
        background: transparent;
        color: #6200EE;
        border: none;
    }

.jqx-fill-state-hover-material-purple {
}

.jqx-fill-state-pressed-material-purple {
}

.jqx-fill-state-hover-material-purple, .jqx-fill-state-focus-material-purple {
    text-decoration: none;
}
 .jqx-expander-header.jqx-fill-state-hover-material-purple,
 .jqx-expander-header.jqx-fill-state-normal-material-purple,
 .jqx-expander-header.jqx-fill-state-pressed-material-purple
 {
      background: #fff;
      border-color: #e0e0e0;
}
.jqx-expander-header.jqx-fill-state-hover-material-purple {
    background: #F5F5F5;
}

.jqx-expander-header-material-purple {
    padding:10px;
}
.jqx-button-material-purple.jqx-fill-state-hover {
    opacity: 0.9;
    cursor: pointer;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
    *zoom: 1;
}

    .jqx-button-material-purple.jqx-fill-state-hover.outlined,
    .jqx-button-material-purple.jqx-fill-state-hover.flat {
        color: #6200EE;
        box-shadow: none;
    }

.jqx-button-material-purple.jqx-fill-state-pressed {
    cursor: pointer;
    background: #883DF2;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

    .jqx-button-material-purple.jqx-fill-state-pressed.float {
        box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
    }

    .jqx-slider-button-material-purple.jqx-fill-state-pressed-material-purple,
    .jqx-button-material-purple.jqx-fill-state-pressed.outlined,
    .jqx-button-material-purple.jqx-fill-state-pressed.flat {
        background: rgba(99,0,238,0.15);
        box-shadow: none;
    }

.jqx-button-material-purple.jqx-fill-state-focus {
    background: #883DF2;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
  .jqx-slider-button-material-purple.jqx-fill-state-focus-material-purple {
      background: transparent;
      border-color: transparent;
      box-shadow:none;
  }
  
    .jqx-button-material-purple.jqx-fill-state-focus.outlined,
    .jqx-button-material-purple.jqx-fill-state-focus.flat {
        box-shadow: none;
        background: rgba(99,0,238,0.15);
        color: #883DF2;
    }

.jqx-dropdownlist-state-normal-material-purple, .jqx-dropdownlist-state-hover-material-purple, .jqx-dropdownlist-state-selected-material-purple,
.jqx-scrollbar-button-state-hover-material-purple, .jqx-scrollbar-button-state-normal-material-purple, .jqx-scrollbar-button-state-pressed-material-purple,
.jqx-scrollbar-thumb-state-normal-horizontal-material-purple, .jqx-scrollbar-thumb-state-hover-horizontal-material-purple, .jqx-scrollbar-thumb-state-pressed-horizontal-material-purple,
.jqx-scrollbar-thumb-state-normal-material-purple, .jqx-scrollbar-thumb-state-pressed-material-purple, .jqx-tree-item-hover-material-purple, .jqx-tree-item-selected-material-purple,
.jqx-tree-item-material-purple, .jqx-menu-item-material-purple, .jqx-menu-item-hover-material-purple, .jqx-menu-item-selected-material-purple, .jqx-menu-item-top-material-purple, .jqx-menu-item-top-hover-material-purple,
.jqx-menu-item-top-selected-material-purple, .jqx-slider-button-material-purple, .jqx-slider-slider-material-purple {
    -webkit-transition: background-color 100ms linear;
    -moz-transition: background-color 100ms linear;
    -o-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}


.jqx-primary-material-purple.jqx-input-label-material-purple {
   color: #1ca8dd !important;
}
.jqx-primary-material-purple.jqx-input-bar-material-purple:before {
   background: #1ca8dd !important;
}
.jqx-success-material-purple.jqx-input-label-material-purple {
   color: #5cb85c !important;
}
.jqx-success-material-purple.jqx-input-bar-material-purple:before {
   background: #5cb85c !important;
}
.jqx-inverse-material-purple.jqx-input-label-material-purple {
   color: #666 !important;
}
.jqx-inverse-material-purple.jqx-input-bar-material-purple:before {
   background: #666 !important;
}
.jqx-danger-material-purple.jqx-input-label-material-purple {
   color: #d9534f !important;
}
.jqx-danger-material-purple.jqx-input-bar-material-purple:before {
   background: #d9534f !important;
}
.jqx-warning-material-purple.jqx-input-label-material-purple {
   color: #f0ad4e !important;
}
.jqx-warning-material-purple.jqx-input-bar-material-purple:before {
   background: #f0ad4e !important;
}
.jqx-info-material-purple.jqx-input-label-material-purple {
   color: #5bc0de !important;
}
.jqx-info-material-purple.jqx-input-bar-material-purple:before {
   background: #5bc0de !important;
}

.jqx-slider-tooltip-material-purple.jqx-primary-slider, .jqx-slider-tooltip-material-purple.jqx-primary-slider .jqx-fill-state-normal-material-purple {
    border-color: #1ca8dd;
    background: #1ca8dd;
}
.jqx-slider-tooltip-material-purple.jqx-success-slider, .jqx-slider-tooltip-material-purple.jqx-success-slider .jqx-fill-state-normal-material-purple {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-material-purple.jqx-inverse-slider, .jqx-slider-tooltip-material-purple.jqx-inverse-slider .jqx-fill-state-normal-material-purple {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-material-purple.jqx-danger-slider, .jqx-slider-tooltip-material-purple.jqx-danger-slider .jqx-fill-state-normal-material-purple {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-material-purple.jqx-warning-slider, .jqx-slider-tooltip-material-purple.jqx-warning-slider .jqx-fill-state-normal-material-purple {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-material-purple.jqx-info-slider, .jqx-slider-tooltip-material-purple.jqx-info-slider .jqx-fill-state-normal-material-purple {
    border-color: #5bc0de;
    background: #5bc0de;
}


.jqx-primary-material-purple {
    color: #1ca8dd !important;
    background: #fff !important;
    border-color: #1ca8dd !important;
    text-shadow: none !important;
}

    .jqx-primary-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-primary-material-purple.jqx-slider-button-material-purple,
    .jqx-primary-material-purple.jqx-slider-slider-material-purple,
    .jqx-primary-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-primary-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-primary-material-purple.jqx-action-button-material-purple,
    .jqx-primary-material-purple:hover,
    .jqx-primary-material-purple:focus,
    .jqx-primary-material-purple:active,
    .jqx-primary-material-purple.active,
    .jqx-primary-material-purple.disabled,
    .jqx-primary-material-purple[disabled] {
        color: #fff !important;
        background: #1ca8dd !important;
        border-color: #1ca8dd !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-primary-material-purple,
    .jqx-primary-material-purple:active,
    .jqx-primary-material-purple.active {
        color: #fff !important;
        background-color: #1ca8dd !important;
        border-color: #1ca8dd !important;
        text-shadow: none !important;
    }

.jqx-success-material-purple {
    color: #5cb85c !important;
    background: #fff !important;
    border-color: #5cb85c !important;
    text-shadow: none !important;
}

    .jqx-success-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-success-material-purple.jqx-slider-button-material-purple,
    .jqx-success-material-purple.jqx-slider-slider-material-purple,
    .jqx-success-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-success-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-success-material-purple.jqx-action-button-material-purple,
    .jqx-success-material-purple:hover,
    .jqx-success-material-purple:focus,
    .jqx-success-material-purple:active,
    .jqx-success-material-purple.active,
    .jqx-success-material-purple.disabled,
    .jqx-success-material-purple[disabled] {
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-success-material-purple,
    .jqx-success-material-purple:active,
    .jqx-success-material-purple.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
    }

.jqx-inverse-material-purple {
    text-shadow: none !important;
    color: #666 !important;
    background: #fff !important;
    border-color: #cccccc !important;
}

    .jqx-inverse-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-inverse-material-purple.jqx-slider-button-material-purple,
    .jqx-inverse-material-purple.jqx-slider-slider-material-purple,
    .jqx-inverse-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-inverse-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-inverse-material-purple.jqx-action-button-material-purple,
    .jqx-inverse-material-purple:hover,
    .jqx-inverse-material-purple:focus,
    .jqx-inverse-material-purple:active,
    .jqx-inverse-material-purple.active,
    .jqx-inverse-material-purple.disabled,
    .jqx-inverse-material-purple[disabled] {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-inverse-material-purple,
    .jqx-inverse-material-purple:active,
    .jqx-inverse-material-purple.active {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }


.jqx-danger-material-purple {
    text-shadow: none !important;
    color: #d9534f !important;
    background: #fff !important;
    border-color: #d9534f !important;
}

    .jqx-danger-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-danger-material-purple.jqx-slider-button-material-purple,
    .jqx-danger-material-purple.jqx-slider-slider-material-purple,
    .jqx-danger-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-danger-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-danger-material-purple.jqx-action-button-material-purple,
    .jqx-danger-material-purple:hover,
    .jqx-danger-material-purple:focus,
    .jqx-danger-material-purple:active,
    .jqx-danger-material-purple.active,
    .jqx-danger-material-purple.disabled,
    .jqx-danger-material-purple[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-danger-material-purple,
    .jqx-danger-material-purple:active,
    .jqx-danger-material-purple.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

.jqx-validator-error-label-material-purple {
    color: #d9534f !important;
}

.jqx-warning-material-purple {
    text-shadow: none !important;
    color: #f0ad4e !important;
    background: #fff !important;
    border-color: #f0ad4e !important;
}

    .jqx-warning-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-warning-material-purple.jqx-slider-button-material-purple,
    .jqx-warning-material-purple.jqx-slider-slider-material-purple,
    .jqx-warning-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-warning-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-warning-material-purple.jqx-action-button-material-purple,
    .jqx-warning-material-purple:hover,
    .jqx-warning-material-purple:focus,
    .jqx-warning-material-purple:active,
    .jqx-warning-material-purple.active,
    .jqx-warning-material-purple.disabled,
    .jqx-warning-material-purple[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-warning-material-purple,
    .jqx-warning-material-purple:active,
    .jqx-warning-material-purple.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }


.jqx-info-material-purple {
    text-shadow: none !important;
    color: #5bc0de !important;
    background: #fff !important;
    border-color: #5bc0de !important;
}

    .jqx-info-material-purple.jqx-dropdownlist-state-normal-material-purple,
    .jqx-info-material-purple.jqx-slider-button-material-purple,
    .jqx-info-material-purple.jqx-slider-slider-material-purple,
    .jqx-info-material-purple.jqx-combobox-arrow-hover-material-purple,
    .jqx-info-material-purple.jqx-combobox-arrow-normal-material-purple,
    .jqx-info-material-purple.jqx-action-button-material-purple,
    .jqx-info-material-purple:hover,
    .jqx-info-material-purple:focus,
    .jqx-info-material-purple:active,
    .jqx-info-material-purple.active,
    .jqx-info-material-purple.disabled,
    .jqx-info-material-purple[disabled] {
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-purple.jqx-info-material-purple,
    .jqx-info-material-purple:active,
    .jqx-info-material-purple.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
    }

.jqx-fill-state-pressed-material-purple {
    background-image: none;
    outline: 0;
}

.jqx-grid-group-column-material-purple {
    border-color: transparent;
}
.jqx-grid-column-menubutton-material-purple {
    border-width: 0px;
}
.jqx-grid-groups-row-material-purple > span {
    padding-left: 4px;
}

.jqx-grid-cell-material-purple {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-pager-top-material-purple .jqx-button-material-purple,
.jqx-grid-pager-material-purple .jqx-button-material-purple {
    color: rgba(0,0,0,.54) !important;
    background-color: transparent;
    border-color: transparent;
    top: -4px;
    position: relative;
    height:30px !important;
    width:30px !important;
    border-radius:50%;

}

.jqx-grid-pager-input-material-purple  {
    padding:0px !important;
}

.jqx-grid-pager-top-material-purple .jqx-button-material-purple > div,
.jqx-grid-pager-material-purple .jqx-button-material-purple > div {
    top: 3px;
    position: relative;
    left: 3px;
}

.jqx-grid-pager-top-material-purple .jqx-button-material-purple.jqx-fill-state-hover,
.jqx-grid-pager-top-material-purple .jqx-button-material-purple.jqx-fill-state-pressed,
.jqx-grid-pager-material-purple .jqx-button-material-purple.jqx-fill-state-hover,
.jqx-grid-pager-material-purple .jqx-button-material-purple.jqx-fill-state-pressed
{
    color: rgba(0,0,0,.54) !important;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;    
}

.jqx-grid-pager-top-material-purple .jqx-grid-pager-number-material-purple,
.jqx-grid-pager-material-purple .jqx-grid-pager-number-material-purple {

    background-color: transparent;
    border-color: transparent;
    color: rgba(0,0,0,.54) !important;
    font-size:12px;
}

.jqx-grid-pager-top-material-purple .jqx-grid-pager-number-material-purple:hover,
.jqx-grid-pager-material-purple .jqx-grid-pager-number-material-purple:hover {
    font-size:12px;
}

.jqx-grid-pager-top-material-purple .jqx-grid-pager-number-material-purple.jqx-fill-state-pressed-material-purple ,
.jqx-grid-pager-material-purple .jqx-grid-pager-number-material-purple.jqx-fill-state-pressed-material-purple {
    color: #6200EE !important;
    font-weight: bold !important;
}

.jqx-grid-column-menubutton-material-purple {
    background-color: transparent;
    border-color: #E0E0E0 !important;
}

.jqx-cell-material-purple {
    font-size: 14px;
}

.jqx-calendar-material-purple > div {
    padding: 10px;
    box-sizing: border-box;
}
.jqx-calendar-row-header-material-purple, .jqx-calendar-top-left-header-material-purple {
    background-color: #f0f0f0;
    border: 0px solid #f2f2f2;
}

.jqx-calendar-column-header-material-purple {
    background-color: #FFF;
    border-top: 1px solid #FFF;
    border-bottom: 1px solid #e9e9e9;
}

.jqx-expander-header-material-purple {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jqx-ribbon-header-vertical-material-purple, .jqx-widget-header-vertical-material-purple {
    background: #fff;
}

.jqx-scrollbar-state-normal-material-purple {
    background-color: #f5f5f5;
    border: 1px solid #f5f5f5;
    border-left-color: #ddd;
}

.jqx-scrollbar-thumb-state-normal-material-purple, .jqx-scrollbar-thumb-state-normal-horizontal-material-purple {
    background: #f5f5f5;
    border-color: #b3b3b3;
}

.jqx-scrollbar-thumb-state-hover-material-purple, .jqx-scrollbar-thumb-state-hover-horizontal-material-purple {
    background: #e6e6e6;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-material-purple {
    background: #f7f7f7 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-material-purple, .jqx-splitter-collapse-button-horizontal-material-purple {
    background: #6200EE;
}

.jqx-splitter-collapse-button-vertical-material-purple, .jqx-progressbar-value-vertical-material-purple {
    background: #6200EE;
}


.jqx-scrollbar-thumb-state-pressed-material-purple, .jqx-splitter-splitbar-vertical-material-purple, .jqx-splitter-splitbar-horizontal-material-purple, .jqx-scrollbar-thumb-state-pressed-horizontal-material-purple,
.jqx-scrollbar-button-state-pressed-material-purple {
    background: #d9d9d9;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-grid-column-sortdescbutton-material-purple, jqx-grid-column-filterbutton-material-purple, .jqx-grid-column-sortascbutton-material-purple {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #E0E0E0;
}

.jqx-menu-vertical-material-purple {
    background: #ffffff;
    filter: none;
}

.jqx-grid-bottomright-material-purple, .jqx-panel-bottomright-material-purple, .jqx-listbox-bottomright-material-purple {
    background-color: #fafafa;
}

.jqx-window-material-purple, .jqx-tooltip-material-purple {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}
.jqx-tooltip-material-purple, .jqx-tooltip-material-purple.jqx-popup-material-purple, .jqx-tooltip-material-purple .jqx-fill-state-normal-material-purple {
    background: #6F6F6F;
    border-color: #6F6F6F;
    box-shadow:none;
    color: #fff;
}
.jqx-docking-material-purple .jqx-window-material-purple {
    box-shadow: none;
}

.jqx-docking-panel-material-purple .jqx-window-material-purple {
    box-shadow: none;
}

.jqx-checkbox-material-purple {
    line-height:20px;
    overflow: visible;
}
.jqx-radiobutton-material-purple {
    overflow: visible;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
    line-height:20px;
}

.jqx-radiobutton-material-purple-material-purple, .jqx-radiobutton-hover-material-purple {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
    transition: background-color ease-in .3s;
}

.jqx-radiobutton-check-checked-material-purple {
    filter: none;
    background: #fff;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-material-purple {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-material-purple {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-checkbox-default-material-purple,
.jqx-radiobutton-default-material-purple
 {
    border-width: 1px;
    border-color: #E0E0E0;
    background-color: #fff;
    overflow: visible;
}

.jqx-tree-material-purple .jqx-checkbox-material-purple .jqx-checkbox-default-material-purple,
.jqx-checkbox-material-purple[checked] .jqx-checkbox-default-material-purple,
.jqx-tree-grid-checkbox[checked].jqx-checkbox-default-material-purple,
.jqx-radiobutton-material-purple[checked] .jqx-radiobutton-default-material-purple
 {
    background-color: #6200EE;
    border-color: #6200EE;
}

.jqx-checkbox-check-checked-material-purple {
    background: transparent url(./images/material_check_white.png) center center no-repeat;
}
.jqx-checkbox-check-indeterminate-material-purple {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: white;
}
.jqx-tree-material-purple .jqx-checkbox-check-indeterminate-material-purple {
    width:12px !important;
    height:12px !important;
    top: 2px;
    left:2px;
}
.jqx-checkbox-hover-material-purple,
.jqx-radiobutton-hover-material-purple {
    background-color: #6200EE;
    border-color: #6200EE;
}


.jqx-slider-slider-material-purple {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-slider-material-purple:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}
.jqx-slider-material-purple[discrete] .jqx-slider-slider-material-purple:active
 {
    transform: scaleX(0);
    
}
.jqx-slider-slider-horizontal-material-purple {
    background: #6200EE; 
}
.jqx-slider-slider-vertical-material-purple {
    background: #6200EE; 
}
.jqx-slider-tooltip-material-purple {
    width: 25px;
    height: 25px;
    transform-origin: 50% 100%;
    border-radius: 50%;
    transform: scale(0) rotate(45deg);
    padding:0px;
    background: transparent !important;
}
.jqx-slider-tooltip-material-purple.init {
     transform: scale(1) rotate(45deg);
}
.jqx-slider-tooltip-material-purple.hide {
     transition: transform 0.2s ease;
     transform-origin:50% 100%;
     transform: scale(0) rotate(45deg); 
}
.jqx-slider-tooltip-material-purple.show {
     transition: transform 0.2s ease;
     transform: scale(1) rotate(45deg); 
}


.jqx-slider-tooltip-material-purple .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-material-purple .jqx-tooltip-arrow-l-r {
    display:none;
    visibility:hidden;
}

.jqx-slider-tooltip-material-purple, .jqx-slider-tooltip-material-purple .jqx-fill-state-normal-material-purple {
    border-radius: 15px 15px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #6200EE; 
    color: #fff;
    font-size:11px;
}
.jqx-slider-tooltip-material-purple.far, .jqx-slider-tooltip-material-purple.far .jqx-fill-state-normal-material-purple {
   border-radius: 0px 15px 15px 15px; 
}
.jqx-slider-tooltip-material-purple.vertical, .jqx-slider-tooltip-material-purple.vertical .jqx-fill-state-normal-material-purple {
   border-radius: 15px 0px 15px 15px; 
}
.jqx-slider-tooltip-material-purple.vertical.far, .jqx-slider-tooltip-material-purple.vertical.far .jqx-fill-state-normal-material-purple {
   border-radius: 15px 15px 15px 0px; 
}
.jqx-slider-tooltip-material-purple {
    background:transparent;
    border:none !important;
    box-shadow:none;
}
.jqx-slider-tooltip-material-purple .jqx-tooltip-main-material-purple {
    top: -7px;
    right: 11px;
}
.jqx-slider-tooltip-material-purple.far .jqx-tooltip-main-material-purple {
    top: 3px;
    right: 4px;
}
.jqx-slider-tooltip-material-purple.vertical .jqx-tooltip-main-material-purple {
    top: -3px;
    right: 3px;
}
.jqx-slider-tooltip-material-purple .jqx-tooltip-text {
    background: transparent;
    border:none;
    padding: 0px;
    overflow:visible;
}
.jqx-slider-tooltip-material-purple .jqx-tooltip-text>span {
     transform: rotate(-45deg);
}
.jqx-slider-tooltip-material-purple.range {
    width: 35px;
    height:35px;
}

.jqx-slider-rangebar-material-purple {
    border-color: #6200EE;
    background: #6200EE;
}

.jqx-slider-track-horizontal-material-purple, .jqx-slider-track-vertical-material-purple {
    border-color: #E0E0E0;
    background: #f0f0f0;
}

.jqx-slider-button-material-purple {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-button-material-purple.jqx-fill-state-normal-material-purple,
.jqx-slider-button-material-purple.jqx-fill-state-hover-material-purple,
.jqx-slider-button-material-purple.jqx-fill-state-pressed-material-purple 
{
    background: transparent !important;
}

.jqx-listitem-state-hover-material-purple,
.jqx-listitem-state-selected-material-purple,
.jqx-listitem-state-normal-material-purple {
    padding: 5px;
}

.jqx-scheduler-edit-dialog-label-material-purple {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;

}
.jqx-scheduler-edit-dialog-field-material-purple {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-label-rtl-material-purple {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-field-rtl-material-purple {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-material-purple, .jqx-menu-item-hover-material-purple, .jqx-tree-item-hover-material-purple, .jqx-calendar-cell-hover-material-purple, .jqx-grid-cell-hover-material-purple,
.jqx-menu-vertical-material-purple .jqx-menu-item-top-hover-material-purple, .jqx-input-popup-material-purple .jqx-fill-state-hover-material-purple,
.jqx-input-popup-material-purple .jqx-fill-state-pressed-material-purple {
    color: #333 !important;
    border-color: #F5F5F5;
    text-decoration: none;
    background-color: #F5F5F5;
    background-repeat: repeat-x;
    outline: 0;
    background: #F5F5F5; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}

.jqx-scheduler-cell-hover-material-purple {
    border-color: #F5F5F5 !important;
    background: #F5F5F5 !important;
}

.jqx-listitem-state-selected-material-purple, .jqx-menu-item-selected-material-purple, .jqx-tree-item-selected-material-purple, .jqx-calendar-cell-selected-material-purple, .jqx-grid-cell-selected-material-purple,
.jqx-menu-vertical-material-purple .jqx-menu-item-top-selected-material-purple, .jqx-grid-selectionarea-material-purple, .jqx-input-button-header-material-purple, .jqx-input-button-innerHeader-material-purple {
    color: #6200EE !important;
    border-color: #ECE0FD !important;
    background: #ECE0FD; /* Old browsers */
    box-shadow: none;
}

.jqx-scheduler-cell-selected-material-purple {
    border-color: #ECE0FD !important;
    background: #ECE0FD !important;
}

.jqx-grid-cell-material-purple .jqx-button-material-purple, .jqx-grid-cell-material-purple .jqx-button-material-purple.jqx-fill-state-hover-material-purple, .jqx-grid-cell-material-purple .jqx-button-material-purple.jqx-fill-state-pressed-material-purple {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.jqx-popup-material-purple {
    border: 1px solid #E0E0E0;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}

.jqx-grid-column-sortascbutton-material-purple, .jqx-expander-arrow-bottom-material-purple, .jqx-window-collapse-button-material-purple, .jqx-menu-item-arrow-up-material-purple, .jqx-menu-item-arrow-up-selected-material-purple, .jqx-menu-item-arrow-top-up-material-purple, .jqx-icon-arrow-up-material-purple, .jqx-icon-arrow-up-hover-material-purple, .jqx-icon-arrow-up-selected-material-purple {
    background-image: url('images/material-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-material-purple .jqx-grid-group-expand-material-purple, .jqx-grid-group-expand-material-purple, .jqx-grid-column-menubutton-material-purple, .jqx-grid-column-sortdescbutton-material-purple, .jqx-expander-arrow-top-material-purple, .jqx-window-collapse-button-collapsed-material-purple, .jqx-menu-item-arrow-down-material-purple, .jqx-menu-item-arrow-down-selected-material-purple, .jqx-menu-item-arrow-down-material-purple, .jqx-icon-arrow-down-material-purple, .jqx-icon-arrow-down-hover-material-purple, .jqx-icon-arrow-down-selected-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-material-purple, .jqx-menu-item-arrow-left-selected-material-purple, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-material-purple, .jqx-icon-arrow-down-left-material-purple, .jqx-icon-arrow-left-selected-material-purple {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-material-purple .jqx-grid-group-collapse-material-purple, .jqx-grid-group-collapse-material-purple, .jqx-tabs-arrow-right-material-purple, .jqx-menu-item-arrow-right-selected-material-purple, .jqx-menu-item-arrow-top-right-material-purple, .jqx-icon-arrow-right-material-purple, .jqx-icon-arrow-right-hover-material-purple, .jqx-icon-arrow-right-selected-material-purple {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tree-item-arrow-collapse-rtl-material-purple, .jqx-tree-item-arrow-collapse-hover-rtl-material-purple {
    background-image: url(./images/material-icon-left.png);
}

.jqx-menu-item-arrow-left-selected-material-purple {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-material-purple {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-material-purple {
    font-size: 10px;
}

.jqx-widget .jqx-grid-column-header-cell-material-purple {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-row-cell-material-purple {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-cell, .jqx-widget .jqx-grid-column-header, .jqx-widget .jqx-grid-group-cell {
    border-color: #E0E0E0;
}

.jqx-combobox-material-purple .jqx-icon-close-material-purple {
    background-image:url(./images/close_white.png)
}
.jqx-combobox-material-purple, .jqx-input-material-purple {
    border-color: #E0E0E0;
    color: #555555;
    background-color: #ffffff;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-material-purple,
.jqx-datetimeinput-content-material-purple
 {
    border-color: transparent;
}
.jqx-combobox-arrow-normal-material-purple {
    background: #fff;
    border-color: transparent;
}

.jqx-combobox-content-focus-material-purple, 
.jqx-combobox-state-focus-material-purple,
.jqx-numberinput-focus-material-purple {
    outline: none;
}

.jqx-input-group-material-purple {
    position: relative;
    display: inline-block;
    overflow: visible;
    border: none;
    box-shadow: none;
}

    .jqx-input-group-material-purple input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
    .jqx-input-group-material-purple textarea {
        width: 100%;
        height: 100%;
        outline: none;
        resize: none;
        border-left: none;
        border-right: none;
        border-top: none;
        border-bottom-color: #e0e0e0;
    }
.jqx-numberinput-material-purple,
.jqx-maskedinput-material-purple
 {
    position:relative;
}
.jqx-numberinput-material-purple input {
    height:100% !important;
}
.jqx-input-material-purple.jqx-validator-error-element {
    border-color: transparent !important;
    border-bottom: 1px solid #df2227 !important; 
}
.jqx-input-material-purple input,
.jqx-dropdownlist-state-normal-material-purple,
.jqx-combobox-state-normal-material-purple,
.jqx-datetimeinput-material-purple,
.jqx-numberinput-material-purple,
.jqx-maskedinput-material-purple
 {
    background: #fff;
    border-color: #fff;
    border-radius: 0;
    box-shadow: none;
    border-bottom: 1px solid #E0E0E0;
    outline: none;
}
.jqx-datetimeinput-material-purple .jqx-action-button-material-purple,
.jqx-datetimeinput-material-purple .jqx-action-button-rtl-material-purple 
 {
    background-color: transparent;
    border-color: transparent;
}
    .jqx-datetimeinput-material-purple, .jqx-datetimeinput-material-purple > div,
    .jqx-numberinput-material-purple, .jqx-numberinput-material-purple > div,
    .jqx-maskedinput-material-purple, .jqx-maskedinput-material-purple > div,
    .jqx-dropdownlist-state-normal-material-purple, .jqx-dropdownlist-state-normal-material-purple > div, .jqx-dropdownlist-state-normal-material-purple > div > div,
    .jqx-combobox-state-normal-material-purple, .jqx-combobox-state-normal-material-purple > div, .jqx-combobox-state-normal-material-purple > div > div {
        overflow: visible !important;
    }

    .jqx-input-material-purple input:focus {
        border-radius: 0;
        box-shadow: none;
    }

.jqx-input-material-purple input, input[type="text"].jqx-input-material-purple, input[type="password"].jqx-input-material-purple, input[type="text"].jqx-widget-content-material-purple, input[type="textarea"].jqx-widget-content-material-purple, textarea.jqx-input-material-purple {
    font-size: 14px;
    resize: none;
    background: #fff;
    border: none;
    border-radius: 0;
    box-sizing:border-box;
    box-shadow: none;
    border-bottom: 1px solid #E0E0E0;
}
.jqx-input-label {
    visibility:inherit;
}
.jqx-input-bar{
    visibility:inherit;
}
input:focus ~ .jqx-input-label-material-purple,
textarea:focus ~ .jqx-input-label-material-purple,
.jqx-input-widget-material-purple[hint=true] .jqx-input-label,
.jqx-text-area-material-purple[hint=true] .jqx-input-label,
.jqx-dropdownlist-state-selected-material-purple .jqx-input-label,
.jqx-dropdownlist-state-normal-material-purple[hint=true] .jqx-input-label,
.jqx-combobox-state-normal-material-purple[hint=true] .jqx-input-label,
.jqx-combobox-material-purple .jqx-input-label.focused,
.jqx-dropdownlist-material-purple .jqx-input-label.focused,
.jqx-datetimeinput-material-purple[hint=true] .jqx-input-label,
.jqx-maskedinput-material-purple[hint=true] .jqx-input-label,
.jqx-numberinput-material-purple[hint=true] .jqx-input-label,
.jqx-formattedinput-material-purple[hint=true] .jqx-input-label
 {
    top: -15px;
    font-size: 12px;
    color: #6200EE;
}
.jqx-dropdownlist-material-purple[default-placeholder="true"] .jqx-input-label {
    visibility: hidden;
}
input:focus ~ .jqx-input-bar:before,
textarea:focus ~ .jqx-input-bar:before,
.jqx-dropdownlist-state-selected-material-purple .jqx-input-bar:before,
.jqx-dropdownlist-material-purple .jqx-input-bar.focused:before,
.jqx-complex-input-group-material-purple .jqx-input-bar.focused::before,
.jqx-combobox-material-purple .jqx-input-bar.focused:before,
.jqx-dropdownbutton-material-purple .jqx-input-bar.focused:before,
.jqx-combobox-state-selected-material-purple .jqx-input-bar:before {
    width: 100%;
}
.jqx-complex-input-group-material-purple .jqx-fill-state-normal-material-purple {
    border-color: #fafafa;
}
input[type="password"] {
    letter-spacing: 0.3em;
}

.jqx-input-widget-material-purple.jqx-rtl > input {
    direction: rtl
}

.jqx-input-label-material-purple {
    color: #E0E0E0;
    font-size: 14px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 2px;
    top:10px;
    top: calc(50% - 7px);
    transition: 300ms ease all;
}
.jqx-input-label.initial {
    transition: none;
}
.jqx-input-bar {
    position: relative;
    display: block;
    z-index:1;
}

    .jqx-input-bar-material-purple:before {
        content: '';
        height: 2px;
        width: 0;
        bottom: 0px;
        position: absolute;
        background: #6200EE;
        transition: 300ms ease all;
        left: 0%;
    }
.jqx-formatted-input-spin-button-material-purple, .jqx-input-group-addon-material-purple {
    border-color: #fff;
    background: #fff;
}
.jqx-dropdownlist-state-selected-material-purple,
.jqx-combobox-state-selected-material-purple {
    color: #6200EE;
}


.jqx-dropdownlist-state-normal-material-purple .jqx-icon-arrow-down-material-purple,
.jqx-combobox-state-normal-material-purple .jqx-icon-arrow-down-material-purple,
.sorticon.descending .jqx-grid-column-sorticon-material-purple,
.jqx-tree-item-arrow-expand-material-purple,
 .jqx-expander-header-material-purple .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-material-purple .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
    background-image: url('images/material-icon-down.png');
}

.jqx-tree-item-arrow-collapse-material-purple
{
    transform: rotate(-90deg);
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-material-purple .jqx-icon-arrow-down-material-purple,
.jqx-combobox-state-selected-material-purple .jqx-icon-arrow-down-material-purple,
.sorticon.ascending .jqx-grid-column-sorticon-material-purple
 {
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
    left: -1px;
}
.jqx-combobox-state-selected-material-purple .jqx-icon-arrow-down-material-purple{
    left:-1px;
}
.jqx-listbox-container {
    margin-top: 1px;
}

input[type="text"].jqx-input-material-purple:-moz-placeholder, input[type="text"].jqx-widget-content-material-purple:-moz-placeholder, input[type="textarea"].jqx-widget-content-material-purple:-moz-placeholder, textarea.jqx-input-material-purple:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-material-purple:-webkit-input-placeholder, input[type="text"].jqx-widget-content-material-purple:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-material-purple:-webkit-input-placeholder, textarea.jqx-input-material-purple:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-material-purple:-ms-input-placeholder, input[type="text"].jqx-widget-content-material-purple:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-material-purple:-ms-input-placeholder, textarea.jqx-input-material-purple:-ms-input-placeholder {
    color: #999999;
}

.jqx-combobox-content-focus-material-purple, .jqx-combobox-state-focus-material-purple, .jqx-fill-state-focus-material-purple,
.jqx-numberinput-focus-material-purple {
    outline: none;
}

.jqx-popup-material-purple.jqx-fill-state-focus-material-purple {
    outline: none;
    border-color: #E0E0E0 !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-text-area-material-purple, .jqx-text-area-material-purple > div {
    overflow:visible !important;
}
.jqx-text-area-element-material-purple {
   box-sizing: border-box;
}
.jqx-pivotgrid-content-wrapper.jqx-fill-state-normal-material-purple {
    border-color: #E0E0E0;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-expand-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-collapse-material-purple {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-collapse-rtl-material-purple {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-expand-rtl-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-title-selected-top-material-purple, .jqx-tabs-selection-tracker-top-material-purple {
    border-color: transparent;
    filter: none;
    background: #fff;
    color: #333;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-tabs-title-material-purple, .jqx-ribbon-item-material-purple {
    color: #333;
}
.jqx-tabs-title-selected-bottom-material-purple,
.jqx-tabs-title-selected-top-material-purple
 {
    color: #6200EE;
    font-weight:500;   
    padding-top:5px;
    padding-bottom:5px;
}
.jqx-tabs-title.jqx-fill-state-hover-material-purple {
    border-color: transparent;
}
.jqx-ribbon-item-material-purple {
    cursor: pointer;
}
.jqx-ribbon-item-selected-material-purple {
    color: #6200EE;
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-material-purple {
    background: transparent;
}

.jqx-ribbon-header-top-material-purple {
    border-color: transparent;
    border-bottom-color: #E0E0E0;
}

.jqx-ribbon-header-bottom-material-purple {
    border-color: transparent;
    border-top-color: #E0E0E0;
}

.jqx-ribbon-header-right-material-purple {
    border-color: transparent;
    border-left-color:#E0E0E0;
}

.jqx-ribbon-header-left-material-purple {
    border-color: transparent;
    border-right-color:#E0E0E0;
}

.jqx-tabs-title-selected-bottom-material-purple, .jqx-tabs-selection-tracker-bottom-material-purple {
    border-color: transparent;
    border-top: 1px solid #fff;
    filter: none;
    background: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-material-purple, .jqx-ribbon-material-purple {
    border-color: transparent;
}


.jqx-tabs-header-material-purple, .jqx-ribbon-header-material-purple {
    background: transparent;
}
.jqx-tabs-position-bottom .jqx-tabs-header-material-purple {
    border-color: transparent;
}
.jqx-layout-material-purple .jqx-tabs-header-material-purple, .jqx-layout-material-purple .jqx-ribbon-header-material-purple {
    background: #fff;
    border-color: #E0E0E0;
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-material-purple, .jqx-tabs-title-hover-bottom-material-purple, .jqx-tabs-header-material-purple {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    background: transparent;
}

.jqx-tabs-content-material-purple {
    box-sizing: border-box;
    border: 1px solid #E0E0E0;
    border-top-color: transparent;
    padding:5px;
}
.jqx-tabs-bar-material-purple {
    position: absolute;
    bottom: 0;
    background: #6200EE;
    height: 2px;
    z-index:10;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-material-purple.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-material-purple {
    top: 0;
}


.jqx-layout-material-purple {
    background-color: #cccccc;
}

.jqx-kanban-column-header-collapsed-material-purple {
    background: -moz-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ff3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(248,248,248,1)), color-stop(100%, rgba(234,234,234,1))); /* safari4+,chrome */
    background: -webkit-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* safari5.1+,chrome10+ */
    background: -o-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ie10+ */
    background: linear-gradient(90deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* w3c */
}

.jqx-calendar-material-purple > div {
    padding: 10px;
    box-sizing: border-box;
}

.jqx-calendar-cell-material-purple {
    border-radius: 50%;
    font-size:12px;
}
.jqx-calendar-cell-year-material-purple,
.jqx-calendar-cell-decade-material-purple {
    border-radius: 25%;
}

.jqx-calendar-title-content-material-purple {
    font-weight:bold;
}
.jqx-calendar-column-cell-material-purple {
    color: rgba(0,0,0,.38);
    font-size:12px;
}
.jqx-grid-column-menubutton-material-purple {
    background-image: url('images/material-icon-down.png');
}

.jqx-tabs-close-button-material-purple {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-close-button-selected-material-purple {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-close-button-hover-material-purple {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-purple .jqx-icon-arrow-up-selected-material-purple {
    background-image: url('images/material-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-purple .jqx-icon-arrow-down-selected-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-purple .jqx-icon-arrow-left-selected-material-purple {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-purple .jqx-icon-arrow-right-selected-material-purple {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-expand-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-collapse-material-purple {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-collapse-rtl-material-purple {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-purple.jqx-grid-cell-selected-material-purple > .jqx-grid-group-expand-rtl-material-purple {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-group-collapse-material-purple {
    background-image: url(./images/material-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.jqx-grid-group-collapse-rtl-material-purple {
    padding-right: 0px;
    background-image: url(./images/material-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.jqx-grid-group-expand-material-purple, .jqx-grid-group-expand-rtl-material-purple {
    padding-right: 0px;
    background-image: url(./images/material-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-cell-filter-row-material-purple {
    background-color: #fafafa;
}

.jqx-icon-arrow-first-material-purple {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-material-purple {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-first-hover-material-purple {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-hover-material-purple {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-first-selected-material-purple {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-selected-material-purple {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-fill-state-pressed-material-purple .jqx-icon-arrow-first-selected-material-purple {
    background-image: url('images/material-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-fill-state-pressed-material-purple .jqx-icon-arrow-last-selected-material-purple {
    background-image: url('images/material-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-fill-state-pressed-material-purple .jqx-icon-arrow-left-selected-material-purple {
    background-image: url('images/material-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-fill-state-pressed-material-purple .jqx-icon-arrow-right-selected-material-purple {
    background-image: url('images/material-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-primary .jqx-icon-arrow-down-material-purple, .jqx-warning .jqx-icon-arrow-down-material-purple, .jqx-danger .jqx-icon-arrow-down-material-purple, .jqx-success .jqx-icon-arrow-down-material-purple, .jqx-info .jqx-icon-arrow-down-material-purple {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-down-selected-material-purple, .jqx-warning .jqx-icon-arrow-down-selected-material-purple, .jqx-danger .jqx-icon-arrow-down-selected-material-purple, .jqx-success .jqx-icon-arrow-down-selected-material-purple, .jqx-info .jqx-icon-arrow-down-selected-material-purple {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-down-hover-material-purple, .jqx-warning .jqx-icon-arrow-down-hover-material-purple, .jqx-danger .jqx-icon-arrow-down-hover-material-purple, .jqx-success .jqx-icon-arrow-down-hover-material-purple, .jqx-info .jqx-icon-arrow-down-hover-material-purple {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-up-material-purple, .jqx-warning .jqx-icon-arrow-up-material-purple, .jqx-danger .jqx-icon-arrow-up-material-purple, .jqx-success .jqx-icon-arrow-up-material-purple, .jqx-info .jqx-icon-arrow-up-material-purple {
    background-image: url('images/material-icon-up.png');
}

.jqx-primary .jqx-icon-arrow-up-selected-material-purple, .jqx-warning .jqx-icon-arrow-up-selected-material-purple, .jqx-danger .jqx-icon-arrow-up-selected-material-purple, .jqx-success .jqx-icon-arrow-up-selected-material-purple, .jqx-info .jqx-icon-arrow-up-selected-material-purple {
    background-image: url('images/material-icon-up.png');
}

.jqx-primary .jqx-icon-arrow-up-hover-material-purple, .jqx-warning .jqx-icon-arrow-up-hover-material-purple, .jqx-danger .jqx-icon-arrow-up-hover-material-purple, .jqx-success .jqx-icon-arrow-up-hover-material-purple, .jqx-info .jqx-icon-arrow-up-hover-material-purple {
    background-image: url('images/material-icon-up.png');
}


.jqx-primary .jqx-icon-arrow-left-material-purple, .jqx-warning .jqx-icon-arrow-left-material-purple, .jqx-danger .jqx-icon-arrow-left-material-purple, .jqx-success .jqx-icon-arrow-left-material-purple, .jqx-info .jqx-icon-arrow-left-material-purple {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-left-selected-material-purple, .jqx-warning .jqx-icon-arrow-left-selected-material-purple, .jqx-danger .jqx-icon-arrow-left-selected-material-purple, .jqx-success .jqx-icon-arrow-left-selected-material-purple, .jqx-info .jqx-icon-arrow-left-selected-material-purple {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-left-hover-material-purple, .jqx-warning .jqx-icon-arrow-left-hover-material-purple, .jqx-danger .jqx-icon-arrow-left-hover-material-purple, .jqx-success .jqx-icon-arrow-left-hover-material-purple, .jqx-info .jqx-icon-arrow-left-hover-material-purple {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-right-material-purple, .jqx-warning .jqx-icon-arrow-right-material-purple, .jqx-danger .jqx-icon-arrow-right-material-purple, .jqx-success .jqx-icon-arrow-right-material-purple, .jqx-info .jqx-icon-arrow-right-material-purple {
    background-image: url('images/material-icon-right.png');
}

.jqx-primary .jqx-icon-arrow-right-selected-material-purple, .jqx-warning .jqx-icon-arrow-right-selected-material-purple, .jqx-danger .jqx-icon-arrow-right-selected-material-purple, .jqx-success .jqx-icon-arrow-right-selected-material-purple, .jqx-info .jqx-icon-arrow-right-selected-material-purple {
    background-image: url('images/material-icon-right.png');
}

.jqx-primary .jqx-icon-arrow-right-hover-material-purple, .jqx-warning .jqx-icon-arrow-right-hover-material-purple, .jqx-danger .jqx-icon-arrow-right-hover-material-purple, .jqx-success .jqx-icon-arrow-right-hover-material-purple, .jqx-info .jqx-icon-arrow-right-hover-material-purple {
    background-image: url('images/material-icon-right.png');
}


/* Ripple effect */
.ripple {
    position: relative;
    transform: translate3d(0, 0, 0);
    overflow:hidden;
}

.ink {
    display: block;
    position: absolute;
    pointer-events: none;
    border-radius: 0%;
    transform: scaleX(0);
    background: rgba(98,0,238,0.5); 
    opacity: 0.25;
}


.outlined .ink, .flat .ink {
    background: rgba(98,0,238,0.5);
    overflow:hidden;
}

.ink.animate {
    animation: ripple .7s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-material-purple .ripple,
.jqx-radiobutton-material-purple .ripple
 {
    overflow:visible;
}
.jqx-checkbox-material-purple .ink,
.jqx-radiobutton-material-purple .ink 
 {
    transform: scale(0); 
    background: #6200EE;
    border-radius: 50%;
}
.jqx-checkbox-material-purple .ink.animate,
.jqx-radiobutton-material-purple .ink.animate
 {
    animation: checkRipple 0.3s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-material-purple .ink.active,
.jqx-radiobutton-material-purple .ink.active
 {
    opacity: 0.2;
    transform: scale(2);
}
.jqx-checkbox-default-material-purple.active .ink,
.jqx-radiobutton-default-material-purple.active .ink
 {
    opacity: 0.2;
    transform: scale(2);
}
/* Ripple effect */
.buttonRipple {
  background-position: center;
  transition: background 0.8s;
}
.buttonRipple:hover {
  background: #6200EE radial-gradient(circle, transparent 1%, #6200EE 1%) center/15000%;
}
.buttonRipple:active {
  background-color: #ECE0FD;
  background-size: 100%;
  transition: background 0s;
}
.buttonRipple:active:not(:hover) {
      color: #333;
}
@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(5);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}
@keyframes checkRipple {
    100% {
        opacity: 0.2;
        transform: scale(2);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}




.jqx-fill-state-pressed-material-purple-purple .jqx-icon-delete-material-purple
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-material-purple .jqx-icon-edit-material-purple
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-material-purple .jqx-icon-save-material-purple
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-material-purple .jqx-icon-cancel-material-purple
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-material-purple .jqx-icon-search-material-purple
{
    background-image: url(./images/search_white.png);
}
.jqx-fill-state-pressed-material-purple .jqx-icon-plus-material-purple
{
    background-image: url(./images/plus_white.png);
}
.jqx-fill-state-pressed-material-purple .jqx-menu-minimized-button-material-purple {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-fill-state-hover-material-purple .jqx-editor-toolbar-icon-material-purple, .jqx-fill-state-pressed-material-purple .jqx-editor-toolbar-icon-material-purple {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-fill-state-hover-material-purple .jqx-editor-toolbar-icon-fontsize-material-purple,
.jqx-fill-state-pressed-material-purple .jqx-editor-toolbar-icon-fontsize-material-purple,
.jqx-fill-state-hover-material-purple .jqx-editor-toolbar-icon-forecolor-material-purple,
.jqx-fill-state-pressed-material-purple .jqx-editor-toolbar-icon-forecolor-material-purple
{
        background: url('images/html_editor.png') no-repeat;
}
.jqx-editor-toolbar-button-material-purple{
    border-color: #ddd;
    box-shadow: none !important;
	color: #333;
}
.jqx-input-group-material-purple.jqx-fill-state-disabled {
	pointer-events: none;
}

/*applied to the timepicker*/
.jqx-needle-central-circle-material-purple {
	fill: rgb(98, 0, 238);
}

.jqx-needle-material-purple {
	fill: rgb(98, 0, 238);
}

.jqx-time-picker .jqx-header .jqx-selected-material-purple:focus {
    outline: 2px solid rgba(98, 0, 238, 0.5);
	box-shadow: 0px 0px 4px 2px rgba(98, 0, 238, 0.125);
}

.jqx-svg-picker-material-purple:focus {
	border: 1px solid rgb(98, 0, 238) !important;
}


.jqx-split-layout-component-material-purple .jqx-split-layout {
    --jqx-primary-rgb: 98, 0, 238;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-primary-color: #fff;
    --jqx-background: #fff;
    --jqx-background-color: rgba(0,0,0, .88);
    --jqx-background-hover-rgb: 225, 225, 225;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(0,0,0,.54);
    --jqx-surface-rgb: 255, 255, 255;
    --jqx-surface: rgb(var(--jqx-surface-rgb));
    --jqx-surface-color: rgba(0,0,0, .88);
    --jqx-border: rgba(98, 0, 238, 1);
}

.jqx-dropdownbutton-popup,
.jqx-calendar-material-purple.jqx-popup,
.jqx-listbox-material-purple.jqx-popup,
.jqx-grid-menu.jqx-popup {
  transition: transform 0.25s ease-in-out, opacity 0.35s ease-in-out;
  transform: scaleY(0);
  opacity: 0;
  transform-origin: top left;
  display: block !important;
}

.jqx-dropdownbutton-popup.jqx-popup-show,
.jqx-calendar-material-purple.jqx-popup-show,
.jqx-listbox-material-purple.jqx-popup-show,
.jqx-grid-menu.jqx-popup-show {
  transform: scaleY(1);
  opacity: 1;
}
