/*
jQWidgets v19.2.0 (2024-May)
Copyright (c) 2011-2024 jQWidgets.
License: https://jqwidgets.com/license/
*/
/* eslint-disable */

function _typeof(b){if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){a=function a(c){return typeof c}}else{a=function a(c){return c&&typeof Symbol==="function"&&c.constructor===Symbol&&c!==Symbol.prototype?"symbol":typeof c}}return a(b)}function _get(d,c,b){if(typeof Reflect!=="undefined"&&Reflect.get){a=Reflect.get}else{a=function a(h,g,f){var e=_superPropBase(h,g);if(!e){return}var i=Object.getOwnPropertyDescriptor(e,g);if(i.get){return i.get.call(f)}return i.value}}return a(d,c,b||d)}function _superPropBase(a,b){while(!Object.prototype.hasOwnProperty.call(a,b)){a=_getPrototypeOf(a);if(a===null){break}}return a}function _classCallCheck(a,b){if(!(a instanceof b)){throw new TypeError("Cannot call a class as a function")}}function _defineProperties(d,b){for(var a=0;a<b.length;a++){var c=b[a];c.enumerable=c.enumerable||false;c.configurable=true;if("value" in c){c.writable=true}Object.defineProperty(d,c.key,c)}}function _createClass(c,a,b){if(a){_defineProperties(c.prototype,a)}if(b){_defineProperties(c,b)}return c}function _inherits(b,a){if(typeof a!=="function"&&a!==null){throw new TypeError("Super expression must either be null or a function")}b.prototype=Object.create(a&&a.prototype,{constructor:{value:b,writable:true,configurable:true}});if(a){_setPrototypeOf(b,a)}}function _createSuper(b){var c=_isNativeReflectConstruct();return function a(){var e=_getPrototypeOf(b),d;if(c){var f=_getPrototypeOf(this).constructor;d=Reflect.construct(e,arguments,f)}else{d=e.apply(this,arguments)}return _possibleConstructorReturn(this,d)}}function _possibleConstructorReturn(a,b){if(b&&(_typeof(b)==="object"||typeof b==="function")){return b}return _assertThisInitialized(a)}function _assertThisInitialized(a){if(a===void 0){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return a}function _wrapNativeSuper(a){var c=typeof Map==="function"?new Map():undefined;b=function b(d){if(d===null||!_isNativeFunction(d)){return d}if(typeof d!=="function"){throw new TypeError("Super expression must either be null or a function")}if(typeof c!=="undefined"){if(c.has(d)){return c.get(d)}c.set(d,e)}function e(){return _construct(d,arguments,_getPrototypeOf(this).constructor)}e.prototype=Object.create(d.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});return _setPrototypeOf(e,d)};return b(a)}function _construct(d,c,b){if(_isNativeReflectConstruct()){a=Reflect.construct}else{a=function a(i,h,g){var f=[null];f.push.apply(f,h);var j=Function.bind.apply(i,f);var e=new j();if(g){_setPrototypeOf(e,g.prototype)}return e}}return a.apply(null,arguments)}function _isNativeReflectConstruct(){if(typeof Reflect==="undefined"||!Reflect.construct){return false}if(Reflect.construct.sham){return false}if(typeof Proxy==="function"){return true}try{Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));return true}catch(a){return false}}function _isNativeFunction(a){return Function.toString.call(a).indexOf("[native code]")!==-1}function _setPrototypeOf(b,a){c=Object.setPrototypeOf||function c(e,d){e.__proto__=d;return e};return c(b,a)}function _getPrototypeOf(b){a=Object.setPrototypeOf?Object.getPrototypeOf:function a(c){return c.__proto__||Object.getPrototypeOf(c)};return a(b)}if(!window.JQX){window.JQX={Utilities:{Core:{isMobile:function isMobile(){var a=/(iphone|ipod|ipad|android|iemobile|blackberry|bada)/.test(window.navigator.userAgent.toLowerCase());var b=function b(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend" in document};if(!a){return b()}return a}}}}}var LayoutItem=function(e){_inherits(j,e);var i=_createSuper(j);function j(){var k;_classCallCheck(this,j);k=i.call(this);k._properties={min:50,label:"Item",modifiers:["resize","drag","close"],size:null};return k}_createClass(j,[{key:"_setProperty",value:function d(n,m){var l=this;if(l._properties[n]===m){return}l._properties[n]=m;l._updating=true;if(n==="disabled"||n==="modifiers"){if(m){l.setAttribute(n,m)}else{l.removeAttribute(n)}}else{if(m===null){l.removeAttribute(n)}else{l.setAttribute(n,m)}}if(!l.isCompleted){return}var k=l.closest("jqx-layout");if(k){if(!k._resizeDetails&&!k._updating&&k.isRendered){k.refresh()}}l._updating=false}},{key:"label",get:function b(){return this._properties.label},set:function h(k){this._setProperty("label",k)}},{key:"modifiers",get:function b(){return this._properties.modifiers},set:function h(k){this._setProperty("modifiers",k)}},{key:"min",get:function b(){return this._properties.min},set:function h(k){this._setProperty("min",k)}},{key:"size",get:function b(){return this._properties.size},set:function h(k){if(k!==null){if(typeof k==="string"){this._setProperty("size",k)}else{this._setProperty("size",Math.max(this.min,k))}}else{this._setProperty("size",k)}}},{key:"attributeChangedCallback",value:function f(l,k,n){var m=this;if(k===n){return}if(!m.isCompleted){return}if(l==="size"){if(!m._updating){if(n===null){this[l]=null;return}m[l]=Math.max(m.min,parseInt(n))}}else{m[l]=n}}},{key:"connectedCallback",value:function g(){if(!this.isCompleted){this.render()}}},{key:"whenRendered",value:function c(l){var k=this;if(k.isRendered){l();return}if(!k.whenRenderedCallbacks){k.whenRenderedCallbacks=[]}k.whenRenderedCallbacks.push(l)}},{key:"render",value:function a(){var o=this;if(!o.hasAttribute("data-id")){o.setAttribute("data-id","id"+Math.random().toString(16).slice(2))}if(!o.hasAttribute("label")){o.setAttribute("label",o.label)}if(!o.hasAttribute("min")){o.setAttribute("min",o.min)}if(!o.hasAttribute("label")){o.setAttribute("label",o.label)}if(!o.hasAttribute("modifiers")){o.setAttribute("modifiers",o.modifiers)}for(var l=0;l<o.attributes.length;l++){var n=o.attributes[l];var k=n.name;var m=n.value;if(!isNaN(m)&&(k==="min"||k==="size")){o._properties[k]=parseInt(m);continue}o._properties[k]=m}o.classList.add("jqx-layout-item");o.isCompleted=true;if(o.whenRenderedCallbacks){for(var l=0;l<o.whenRenderedCallbacks.length;l++){o.whenRenderedCallbacks[l]()}o.whenRenderedCallbacks=[]}}}],[{key:"observedAttributes",get:function b(){return["min","size","label","modifiers"]}}]);return j}(_wrapNativeSuper(HTMLElement));var LayoutGroup=function(e){_inherits(d,e);var c=_createSuper(d);function d(){var g;_classCallCheck(this,d);g=c.call(this);g._properties.label="Group";g._properties.orientation="vertical";return g}_createClass(d,[{key:"orientation",get:function a(){return this._properties.orientation},set:function f(g){this._setProperty("orientation",g)}},{key:"render",value:function b(){var g=this;_get(_getPrototypeOf(d.prototype),"render",this).call(this);g.className="jqx-layout-group";if(!g.hasAttribute("orientation")){g.setAttribute("orientation",g._properties.orientation)}else{g._properties.orientation=g.getAttribute("orientation")}}}],[{key:"observedAttributes",get:function a(){return["min","size","modifiers","orientation","position"]}}]);return d}(LayoutItem);var TabLayoutGroup=function(d){_inherits(b,d);var e=_createSuper(b);function b(){var g;_classCallCheck(this,b);g=e.call(this);g._properties.position="top";g._properties.label="TabGroup";return g}_createClass(b,[{key:"position",get:function a(){return this._properties.position},set:function f(g){this._setProperty("position",g)}},{key:"render",value:function c(){var g=this;_get(_getPrototypeOf(b.prototype),"render",this).call(this);if(!g.hasAttribute("position")&&g.position){g.setAttribute("position","top")}}}],[{key:"observedAttributes",get:function a(){return["min","size","modifiers","orientation","position"]}}]);return b}(LayoutGroup);var TabLayoutItem=function(b){_inherits(a,b);var c=_createSuper(a);function a(){var d;_classCallCheck(this,a);d=c.call(this);d._properties.label="TabItem";return d}return a}(LayoutGroup);(function(g){g.jqx.jqxWidget("jqxSplitLayout","",{});g.extend(g.jqx._jqxSplitLayout.prototype,{defineInstance:function e(){var h={dataSource:null,ready:null,orientation:"vertical"};if(this===g.jqx._jqxSplitLayout.prototype){return h}g.extend(true,this,h);return h},createInstance:function a(){var h=this;this._properties={dataSource:null,ready:null,orientation:"vertical"};var h=this;h.layout=document.createElement("jqx-split-layout");h.layout.style.width="100%";h.layout.style.height="100%";h.element.className+=h.toThemeProperty("jqx-split-layout-component jqx-rc-all jqx-widget");h.layout.dataSource=h.dataSource;h.layout.orientation=h.orientation;h.layout.ready=h.ready;h.element.appendChild(h.layout)},propertyChangedHandler:function b(i,j,h,l){var k=i;if(h!=l||l instanceof Object){if(!k.layout){return}k.layout[j]=l}},render:function d(){var h=this;if(!h.layout){return}h.layout.render()},refresh:function c(){var h=this;if(!h.layout){return}if(!h.layout.isRendered){return}h.layout.refresh()},dataBind:function f(){var h=this;if(!h.layout){return}h.layout.dataBind()}})})(jqxBaseFramework);var SplitLayout=function(G){_inherits(i,G);var g=_createSuper(i);function i(){var O;_classCallCheck(this,i);O=g.call(this);O._properties={dataSource:null,orientation:"vertical"};return O}_createClass(i,[{key:"orientation",get:function J(){return this._properties.orientation},set:function s(O){this._properties.orientation=O}},{key:"dataSource",get:function J(){return this._properties.dataSource},set:function s(O){this._properties.dataSource=O}},{key:"_dragStart",value:function L(O){O.stopPropagation();O.preventDefault()}},{key:"_leaveHandler",value:function C(){var O=this;if(O._resizeDetails){return}O._handleButtonsVisibility(null);O._hideSplitter();requestAnimationFrame(function(){})}},{key:"_enterHandler",value:function y(){var O=this;if(O._resizeDetails){return}O._handleButtonsVisibility(O._selectedItem);O._updateSplitter();requestAnimationFrame(function(){O.classList.add("outline")})}},{key:"template",value:function j(){return'<div class="jqx-container" id="container" role="presentation"><jqx-layout-group data-id="root" id="itemsContainer"></jqx-layout-group><div root-splitter id="splitter" class="jqx-layout-splitter"></div>'}},{key:"propertyChangedHandler",value:function B(O,P,R){var Q=this;switch(O){case"orientation":if(Q.$.itemsContainer){Q.$.itemsContainer.orientation=Q.orientation}break;case"dataSource":Q.dataBind();break;case"selectedIndex":Q._handleItemClick(Q.getItem(R+""),true);break;default:_get(_getPrototypeOf(i.prototype),"propertyChangedHandler",this).call(this,O,P,R);break}}},{key:"dataBind",value:function p(){var Q=this;Q.$.itemsContainer.innerHTML="";var O="";var P=function P(R,Y){for(var V=0;V<R.length;V++){var ad=R[V];var ae=ad.size;var U=ad.min;var ac=ad.modifiers;var aa=ad.type;var W=ad.position;var T=ad.orientation?ad.orientation:"vertical";var S=ad.id;var ab=ad.label;var Z="";if(S!==undefined){Z+='id="'.concat(S,'" ')}if(ae!==undefined){Z+='size="'.concat(ae,'" ')}if(ab!==undefined){Z+='label="'.concat(ab,'" ')}if(U!==undefined){Z+='min="'.concat(U,'" ')}if(ac!==undefined){Z+='modifiers="'.concat(ac,'" ')}if(W!==undefined){Z+='position="'.concat(W,'" ')}if(ad.items){Z+="orientation=".concat(T," ");if(aa==="tabs"){O+="<jqx-tab-layout-group ".concat(Z,">");P(ad.items,true);O+="</jqx-tab-layout-group>"}else{O+="<jqx-layout-group ".concat(Z,">");P(ad.items);O+="</jqx-layout-group>"}}else{var X=ad.content||"";if(Y){O+="<jqx-tab-layout-item ".concat(Z,">")+X+"</jqx-tab-layout-item>"}else{if(aa==="tabs"){O+="<jqx-tab-layout-group>";O+="<jqx-tab-layout-item ".concat(Z,">")+X+"</jqx-tab-layout-item>";O+="</jqx-tab-layout-group>"}else{O+="<jqx-layout-item ".concat(Z,">")+X+"</jqx-layout-item>"}}}}};P(Q.dataSource);Q.$.itemsContainer.innerHTML=O;Q.refresh()}},{key:"render",value:function w(){var P=this;P.setAttribute("role","group");if(P.selectedIndex){P._handleItemClick(P.getItem(P.selectedIndex+""),true)}var O=function O(){if(!P.dataSource){P.dataSource=P._getDataSource(P._getLayout())}else{P.dataBind()}P.$.itemsContainer.orientation=P.orientation;P.refresh();P._updateSplitter();P.isRendered=true;P.classList.add("outline");if(P.ready){P.ready()}};if(document.readyState==="complete"){O()}else{window.addEventListener("load",function(){O()})}}},{key:"connectedCallback",value:function H(){var P=this;var O=function O(){var Q=document.createDocumentFragment();while(P.childNodes.length){Q.appendChild(P.firstChild)}P.innerHTML=P.template();P.classList.add("jqx-widget");P.$={container:P.querySelector("#container"),itemsContainer:P.querySelector("#itemsContainer"),splitter:P.querySelector("#splitter")};delete P.$.container.id;delete P.$.itemsContainer.id;delete P.$.splitter.id;P.$.itemsContainer.appendChild(Q);P.classList.add("jqx-split-layout");document.addEventListener("pointerdown",function(R){P._documentDownHandler(R)});document.addEventListener("pointermove",function(R){P._documentMoveHandler(R)});document.addEventListener("pointerup",function(R){P._documentUpHandler(R)});document.addEventListener("selectstart",function(R){P._documentSelectStartHandler(R)});document.addEventListener("keyup",function(R){P._keyUpHandler(R)});P.addEventListener("mouseleave",function(R){P._leaveHandler(R)});P.addEventListener("mouseenter",function(R){P._enterHandler(R)});P.addEventListener("dragStart",function(R){P._dragStart(R)});P.render()};if(document.readyState==="complete"){O()}else{window.addEventListener("load",function(){O()})}}},{key:"getItem",value:function c(P){var S=this;if(P===undefined||P===null){return}P=(P+"").split(".");var O=S._getDataSource(S._getLayout()),R;for(var Q=0;Q<P.length;Q++){R=O[P[Q]];if(!R){break}O=R.items}return R}},{key:"_documentDownHandler",value:function D(P){var O=this,Q=P.target;if(O.contains(Q)&&Q.closest){O._target=Q;O._updateSplitter()}}},{key:"_documentMoveHandler",value:function u(ak){var ab=this,au=ak.target,Q=ab._contextMenu;if(Q&&!JQX.Utilities.Core.isMobile){if(Q.querySelector(".jqx-layout-context-menu-item[hover]")){var ai=Q.children;for(var al=0;al<ai.length;al++){ai[al].removeAttribute("hover")}}if(Q.contains(au)&&au.closest&&au.closest(".jqx-layout-context-menu-item")){au.setAttribute("hover","")}}if(ab._dragDetails){var ao=Math.abs(ab._dragDetails.pageX-ak.pageX);var an=Math.abs(ab._dragDetails.pageY-ak.pageY);if(an<=5&&ao<=5){return}if(!ab._dragDetails.feedback.parentElement){document.body.appendChild(ab._dragDetails.feedback);document.body.appendChild(ab._dragDetails.overlay);setTimeout(function(){ab._dragDetails.feedback.classList.add("dragging")},100)}ab._dragDetails.dragging=true;ab._dragDetails.feedback.style.left=ak.pageX-ab._dragDetails.feedback.offsetWidth/2-5+"px";ab._dragDetails.feedback.style.top=ak.pageY-ab._dragDetails.feedback.offsetHeight/2-5+"px";var ag=document.elementsFromPoint(ak.pageX,ak.pageY);var ad=null;var Z=false;for(var al=0;al<ag.length;al++){var S=ag[al];if(ab._dragDetails.feedback.contains(S)){continue}if(S.classList.contains("jqx-layout-tab-strip")){if(ab._dragDetails.element.contains(S)){continue}ad=S.parentElement;Z=true;break}if((S.parentElement===ab._dragDetails.parent||S===ab._dragDetails.parent)&&ab._dragDetails.layoutGroup.items.length===1){continue}if(ab._dragDetails.element.contains(S)){continue}if(S instanceof TabLayoutItem){ad=S.parentElement;break}else{if(S instanceof TabLayoutGroup){ad=S;break}}}var at=function at(aC,aF){var ay=ab.offset(aC);var aA=null;var ax=50;var aE=aF;var aw=aF;if(!aF){aw=aC.offsetWidth/3;aE=aC.offsetHeight/3}else{ax=0}var aD=[{left:ay.left,top:ay.top,right:ay.left+ax,bottom:ay.top+ax,position:"top"},{left:ay.left+ax,top:ay.top,right:ay.left+aC.offsetWidth-ax,bottom:ay.top+aE-ax,position:"top"},{left:ay.left+aC.offsetWidth-ax,top:ay.top,right:ay.left+aC.offsetWidth,bottom:ay.top+ax,position:"top"},{left:ay.left,top:ay.top+ax,right:ay.left+aw,bottom:ay.top+aC.offsetHeight-ax,position:"left"},{left:ay.left+aC.offsetWidth-aw,top:ay.top+ax,right:ay.left+aC.offsetWidth,bottom:ay.top+aC.offsetHeight-ax,position:"right"},{left:ay.left,top:ay.top+aC.offsetHeight-ax,right:ay.left+ax,bottom:ay.top+aC.offsetHeight,position:"bottom"},{left:ay.left+ax,top:ay.top+aC.offsetHeight-aE+ax,right:ay.left+aC.offsetWidth-ax,bottom:ay.top+aC.offsetHeight,position:"bottom"},{left:ay.left+aC.offsetWidth-ax,top:ay.top+aC.offsetHeight-ax,right:ay.left+aC.offsetWidth,bottom:ay.top+aC.offsetHeight,position:"bottom"}];for(var az=0;az<aD.length;az++){var aB=aD[az];if(aB.left<=ak.pageX&&ak.pageX<=aB.right){if(aB.top<=ak.pageY&&ak.pageY<=aB.bottom){aA=aB.position;break}}}return aA};var af=ab.querySelector("jqx-layout-group");var av=at(af,10);var U=null;if(!av){if(!ad){ab._handleDropArea(null)}else{if(Z){if(ad!==ab._dragDetails.parent){av="center";U=ad}}else{av=at(ad)||"center";U=ad}}}else{U=af}if(U){ab._dragDetails.current=U;ab._dragDetails.position=av;ab._handleDropArea(U,av)}}if(ab._resizeDetails){var ao=Math.abs(ab._resizeDetails.clientX-ak.clientX);var an=Math.abs(ab._resizeDetails.clientY-ak.clientY);var T=ab._resizeDetails.splitter;var aq=ab._resizeDetails.item;var ap=ab._resizeDetails.itemRect;var ar=ab._resizeDetails.previousItemRect;var am=ab._resizeDetails.previousItem;var R=ab._resizeDetails.nextItemRect;var aa=ab._resizeDetails.nextItem;var P=parseInt(aq.getAttribute("min"));var ac=function ac(aw){if(aw.classList.contains("jqx-visibility-hidden")){return}aw.style.right="";aw.style.top="";aw.style.left="";aw.style.bottom=""};ac(T);ac(ab.$.splitter);T.classList.remove("error");T.classList.add("active");if(!ab._resizeDetails.dragging){if(T.classList.contains("horizontal")&&an<=5){return}else{if(T.classList.contains("vertical")&&ao<=5){return}}ab._resizeDetails.dragging=true}var aj={clientPos:"clientX",pos:"x",size:"width",near:"left",far:"right",offsetSize:"offsetWidth"};if(T.classList.contains("horizontal")){aj={clientPos:"clientY",pos:"y",size:"height",near:"top",far:"bottom",offsetSize:"offsetHeight"}}var W=function W(ax){var ay=ab.offset(ax);var aw=ab.offset(ab);aw.left++;aw.top++;ab.$.splitter.style.width=ax.offsetWidth+"px";ab.$.splitter.style.height=ax.offsetHeight+"px";ab.$.splitter.className=ax.className;ab.$.splitter.style.left=ay.left-aw.left+"px";ab.$.splitter.style.top=ay.top-aw.top+"px";ax.setAttribute("drag","");ab.$.splitter.setAttribute("drag","")};if(T.classList.contains("last")){var V=ak[aj.clientPos]-ab._resizeDetails.splitterRect[aj.pos];var Y=ap[aj.size]-P;if(V>Y){V=Y;T.classList.add("error")}if(ar){var P=parseInt(am.getAttribute("min"));var X=ar[aj.size]-P;if(V<-X){V=-X;T.classList.add("error")}}T.style[aj.near]=V+"px";var O=aq[aj.offsetSize]-V;aq.setAttribute("size",O);if(am){var ae=aq[aj.offsetSize]+am[aj.offsetSize]-O;am.setAttribute("size",ae)}}else{var V=-ak[aj.clientPos]+ab._resizeDetails.splitterRect[aj.pos];var X=ap[aj.size]-P;if(V>X){V=X;T.classList.add("error")}if(R){var P=parseInt(aa.getAttribute("min"));var Y=-R[aj.size]+P;if(V<Y){V=Y;T.classList.add("error")}}T.style[aj.far]=V+"px";var O=aq[aj.offsetSize]-V;aq.setAttribute("size",O);if(aa){var ah=aa[aj.offsetSize]+aq[aj.offsetSize]-O;aa.setAttribute("size",ah)}}W(T)}}},{key:"_offsetTop",value:function r(O){var P=this;if(!O){return 0}return O.offsetTop+P._offsetTop(O.offsetParent)}},{key:"_offsetLeft",value:function z(O){var P=this;if(!O){return 0}return O.offsetLeft+P._offsetLeft(O.offsetParent)}},{key:"offset",value:function F(O){return{left:this._offsetLeft(O),top:this._offsetTop(O)}}},{key:"_keyUpHandler",value:function M(Q){var P=this;if(Q.key==="Escape"){if(P._dragDetails){P._dragDetails.feedback.remove();P._dragDetails.overlay.remove();P._dragDetails=null;P._handleDropArea(null)}if(P._resizeDetails){var O=P._resizeDetails;O.splitter.classList.contains("last")?O.previousItem.size=O.previousItemSize:O.nextItem.size=O.nextItem.previousItemSize;O.item.size=O.itemSize;P.refresh();P._handleItemClick(O.item);P._resizeDetails=null;return}}else{if(Q.key==="Delete"){if(P._selectedItem){P._removeLayoutItem(P._selectedItem)}}}}},{key:"_endDrag",value:function e(){var T=this;T._handleDropArea(null);if(!T._dragDetails.dragging){T._dragDetails=null;return}var V=T._dragDetails.current;var S=T._dragDetails.element;var O=T._dragDetails.position;T._handleDropArea(null);if(V){T._addTabLayoutItem(V,O,S);T._removeLayoutItem(S);if(V.parentElement&&Array.from(V.parentElement.parentElement.children).filter(function(W){if(W.classList.contains("jqx-layout-group")){return true}return false}).length===1){var R=V.parentElement;var U=R.parentElement;var P=U.parentElement;if(!(U.getAttribute("data-id")==="root"||P.getAttribute("data-id")==="root")&&P!==T){var Q=Array.from(P.children).indexOf(R.parentElement);if(Q>=0){P.insertBefore(R,P.children[Q])}else{P.appendChild(R)}U.remove()}}T.refresh();T._updateSplitter();requestAnimationFrame(function(){T.classList.add("outline");T.querySelectorAll(".jqx-element").forEach(function(W){T.dispatchEvent(new CustomEvent("resize"))})})}T.dispatchEvent(new CustomEvent("stateChange",{type:"insert",item:S}));T._dragDetails.feedback.remove();T._dragDetails.overlay.remove();T._dragDetails=null}},{key:"_documentUpHandler",value:function q(S){var R=this,O=JQX.Utilities.Core.isMobile,T=O?document.elementFromPoint(S.pageX-window.pageXOffset,S.pageY-window.pageYOffset):S.target;if(S.button===2){return}if(R._dragDetails){R._endDrag(S)}if(R._resizeDetails){var Q=R._resizeDetails;if(Q.item){Q.item.style.overflow=""}if(Q.previousItem){Q.previousItem.style.overflow=""}if(Q.nextItem){Q.nextItem.style.overflow=""}R.refresh();R._handleItemClick(Q.item);R._resizeDetails=null;window.dispatchEvent(new Event("resize"));R.querySelectorAll(".jqx-element").forEach(function(U){U.dispatchEvent(new CustomEvent("resize"))});return}if(!R.contains(T)){return}R.classList.add("outline");if(R._target&&!T.item){if(T instanceof TabLayoutItem){R._handleItemClick(T)}else{R._handleItemClick(T.closest(".jqx-layout-item"))}}if(R._target){if(R._target!==T){delete R._target;return}if(!S.button&&T.closest(".jqx-layout-buttons-container")){var P=S.target;R._handleButtonClick(P.item,P.position)}else{if(T.closest(".jqx-layout-context-menu")&&(!O&&!S.button||O)){R._handleMenuItemClick(T.closest(".jqx-layout-context-menu-item"))}}delete R._target}}},{key:"_documentSelectStartHandler",value:function t(P){var O=this;if(O._target){P.preventDefault()}}},{key:"_getDataSource",value:function o(R,W,T){var S=this;var P=[];if(!T){T=0}if(!W){W=""}for(var Q=0;Q<R.length;Q++){var O=R[Q];var V={label:O.label,id:O.getAttribute("data-id"),orientation:O.orientation,size:O.size,min:O.min,type:O.type,modifiers:O.modifiers,position:O.position};O.removeAttribute("index");if(O instanceof LayoutGroup){P.push(V);V.index=W!==""?W+"."+T:T.toString();O.setAttribute("index",V.index);if(O.items){var U=S._getDataSource(O.items,V.index,0);V.items=U}}else{if(O instanceof LayoutItem){if(O.items){var U=S._getDataSource(O.items,W,T);P=P.concat(U)}else{V.index=W!==""?W+"."+T:T.toString();O.setAttribute("index",V.index);P.push(V)}}}T++}return P}},{key:"_getLayout",value:function K(){var S=this;var V=!arguments.length?S.$.itemsContainer:arguments[0];if(S._buttons){S._buttons.remove()}if(S._dropArea){S._dropArea.remove()}var R=S.querySelectorAll(".jqx-layout-splitter");for(var P=0;P<R.length;P++){var U=R[P];if(U!==S.$.splitter){U.remove()}}V.items=Array.from(V.children);V.items=V.items.filter(function(W){return W!==V.tabs&&W.hasAttribute("data-id")});var O=V.items.map(function(X){if(X.classList.contains("jqx-layout-tab-strip")){return null}var W=X;var Y=X instanceof LayoutGroup?X:null;if(Y){W.items=S._getLayout(Y)}return W});if(V!==S.$.itemsContainer){return O.filter(function(W){return W!==null&&W!==V.tabs})}var T=[];var Q=V;Q.items=O.filter(function(W){return W!==null&&W!==V.tabs});T.push(Q);return T}},{key:"_updateSplitter",value:function l(){var R=this;if(R._buttons&&R._dragDetails){R._buttons.remove()}R._removeSplitter();var O=R.querySelectorAll("[data-id]");for(var P=0;P<O.length;P++){var Q=O[P];if(Q.getAttribute("data-id")==="root"){continue}if(Q.hasAttribute("role")){var S=Q.getAttribute("role");if(S==="gridcell"||S==="row"||S==="columnheader"||S==="rowheader"){continue}}Q.setAttribute("hover","");R._handleSplitter(Q)}}},{key:"_hideSplitter",value:function h(){var R=this;var O=R.querySelectorAll("[data-id]");for(var P=0;P<O.length;P++){var Q=O[P];Q.removeAttribute("hover")}}},{key:"_removeSplitter",value:function x(){var Q=this;var P=Q.querySelectorAll(".jqx-layout-splitter");for(var O=0;O<P.length;O++){var R=P[O];if(R!==Q.$.splitter){R.remove()}}Q._hideSplitter()}},{key:"_handleItemClick",value:function I(R){var Q=this,O=Q.selectedIndex;var P=null;if(!R){Q.selectedIndex=null;Q.querySelectorAll("[data-id]").forEach(function(S){S.removeAttribute("selected")});Q._selectedItem=null;return}else{P=R instanceof HTMLElement?R:Q.querySelector("[data-id="+R.id+"]");if(P&&P.readonly){Q.selectedIndex=null;return}Q.querySelectorAll("[data-id]").forEach(function(S){S.removeAttribute("selected")});if(!P){Q.refresh();return}Q.selectedIndex=P.getAttribute("index");P.setAttribute("selected","");P.setAttribute("hover","");Q._selectedItem=P;if(P.classList.contains("jqx-hidden")){Q.refresh()}Q._handleButtonsVisibility(P);if(O!==Q.selectedIndex){Q.dispatchEvent(new CustomEvent("change"))}}Q._updateSplitter()}},{key:"_handleButtonClick",value:function n(R,O){var Q=this,P=Q._addLayoutItem(R,O);Q.dispatchEvent(new CustomEvent("stateChange",{type:"insert",item:P}));Q._handleItemClick(P,true)}},{key:"_removeLayoutItem",value:function k(R){var Q=this;if(R.getAttribute("data-id")==="root"){return}if(R instanceof LayoutItem&&R.parentElement.items.length===1){var P=R.parentElement;var O=P;while(P&&P.items&&P.items.length===1){if(P.getAttribute("data-id")==="root"){break}O=P;P=P.parentElement}if(O.getAttribute("data-id")!=="root"){O.remove()}else{if(Q.allowLiveSplit){O.appendChild(document.createElement("jqx-layout-item"))}}}else{R.remove()}Q.refresh();Q.dispatchEvent(new CustomEvent("stateChange",{type:"delete",item:R}))}},{key:"refresh",value:function N(){var Q=this;if(Q._isUpdating){return}Q.dataSource=Q._getDataSource(Q._getLayout());Q.$.splitter.className="jqx-visibility-hidden jqx-layout-splitter";var R=function R(ab){var ae=Q.getItem(ab.getAttribute("index"));if(!ae){return}ab.style.gridTemplateColumns="";ab.style.gridTemplateRows="";var ac="";var S=0;var Y=0;if(ab instanceof TabLayoutGroup){if(ab.tabs){ab.tabs.remove()}var X=document.createElement("div");X.classList.add("jqx-layout-tab-strip");if(Q._selectedItem&&ab.contains(Q._selectedItem)&&Q._selectedItem instanceof TabLayoutItem){ab.selectedIndex=Math.max(0,ab.items.indexOf(Q._selectedItem))}if(ab.selectedIndex>=ab.children.length){ab.selectedIndex=0}for(var W=0;W<ab.children.length;W++){var T=ab.children[W];var Z=Q.getItem(T.getAttribute("index"));if(!Z){continue}var U=document.createElement("div");U.classList.add("jqx-layout-tab");U.innerHTML="<label>"+Z.label+'</label><span class="jqx-close-button"></span>';X.appendChild(U);T.setAttribute("tab","");T.classList.add("jqx-hidden");U.content=T;U.item=Z;U.group=ae;if(T.modifiers){if(T.modifiers.indexOf("close")===-1){U.querySelector(".jqx-close-button").classList.add("jqx-hidden")}}else{U.querySelector(".jqx-close-button").classList.add("jqx-hidden")}if(undefined===ab.selectedIndex||W===ab.selectedIndex){U.classList.add("selected");T.classList.remove("jqx-hidden");ab.selectedIndex=W}U.onpointerup=function(ag){if(ag.target.classList.contains("jqx-close-button")&&U.close){ab.selectedIndex=0;Q._removeLayoutItem(Q._selectedItem);Q._handleItemClick(parent)}};U.onpointerdown=function(ah){var ag=this.closest(".jqx-layout-group");Q._handleItemClick(this.content);U.close=false;if(!ah.target.classList.contains("jqx-close-button")){if(Z.modifiers&&Z.modifiers.indexOf("drag")>=0&&ag.modifiers.indexOf("drag")>=0){Q._beginDrag(ag,this,ah)}}else{U.close=true}}}ab.tabs=X;if(ae.position==="top"||ae.position==="left"){ab.insertBefore(X,ab.firstChild)}else{ab.appendChild(X)}}else{for(var W=0;W<ab.children.length;W++){var T=ab.children[W];if(T.hasAttribute("size")){var af=T.getAttribute("size");var V=parseFloat(af);var aa=ab.orientation==="vertical"?ab.offsetWidth:ab.offsetHeight;var ad=af.indexOf("%")>=0?parseFloat(af):parseFloat(V/aa*100);S+=ad;Y++;if(Y===ab.children.length){if(S<100){ac+="1fr ";S=100;continue}else{if(S>100){S-=ad;ad=100-S;S=100}}}else{if(S>100||ad===0){Y=ab.children.length;S=0;break}}ac+=ad+"% ";continue}ac+="1fr "}if(Y===ab.children.length){if(S<99||S>100){ac="";for(var W=0;W<ab.children.length;W++){var T=ab.children[W];T.removeAttribute("size");ac+="1fr "}}}if(ab.orientation==="vertical"){ab.style.gridTemplateColumns=ac}else{ab.style.gridTemplateRows=ac}}ab.items=Array.from(ab.children);ab.items=ab.items.filter(function(ag){return ag!==ab.tabs})};var P=Q.querySelectorAll(".jqx-layout-group");for(var O=0;O<P.length;O++){R(P[O])}}},{key:"_beginDrag",value:function A(W,S,O){var T=this;if(T._dragDetails){T._dragDetails.feedback.remove()}var P=document.createElement("div");var Q=document.createElement("div");var U=W.querySelector(".jqx-layout-tab-strip");var V="";if(U){for(var R=0;R<Array.from(U.children).length;R++){if(R===W.selectedIndex){V=U.children[R].innerText}}}P.innerHTML='<jqx-split-layout><jqx-tab-layout-group><jqx-tab-layout-item label="'.concat(V,'"></jqx-tab-layout-item></jqx-tab-layout-group></jqx-split-layout>');T._feedback=P;T._feedback.classList.add("jqx-split-layout-feedback","jqx-split-layout","jqx-widget");Q.classList.add("jqx-split-layout-overlay");T._dragDetails={element:S.content,item:S.item,layoutGroup:S.group,parent:W,overlay:Q,feedback:P,pageX:O.pageX,pageY:O.pageY}}},{key:"moveChildren",value:function v(Q,O){O.innerHTML="";var P=Q;while(P.firstChild){var R=P.firstChild;O.appendChild(R)}}},{key:"createLayoutItem",value:function d(R,O){var S=this;var P=function P(){var V=document.createElement("jqx-layout-item");V.innerHTML="";S.dispatchEvent(new CustomEvent("createItem",{type:"layoutItem",item:V}));return V};var U=function U(){var V=document.createElement("jqx-tab-layout-item");V.innerHTML="";S.dispatchEvent(new CustomEvent("createItem",{type:"tabLayoutItem",item:V}));return V};var T=function T(V){var X=document.createElement("jqx-layout-group");var W=V==="top"||V==="bottom"?"horizontal":"vertical";S.dispatchEvent(new CustomEvent("createGroup",{type:"layoutGroup",item:X}));X.setAttribute("orientation",W);X.orientation=W;return X};var Q=function Q(V){var X=document.createElement("jqx-tab-layout-group");var W=V==="top"||V==="bottom"?"horizontal":"vertical";X.setAttribute("orientation",W);X.orientation=W;S.dispatchEvent(new CustomEvent("tabLayoutGroup",{type:"layoutGroup",item:X}));return X};if(R==="layoutItem"||!R){return P()}else{if(R==="tabLayoutItem"||!R){return U()}else{if(R==="tabLayoutGroup"){return Q(O)}else{return T(O)}}}}},{key:"_addTabLayoutItem",value:function b(S,T,Q){var W=this;var U=W.createLayoutItem("tabLayoutItem");var R=S.closest("jqx-tab-layout-group");var X;if(Q){U.label=Q.label;U.modifiers=Q.modifiers;W.moveChildren(Q,U)}var Z=function Z(ab){for(var aa=0;aa<ab.children.length;aa++){var ac=ab.children[aa];ac.removeAttribute("size")}ab.removeAttribute("size")};var P=function P(aa){S.removeAttribute("size");if(S.querySelector("jqx-layout-group")){W._addLayoutItem(S.querySelector("jqx-layout-group"),aa)}else{X=W.createLayoutItem("layoutGroup",aa);var ab=W.createLayoutItem();W.moveChildren(S,ab);if(aa==="top"||aa==="left"){X.appendChild(W.createLayoutItem());X.appendChild(ab)}else{X.appendChild(ab);X.appendChild(W.createLayoutItem())}S.appendChild(X)}};var O=function O(ab,aa){var ac=S.parentElement;var ad=S;var ae=W.createLayoutItem("layoutGroup",aa);ac.insertBefore(ae,ad);if(aa==="top"||aa==="left"){ae.append(ab);ae.appendChild(ad)}else{ae.appendChild(ad);ae.append(ab)}if(ad.getAttribute("data-id")==="root"){ad.setAttribute("data-id",ae.getAttribute("data-id"));ae.setAttribute("data-id","root");W.$.itemsContainer=ae}Z(ad);Z(ac)};if(Q){switch(T){case"center":if(S instanceof TabLayoutGroup||S instanceof TabLayoutItem){R.appendChild(U)}else{var V=W.createLayoutItem("tabLayoutGroup","top");V.appendChild(U);if(S instanceof LayoutGroup&&!(S instanceof TabLayoutItem)){S.appendChild(V);Z(S)}else{if(S instanceof LayoutItem){X=W.createLayoutItem("layoutGroup");S.parentElement.insertBefore(X,S);X.appendChild(S);X.appendChild(V);Z(X)}}}break;case"left":case"right":var V=W.createLayoutItem("tabLayoutGroup","top");V.appendChild(U);if(S.getAttribute("data-id")==="root"){V.position=T;O(V,T)}else{O(V,T)}break;case"top":case"bottom":var V=W.createLayoutItem("tabLayoutGroup","top");V.appendChild(U);if(S.getAttribute("data-id")==="root"){V.position=T;O(V,T)}else{O(V,T)}break}return}switch(T){case"center":if(S instanceof TabLayoutGroup||S instanceof TabLayoutItem){R.appendChild(U)}else{P()}break;case"left":case"right":if(S instanceof TabLayoutGroup){var Y=S.querySelector("jqx-tab-layout-item");if(Y&&T==="left"){S.insertBefore(U,Y)}else{S.appendChild(U)}}else{if(S instanceof TabLayoutItem){var V=W.createLayoutItem("tabLayoutGroup","top");var R=S.parentElement;V.appendChild(U);X=W.createLayoutItem("layoutGroup");R.parentElement.insertBefore(X,R);if(T==="right"){X.appendChild(R);X.appendChild(V)}else{if(T==="left"){X.appendChild(V);X.appendChild(R)}}}else{if(Q){var V=W.createLayoutItem("tabLayoutGroup","top");V.appendChild(U);if(S instanceof LayoutGroup){S.insertBefore(S.firstChild,V)}else{if(S instanceof LayoutItem){X=W.createLayoutItem("layoutGroup");X.orientation=R.orientation;X.setAttribute("orientation",R.orientation);S.removeAttribute("size");S.parentElement.insertBefore(X,S);X.appendChild(S);X.appendChild(V)}}}else{P(T)}}}break;case"top":case"bottom":if(S instanceof TabLayoutGroup){X=W.createLayoutItem("layoutGroup","top");S.removeAttribute("size");S.parentElement.insertBefore(X,S);if(T==="top"){X.appendChild(W.createLayoutItem());X.appendChild(S)}else{X.appendChild(S);X.appendChild(W.createLayoutItem())}}else{P(T)}break}W.refresh()}},{key:"_addLayoutItem",value:function E(R,S,O){var W=this;if(!R){return}var Z=function Z(ab){for(var aa=0;aa<ab.children.length;aa++){var ac=ab.children[aa];ac.removeAttribute("size")}ab.removeAttribute("size")};var Y=R instanceof TabLayoutItem||R instanceof TabLayoutGroup||O&&O instanceof TabLayoutItem;if(Y){return W._addTabLayoutItem(R,S,O)}var U=W.createLayoutItem();var P=R.closest(".jqx-layout-group");var X;if(O){W.moveChildren(O,U)}if(S==="center"){if(R instanceof LayoutGroup){X=P;X.appendChild(U);Z(X);W.refresh();return U}else{if(R instanceof LayoutItem){X=W.createLayoutItem("layoutGroup");X.orientation=P.orientation;X.setAttribute("orientation",P.orientation);R.removeAttribute("size");R.parentElement.insertBefore(X,R);X.appendChild(R);X.appendChild(U);W.refresh();return X}}}if(P.orientation==="vertical"&&(S==="left"||S==="right")||P.orientation==="horizontal"&&(S==="top"||S==="bottom")){X=P;if(R instanceof LayoutGroup){if(S==="left"||S==="top"){X.insertBefore(U,X.children[0])}else{X.appendChild(U)}Z(R)}else{var T=X.items,Q=Math.max(0,T.indexOf(R)+(S==="top"||S==="left"?0:1));X.insertBefore(U,T[Q]);Z(X)}}else{if(R instanceof LayoutGroup){var P=R.parentElement;X=R;var V=W.createLayoutItem("layoutGroup",S);P.insertBefore(V,X);if(S==="top"||S==="left"){V.append(U);V.appendChild(X)}else{V.appendChild(X);V.append(U)}if(X.getAttribute("data-id")==="root"){X.setAttribute("data-id",V.getAttribute("data-id"));V.setAttribute("data-id","root");W.$.itemsContainer=V}Z(P)}else{X=W.createLayoutItem("layoutGroup",S);P.insertBefore(X,R);if(S==="top"||S==="left"){X.appendChild(U);X.appendChild(R)}else{X.appendChild(R);X.appendChild(U)}Z(X)}}W.refresh();return U}},{key:"_handleButtonsVisibility",value:function a(T){var S=this;if(!S._buttons){S._buttons=document.createElement("div");S._buttons.classList.add("jqx-layout-buttons-container");S._buttons.innerHTML='<div role="button" position="top"></div>\n                                       <div role="button" position="bottom"></div>\n                                       <div role="button" position="center"></div>\n                                       <div role="button" position="left"></div>\n                                       <div role="button" position="right"></div>'}if(!T){if(S._buttons.parentElement){S._buttons.parentElement.removeChild(S._buttons);return}}if(T){var Q=T._buttonPosition||[],R=S._buttons.children;for(var O=0;O<R.length;O++){var P=R[O];P.position=P.getAttribute("position");P.item=T;Q.length&&Q.indexOf(P.getAttribute("position"))<0?P.classList.add("jqx-hidden"):P.classList.remove("jqx-hidden");P.onmouseenter=function(){P.setAttribute("hover","")};P.onmouseleave=function(){P.removeAttribute("hover")}}if(S.allowLiveSplit&&S._buttons.parentElement!==T){T.appendChild(S._buttons)}}}},{key:"_handleDropArea",value:function f(R){var O=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"center";var Q=this;var P=function P(T){var S=50;switch(T){case"left":Q._dropArea.style.top="0px";Q._dropArea.style.left="0px";Q._dropArea.style.width=S+"%";Q._dropArea.style.height="100%";break;case"right":Q._dropArea.style.top="0px";Q._dropArea.style.left="calc(100% - ".concat(S,"%)");Q._dropArea.style.width=S+"%";Q._dropArea.style.height="100%";break;case"top":Q._dropArea.style.top="0px";Q._dropArea.style.left="0px";Q._dropArea.style.width="100%";Q._dropArea.style.height=S+"%";break;case"bottom":Q._dropArea.style.top="calc(100% - ".concat(S,"%)");Q._dropArea.style.left="0px";Q._dropArea.style.width="100%";Q._dropArea.style.height=S+"%";break;case"center":Q._dropArea.style.top="0px";Q._dropArea.style.left="0px";Q._dropArea.style.width="100%";Q._dropArea.style.height="100%";break}};if(Q._dropArea&&Q._dropArea.parentElement===R){P(O);return}if(Q._dropArea){Q._dropArea.remove()}if(!Q._dragDetails||!R){return}Q._dropArea=document.createElement("div");Q._dropArea.classList.add("jqx-layout-drop-area");R.appendChild(Q._dropArea);Q._dropArea.style.opacity=1;P(O)}},{key:"_handleSplitter",value:function m(R){var Q=this;if(!R){return}if(R.hasAttribute("tab")){R=R.parentElement}if(R._splitter){R._splitter.remove()}if(!R._splitter){R._splitter=document.createElement("div")}if(Q._dragDetails&&Q._dragDetails.dragging){R._splitter.remove();return}if(R.modifiers.indexOf("resize")===-1){return}R.appendChild(R._splitter);var P=R.parentElement;if(P){R._splitter.className="jqx-layout-splitter";R._splitter.item=R;R._splitter.removeAttribute("drag");var O=P.orientation;if(R.nextElementSibling&&R.nextElementSibling.hasAttribute("data-id")){R._splitter.classList.add(O)}else{if(R.previousElementSibling&&R.previousElementSibling.hasAttribute("data-id")){R._splitter.classList.add(O);R._splitter.classList.add("last")}}var S=function S(T){T.style.top="";T.style.left="";T.style.bottom="";T.style.right="";T.onpointerdown=function(V){var U=V.target.item;U.style.overflow="hidden";Q._resizeDetails={splitter:V.target,splitterRect:V.target.getBoundingClientRect(),itemRect:U.getBoundingClientRect(),item:U,itemSize:U.size,group:U.parentElement,clientX:V.clientX,clientY:V.clientY};if(Q._selectedItem!==U){Q.querySelectorAll("[data-id]").forEach(function(W){W.removeAttribute("selected")});Q.selectedIndex=U.getAttribute("index");U.setAttribute("selected","");Q._selectedItem=U;Q._handleButtonsVisibility(U)}if(U.previousElementSibling&&U.previousElementSibling.hasAttribute("data-id")){Q._resizeDetails.previousItemRect=U.previousElementSibling.getBoundingClientRect();Q._resizeDetails.previousItem=U.previousElementSibling;Q._resizeDetails.previousItemSize=U.previousElementSibling.size;Q._resizeDetails.previousItem.style.overflow="hidden"}else{Q._resizeDetails.previousItemRect=null;Q._resizeDetails.previousItem=null}if(U.nextElementSibling&&U.nextElementSibling.hasAttribute("data-id")){Q._resizeDetails.nextItemRect=U.nextElementSibling.getBoundingClientRect();Q._resizeDetails.nextItem=U.nextElementSibling;Q._resizeDetails.nextItemSize=U.nextElementSibling.size;Q._resizeDetails.nextItem.style.overflow="hidden"}else{Q._resizeDetails.nextItemRect=null;Q._resizeDetails.nextItem=null}}};S(R._splitter)}}}]);return i}(_wrapNativeSuper(HTMLElement));customElements.define("jqx-layout-group",LayoutGroup);customElements.define("jqx-layout-item",LayoutItem);customElements.define("jqx-tab-layout-group",TabLayoutGroup);customElements.define("jqx-tab-layout-item",TabLayoutItem);customElements.define("jqx-split-layout",SplitLayout);

