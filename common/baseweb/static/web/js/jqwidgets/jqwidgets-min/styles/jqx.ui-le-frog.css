.jqx-rc-tl-ui-le-frog{border-top-left-radius:10px; moz-border-radius-topleft:10px; webkit-border-top-left-radius:10px}
.jqx-rc-tr-ui-le-frog{border-top-right-radius:10px; moz-border-radius-topright:10px; webkit-border-top-right-radius:10px}
.jqx-rc-bl-ui-le-frog{border-bottom-left-radius:10px; moz-border-radius-bottomleft:10px; webkit-border-bottom-left-radius:10px}
.jqx-rc-br-ui-le-frog{border-bottom-right-radius:10px; moz-border-radius-bottomright:10px; webkit-border-bottom-right-radius:10px}
.jqx-rc-t-ui-le-frog{border-top-left-radius:10px; border-top-right-radius:10px; moz-border-radius-topleft:10px; moz-border-radius-topright:10px; webkit-border-top-left-radius:10px; webkit-border-top-right-radius:10px}
.jqx-rc-b-ui-le-frog{border-bottom-left-radius:10px; border-bottom-right-radius:10px; moz-border-radius-bottomleft:10px; moz-border-radius-bottomright:10px; webkit-border-bottom-left-radius:10px; webkit-border-bottom-right-radius:10px}
.jqx-rc-r-ui-le-frog{border-bottom-right-radius:10px; border-top-right-radius:10px; moz-border-radius-bottomright:10px; moz-border-radius-topright:10px; webkit-border-bottom-right-radius:10px; webkit-border-top-right-radius:10px}
.jqx-rc-l-ui-le-frog{border-bottom-left-radius:10px; border-top-left-radius:10px; moz-border-radius-bottomleft:10px; moz-border-radius-topleft:10px; webkit-border-bottom-left-radius:10px; webkit-border-top-left-radius:10px}
.jqx-rc-all-ui-le-frog{border-radius:10px; moz-border-radius:10px; webkit-border-radius:10px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-le-frog{background-position:-96px -192px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-le-frog{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;background-position:-64px -192px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-grid-column-menubutton-ui-le-frog{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); border-width:0px}
.jqx-grid-cell-ui-le-frog{background: #35650f; border-color: #72b42d; color: #fff;}
/*Tree*/
.jqx-tree-item-arrow-expand-ui-le-frog, .jqx-tree-item-arrow-expand-hover-ui-le-frog{background-position:-65px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-le-frog, .jqx-tree-item-arrow-collapse-hover-ui-le-frog{background-position:-32px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-menu-item-arrow-right-ui-le-frog, .jqx-menu-item-arrow-right-selected-ui-le-frog{background-position:-32px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-menu-item-arrow-left-ui-le-frog, .jqx-menu-item-arrow-left-selected-ui-le-frog{background-position:-96px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
/*Tabs*/
.jqx-tabs-title-ui-le-frog{border:1px solid transparent;}
.jqx-tabs-header-ui-le-frog{border-width: 1px; margin:2px; border-radius:10px; moz-border-radius:10px; webkit-border-radius:10px}
.jqx-tabs-header-bottom-ui-le-frog{margin-top:-2px !important; padding-bottom: 3px; padding-top:1px}
.jqx-tabs-selection-tracker-top-ui-le-frog{background: #2d6006;border-bottom: 0px solid transparent;}
.jqx-tabs-selection-tracker-bottom-ui-le-frog{background: #2d6006; border-top: 0px solid transparent;}
/*Radio Button*/
.jqx-radiobutton-ui-le-frog .jqx-fill-state-pressed-ui-le-frog{background:#fcfefc; border:1px solid #72b42d}
/*Calendar*/
.jqx-calendar-cell-ui-le-frog{font-size: 11px; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-le-frog{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-le-frog{border: 1px solid #72b42d; border-bottom-width: 0px; border-radius:10px; moz-border-radius:10px; webkit-border-radius:10px; font-weight: bold;}
.jqx-calendar-month-container-ui-le-frog{border:none !important}
.jqx-calendar-ui-le-frog{padding:2px}
.jqx-calendar-column-cell-ui-le-frog{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-le-frog .jqx-icon-arrow-left-ui-le-frog{background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-le-frog .jqx-icon-arrow-right-ui-le-frog{background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:5px; position:relative}

/*Icons*/
.jqx-icon-arrow-up-ui-le-frog{background-position:0 -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-down-ui-le-frog{background-position:-65px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-left-ui-le-frog{background-position:-96px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-right-ui-le-frog{background-position:-32px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-up-hover-ui-le-frog{background-position:0 -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-down-hover-ui-le-frog{background-position:-65px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-left-selected-ui-le-frog{background-position:-96px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-right-selected-ui-le-frog{background-position:-32px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-up-selected-ui-le-frog, .jqx-menu-item-arrow-up-selected-ui-le-frog, .jqx-menu-item-arrow-up-ui-le-frog, .jqx-menu-item-arrow-up-hover-ui-le-frog{background-position:0 -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-down-selected-ui-le-frog, .jqx-menu-item-arrow-down-selected-ui-le-frog, .jqx-menu-item-arrow-down-ui-le-frog, .jqx-menu-item-arrow-down-hover-ui-le-frog{background-position:-65px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-left-selected-ui-le-frog, .jqx-tree-item-arrow-collapse-rtl-ui-le-frog, .jqx-tree-item-arrow-collapse-hover-rtl-ui-le-frog{background-position:-96px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-arrow-right-selected-ui-le-frog{background-position:-32px -17px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-icon-close-ui-le-frog{background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-le-frog{background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png); background-position:-80px -128px; cursor:pointer}
/*Window*/
.jqx-window-ui-le-frog{padding: 2px;}
.jqx-window-header-ui-le-frog{border: 1px solid #72b42d; font-weight: bold; font-size: 11px; moz-border-radius:10px; border-radius:10px; webkit-border-radius:10px}
.jqx-window-content-ui-le-frog{border-width:0px !important}
.jqx-window-close-button-ui-le-frog{background-position:-96px -128px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png);moz-border-radius:10px; border-radius:10px; webkit-border-radius:10px}
.jqx-window-collapse-button-ui-le-frog{background-position:0 -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-window-collapse-button-hover-ui-le-frog{background-image:url(./images/le-frog/ui-icons_72b42d_256x240.png); border-radius:10px; moz-border-radius:10px; webkit-border-radius:10px}
.jqx-window-collapse-button-collapsed-ui-le-frog, .jqx-window-collapse-button-collapsed-hover-ui-le-frog{background-position:-65px -16px}
.jqx-window-modal-ui-le-frog{}
.jqx-window-close-button-hover-ui-le-frog{background-color:#fff; background-position:-96px -128px; background-image:url(./images/le-frog/ui-icons_72b42d_256x240.png); cursor:pointer; width:16px; height:16px}

/*Common Settings*/
.jqx-widget-ui-le-frog{font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; font-size:12px; font-style:normal;}
.jqx-widget-content-ui-le-frog{font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; border-color: #72b42d; background: #285c00 url(./images/le-frog/ui-bg_inset-soft_10_285c00_1x100.png) 50% bottom repeat-x; color: #ffffff; font-size:12px}
.jqx-widget-content-ui-le-frog a{color:#ffffff}
.jqx-widget-header-ui-le-frog{font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; border-color: #3f7506; background: #3a8104 url(./images/le-frog/ui-bg_highlight-soft_33_3a8104_1x100.png) 50% 50% repeat-x; color: #ffffff; font-size:12px}
.jqx-widget-header-ui-le-frog a{color:#ffffff}
.jqx-fill-state-normal-ui-le-frog, .jqx-calendar-cell-ui-le-frog, .jqx-tabs-title-ui-le-frog, .jqx-expander-header-ui-le-frog{border-color: #45930b; background: #4ca20b url(./images/le-frog/ui-bg_highlight-soft_60_4ca20b_1x100.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff;}
.jqx-fill-state-normal-ui-le-frog a, .jqx-fill-state-normal-ui-le-frog a:link, .jqx-fill-state-normal-ui-le-frog a:visited{color:#ffffff; text-decoration:none}
.jqx-fill-state-hover-ui-le-frog{border-color: #8bd83b; background: #4eb305 url(./images/le-frog/ui-bg_highlight-soft_50_4eb305_1x100.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff; }
.jqx-fill-state-hover-ui-le-frog a, .jqx-fill-state-hover-ui-le-frog a:hover{color:#ffffff; text-decoration:none}
.jqx-fill-state-focus-ui-le-frog {border-color: #31630b; }
.jqx-fill-state-pressed-ui-le-frog{border-color: #72b42d; background: #285c00 url(./images/le-frog/ui-bg_highlight-hard_30_285c00_1x100.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff;}
.jqx-fill-state-pressed-ui-le-frog a, .jqx-fill-state-pressed-ui-le-frog a:link, .jqx-fill-state-pressed-ui-le-frog a:visited{color:#ffffff; text-decoration:none}

.jqx-input-button-content-ui-le-frog{font-size:10px}
.jqx-input-icon-ui-le-frog{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-le-frog{margin-top:0px; background-position:-65px -147px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-le-frog .jqx-fill-state-pressed-ui-le-frog{background: #498b18; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-le-frog{background: #498b18; border-right: none; border-bottom: 1px solid #72b42d;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-le-frog, .jqx-scrollbar-thumb-state-normal-horizontal-ui-le-frog{ border: 1px solid #72b42d; background: #498b18;}
.jqx-scrollbar-thumb-state-hover-ui-le-frog, .jqx-scrollbar-thumb-state-hover-horizontal-ui-le-frog{ background: #8bd83b;}
.jqx-scrollbar-thumb-state-pressed-ui-le-frog, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-le-frog{ background: #285c00;}
.jqx-splitter-splitbar-horizontal-ui-le-frog, .jqx-splitter-splitbar-vertical-ui-le-frog{background: #72b42d;}
.jqx-splitter-collapse-button-horizontal-ui-le-frog, .jqx-splitter-collapse-button-vertical-ui-le-frog{background: #285c00;}

.jqx-tabs-title-selected-top-ui-le-frog
{
    border-bottom: 1px solid #285c00;
    background-color: #285c00;
}
/*applied to the tab's title when the tab is selected and the jqxTab's position property is set to 'bottom' .*/
.jqx-tabs-title-selected-bottom-ui-le-frog
{
    border-top: 1px solid #285c00;
    background-color: #285c00;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'top'.*/
.jqx-tabs-selection-tracker-top-ui-le-frog
{
   border-bottom: 1px solid #285c00;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'bottom'.*/
.jqx-tabs-selection-tracker-bottom-ui-le-frog
{
   border-top: 1px solid #285c00;
}
/*Slider*/
.jqx-slider-ui-le-frog .jqx-fill-state-pressed-ui-le-frog{background:#619a35;}
.jqx-slider-track-ui-le-frog{border: 1px solid #72b42d; background: #35650f;}
/*Grid*/
.jqx-grid-cell-sort-ui-le-frog, .jqx-grid-cell-filter-ui-le-frog, .jqx-grid-cell-pinned-ui-le-frog, .jqx-grid-cell-selected-ui-le-frog{background: #1c4000; color: #fcfefc;}
.jqx-grid-bottomright-ui-le-frog, .jqx-panel-bottomright-ui-le-frog, .jqx-listbox-bottomright-ui-le-frog, .jqx-scrollbar-state-normal-ui-le-frog{background: #35650f;}
.jqx-widget-ui-le-frog .jqx-grid-column-header-ui-le-frog, .jqx-grid-cell-ui-le-frog, .jqx-widget-ui-le-frog .jqx-grid-cell-ui-le-frog, .jqx-widget-ui-le-frog .jqx-grid-group-cell-ui-le-frog, .jqx-grid-group-cell-ui-le-frog{border-color:#72b42d}
.jqx-grid-group-expand-ui-le-frog{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down-white.png);}
.jqx-grid-group-collapse-ui-le-frog{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right-white.png);}
.jqx-grid-group-cell-ui-le-frog {
    background-color: transparent;
}
/*Menu*/
.jqx-menu-dropdown-ui-le-frog
{
    -moz-border-radius-bottomleft: 10px;
    -webkit-border-bottom-left-radius: 10px;
    border-bottom-left-radius: 10px;
    -moz-border-radius-topright: 10px;
    -webkit-border-top-right-radius: 10px;
    border-top-right-radius: 10px;
    -moz-border-radius-bottomright: 10px;
    -webkit-border-bottom-right-radius: 10px;
    border-bottom-right-radius: 10px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-le-frog{overflow: visible;}
.jqx-expander-header-ui-le-frog{border:1px solid transparent; border-radius:10px !important; moz-border-radius:10px !important; webkit-border-radius:10px !important}
.jqx-expander-header-hover-ui-le-frog{border:1px solid #72b42d;}
.jqx-expander-header-expanded-ui-le-frog{border-color: #72b42d; background: #285c00 url(./images/le-frog/ui-bg_highlight-hard_30_285c00_1x100.png) 50% 50% repeat-x; border:1px solid #72b42d; border-bottom-width:1px; border-top-left-radius:10px !important; border-top-right-radius:10px !important; moz-border-radius-topleft:10px !important; moz-border-radius-topright:10px !important; webkit-border-top-left-radius:10px !important; webkit-border-top-right-radius:10px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-le-frog{border-bottom-left-radius:10px !important; border-bottom-right-radius:10px !important; moz-border-radius-bottomleft:10px !important; moz-border-radius-bottomright:10px !important; webkit-border-bottom-left-radius:10px !important; webkit-border-bottom-right-radius:10px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-le-frog{background-position:-65px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-expander-arrow-bottom-ui-le-frog{background-position:0 -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-tabs-arrow-right-ui-le-frog{background-position:-32px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}
.jqx-tabs-arrow-left-ui-le-frog{background-position:-96px -16px; background-image:url(./images/le-frog/ui-icons_ffffff_256x240.png)}

/*Scroll Bar*/
.jqx-scrollbar-ui-le-frog .jqx-icon-arrow-up-ui-le-frog{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-le-frog .jqx-icon-arrow-down-ui-le-frog{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-le-frog .jqx-icon-arrow-left-ui-le-frog{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-le-frog .jqx-icon-arrow-right-ui-le-frog{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-icon-arrow-first-ui-le-frog
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-ui-le-frog
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-le-frog>.jqx-grid-group-expand-ui-le-frog {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-le-frog>.jqx-grid-group-collapse-ui-le-frog {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-le-frog>.jqx-grid-group-collapse-rtl-ui-le-frog {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-le-frog>.jqx-grid-group-expand-rtl-ui-le-frog {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-ui-le-frog
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-ui-le-frog, .jqx-icon-calendar-hover-ui-le-frog, .jqx-icon-calendar-pressed-ui-le-frog {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-ui-le-frog, .jqx-icon-time-hover-ui-le-frog, .jqx-icon-time-pressed-ui-le-frog {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-delete-ui-le-frog
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-ui-le-frog
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-ui-le-frog
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-ui-le-frog
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-ui-le-frog
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-ui-le-frog
{
    background-image: url(./images/plus_white.png);
}
.jqx-menu-minimized-button-ui-le-frog {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-ui-le-frog {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-ui-le-frog
{
    background-color: #3f7506;
}
.jqx-layout-pseudo-window-pin-icon-ui-le-frog
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-le-frog
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-ui-le-frog, .jqx-scheduler-time-column-ui-le-frog, .jqx-scheduler-toolbar-ui-le-frog
{
    background: #3f7506 !important;
    color: #fff  !important;
}
.jqx-widget-ui-le-frog .jqx-scheduler-middle-cell-ui-le-frog, .jqx-scheduler-middle-cell-ui-le-frog {
    border-bottom-color: #3f7506 !important;
}
.jqx-docking-layout-group-floating-ui-le-frog .jqx-window-header-ui-le-frog
{
    background-image: none;
}

 .jqx-grid-column-menubutton-ui-le-frog {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
 }

.jqx-item-ui-le-frog .jqx-grid-sortasc-icon
 {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-ui-le-frog .jqx-grid-sortdesc-icon
 {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the grid menu's sort remove item/*/
.jqx-item-ui-le-frog .jqx-grid-sortremove-icon
 {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
/*applied to the timepicker*/
.jqx-label-ui-le-frog {
	fill: white;
	opacity: 0.5;
}
.jqx-needle-ui-le-frog {
	fill: rgb(40, 92, 0);
}
.jqx-needle-central-circle-ui-le-frog {
	fill: rgb(83, 163, 21);
	stroke: rgb(40, 92, 0);
	stroke-width: 1.5;
}
.jqx-needle-central-circle-ui-le-frog:first-of-type {
	fill: rgb(40, 92, 0);
}
.jqx-container-ui-le-frog {
	border-radius: inherit;
}
.jqx-header-ui-le-frog {
	border-top-left-radius: inherit;
	border-top-right-radius: inherit;
}
.jqx-time-picker .jqx-header .jqx-selected-ui-le-frog:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
}
.jqx-svg-picker-ui-le-frog {
	background-image: linear-gradient(rgb(63, 117, 6) 5%, rgb(83, 155, 27) 35%, rgb(58, 129, 4), rgb(40, 92, 0) 97%);
}
.jqx-widget-content-ui-le-frog {
	background: none;
}
.jqx-svg-picker-ui-le-frog:focus {
	border: 2px solid rgb(51, 94, 5) !important;
}

.jqx-split-layout-component-ui-le-frog .jqx-split-layout {
    --jqx-primary-rgb: 63, 117, 6;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-primary-color: #fff;
    --jqx-background: #aaa;
    --jqx-surface: #aaa;
    color: #E7E7E7;
	background: var(--jqx-background);
}


.jqx-grid-pager-ui-le-frog .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-le-frog .jqx-icon-arrow-left-ui-le-frog{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-le-frog .jqx-icon-arrow-right-ui-le-frog{
	position: relative;
    top: 6px;
}