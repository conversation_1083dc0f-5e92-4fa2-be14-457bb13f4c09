﻿/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-material-green {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-material-green {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-material-green {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-material-green {
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*top rounded Corners*/
.jqx-rc-t-material-green {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom rounded Corners*/
.jqx-rc-b-material-green {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*right rounded Corners*/
.jqx-rc-r-material-green {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*left rounded Corners*/
.jqx-rc-l-material-green {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*all rounded Corners*/
.jqx-rc-all-material-green {
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

.jqx-widget-material-green, .jqx-widget-header-material-green, .jqx-fill-state-normal-material-green,
.jqx-widget-content-material-green, .jqx-fill-state-hover-material-green, .jqx-fill-state-pressed-material-green {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
}

.jqx-widget-material-green {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #555;
}

.jqx-widget-content-material-green {
      font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #555;
    background-color: #ffffff;
    border-color: #E0E0E0;
}

.jqx-widget-header-material-green {
    background-color: #fff;
    border-color: #E0E0E0;
    color: #757575;
    font-weight: 500;
    *zoom: 1;
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background: #fff;
    color: #757575;
    font-size: 14px;
}

.jqx-fill-state-normal-material-green {
    border-color: #558B2F;
    *zoom: 1;
    background: #fafafa;
}
.jqx-widget-material-green input::selection, input.jqx-input-widget-material-green::selection, .jqx-widget-content-material-green input::selection {
    background: #558B2F;
    color: #fff;
}
.jqx-toolbar-material-green{
     border-color: #e0e0e0;
}

.jqx-button-material-green, jqx-button-material-green.jqx-fill-state-normal-material-green {
    color: #fff;
    background: #558B2F;
    border-color: #558B2F;
    *zoom: 1;
    outline: none;
    transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
}
.jqx-button-material-green button, jqx-button-material-green input {
    background: transparent;
    color: inherit;
    border:none;
    outline: none;
}

.jqx-slider-button-material-green {
    padding:3px;
    background: transparent;
    border:transparent;
}
    .jqx-button-material-green.float {
        border-radius: 100%;
        min-height: 48px;
        min-width: 48px;
        width: 48px;
        height: 48px;
        max-height: 48px;
        max-width:48px;
    }

    .jqx-button-material-green.outlined {
        background: transparent;
        color: #558B2F;
        border-width: 2px;
    }

    .jqx-button-material-green.flat {
        background: transparent;
        color: #558B2F;
        border: none;
    }

.jqx-fill-state-hover-material-green {
}

.jqx-fill-state-pressed-material-green {
}

.jqx-fill-state-hover-material-green, .jqx-fill-state-focus-material-green {
    text-decoration: none;
}

 .jqx-expander-header.jqx-fill-state-hover-material-green,
 .jqx-expander-header.jqx-fill-state-normal-material-green,
 .jqx-expander-header.jqx-fill-state-pressed-material-green
 {
      background: #fff;
      border-color: #e0e0e0;
}
.jqx-expander-header.jqx-fill-state-hover-material-green {
    background: #F5F5F5;
}

.jqx-expander-header-material-green {
    padding:10px;
}
.jqx-button-material-green.jqx-fill-state-hover {
    opacity: 0.9;
    cursor: pointer;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
    *zoom: 1;
}

    .jqx-button-material-green.jqx-fill-state-hover.outlined,
    .jqx-button-material-green.jqx-fill-state-hover.flat {
        color: #558B2F;
        box-shadow: none;
    }

.jqx-button-material-green.jqx-fill-state-pressed {
    cursor: pointer;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

    .jqx-button-material-green.jqx-fill-state-pressed.float {
        box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
    }

    .jqx-slider-button-material-green.jqx-fill-state-pressed-material-green,
    .jqx-button-material-green.jqx-fill-state-pressed.outlined,
    .jqx-button-material-green.jqx-fill-state-pressed.flat {
        background: rgba(99,0,238,0.15);
        box-shadow: none;
    }

.jqx-button-material-green.jqx-fill-state-focus {
    background: #55882F;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
  .jqx-slider-button-material-green.jqx-fill-state-focus-material-green {
      background: transparent;
      border-color: transparent;
      box-shadow:none;
  }
  
    .jqx-button-material-green.jqx-fill-state-focus.outlined,
    .jqx-button-material-green.jqx-fill-state-focus.flat {
        box-shadow: none;
        background: rgba(99,0,238,0.15);
        color: #55882F;
    }

.jqx-dropdownlist-state-normal-material-green, .jqx-dropdownlist-state-hover-material-green, .jqx-dropdownlist-state-selected-material-green,
.jqx-scrollbar-button-state-hover-material-green, .jqx-scrollbar-button-state-normal-material-green, .jqx-scrollbar-button-state-pressed-material-green,
.jqx-scrollbar-thumb-state-normal-horizontal-material-green, .jqx-scrollbar-thumb-state-hover-horizontal-material-green, .jqx-scrollbar-thumb-state-pressed-horizontal-material-green,
.jqx-scrollbar-thumb-state-normal-material-green, .jqx-scrollbar-thumb-state-pressed-material-green, .jqx-tree-item-hover-material-green, .jqx-tree-item-selected-material-green,
.jqx-tree-item-material-green, .jqx-menu-item-material-green, .jqx-menu-item-hover-material-green, .jqx-menu-item-selected-material-green, .jqx-menu-item-top-material-green, .jqx-menu-item-top-hover-material-green,
.jqx-menu-item-top-selected-material-green, .jqx-slider-button-material-green, .jqx-slider-slider-material-green {
    -webkit-transition: background-color 100ms linear;
    -moz-transition: background-color 100ms linear;
    -o-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}


.jqx-primary-material-green.jqx-input-label-material-green {
   color: #1ca8dd !important;
}
.jqx-primary-material-green.jqx-input-bar-material-green:before {
   background: #1ca8dd !important;
}
.jqx-success-material-green.jqx-input-label-material-green {
   color: #5cb85c !important;
}
.jqx-success-material-green.jqx-input-bar-material-green:before {
   background: #5cb85c !important;
}
.jqx-inverse-material-green.jqx-input-label-material-green {
   color: #666 !important;
}
.jqx-inverse-material-green.jqx-input-bar-material-green:before {
   background: #666 !important;
}
.jqx-danger-material-green.jqx-input-label-material-green {
   color: #d9534f !important;
}
.jqx-danger-material-green.jqx-input-bar-material-green:before {
   background: #d9534f !important;
}
.jqx-warning-material-green.jqx-input-label-material-green {
   color: #f0ad4e !important;
}
.jqx-warning-material-green.jqx-input-bar-material-green:before {
   background: #f0ad4e !important;
}
.jqx-info-material-green.jqx-input-label-material-green {
   color: #5bc0de !important;
}
.jqx-info-material-green.jqx-input-bar-material-green:before {
   background: #5bc0de !important;
}

.jqx-primary-material-green {
    color: #1ca8dd !important;
    background: #fff !important;
    border-color: #1ca8dd !important;
    text-shadow: none !important;
}

    .jqx-primary-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-primary-material-green.jqx-slider-button-material-green,
    .jqx-primary-material-green.jqx-slider-slider-material-green,
    .jqx-primary-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-primary-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-primary-material-green.jqx-action-button-material-green,
    .jqx-primary-material-green:hover,
    .jqx-primary-material-green:focus,
    .jqx-primary-material-green:active,
    .jqx-primary-material-green.active,
    .jqx-primary-material-green.disabled,
    .jqx-primary-material-green[disabled] {
        color: #fff !important;
        background: #1ca8dd !important;
        border-color: #1ca8dd !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-primary-material-green,
    .jqx-primary-material-green:active,
    .jqx-primary-material-green.active {
        color: #fff !important;
        background-color: #1ca8dd !important;
        border-color: #1ca8dd !important;
        text-shadow: none !important;
    }

.jqx-success-material-green {
    color: #5cb85c !important;
    background: #fff !important;
    border-color: #5cb85c !important;
    text-shadow: none !important;
}

    .jqx-success-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-success-material-green.jqx-slider-button-material-green,
    .jqx-success-material-green.jqx-slider-slider-material-green,
    .jqx-success-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-success-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-success-material-green.jqx-action-button-material-green,
    .jqx-success-material-green:hover,
    .jqx-success-material-green:focus,
    .jqx-success-material-green:active,
    .jqx-success-material-green.active,
    .jqx-success-material-green.disabled,
    .jqx-success-material-green[disabled] {
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-success-material-green,
    .jqx-success-material-green:active,
    .jqx-success-material-green.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
    }

.jqx-inverse-material-green {
    text-shadow: none !important;
    color: #666 !important;
    background: #fff !important;
    border-color: #cccccc !important;
}

    .jqx-inverse-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-inverse-material-green.jqx-slider-button-material-green,
    .jqx-inverse-material-green.jqx-slider-slider-material-green,
    .jqx-inverse-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-inverse-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-inverse-material-green.jqx-action-button-material-green,
    .jqx-inverse-material-green:hover,
    .jqx-inverse-material-green:focus,
    .jqx-inverse-material-green:active,
    .jqx-inverse-material-green.active,
    .jqx-inverse-material-green.disabled,
    .jqx-inverse-material-green[disabled] {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-inverse-material-green,
    .jqx-inverse-material-green:active,
    .jqx-inverse-material-green.active {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }


.jqx-danger-material-green {
    text-shadow: none !important;
    color: #d9534f !important;
    background: #fff !important;
    border-color: #d9534f !important;
}

    .jqx-danger-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-danger-material-green.jqx-slider-button-material-green,
    .jqx-danger-material-green.jqx-slider-slider-material-green,
    .jqx-danger-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-danger-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-danger-material-green.jqx-action-button-material-green,
    .jqx-danger-material-green:hover,
    .jqx-danger-material-green:focus,
    .jqx-danger-material-green:active,
    .jqx-danger-material-green.active,
    .jqx-danger-material-green.disabled,
    .jqx-danger-material-green[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-danger-material-green,
    .jqx-danger-material-green:active,
    .jqx-danger-material-green.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

.jqx-validator-error-label-material-green {
    color: #d9534f !important;
}

.jqx-warning-material-green {
    text-shadow: none !important;
    color: #f0ad4e !important;
    background: #fff !important;
    border-color: #f0ad4e !important;
}

    .jqx-warning-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-warning-material-green.jqx-slider-button-material-green,
    .jqx-warning-material-green.jqx-slider-slider-material-green,
    .jqx-warning-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-warning-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-warning-material-green.jqx-action-button-material-green,
    .jqx-warning-material-green:hover,
    .jqx-warning-material-green:focus,
    .jqx-warning-material-green:active,
    .jqx-warning-material-green.active,
    .jqx-warning-material-green.disabled,
    .jqx-warning-material-green[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-warning-material-green,
    .jqx-warning-material-green:active,
    .jqx-warning-material-green.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }


.jqx-info-material-green {
    text-shadow: none !important;
    color: #5bc0de !important;
    background: #fff !important;
    border-color: #5bc0de !important;
}

    .jqx-info-material-green.jqx-dropdownlist-state-normal-material-green,
    .jqx-info-material-green.jqx-slider-button-material-green,
    .jqx-info-material-green.jqx-slider-slider-material-green,
    .jqx-info-material-green.jqx-combobox-arrow-hover-material-green,
    .jqx-info-material-green.jqx-combobox-arrow-normal-material-green,
    .jqx-info-material-green.jqx-action-button-material-green,
    .jqx-info-material-green:hover,
    .jqx-info-material-green:focus,
    .jqx-info-material-green:active,
    .jqx-info-material-green.active,
    .jqx-info-material-green.disabled,
    .jqx-info-material-green[disabled] {
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-material-green.jqx-info-material-green,
    .jqx-info-material-green:active,
    .jqx-info-material-green.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
    }


.jqx-slider-tooltip-material-green.jqx-primary-slider, .jqx-slider-tooltip-material-green.jqx-primary-slider .jqx-fill-state-normal-material-green {
    border-color: #1ca8dd;
    background: #1ca8dd;
}
.jqx-slider-tooltip-material-green.jqx-success-slider, .jqx-slider-tooltip-material-green.jqx-success-slider .jqx-fill-state-normal-material-green {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-material-green.jqx-inverse-slider, .jqx-slider-tooltip-material-green.jqx-inverse-slider .jqx-fill-state-normal-material-green {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-material-green.jqx-danger-slider, .jqx-slider-tooltip-material-green.jqx-danger-slider .jqx-fill-state-normal-material-green {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-material-green.jqx-warning-slider, .jqx-slider-tooltip-material-green.jqx-warning-slider .jqx-fill-state-normal-material-green {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-material-green.jqx-info-slider, .jqx-slider-tooltip-material-green.jqx-info-slider .jqx-fill-state-normal-material-green {
    border-color: #5bc0de;
    background: #5bc0de;
}


.jqx-fill-state-pressed-material-green {
    background-image: none;
    outline: 0;
}

.jqx-grid-group-column-material-green {
    border-color: transparent;
}
.jqx-grid-column-menubutton-material-green {
    border-width: 0px;
}
.jqx-grid-groups-row-material-green > span {
    padding-left: 4px;
}

.jqx-grid-cell-material-green {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-pager-top-material-green .jqx-button-material-green,
.jqx-grid-pager-material-green .jqx-button-material-green {
    color: rgba(0,0,0,.54) !important;
    background-color: transparent;
    border-color: transparent;
    top: -4px;
    position: relative;
    height:30px !important;
    width:30px !important;
    border-radius:50%;

}

.jqx-grid-pager-input-material-green  {
    padding:0px !important;
}

.jqx-grid-pager-top-material-green .jqx-button-material-green > div,
.jqx-grid-pager-material-green .jqx-button-material-green > div {
    top: 3px;
    position: relative;
    left: 3px;
}

.jqx-grid-pager-top-material-green .jqx-button-material-green.jqx-fill-state-hover,
.jqx-grid-pager-top-material-green .jqx-button-material-green.jqx-fill-state-pressed,
.jqx-grid-pager-material-green .jqx-button-material-green.jqx-fill-state-hover,
.jqx-grid-pager-material-green .jqx-button-material-green.jqx-fill-state-pressed
{
    color: rgba(0,0,0,.54) !important;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;    
}

.jqx-grid-pager-top-material-green .jqx-grid-pager-number-material-green,
.jqx-grid-pager-material-green .jqx-grid-pager-number-material-green {

    background-color: transparent;
    border-color: transparent;
    color: rgba(0,0,0,.54) !important;
    font-size:12px;
}

.jqx-grid-pager-top-material-green .jqx-grid-pager-number-material-green:hover,
.jqx-grid-pager-material-green .jqx-grid-pager-number-material-green:hover {
    font-size:12px;
}

.jqx-grid-pager-top-material-green .jqx-grid-pager-number-material-green.jqx-fill-state-pressed-material-green ,
.jqx-grid-pager-material-green .jqx-grid-pager-number-material-green.jqx-fill-state-pressed-material-green {
    color: #558B2F !important;
    font-weight: bold !important;
}

.jqx-grid-column-menubutton-material-green {
    background-color: transparent;
    border-color: #E0E0E0 !important;
}

.jqx-cell-material-green {
    font-size: 14px;
}

.jqx-calendar-material-green > div {
    padding: 10px;
    box-sizing: border-box;
}
.jqx-calendar-row-header-material-green, .jqx-calendar-top-left-header-material-green {
    background-color: #f0f0f0;
    border: 0px solid #f2f2f2;
}

.jqx-calendar-column-header-material-green {
    background-color: #FFF;
    border-top: 1px solid #FFF;
    border-bottom: 1px solid #e9e9e9;
}

.jqx-expander-header-material-green {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jqx-ribbon-header-vertical-material-green, .jqx-widget-header-vertical-material-green {
    background: #fff;
}

.jqx-scrollbar-state-normal-material-green {
    background-color: #f5f5f5;
    border: 1px solid #f5f5f5;
    border-left-color: #ddd;
}

.jqx-scrollbar-thumb-state-normal-material-green, .jqx-scrollbar-thumb-state-normal-horizontal-material-green {
    background: #f5f5f5;
    border-color: #b3b3b3;
}

.jqx-scrollbar-thumb-state-hover-material-green, .jqx-scrollbar-thumb-state-hover-horizontal-material-green {
    background: #e6e6e6;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-material-green {
    background: #f7f7f7 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-material-green, .jqx-splitter-collapse-button-horizontal-material-green {
    background: #558B2F;
}

.jqx-splitter-collapse-button-vertical-material-green, .jqx-progressbar-value-vertical-material-green {
    background: #558B2F;
}


.jqx-scrollbar-thumb-state-pressed-material-green, .jqx-splitter-splitbar-vertical-material-green, .jqx-splitter-splitbar-horizontal-material-green, .jqx-scrollbar-thumb-state-pressed-horizontal-material-green,
.jqx-scrollbar-button-state-pressed-material-green {
    background: #d9d9d9;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-grid-column-sortdescbutton-material-green, jqx-grid-column-filterbutton-material-green, .jqx-grid-column-sortascbutton-material-green {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #E0E0E0;
}

.jqx-menu-vertical-material-green {
    background: #ffffff;
    filter: none;
}

.jqx-grid-bottomright-material-green, .jqx-panel-bottomright-material-green, .jqx-listbox-bottomright-material-green {
    background-color: #fafafa;
}

.jqx-window-material-green, .jqx-tooltip-material-green {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}
.jqx-tooltip-material-green, .jqx-tooltip-material-green.jqx-popup-material-green, .jqx-tooltip-material-green .jqx-fill-state-normal-material-green {
    background: #6F6F6F;
    border-color: #6F6F6F;
    box-shadow:none;
    color: #fff;
}
.jqx-docking-material-green .jqx-window-material-green {
    box-shadow: none;
}

.jqx-docking-panel-material-green .jqx-window-material-green {
    box-shadow: none;
}

.jqx-checkbox-material-green {
    line-height:20px;
    overflow: visible;
}
.jqx-radiobutton-material-green {
    overflow: visible;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
    line-height:20px;
}

.jqx-radiobutton-material-green-material-green, .jqx-radiobutton-hover-material-green {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
    transition: background-color ease-in .3s;
}

.jqx-radiobutton-check-checked-material-green {
    filter: none;
    background: #fff;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-material-green {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-material-green {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-checkbox-default-material-green,
.jqx-radiobutton-default-material-green
 {
    border-width: 1px;
    border-color: #E0E0E0;
    background-color: #fff;
    overflow: visible;
}

.jqx-tree-material-green .jqx-checkbox-material-green .jqx-checkbox-default-material-green,
.jqx-checkbox-material-green[checked] .jqx-checkbox-default-material-green,
.jqx-tree-grid-checkbox[checked].jqx-checkbox-default-material-green,
.jqx-radiobutton-material-green[checked] .jqx-radiobutton-default-material-green
 {
    background-color: #558B2F;
    border-color: #558B2F;
}

.jqx-checkbox-check-checked-material-green {
    background: transparent url(./images/material_check_white.png) center center no-repeat;
}
.jqx-checkbox-check-indeterminate-material-green {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: white;
}
.jqx-tree-material-green .jqx-checkbox-check-indeterminate-material-green {
    width:12px !important;
    height:12px !important;
    top: 2px;
    left:2px;
}
.jqx-checkbox-hover-material-green,
.jqx-radiobutton-hover-material-green {
    background-color: #558B2F;
    border-color: #558B2F;
}


.jqx-slider-slider-material-green {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-slider-material-green:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}
.jqx-slider-material-green[discrete] .jqx-slider-slider-material-green:active
 {
    transform: scaleX(0);
    
}
.jqx-slider-slider-horizontal-material-green {
    background: #558B2F; 
}
.jqx-slider-slider-vertical-material-green {
    background: #558B2F; 
}
.jqx-slider-tooltip-material-green {
    width: 25px;
    height: 25px;
    transform-origin: 50% 100%;
    border-radius: 50%;
    transform: scale(0) rotate(45deg);
    padding:0px;
    background: transparent !important;
}
.jqx-slider-tooltip-material-green.init {
     transform: scale(1) rotate(45deg);
}
.jqx-slider-tooltip-material-green.hide {
     transition: transform 0.2s ease;
     transform-origin:50% 100%;
     transform: scale(0) rotate(45deg); 
}
.jqx-slider-tooltip-material-green.show {
     transition: transform 0.2s ease;
     transform: scale(1) rotate(45deg); 
}


.jqx-slider-tooltip-material-green .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-material-green .jqx-tooltip-arrow-l-r {
    display:none;
    visibility:hidden;
}

.jqx-slider-tooltip-material-green, .jqx-slider-tooltip-material-green .jqx-fill-state-normal-material-green {
    border-radius: 15px 15px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #558B2F; 
    color: #fff;
    font-size:11px;
}
.jqx-slider-tooltip-material-green.far, .jqx-slider-tooltip-material-green.far .jqx-fill-state-normal-material-green {
   border-radius: 0px 15px 15px 15px; 
}
.jqx-slider-tooltip-material-green.vertical, .jqx-slider-tooltip-material-green.vertical .jqx-fill-state-normal-material-green {
   border-radius: 15px 0px 15px 15px; 
}
.jqx-slider-tooltip-material-green.vertical.far, .jqx-slider-tooltip-material-green.vertical.far .jqx-fill-state-normal-material-green {
   border-radius: 15px 15px 15px 0px; 
}
.jqx-slider-tooltip-material-green {
    background:transparent;
    border:none !important;
    box-shadow:none;
}
.jqx-slider-tooltip-material-green .jqx-tooltip-main-material-green {
    top: -7px;
    right: 11px;
}
.jqx-slider-tooltip-material-green.far .jqx-tooltip-main-material-green {
    top: 3px;
    right: 4px;
}
.jqx-slider-tooltip-material-green.vertical .jqx-tooltip-main-material-green {
    top: -3px;
    right: 3px;
}
.jqx-slider-tooltip-material-green .jqx-tooltip-text {
    background: transparent;
    border:none;
    padding: 0px;
    overflow:visible;
}
.jqx-slider-tooltip-material-green .jqx-tooltip-text>span {
     transform: rotate(-45deg);
}
.jqx-slider-tooltip-material-green.range {
    width: 35px;
    height:35px;
}

.jqx-slider-rangebar-material-green {
    border-color: #558B2F;
    background: #558B2F;
}

.jqx-slider-track-horizontal-material-green, .jqx-slider-track-vertical-material-green {
    border-color: #E0E0E0;
    background: #f0f0f0;
}

.jqx-slider-button-material-green {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-button-material-green.jqx-fill-state-normal-material-green,
.jqx-slider-button-material-green.jqx-fill-state-hover-material-green,
.jqx-slider-button-material-green.jqx-fill-state-pressed-material-green 
{
    background: transparent !important;
}

.jqx-listitem-state-hover-material-green,
.jqx-listitem-state-selected-material-green,
.jqx-listitem-state-normal-material-green {
    padding: 5px;
}

.jqx-scheduler-edit-dialog-label-material-green {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;

}
.jqx-scheduler-edit-dialog-field-material-green {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-label-rtl-material-green {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-field-rtl-material-green {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-material-green, .jqx-menu-item-hover-material-green, .jqx-tree-item-hover-material-green, .jqx-calendar-cell-hover-material-green, .jqx-grid-cell-hover-material-green,
.jqx-menu-vertical-material-green .jqx-menu-item-top-hover-material-green, .jqx-input-popup-material-green .jqx-fill-state-hover-material-green,
.jqx-input-popup-material-green .jqx-fill-state-pressed-material-green {
    color: #333 !important;
    border-color: #F5F5F5;
    text-decoration: none;
    background-color: #F5F5F5;
    background-repeat: repeat-x;
    outline: 0;
    background: #F5F5F5; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}

.jqx-scheduler-cell-hover-material-green {
    border-color: #F5F5F5 !important;
    background: #F5F5F5 !important;
}



.jqx-listitem-state-selected-material-green, .jqx-menu-item-selected-material-green, .jqx-tree-item-selected-material-green, .jqx-calendar-cell-selected-material-green, .jqx-grid-cell-selected-material-green,
.jqx-menu-vertical-material-green .jqx-menu-item-top-selected-material-green, .jqx-grid-selectionarea-material-green, .jqx-input-button-header-material-green, .jqx-input-button-innerHeader-material-green {
    color: #558B2F !important;
    border-color: rgba(85,139,47,0.2) !important;
    background: rgba(85,139,47,0.2);
    box-shadow: none;
}

.jqx-scheduler-cell-selected-material-green {
      border-color: rgba(85,139,47,0.2) !important;
    background: rgba(85,139,47,0.2) !important;
 
}

.jqx-grid-cell-material-green .jqx-button-material-green, .jqx-grid-cell-material-green .jqx-button-material-green.jqx-fill-state-hover-material-green, .jqx-grid-cell-material-green .jqx-button-material-green.jqx-fill-state-pressed-material-green {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.jqx-popup-material-green {
    border: 1px solid #E0E0E0;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}

.jqx-grid-column-sortascbutton-material-green, .jqx-expander-arrow-bottom-material-green, .jqx-window-collapse-button-material-green, .jqx-menu-item-arrow-up-material-green, .jqx-menu-item-arrow-up-selected-material-green, .jqx-menu-item-arrow-top-up-material-green, .jqx-icon-arrow-up-material-green, .jqx-icon-arrow-up-hover-material-green, .jqx-icon-arrow-up-selected-material-green {
    background-image: url('images/material-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-material-green .jqx-grid-group-expand-material-green, .jqx-grid-group-expand-material-green, .jqx-grid-column-menubutton-material-green, .jqx-grid-column-sortdescbutton-material-green, .jqx-expander-arrow-top-material-green, .jqx-window-collapse-button-collapsed-material-green, .jqx-menu-item-arrow-down-material-green, .jqx-menu-item-arrow-down-selected-material-green, .jqx-menu-item-arrow-down-material-green, .jqx-icon-arrow-down-material-green, .jqx-icon-arrow-down-hover-material-green, .jqx-icon-arrow-down-selected-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-material-green, .jqx-menu-item-arrow-left-selected-material-green, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-material-green, .jqx-icon-arrow-down-left-material-green, .jqx-icon-arrow-left-selected-material-green {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-material-green .jqx-grid-group-collapse-material-green, .jqx-grid-group-collapse-material-green, .jqx-tabs-arrow-right-material-green, .jqx-menu-item-arrow-right-selected-material-green, .jqx-menu-item-arrow-top-right-material-green, .jqx-icon-arrow-right-material-green, .jqx-icon-arrow-right-hover-material-green, .jqx-icon-arrow-right-selected-material-green {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tree-item-arrow-collapse-rtl-material-green, .jqx-tree-item-arrow-collapse-hover-rtl-material-green {
    background-image: url(./images/material-icon-left.png);
}

.jqx-menu-item-arrow-left-selected-material-green {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-material-green {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-material-green {
    font-size: 10px;
}

.jqx-widget .jqx-grid-column-header-cell-material-green {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-row-cell-material-green {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-cell, .jqx-widget .jqx-grid-column-header, .jqx-widget .jqx-grid-group-cell {
    border-color: #E0E0E0;
}

.jqx-combobox-material-green .jqx-icon-close-material-green {
    background-image:url(./images/close_white.png)
}
.jqx-combobox-material-green, .jqx-input-material-green {
    border-color: #E0E0E0;
    color: #555555;
    background-color: #ffffff;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-material-green,
.jqx-datetimeinput-content-material-green
 {
    border-color: transparent;
}
.jqx-combobox-arrow-normal-material-green {
    background: #fff;
    border-color: transparent;
}

.jqx-combobox-content-focus-material-green, 
.jqx-combobox-state-focus-material-green,
.jqx-numberinput-focus-material-green {
    outline: none;
}

.jqx-input-group-material-green {
    position: relative;
    display: inline-block;
    overflow: visible;
    border: none;
    box-shadow: none;
}

    .jqx-input-group-material-green input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
    .jqx-input-group-material-green textarea {
        width: 100%;
        height: 100%;
        outline: none;
        resize: none;
        border-left: none;
        border-right: none;
        border-top: none;
        border-bottom-color: #e0e0e0;
    }
.jqx-numberinput-material-green,
.jqx-maskedinput-material-green
 {
    position:relative;
}
.jqx-numberinput-material-green input {
    height:100% !important;
}
.jqx-input-material-green.jqx-validator-error-element {
    border-color: transparent !important;
    border-bottom: 1px solid #df2227 !important; 
}
.jqx-input-material-green input,
.jqx-dropdownlist-state-normal-material-green,
.jqx-combobox-state-normal-material-green,
.jqx-datetimeinput-material-green,
.jqx-numberinput-material-green,
.jqx-maskedinput-material-green
 {
    background: #fff;
    border-color: #fff;
    border-radius: 0;
    box-shadow: none;
    border-bottom: 1px solid #E0E0E0;
    outline: none;
}
.jqx-datetimeinput-material-green .jqx-action-button-material-green, 
.jqx-datetimeinput-material-green .jqx-action-button-rtl-material-green 
{
    background-color: transparent;
    border-color: transparent;
}
    .jqx-datetimeinput-material-green, .jqx-datetimeinput-material-green > div,
    .jqx-numberinput-material-green, .jqx-numberinput-material-green > div,
    .jqx-maskedinput-material-green, .jqx-maskedinput-material-green > div,
    .jqx-dropdownlist-state-normal-material-green, .jqx-dropdownlist-state-normal-material-green > div, .jqx-dropdownlist-state-normal-material-green > div > div,
    .jqx-combobox-state-normal-material-green, .jqx-combobox-state-normal-material-green > div, .jqx-combobox-state-normal-material-green > div > div {
        overflow: visible !important;
    }

    .jqx-input-material-green input:focus {
        border-radius: 0;
        box-shadow: none;
    }

.jqx-input-material-green input, input[type="text"].jqx-input-material-green, input[type="password"].jqx-input-material-green, input[type="text"].jqx-widget-content-material-green, input[type="textarea"].jqx-widget-content-material-green, textarea.jqx-input-material-green {
    font-size: 14px;
    resize: none;
    background: #fff;
    border: none;
    border-radius: 0;
    box-sizing:border-box;
    box-shadow: none;
    border-bottom: 1px solid #E0E0E0;
}
.jqx-input-label {
    visibility:inherit;
}
.jqx-input-bar{
    visibility:inherit;
}
input:focus ~ .jqx-input-label-material-green,
textarea:focus ~ .jqx-input-label-material-green,
.jqx-input-widget-material-green[hint=true] .jqx-input-label,
.jqx-text-area-material-green[hint=true] .jqx-input-label,
.jqx-dropdownlist-state-selected-material-green .jqx-input-label,
.jqx-dropdownlist-state-normal-material-green[hint=true] .jqx-input-label,
.jqx-combobox-state-normal-material-green[hint=true] .jqx-input-label,
.jqx-combobox-material-green .jqx-input-label.focused,
.jqx-dropdownlist-material-green .jqx-input-label.focused,
.jqx-dropdownbutton-material-green .jqx-input-label.focused,
.jqx-datetimeinput-material-green[hint=true] .jqx-input-label,
.jqx-maskedinput-material-green[hint=true] .jqx-input-label,
.jqx-numberinput-material-green[hint=true] .jqx-input-label,
.jqx-formattedinput-material-green[hint=true] .jqx-input-label
 {
    top: -15px;
    font-size: 12px;
    color: #558B2F;
}
.jqx-dropdownlist-material-green[default-placeholder="true"] .jqx-input-label {
    visibility: hidden;
}
input:focus ~ .jqx-input-bar-material-green:before,
textarea:focus ~ .jqx-input-bar-material-green:before,
.jqx-dropdownlist-state-selected-material-green .jqx-input-bar:before,
.jqx-dropdownlist-material-green .jqx-input-bar.focused:before,
.jqx-dropdownbutton-material-green .jqx-input-bar.focused:before,
.jqx-complex-input-group-material-green .jqx-input-bar.focused::before,
.jqx-combobox-material-green .jqx-input-bar.focused:before,
.jqx-combobox-state-selected-material-green .jqx-input-bar:before {
    width: 100%;
}
.jqx-complex-input-group-material-green .jqx-fill-state-normal-material-green {
    border-color: #fafafa;
}
input[type="password"] {
    letter-spacing: 0.3em;
}

.jqx-input-widget-material-green.jqx-rtl > input {
    direction: rtl
}

.jqx-input-label-material-green {
    color: #E0E0E0;
    font-size: 14px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 2px;
    top:10px;
    top: calc(50% - 7px);
    transition: 300ms ease all;
}
.jqx-input-label.initial {
    transition: none;
}
.jqx-input-bar {
    position: relative;
    display: block;
    z-index:1;
}

    .jqx-input-bar-material-green:before {
        content: '';
        height: 2px;
        width: 0;
        bottom: 0px;
        position: absolute;
        background: #558B2F;
        transition: 300ms ease all;
        left: 0%;
    }
.jqx-formatted-input-spin-button-material-green, .jqx-input-group-addon-material-green {
    border-color: #fff;
    background: #fff;
}
.jqx-dropdownlist-state-selected-material-green,
.jqx-combobox-state-selected-material-green {
    color: #558B2F;
}


.jqx-dropdownlist-state-normal-material-green .jqx-icon-arrow-down-material-green,
.jqx-combobox-state-normal-material-green .jqx-icon-arrow-down-material-green,
.sorticon.descending .jqx-grid-column-sorticon-material-green,
.jqx-tree-item-arrow-expand-material-green,
 .jqx-expander-header-material-green .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-material-green .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
    background-image: url('images/material-icon-down.png');
}

.jqx-tree-item-arrow-collapse-material-green
{
    transform: rotate(-90deg);
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-material-green .jqx-icon-arrow-down-material-green,
.jqx-combobox-state-selected-material-green .jqx-icon-arrow-down-material-green,
.sorticon.ascending .jqx-grid-column-sorticon-material-green
 {
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
    left: -1px;
}
.jqx-combobox-state-selected-material-green .jqx-icon-arrow-down-material-green{
    left:-1px;
}
.jqx-listbox-container {
    margin-top: 1px;
}

input[type="text"].jqx-input-material-green:-moz-placeholder, input[type="text"].jqx-widget-content-material-green:-moz-placeholder, input[type="textarea"].jqx-widget-content-material-green:-moz-placeholder, textarea.jqx-input-material-green:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-material-green:-webkit-input-placeholder, input[type="text"].jqx-widget-content-material-green:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-material-green:-webkit-input-placeholder, textarea.jqx-input-material-green:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-material-green:-ms-input-placeholder, input[type="text"].jqx-widget-content-material-green:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-material-green:-ms-input-placeholder, textarea.jqx-input-material-green:-ms-input-placeholder {
    color: #999999;
}

.jqx-combobox-content-focus-material-green, .jqx-combobox-state-focus-material-green, .jqx-fill-state-focus-material-green,
.jqx-numberinput-focus-material-green {
    outline: none;
}

.jqx-popup-material-green.jqx-fill-state-focus-material-green {
    outline: none;
    border-color: #E0E0E0 !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-text-area-material-green, .jqx-text-area-material-green > div {
    overflow:visible !important;
}
.jqx-text-area-element-material-green {
   box-sizing: border-box;
}
.jqx-pivotgrid-content-wrapper.jqx-fill-state-normal-material-green {
    border-color: #E0E0E0;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-expand-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-collapse-material-green {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-collapse-rtl-material-green {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-expand-rtl-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-title-selected-top-material-green, .jqx-tabs-selection-tracker-top-material-green {
    border-color: transparent;
    filter: none;
    background: #fff;
    color: #333;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-tabs-title-material-green, .jqx-ribbon-item-material-green {
    color: #333;
}
.jqx-tabs-title-selected-bottom-material-green,
.jqx-tabs-title-selected-top-material-green
 {
    color: #558B2F;
    font-weight:500;   
    padding-top:5px;
    padding-bottom:5px;
}
.jqx-tabs-title.jqx-fill-state-hover-material-green {
    border-color: transparent;
}
.jqx-ribbon-item-material-green {
    cursor: pointer;
}
.jqx-ribbon-item-selected-material-green {
    color: #558B2F;
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-material-green {
    background: transparent;
}

.jqx-ribbon-header-top-material-green {
    border-color: transparent;
    border-bottom-color: #E0E0E0;
}

.jqx-ribbon-header-bottom-material-green {
    border-color: transparent;
    border-top-color: #E0E0E0;
}

.jqx-ribbon-header-right-material-green {
    border-color: transparent;
    border-left-color:#E0E0E0;
}

.jqx-ribbon-header-left-material-green {
    border-color: transparent;
    border-right-color:#E0E0E0;
}

.jqx-tabs-title-selected-bottom-material-green, .jqx-tabs-selection-tracker-bottom-material-green {
    border-color: transparent;
    border-top: 1px solid #fff;
    filter: none;
    background: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-material-green, .jqx-ribbon-material-green {
    border-color: transparent;
}


.jqx-tabs-header-material-green, .jqx-ribbon-header-material-green {
    background: transparent;
}
.jqx-tabs-position-bottom .jqx-tabs-header-material-green {
    border-color: transparent;
}
.jqx-layout-material-green .jqx-tabs-header-material-green, .jqx-layout-material-green .jqx-ribbon-header-material-green {
    background: #fff;
    border-color: #E0E0E0;
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-material-green, .jqx-tabs-title-hover-bottom-material-green, .jqx-tabs-header-material-green {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    background: transparent;
}

.jqx-tabs-content-material-green {
    box-sizing: border-box;
    border: 1px solid #E0E0E0;
    border-top-color: transparent;
    padding:5px;
}
.jqx-tabs-bar-material-green {
    position: absolute;
    bottom: 0;
    background: #558B2F;
    height: 2px;
    z-index:10;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-material-green.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-material-green {
    top: 0;
}


.jqx-layout-material-green {
    background-color: #cccccc;
}

.jqx-kanban-column-header-collapsed-material-green {
    background: -moz-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ff3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(248,248,248,1)), color-stop(100%, rgba(234,234,234,1))); /* safari4+,chrome */
    background: -webkit-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* safari5.1+,chrome10+ */
    background: -o-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ie10+ */
    background: linear-gradient(90deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* w3c */
}

.jqx-calendar-material-green > div {
    padding: 10px;
    box-sizing: border-box;
}

.jqx-calendar-cell-material-green {
    border-radius: 50%;
    font-size:12px;
}
.jqx-calendar-cell-year-material-green,
.jqx-calendar-cell-decade-material-green {
    border-radius: 25%;
}

.jqx-calendar-title-content-material-green {
    font-weight:bold;
}
.jqx-calendar-column-cell-material-green {
    color: rgba(0,0,0,.38);
    font-size:12px;
}
.jqx-grid-column-menubutton-material-green {
    background-image: url('images/material-icon-down.png');
}

.jqx-tabs-close-button-material-green {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-close-button-selected-material-green {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-close-button-hover-material-green {
    background-image: url(./images/close.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-green .jqx-icon-arrow-up-selected-material-green {
    background-image: url('images/material-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-green .jqx-icon-arrow-down-selected-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-green .jqx-icon-arrow-left-selected-material-green {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-scrollbar-button-state-pressed-material-green .jqx-icon-arrow-right-selected-material-green {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-expand-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-collapse-material-green {
    background-image: url('images/material-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-collapse-rtl-material-green {
    background-image: url('images/material-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-material-green.jqx-grid-cell-selected-material-green > .jqx-grid-group-expand-rtl-material-green {
    background-image: url('images/material-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-group-collapse-material-green {
    background-image: url(./images/material-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.jqx-grid-group-collapse-rtl-material-green {
    padding-right: 0px;
    background-image: url(./images/material-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.jqx-grid-group-expand-material-green, .jqx-grid-group-expand-rtl-material-green {
    padding-right: 0px;
    background-image: url(./images/material-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-cell-filter-row-material-green {
    background-color: #fafafa;
}

.jqx-icon-arrow-first-material-green {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-material-green {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-first-hover-material-green {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-hover-material-green {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-first-selected-material-green {
    background-image: url('images/material-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-selected-material-green {
    background-image: url('images/material-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-fill-state-pressed-material-green .jqx-icon-arrow-first-selected-material-green {
    background-image: url('images/material-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-fill-state-pressed-material-green .jqx-icon-arrow-last-selected-material-green {
    background-image: url('images/material-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-fill-state-pressed-material-green .jqx-icon-arrow-left-selected-material-green {
    background-image: url('images/material-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-fill-state-pressed-material-green .jqx-icon-arrow-right-selected-material-green {
    background-image: url('images/material-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-primary .jqx-icon-arrow-down-material-green, .jqx-warning .jqx-icon-arrow-down-material-green, .jqx-danger .jqx-icon-arrow-down-material-green, .jqx-success .jqx-icon-arrow-down-material-green, .jqx-info .jqx-icon-arrow-down-material-green {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-down-selected-material-green, .jqx-warning .jqx-icon-arrow-down-selected-material-green, .jqx-danger .jqx-icon-arrow-down-selected-material-green, .jqx-success .jqx-icon-arrow-down-selected-material-green, .jqx-info .jqx-icon-arrow-down-selected-material-green {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-down-hover-material-green, .jqx-warning .jqx-icon-arrow-down-hover-material-green, .jqx-danger .jqx-icon-arrow-down-hover-material-green, .jqx-success .jqx-icon-arrow-down-hover-material-green, .jqx-info .jqx-icon-arrow-down-hover-material-green {
    background-image: url('images/material-icon-down.png');
}

.jqx-primary .jqx-icon-arrow-up-material-green, .jqx-warning .jqx-icon-arrow-up-material-green, .jqx-danger .jqx-icon-arrow-up-material-green, .jqx-success .jqx-icon-arrow-up-material-green, .jqx-info .jqx-icon-arrow-up-material-green {
    background-image: url('images/material-icon-up.png');
}

.jqx-primary .jqx-icon-arrow-up-selected-material-green, .jqx-warning .jqx-icon-arrow-up-selected-material-green, .jqx-danger .jqx-icon-arrow-up-selected-material-green, .jqx-success .jqx-icon-arrow-up-selected-material-green, .jqx-info .jqx-icon-arrow-up-selected-material-green {
    background-image: url('images/material-icon-up.png');
}

.jqx-primary .jqx-icon-arrow-up-hover-material-green, .jqx-warning .jqx-icon-arrow-up-hover-material-green, .jqx-danger .jqx-icon-arrow-up-hover-material-green, .jqx-success .jqx-icon-arrow-up-hover-material-green, .jqx-info .jqx-icon-arrow-up-hover-material-green {
    background-image: url('images/material-icon-up.png');
}


.jqx-primary .jqx-icon-arrow-left-material-green, .jqx-warning .jqx-icon-arrow-left-material-green, .jqx-danger .jqx-icon-arrow-left-material-green, .jqx-success .jqx-icon-arrow-left-material-green, .jqx-info .jqx-icon-arrow-left-material-green {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-left-selected-material-green, .jqx-warning .jqx-icon-arrow-left-selected-material-green, .jqx-danger .jqx-icon-arrow-left-selected-material-green, .jqx-success .jqx-icon-arrow-left-selected-material-green, .jqx-info .jqx-icon-arrow-left-selected-material-green {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-left-hover-material-green, .jqx-warning .jqx-icon-arrow-left-hover-material-green, .jqx-danger .jqx-icon-arrow-left-hover-material-green, .jqx-success .jqx-icon-arrow-left-hover-material-green, .jqx-info .jqx-icon-arrow-left-hover-material-green {
    background-image: url('images/material-icon-left.png');
}

.jqx-primary .jqx-icon-arrow-right-material-green, .jqx-warning .jqx-icon-arrow-right-material-green, .jqx-danger .jqx-icon-arrow-right-material-green, .jqx-success .jqx-icon-arrow-right-material-green, .jqx-info .jqx-icon-arrow-right-material-green {
    background-image: url('images/material-icon-right.png');
}

.jqx-primary .jqx-icon-arrow-right-selected-material-green, .jqx-warning .jqx-icon-arrow-right-selected-material-green, .jqx-danger .jqx-icon-arrow-right-selected-material-green, .jqx-success .jqx-icon-arrow-right-selected-material-green, .jqx-info .jqx-icon-arrow-right-selected-material-green {
    background-image: url('images/material-icon-right.png');
}

.jqx-primary .jqx-icon-arrow-right-hover-material-green, .jqx-warning .jqx-icon-arrow-right-hover-material-green, .jqx-danger .jqx-icon-arrow-right-hover-material-green, .jqx-success .jqx-icon-arrow-right-hover-material-green, .jqx-info .jqx-icon-arrow-right-hover-material-green {
    background-image: url('images/material-icon-right.png');
}


/* Ripple effect */
.ripple {
    position: relative;
    transform: translate3d(0, 0, 0);
    overflow:hidden;
}

.ink {
    display: block;
    position: absolute;
    pointer-events: none;
    border-radius: 0%;
    transform: scaleX(0);
    background: rgba(85,139,47,0.5); 
    opacity: 0.25;
}


.outlined .ink, .flat .ink {
    background: rgba(85,139,47,0.5); 
    overflow:hidden;
}

.ink.animate {
    animation: ripple .7s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-material-green .ripple,
.jqx-radiobutton-material-green .ripple
 {
    overflow:visible;
}
.jqx-checkbox-material-green .ink,
.jqx-radiobutton-material-green .ink 
 {
    transform: scale(0); 
    background: #558B2F;
    border-radius: 50%;
}
.jqx-checkbox-material-green .ink.animate,
.jqx-radiobutton-material-green .ink.animate
 {
    animation: checkRipple 0.3s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-material-green .ink.active,
.jqx-radiobutton-material-green .ink.active
 {
    opacity: 0.2;
    transform: scale(2);
}
.jqx-checkbox-default-material-green.active .ink,
.jqx-radiobutton-default-material-green.active .ink
 {
    opacity: 0.2;
    transform: scale(2);
}
/* Ripple effect */
.buttonRipple {
  background-position: center;
  transition: background 0.8s;
}
.buttonRipple:hover {
  background: #558B2F radial-gradient(circle, transparent 1%, #558B2F 1%) center/15000%;
}
.buttonRipple:active {
  background-color: #ECE0FD;
  background-size: 100%;
  transition: background 0s;
}
.buttonRipple:active:not(:hover) {
      color: #333;
}
@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(5);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}
@keyframes checkRipple {
    100% {
        opacity: 0.2;
        transform: scale(2);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}

.jqx-fill-state-pressed-material-green .jqx-icon-delete-material-green
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-material-green .jqx-icon-edit-material-green
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-material-green .jqx-icon-save-material-green
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-material-green .jqx-icon-cancel-material-green
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-material-green .jqx-icon-search-material-green
{
    background-image: url(./images/search_white.png);
}
.jqx-fill-state-pressed-material-green .jqx-icon-plus-material-green
{
    background-image: url(./images/plus_white.png);
}
.jqx-fill-state-pressed-material-green .jqx-menu-minimized-button-material-green {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-fill-state-hover-material-green .jqx-editor-toolbar-icon-material-green, .jqx-fill-state-pressed-material-green .jqx-editor-toolbar-icon-material-green {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-fill-state-hover-material-green .jqx-editor-toolbar-icon-fontsize-material-green,
.jqx-fill-state-pressed-material-green .jqx-editor-toolbar-icon-fontsize-material-green,
.jqx-fill-state-hover-material-green .jqx-editor-toolbar-icon-forecolor-material-green,
.jqx-fill-state-pressed-material-green .jqx-editor-toolbar-icon-forecolor-material-green
{
        background: url('images/html_editor.png') no-repeat;
}
.jqx-editor-toolbar-button-material-green {
    border-color: #ddd;
    box-shadow: none !important;
	color: #333;
}
.jqx-input-group-material-green.jqx-fill-state-disabled {
	pointer-events: none;
}

/*applied to the timepicker*/
.jqx-needle-central-circle-material-green {
	fill: rgb(85, 139, 47);
}

.jqx-needle-material-green {
	fill: rgb(85, 139, 47);
}

.jqx-time-picker .jqx-header .jqx-selected-material-green:focus {
    outline: 2px solid rgba(85, 139, 47, 0.5);
	box-shadow: 0px 0px 4px 2px rgba(85, 139, 47, 0.125);
}

.jqx-svg-picker-material-green:focus {
	border: 1px solid rgb(85, 139, 47) !important;
}


.jqx-split-layout-component-material-green .jqx-split-layout {
    --jqx-primary-rgb: 85, 139, 47;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-primary-color: #fff;
    --jqx-background: #fff;
    --jqx-background-color: rgba(0,0,0, .88);
    --jqx-background-hover-rgb: 225, 225, 225;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(0,0,0,.54);
    --jqx-surface-rgb: 255, 255, 255;
    --jqx-surface: rgb(var(--jqx-surface-rgb));
    --jqx-surface-color: rgba(0,0,0, .88);
    --jqx-border: rgba(85, 139, 47, 1);
}
.jqx-dropdownbutton-popup,
.jqx-calendar-material-green.jqx-popup,
.jqx-listbox-material-green.jqx-popup,
.jqx-grid-menu.jqx-popup {
  transition: transform 0.25s ease-in-out, opacity 0.35s ease-in-out;
  transform: scaleY(0);
  opacity: 0;
  transform-origin: top left;
  display: block !important;
}

.jqx-dropdownbutton-popup.jqx-popup-show,
.jqx-calendar-material-green.jqx-popup-show,
.jqx-listbox-material-green.jqx-popup-show,
.jqx-grid-menu.jqx-popup-show {
  transform: scaleY(1);
  opacity: 1;
}
