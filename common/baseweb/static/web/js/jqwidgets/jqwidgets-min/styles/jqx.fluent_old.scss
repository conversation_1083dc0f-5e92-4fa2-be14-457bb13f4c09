﻿$themeName: 'fluent';

$primary: #0078D4;

:root {
  --fluent-black: #000;
  --fluent-white: #fff;

  --fluent-surface: #fff;
  --fluent-surface-color: #000;

  --fluent-background: #fff;
  --fluent-background-color: #000;

  --fluent-type-primary: #201F1E;
  --fluent-type-secondary: #323130;
  --fluent-type-disabled: #323130;

  --fluent-body-divider: #EDEBE9;
  --fluent-input-background: #fff;
  --fluent-input-disabled-background: #EDEBE9;
  --fluent-input-border: #8A8886;
  --fluent-input-border-hover: #323130;

  --fluent-alert-color: #201F1E;

  --fluent-theme-primary: #0078D4;
  --fluent-theme-lighter-alt: #EFF6FC;
  --fluent-theme-lighter: #DEECF9;
  --fluent-theme-light: #C7E0F4;
  --fluent-theme-tertiary: #2B88D8;
  --fluent-theme-dark-alt: #106EBE;
  --fluent-theme-dark: #005A9E;
  --fluent-theme-darker: #004578;

  --fluent-btn-primary-color: #fff;
  --fluent-btn-primary-bg: #0078D4;
  --fluent-btn-primary-bg-hover: #106EBE;
  --fluent-btn-primary-bg-active: #005A9E;

  --fluent-btn-secondary-color: #201F1E;
  --fluent-btn-secondary-bg: #fff;
  --fluent-btn-secondary-bg-hover: #F3F2F1;
  --fluent-btn-secondary-bg-active: #EDEBE9;

  --fluent-btn-text-color: #201F1E;
  --fluent-btn-text-bg: #fff;
  --fluent-btn-text-color-hover: #201F1E;
  --fluent-btn-text-bg-hover: #F3F2F1;
  --fluent-btn-text-color-active: #201F1E;
  --fluent-btn-text-bg-active: #EDEBE9;

  --fluent-window-bg: #fff;
  --fluent-tabs-item-bg-hover: #F3F2F1;
  --fluent-list-items-group-bg: #EDEBE9;
  --fluent-tooltip-bg: #fff;
  --fluent-tooltip-color: #201F1E;

  --fluent-tag-bg: #F3F2F1;
  --fluent-tag-color: #201F1E;

  --fluent-breadcrumb-color: #605E5C;
  --fluent-breadcrumb-bg: transparent;
  --fluent-breadcrumb-color-hover: #201F1E;
  --fluent-breadcrumb-bg-hover: #F3F2F1;

  --fluent-accordion-header-color: #201F1E;
  --fluent-accordion-header-bg: #EDEBE9;

  --fluent-pager-color: #0078D4;
  --fluent-pager-bg: #fff;
  --fluent-pager-border: #C8C6C4;

  --fluent-sortable-border: #EDEBE9;

  --fluent-error-bg: #FDE7E9;
  --fluent-error-color: #A80000;

  --fluent-success-bg: #DFF6DD;
  --fluent-success-color: #107C10;

  --fluent-severe-warning-bg: #FED9CC;
  --fluent-severe-warning-color: #D83B01;

  --fluent-warning-bg: #FFF4CE;
  --fluent-warning-color: #797775;

  --fluent-greys-white: #fff;
  --fluent-greys-grey10: #FAF9F8;
  --fluent-greys-grey20: #F3F2F1;
  --fluent-greys-grey30: #EDEBE9;
  --fluent-greys-grey40: #E1DFDD;
  --fluent-greys-grey50: #D2D0CE;
  --fluent-greys-grey60: #C8C6C4;

  --fluent-greys-grey90: #A19F9D;
  --fluent-greys-grey110: #8A8886;
  --fluent-greys-grey130: #605E5C;
  --fluent-greys-grey150: #3B3A39;
  --fluent-greys-grey160: #323130;
  --fluent-greys-grey190: #201F1E;

  --fluent-overlay-light: rgba(red('#fff'), green('#fff'), blue('#fff'), 0.4);
  --fluent-overlay-dark: rgba(red('#000'), green('#000'), blue('#000'), 0.4);

  --fluent-box-shadow-4: 0px 0.3px 0.9px rgba(0, 0, 0, 0.1), 0px 1.6px 3.6px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-8: 0px 0.6px 1.8px rgba(0, 0, 0, 0.1), 0px 3.2px 7.2px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-16: 0px 1.2px 3.6px rgba(0, 0, 0, 0.1), 0px 6.4px 14.4px rgba(0, 0, 0, 0.13);
  --fluent-box-shadow-64: 0px 4.8px 14.4px rgba(0, 0, 0, 0.18), 0px 25.6px 57.6px rgba(0, 0, 0, 0.22);

  --jqx-primary: var(--fluent-theme-primary);
}

body[theme="dark"] {
  --fluent-surface: #282727;
  --fluent-surface-color: #fff;

  --fluent-background: #282727;
  --fluent-background-color: #fff;

  --fluent-input-background: #282727;

  --fluent-input-disabled-background: #605E5C;

  --fluent-greys-grey190: #fff;

  --fluent-type-primary: #EDEBE9;

  --fluent-input-border: #6c6c6c;
  --fluent-input-border-hover: #EDEBE9;

  --fluent-btn-secondary-color: #EDEBE9;
  --fluent-btn-secondary-bg: #8A8886;
  --fluent-btn-secondary-bg-hover: #605E5C;
  --fluent-btn-secondary-bg-active: #3B3A39;

  --fluent-btn-text-color: #201F1E;
  --fluent-btn-text-bg: #fff;
  --fluent-btn-text-color-hover: #fff;
  --fluent-btn-text-bg-hover: #605E5C;
  --fluent-btn-text-color-active: #fff;
  --fluent-btn-text-bg-active: #3B3A39;

  --fluent-window-bg: #282727;
  --fluent-tabs-item-bg-hover: #605E5C;
  --fluent-list-items-group-bg: #605E5C;

  --fluent-tooltip-bg: #201F1E;
  --fluent-tooltip-color: #fff;

  --fluent-tag-bg: #605E5C;
  --fluent-tag-color: #F3F2F1;

  --fluent-breadcrumb-color: #C8C6C4;
  --fluent-breadcrumb-bg: transparent;
  --fluent-breadcrumb-color-hover: #C8C6C4;
  --fluent-breadcrumb-bg-hover: #3B3A39;

  --fluent-accordion-header-color: #EDEBE9;
  --fluent-accordion-header-bg: #201F1E;

  --fluent-pager-color: #C8C6C4;
  --fluent-pager-bg: #201F1E;
  --fluent-pager-border: #3B3A39;

  --fluent-sortable-border: #3B3A39;
}

@import url('https://fonts.cdnfonts.com/css/segoe-ui-4');

html {
  font-size: 14px;
}

body {
  font-family: 'Segoe UI', sans-serif;
  color: var(--fluent-greys-grey190);

  * {
    box-sizing: border-box;
  }
}

.jqx-splitter {
  overflow: auto !important;
  max-width: 100% !important;
}

.jqx-scrollbar-thumb-state-normal-horizontal,
.jqx-scrollbar-thumb-state-normal {
  &.jqx-fill-state-normal {
    border-color: var(--fluent-greys-grey50);
    background: var(--fluent-greys-grey50);
  }
}


.jqx-button {
  &.jqx-button-#{$themeName} {
    cursor: pointer;

    --jqx-border: var(--fluent-input-border);
    --jqx-background: var(--fluent-btn-secondary-bg);
    --jqx-background-color: var(--fluent-btn-secondary-color);

    &:hover {
      --jqx-background: var(--fluent-btn-secondary-bg-hover);
    }

    &:active {
      --jqx-background: var(--fluent-btn-secondary-bg-active);
    }

    &:not(.jqx-navbar-block) {
      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        outline-offset: 1px;
        outline: 2px solid var(--jqx-border);
      }
    }

    &.jqx-navbar-block {
      &.jqx-fill-state-pressed,
      &.jqx-fill-state-focus,
      &:focus {
        background: var(--fluent-btn-secondary-bg-active);
      }
    }

    &[disabled] {
      opacity: 1;
      --jqx-border: var(--fluent-greys-grey20);
      --jqx-background: var(--fluent-greys-grey20);
      --jqx-background-color: var(--fluent-greys-grey90);
    }

    &.jqx-primary,
    &.primary {
      --jqx-border: var(--fluent-btn-primary-bg);
      --jqx-background: var(--fluent-btn-primary-bg);
      --jqx-background-color: var(--fluent-btn-primary-color);

      &:hover {
        --jqx-border: var(--fluent-btn-primary-bg-hover);
        --jqx-background: var(--fluent-btn-primary-bg-hover);
      }

      &:active {
        --jqx-background: var(--fluent-btn-primary-bg-active);
      }

      &[disabled] {
        opacity: 1;
        --jqx-border: var(--fluent-greys-grey20);
        --jqx-background: var(--fluent-greys-grey20);
        --jqx-background-color: var(--fluent-greys-grey90);
      }
    }

    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: 1px solid var(--jqx-border);
    border-radius: 2px;
  }
}

.jqx-dropdownlist,
.jqx-combobox,
.jqx-dropdownbutton,
.jqx-text-area,
.jqx-input {
  --jqx-border: var(--fluent-input-border);
  --jqx-background: var(--fluent-input-background);

  &.jqx-fill-state-pressed,
  &.jqx-fill-state-focus,
  &:focus {
    --jqx-border: var(--fluent-theme-primary) !important;
    outline: 1px solid var(--jqx-border) !important;
  }

  &:hover,
  &[hover] {
    --jqx-border: var(--fluent-input-border-hover);
  }

  &.jqx-input-disabled,
  &[disabled] {
    --jqx-background: var(--fluent-input-disabled-background);
    --jqx-background-color: var(--fluent-greys-grey90);
    --jqx-border: var(--fluent-input-disabled-background);
    opacity: 1;

    input {
      color: var(--jqx-background-color);
    }
  }

  input {
    background: transparent
  }

  .jqx-combobox-content,
  .jqx-combobox-arrow-normal,
  .jqx-action-button {
    background-color: transparent;
    border: 0 !important;
  }

  .jqx-icon-arrow-up{
    transform: rotate(180deg);
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='7' viewBox='0 0 12 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 6.70898L0.146484 0.855469L0.855469 0.146484L6 5.29102L11.1445 0.146484L11.8535 0.855469L6 6.70898Z' fill='%23605E5C'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat;
    background-position: center center;
  }

  .jqx-icon-arrow-down,
  .jqx-combobox-arrow-normal,
  .jqx-combobox-arrow-hover {
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='7' viewBox='0 0 12 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 6.70898L0.146484 0.855469L0.855469 0.146484L6 5.29102L11.1445 0.146484L11.8535 0.855469L6 6.70898Z' fill='%23605E5C'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat;
    background-position: center center;
  }

  min-height: 22px !important;
  background: var(--jqx-background);
  color: var(--jqx-background-color);
  border: 1px solid var(--jqx-border);
  border-radius: 2px;
  padding-left: 3px !important;
  padding-right: 3px !important;
}

input {
  &.jqx-input {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

.jqx-text-area {
  padding: 7px 10px !important;
}

.jqx-input {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.jqx-listbox-container {
  overflow: unset !important;
}

.jqx-listbox {
  border-radius: 2px;
  margin-top: 4px !important;
  border-color: transparent !important;
  box-shadow: var(--fluent-box-shadow-8) !important;


  .jqx-item {
    --jqx-background: var(--fluent-btn-secondary-bg);
    --jqx-border: var(--jqx-background);
    --jqx-background-color: var(--fluent-btn-secondary-color);

    &:hover {
      --jqx-background: var(--fluent-btn-secondary-bg-hover);
    }

    &.jqx-fill-state-pressed,
    &.jqx-fill-state-focus,
    &:focus {
      //--jqx-border: var(--fluent-input-border);
      --jqx-border: var(--fluent-greys-grey190);
    }

    &.jqx-fill-state-disabled {
      opacity: 1;
      --jqx-border: var(--fluent-greys-grey20);
      --jqx-background: var(--fluent-greys-grey20);
      --jqx-background-color: var(--fluent-greys-grey90);
    }

    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: 1px solid var(--jqx-border);
    border-radius: 1px;
  }
}

.jqx-popup {
  overflow: unset !important;
  border-radius: 2px;
  margin-top: 4px !important;
  border-color: transparent !important;
  box-shadow: var(--fluent-box-shadow-8) !important;
  background: var(--fluent-greys-white);

  .jqx-item {
    --jqx-background: var(--fluent-btn-secondary-bg);
    --jqx-border: var(--jqx-background);
    --jqx-background-color: var(--fluent-btn-secondary-color);

    &:hover {
      --jqx-background: var(--fluent-btn-secondary-bg-hover);
    }

    &.jqx-fill-state-pressed,
    &.jqx-fill-state-focus,
    &:focus {
      //--jqx-border: var(--fluent-input-border);
      --jqx-border: var(--fluent-greys-grey190);
    }

    &.jqx-fill-state-disabled {
      opacity: 1;
      --jqx-border: var(--fluent-greys-grey20);
      --jqx-background: var(--fluent-greys-grey20);
      --jqx-background-color: var(--fluent-greys-grey90);
    }

    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: 1px solid var(--jqx-border);
    border-radius: 1px;
    margin: 0;
  }
}

.jqx-color-picker {
  background: var(--fluent-greys-white);
  padding: 8px 8px 35px 8px;

  .jqx-color-picker-panel {
    > div {
      display: flex;
      align-items: center;
    }
  }
}

.jqx-slider {
  --jqx-primary: var(--fluent-greys-grey130);
  --jqx-ui-state-active: var(--fluent-greys-grey130);
  --jqx-disabled: var(--fluent-greys-grey60);
  --jqx-slider-track-size: 4px;
  --jqx-slider-thumb-width: 16px;
  --jqx-slider-thumb-height: 16px;
  overflow: unset;
  opacity: 1;

  .jqx-track-container {
    border-radius: 2px;
  }


  .jqx-slider-track {
    background: var(--jqx-disabled);

    .jqx-fill-state-normal {
      width: 16px;
      height: 16px;
      background-color: var(--fluent-greys-white) !important;
      border-width: 2px;
      border-color: var(--jqx-ui-state-active);
    }

    .jqx-fill-state-pressed {
      background-color: var(--jqx-ui-state-active);
      border-width: 2px;
    }

    &:hover {
      background: var(--fluent-theme-light);

      .jqx-fill-state-pressed {
        background-color: var(--fluent-theme-primary);
      }

      .jqx-fill-state-normal,
      .jqx-fill-state-hover {
        border-color: var(--fluent-theme-primary);
      }
    }

    overflow: unset;
  }

  &[disabled] {
    opacity: 1;

    .jqx-track {
      --jqx-primary: var(--fluent-greys-grey90);
      background-color: var(--fluent-greys-grey20);
      --jqx-disabled: var(--fluent-greys-grey20);

      .jqx-value {
        background-color: var(--fluent-greys-grey90);
      }
    }
  }
}

.jqx-calendar {
  box-shadow: var(--fluent-box-shadow-8);
  border: 0;
  border-radius: 2px;

  .jqx-calendar-title-header {
    background: transparent;
  }

  .jqx-calendar-column-header {
    border: 0;
  }

  td {
    border-radius: 0px;

    &.jqx-fill-state-pressed {
      border-color: var(--fluent-greys-grey130) !important;
      background: var(--fluent-greys-grey30) !important;
      color: var(--fluent-greys-grey190) !important;
    }

    &.jqx-fill-state-hover {
      border-color: var(--fluent-greys-grey20);
      background: var(--fluent-greys-grey20);
    }

    &.jqx-calendar-cell-today {
      border-color: var(--fluent-theme-primary);
      background: var(--fluent-theme-primary);
      color: var(--fluent-greys-white);
      border-radius: 50px;
    }
  }
}

.jqx-rating-image-default,
.jqx-rating-image-hover,
.jqx-rating-image-backward {
  img {
    visibility: hidden;
  }

  &:after {
    content: '';
    top: 0;
    position: absolute;
    display: block;

    background-position: center center;
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
  }
}

.jqx-rating-image-backward {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.8281 12.2461L16.25 20L10 15.1953L3.75 20L6.17188 12.2461L0 7.5H7.65625L10 0L12.3438 7.5H20L13.8281 12.2461ZM13.877 16.6016C13.6296 15.7943 13.3822 14.9935 13.1348 14.1992C12.8874 13.3984 12.6335 12.5944 12.373 11.7871C13.0436 11.2858 13.7044 10.7812 14.3555 10.2734C15.0065 9.76562 15.6641 9.25781 16.3281 8.75H11.4258L10 4.18945L8.57422 8.75H3.67188C4.33594 9.25781 4.99349 9.76562 5.64453 10.2734C6.29557 10.7812 6.95638 11.2858 7.62695 11.7871C7.36654 12.5944 7.11263 13.3984 6.86523 14.1992C6.61784 14.9935 6.37044 15.7943 6.12305 16.6016L10 13.6133L13.877 16.6016Z' fill='%23A19F9D'/%3E%3C/svg%3E");
    background-color: #fff;
  }
}

.jqx-rating-image-hover {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='36' viewBox='0 0 24 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.8281 20.2461L18.25 28L12 23.1953L5.75 28L8.17188 20.2461L2 15.5H9.65625L12 8L14.3438 15.5H22L15.8281 20.2461Z' fill='%230078D4'/%3E%3C/svg%3E");
  }
}

.jqx-rating-image-default {
  &:after {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='36' viewBox='0 0 24 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.8281 20.2461L18.25 28L12 23.1953L5.75 28L8.17188 20.2461L2 15.5H9.65625L12 8L14.3438 15.5H22L15.8281 20.2461Z' fill='%23201F1E'/%3E%3C/svg%3E");
  }
}

.jqx-tooltip {
  //visibility: visible !important;
  //display: block !important;
  //opacity: 1 !important;

  --jqx-tooltip-arrow-width: 16px;
  --jqx-tooltip-arrow-translate: 0;
  border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
  background-color: var(--fluent-tooltip-bg);
  color: var(--fluent-tooltip-color);
  box-shadow: var(--fluent-box-shadow-16) !important;
  border-radius: 2px;
  opacity: 1 !important;

  .jqx-tooltip-main {
    border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
    background-color: var(--fluent-tooltip-bg);
    color: var(--fluent-tooltip-color);
    border-radius: 2px;

    > div {
      background: transparent !important;
      border: 0 !important;;
    }

    &:after {
      content: "";
      width: 0;
      height: 0;
      border-left: var(--jqx-tooltip-arrow-width) solid transparent;
      border-right: var(--jqx-tooltip-arrow-width) solid transparent;
      border-top: var(--jqx-tooltip-arrow-width) solid var(--fluent-tooltip-bg);
      position: absolute;
      bottom: 0;
      left: calc(100% / 2 - var(--jqx-tooltip-arrow-width));
      transform: translateX(var(--jqx-tooltip-arrow-translate));
      top: 100%;
    }
  }
}

.jqx-popover {
  border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
  background-color: var(--fluent-tooltip-bg);
  color: var(--fluent-tooltip-color);
  box-shadow: var(--fluent-box-shadow-16) !important;
  border-radius: 2px;

  .jqx-popover-title {
    border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
    background-color: var(--fluent-tooltip-bg);
    color: var(--fluent-tooltip-color);
    border-radius: 2px;
  }

  .jqx-popover-arrow {
    &:after {
      border-width: 16px;
    }
  }

  &.bottom {
    .jqx-popover-arrow {
      &:after {
        top: 0;
        margin-left: -16px;
      }
    }
  }

  &.top {
    .jqx-popover-arrow {
      &:after {
        bottom: 0;
        margin-left: -16px;
      }
    }
  }

  &.left {
    .jqx-popover-arrow {
      &:after {
        right: 0;
        bottom: -16px;
      }
    }
  }

  &.right {
    .jqx-popover-arrow {
      &:after {
        left: 0;
        bottom: -16px;
      }
    }
  }
}

.jqx-window {
  border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
  background-color: var(--fluent-tooltip-bg);
  color: var(--fluent-tooltip-color);
  box-shadow: var(--fluent-box-shadow-64) !important;
  border-radius: 2px;
  padding: 18px 24px;

  .jqx-window-collapse-button-background {
    display: none;
  }

  .jqx-icon-close {
    background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.71094 7L13.1016 12.3984L12.3984 13.1016L7 7.71094L1.60156 13.1016L0.898438 12.3984L6.28906 7L0.898438 1.60156L1.60156 0.898438L7 6.28906L12.3984 0.898438L13.1016 1.60156L7.71094 7Z' fill='%23605E5C'/%3E%3C/svg%3E");
  }

  .jqx-window-header {
    border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
    background-color: var(--fluent-tooltip-bg);
    color: var(--fluent-tooltip-color);
    border-radius: 2px;
    font-size: 20px;
  }
}

.jqx-time-picker {
  border: var(--jqx-border-width) solid var(--fluent-tooltip-bg);
  background-color: var(--fluent-tooltip-bg);
  color: var(--fluent-tooltip-color);
  box-shadow: var(--fluent-box-shadow-16) !important;
  border-radius: 2px;

  .jqx-svg-picker:focus {
    border-color: var(--fluent-theme-primary);
  }

  .jqx-widget-header {
    fill: var(--fluent-theme-primary);

  }

  .jqx-header {
    background: var(--fluent-theme-primary);
    color: var(--fluent-greys-white);
  }
}

.jqx-progressbar {
  border: 0;
  background: var(--fluent-greys-grey30);

  .jqx-fill-state-pressed {
    background: var(--fluent-theme-primary);
  }
}


@keyframes css-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.jqx-loader {
  background: transparent;
  color: var(--fluent-theme-primary);
  box-shadow: unset !important;

  .jqx-loader-icon {
    background-image: unset;
    box-sizing: border-box;
    border-radius: 50%;
    border-width: 1.5px;
    border-style: solid;
    border-color: rgb(0, 120, 212) rgb(199, 224, 244) rgb(199, 224, 244);
    border-image: initial;
    animation-name: css-spinner;
    animation-duration: 1.3s;
    animation-iteration-count: infinite;
    animation-timing-function: cubic-bezier(0.53, 0.21, 0.29, 0.67);
    width: 28px;
    height: 28px;
    position: relative;
    margin: 0 auto 25px auto;
  }
}

/* ALERT */
.jqx-notification {
  display: flex;
  align-items: center;
  border-radius: 0;
  padding: 3px 10px;
  border: 0;
  font-size: 12px;
  color: var(--fluent-alert-color);

  box-shadow: unset !important;

  > div {
    display: flex;
    align-items: center;
  }

  &.jqx-notification-primary {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-theme-light) !important;
  }

  &.jqx-notification-info {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-greys-grey20) !important;
  }

  &.jqx-notification-success {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-success-bg) !important;
  }

  &.jqx-notification-warning {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-warning-bg) !important;
  }

  &.jqx-notification-severe-warning {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-severe-warning-bg) !important;
  }

  &.jqx-notification-error {
    color: var(--fluent-alert-color) !important;
    background-color: var(--fluent-error-bg) !important;
  }

  .jqx-notification-icon.jqx-notification-icon-info,
  .jqx-notification-icon.jqx-notification-icon-primary,
  .jqx-notification-icon.jqx-notification-icon-warning {
    background-image: url("data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 15C6.80729 15 6.14062 14.9115 5.5 14.7344C4.86458 14.5573 4.26823 14.3073 3.71094 13.9844C3.15365 13.6562 2.64583 13.2656 2.1875 12.8125C1.73438 12.3542 1.34375 11.8464 1.01562 11.2891C0.692708 10.7318 0.442708 10.1354 0.265625 9.5C0.0885417 8.85938 0 8.19271 0 7.5C0 6.80729 0.0885417 6.14323 0.265625 5.50781C0.442708 4.86719 0.692708 4.26823 1.01562 3.71094C1.34375 3.15365 1.73438 2.64844 2.1875 2.19531C2.64583 1.73698 3.15365 1.34635 3.71094 1.02344C4.26823 0.695312 4.86458 0.442708 5.5 0.265625C6.14062 0.0885417 6.80729 0 7.5 0C8.19271 0 8.85677 0.0885417 9.49219 0.265625C10.1328 0.442708 10.7318 0.695312 11.2891 1.02344C11.8464 1.34635 12.3516 1.73698 12.8047 2.19531C13.263 2.64844 13.6536 3.15365 13.9766 3.71094C14.3047 4.26823 14.5573 4.86719 14.7344 5.50781C14.9115 6.14323 15 6.80729 15 7.5C15 8.19271 14.9115 8.85938 14.7344 9.5C14.5573 10.1354 14.3047 10.7318 13.9766 11.2891C13.6536 11.8464 13.263 12.3542 12.8047 12.8125C12.3516 13.2656 11.8464 13.6562 11.2891 13.9844C10.7318 14.3073 10.1328 14.5573 9.49219 14.7344C8.85677 14.9115 8.19271 15 7.5 15ZM7.5 1C6.90104 1 6.32552 1.07812 5.77344 1.23438C5.22135 1.39062 4.70312 1.60938 4.21875 1.89062C3.73958 2.17188 3.30208 2.51042 2.90625 2.90625C2.51042 3.30208 2.17188 3.74219 1.89062 4.22656C1.60938 4.70573 1.39062 5.22396 1.23438 5.78125C1.07812 6.33333 1 6.90625 1 7.5C1 8.09375 1.07812 8.66927 1.23438 9.22656C1.39062 9.77865 1.60938 10.2969 1.89062 10.7812C2.17188 11.2604 2.51042 11.6979 2.90625 12.0938C3.30208 12.4896 3.73958 12.8281 4.21875 13.1094C4.70312 13.3906 5.22135 13.6094 5.77344 13.7656C6.32552 13.9219 6.90104 14 7.5 14C8.09375 14 8.66667 13.9219 9.21875 13.7656C9.77604 13.6094 10.2943 13.3906 10.7734 13.1094C11.2578 12.8281 11.6979 12.4896 12.0938 12.0938C12.4896 11.6979 12.8281 11.2604 13.1094 10.7812C13.3906 10.2969 13.6094 9.77865 13.7656 9.22656C13.9219 8.67448 14 8.09896 14 7.5C14 6.90625 13.9219 6.33333 13.7656 5.78125C13.6094 5.22396 13.3906 4.70573 13.1094 4.22656C12.8281 3.74219 12.4896 3.30208 12.0938 2.90625C11.6979 2.51042 11.2578 2.17188 10.7734 1.89062C10.2943 1.60938 9.77604 1.39062 9.21875 1.23438C8.66667 1.07812 8.09375 1 7.5 1ZM7 6H8V11H7V6ZM7 4H8V5H7V4Z' fill='%23605E5C'/%3E%3C/svg%3E");
    width: 30px;
    height: 25px;
  }

  .jqx-notification-icon.jqx-notification-icon-serve-warning {
    background-image: url("data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 15H0L7.5 0L15 15ZM1.61719 14H13.3828L7.5 2.23438L1.61719 14ZM8 6V11H7V6H8ZM7 12H8V13H7V12Z' fill='%23D83B01'/%3E%3C/svg%3E");
    width: 30px;
    height: 25px;
  }

  .jqx-notification-icon.jqx-notification-icon-success {
    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.6484 4.64844L12.3516 5.35156L6.5 11.2031L3.64844 8.35156L4.35156 7.64844L6.5 9.79688L11.6484 4.64844ZM8 0C8.73438 0 9.44271 0.0963542 10.125 0.289062C10.8073 0.476562 11.4453 0.744792 12.0391 1.09375C12.6328 1.4375 13.1719 1.85417 13.6562 2.34375C14.1458 2.82812 14.5625 3.36719 14.9062 3.96094C15.2552 4.55469 15.5234 5.19271 15.7109 5.875C15.9036 6.55729 16 7.26562 16 8C16 8.73438 15.9036 9.44271 15.7109 10.125C15.5234 10.8073 15.2552 11.4453 14.9062 12.0391C14.5625 12.6328 14.1458 13.1745 13.6562 13.6641C13.1719 14.1484 12.6328 14.5651 12.0391 14.9141C11.4453 15.2578 10.8073 15.526 10.125 15.7188C9.44271 15.9062 8.73438 16 8 16C7.26562 16 6.55729 15.9062 5.875 15.7188C5.19271 15.526 4.55469 15.2578 3.96094 14.9141C3.36719 14.5651 2.82552 14.1484 2.33594 13.6641C1.85156 13.1745 1.4349 12.6328 1.08594 12.0391C0.742188 11.4453 0.473958 10.8099 0.28125 10.1328C0.09375 9.45052 0 8.73958 0 8C0 7.26562 0.09375 6.55729 0.28125 5.875C0.473958 5.19271 0.742188 4.55469 1.08594 3.96094C1.4349 3.36719 1.85156 2.82812 2.33594 2.34375C2.82552 1.85417 3.36719 1.4375 3.96094 1.09375C4.55469 0.744792 5.1901 0.476562 5.86719 0.289062C6.54948 0.0963542 7.26042 0 8 0ZM8 15C8.64062 15 9.25781 14.9167 9.85156 14.75C10.4505 14.5833 11.0078 14.349 11.5234 14.0469C12.0443 13.7396 12.5182 13.3724 12.9453 12.9453C13.3724 12.5182 13.737 12.0469 14.0391 11.5312C14.3464 11.0104 14.5833 10.4531 14.75 9.85938C14.9167 9.26562 15 8.64583 15 8C15 7.35938 14.9167 6.74219 14.75 6.14844C14.5833 5.54948 14.3464 4.99219 14.0391 4.47656C13.737 3.95573 13.3724 3.48177 12.9453 3.05469C12.5182 2.6276 12.0443 2.26302 11.5234 1.96094C11.0078 1.65365 10.4505 1.41667 9.85156 1.25C9.25781 1.08333 8.64062 1 8 1C7.35938 1 6.73958 1.08333 6.14062 1.25C5.54688 1.41667 4.98958 1.65365 4.46875 1.96094C3.95312 2.26302 3.48177 2.6276 3.05469 3.05469C2.6276 3.48177 2.26042 3.95573 1.95312 4.47656C1.65104 4.99219 1.41667 5.54948 1.25 6.14844C1.08333 6.74219 1 7.35938 1 8C1 8.64062 1.08333 9.26042 1.25 9.85938C1.41667 10.4531 1.65104 11.0104 1.95312 11.5312C2.26042 12.0469 2.6276 12.5182 3.05469 12.9453C3.48177 13.3724 3.95312 13.7396 4.46875 14.0469C4.98958 14.349 5.54688 14.5833 6.14062 14.75C6.73438 14.9167 7.35417 15 8 15Z' fill='%23107C10'/%3E%3C/svg%3E");
  }

  .jqx-notification-icon.jqx-notification-icon-error {
    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 0C8.73438 0 9.44271 0.0963542 10.125 0.289062C10.8073 0.476562 11.4427 0.744792 12.0312 1.09375C12.625 1.44271 13.1641 1.86198 13.6484 2.35156C14.138 2.83594 14.5573 3.375 14.9062 3.96875C15.2552 4.55729 15.5234 5.19271 15.7109 5.875C15.9036 6.55729 16 7.26562 16 8C16 8.73438 15.9036 9.44271 15.7109 10.125C15.5234 10.8073 15.2552 11.4453 14.9062 12.0391C14.5573 12.6276 14.138 13.1667 13.6484 13.6562C13.1641 14.1406 12.625 14.5573 12.0312 14.9062C11.4427 15.2552 10.8073 15.526 10.125 15.7188C9.44271 15.9062 8.73438 16 8 16C7.26562 16 6.55729 15.9062 5.875 15.7188C5.19271 15.526 4.55469 15.2552 3.96094 14.9062C3.3724 14.5573 2.83333 14.1406 2.34375 13.6562C1.85938 13.1667 1.44271 12.6276 1.09375 12.0391C0.744792 11.4453 0.473958 10.8073 0.28125 10.125C0.09375 9.44271 0 8.73438 0 8C0 7.26562 0.09375 6.55729 0.28125 5.875C0.473958 5.19271 0.744792 4.55729 1.09375 3.96875C1.44271 3.375 1.85938 2.83594 2.34375 2.35156C2.83333 1.86198 3.3724 1.44271 3.96094 1.09375C4.55469 0.744792 5.19271 0.476562 5.875 0.289062C6.55729 0.0963542 7.26562 0 8 0ZM8 15C8.64583 15 9.26562 14.9167 9.85938 14.75C10.4583 14.5833 11.0156 14.349 11.5312 14.0469C12.0521 13.7396 12.5234 13.375 12.9453 12.9531C13.3724 12.526 13.737 12.0547 14.0391 11.5391C14.3464 11.0182 14.5833 10.4609 14.75 9.86719C14.9167 9.26823 15 8.64583 15 8C15 7.35417 14.9167 6.73438 14.75 6.14062C14.5833 5.54167 14.3464 4.98438 14.0391 4.46875C13.737 3.94792 13.3724 3.47656 12.9453 3.05469C12.5234 2.6276 12.0521 2.26302 11.5312 1.96094C11.0156 1.65365 10.4583 1.41667 9.85938 1.25C9.26562 1.08333 8.64583 1 8 1C7.35417 1 6.73177 1.08333 6.13281 1.25C5.53906 1.41667 4.98177 1.65365 4.46094 1.96094C3.94531 2.26302 3.47396 2.6276 3.04688 3.05469C2.625 3.47656 2.26042 3.94792 1.95312 4.46875C1.65104 4.98438 1.41667 5.54167 1.25 6.14062C1.08333 6.73438 1 7.35417 1 8C1 8.64583 1.08333 9.26823 1.25 9.86719C1.41667 10.4609 1.65104 11.0182 1.95312 11.5391C2.26042 12.0547 2.625 12.526 3.04688 12.9531C3.47396 13.375 3.94531 13.7396 4.46094 14.0469C4.98177 14.349 5.53906 14.5833 6.13281 14.75C6.73177 14.9167 7.35417 15 8 15ZM11.4609 5.24219L8.71094 8L11.4609 10.7578L10.7578 11.4609L8 8.71094L5.24219 11.4609L4.53906 10.7578L7.28906 8L4.53906 5.24219L5.24219 4.53906L8 7.28906L10.7578 4.53906L11.4609 5.24219Z' fill='%23A4262C'/%3E%3C/svg%3E");
  }

  .jqx-notification-close-button {
    background-image: url("data:image/svg+xml,%3Csvg width='10' height='10' viewBox='0 0 10 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.44434 5L9.90723 9.46777L9.46777 9.90723L5 5.44434L0.532227 9.90723L0.0927734 9.46777L4.55566 5L0.0927734 0.532227L0.532227 0.0927734L5 4.55566L9.46777 0.0927734L9.90723 0.532227L5.44434 5Z' fill='%23201F1E'/%3E%3C/svg%3E") !important;
    right: 0 !important;
    top: 0 !important;
    background-position: center center !important;
  }
}

.jqx-bar-gauge {
  svg > path.jqx-bar-gauge-background-slice {
    fill: var(--fluent-greys-grey10) !important;
  }

  svg > path#jqxBarGaugeSlice0 {
    fill: var(--fluent-success-bg) !important;
  }

  svg > path#jqxBarGaugeSlice1 {
    fill: var(--fluent-error-bg) !important;
  }

  svg > path#jqxBarGaugeSlice2 {
    fill: var(--fluent-warning-bg) !important;
  }

  svg > path#jqxBarGaugeSlice3 {
    fill: var(--fluent-theme-lighter) !important;
  }

  #jqxBarGaugeLabelLine0,
  #jqxBarGaugeLabel0 {
    fill: var(--fluent-success-color) !important;
    stroke: var(--fluent-success-color) !important;
  }

  #jqxBarGaugeLabelLine1,
  #jqxBarGaugeLabel1 {
    fill: var(--fluent-error-color) !important;
    stroke: var(--fluent-error-color) !important;
  }

  #jqxBarGaugeLabelLine2,
  #jqxBarGaugeLabel2 {
    fill: var(--fluent-warning-color) !important;
    stroke: var(--fluent-warning-color) !important;
  }

  #jqxBarGaugeLabelLine3,
  #jqxBarGaugeLabel3 {
    fill: var(--fluent-theme-primary) !important;
    stroke: var(--fluent-theme-primary) !important;
  }
}

#jqxMenu.jqx-menu {
  border: 0;
  background: var(--fluent-theme-primary);
  border-radius: 0;
  color: var(--fluent-greys-white);
  position: relative;
  display: flex;
  align-items: center;

  &:not(.jqx-touch) {
    .jqx-menu-ul {
      display: flex;
      height: 100%;
      align-items: center;

      li {
        display: flex;
        height: 100%;
        align-items: center;
        border: 0;
      }
    }
  }

  .jqx-menu-minimized-button {
    filter: invert(1000%);
  }

  .jqx-menu-item-top {
    border-radius: 0px;

    &.jqx-menu-item-top-hover {
      background: var(--fluent-theme-dark);
      color: var(--fluent-greys-white);
    }

    &.jqx-menu-item-top-selected:not(.jqx-menu-item-top-hover) {
      background: var(--fluent-theme-darker);
      color: var(--fluent-greys-white);
    }
  }
}

.jqx-tabs {
  border: 0;
  background: transparent;

  .jqx-tabs-header {
    border: 0;
    background: transparent;
    margin-bottom: 15px;
    padding: 0;

    .jqx-item {
      margin: 0 7px !important;
      border: 0;
      border-bottom: 2px solid transparent;
      color: var(--fluent-btn-secondary-color);
      padding: 7px 5px !important;


      &:focus {
        border: 1px solid var(--fluent-input-border-hover);
        border-radius: 2px;
      }

      &.jqx-fill-state-pressed {
        border-color: var(--fluent-theme-primary);

        &:hover {
          background: var(--fluent-btn-secondary-bg-hover);
        }
      }

      &.jqx-fill-state-hover {
        border-color: var(--fluent-btn-secondary-bg-hover);
        background: var(--fluent-btn-secondary-bg-hover);
      }
    }
  }
}

.jqx-tabs.primary {
  border: 0;
  background: transparent;

  .jqx-tabs-header {
    border: 0;
    background: transparent;
    margin-bottom: 15px;
    padding: 0;

    .jqx-item {
      margin: 0 7px !important;
      border: 1px solid transparent;
      color: var(--fluent-btn-secondary-color);
      padding: 7px 5px !important;

      border-radius: 0 !important;

      &:focus {
        border: 1px solid var(--fluent-input-border-hover);
        border-radius: 2px;
      }

      &.jqx-fill-state-pressed {
        color: var(--fluent-btn-primary-color);
        background: var(--fluent-btn-primary-bg);

        &:hover {
          background: var(--fluent-btn-primary-bg-hover);
        }
      }

      &.jqx-fill-state-hover {
        color: var(--fluent-btn-primary-color);
        border-color: var(--fluent-btn-primary-bg-hover);
        background: var(--fluent-btn-primary-bg-hover);
      }
    }
  }
}

.jqx-navigationbar,
.jqx-expander {
  background: var(--fluent-greys-white);

  .jqx-expander-header {
    background: transparent;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: var(--fluent-greys-grey190);
    flex-direction: row-reverse;
    gap: 10px;
    padding: 10px 20px;


    .jqx-expander-arrow {
      transform: rotate(-90deg);
      background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");

      &.jqx-expander-arrow-expanded {
        transform: rotate(0deg);
      }

    }
  }
}

.jqx-bulletchart {
  .jqx-bulletchart-target,
  .jqx-bulletchart-pointer,
  .jqx-bulletchart-range {
    background-color: var(--fluent-theme-primary) !important;
  }
}

.jqx-tree {
  margin: 0;
  border: 0;
  background: var(--fluent-greys-white);

  .jqx-tree-item-arrow-expand {
    margin-right: 10px;
    margin-top: 9px !important;
    transform: rotate(180deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
  }

  .jqx-tree-item-arrow-collapse {
    margin-right: 10px;
    margin-top: 9px !important;
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(0deg);
  }

  .jqx-tree-dropdown-root {
    width: 100% !important;
    margin: 10px 0 !important;

    .jqx-tree-item {
      border: 0 !important;
      //padding-top: 10px !important;
      //padding-bottom: 10px !important;
      display: block !important;
      overflow: hidden;
      border-radius: 0;
      padding: 10px !important;
    }


    .jqx-tree-item-hover {
      background: var(--fluent-greys-grey20);
      color: var(--fluent-greys-grey190);
    }

    .jqx-fill-state-pressed,
    .jqx-tree-item-selected {
      background: var(--fluent-theme-primary);
      color: var(--fluent-greys-white);
    }

    li.xx-jqx-tree-item-li {
      display: block;
      width: 100%;
      margin: 0 !important;
      margin-top: 0 !important;
      line-height: 1 !important;
      padding-top: 10px !important;
      padding-bottom: 10px !important;

      .jqx-tree-item {
        border: 0 !important;
        padding-top: 10px !important;
        padding-bottom: 10px !important;
        display: block !important;
        overflow: hidden;
      }

      .jqx-tree-item-selected,
      .jqx-fill-state-pressed {
        display: block !important;
        background: transparent;

        &[aria-selected="true"] {
          color: var(--fluent-greys-grey190);
        }
      }

      &[aria-selected="true"] {
        background: var(--fluent-theme-primary);
        color: var(--fluent-greys-white);

        &:hover {
          color: var(--fluent-greys-grey190);
        }
      }

      .jqx-tree-item-hover {
        background: var(--fluent-greys-grey20);

        .jqx-tree-item {
          border: 0;
          background: transparent;
        }

        ul {
          //background: var(--fluent-greys-white) !important;
        }
      }

      .jqx-tree-item {
        //display: block;
        //width: 100%;
      }
    }
  }
}

.jqx-datatable.jqx-grid {
  background: var(--fluent-greys-white);
  border: 0;

  .jqx-cell {
    padding: 8px 4px;
  }

  .jqx-grid-column-header,
  .jqx-grid-columngroup-header {
    font-family: 'Segoe UI';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--fluent-greys-grey190);
  }

  .jqx-widget-header,
  .jqx-grid-pager,
  .jqx-grid-toolbar {
    background: var(--fluent-greys-grey10);
  }

  .jqx-grid-header {
    background: var(--fluent-greys-white);
    font-family: 'Segoe UI';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--fluent-greys-grey190);
    border-bottom: 2px solid #F8F8F8;
  }

  .jqx-grid-cell,
  .jqx-grid-column-header,
  .jqx-grid-group-cell {
    background-color: var(--fluent-greys-white);
    border: 0;

    &.jqx-grid-cell-selected {
      background-color: var(--fluent-greys-grey10);

      &.jqx-grid-cell-hover {
      }
    }
  }

  .jqx-icon-arrow-right {
    transform: rotate(-90deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    margin-right: 10px;
  }

  .jqx-icon-arrow-down {
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(0deg);
    background-position: center;
    background-repeat: no-repeat;
    margin-right: 10px;
  }

  .jqx-cell.jqx-grid-cell-hover {
    background-color: var(--fluent-greys-grey20);
  }
}

.jqx-grid:not(.jqx-datatable) {
  background: var(--fluent-greys-white);
  border: 0;

  .jqx-grid-card-cell table{
    box-shadow: var(--fluent-box-shadow-4);
    border-radius: 2px;
  }
  .jqx-cell {
    padding: 8px 4px;
  }

  .jqx-grid-column-header,
  .jqx-grid-columngroup-header {
    font-family: 'Segoe UI';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--fluent-greys-grey190);
  }

  .jqx-grid-header {
    font-family: 'Segoe UI';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--fluent-greys-grey190);
    border-bottom: 2px solid #F8F8F8;
  }

  .jqx-grid-cell,
  .jqx-grid-column-header,
  .jqx-grid-group-cell {
    background-color: var(--fluent-greys-white);
    border-color: var(--fluent-body-divider);

    &.jqx-grid-cell-selected {
      background-color: var(--fluent-theme-lighter) !important;
      border-color: var(--fluent-theme-light) !important;
    }
  }

  .jqx-grid-cell-alt {
    background: var(--fluent-greys-grey10) !important;
  }

  .jqx-icon-arrow-right {
    transform: rotate(-90deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
  }

  .jqx-icon-arrow-left {
    transform: rotate(90deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
  }

  .jqx-icon-arrow-down {
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(0deg);
    background-position: center;
    background-repeat: no-repeat;
  }

  .jqx-icon-arrow-up {
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(180deg);
    background-position: center;
    background-repeat: no-repeat;
  }

  .jqx-cell.jqx-grid-cell-hover {
    background-color: var(--fluent-greys-grey20);
  }

  .jqx-widget-header,
  .jqx-grid-pager,
  .jqx-grid-toolbar {
    //background: var(--fluent-greys-grey10);
    background: var(--fluent-greys-white);
  }


  .jqx-checkbox-check-checked {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='20' height='20' rx='2' fill='%230078D4'/%3E%3Cpath d='M16.3516 6.35156L8 14.7109L3.64844 10.3516L4.35156 9.64844L8 13.2891L15.6484 5.64844L16.3516 6.35156Z' fill='white'/%3E%3C/svg%3E");
    background-position: center center;
    background-repeat: no-repeat;
  }
}

.jqx-scheduler {
  .jqx-scheduler-cell-selected {
    background: var(--fluent-theme-lighter) !important;
    border-color: var(--fluent-theme-primary) !important;
  }

  .jqx-scheduler-cell-focus {
    border-color: var(--fluent-theme-primary) !important;
    background: var(--fluent-theme-primary) !important;
  }
}


.jqx-scheduler-edit-dialog-label {
  line-height: 32px !important;
}

.jqx-scheduler-edit-dialog-field {
  min-height: 32px !important;

  .jqx-input {
    min-height: 32px !important;
  }
}

.jqx-docking {
  gap: 15px;
  display: flex;
  padding: 15px;
  box-sizing: border-box;

  .jqx-docking-panel {
    gap: 15px;
    display: flex;
    flex-flow: column;
  }

  .jqx-window {
    box-shadow: var(--fluent-box-shadow-16) !important;
    padding: 10px !important;
  }
}

.jqx-docking-layout {
  .jqx-layout-group-root {
    display: flex;
    gap: 10px;
    box-sizing: border-box;
    background: var(--fluent-greys-white);
  }

  .jqx-window {
    box-shadow: var(--fluent-box-shadow-16) !important;
    padding: 10px !important;
  }

  .jqx-layout-group-default-horizontal > div {
    display: flex;
    flex-flow: column;
    gap: 10px;
    box-sizing: border-box;
  }

  .jqx-layout-ribbon-header {
    border-color: var(--fluent-theme-light);
    background: var(--fluent-theme-lighter);
  }
}

.jqx-splitter {
  border: 0;
}

.jqx-kanban {
  border: 0;

  .jqx-fill-state-normal {
    border-color: var(--fluent-greys-grey50);
    background: var(--fluent-greys-grey20);
  }

  .jqx-widget-content {
    //border-color: var(--fluent-greys-grey20);
    border-color: var(--fluent-greys-white);
  }

  .jqx-kanban-item {
    box-shadow: var(--fluent-box-shadow-4);
    margin-bottom: 10px;
  }

  .jqx-icon-arrow-right {
    transform: rotate(-90deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 150%;
  }

  .jqx-icon-arrow-left {
    transform: rotate(90deg);
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 150%;
  }

  .jqx-icon-arrow-down {
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(0deg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 150%;
  }

  .jqx-icon-arrow-up {
    background-image: url("data:image/svg+xml,%3Csvg width='36' height='42' viewBox='0 0 36 42' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 25.9453L10.1953 18.1406L11.1406 17.1953L18 24.0547L24.8594 17.1953L25.8047 18.1406L18 25.9453Z' fill='%23605E5C'/%3E%3C/svg%3E");
    transform: rotate(180deg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 150%;
  }

  .jqx-kanban-item-keyword {
    border-radius: 100px;
    border-color: var(--fluent-theme-lighter);
    background-color: var(--fluent-theme-lighter);
    padding: 2px 5px;
  }

  .jqx-widget-header {
    border-color: var(--fluent-greys-grey30);
    background: var(--fluent-greys-white);
    border: 1px solid var(--fluent-greys-grey50);
    border-radius: 2px;
  }
}

.jqx-editor {
  border-color: var(--fluent-theme-light);
  background: var(--fluent-theme-lighter);
  border-radius: 2px;

  .jqx-editor-content {
    border-color: var(--fluent-theme-light);
    padding: 10px;
    box-sizing: border-box !important;
  }

  .jqx-editor-toolbar {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }

  .jqx-widget-header {
    border-color: var(--fluent-theme-light);
    background: var(--fluent-theme-lighter);
    border-radius: 2px;
  }
}

.jqx-date-time-input-popup table td input {
  padding: 4px 0px !important;
}