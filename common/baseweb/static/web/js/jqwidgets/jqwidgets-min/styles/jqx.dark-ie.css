﻿.jqx-widget-dark {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
}
.jqx-widget-content-dark {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
}
.jqx-widget-header-dark {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
}

/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-dark {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-dark {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-dark {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-dark {
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*top rounded Corners*/
.jqx-rc-t-dark {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom rounded Corners*/
.jqx-rc-b-dark {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*right rounded Corners*/
.jqx-rc-r-dark {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*left rounded Corners*/
.jqx-rc-l-dark {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*all rounded Corners*/
.jqx-rc-all-dark {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.jqx-widget-dark, .jqx-widget-header-dark, .jqx-fill-state-normal-dark,
.jqx-widget-content-dark, .jqx-fill-state-hover-dark, .jqx-fill-state-pressed-dark {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size:14px;
}

.jqx-widget-dark {
    color: inherit;
}
.jqx-widget-content-dark{ border-color: #252830; color: #ffffff; background-color: #252830;}
.jqx-widget-header-dark{ color: #ffffff; border-color:#3e3e42; background-color:#3e3e42;}
.jqx-scheduler-dark .jqx-grid-column-header-dark, .jqx-scheduler-dark.jqx-widget-dark .jqx-grid-column-header-dark
{
    border-bottom: 1px solid #434857;
}
.jqx-fill-state-normal-dark{ border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-fill-state-hover-dark{  color: #fff; 
    background: #434857;
    border-color: #434857;
}
.jqx-fill-state-focus-dark {  
   border-color: #0077BE;color: #fff; }
.jqx-fill-state-pressed-dark{border-color:#0077BE; color: #fff; background-color:#0077BE;

}
.jqx-fill-state-pressed-dark.jqx-fill-state-hover-dark  {
color: #fff !important; 
}
.jqx-fill-state-disabled-dark {
    color: #898989;
}
.jqx-input-dark {
    border-color: #35353A;
}
.jqx-grid-table-dark {
    font-size: 14px;
   border-right: 1px solid #35353a;
 
}

.jqx-scrollbar-state-normal-dark, .jqx-grid-bottomright-dark, .jqx-panel-bottomright-dark, .jqx-listbox-bottomright-dark{background-color:#3E3E42;}
.jqx-widget-dark .jqx-grid-column-header-dark, .jqx-grid-cell-dark, .jqx-widget-dark .jqx-grid-cell-dark, .jqx-widget-dark .jqx-grid-group-cell-dark, .jqx-grid-group-cell-dark{ border-color: #1C1C1E; background-color: #2A2A2C; color: #a9acb4;}
.jqx-tabs-title-selected-bottom-dark, .jqx-tabs-selection-tracker-bottom-dark, .jqx-tabs-title-selected-top-dark, .jqx-tabs-selection-tracker-top-dark{color: #ffffff; border-color:#35353A; border-bottom:1px solid #252526; background:#0077BE}
.jqx-widget-dark .jqx-grid-cell-alt-dark, .jqx-widget-dark .jqx-grid-cell-sort-dark, .jqx-widget-dark .jqx-grid-cell-pinned-dark, .jqx-widget-dark .jqx-grid-cell-filter-dark, .jqx-grid-cell-sort-alt-dark, .jqx-grid-cell-filter-alt-dark, .jqx-grid-cell-pinned-dark, .jqx-grid-cell-alt-dark, .jqx-grid-cell-sort-dark{ background-color:#3E3E42; color: #fff;}
.jqx-menu-vertical-dark{background: #3E3E42; border-color: #3E3E42;}
.jqx-widget-dark .jqx-grid-cell-dark, .jqx-widget-dark .jqx-grid-column-header-dark, .jqx-widget-dark .jqx-grid-group-cell-dark { border-color: #35353A;}

.jqx-input-dark {
    color: #fff;
}

.jqx-scheduler-cell-hover-dark {
    border-color: #434857 !important;
    background: #434857 !important;
}

.jqx-widget-dark .jqx-grid-column-menubutton-dark, .jqx-widget-dark .jqx-grid-column-sortascbutton-dark, .jqx-widget-dark .jqx-grid-column-sortdescbutton-dark, .jqx-widget-dark .jqx-grid-column-filterbutton-dark {
    background-color: transparent;
    border-color: #35353A;
}
.jqx-window-header-dark, .jqx-input-button-header-dark, .jqx-calendar-title-header-dark, .jqx-grid-dark .jqx-widget-header-dark, .jqx-grid-header-dark, .jqx-grid-column-header-dark { border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-grid-column-menubutton-dark {
    background-image: url('images/metro-icon-down-white.png');
 }
.jqx-calendar-dark > div {
    padding: 10px;
    box-sizing: border-box;
}

.jqx-calendar-cell-today-dark {
    color: #35353A;
}

.jqx-widget-dark .jqx-grid-cell-selected-dark, .jqx-grid-cell-selected-dark{ background-color:#0077BE; color: #222830; border-color: #0077BE; }

.jqx-grid-cell-selected-dark.jqx-grid-cell-edit-dark{
     border-color: #333 !important;
     background-color: #fff !important;
 }

.jqx-widget-dark .jqx-grid-cell-hover-dark, .jqx-grid-cell-hover-dark{ background-color:#434857; border-color: #434857;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-dark {
    background-image: url('images/metro-icon-up-white.png');
 }
.jqx-grid-column-sortdescbutton-dark {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-checkbox-check-checked-dark{background:transparent url(./images/check_white.png) center center no-repeat}
.jqx-checkbox-check-indeterminate-dark{background:transparent url(./images/check_indeterminate_white.png) center center no-repeat}
.jqx-checkbox-hover-dark, .jqx-radiobutton-hover-dark {
    background-color: #3E3E42;
    border-color: #3E3E42;
}
.jqx-radiobutton-check-checked-dark {
    background: #fff;
    border-color: #fff;
}

.jqx-scrollbar-thumb-state-normal-horizontal-dark, .jqx-scrollbar-thumb-state-normal-dark {
    background: #686868; border-color: #686868;
}
.jqx-scrollbar-thumb-state-hover-horizontal-dark, .jqx-scrollbar-thumb-state-hover-dark {
    background: #9E9E9E; border-color: #9E9E9E;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-dark, .jqx-scrollbar-thumb-state-pressed-dark {
    background: #ffffff; border-color: #ffffff;
}
.jqx-scrollbar-button-state-normal-dark
{
    border: 1px solid #3E3E42; 
    background: #3E3E42;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-dark
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-dark
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}

/*icons*/
.jqx-window-collapse-button-dark
{
    background-image: url(./images/metro-icon-up-white.png);
}
.jqx-window-collapse-button-collapsed-dark {
  background-image: url(./images/metro-icon-down-white.png);
}
.jqx-icon-arrow-up-dark, .jqx-expander-arrow-bottom-dark, .jqx-menu-item-arrow-up-dark
{
    background-image: url('images/metro-icon-up-white.png');
}
.jqx-icon-arrow-down-dark, .jqx-expander-arrow-top-dark, .jqx-tree-item-arrow-expand-dark, .jqx-tree-item-arrow-expand-hover-dark, .jqx-menu-item-arrow-down-dark
{
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-icon-arrow-left-dark, .jqx-menu-item-arrow-left-dark
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-icon-arrow-right-dark, .jqx-menu-item-arrow-right-dark, .jqx-tree-item-arrow-collapse-dark, .jqx-tree-item-arrow-collapse-hover-dark
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-tabs-arrow-left-dark, .jqx-tree-item-arrow-collapse-rtl-dark, .jqx-tree-item-arrow-collapse-hover-rtl-dark
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-tabs-arrow-right-dark
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-menu-item-arrow-up-selected-dark, .jqx-icon-arrow-up-selected-dark{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-dark, .jqx-icon-arrow-down-selected-dark{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-dark, .jqx-icon-arrow-left-selected-dark{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-dark, .jqx-icon-arrow-right-selected-dark{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-window-close-button-dark, .jqx-icon-close-dark, .jqx-tabs-close-button-dark, .jqx-tabs-close-button-hover-dark, .jqx-tabs-close-button-selected-dark{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}
.jqx-listbox-feedback-dark {
    border-top: 1px dashed #fff;
}

.jqx-scrollbar-dark .jqx-icon-arrow-up-selected-dark{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-dark .jqx-icon-arrow-down-selected-dark{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-dark .jqx-icon-arrow-left-selected-dark{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-dark .jqx-icon-arrow-right-selected-dark{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}
.jqx-slider-button-dark
{
    border-radius: 100%;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
}
.jqx-input-button-content-dark
{  
    font-size: 10px;
}
.jqx-dropdownlist-state-normal-dark, .jqx-dropdownlist-state-hover-dark, .jqx-dropdownlist-state-selected-dark,
.jqx-scrollbar-button-state-hover-dark, .jqx-scrollbar-button-state-normal-dark, .jqx-scrollbar-button-state-pressed-dark,
.jqx-scrollbar-thumb-state-normal-horizontal-dark, .jqx-scrollbar-thumb-state-hover-horizontal-dark, .jqx-scrollbar-thumb-state-pressed-horizontal-dark,
.jqx-scrollbar-thumb-state-normal-dark, .jqx-scrollbar-thumb-state-pressed-dark, .jqx-button-dark, .jqx-tree-item-hover-dark, .jqx-tree-item-selected-dark,
.jqx-tree-item-dark, .jqx-menu-item-dark, .jqx-menu-item-hover-dark, .jqx-menu-item-selected-dark, .jqx-menu-item-top-dark, .jqx-menu-item-top-hover-dark, 
.jqx-menu-item-top-selected-dark, .jqx-slider-button-dark, .jqx-slider-slider-dark
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-dark {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #35353A;
}
.jqx-switchbutton-thumb-dark {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-dark {
    background: #0077BE;
    color: #0077BE;
}
.jqx-switchbutton-label-off-dark {
    background: #a6a6a6;
    color: #a6a6a6;
}

.jqx-switchbutton-wrapper-dark {
}
.jqx-grid-group-collapse-dark {
    background-image: url(./images/metro-icon-right-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-dark
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-dark, .jqx-grid-group-expand-rtl-dark
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-dark, .jqx-icon-arrow-first-hover-dark, .jqx-icon-arrow-first-selected-dark
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-dark, .jqx-icon-arrow-last-hover-dark, .jqx-icon-arrow-last-selected-dark
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-dark.jqx-grid-cell-selected-dark>.jqx-grid-group-expand-dark,
.jqx-grid-cell-dark.jqx-grid-cell-hover-dark>.jqx-grid-group-expand-dark {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-dark.jqx-grid-cell-selected-dark>.jqx-grid-group-collapse-dark,
.jqx-grid-cell-dark.jqx-grid-cell-hover-dark>.jqx-grid-group-collapse-dark {
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-dark.jqx-grid-cell-selected-dark>.jqx-grid-group-collapse-rtl-dark,
.jqx-grid-cell-dark.jqx-grid-cell-hover-dark>.jqx-grid-group-collapse-rtl-dark {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-dark.jqx-grid-cell-selected-dark>.jqx-grid-group-expand-rtl-dark,
.jqx-grid-cell-dark.jqx-grid-cell-hover-dark>.jqx-grid-group-expand-rtl-dark {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-dark
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-passwordinput-password-icon-dark, .jqx-passwordinput-password-icon-rtl-dark
{
    background-image: url(./images/icon-showpassword-white.png) !important;
    background-repeat: no-repeat !important;
}
.jqx-icon-calendar-dark, .jqx-icon-calendar-hover-dark, .jqx-icon-calendar-pressed-dark {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-dark, .jqx-icon-time-hover-dark, .jqx-icon-time-pressed-dark {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-delete-dark
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-icon-edit-dark
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-icon-save-dark
{
    background-image: url('images/icon-save-white.png');
}
.jqx-icon-cancel-dark
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-icon-search-dark
{
    background-image: url(./images/search_white.png);
}
.jqx-icon-plus-dark
{
    background-image: url(./images/plus_white.png);
}
.jqx-menu-minimized-button-dark {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-dark {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-dark
{
    background-color: #35353A;
}
.jqx-layout-pseudo-window-pin-icon-dark
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-dark
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-month-cell-dark, .jqx-scheduler-time-column-dark, .jqx-scheduler-toolbar-dark
{
    background: #35353A !important;
    color: #fff  !important;
}
.jqx-widget-dark .jqx-scheduler-middle-cell-dark, .jqx-scheduler-middle-cell-dark {
    border-bottom-color: #35353A !important;
}
.jqx-kanban-item-dark {
    box-shadow:none;
}


.jqx-slider-tooltip-dark .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-dark .jqx-tooltip-arrow-l-r {
     background: #0077BE; 
    border-color: #0077BE;
}
.jqx-listitem-state-normal-dark,
.jqx-listitem-state-hover-dark,
.jqx-listitem-state-selected-dark
 {
    padding-top:5px;
    padding-bottom:5px;
    margin:0px;
    border-radius: 0px;
}

.jqx-listitem-state-normal-dark.checkboxes,
.jqx-listitem-state-hover-dark.checkboxes,
.jqx-listitem-state-selected-dark.checkboxes {
    border-radius: 4px;
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-dark, .jqx-menu-item-hover-dark, .jqx-tree-item-hover-dark, .jqx-calendar-cell-hover-dark, .jqx-grid-cell-hover-dark,
.jqx-menu-vertical-dark .jqx-menu-item-top-hover-dark, .jqx-input-popup-dark .jqx-fill-state-hover-dark,
.jqx-input-popup-dark .jqx-fill-state-pressed-dark {
    color: #0077BE !important;
    border-color: #e1f5fe;
    text-decoration: none;
    background-color: #e1f5fe;
    background-repeat: repeat-x;
    outline: 0;
    background: #e1f5fe; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}
.jqx-scheduler-cell-hover-dark {
    border-color: #e3f5fb !important;
    background: #e3f5fb !important;
}
.jqx-grid-table-dark {
    font-size: 14px;
}


.jqx-listitem-state-selected-dark, .jqx-menu-item-selected-dark, .jqx-tree-item-selected-dark, .jqx-calendar-cell-selected-dark, .jqx-grid-cell-selected-dark,
.jqx-menu-vertical-dark .jqx-menu-item-top-selected-dark, .jqx-grid-selectionarea-dark, .jqx-input-button-header-dark, .jqx-input-button-innerHeader-dark {
    color: #ffffff !important;
    background-color: #0077BE;
    *background-color: #0077BE;
    background-repeat: repeat-x;
    border-color: #0077BE !important;
    background: #0077BE; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-scheduler-cell-selected-dark {
    border-color: #0077BE !important;
    background: #0077BE !important;
}


.jqx-dropdownlist-state-normal-dark .jqx-icon-arrow-down-dark,
.jqx-combobox-state-normal-dark .jqx-icon-arrow-down-dark,
.sorticon.descending .jqx-grid-column-sorticon-dark,
.jqx-tree-item-arrow-expand-dark,
 .jqx-expander-header-dark .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-dark .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
    background-image: url('images/metro-icon-down.png');
}

.jqx-tree-item-arrow-collapse-dark
{
    transform: rotate(-90deg);
    background-image: url('images/metro-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-dark .jqx-icon-arrow-down-dark,
.jqx-combobox-state-selected-dark .jqx-icon-arrow-down-dark,
.sorticon.ascending .jqx-grid-column-sorticon-dark
 {
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
}
.jqx-combobox-state-selected-dark .jqx-icon-arrow-down-dark{
    left:-1px;
}

.jqx-primary-dark
{
  color: #0077BE  !important;
  background: #fff  !important;
  border-color: #0077BE!important;
  text-shadow: none !important;
}

.jqx-primary-dark.jqx-combobox-arrow-normal-dark {
   background: #434857 !important;
}
.jqx-primary-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-primary-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-primary-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #0077BE !important;
}
.jqx-primary-dark.jqx-dropdownlist-state-normal-dark,
.jqx-primary-dark.jqx-combobox-arrow-hover-dark,
.jqx-primary-dark.jqx-combobox-arrow-normal-dark,
.jqx-primary-dark.jqx-slider-button-dark,
.jqx-primary-dark.jqx-slider-slider-dark,
.jqx-primary-dark.jqx-action-button-dark,
.jqx-primary-dark:hover,
.jqx-primary-dark:focus,
.jqx-primary-dark:active,
.jqx-primary-dark.active,
.jqx-primary-dark.disabled,
.jqx-primary-dark[disabled] {
 color: #fff  !important;
  background: #0077BE  !important;
  border-color: #0077BE !important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-dark.jqx-primary-dark,
.jqx-primary-dark:active,
.jqx-primary-dark.active {
 color: #fff  !important;
  background-color: #0077BE  !important;
  border-color: #0077BE!important;
  text-shadow: none !important;
}

.jqx-success-dark
{
  color: #5cb85c  !important;
  background: #fff  !important;
  border-color: #5cb85c!important;
  text-shadow: none !important;
}
.jqx-success-dark.jqx-combobox-arrow-normal-dark {
   background: #434857 !important;
}
.jqx-success-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #5cb85c !important;
}
.jqx-success-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-success-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-success-dark.jqx-dropdownlist-state-normal-dark,
.jqx-success-dark.jqx-slider-button-dark,
.jqx-success-dark.jqx-slider-slider-dark,
.jqx-success-dark.jqx-combobox-arrow-hover-dark,
.jqx-success-dark.jqx-combobox-arrow-normal-dark,
.jqx-success-dark.jqx-action-button-dark,
.jqx-success-dark:hover,
.jqx-success-dark:focus,
.jqx-success-dark:active,
.jqx-success-dark.active,
.jqx-success-dark.disabled,
.jqx-success-dark[disabled] {
 color: #fff  !important;
  background: #5cb85c  !important;
  border-color: #5cb85c!important;
  text-shadow: none !important;
}


.jqx-fill-state-pressed-dark.jqx-success-dark,
.jqx-success-dark:active,
.jqx-success-dark.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #5cb85c  !important;
  border-color: #5cb85c!important;
}

.jqx-inverse-dark
{
  text-shadow: none !important;
  color: #666  !important;
  background: #fff  !important;
  border-color: #cccccc!important;
}
.jqx-inverse-dark.jqx-combobox-arrow-normal-dark {
   background: #666 !important;
}
.jqx-inverse-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #666 !important;
}
.jqx-inverse-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-inverse-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-inverse-dark.jqx-dropdownlist-state-normal-dark,
.jqx-inverse-dark.jqx-slider-button-dark,
.jqx-inverse-dark.jqx-slider-slider-dark,
.jqx-inverse-dark.jqx-combobox-arrow-hover-dark,
.jqx-inverse-dark.jqx-combobox-arrow-normal-dark,
.jqx-inverse-dark.jqx-action-button-dark,
.jqx-inverse-dark:hover,
.jqx-inverse-dark:focus,
.jqx-inverse-dark:active,
.jqx-inverse-dark.active,
.jqx-inverse-dark.disabled,
.jqx-inverse-dark[disabled] {
  text-shadow: none !important;
 color: #666  !important;
  background: #cccccc  !important;
  border-color: #cccccc!important;
}

.jqx-fill-state-pressed-dark.jqx-inverse-dark,
.jqx-inverse-dark:active,
.jqx-inverse-dark.active {
  text-shadow: none !important;
 color: #666  !important;
  background: #cccccc  !important;
  border-color: #cccccc!important;
}


.jqx-danger-dark
{
  text-shadow: none !important;
  color: #d9534f  !important;
  background: #fff  !important;
  border-color: #d9534f!important;
}
.jqx-danger-dark.jqx-combobox-arrow-normal-dark {
   background: #434857 !important;
}
.jqx-danger-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-danger-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-danger-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #d9534f !important;
}
.jqx-danger-dark.jqx-dropdownlist-state-normal-dark,
.jqx-danger-dark.jqx-slider-button-dark,
.jqx-danger-dark.jqx-slider-slider-dark,
.jqx-danger-dark.jqx-combobox-arrow-hover-dark,
.jqx-danger-dark.jqx-action-button-dark,
.jqx-danger-dark.jqx-combobox-arrow-normal-dark,
.jqx-danger-dark:hover,
.jqx-danger-dark:focus,
.jqx-danger-dark:active,
.jqx-danger-dark.active,
.jqx-danger-dark.disabled,
.jqx-danger-dark[disabled] {
  text-shadow: none !important;
 color: #fff  !important;
  background: #d9534f  !important;
  border-color: #d9534f!important;
}

.jqx-fill-state-pressed-dark.jqx-danger-dark,
.jqx-danger-dark:active,
.jqx-danger-dark.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #d9534f  !important;
  border-color: #d9534f!important;
}


.jqx-warning-dark
{
  text-shadow: none !important;
  color: #f0ad4e  !important;
  background: #fff  !important;
  border-color: #f0ad4e!important;
}
.jqx-warning-dark.jqx-combobox-arrow-normal-dark {
   background: #434857 !important;
}
.jqx-warning-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-warning-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-warning-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #f0ad4e !important;
}
.jqx-warning-dark.jqx-dropdownlist-state-normal-dark,
.jqx-warning-dark.jqx-slider-button-dark,
.jqx-warning-dark.jqx-slider-slider-dark,
.jqx-warning-dark.jqx-combobox-arrow-hover-dark,
.jqx-warning-dark.jqx-combobox-arrow-normal-dark,
.jqx-warning-dark.jqx-action-button-dark,
.jqx-warning-dark:hover,
.jqx-warning-dark:focus,
.jqx-warning-dark:active,
.jqx-warning-dark.active,
.jqx-warning-dark.disabled,
.jqx-warning-dark[disabled] {
  text-shadow: none !important;
 color: #fff  !important;
  background: #f0ad4e  !important;
  border-color: #f0ad4e!important;
}

.jqx-fill-state-pressed-dark.jqx-warning-dark,
.jqx-warning-dark:active,
.jqx-warning-dark.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #f0ad4e  !important;
  border-color: #f0ad4e!important;
}


.jqx-info-dark
{
  text-shadow: none !important;
  color: #5bc0de  !important;
  background: #fff  !important;
  border-color: #5bc0de!important;
}
.jqx-info-dark.jqx-combobox-arrow-normal-dark {
   background: #434857 !important;
}
.jqx-info-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-down-dark {
   background-image: url('images/metro-icon-down-white.png')
}
.jqx-info-dark.jqx-combobox-arrow-normal-dark .jqx-icon-arrow-up-dark {
   background-image: url('images/metro-icon-up-white.png')
}
.jqx-info-dark.jqx-combobox-arrow-normal-dark.jqx-combobox-arrow-selected-dark  {
   background: #5bc0de !important;
}
.jqx-info-dark.jqx-dropdownlist-state-normal-dark,
.jqx-info-dark.jqx-slider-button-dark,
.jqx-info-dark.jqx-slider-slider-dark,
.jqx-info-dark.jqx-combobox-arrow-hover-dark,
.jqx-info-dark.jqx-combobox-arrow-normal-dark,
.jqx-info-dark.jqx-action-button-dark,
.jqx-info-dark:hover,
.jqx-info-dark:focus,
.jqx-info-dark:active,
.jqx-info-dark.active,
.jqx-info-dark.disabled,
.jqx-info-dark[disabled] {
 color: #fff  !important;
  background: #5bc0de  !important;
  border-color: #5bc0de!important;
  text-shadow: none !important;
}

.jqx-fill-state-pressed-dark.jqx-info-dark,
.jqx-info-dark:active,
.jqx-info-dark.active {
  text-shadow: none !important;
 color: #fff  !important;
  background: #5bc0de  !important;
  border-color: #5bc0de!important;
}

.jqx-primary .jqx-icon-arrow-down-dark, .jqx-warning .jqx-icon-arrow-down-dark, .jqx-danger .jqx-icon-arrow-down-dark, .jqx-success .jqx-icon-arrow-down-dark, .jqx-info .jqx-icon-arrow-down-dark {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-selected-dark, .jqx-warning .jqx-icon-arrow-down-selected-dark, .jqx-danger .jqx-icon-arrow-down-selected-dark, .jqx-success .jqx-icon-arrow-down-selected-dark, .jqx-info .jqx-icon-arrow-down-selected-dark {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-hover-dark, .jqx-warning .jqx-icon-arrow-down-hover-dark, .jqx-danger .jqx-icon-arrow-down-hover-dark, .jqx-success .jqx-icon-arrow-down-hover-dark, .jqx-info .jqx-icon-arrow-down-hover-dark {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-up-dark, .jqx-warning .jqx-icon-arrow-up-dark, .jqx-danger .jqx-icon-arrow-up-dark, .jqx-success .jqx-icon-arrow-up-dark, .jqx-info .jqx-icon-arrow-up-dark {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-selected-dark, .jqx-warning .jqx-icon-arrow-up-selected-dark, .jqx-danger .jqx-icon-arrow-up-selected-dark, .jqx-success .jqx-icon-arrow-up-selected-dark, .jqx-info .jqx-icon-arrow-up-selected-dark {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-hover-dark, .jqx-warning .jqx-icon-arrow-up-hover-dark, .jqx-danger .jqx-icon-arrow-up-hover-dark, .jqx-success .jqx-icon-arrow-up-hover-dark, .jqx-info .jqx-icon-arrow-up-hover-dark {
  background-image: url('images/metro-icon-up-white.png');
}


.jqx-primary-item-dark .jqx-listitem-state-hover, .jqx-primary-item-dark .jqx-menu-item-dark-hover, .jqx-primary-item-dark .jqx-tree-item-dark-hover, .jqx-primary-item-dark .jqx-calendar-cell-hover, .jqx-primary-item-dark .jqx-grid-cell-hover,
.jqx-primary-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-primary-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-primary-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-primary-item-dark .jqx-listitem-state-selected, .jqx-primary-item-dark .jqx-menu-item-dark-selected, .jqx-primary-item-dark .jqx-tree-item-dark-selected, .jqx-primary-item-dark .jqx-calendar-cell-selected, .jqx-primary-item-dark .jqx-grid-cell-selected,
.jqx-primary-item-dark .jqx-menu-vertical .jqx-primary-item-dark .jqx-menu-item-dark-top-selected, .jqx-primary-item-dark .jqx-grid-selectionarea, .jqx-primary-item-dark .jqx-input-button-header, .jqx-primary-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-warning-item-dark .jqx-listitem-state-hover, .jqx-warning-item-dark .jqx-menu-item-dark-hover, .jqx-warning-item-dark .jqx-tree-item-dark-hover, .jqx-warning-item-dark .jqx-calendar-cell-hover, .jqx-warning-item-dark .jqx-grid-cell-hover,
.jqx-warning-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-warning-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-warning-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-warning-item-dark .jqx-listitem-state-selected, .jqx-warning-item-dark .jqx-menu-item-dark-selected, .jqx-warning-item-dark .jqx-tree-item-dark-selected, .jqx-warning-item-dark .jqx-calendar-cell-selected, .jqx-warning-item-dark .jqx-grid-cell-selected,
.jqx-warning-item-dark .jqx-menu-vertical .jqx-warning-item-dark .jqx-menu-item-dark-top-selected, .jqx-warning-item-dark .jqx-grid-selectionarea, .jqx-warning-item-dark .jqx-input-button-header, .jqx-warning-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-danger-item-dark .jqx-listitem-state-hover, .jqx-danger-item-dark .jqx-menu-item-dark-hover, .jqx-danger-item-dark .jqx-tree-item-dark-hover, .jqx-danger-item-dark .jqx-calendar-cell-hover, .jqx-danger-item-dark .jqx-grid-cell-hover,
.jqx-danger-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-danger-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-danger-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-danger-item-dark .jqx-listitem-state-selected, .jqx-danger-item-dark .jqx-menu-item-dark-selected, .jqx-danger-item-dark .jqx-tree-item-dark-selected, .jqx-danger-item-dark .jqx-calendar-cell-selected, .jqx-danger-item-dark .jqx-grid-cell-selected,
.jqx-danger-item-dark .jqx-menu-vertical .jqx-danger-item-dark .jqx-menu-item-dark-top-selected, .jqx-danger-item-dark .jqx-grid-selectionarea, .jqx-danger-item-dark .jqx-input-button-header, .jqx-danger-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-success-item-dark .jqx-listitem-state-hover, .jqx-success-item-dark .jqx-menu-item-dark-hover, .jqx-success-item-dark .jqx-tree-item-dark-hover, .jqx-success-item-dark .jqx-calendar-cell-hover, .jqx-success-item-dark .jqx-grid-cell-hover,
.jqx-success-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-success-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-success-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-success-item-dark .jqx-listitem-state-selected, .jqx-success-item-dark .jqx-menu-item-dark-selected, .jqx-success-item-dark .jqx-tree-item-dark-selected, .jqx-success-item-dark .jqx-calendar-cell-selected, .jqx-success-item-dark .jqx-grid-cell-selected,
.jqx-success-item-dark .jqx-menu-vertical .jqx-success-item-dark .jqx-menu-item-dark-top-selected, .jqx-success-item-dark .jqx-grid-selectionarea, .jqx-success-item-dark .jqx-input-button-header, .jqx-success-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-info-item-dark .jqx-listitem-state-hover, .jqx-info-item-dark .jqx-menu-item-dark-hover, .jqx-info-item-dark .jqx-tree-item-dark-hover, .jqx-info-item-dark .jqx-calendar-cell-hover, .jqx-info-item-dark .jqx-grid-cell-hover,
.jqx-info-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-info-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-info-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-info-item-dark .jqx-listitem-state-selected, .jqx-info-item-dark .jqx-menu-item-dark-selected, .jqx-info-item-dark .jqx-tree-item-dark-selected, .jqx-info-item-dark .jqx-calendar-cell-selected, .jqx-info-item-dark .jqx-grid-cell-selected,
.jqx-info-item-dark .jqx-menu-vertical .jqx-info-item-dark .jqx-menu-item-dark-top-selected, .jqx-info-item-dark .jqx-grid-selectionarea, .jqx-info-item-dark .jqx-input-button-header, .jqx-info-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-inverse-item-dark .jqx-listitem-state-hover, .jqx-inverse-item-dark .jqx-menu-item-dark-hover, .jqx-inverse-item-dark .jqx-tree-item-dark-hover, .jqx-inverse-item-dark .jqx-calendar-cell-hover, .jqx-inverse-item-dark .jqx-grid-cell-hover,
.jqx-inverse-item-dark .jqx-menu-vertical .jqx-menu-item-dark-top-hover, .jqx-inverse-item-dark .jqx-input-popup .jqx-fill-state-hover,
.jqx-inverse-item-dark .jqx-input-popup .jqx-fill-state-pressed {
    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}
.jqx-inverse-item-dark .jqx-listitem-state-selected, .jqx-inverse-item-dark .jqx-menu-item-dark-selected, .jqx-inverse-item-dark .jqx-tree-item-dark-selected, .jqx-inverse-item-dark .jqx-calendar-cell-selected, .jqx-inverse-item-dark .jqx-grid-cell-selected,
.jqx-inverse-item-dark .jqx-menu-vertical .jqx-inverse-item-dark .jqx-menu-item-dark-top-selected, .jqx-inverse-item-dark .jqx-grid-selectionarea, .jqx-inverse-item-dark .jqx-input-button-header, .jqx-inverse-item-dark .jqx-input-button-innerHeader {

    background-color: #434857 !important;
    color: #fff !important;
    text-shadow: none !important;
    border-color:  #434857 !important;
}


 .jqx-grid-column-menubutton-dark {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
 }
.jqx-item-dark .jqx-grid-sortasc-icon
 {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-dark .jqx-grid-sortdesc-icon
 {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
  /*applied to the grid menu's sort remove item/*/
.jqx-item-dark .jqx-grid-sortremove-icon
 {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
 }
 /*applied to the timepicker*/ 
 .jqx-time-picker .jqx-header .jqx-hour-container-dark:focus {
    outline: 1px solid rgb(0, 119, 190) !important;
}
.jqx-time-picker .jqx-header .jqx-minute-container-dark:focus {
    outline: 1px solid rgb(0, 119, 190) !important;
}
.jqx-time-picker .jqx-header .jqx-am-container-dark:focus {
    outline: 1px solid rgb(0, 119, 190) !important;
}
.jqx-time-picker .jqx-header .jqx-pm-container-dark:focus {
    outline: 1px solid rgb(0, 119, 190) !important;
}
.jqx-svg-picker-dark:focus {
	border: 1px solid rgb(0, 119, 190) !important;
}
.jqx-container-dark {
	border-radius: inherit;
}
.jqx-time-picker[view="portrait"] .jqx-header-dark {
	border-top-left-radius: inherit;
	border-top-right-radius: inherit;
}
.jqx-time-picker[view="portrait"] .jqx-main-container-dark {
	border-bottom-left-radius: inherit;
	border-bottom-right-radius: inherit;
}
.jqx-time-picker[view="landscape"] .jqx-header-dark {
	border-top-left-radius: inherit;
	border-bottom-left-radius: inherit;
}
.jqx-time-picker[view="landscape"] .jqx-main-container-dark {
	border-top-right-radius: inherit;
	border-bottom-right-radius: inherit;
}
.jqx-time-picker .jqx-main-container-dark {
    background-color: rgb(37, 40, 48);
}
.jqx-time-picker .jqx-label-dark {
    fill: white;
    opacity: 0.5;
}
.jqx-time-picker .jqx-svg-picker-dark:focus {
    border: 1px solid rgb(139, 139, 139);
    outline: none;
}
.jqx-time-picker .jqx-label-dark.jqx-selected {
    opacity: 1;
}
