﻿.jqx-rc-tl-android
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-android
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-android
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-android
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-android
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-android
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-android
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-android
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-android
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-android {
    font-size: 16px; 
    font-family: Roboto,HelveticaNeue,sans-serif;
    color: inherit;
}
input[type=text].jqx-input-android, input[type=password].jqx-input-android, .jqx-input-group-addon-android{
   font-size: 16px; 
   font-family: Roboto,HelveticaNeue,sans-serif;
   border-color: #636466;
}
.jqx-widget-content-android{font-size: 16px;  font-family: Roboto,HelveticaNeue,sans-serif; border-color: #3c4648; color: #ffffff; background-color: #000;}
.jqx-widget-header-android{
    font-size: 16px; 
    font-family: Roboto,HelveticaNeue,sans-serif; 
    color: #ffffff; 
    border-color:#000;
    border-bottom-color: #32abda;
    background: #000;
}
.jqx-fill-state-normal-android, .jqx-fill-state-hover-android{font-size: 16px;  font-family: Roboto,HelveticaNeue,sans-serif; border-color: #1C1C1E; color: #ffffff; background: #3E3E42;}

.jqx-button-android, .jqx-grid-pager-number-android {
   background: #4b4c4e;
   border-color: #636466;
   color: #fbfbfd;
}
.jqx-fill-state-focus-android {
    border-color: #3C4648;
}
.jqx-listitem-state-normal-android, .jqx-listmenu-item-android {
    background: #000;
    border-top: 1px solid #191919;
}
.jqx-listmenu-header-label-android {
    font-size: 24px;
    margin-left: -5px;
}
.jqx-listmenu-item-label-android {
    display: inline-block;
    width: 100%;
    height: 100%;
}
.jqx-listmenu-item-android a:link, .jqx-listmenu-item-android a:visited {
    display: inline-block;
    text-decoration: none;
    color: inherit;
    font-size: 16px;
    font-family: Roboto,HelveticaNeue,sans-serif;
    color: #fff;
    width: 100%;
    height: 100%;
    padding: 15px;
}
.jqx-listmenu-separator-mobile {
    font-size: 16px; 
    font-family: Roboto,HelveticaNeue,sans-serif;
}

.jqx-listmenu-item-android {
    padding: 0px;
}

.jqx-listitem-state-normal-touch-android {
    padding-top: 5px;
    padding-bottom: 5px;
}
.jqx-listitem-element:first-child .jqx-listitem-state-normal-android {
    border-top: 1px solid #000;
}

.jqx-listitem-state-hover-android {
    background: #000;
    border-color: #000;
    border-bottom-color: #191919;
}

.jqx-listmenu-auto-separator-android, .jqx-listitem-state-group-android {
    background: #293943  !important;
}
.jqx-fill-state-pressed-android{border-color:#10618c; color: #fff; background-color:#10618c;

}
.jqx-fill-state-disabled-android {
    color: #898989;
}
.jqx-scrollbar-state-normal-android, .jqx-grid-bottomright-android, .jqx-panel-bottomright-android, .jqx-listbox-bottomright-android{background-color:#3E3E42;}
.jqx-widget-android .jqx-grid-column-header-android, .jqx-grid-cell-android, .jqx-widget-android .jqx-grid-cell-android, .jqx-widget-android .jqx-grid-group-cell-android, .jqx-grid-group-cell-android{font-size: 16px;  font-family: Roboto,HelveticaNeue,sans-serif; border-color: #1C1C1E; background-color: #000; color: #fff;}
.jqx-widget-android .jqx-grid-cell-alt-android, .jqx-widget-android .jqx-grid-cell-sort-android, .jqx-widget-android .jqx-grid-cell-pinned-android, .jqx-widget-android .jqx-grid-cell-filter-android, .jqx-grid-cell-sort-alt-android, .jqx-grid-cell-filter-alt-android, .jqx-grid-cell-pinned-android, .jqx-grid-cell-alt-android, .jqx-grid-cell-sort-android{border-color: #202023; background-color:#3E3E42; color: #fff;}
.jqx-menu-vertical-android{background: #3E3E42; border-color: #3E3E42;}
.jqx-widget-android .jqx-grid-cell-android, .jqx-widget-android .jqx-grid-column-header-android, .jqx-widget-android .jqx-grid-group-cell-android { border-color: #202023;}

.jqx-widget-android .jqx-grid-column-menubutton-android, .jqx-widget-android .jqx-grid-column-sortascbutton-android, .jqx-widget-android .jqx-grid-column-sortdescbutton-android, .jqx-widget-android .jqx-grid-column-filterbutton-android {
    background-color: transparent;
    border-color: #010f18;
}
.jqx-grid-cell-filter-row-android, .jqx-grid-android .jqx-widget-header-android, .jqx-grid-header-android, .jqx-grid-column-header-android {font-size: 16px;  font-family: Roboto,HelveticaNeue,sans-serif; 
    border-color:#000 !important;
    border-bottom-color: #32b3e3 !important;
    background: #000 !important;
}
.jqx-grid-cell-filter-row-android {
   border-top: 2px solid transparent;
   border-bottom: 1px solid #32b3e3 !important;
   margin-top: -1px;
}

.jqx-widget-header-android .jqx-calendar-title-header-android, .jqx-calendar-title-header-android, .jqx-input-button-header-android, .jqx-window-header-android {
   background: #262626;
   border-color: #262626;
   border-bottom-color: #262626 !important;
}
.jqx-window-content-android {
    background: #262626;
 }
.jqx-calendar-month-android {
    border-top: 1px solid #262626;
   background: #262626;
 }
.jqx-calendar-column-header-android {
   background: #262626;
   border-color: #262626;
 }
.jqx-grid-column-menubutton-android {
    background-image: url('images/metro-icon-down-white.png');
 }
.jqx-input-android {
    border-color: #3C4648;
}
.jqx-widget-android .jqx-grid-cell-selected-android, .jqx-grid-cell-selected-android{ background-color:#10638d !important; border-color: #10638d !important; font-size: 16px;  color:#fff !important}
.jqx-widget-android .jqx-grid-cell-hover-android, .jqx-grid-cell-hover-android{ background-color:#3E3E42;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-android {
    background-image: url('images/metro-icon-up-white.png');
 }
.jqx-grid-column-sortdescbutton-android {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-checkbox-default-android {
    background: #000;
    border-color: #636466;
}
.jqx-checkbox-check-checked-android{position: relative; background:transparent url(./images/check_lightblue.png) center center no-repeat}
.jqx-checkbox-check-indeterminate-android{background:transparent url(./images/check_lightblue_indeterminate.png) center center no-repeat}
.jqx-checkbox-hover-android, .jqx-radiobutton-hover-android {

}
.jqx-radiobutton-check-checked-android {
    background: #33b5e5;
    border-color: #33b5e5;
}

.jqx-scrollbar-thumb-state-normal-horizontal-android, .jqx-scrollbar-thumb-state-normal-android {
    background: #686868; border-color: #686868;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}
.jqx-scrollbar-thumb-state-hover-horizontal-android, .jqx-scrollbar-thumb-state-hover-android {
    background: #9E9E9E; border-color: #9E9E9E;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-android, .jqx-scrollbar-thumb-state-pressed-android {
    background: #ffffff; border-color: #ffffff;
}
.jqx-scrollbar-button-state-normal-android
{
    border: 1px solid #3E3E42; 
    background: #3E3E42;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-android
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-android
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}

/*icons*/
.jqx-window-collapse-button-android
{
    background-image: url(./images/metro-icon-up-white.png);
}
.jqx-window-collapse-button-collapsed-android {
  background-image: url(./images/metro-icon-down-white.png);
}
.jqx-icon-arrow-up-android, .jqx-expander-arrow-bottom-android, .jqx-menu-item-arrow-up-android
{
    background-image: url('images/metro-icon-up-white.png');
}
.jqx-icon-arrow-down-android, .jqx-expander-arrow-top-android, .jqx-tree-item-arrow-expand-android, .jqx-tree-item-arrow-expand-hover-android, .jqx-menu-item-arrow-down-android
{
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-icon-arrow-left-android, .jqx-menu-item-arrow-left-android
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-icon-arrow-right-android, .jqx-menu-item-arrow-right-android, .jqx-tree-item-arrow-collapse-android, .jqx-tree-item-arrow-collapse-hover-android
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-tabs-arrow-left-android, .jqx-tree-item-arrow-collapse-rtl-android, .jqx-tree-item-arrow-collapse-hover-rtl-android
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-tabs-arrow-right-android
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-menu-item-arrow-up-selected-android, .jqx-icon-arrow-up-selected-android{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-android, .jqx-icon-arrow-down-selected-android{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-android, .jqx-icon-arrow-left-selected-android{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-android, .jqx-icon-arrow-right-selected-android{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-window-close-button-android, .jqx-icon-close-android, .jqx-tabs-close-button-android, .jqx-tabs-close-button-hover-android, .jqx-tabs-close-button-selected-android{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}
.jqx-listbox-feedback-android {
    border-top: 1px dashed #fff;
}

.jqx-scrollbar-android .jqx-icon-arrow-up-selected-android{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-android .jqx-icon-arrow-down-selected-android{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-android .jqx-icon-arrow-left-selected-android{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-android .jqx-icon-arrow-right-selected-android{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}

.jqx-input-button-content-android
{  
    background: #262626;
    border-color: #262626; 
    font-size: 13px;
}
.jqx-input-button-header-android {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}
.jqx-button-android {
   padding: 10px 7px;
}
.jqx-slider-button-android
{
    border-radius: 100%;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    padding: 4px !important;
}
.jqx-slider-slider-android {
    width: 25px;
    height: 25px;
    margin-top: -10px;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background: rgba(51,181,229,0.6);
    background-image: -moz-radial-gradient(#33b5e5 10%,transparent 90%);
    background-image: radial-gradient(#33b5e5 10%,transparent 90%);
    border: none;
}
.jqx-slider-rangebar-android {
    background: rgba(51,181,229,0.5);
 }
.jqx-dropdownlist-state-normal-android, .jqx-dropdownlist-state-hover-android, .jqx-dropdownlist-state-selected-android,
.jqx-scrollbar-button-state-hover-android, .jqx-scrollbar-button-state-normal-android, .jqx-scrollbar-button-state-pressed-android,
.jqx-scrollbar-thumb-state-normal-horizontal-android, .jqx-scrollbar-thumb-state-hover-horizontal-android, .jqx-scrollbar-thumb-state-pressed-horizontal-android,
.jqx-scrollbar-thumb-state-normal-android, .jqx-scrollbar-thumb-state-pressed-android, .jqx-button-android, .jqx-tree-item-hover-android, .jqx-tree-item-selected-android,
.jqx-tree-item-android, .jqx-menu-item-android, .jqx-menu-item-hover-android, .jqx-menu-item-selected-android, .jqx-menu-item-top-android, .jqx-menu-item-top-hover-android, 
.jqx-menu-item-top-selected-android, .jqx-slider-button-android, .jqx-slider-slider-android
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-android {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #010f18;
}
.jqx-switchbutton-thumb-android {
    width: 16px;
    background: #222222;
    border: 1px solid #222222;
}
.jqx-switchbutton-label-on-android {
    background: #32b3e3;
    color: #fff;
}
.jqx-switchbutton-label-off-android {
    background: #444;
    color: #fff;
}
.jqx-tabs-android {
    border: 1px solid #1a1a1a;
}
.jqx-tabs-title-android {
    background: transparent;
    border-color: transparent;
    border-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    -webkit-border-radius: 0px;
    -webkit-border-top-left-radius: 0px;
    -webkit-border-top-right-radius: 0px;
    -webkit-border-bottom-left-radius: 0px;
    -webkit-border-bottom-right-radius: 0px;
    -moz-border-radius: 0px;
    -moz-border-top-left-radius: 0px;
    -moz-border-top-right-radius: 0px;
    -moz-border-bottom-left-radius: 0px;
    -moz-border-bottom-right-radius: 0px;
}
.jqx-tabs-header-android, .jqx-tabs-arrow-background {
    background: #000;
    color: #fff;
    border-bottom-color: #636466 !important;
}
.jqx-tabs-header-bottom-android {
    background: #000;
    color: #fff;
    border-top-color: #636466 !important;
    border-top-style: solid;
    border-top-width: 1px;
}
.jqx-tabs-title-selected-top-android, .jqx-tabs-selection-tracker-top-android {
    border-bottom: 1px solid #000;
    border-bottom: 2px solid #32b3e3;
    color: #fff;
    background: #000;
    box-shadow: 0 0px 5px #000;
    -webkit-box-shadow: 0 0px 5px #000;
    padding-top: 5px;
    padding-bottom: 5px;
}

.jqx-tabs-title-selected-bottom-android, .jqx-tabs-selection-tracker-bottom-android {
    border-top: 2px solid #32b3e3;
    color: #fff;
   background: #000;  
    box-shadow: 0 0px 5px #000;
    -webkit-box-shadow: 0 0px 5px #000;
    padding-top: 6px;
    padding-bottom: 6px;
}
.jqx-tabs-title-hover-top-android, .jqx-tabs-title-hover-bottom-android, .jqx-tabs-header-android
{
    color: #fff;
}
.jqx-switchbutton-wrapper-android {
}
.jqx-icon-arrow-first-android
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-android
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-progressbar-text-android {
    font-size: 16px;
}
.jqx-grid-pager-android .jqx-button-android {
    padding: 3px !important;
}
.jqx-grid-pager-number-android {
    top: 2px;
    position: relative;
    padding: 1px 7px;
}
.jqx-widget-android .jqx-grid-groups-header-android,
.jqx-grid-groups-header-android {
    border-bottom-color: #000 !important;
 }
.jqx-grid-group-collapse-android {
    background-image: url(./images/metro-icon-right-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-android
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-android, .jqx-grid-group-expand-rtl-android
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-tree-grid-expand-button-android {
    margin-top: 0px;
}
.jqx-tree-grid-checkbox-android {
    margin-top: 2px;
}
.jqx-grid-table-android {
    border-color: #000;
}
.jqx-icon-search-android
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-passwordinput-password-icon-android, .jqx-passwordinput-password-icon-rtl-android
{
    background-image: url(./images/icon-showpassword-white.png) !important;
    background-repeat: no-repeat !important;
}
.jqx-icon-calendar-android, .jqx-icon-calendar-hover-android, .jqx-icon-calendar-pressed-android {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-android, .jqx-icon-time-hover-android, .jqx-icon-time-pressed-android {
    background-image: url('images/icon-time-white.png');
}
.jqx-calendar-cell-today-android {
    color: black;
}
.jqx-editor-toolbar-icon-android {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-menu-minimized-button-android {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-file-upload-file-name-android{padding:3px;}
.jqx-file-upload-file-row-android{ height: 40px;}
.jqx-layout-android
{
    background-color: #000;
}
.jqx-layout-pseudo-window-pin-icon-android
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-android
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-android, .jqx-scheduler-time-column-android, .jqx-scheduler-toolbar-details-android {
    color: white !important;
}

.jqx-label-android {
	fill: lightgray;
}