﻿/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-arctic {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-arctic {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-arctic {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-arctic {
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*top rounded Corners*/
.jqx-rc-t-arctic {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
}
/*bottom rounded Corners*/
.jqx-rc-b-arctic {
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*right rounded Corners*/
.jqx-rc-r-arctic {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
/*left rounded Corners*/
.jqx-rc-l-arctic {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
/*all rounded Corners*/
.jqx-rc-all-arctic {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.jqx-widget-arctic, .jqx-widget-header-arctic, .jqx-fill-state-normal-arctic,
.jqx-widget-content-arctic, .jqx-fill-state-hover-arctic, .jqx-fill-state-pressed-arctic {
    -webkit-transition: box-shadow linear 0.2s;
    -moz-transition: box-shadow linear 0.1s;
    -o-transition: box-shadow linear 0.1s;
    transition: box-shadow linear 0.1s;
}

.jqx-widget-content-arctic {
    background-color: #ffffff;
    border-color: #cccccc;
}

.jqx-widget-header-arctic {
    color: #333333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
   	background-color:#f0f0f0; 
   	background:#f0f0f0;
	background:-webkit-gradient(linear, 0 0, 0 100%, from(#f8f8f8), to(#eaeaea));
	background:-moz-linear-gradient(100% 100% 90deg, #eaeaea, #f8f8f8);
	background:-ms-linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);
	background:linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);	
    border-color:#cccccc;
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.jqx-button-arctic, .jqx-fill-state-normal-arctic  {
    color: #333333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
 	background:#f0f0f0;
	background:-webkit-gradient(linear, 0 0, 0 100%, from(#f8f8f8), to(#eaeaea));
	background:-moz-linear-gradient(100% 100% 90deg, #eaeaea, #f8f8f8);
	background:-ms-linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);
	background:linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);	
    border-color: #cccccc;
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.jqx-fill-state-hover-arctic {
    text-shadow: 0 1px 0 rgb(240, 240, 240);
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.3);
    color: black;
    background-color: #f0f0f0;
    background-image: linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -webkit-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -moz-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -o-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    box-shadow: 0 1px 0 #f0f0f0, inset 0 1px 2px #f0f0f0;
    -webkit-box-shadow: 0 1px 0 #f0f0f0, inset 0 1px 2px #f0f0f0;
    -moz-box-shadow: 0 1px 0 #f0f0f0, inset 0 1px 2px #f0f0f0;
}
.jqx-fill-state-pressed-arctic {
    color: #333333;
    background-color: #e6e6e6;
    *background-color: #d9d9d9;
}

.jqx-fill-state-hover-arctic, .jqx-fill-state-focus-arctic {
    color: #333333;
    text-decoration: none;
}

.jqx-fill-state-pressed-arctic {
    background-image: none;
    outline: 0;
    -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.jqx-grid-cell-arctic {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-column-menubutton-arctic {
    background-color: transparent;
    border-color: #cccccc;
}

.jqx-calendar-row-header-arctic, .jqx-calendar-top-left-header-arctic {
    background-color: #f0f0f0;
    border: 0px solid #f2f2f2;
}

.jqx-calendar-column-header-arctic {
    background-color: #FFF;
    border-top: 1px solid #FFF;
    border-bottom: 1px solid #e9e9e9;
}

.jqx-expander-header-arctic {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  	background:#f0f0f0;
	background:-webkit-gradient(linear, 0 0, 0 100%, from(#f8f8f8), to(#eaeaea));
	background:-moz-linear-gradient(100% 100% 90deg, #eaeaea, #f8f8f8);
	background:-ms-linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);
	background:linear-gradient(top, #f8f8f8 0%,#eaeaea 100%);	
    border-color:#cccccc;
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.jqx-ribbon-header-vertical-arctic, .jqx-widget-header-vertical-arctic {
	background:#f0f0f0;
	background:-ms-linear-gradient(left, #f8f8f8 0%,#eaeaea 100%);
	background:linear-gradient(left, #f8f8f8 0%,#eaeaea 100%);	
}

.jqx-scrollbar-state-normal-arctic {
    background-color: #f8f8f8;
    border: 1px solid #f8f8f8;
}

.jqx-scrollbar-thumb-state-normal-arctic, .jqx-scrollbar-thumb-state-normal-horizontal-arctic {
    background: #f5f5f5;
    border-color: #b3b3b3;
}

.jqx-scrollbar-thumb-state-hover-arctic, .jqx-scrollbar-thumb-state-hover-horizontal-arctic {
    background: #e6e6e6;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-arctic {
    background: #f7f7f7 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-arctic, .jqx-splitter-collapse-button-horizontal-arctic {
    background-color: #0081c2;
    background-repeat: repeat-x;
    background-image: linear-gradient(to bottom, #149bdf, #0480be);
}

.jqx-splitter-collapse-button-vertical-arctic, .jqx-progressbar-value-vertical-arctic {
    background-color: #0081c2;
    background-repeat: repeat-x;
    background-image: linear-gradient(to right, #149bdf, #0480be);
}

.jqx-scrollbar-thumb-state-pressed-arctic, .jqx-splitter-splitbar-vertical-arctic, .jqx-splitter-splitbar-horizontal-arctic, .jqx-scrollbar-thumb-state-pressed-horizontal-arctic {
    background: #d9d9d9;
    border-color: #b3b3b3;
     box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-grid-column-sortdescbutton-arctic, jqx-grid-column-filterbutton-arctic, .jqx-grid-column-sortascbutton-arctic {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #cccccc;
}

.jqx-slider-rangebar-arctic {
    background: #cccccc;
}

.jqx-menu-vertical-arctic {
    background: #ffffff;
    filter: none;
}

.jqx-checkbox-check-checked-arctic {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAcklEQVQY02NgwA/YoJgoEA/Es4DYgJBCJSBeD8SboRinBiYg7kZS2IosyQ/Eakh8LySFq4FYHFlxGRBvBOJYqMRqJMU+yApNkSRAeC0Sux3dfSCTetE0wKyXxOWhMKhTYIr9CAUXyJMzgLgBagBBgDPGAI2LGdNt0T1AAAAAAElFTkSuQmCC');
}
.jqx-checkbox-hover {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}
.jqx-combobox-content-arctic, .jqx-input-arctic {
}

.jqx-combobox-content-arctic {
    border-color: #cccccc;
    border-color: rgba(0, 0, 0, 0.25);
}

.jqx-grid-bottomright-arctic, .jqx-panel-bottomright-arctic, .jqx-listbox-bottomright-arctic {
    background-color: #efefef;
}

.jqx-window-arctic, .jqx-tooltip-arctic {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}

.jqx-docking-arctic .jqx-window-arctic {
    box-shadow: none;
}

.jqx-docking-panel-arctic .jqx-window-arctic {
    box-shadow: none;
}

.jqx-radiobutton-arctic {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
}

.jqx-radiobutton-arctic-arctic, .jqx-radiobutton-hover-arctic {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
}

.jqx-radiobutton-check-checked-arctic {
    filter: none;
    background: #666;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-arctic {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-arctic {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-slider-track-horizontal-arctic, .jqx-slider-track-vertical-arctic {
    border-color: #cccccc;
    background: #f0f0f0;
}

.jqx-slider-button-arctic {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-arctic, .jqx-menu-item-hover-arctic, .jqx-tree-item-hover-arctic, .jqx-calendar-cell-hover-arctic, .jqx-grid-cell-hover-arctic,
.jqx-menu-vertical-arctic .jqx-menu-item-top-hover-arctic, .jqx-input-popup-arctic .jqx-fill-state-hover-arctic,
.jqx-input-popup-arctic .jqx-fill-state-pressed-arctic {
    color: #ffffff !important;
    border-color: #316293 #316293 #29547E !important;
    text-decoration: none;
    background-color: #0081c2;
    background-repeat: repeat-x;
    outline: 0;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background: #2f7eb6; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}

.jqx-listitem-state-selected-arctic, .jqx-menu-item-selected-arctic, .jqx-tree-item-selected-arctic, .jqx-calendar-cell-selected-arctic, .jqx-grid-cell-selected-arctic,
.jqx-menu-vertical-arctic .jqx-menu-item-top-selected-arctic, .jqx-grid-selectionarea-arctic, .jqx-input-button-header-arctic, .jqx-input-button-innerHeader-arctic {
    color: #ffffff !important;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #003399;
    *background-color: #003399;
    background-repeat: repeat-x;
    border-color: #316293 #316293 #29547E !important;
    background: #356aa0; /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-cell-arctic .jqx-button-arctic, .jqx-grid-cell-arctic .jqx-button-arctic.jqx-fill-state-hover-arctic, .jqx-grid-cell-arctic .jqx-button-arctic.jqx-fill-state-pressed-arctic {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
   -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.jqx-popup-arctic {
    border: 1px solid #ccc;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}
.jqx-grid-column-sortascbutton-arctic, .jqx-expander-arrow-bottom-arctic, .jqx-window-collapse-button-arctic, .jqx-menu-item-arrow-up-arctic, .jqx-menu-item-arrow-up-selected-arctic, .jqx-menu-item-arrow-top-up-arctic, .jqx-icon-arrow-up-arctic, .jqx-icon-arrow-up-hover-arctic, .jqx-icon-arrow-up-selected-arctic {
    background-image: url('images/icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-arctic .jqx-grid-group-expand-arctic, .jqx-grid-group-expand-arctic, .jqx-grid-column-menubutton-arctic, .jqx-grid-column-sortdescbutton-arctic, .jqx-expander-arrow-top-arctic, .jqx-window-collapse-button-collapsed-arctic, .jqx-menu-item-arrow-down-arctic, .jqx-menu-item-arrow-down-selected-arctic, .jqx-menu-item-arrow-down-arctic, .jqx-icon-arrow-down-arctic, .jqx-icon-arrow-down-hover-arctic, .jqx-icon-arrow-down-selected-arctic {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-arctic, .jqx-menu-item-arrow-left-selected-arctic, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-arctic, .jqx-icon-arrow-down-left-arctic, .jqx-icon-arrow-left-selected-arctic {
    background-image: url('images/icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-arctic .jqx-grid-group-collapse-arctic, .jqx-grid-group-collapse-arctic, .jqx-tabs-arrow-right-arctic, .jqx-menu-item-arrow-right-selected-arctic, .jqx-menu-item-arrow-top-right-arctic, .jqx-icon-arrow-right-arctic, .jqx-icon-arrow-right-hover-arctic, .jqx-icon-arrow-right-selected-arctic {
    background-image: url('images/icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tree-item-arrow-collapse-rtl-arctic, .jqx-tree-item-arrow-collapse-hover-rtl-arctic {
    background-image: url(./images/icon-left.png);
}

.jqx-menu-item-arrow-left-selected-arctic {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-arctic {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-arctic {
    font-size: 10px;
}
.jqx-widget .jqx-grid-cell, .jqx-widget .jqx-grid-column-header, .jqx-widget .jqx-grid-group-cell {border-color: #ccc;}
.jqx-combobox-content-arctic, .jqx-input-arctic {
    border-color: #cccccc;
    color: #555555;
    background-color: #ffffff;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-arctic, .jqx-combobox-arctic, .jqx-combobox-state-normal-arctic {
    border-color: #cccccc;
}

.jqx-combobox-content-focus-arctic, .jqx-combobox-state-focus-arctic, .jqx-fill-state-focus-arctic,
.jqx-numberinput-focus-arctic {
    outline: none;
    border-color: #959595;
}


input[type="text"].jqx-input-arctic, input[type="password"].jqx-input-arctic, input[type="text"].jqx-widget-content-arctic, input[type="textarea"].jqx-widget-content-arctic, textarea.jqx-input-arctic {
    padding-left: 0px !important;
}

input[type="text"].jqx-input-arctic:-moz-placeholder, input[type="text"].jqx-widget-content-arctic:-moz-placeholder, input[type="textarea"].jqx-widget-content-arctic:-moz-placeholder, textarea.jqx-input-arctic:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-arctic:-webkit-input-placeholder, input[type="text"].jqx-widget-content-arctic:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-arctic:-webkit-input-placeholder, textarea.jqx-input-arctic:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-arctic:-ms-input-placeholder, input[type="text"].jqx-widget-content-arctic:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-arctic:-ms-input-placeholder, textarea.jqx-input-arctic:-ms-input-placeholder {
    color: #999999;
}

.jqx-slider-rangebar-arctic {
    border-color: #0081c2;
    background: #0081c2;
}

.jqx-grid-cell-arctic.jqx-grid-cell-selected-arctic>.jqx-grid-group-expand-arctic,
.jqx-grid-cell-arctic.jqx-grid-cell-hover-arctic>.jqx-grid-group-expand-arctic {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-arctic.jqx-grid-cell-selected-arctic>.jqx-grid-group-collapse-arctic,
.jqx-grid-cell-arctic.jqx-grid-cell-hover-arctic>.jqx-grid-group-collapse-arctic {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-arctic.jqx-grid-cell-selected-arctic>.jqx-grid-group-collapse-rtl-arctic,
.jqx-grid-cell-arctic.jqx-grid-cell-hover-arctic>.jqx-grid-group-collapse-rtl-arctic {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-arctic.jqx-grid-cell-selected-arctic>.jqx-grid-group-expand-rtl-arctic,
.jqx-grid-cell-arctic.jqx-grid-cell-hover-arctic>.jqx-grid-group-expand-rtl-arctic {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-title-selected-top-arctic, .jqx-tabs-selection-tracker-top-arctic {
    border-color: #CCCCCC;
    border-bottom: 1px solid #fff;
    text-shadow: 0 1px 0 #f2f2f2;
    filter: none;
    background: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-title-selected-bottom-arctic, .jqx-tabs-selection-tracker-bottom-arctic {
    border-color: #CCCCCC;
    border-top: 1px solid #fff;
    text-shadow: 0 1px 0 #f2f2f2;
    filter: none;
    background: #fff;
   -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-tabs-title-hover-top-arctic, .jqx-tabs-title-hover-bottom-arctic, .jqx-tabs-header-arctic
{
   -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}
.jqx-window-collapse-button-arctic
{
    margin-top: 2px;
}
.jqx-window-collapse-button-collapsed-arctic {
    margin-top: 0px;
}
.jqx-layout-arctic
{
    background-color: #cccccc;
}
.jqx-kanban-column-header-collapsed-arctic {
   background: -moz-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ff3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(248,248,248,1)), color-stop(100%, rgba(234,234,234,1))); /* safari4+,chrome */
    background: -webkit-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* safari5.1+,chrome10+ */
    background: -o-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ie10+ */
    background: linear-gradient(90deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* w3c */
}