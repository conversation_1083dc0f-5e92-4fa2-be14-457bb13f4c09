﻿.jqx-rc-tl-blackberry
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-blackberry
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-blackberry
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-blackberry
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-blackberry
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-blackberry
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-blackberry
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-blackberry
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-blackberry
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-blackberry {
    font-size: 16px;
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
    color: inherit;
}
.jqx-widget-content-blackberry {
   font-family: Slate Pro, Slate, Helvetica, sans-serif;
    background-color: #ffffff;
    border-color: #ccc;
    border-color: rgba(0,0,0,0.2);
    font-size: 16px;
    color: #000;
 }

.jqx-widget-blackberry {
    border-color: #015e98;
}
.jqx-widget-header-blackberry {
    color: #fff;
    background-color: #0076ae;
    border-color: #296b8e;
    font-size: 16px;
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
}
.jqx-grid-cell-blackberry {
    font-size: 16px;
}
.jqx-button-blackberry, .jqx-fill-state-normal-blackberry, .jqx-fill-state-hover-blackberry   {
    border-color: #CECACE;
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
    background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #c6c3c6), color-stop(100%, #e7e7e7));
    background: -webkit-linear-gradient(#c6c3c6, #e7e7e7);
    background: -moz-linear-gradient(#c6c3c6, #e7e7e7);
    background: -o-linear-gradient(#c6c3c6, #e7e7e7);
    background: linear-gradient(#c6c3c6, #e7e7e7);
    font-size: 16px;
    color: #000;
}
.jqx-fill-state-pressed-blackberry, .jqx-combobox-multi-item-blackberry {
    background: #2b85a2;
    border-color: #5e5e5e;
    color: #fff;
    font-size: 16px;
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
}
.jqx-grid-group-column-blackberry {
    color: #fff;
    background: #0076ae;
    border-color: #296b8e;
}
.jqx-radiobutton-default-blackberry {
   background: #e7e7e7; 
}
.jqx-button-blackberry {
    border-width: 2px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
}
.jqx-widget-blackberry .jqx-grid-column-menubutton-blackberry, .jqx-grid-column-menubutton-blackberry {
    background-color: transparent;
    border-color: #0076ae;
}

.jqx-calendar-row-header-blackberry, .jqx-calendar-top-left-header-blackberry {
    background-color: #f0f0f0;
    border: 0px solid rgba(0,0,0,0.2);
}

.jqx-calendar-column-header-blackberry {
    background-color: #FFF;
    border-top: 1px solid #FFF;
    border-bottom: 1px solid #e9e9e9;
}

.jqx-scrollbar-state-normal-blackberry {
    background-color: #f8f8f8;
    border: 1px solid #f8f8f8;
}

.jqx-scrollbar-thumb-state-normal-blackberry, .jqx-scrollbar-thumb-state-normal-horizontal-blackberry {
    background: #9b9b9b;
    border-color: #b3b3b3;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

.jqx-scrollbar-thumb-state-hover-blackberry, .jqx-scrollbar-thumb-state-hover-horizontal-blackberry {
    background: #e6e6e6;
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-blackberry {
    background: #f7f7f7 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-blackberry, .jqx-splitter-collapse-button-horizontal-blackberry {
    background-color: #00aae7;
    background-repeat: repeat-x;
    background-image: linear-gradient(to bottom, #00aae7, #00aae7);
}

.jqx-splitter-collapse-button-vertical-blackberry, .jqx-progressbar-value-vertical-blackberry {
    background-color: #00aae7;
    background-repeat: repeat-x;
    background-image: linear-gradient(to right, #00aae7, #00aae7);
}

.jqx-scrollbar-thumb-state-pressed-blackberry, .jqx-splitter-splitbar-vertical-blackberry, .jqx-splitter-splitbar-horizontal-blackberry, .jqx-scrollbar-thumb-state-pressed-horizontal-blackberry {
    background: #d9d9d9;
    border-color: #b3b3b3;
     box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-grid-column-sortdescbutton-blackberry, jqx-grid-column-filterbutton-blackberry, .jqx-grid-column-sortascbutton-blackberry {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #cccccc;
}

.jqx-slider-rangebar-blackberry {
    background: #cccccc;
}

.jqx-menu-vertical-blackberry {
    background: #ffffff;
    filter: none;
}

.jqx-checkbox-check-checked-blackberry {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAcklEQVQY02NgwA/YoJgoEA/Es4DYgJBCJSBeD8SboRinBiYg7kZS2IosyQ/Eakh8LySFq4FYHFlxGRBvBOJYqMRqJMU+yApNkSRAeC0Sux3dfSCTetE0wKyXxOWhMKhTYIr9CAUXyJMzgLgBagBBgDPGAI2LGdNt0T1AAAAAAElFTkSuQmCC');
}
.jqx-checkbox-hover {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}
.jqx-combobox-content-blackberry, .jqx-input-blackberry {
}

.jqx-combobox-content-blackberry {
    border-color: #cccccc;
    border-color: rgba(0,0,0,0.2);
}

.jqx-grid-bottomright-blackberry, .jqx-panel-bottomright-blackberry, .jqx-listbox-bottomright-blackberry {
    background-color: #efefef;
}
.jqx-listitem-state-normal-touch-blackberry {
    padding-top: 5px;
    padding-bottom: 5px;
}
.jqx-window-blackberry, .jqx-tooltip-blackberry {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}

.jqx-docking-blackberry .jqx-window-blackberry {
    box-shadow: none;
}

.jqx-docking-panel-blackberry .jqx-window-blackberry {
    box-shadow: none;
}

.jqx-radiobutton-blackberry {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
    background: none;
}

.jqx-radiobutton-blackberry-blackberry, .jqx-radiobutton-hover-blackberry {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
}

.jqx-radiobutton-check-checked-blackberry {
    filter: none;
    background: #666;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-blackberry {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-blackberry {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-slider-track-horizontal-blackberry, .jqx-slider-track-vertical-blackberry {
    border-color: #cccccc;
    background: #f0f0f0;
}

.jqx-slider-button-blackberry {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-popup-blackberry {
    border: 1px solid #ccc;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}
.jqx-grid-column-sortascbutton-blackberry, .jqx-expander-arrow-bottom-blackberry, .jqx-window-collapse-button-blackberry, .jqx-menu-item-arrow-up-blackberry, .jqx-menu-item-arrow-up-selected-blackberry, .jqx-menu-item-arrow-top-up-blackberry, .jqx-icon-arrow-up-blackberry, .jqx-icon-arrow-up-hover-blackberry, .jqx-icon-arrow-up-selected-blackberry {
    background-image: url('images/icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-blackberry .jqx-grid-group-expand-blackberry, .jqx-grid-group-expand-blackberry, .jqx-grid-column-menubutton-blackberry, .jqx-grid-column-sortdescbutton-blackberry, .jqx-expander-arrow-top-blackberry, .jqx-window-collapse-button-collapsed-blackberry, .jqx-menu-item-arrow-down-blackberry, .jqx-menu-item-arrow-down-selected-blackberry, .jqx-menu-item-arrow-down-blackberry, .jqx-icon-arrow-down-blackberry, .jqx-icon-arrow-down-hover-blackberry, .jqx-icon-arrow-down-selected-blackberry {
    background-image: url('images/icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-blackberry, .jqx-menu-item-arrow-left-selected-blackberry, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-blackberry, .jqx-icon-arrow-down-left-blackberry, .jqx-icon-arrow-left-selected-blackberry {
    background-image: url('images/icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-blackberry .jqx-grid-group-collapse-blackberry, .jqx-grid-group-collapse-blackberry, .jqx-tabs-arrow-right-blackberry, .jqx-menu-item-arrow-right-selected-blackberry, .jqx-menu-item-arrow-top-right-blackberry, .jqx-icon-arrow-right-blackberry, .jqx-icon-arrow-right-hover-blackberry, .jqx-icon-arrow-right-selected-blackberry {
    background-image: url('images/icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-item-arrow-collapse-blackberry {
    background-image: url('images/icon-right.png') !important;
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-item-arrow-collapse-rtl-blackberry, .jqx-tree-item-arrow-collapse-hover-rtl-blackberry {
    background-image: url(./images/icon-left.png);
}

.jqx-menu-item-arrow-left-selected-blackberry {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-blackberry {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-blackberry
{  
    font-size: 13px;
}
.jqx-input-button-header-blackberry {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}
.jqx-widget .jqx-grid-cell, .jqx-widget .jqx-grid-column-header, .jqx-widget .jqx-grid-group-cell {border-color: #ccc; border-color: rgba(0,0,0,0.2);}
.jqx-combobox-content-blackberry, .jqx-input-blackberry, .jqx-input-group-addon-blackberry {
    border-color: #cccccc;
    border-color: rgba(0,0,0,0.2);
    color: #555555;
    background-color: #ffffff;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-blackberry, .jqx-combobox-blackberry, .jqx-combobox-state-normal-blackberry {
    border-color: #cccccc;
    border-color: rgba(0,0,0,0.2);
}

.jqx-combobox-content-focus-blackberry, .jqx-combobox-state-focus-blackberry, .jqx-fill-state-focus-blackberry,
.jqx-numberinput-focus-blackberry {
    outline: none;
    border-color: #959595;
}

input[type="text"].jqx-input-blackberry:-moz-placeholder, input[type="text"].jqx-widget-content-blackberry:-moz-placeholder, input[type="textarea"].jqx-widget-content-blackberry:-moz-placeholder, textarea.jqx-input-blackberry:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-blackberry:-webkit-input-placeholder, input[type="text"].jqx-widget-content-blackberry:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-blackberry:-webkit-input-placeholder, textarea.jqx-input-blackberry:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-blackberry:-ms-input-placeholder, input[type="text"].jqx-widget-content-blackberry:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-blackberry:-ms-input-placeholder, textarea.jqx-input-blackberry:-ms-input-placeholder {
    color: #999999;
}

.jqx-slider-rangebar-blackberry {
    border-color: #00aae7;
    background: #00aae7;
}
.jqx-tabs-blackberry {
    border: 1px solid #1a1a1a;
}
.jqx-tabs-title-blackberry {
    background: transparent;
    border-color: transparent;
    border-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    -webkit-border-radius: 0px;
    -webkit-border-top-left-radius: 0px;
    -webkit-border-top-right-radius: 0px;
    -webkit-border-bottom-left-radius: 0px;
    -webkit-border-bottom-right-radius: 0px;
    -moz-border-radius: 0px;
    -moz-border-top-left-radius: 0px;
    -moz-border-top-right-radius: 0px;
    -moz-border-bottom-left-radius: 0px;
    -moz-border-bottom-right-radius: 0px;
}
.jqx-tabs-header-blackberry, .jqx-tabs-arrow-background {
    background-color: #1a1a1a;
    background-image: -webkit-gradient(linear,50% 0,50% 100%,color-stop(0,rgba(255,255,255,0.18)),color-stop(1,rgba(255,255,255,0)));
    background-image: -moz-linear-gradient(top,rgba(255,255,255,0.18),rgba(255,255,255,0));
    background-image: linear-gradient(top,rgba(255,255,255,0.18),rgba(255,255,255,0));
    background-image: -ms-linear-gradient(top,rgba(255,255,255,0.18),rgba(255,255,255,0));
    background-image: -o-linear-gradient(top,rgba(255,255,255,0.18),rgba(255,255,255,0));
    color: #fff;
    border-color: #000;
}
.jqx-tabs-title-selected-top-blackberry, .jqx-tabs-selection-tracker-top-blackberry {
    border-bottom: 0px solid transparent;
    border-top: 2px solid #057fe4;
    color: #fff;
    background: #3a3a3a;
    background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #3f3f3f), color-stop(100%, #353535));
    background-image: -webkit-linear-gradient(top, #3f3f3f, #353535);
    background-image: -moz-linear-gradient(top, #3f3f3f, #353535);
    background-image: -o-linear-gradient(top, #3f3f3f, #353535);
    background-image: linear-gradient(top, #3f3f3f, #353535);
    box-shadow: 0 0px 5px #000;
    -webkit-box-shadow: 0 0px 5px #000;
    padding-top: 4px;
    padding-bottom: 4px;
}

.jqx-tabs-title-selected-bottom-blackberry, .jqx-tabs-selection-tracker-bottom-blackberry {
    border-top: 2px solid #057fe4;
    color: #fff;
    margin-top: 0px;
    background: #3a3a3a;
    background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #3f3f3f), color-stop(100%, #353535));
    background-image: -webkit-linear-gradient(top, #3f3f3f, #353535);
    background-image: -moz-linear-gradient(top, #3f3f3f, #353535);
    background-image: -o-linear-gradient(top, #3f3f3f, #353535);
    background-image: linear-gradient(top, #3f3f3f, #353535);
    box-shadow: 0 0px 5px #000;
    -webkit-box-shadow: 0 0px 5px #000;
    padding-top: 4px;
    padding-bottom: 4px;
}
.jqx-tabs-title-hover-top-blackberry, .jqx-tabs-title-hover-bottom-blackberry, .jqx-tabs-header-blackberry
{
    color: #fff;
}
.jqx-expander-arrow-expanded-blackberry, .jqx-icon-arrow-up-selected-blackberry{background-image:url('images/icon-up-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-down-selected-blackberry{background-image:url('images/icon-down-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-left-selected-blackberry{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-right-selected-blackberry{background-image:url('images/icon-right-white.png');background-repeat:no-repeat; background-position:center}
.jqx-grid-column-sortdescbutton-blackberry, jqx-grid-column-filterbutton-blackberry, .jqx-grid-column-sortascbutton-blackberry{ background-color:transparent;  border-style:solid;  border-width:0px 0px 0px 0px;  border-color:#0076ae}

.jqx-menu-item-arrow-right-selected-blackberry{background-image:url(./images/icon-right-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-down-selected-blackberry{background-image:url(./images/icon-down-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-up-selected-blackberry{background-image:url(./images/icon-up-white.png);background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-left-selected-blackberry{background-image:url(./images/icon-left-white.png); background-position:0 50%; background-repeat:no-repeat}

.jqx-button-blackberry .jqx-icon-arrow-left-blackberry {
    background-image:url('images/icon-left.png');
}
.jqx-button-blackberry .jqx-icon-arrow-right-blackberry {
    background-image:url('images/icon-right.png');
}
.jqx-listmenu-item-blackberry.jqx-fill-state-normal-blackberry .jqx-icon-arrow-left-blackberry {
    background-image:url('images/icon-left.png');
}
.jqx-listmenu-item-blackberry.jqx-fill-state-normal-blackberry .jqx-icon-arrow-right-blackberry {
    background-image:url('images/icon-right.png');
}
.jqx-listmenu-item-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-left-blackberry {
    background-image:url('images/icon-left-white.png');
}
.jqx-listmenu-item-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-right-blackberry {
    background-image:url('images/icon-right-white.png');
}
.jqx-listmenu-header-label-blackberry {
    font-size: 24px;
    margin-left: -5px;
}
.jqx-listmenu-item-label-blackberry {
    display: inline-block;
    width: 100%;
    height: 100%;
}
.jqx-listmenu-item-blackberry a:link, .jqx-listmenu-item-blackberry a:visited {
    display: inline-block;
    text-decoration: none;
    color: inherit;
    font-size: 16px;
    width: 100%;
    height: 100%;
    padding: 15px;
}
.jqx-listmenu-item-blackberry {
    padding: 0px;
}
.jqx-listmenu-separator-blackberry {
    font-size: 16px; 
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
}

.jqx-grid-column-sortascbutton-blackberry, .jqx-window-collapse-button-blackberry, .jqx-menu-item-arrow-up-selected-blackberry, .jqx-menu-item-arrow-top-up-blackberry, .jqx-icon-arrow-up-selected-blackberry{background-image:url('images/icon-up-white.png');background-repeat:no-repeat;background-position:center}
.jqx-grid-column-menubutton-blackberry, .jqx-grid-column-sortdescbutton-blackberry, .jqx-window-collapse-button-collapsed-blackberry, .jqx-menu-item-arrow-down-selected-blackberry, .jqx-menu-item-arrow-down-blackberry, .jqx-icon-arrow-down-selected-blackberry{background-image:url('images/icon-down-white.png');background-repeat:no-repeat;background-position:center}
.jqx-tabs-arrow-left-blackberry, .jqx-menu-item-arrow-left-selected-blackberry, .jqx-icon-arrow-left-blackberry, .jqx-menu-item-arrow-top-left-blackberry{background-image:url('images/icon-left-white.png');background-repeat:no-repeat;background-position:center}
.jqx-tabs-arrow-right-blackberry, .jqx-menu-item-arrow-right-selected-blackberry, .jqx-menu-item-arrow-top-right-blackberry, .jqx-icon-arrow-right-blackberry, .jqx-icon-arrow-right-hover-blackberry, .jqx-icon-arrow-right-selected-blackberry{background-image:url('images/icon-right-white.png');background-repeat:no-repeat;background-position:center}
.jqx-window-close-button-blackberry, .jqx-icon-close-blackberry, .jqx-tabs-close-button-blackberry, .jqx-tabs-close-button-hover-blackberry, .jqx-tabs-close-button-selected-blackberry{background-image:url(./images/close_white.png);  background-repeat:no-repeat;  background-position:center}
.jqx-button-blackberry {
   padding: 10px 7px;
}
input[type=text].jqx-input-blackberry, input[type=password].jqx-input-blackberry{
   font-size: 16px; 
    font-family: Slate Pro, Slate, Helvetica, sans-serif;
}

.jqx-group-button-normal-blackberry{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
     -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
.jqx-progressbar-text-blackberry {
    font-size: 16px;
}
.jqx-slider-button-blackberry
{
    border-radius: 100%;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    padding: 4px !important;
}
.jqx-slider-slider-blackberry {
    width: 25px;
    height: 25px;
    margin-top: -10px;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-button-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-right-blackberry {
      background-image: url('images/icon-right-white.png');
}
.jqx-slider-button-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-left-blackberry {
      background-image: url('images/icon-left-white.png');
}
.jqx-slider-button-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-up-blackberry {
      background-image: url('images/icon-up-white.png');
}
.jqx-slider-button-blackberry.jqx-fill-state-pressed-blackberry .jqx-icon-arrow-down-blackberry {
      background-image: url('images/icon-down-white.png');
}
.jqx-grid-cell-blackberry.jqx-grid-cell-selected-blackberry>.jqx-grid-group-expand-blackberry,
.jqx-grid-cell-blackberry.jqx-grid-cell-hover-blackberry>.jqx-grid-group-expand-blackberry {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-blackberry.jqx-grid-cell-selected-blackberry>.jqx-grid-group-collapse-blackberry,
.jqx-grid-cell-blackberry.jqx-grid-cell-hover-blackberry>.jqx-grid-group-collapse-blackberry {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-blackberry.jqx-grid-cell-selected-blackberry>.jqx-grid-group-collapse-rtl-blackberry,
.jqx-grid-cell-blackberry.jqx-grid-cell-hover-blackberry>.jqx-grid-group-collapse-rtl-blackberry {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-blackberry.jqx-grid-cell-selected-blackberry>.jqx-grid-group-expand-rtl-blackberry,
.jqx-grid-cell-blackberry.jqx-grid-cell-hover-blackberry>.jqx-grid-group-expand-rtl-blackberry {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-expand-button-blackberry, .jqx-tree-grid-collapse-button-blackberry {
    margin-top: 0px;
}
.jqx-tree-grid-checkbox-blackberry {
    margin-top: 1px;
}
.jqx-icon-arrow-first-selected-blackberry
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-blackberry
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-button-blackberry .jqx-icon-arrow-left-selected-blackberry, .jqx-icon-arrow-left-selected-blackberry
{
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-button-blackberry .jqx-icon-arrow-right-selected-blackberry, .jqx-icon-arrow-right-selected-blackberry
{
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
 .jqx-icon-calendar-pressed-blackberry {
    background-image: url('images/icon-calendar-white.png');
}
 .jqx-icon-time-pressed-blackberry {
    background-image: url('images/icon-time-white.png');
}
 .jqx-file-upload-blackberry .jqx-icon-arrow-up-blackberry {
  background-image: url('images/icon-up-white.png');
}
.jqx-file-upload-file-name-blackberry{padding:3px;}
.jqx-file-upload-file-row-blackberry{ height: 40px;}
.jqx-layout-blackberry
{
    background-color: #296b8e;
}
.jqx-layout-pseudo-window-pin-icon-blackberry
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-blackberry
{
    background-image: url("images/pinned-white.png");
}