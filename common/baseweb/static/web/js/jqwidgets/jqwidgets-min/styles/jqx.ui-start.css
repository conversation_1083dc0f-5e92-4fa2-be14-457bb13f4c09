.jqx-rc-tl-ui-start{border-top-left-radius:5px; moz-border-radius-topleft:5px; webkit-border-top-left-radius:5px}
.jqx-rc-tr-ui-start{border-top-right-radius:5px; moz-border-radius-topright:5px; webkit-border-top-right-radius:5px}
.jqx-rc-bl-ui-start{border-bottom-left-radius:5px; moz-border-radius-bottomleft:5px; webkit-border-bottom-left-radius:5px}
.jqx-rc-br-ui-start{border-bottom-right-radius:5px; moz-border-radius-bottomright:5px; webkit-border-bottom-right-radius:5px}
.jqx-rc-t-ui-start{border-top-left-radius:5px; border-top-right-radius:5px; moz-border-radius-topleft:5px; moz-border-radius-topright:5px; webkit-border-top-left-radius:5px; webkit-border-top-right-radius:5px}
.jqx-rc-b-ui-start{border-bottom-left-radius:5px; border-bottom-right-radius:5px; moz-border-radius-bottomleft:5px; moz-border-radius-bottomright:5px; webkit-border-bottom-left-radius:5px; webkit-border-bottom-right-radius:5px}
.jqx-rc-r-ui-start{border-bottom-right-radius:5px; border-top-right-radius:5px; moz-border-radius-bottomright:5px; moz-border-radius-topright:5px; webkit-border-bottom-right-radius:5px; webkit-border-top-right-radius:5px}
.jqx-rc-l-ui-start{border-bottom-left-radius:5px; border-top-left-radius:5px; moz-border-radius-bottomleft:5px; moz-border-radius-topleft:5px; webkit-border-bottom-left-radius:5px; webkit-border-top-left-radius:5px}
.jqx-rc-all-ui-start{border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-start{background-position:-96px -192px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png);position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-start{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-grid-column-menubutton-ui-start{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png); border-width:0px}
.jqx-grid-cell-sort-ui-start, .jqx-grid-cell-filter-ui-start, .jqx-grid-cell-pinned-ui-start{background: #ecf2f2; color: #1482b4;} 
.jqx-grid-cell-sort-alt-ui-start, .jqx-grid-cell-filter-alt-ui-start, .jqx-grid-cell-alt-ui-start{background: #ecf2f2;}
/*Tabs*/
.jqx-tabs-title-ui-start{border-color:#77d5f7; border-bottom-color: transparent; background: #0078ae url(./images/start/ui-bg_glass_45_0078ae_1x400.png) 50% 50% repeat-x;}
.jqx-tabs-header-ui-start{border-width: 1px; padding-bottom: 0px; margin:2px; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
.jqx-tabs-title-bottom-ui-start{border-bottom-color:#77d5f7; border-top-color: transparent;}
.jqx-tabs-header-bottom-ui-start{margin-top:-2px !important; padding-bottom: 3px; padding-top:1px}
/*Radio Button*/
.jqx-radiobutton-ui-start .jqx-fill-state-pressed-ui-start{background:#fdfefe; border:1px solid #fdfefe}
/*Calendar*/
.jqx-calendar-cell-ui-start{font-size: 11px; color: #fff; border-color: #77d5f7; background: #0078ae url(./images/start/ui-bg_glass_45_0078ae_1x400.png) 50% 50% repeat-x; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-start{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-start{border: 1px solid #77d5f7; border-bottom-width: 0px; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px; font-weight: bold;}
.jqx-calendar-month-container-ui-start{border:none !important}
.jqx-calendar-ui-start{padding:2px}
.jqx-calendar-column-cell-ui-start{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-start .jqx-icon-arrow-left-ui-start{background-image:url(./images/start/ui-icons_d8e7f3_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-start .jqx-icon-arrow-right-ui-start{background-image:url(./images/start/ui-icons_d8e7f3_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:5px; position:relative}

/*Icons*/
.jqx-icon-arrow-up-ui-start{background-position:0 -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-icon-arrow-down-ui-start{background-position:-65px -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-icon-arrow-left-ui-start{background-position:-96px -17px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-icon-arrow-right-ui-start{background-position:-32px -17px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-icon-arrow-up-hover-ui-start{background-position:0 -16px; background-image:url(./images/start/ui-icons_0078ae_256x240.png)}
.jqx-icon-arrow-down-hover-ui-start{background-position:-65px -16px; background-image:url(./images/start/ui-icons_0078ae_256x240.png)}
.jqx-icon-arrow-left-selected-ui-start{background-position:-96px -17px; background-image:url(./images/start/ui-icons_fcd113_256x240.png)}
.jqx-icon-arrow-right-selected-ui-start{background-position:-32px -17px; background-image:url(./images/start/ui-icons_fcd113_256x240.png)}
.jqx-icon-arrow-up-selected-ui-start{background-position:0 -16px; background-image:url(./images/start/ui-icons_fcd113_256x240.png)}
.jqx-icon-arrow-down-selected-ui-start{background-position:-65px -16px; background-image:url(./images/start/ui-icons_fcd113_256x240.png)}
.jqx-icon-close-ui-start{background-image:url(./images/start/ui-icons_d8e7f3_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-start{background-image:url(./images/start/ui-icons_0078ae_256x240.png); background-position:-80px -128px; cursor:pointer}

.jqx-grid-pager-ui-start .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-start .jqx-icon-arrow-left-ui-start{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-start .jqx-icon-arrow-right-ui-start{
	position: relative;
    top: 6px;
}

/*Window*/
.jqx-window-ui-start{padding: 2px;}
.jqx-window-header-ui-start{border: 1px solid #77d5f7; font-weight: bold; font-size: 11px; moz-border-radius:5px; border-radius:5px; webkit-border-radius:5px}
.jqx-window-content-ui-start{border-width:0px !important}
.jqx-window-close-button-ui-start{background-position:-96px -128px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png);moz-border-radius:5px; border-radius:5px; webkit-border-radius:5px}
.jqx-window-collapse-button-ui-start{background-position:0 -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-window-collapse-button-hover-ui-start{background-image:url(./images/start/ui-icons_d8e7f3_256x240.png); background-color:#fff; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
.jqx-window-collapse-button-collapsed-ui-start, .jqx-window-collapse-button-collapsed-hover-ui-start{background-position:-65px -16px}
.jqx-window-modal-ui-start{}
.jqx-window-close-button-hover-ui-start{background-color:#fff; background-position:-96px -128px; background-image:url(./images/start/ui-icons_056b93_256x240.png); cursor:pointer; width:16px; height:16px}

/*Common Settings*/
.jqx-widget-ui-start{ border-color: #a6c9e2; font-size:12px; font-style:normal;}
.jqx-widget-content-ui-start{border-color: #a6c9e2; background: #fcfdfd url(./images/start/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x; color: #222222;}
.jqx-widget-content-ui-start a{color:#222}
.jqx-widget-header-ui-start{ border-color: #4297d7; background: #2191c0 url(./images/start/ui-bg_gloss-wave_75_2191c0_500x100.png) 50% 50% repeat-x; color: #eaf5f7; font-size:12px}
.jqx-widget-header-ui-start a{color:#eaf5f7}
.jqx-fill-state-normal-ui-start{border-color: #77d5f7; background: #0078ae url(./images/start/ui-bg_glass_45_0078ae_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff;}
.jqx-fill-state-normal-ui-start a, .jqx-fill-state-normal-ui-start a:link, .jqx-fill-state-normal-ui-start a:visited{color:#ffffff; text-decoration:none}
.jqx-fill-state-hover-ui-start{border-color: #448dae; background: #79c9ec url(./images/start/ui-bg_glass_75_79c9ec_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #026890;}
.jqx-fill-state-hover-ui-start a, .jqx-fill-state-hover-ui-start a:hover{color:#026890; text-decoration:none}
.jqx-fill-state-pressed-ui-start{border-color: #acdd4a; background: #6eac2c url(./images/start/ui-bg_gloss-wave_50_6eac2c_500x100.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff;  }
.jqx-fill-state-pressed-ui-start a, .jqx-fill-state-pressed-ui-start a:link, .jqx-fill-state-pressed-ui-start a:visited{color:#ffffff; text-decoration:none}

.jqx-input-button-content-ui-start{font-size:10px}
.jqx-input-icon-ui-start{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-start{margin-top:0px; background-position:-65px -147px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-start .jqx-fill-state-pressed-ui-start{background: #7bbdd9; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-start{background: #7bbdd9; border-right: none; border-bottom: 1px solid #77d5f7;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-start, .jqx-scrollbar-thumb-state-normal-horizontal-ui-start{ border: 1px solid #77d5f7; background: #1482b4;}
.jqx-scrollbar-thumb-state-hover-ui-start, .jqx-scrollbar-thumb-state-hover-horizontal-ui-start{ background: #46a3ca;}
.jqx-scrollbar-thumb-state-pressed-ui-start, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-start{border: 1px solid #acdd4a; background: #6eac2c;}

/*Tabs */
.jqx-tabs-title-ui-start
{
}
.jqx-tabs-selection-tracker-top-ui-start
{
   background: #7cb441;
   border-bottom: 0px solid transparent;
}
.jqx-tabs-selection-tracker-bottom-ui-start
{
   background: #7cb441;
   border-top: 0px solid transparent;
}
.jqx-tabs-title-selected-top-ui-start
{
    padding-bottom: 5px;
    border-bottom: 0px solid #7cb441;
}
.jqx-tabs-title-selected-bottom-ui-start
{
    border-top: 0px solid #7cb441;
}

/*Slider*/
.jqx-slider-track-ui-start{border: 1px solid #77d5f7; background: #fdfefe}
.jqx-slider-rangebar
{
    background: #7bbdd9;
} 

/*Grid*/
.jqx-grid-bottomright-ui-start, .jqx-panel-bottomright-ui-start, .jqx-listbox-bottomright-ui-start, .jqx-scrollbar-state-normal-ui-start{background: #7bbdd9;}
.jqx-widget-ui-start .jqx-grid-column-header-ui-start, .jqx-grid-cell-ui-start, .jqx-widget-ui-start .jqx-grid-cell-ui-start, .jqx-widget-ui-start .jqx-grid-group-cell-ui-start, .jqx-grid-group-cell-ui-start{border-color:#77d5f7}
.jqx-grid-group-expand-ui-start{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down.png);}
.jqx-grid-group-collapse-ui-start{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right.png);}
/*Menu*/
.jqx-menu-dropdown-ui-start
{
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-start{overflow: inherit;}
.jqx-expander-header-ui-start{margin-bottom:2px; margin-top:2px}
.jqx-expander-header-ui-start{ background: #0078ae url(./images/start/ui-bg_glass_45_0078ae_1x400.png) 50% 50% repeat-x; border:1px solid #77d5f7; border-radius:5px !important; moz-border-radius:5px !important; webkit-border-radius:5px !important}
.jqx-expander-header-hover-ui-start{background: #79c9ec url(./images/start/ui-bg_glass_75_79c9ec_1x400.png) 50% 50% repeat-x; border:1px solid #448dae;}
.jqx-expander-header-expanded-ui-start{background: #6eac2c url(./images/start/ui-bg_gloss-wave_50_6eac2c_500x100.png) 50% 50% repeat-x; border:1px solid #acdd4a; border-bottom-width:0px; border-top-left-radius:5px !important; border-top-right-radius:5px !important; moz-border-radius-topleft:5px !important; moz-border-radius-topright:5px !important; webkit-border-top-left-radius:5px !important; webkit-border-top-right-radius:5px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-start{border-bottom-left-radius:5px !important; border-bottom-right-radius:5px !important; moz-border-radius-bottomleft:5px !important; moz-border-radius-bottomright:5px !important; webkit-border-bottom-left-radius:5px !important; webkit-border-bottom-right-radius:5px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-start{width: 16px; height: 16px; position: relative; background-position:-65px -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png);}
.jqx-expander-arrow-bottom-ui-start{width: 16px; height: 16px; position: relative; background-position:0 -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png);}
.jqx-tabs-arrow-right-ui-start{background-position:-32px -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
.jqx-tabs-arrow-left-ui-start{background-position:-96px -16px; background-image:url(./images/start/ui-icons_d8e7f3_256x240.png)}
/*Scroll Bar*/
.jqx-scrollbar-ui-start .jqx-icon-arrow-up-ui-start{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-start .jqx-icon-arrow-down-ui-start{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-start .jqx-icon-arrow-left-ui-start{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-start .jqx-icon-arrow-right-ui-start{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-icon-arrow-first-ui-start
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-ui-start
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-start.jqx-grid-cell-selected-ui-start>.jqx-grid-group-expand-ui-start,
.jqx-grid-cell-ui-start.jqx-grid-cell-hover-ui-start>.jqx-grid-group-expand-ui-start {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-start.jqx-grid-cell-selected-ui-start>.jqx-grid-group-collapse-ui-start,
.jqx-grid-cell-ui-start.jqx-grid-cell-hover-ui-start>.jqx-grid-group-collapse-ui-start {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-start.jqx-grid-cell-selected-ui-start>.jqx-grid-group-collapse-rtl-ui-start,
.jqx-grid-cell-ui-start.jqx-grid-cell-hover-ui-start>.jqx-grid-group-collapse-rtl-ui-start {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-ui-start.jqx-grid-cell-selected-ui-start>.jqx-grid-group-expand-rtl-ui-start,
.jqx-grid-cell-ui-start.jqx-grid-cell-hover-ui-start>.jqx-grid-group-expand-rtl-ui-start {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-search-ui-start
{
    background-image: url(./images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-ui-start, .jqx-icon-calendar-hover-ui-start, .jqx-icon-calendar-pressed-ui-start {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-ui-start, .jqx-icon-time-hover-ui-start, .jqx-icon-time-pressed-ui-start {
    background-image: url('images/icon-time-white.png');
}
.jqx-editor-toolbar-icon-ui-start {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-layout-ui-start
{
    background-color: #4297d7;
    background-image: none;
}
.jqx-layout-pseudo-window-pin-icon-ui-start
{
    background-image: url("images/pin-lightblue.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-start
{
    background-image: url("images/pinned-lightblue.png");
}
.jqx-scheduler-month-cell-ui-start, .jqx-scheduler-time-column-ui-start, .jqx-scheduler-toolbar-ui-start
{
    background: #4297d7 !important;
    color: #fff !important;
}
.jqx-widget-ui-start .jqx-scheduler-middle-cell-ui-start, .jqx-scheduler-middle-cell-ui-start {
    border-bottom-color: #4297d7 !important;
}
.jqx-docking-layout-group-floating-ui-start .jqx-window-header-ui-start
{
    background-image: none;
}
/*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-hour-container-ui-start:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-minute-container-ui-start:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-am-container-ui-start:focus {
    outline: 1px solid white !important;
}
.jqx-time-picker .jqx-header .jqx-pm-container-ui-start:focus {
    outline: 1px solid white !important;
}
.jqx-label-ui-start {
	fill: white;
}
.jqx-label-ui-start.jqx-selected {
	fill: black !important;
}
.jqx-needle-ui-start {
	fill: rgb(0, 120, 174);
}
.jqx-needle-central-circle-ui-start:first-of-type {
	fill: rgb(0, 120, 174);
	stroke: rgb(0, 120, 174);
}
.jqx-needle-central-circle-ui-start {
	fill: white;
	stroke: rgb(33, 145, 192);
}
.jqx-container-ui-start {
	border-radius: inherit;
}
.jqx-header-ui-start {
	border-top-left-radius: inherit;
	border-top-right-radius: inherit;
}
.jqx-svg-picker-ui-start {
	background-image: linear-gradient(rgb(0, 120, 174) 5%, rgb(119, 213, 247) 25%, rgb(186, 233, 255), rgb(119, 213, 247) 45%, rgb(0, 120, 174) 95%);
}
