.jqx-rc-tl-ui-lightness{border-top-left-radius:4px; moz-border-radius-topleft:4px; webkit-border-top-left-radius:4px}
.jqx-rc-tr-ui-lightness{border-top-right-radius:4px; moz-border-radius-topright:4px; webkit-border-top-right-radius:4px}
.jqx-rc-bl-ui-lightness{border-bottom-left-radius:4px; moz-border-radius-bottomleft:4px; webkit-border-bottom-left-radius:4px}
.jqx-rc-br-ui-lightness{border-bottom-right-radius:4px; moz-border-radius-bottomright:4px; webkit-border-bottom-right-radius:4px}
.jqx-rc-t-ui-lightness{border-top-left-radius:4px; border-top-right-radius:4px; moz-border-radius-topleft:4px; moz-border-radius-topright:4px; webkit-border-top-left-radius:4px; webkit-border-top-right-radius:4px}
.jqx-rc-b-ui-lightness{border-bottom-left-radius:4px; border-bottom-right-radius:4px; moz-border-radius-bottomleft:4px; moz-border-radius-bottomright:4px; webkit-border-bottom-left-radius:4px; webkit-border-bottom-right-radius:4px}
.jqx-rc-r-ui-lightness{border-bottom-right-radius:4px; border-top-right-radius:4px; moz-border-radius-bottomright:4px; moz-border-radius-topright:4px; webkit-border-bottom-right-radius:4px; webkit-border-top-right-radius:4px}
.jqx-rc-l-ui-lightness{border-bottom-left-radius:4px; border-top-left-radius:4px; moz-border-radius-bottomleft:4px; moz-border-radius-topleft:4px; webkit-border-bottom-left-radius:4px; webkit-border-top-left-radius:4px}
.jqx-rc-all-ui-lightness{border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-grid-column-sortdescbutton-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_ffffff_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortascbutton-ui-lightness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_ffffff_256x240.png)}
.jqx-grid-ui-lightness .jqx-widget-header-ui-lightness{border-width:0px !important}
.jqx-tree-item-arrow-expand-ui-lightness, .jqx-tree-item-arrow-expand-hover-ui-lightness{background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-lightness, .jqx-tree-item-arrow-collapse-hover-ui-lightness{background-position:-32px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-tabs-arrow-right-ui-lightness, .jqx-menu-item-arrow-right-ui-lightness, .jqx-menu-item-arrow-right-selected-ui-lightness{background-position:-32px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-tabs-arrow-left-ui-lightness, .jqx-menu-item-arrow-left-ui-lightness, .jqx-menu-item-arrow-left-selected-ui-lightness{background-position:-96px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-progressbar-ui-lightness .jqx-fill-state-pressed-ui-lightness{background:#f6a828; border-width:0px; border-right:1px solid #e78f08; color:#fff;}
.jqx-progressbar-value-vertical-ui-lightness{border-width:0px !important; border-bottom: 1px solid #e78f08 !important}
.jqx-tabs-title-ui-lightness{ border-color: #ccc; border-bottom-color: #fafafa; background:#f6f6f6 url(./images/lightness/ui-bg_glass_100_f6f6f6_1x400.png) 50% 50% repeat-x; color:#1c94c4; font-weight:bold;}
.jqx-tabs-title-selected-top-ui-lightness{background-color:#fff;}
.jqx-tabs-title-selected-bottom-ui-lightness{}
.jqx-tabs-title-bottom-ui-lightness{margin-top:-2px; border-top-color: #fafafa; bottom-bottom-color: #ccc;}
.jqx-tabs-header-ui-lightness{margin:2px; border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-tabs-header-bottom-ui-lightness{margin-top:-2px !important; padding-top:2px}
.jqx-tabs-content-ui-lightness{border-width:0px !important}
.jqx-radiobutton-ui-lightness .jqx-fill-state-pressed-ui-lightness{background:#ec8e0c; border:1px solid #fed22f}
.jqx-calendar-cell-ui-lightness{background:#f6f6f6 url(./images/lightness/ui-bg_glass_100_f6f6f6_1x400.png) 50% 50% repeat-x; border:1px solid #ccc; color:#1c94c4; font-weight:bold; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-lightness{background:#ffe45c url(./images/lightness/ui-bg_highlight-soft_75_ffe45c_1x100.png) 50% top repeat-x; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-lightness{border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-calendar-month-container-ui-lightness{border:none !important}
.jqx-calendar-ui-lightness{padding:2px}
.jqx-calendar-ui-lightness .jqx-icon-arrow-left-ui-lightness{background-image:url(./images/lightness/ui-icons_ffffff_256x240.png); background-position:-80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-lightness .jqx-icon-arrow-right-ui-lightness{background-image:url(./images/lightness/ui-icons_ffffff_256x240.png); background-position:-48px -192px; width:16px; height:16px; right:5px; position:relative}
.jqx-calendar-ui-lightness .jqx-icon-arrow-left-hover-ui-lightness{background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png); background-position:-80px -192px}
.jqx-calendar-ui-lightness .jqx-icon-arrow-right-hover-ui-lightness{background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png); background-position:-48px -192px}
.jqx-navigationbar-ui-lighness{overflow: auto;}
.jqx-window-ui-lightness{padding: 2px;}
.jqx-window-header-ui-lightness{ moz-border-radius:4px; border-radius:4px; webkit-border-radius:4px}
.jqx-window-content-ui-lightness{border-width:0px !important}
.jqx-window-close-button-hover-ui-lightness{background-color:#fff; background-position:-96px -128px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png); cursor:pointer; width:16px; height:16px}
.jqx-grid-column-header-ui-lightness{border-width:0px !important}
.jqx-slider-ui-lightness .jqx-fill-state-pressed-ui-lightness{background:#f6a828 url(./images/lightness/ui-bg_gloss-wave_35_f6a828_500x100.png) 50% 50% repeat-x; border-color:#e78f08}
.jqx-icon-arrow-up-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png)}
.jqx-icon-arrow-down-ui-lightness{background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png)}
.jqx-icon-arrow-left-ui-lightness, .jqx-tree-item-arrow-collapse-rtl-ui-lightness, .jqx-tree-item-arrow-collapse-hover-rtl-ui-lightness{background-position:-96px -17px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png)}
.jqx-icon-arrow-right-ui-lightness{background-position:-32px -17px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png)}
.jqx-icon-arrow-up-hover-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-down-hover-ui-lightness{background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-left-selected-ui-lightness{background-position:-96px -17px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-right-selected-ui-lightness{background-position:-32px -17px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-up-selected-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-down-selected-ui-lightness{background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-left-selected-ui-lightness{background-position:-96px -17px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-right-selected-ui-lightness{background-position:-32px -17px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-icon-close-ui-lightness{background-image:url(./images/lightness/ui-icons_222222_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-lightness{background-image:url(./images/lightness/ui-icons_222222_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-grid-column-menubutton-ui-lightness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_ffffff_256x240.png); border-width:0px}
.jqx-window-close-button-ui-lightness{background-position:-96px -128px; background-image:url(./images/lightness/ui-icons_ffffff_256x240.png);moz-border-radius:4px; border-radius:4px; webkit-border-radius:4px}
.jqx-window-collapse-button-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_ffffff_256x240.png)}
.jqx-window-collapse-button-hover-ui-lightness{background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png); background-color:#fff; border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-window-collapse-button-collapsed-ui-lightness, .jqx-window-collapse-button-collapsed-hover-ui-lightness{background-position:-65px -16px}
.jqx-window-modal-ui-lightness{}

.jqx-widget-ui-lightness{line-height: 17px; font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; font-size:12px; font-style:normal; webkit-tap-highlight-color:rgba(0,0,0,0)}
.jqx-widget-content-ui-lightness{font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; background-color:#f8f8f8; background-image:-webkit-gradient(linear,0 0,0 100%,from(#f8f8f8),to(#eee));  background-image:-moz-linear-gradient(top,#f8f8f8,#eee);  background-image:-o-linear-gradient(top,#f8f8f8,#eee); border-color:#ddd; color:#333; font-size:12px}
.jqx-widget-content-ui-lightness a{color:#333}
.jqx-widget-header-ui-lightness{font-family:Trebuchet MS,Tahoma,Verdana,Arial,sans-serif; background:#f6a828 url(./images/lightness/ui-bg_gloss-wave_35_f6a828_500x100.png) 50% 50% repeat-x; border-color: #e78f08; color:#fff; font-size:12px}
.jqx-widget-header-ui-lightness a{color:#fff}
.jqx-fill-state-normal-ui-lightness{background:#f6f6f6 url(./images/lightness/ui-bg_glass_100_f6f6f6_1x400.png) 50% 50% repeat-x; border-color:#ccc; color:#1c94c4}
.jqx-fill-state-normal-ui-lightness a, .jqx-fill-state-normal-ui-lightness a:link, .jqx-fill-state-normal-ui-lightness a:visited{color:#1c94c4; text-decoration:none}
.jqx-fill-state-hover-ui-lightness{background:#fdf5ce url(./images/lightness/ui-bg_glass_100_fdf5ce_1x400.png) 50% 50% repeat-x; border-color:#fbcb09; color:#c77405}
.jqx-fill-state-hover-ui-lightness a, .jqx-fill-state-hover-ui-lightness a:hover{color:#c77405; text-decoration:none}
.jqx-fill-state-pressed-ui-lightness{background:#fff url(./images/lightness/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x; border-color:#fbd850; color:#eb8f00}
.jqx-fill-state-pressed-ui-lightness a, .jqx-fill-state-pressed-ui-lightness a:link, .jqx-fill-state-pressed-ui-lightness a:visited{color:#eb8f00; text-decoration:none}

.jqx-input-button-content-ui-lightness{font-size:10px}
.jqx-input-icon-ui-lightness{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-lightness{margin-top:0px; background-position:-65px -147px; background-image:url(./images/lightness/ui-icons_ef8c08_256x240.png)}
.jqx-grid-cell-sort-ui-lightness, .jqx-grid-cell-filter-ui-lightness, .jqx-grid-cell-pinned-ui-lightness{background-color:#f8f8f8; color:#1c94c4}
.jqx-splitter-collapse-button-horizontal-ui-lightness, .jqx-splitter-collapse-button-vertical-ui-lightness{ background:#ec8e0c;  border:1px solid #fdd02e}
.jqx-dropdownlist-content-ui-lightness{ color:#333}

.jqx-expander-header-ui-lightness{background:#f6f6f6 url(./images/lightness/ui-bg_glass_100_f6f6f6_1x400.png) 50% 50% repeat-x; border:1px solid #ccc; color:#1c94c4; font-weight:bold; border-radius:4px !important; moz-border-radius:4px !important; webkit-border-radius:4px !important}
.jqx-expander-header-hover-ui-lightness{background:#fdf5ce url(./images/lightness/ui-bg_glass_100_fdf5ce_1x400.png) 50% 50% repeat-x; border:1px solid #fbcb09; color:#c77405; font-weight:bold}
.jqx-expander-header-expanded-ui-lightness{background:#fff url(./images/lightness/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x; border:1px solid #fbd850; border-bottom-width:0px; color:#eb8f00; font-weight:bold; border-top-left-radius:4px !important; border-top-right-radius:4px !important; moz-border-radius-topleft:4px !important; moz-border-radius-topright:4px !important; webkit-border-top-left-radius:4px !important; webkit-border-top-right-radius:4px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-lightness{border-bottom-left-radius:4px !important; border-bottom-right-radius:4px !important; moz-border-radius-bottomleft:4px !important; moz-border-radius-bottomright:4px !important; webkit-border-bottom-left-radius:4px !important; webkit-border-bottom-right-radius:4px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-lightness{background-position:-65px -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-expander-arrow-bottom-ui-lightness{background-position:0 -16px; background-image:url(./images/lightness/ui-icons_222222_256x240.png)}
.jqx-tabs-title-selected-top-ui-lightness
{
    border-bottom: 1px solid #fff;
}
.jqx-tabs-title-selected-bottom-ui-lightness
{
    border-top: 1px solid #fff;
}
.jqx-tabs-selection-tracker-top-ui-lightness
{
   border-bottom: 1px solid #fff;
}
.jqx-tabs-selection-tracker-bottom-ui-lightness
{
   border-top: 1px solid #fff;
}
/*Scroll Bar*/
.jqx-scrollbar-ui-lightness .jqx-icon-arrow-up-ui-lightness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-lightness .jqx-icon-arrow-down-ui-lightness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-lightness .jqx-icon-arrow-left-ui-lightness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-lightness .jqx-icon-arrow-right-ui-lightness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-icon-arrow-first-ui-lightness
{
    background-image: url('images/lightness/icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-ui-lightness
{
    background-image: url('images/lightness/icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-pager-number-ui-lightness.jqx-fill-state-pressed-ui-lightness {
    color: #000;
}
.jqx-grid-pager-number-ui-lightness.jqx-fill-state-hover-ui-lightness {
    color: #000;
}
.jqx-grid-group-collapse-ui-lightness
{
    background-image: url(./images/icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-ui-lightness
{
    background-image: url(./images/icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-ui-lightness, .jqx-grid-group-expand-rtl-ui-lightness
{
    background-image: url(./images/icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-layout-ui-lightness
{
    background-color: #e78f08;
}
.jqx-layout-pseudo-window-pin-icon-ui-lightness
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-lightness
{
    background-image: url("images/pinned-white.png");
}
.jqx-docking-layout-group-floating .jqx-window-header-ui-lightness
{
    background-image: none;
}


.jqx-grid-pager-ui-lightness .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-lightness .jqx-icon-arrow-left-ui-lightness{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-lightness .jqx-icon-arrow-right-ui-lightness{
	position: relative;
    top: 6px;
}