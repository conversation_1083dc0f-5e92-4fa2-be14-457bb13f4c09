.jqx-rc-tl-ui-redmond{border-top-left-radius:5px; moz-border-radius-topleft:5px; webkit-border-top-left-radius:5px}
.jqx-rc-tr-ui-redmond{border-top-right-radius:5px; moz-border-radius-topright:5px; webkit-border-top-right-radius:5px}
.jqx-rc-bl-ui-redmond{border-bottom-left-radius:5px; moz-border-radius-bottomleft:5px; webkit-border-bottom-left-radius:5px}
.jqx-rc-br-ui-redmond{border-bottom-right-radius:5px; moz-border-radius-bottomright:5px; webkit-border-bottom-right-radius:5px}
.jqx-rc-t-ui-redmond{border-top-left-radius:5px; border-top-right-radius:5px; moz-border-radius-topleft:5px; moz-border-radius-topright:5px; webkit-border-top-left-radius:5px; webkit-border-top-right-radius:5px}
.jqx-rc-b-ui-redmond{border-bottom-left-radius:5px; border-bottom-right-radius:5px; moz-border-radius-bottomleft:5px; moz-border-radius-bottomright:5px; webkit-border-bottom-left-radius:5px; webkit-border-bottom-right-radius:5px}
.jqx-rc-r-ui-redmond{border-bottom-right-radius:5px; border-top-right-radius:5px; moz-border-radius-bottomright:5px; moz-border-radius-topright:5px; webkit-border-bottom-right-radius:5px; webkit-border-top-right-radius:5px}
.jqx-rc-l-ui-redmond{border-bottom-left-radius:5px; border-top-left-radius:5px; moz-border-radius-bottomleft:5px; moz-border-radius-topleft:5px; webkit-border-bottom-left-radius:5px; webkit-border-top-left-radius:5px}
.jqx-rc-all-ui-redmond{border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-redmond{background-position:-96px -192px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-redmond{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png)}
/*Tree*/
.jqx-tree-item-arrow-expand-ui-redmond, .jqx-tree-item-arrow-expand-hover-ui-redmond{background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-redmond, .jqx-tree-item-arrow-collapse-hover-ui-redmond{background-position:-32px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-menu-item-arrow-right-ui-redmond, .jqx-menu-item-arrow-right-selected-ui-redmond{background-position:-32px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-menu-item-arrow-left-ui-redmond, .jqx-menu-item-arrow-left-selected-ui-redmond, .jqx-tree-item-arrow-collapse-rtl-ui-redmond, .jqx-tree-item-arrow-collapse-hover-rtl-ui-redmond{background-position:-96px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
/*Tabs*/
.jqx-tabs-title-ui-redmond{font-weight: bold; border-color: #c5dbec; background: #dfeffc url(./images/redmond/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x; color: #2e6e9e;}
.jqx-tabs-header-ui-redmond{margin-left:2px; margin-right: 2px; margin-top: 2px; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
.jqx-tabs-arrow-right-ui-redmond{background-position:-32px -16px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png)}
.jqx-tabs-arrow-left-ui-redmond{background-position:-96px -16px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png)}
/*Radio Button*/
.jqx-radiobutton-ui-redmond .jqx-fill-state-pressed-ui-redmond{background:#6caad4; border:1px solid #6caad4}
/*Calendar*/
.jqx-calendar-cell-ui-redmond{font-weight: bold; font-size: 11px; border-color: #c5dbec; background: #dfeffc url(./images/redmond/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x; color: #2e6e9e; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-redmond{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-redmond{border: 1px solid #4297d7; border-bottom-width: 0px; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px; font-weight: bold;}
.jqx-calendar-month-container-ui-redmond{border:none !important}
.jqx-calendar-ui-redmond{padding:2px}
.jqx-calendar-column-cell-ui-redmond{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-redmond .jqx-icon-arrow-left-ui-redmond{background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-redmond .jqx-icon-arrow-right-ui-redmond{background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:5px; position:relative}
/*Icons*/
.jqx-icon-arrow-up-ui-redmond{background-position:0 -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-icon-arrow-down-ui-redmond{background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-icon-arrow-left-ui-redmond{background-position:-96px -17px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-icon-arrow-right-ui-redmond{background-position:-32px -17px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-icon-arrow-up-hover-ui-redmond{background-position:0 -16px; background-image:url(./images/redmond/ui-icons_217bc0_256x240.png)}
.jqx-icon-arrow-down-hover-ui-redmond{background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_217bc0_256x240.png)}
.jqx-icon-arrow-left-hover-ui-redmond{background-position:-96px -17px; background-image:url(./images/redmond/ui-icons_217bc0_256x240.png)}
.jqx-icon-arrow-right-hover-ui-redmond{background-position:-32px -17px; background-image:url(./images/redmond/ui-icons_217bc0_256x240.png)}
.jqx-icon-arrow-left-selected-ui-redmond{background-position:-96px -17px; background-image:url(./images/redmond/ui-icons_f9bd01_256x240.png)}
.jqx-icon-arrow-right-selected-ui-redmond{background-position:-32px -17px; background-image:url(./images/redmond/ui-icons_f9bd01_256x240.png)}
.jqx-icon-arrow-up-selected-ui-redmond{background-position:0 -16px; background-image:url(./images/redmond/ui-icons_f9bd01_256x240.png)}
.jqx-icon-arrow-down-selected-ui-redmond{background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_f9bd01_256x240.png)}
.jqx-icon-close-ui-redmond{background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-redmond{background-image:url(./images/redmond/ui-icons_217bc0_256x240.png); background-position:-80px -128px; cursor:pointer}
/*Window*/
.jqx-window-ui-redmond{padding: 2px;}
.jqx-window-header-ui-redmond{border: 1px solid #4297d7; font-weight: bold; font-size: 11px; moz-border-radius:5px; border-radius:5px; webkit-border-radius:5px}
.jqx-window-content-ui-redmond{border-width:0px !important}
.jqx-window-close-button-ui-redmond{background-position:-96px -128px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png);moz-border-radius:5px; border-radius:5px; webkit-border-radius:5px}
.jqx-window-collapse-button-ui-redmond{background-position:0 -16px; background-image:url(./images/redmond/ui-icons_d8e7f3_256x240.png)}
.jqx-window-collapse-button-hover-ui-redmond{background-image:url(./images/redmond/ui-icons_217bc0_256x240.png); background-color:#fff; border-radius:5px; moz-border-radius:5px; webkit-border-radius:5px}
.jqx-window-collapse-button-collapsed-ui-redmond, .jqx-window-collapse-button-collapsed-hover-ui-redmond{background-position:-65px -16px}
.jqx-window-modal-ui-redmond{background: #aaaaaa; opacity: .30;filter:Alpha(Opacity=30);}
.jqx-window-close-button-hover-ui-redmond{background-color:#fff; background-position:-96px -128px; background-image:url(./images/redmond/ui-icons_217bc0_256x240.png); cursor:pointer; width:16px; height:16px}
/*Common Settings*/
.jqx-widget-ui-redmond{font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; font-size:13px; font-style:normal; webkit-tap-highlight-color:rgba(0,0,0,0)}
.jqx-widget-content-ui-redmond{ font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; border-color: #a6c9e2; background: #fcfdfd url(./images/redmond/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x; color: #222222; font-size:13px}
.jqx-widget-content-ui-redmond a{color:#222}
.jqx-widget-header-ui-redmond{font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; border-color: #4297d7; background: #5c9ccc url(./images/redmond/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x; color: #ffffff; font-size:13px}
.jqx-widget-header-ui-redmond a{color:#222}
.jqx-fill-state-normal-ui-redmond{border-color: #c5dbec; background: #dfeffc url(./images/redmond/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x; color: #2e6e9e;}
.jqx-fill-state-normal-ui-redmond a, .jqx-fill-state-normal-ui-redmond a:link, .jqx-fill-state-normal-ui-redmond a:visited{color:#2e6e9e; text-decoration:none}
.jqx-fill-state-hover-ui-redmond{border-color: #79b7e7; background: #d0e5f5 url(./images/redmond/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x; color: #1d5987;}
.jqx-fill-state-hover-ui-redmond a, .jqx-fill-state-hover-ui-redmond a:hover{color:#1d5987; text-decoration:none}
.jqx-fill-state-focus-ui-redmond {border-color: #79b7e7; color: #1d5987; }
.jqx-fill-state-pressed-ui-redmond{border-color: #79b7e7; background: #f5f8f9 url(./images/redmond/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x; color: #e17009; }
.jqx-fill-state-pressed-ui-redmond a, .jqx-fill-state-pressed-ui-redmond a:link, .jqx-fill-state-pressed-ui-redmond a:visited{color:#e17009; text-decoration:none}
.jqx-fill-state-disabled-ui-redmond {cursor: default; color: #000; opacity: .55; filter:Alpha(Opacity=45);}
.jqx-grid-column-menubutton-ui-redmond{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_f9bd01_256x240.png); border-width:0px}

.jqx-input-button-content-ui-redmond{font-size:10px}
.jqx-input-icon-ui-redmond{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-redmond{margin-top:0px; background-position:-65px -147px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-redmond .jqx-fill-state-pressed-ui-redmond{background: #8db9db; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-redmond{background: #8db9db; border-right: none; border-bottom: 1px solid #c5dbec;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-redmond, .jqx-scrollbar-thumb-state-normal-horizontal-ui-redmond{ border: 1px solid #c5dbec; background: #e1effb;}
.jqx-scrollbar-thumb-state-hover-ui-redmond, .jqx-scrollbar-thumb-state-hover-horizontal-ui-redmond{ border: 1px solid #79b7e7; background: #d0e5f5;}
.jqx-scrollbar-thumb-state-pressed-ui-redmond, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-redmond{ border: 1px solid #79b7e7; background: #f5f8f9;}

.jqx-tabs-title-selected-top-ui-redmond
{
    border-color: #c5dbec;
    border-bottom: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's title when the tab is selected and the jqxTab's position property is set to 'bottom' .*/
.jqx-tabs-title-selected-bottom-ui-redmond
{
    border-color: #c5dbec;
    border-top: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'top'.*/
.jqx-tabs-selection-tracker-top-ui-redmond
{
   border-color: #c5dbec;
   border-bottom: 1px solid #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'bottom'.*/
.jqx-tabs-selection-tracker-bottom-ui-redmond
{
   border-color: #c5dbec;
   border-top: 1px solid #fff;
}
/*Slider*/
.jqx-slider-ui-redmond .jqx-fill-state-pressed-ui-redmond{background:#8db9db;}
.jqx-slider-track-ui-redmond{border: 1px solid #c5dbec; background: #fdfefe;}
/*Grid*/
.jqx-grid-cell-selected-ui-redmond{border-color: #c5dbec; color: #e17009; background: #f5f8f9 url(./images/redmond/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x;}
.jqx-grid-cell-sort-ui-redmond, .jqx-grid-cell-filter-ui-redmond, .jqx-grid-cell-pinned-ui-redmond{background:#e1effb;}
.jqx-grid-bottomright-ui-redmond, .jqx-panel-bottomright-ui-redmond, .jqx-listbox-bottomright-ui-redmond, .jqx-scrollbar-state-normal-ui-redmond{background: #fff;}
.jqx-grid-cell-sort-alt-ui-redmond, .jqx-grid-cell-alt-ui-redmond, .jqx-grid-cell-filter-alt-ui-redmond{background: #e1effb;}
.jqx-widget-ui-redmond .jqx-grid-column-header-ui-redmond, .jqx-grid-cell-ui-redmond, .jqx-widget-ui-redmond .jqx-grid-cell-ui-redmond, .jqx-widget-ui-redmond .jqx-grid-group-cell-ui-redmond, .jqx-grid-group-cell-ui-redmond{border-color:#c5dbec}
.jqx-grid-column-header-ui-redmond{font-weight: bold;}
.jqx-grid-group-expand-ui-redmond{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down.png);}
.jqx-grid-group-collapse-ui-redmond{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right.png);}
/*Menu*/
.jqx-menu-item-top-ui-redmond
{
font-weight: bold;
}
.jqx-menu-dropdown-ui-redmond
{
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-redmond{overflow: inherit;}
.jqx-expander-header-ui-redmond{font-weight: bold; margin-top:2px}
.jqx-expander-header-ui-redmond{background: #dfeffc url(./images/redmond/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x; color: #2e6e9e; border:1px solid #c5dbec; border-radius:5px !important; moz-border-radius:5px !important; webkit-border-radius:5px !important}
.jqx-expander-header-hover-ui-redmond{background: #d0e5f5 url(./images/redmond/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x; color: #1d5987; border:1px solid #79b7e7;}
.jqx-expander-header-expanded-ui-redmond{color: #e17009; background:#f5f8f9; border:1px solid #a6c9e2; border-bottom-width:0px; border-top-left-radius:5px !important; border-top-right-radius:5px !important; moz-border-radius-topleft:5px !important; moz-border-radius-topright:5px !important; webkit-border-top-left-radius:5px !important; webkit-border-top-right-radius:5px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-redmond{border-color: #a6c9e2; border-bottom-left-radius:5px !important; border-bottom-right-radius:5px !important; moz-border-radius-bottomleft:5px !important; moz-border-radius-bottomright:5px !important; webkit-border-bottom-left-radius:5px !important; webkit-border-bottom-right-radius:5px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-redmond{position: relative; width: 16px; height: 16px; background-position:-65px -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
.jqx-expander-arrow-bottom-ui-redmond{position: relative; width: 16px; height: 16px;  background-position:0 -16px; background-image:url(./images/redmond/ui-icons_6da8d5_256x240.png)}
/*Scroll Bar*/
.jqx-scrollbar-ui-redmond .jqx-icon-arrow-up-ui-redmond{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-redmond .jqx-icon-arrow-down-ui-redmond{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-redmond .jqx-icon-arrow-left-ui-redmond{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-redmond .jqx-icon-arrow-right-ui-redmond{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-icon-arrow-first-ui-redmond
{
    background-image: url('images/redmond/icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-ui-redmond
{
    background-image: url('images/redmond/icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-layout-ui-redmond
{
    background-color: #4297d7;
    background-image: none;
}
.jqx-layout-pseudo-window-pin-icon-ui-redmond
{
    background-image: url("images/pin-lightblue.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-redmond
{
    background-image: url("images/pinned-lightblue.png");
}
.jqx-docking-layout-group-floating .jqx-window-header-ui-redmond
{
    background-image: none;
}

.jqx-grid-pager-ui-redmond .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-redmond .jqx-icon-arrow-left-ui-redmond{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-redmond .jqx-icon-arrow-right-ui-redmond{
	position: relative;
    top: 6px;
}