﻿.jqx-widget-classic{}
.jqx-fill-state-normal-classic, .jqx-widget-header-classic{ border-color: #aaa; background-color:#E8E8E8; background-image:-webkit-gradient(linear,0 0,0 100%,from(#fafafa),to(#dadada));  background-image:-moz-linear-gradient(top,#fafafa,#dadada);  background-image:-o-linear-gradient(top,#fafafa,#dadada)}
.jqx-fill-state-hover-classic{ border-color:#999;  background-color:#E8E8E8; background-image:-webkit-gradient(linear,0 0,0 100%,from(#fafafa),to(#dadada));  background-image:-moz-linear-gradient(top,#fafafa,#dadada);  background-image:-o-linear-gradient(top,#fafafa,#dadada)}
.jqx-fill-state-pressed-classic{ background-color:#7A7A7A; background-image:-webkit-gradient(linear,0 0,0 100%,from(#989898),to(#696969));  background-image:-moz-linear-gradient(top,#989898,#696969);  background-image:-o-linear-gradient(top,#989898,#696969);  border-color:#666;  color:white;  text-shadow:0 1px 0 #333;  border-image:initial}

.jqx-grid-column-menubutton-classic{ background-color:transparent}
.jqx-calendar-row-header-classic, .jqx-calendar-top-left-header-classic{ background-color:#f2f2f2;  border:0px solid #f2f2f2}
.jqx-calendar-column-header-classic{ background-color:#FFF;  border-top:1px solid #FFF;  border-bottom:1px solid #e9e9e9}
.jqx-scrollbar-state-normal-classic{ background-color:#efefef;  border:1px solid #efefef}
.jqx-scrollbar-button-state-normal-classic{ border:1px solid #ececed;  background-color:#ececed}
.jqx-scrollbar-thumb-state-normal-classic{ background-color:#E8E8E8; background-image:-webkit-gradient(linear,left top,right top,from(#fafafa),to(#dadada));  background-image:-moz-linear-gradient(left,#fafafa,#dadada);  background-image:-o-linear-gradient(left,#fafafa,#dadada);  border:1px solid #bbb}
.jqx-scrollbar-thumb-state-hover-classic{ background-color:#e8e8e8;  border:1px solid #aaa}
.jqx-scrollbar-thumb-state-pressed-classic, .jqx-progressbar-value-vertical-classic{ background-color:#7A7A7A; background-image:-webkit-gradient(linear,left top,right top,from(#989898),to(#696969));    background-image:-moz-linear-gradient(left,#989898,#696969);    background-image:-o-linear-gradient(left,#989898,#696969); border:1px solid #666}

.jqx-icon-arrow-up-selected-classic{background-image:url('images/icon-up-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-down-selected-classic{background-image:url('images/icon-down-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-left-selected-classic{background-image:url('images/icon-left-white.png'); background-repeat:no-repeat; background-position:center}
.jqx-icon-arrow-right-selected-classic{background-image:url('images/icon-right-white.png');background-repeat:no-repeat; background-position:center}
.jqx-scrollbar-classic .jqx-icon-arrow-up-selected-classic{background-image:url('images/icon-up.png'); background-repeat:no-repeat; background-position:center}
.jqx-scrollbar-classic .jqx-icon-arrow-down-selected-classic{background-image:url('images/icon-down.png'); background-repeat:no-repeat; background-position:center}
.jqx-scrollbar-classic .jqx-icon-arrow-left-selected-classic{background-image:url('images/icon-left.png'); background-repeat:no-repeat; background-position:center}
.jqx-scrollbar-classic .jqx-icon-arrow-right-selected-classic{background-image:url('images/icon-right.png');background-repeat:no-repeat; background-position:center}

.jqx-slider-track-horizontal-classic, .jqx-slider-track-vertical-classic{border-color: #e8e8e8; background: #e8e8e8;}
.jqx-slider-rangebar-classic{background:#7A7A7A;}
.jqx-menu-vertical-classic{ background:#E8E8E8; filter: none;}
.jqx-menu-item-arrow-right-selected-classic{background-image:url(./images/icon-right-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-down-selected-classic{background-image:url(./images/icon-down-white.png); background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-up-selected-classic{background-image:url(./images/icon-up-white.png);background-position:100% 50%; background-repeat:no-repeat}
.jqx-menu-item-arrow-left-selected-classic{background-image:url(./images/icon-left-white.png); background-position:0 50%; background-repeat:no-repeat}
.jqx-icon-arrow-first-selected-classic
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-classic
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-radiobutton-classic{border:none; background: none;}
.jqx-radiobutton-default-classic{filter: none; background:transparent url(./images/roundbg_classic_normal.png) left center scroll repeat-x;  border:0px solid #c9c9c9; -moz-border-radius:0px; -webkit-border-radius:0px; border-radius:0px}
.jqx-radiobutton-hover-classic{filter: none;  -moz-border-radius:0px;-webkit-border-radius:0px;border-radius:0px;background:transparent url(./images/roundbg_classic_hover.png) left center scroll repeat-x;     border:0px solid #000}
.jqx-radiobutton-check-checked-classic{filter: none; margin:0px; width:12px;height:12px;background:transparent url(./images/roundbg_check_black.png) left top no-repeat; border:none}
.jqx-radiobutton-check-indeterminate-classic{filter: none; background:transparent url(./images/roundbg_check_indeterminate.png) left top no-repeat; border:none}
.jqx-radiobutton-check-indeterminate-disabled-classic{filter: none; background:transparent url(./images/roundbg_check_disabled.png) left top no-repeat;border:none}
.jqx-fill-state-focus-classic { border-color: #747474;}
.jqx-grid-bottomright-classic, .jqx-panel-bottomright-classic, .jqx-listbox-bottomright-classic{background-color: #efefef;}
.jqx-tabs-title-selected-top-classic, .jqx-tabs-selection-tracker-top-classic {border-color: #aaa; border-bottom: 1px solid #fff; text-shadow:0 1px 0 #f2f2f2; filter: none; color: #222; background: #fff;}
.jqx-tabs-title-selected-bottom-classic, .jqx-tabs-selection-tracker-bottom-classic {border-color: #aaa; border-top: 1px solid #fff; text-shadow:0 1px 0 #f2f2f2; filter: none; color: #222; background: #fff;}
.jqx-icon-calendar-pressed-classic {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-pressed-classic {
    background-image: url('images/icon-time-white.png');
}
.jqx-grid-cell-classic.jqx-grid-cell-selected-classic>.jqx-grid-group-expand-classic {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-classic.jqx-grid-cell-selected-classic>.jqx-grid-group-collapse-classic{
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-classic.jqx-grid-cell-selected-classic>.jqx-grid-group-collapse-rtl-classic {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-classic.jqx-grid-cell-selected-classic>.jqx-grid-group-expand-rtl-classic{
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-layout-classic
{
    background-color: #aaa;
}