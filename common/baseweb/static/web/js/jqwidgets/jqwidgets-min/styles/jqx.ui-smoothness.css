.jqx-rc-tl-ui-smoothness{border-top-left-radius:4px; moz-border-radius-topleft:4px; webkit-border-top-left-radius:4px}
.jqx-rc-tr-ui-smoothness{border-top-right-radius:4px; moz-border-radius-topright:4px; webkit-border-top-right-radius:4px}
.jqx-rc-bl-ui-smoothness{border-bottom-left-radius:4px; moz-border-radius-bottomleft:4px; webkit-border-bottom-left-radius:4px}
.jqx-rc-br-ui-smoothness{border-bottom-right-radius:4px; moz-border-radius-bottomright:4px; webkit-border-bottom-right-radius:4px}
.jqx-rc-t-ui-smoothness{border-top-left-radius:4px; border-top-right-radius:4px; moz-border-radius-topleft:4px; moz-border-radius-topright:4px; webkit-border-top-left-radius:4px; webkit-border-top-right-radius:4px}
.jqx-rc-b-ui-smoothness{border-bottom-left-radius:4px; border-bottom-right-radius:4px; moz-border-radius-bottomleft:4px; moz-border-radius-bottomright:4px; webkit-border-bottom-left-radius:4px; webkit-border-bottom-right-radius:4px}
.jqx-rc-r-ui-smoothness{border-bottom-right-radius:4px; border-top-right-radius:4px; moz-border-radius-bottomright:4px; moz-border-radius-topright:4px; webkit-border-bottom-right-radius:4px; webkit-border-top-right-radius:4px}
.jqx-rc-l-ui-smoothness{border-bottom-left-radius:4px; border-top-left-radius:4px; moz-border-radius-bottomleft:4px; moz-border-radius-topleft:4px; webkit-border-bottom-left-radius:4px; webkit-border-top-left-radius:4px}
.jqx-rc-all-ui-smoothness{border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
/*Grid*/
.jqx-grid-column-sortascbutton-ui-smoothness{background-position:-96px -192px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png); position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px;}
.jqx-grid-column-sortdescbutton-ui-smoothness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-grid-column-menubutton-ui-smoothness{position: relative; max-height: 16px; height: 16px !important; top:50%; margin-top:-8px; background-position:-64px -192px; background-image:url(./images/smoothness/ui-icons_454545_256x240.png); border-width:0px}
/*Tree*/
.jqx-tree-item-arrow-expand-ui-smoothness, .jqx-tree-item-arrow-expand-hover-ui-smoothness{background-position:-65px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-tree-item-arrow-collapse-ui-smoothness, .jqx-tree-item-arrow-collapse-hover-ui-smoothness{background-position:-32px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-menu-item-arrow-right-ui-smoothness, .jqx-menu-item-arrow-right-selected-ui-smoothness{background-position:-32px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-menu-item-arrow-left-ui-smoothness, .jqx-menu-item-arrow-left-selected-ui-smoothness, .jqx-tree-item-arrow-collapse-rtl-ui-smoothness, .jqx-tree-item-arrow-collapse-hover-rtl-ui-smoothness{background-position:-96px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
/*Tabs*/
.jqx-tabs-title-ui-smoothness{border:1px solid #ccc; background:#E6E6E6 url(./images/smoothness/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x;}
.jqx-tabs-header-ui-smoothness{border-width: 1px; margin:2px; border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-tabs-title-bottom-ui-smoothness{border-bottom-color:#ccc; border-top-color: transparent;}
.jqx-tabs-header-bottom-ui-smoothness{margin-top:-2px !important; padding-bottom: 3px; padding-top:1px}

/*Radio Button*/
.jqx-radiobutton-ui-smoothness .jqx-fill-state-pressed-ui-smoothness{background:#222; border:1px solid #000}
/*Calendar*/
.jqx-calendar-cell-ui-smoothness{font-size: 11px; background: #e6e6e6 url(./images/smoothness/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x; border-color:#d3d3d3; padding:.2em; text-align:right; text-decoration:none; moz-border-radius:0px !important; webkit-border-radius:0px !important; border-radius:0px !important}
.jqx-calendar-cell-today-ui-smoothness{background:#ffe45c; border:1px solid #fed22f; color:#363636}
.jqx-calendar-title-container-ui-smoothness{border: 1px solid #aaa; border-bottom-width: 0px; border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px; font-weight: bold;}
.jqx-calendar-month-container-ui-smoothness{border:none !important}
.jqx-calendar-ui-smoothness{padding:2px}
.jqx-calendar-column-cell-ui-smoothness{font-size: 11px; font-weight: bold;}
.jqx-calendar-ui-smoothness .jqx-icon-arrow-left-ui-smoothness{background-image:url(./images/smoothness/ui-icons_222222_256x240.png); background-position: -80px -192px; width:16px; height:16px; left:5px; position:relative}
.jqx-calendar-ui-smoothness .jqx-icon-arrow-right-ui-smoothness{background-image:url(./images/smoothness/ui-icons_222222_256x240.png); background-position: -48px -192px; width:16px; height:16px; right:5px; position:relative}

/*Icons*/
.jqx-icon-arrow-up-ui-smoothness{background-position:0 -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-down-ui-smoothness{background-position:-65px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-left-ui-smoothness{background-position:-96px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-right-ui-smoothness{background-position:-32px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-up-hover-ui-smoothness{background-position:0 -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-down-hover-ui-smoothness{background-position:-65px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-left-selected-ui-smoothness{background-position:-96px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-right-selected-ui-smoothness{background-position:-32px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-up-selected-ui-smoothness{background-position:0 -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-down-selected-ui-smoothness{background-position:-65px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-left-selected-ui-smoothness{background-position:-96px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-arrow-right-selected-ui-smoothness{background-position:-32px -17px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-icon-close-ui-smoothness{background-image:url(./images/smoothness/ui-icons_222222_256x240.png); background-position:-80px -128px; cursor:pointer}
.jqx-icon-close-hover-ui-smoothness{background-image:url(./images/smoothness/ui-icons_222222_256x240.png); background-position:-80px -128px; cursor:pointer}
/*Window*/
.jqx-window-ui-smoothness{padding: 2px;}
.jqx-window-header-ui-smoothness{border: 1px solid #aaa; font-weight: bold; font-size: 11px; moz-border-radius:4px; border-radius:4px; webkit-border-radius:4px}
.jqx-window-content-ui-smoothness{border-width:0px !important}
.jqx-window-close-button-ui-smoothness{background-position:-96px -128px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png);moz-border-radius:4px; border-radius:4px; webkit-border-radius:4px}
.jqx-window-collapse-button-ui-smoothness{background-position:0 -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-window-collapse-button-hover-ui-smoothness{background-image:url(./images/smoothness/ui-icons_222222_256x240.png); background-color:#fff; border-radius:4px; moz-border-radius:4px; webkit-border-radius:4px}
.jqx-window-collapse-button-collapsed-ui-smoothness, .jqx-window-collapse-button-collapsed-hover-ui-smoothness{background-position:-65px -16px}
.jqx-window-modal-ui-smoothness{}
.jqx-window-close-button-hover-ui-smoothness{background-color:#fff; background-position:-96px -128px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png); cursor:pointer; width:16px; height:16px}

/*Common Settings*/
.jqx-widget-ui-smoothness{ font-size:12px; font-style:normal; webkit-tap-highlight-color:rgba(0,0,0,0)}
.jqx-widget-content-ui-smoothness{ background: #ffffff url(./images/smoothness/ui-bg_flat_75_ffffff_40x100.png) 50% 50% repeat-x; color: #222222; border-color: #aaa; font-size:12px}
.jqx-widget-content-ui-smoothness a{color:#222}
.jqx-widget-header-ui-smoothness{ background: #cccccc url(./images/smoothness/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x; border-color: #aaa; color: #222222; font-size:12px}
.jqx-widget-header-ui-smoothness a{color:#222}
.jqx-fill-state-normal-ui-smoothness{background: #e6e6e6 url(./images/smoothness/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x; border-color:#d3d3d3; color:#555}
.jqx-fill-state-normal-ui-smoothness a, .jqx-fill-state-normal-ui-smoothness a:link, .jqx-fill-state-normal-ui-smoothness a:visited{color:#555; text-decoration:none}
.jqx-fill-state-hover-ui-smoothness{border-color: #999999; background: #dadada url(./images/smoothness/ui-bg_glass_75_dadada_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #212121;}
.jqx-fill-state-hover-ui-smoothness a, .jqx-fill-state-hover-ui-smoothness a:hover{color:#212121; text-decoration:none}
.jqx-fill-state-focus-ui-smoothness {border-color: #aaaaaa; }
.jqx-fill-state-pressed-ui-smoothness{border-color: #aaaaaa; background: #ffffff url(./images/smoothness/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #212121; }
.jqx-fill-state-pressed-ui-smoothness a, .jqx-fill-state-pressed-ui-smoothness a:link, .jqx-fill-state-pressed-ui-smoothness a:visited{color:#212121; text-decoration:none}

.jqx-input-button-content-ui-smoothness{font-size:10px}
.jqx-input-icon-ui-smoothness{margin-left:2px; margin-top:-1px}
.jqx-checkbox-check-checked-ui-smoothness{margin-top:0px; background-position:-65px -147px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
/*Progress Bar*/
.jqx-progressbar-ui-smoothness .jqx-fill-state-pressed-ui-smoothness{background: #cccccc; border-bottom: none;}
.jqx-progressbar-value-vertical-ui-smoothness{background: #cccccc; border-right: none; border-bottom: 1px solid #aaa;}
/*ScrollBar */
.jqx-scrollbar-thumb-state-normal-ui-smoothness, .jqx-scrollbar-thumb-state-normal-horizontal-ui-smoothness{ border: 1px solid #aaa; background: #cccccc;}
.jqx-scrollbar-thumb-state-hover-ui-smoothness, .jqx-scrollbar-thumb-state-hover-horizontal-ui-smoothness{ background: #dadada;}
.jqx-scrollbar-thumb-state-pressed-ui-smoothness, .jqx-scrollbar-thumb-state-pressed-horizontal-ui-smoothness{ background: #ffffff;}

.jqx-tabs-title-selected-top-ui-smoothness
{
    border-color: #aaa;
    border-bottom: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's title when the tab is selected and the jqxTab's position property is set to 'bottom' .*/
.jqx-tabs-title-selected-bottom-ui-smoothness
{
    border-color: #aaa;
    border-top: 1px solid #fff;
    background-color: #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'top'.*/
.jqx-tabs-selection-tracker-top-ui-smoothness
{
   border-color: #aaa;
   border-bottom: 1px solid #fff;
}
/*applied to the tab's selection tracker when the jqxTab's position property is set to 'bottom'.*/
.jqx-tabs-selection-tracker-bottom-ui-smoothness
{
   border-color: #aaa;
   border-top: 1px solid #fff;
}
/*Slider*/
.jqx-slider-ui-smoothness .jqx-fill-state-pressed-ui-smoothness{background:#cccccc;}
.jqx-slider-track-ui-smoothness{border: 1px solid #aaa; background: #fff;}
/*Grid*/
.jqx-grid-cell-sort-ui-smoothness, .jqx-grid-cell-filter-ui-smoothness, .jqx-grid-cell-pinned-ui-smoothness, .jqx-grid-cell-selected-ui-smoothness{background:#e2e2e2;}
.jqx-grid-bottomright-ui-smoothness, .jqx-panel-bottomright-ui-smoothness, .jqx-listbox-bottomright-ui-smoothness, .jqx-scrollbar-state-normal-ui-smoothness{background: #fff;}
.jqx-grid-group-expand-ui-smoothness{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-down.png);}
.jqx-grid-group-collapse-ui-smoothness{background-position: 50% 50%; background-repeat: no-repeat;background-image: url(./images/icon-right.png);}
/*Menu*/
.jqx-menu-dropdown-ui-smoothness
{
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
    right: -1px;
}
/*Navigation Bar*/
.jqx-navigationbar-ui-smoothness{overflow: inherit;}
.jqx-expander-header-ui-smoothness{margin-bottom:2px; margin-top:2px}
.jqx-expander-header-ui-smoothness{background:#e6e6e6 url(./images/smoothness/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x; border:1px solid #aaa; border-radius:4px !important; moz-border-radius:4px !important; webkit-border-radius:4px !important}
.jqx-expander-header-hover-ui-smoothness{border:1px solid #aaa;}
.jqx-expander-header-expanded-ui-smoothness{background:#fff url(./images/smoothness/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x; border:1px solid #aaa; border-bottom-width:0px; border-top-left-radius:4px !important; border-top-right-radius:4px !important; moz-border-radius-topleft:4px !important; moz-border-radius-topright:4px !important; webkit-border-top-left-radius:4px !important; webkit-border-top-right-radius:4px !important; border-bottom-left-radius:0px !important; border-bottom-right-radius:0px !important; moz-border-radius-bottomleft:0px !important; moz-border-radius-bottomright:0px !important; webkit-border-bottom-left-radius:0px !important; webkit-border-bottom-right-radius:0px !important;  margin-bottom:0px}
.jqx-expander-content-bottom-ui-smoothness{border-bottom-left-radius:4px !important; border-bottom-right-radius:4px !important; moz-border-radius-bottomleft:4px !important; moz-border-radius-bottomright:4px !important; webkit-border-bottom-left-radius:4px !important; webkit-border-bottom-right-radius:4px !important; border-top-width:0px !important}
.jqx-expander-arrow-top-ui-smoothness{background-position:-65px -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
.jqx-expander-arrow-bottom-ui-smoothness{background-position:0 -16px; background-image:url(./images/smoothness/ui-icons_222222_256x240.png)}
/*Scroll Bar*/
.jqx-scrollbar-ui-smoothness .jqx-icon-arrow-up-ui-smoothness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-smoothness .jqx-icon-arrow-down-ui-smoothness{width: 16px; height: 16px; margin-left: auto;}
.jqx-scrollbar-ui-smoothness .jqx-icon-arrow-left-ui-smoothness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-scrollbar-ui-smoothness .jqx-icon-arrow-right-ui-smoothness{width: 16px; height: 16px; position: relative; top: 50%; margin-top: -8px;}
.jqx-layout-ui-smoothness
{
    background-color: #aaa;
}
.jqx-layout-pseudo-window-pin-icon-ui-smoothness
{
    background-image: url("images/pin-black.png");
}
.jqx-layout-pseudo-window-pinned-icon-ui-smoothness
{
    background-image: url("images/pinned-black.png");
}
.jqx-docking-layout-group-floating .jqx-window-header-ui-smoothness
{
    background-image: none;
}


.jqx-grid-pager-ui-smoothness .jqx-button {
	overflow: hidden;
}
.jqx-grid-pager-ui-smoothness .jqx-icon-arrow-left-ui-smoothness{
	position: relative;
    top: 6px;
}
.jqx-grid-pager-ui-smoothness .jqx-icon-arrow-right-ui-smoothness{
	position: relative;
    top: 6px;
}