:root { 
   --jqx-accent-color-theme: rgba(249, 9, 217, 1);
   --jqx-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-header-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-border-color-theme: rgba(221, 221, 221, 1);
   --jqx-border-radius-theme: 4px;
   --jqx-font-family-theme: "Roboto", "Helvetice Neue", "Helvetica", "Arial", sans-serif;
   --jqx-font-size-theme: 14px;
   --jqx-hovered-state-text-color-theme: rgba(51, 51, 51, 1);
   --jqx-hovered-state-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-hovered-state-content-text-color-theme: rgba(0, 119, 190, 1);
   --jqx-hovered-state-content-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-focused-state-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-active-state-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-arrow-up-hovered-color-theme: url('images/metro-icon-up.png');
   --jqx-arrow-down-hovered-color-theme: url('images/metro-icon-down.png');
   --jqx-arrow-up-selected-color-theme: url('images/metro-icon-up-white.png');
   --jqx-arrow-down-selected-color-theme: url('images/metro-icon-down-white.png');
   --jqx-accordion-item-title-text-color-theme: rgba(51, 51, 51, 1);
   --jqx-accordion-item-title-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-accordion-item-title-border-color-theme: rgba(224, 224, 224, 1);
   --jqx-accordion-item-title-hovered-text-color-theme: rgba(51, 51, 51, 1);
   --jqx-accordion-item-title-hovered-background-color-theme: rgba(246, 246, 246, 1);
   --jqx-accordion-item-title-hovered-border-color-theme: rgba(246, 246, 246, 1);
   --jqx-accordion-item-title-selected-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-accordion-item-title-selected-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-accordion-item-title-selected-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-accordion-item-title-focused-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-accordion-item-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-accordion-item-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-accordion-item-focused-text-color-theme: rgba(224, 224, 224, 1);
   --jqx-accordion-item-focused-background-color-theme: rgba(253, 253, 253, 1);
   --jqx-accordion-item-focused-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-accordion-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-accordion-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-accordion-arrow-up-hovered-color-theme: url('images/metro-icon-up.png');
   --jqx-accordion-arrow-down-hovered-color-theme: url('images/metro-icon-down.png');
   --jqx-accordion-arrow-down-selected-color-theme: url('images/metro-icon-up-white.png');
   --jqx-button-default-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-button-default-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-button-default-hovered-state-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-button-default-hovered-state-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-button-default-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-default-focused-state-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-button-default-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-default-active-state-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-button-primary-text-color-theme: rgba(0, 119, 190, 1);
   --jqx-button-primary-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-primary-hovered-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-primary-hovered-state-background-color-theme: rgba(0, 119, 190, 1);
   --jqx-button-primary-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-primary-focused-state-background-color-theme: rgba(0, 119, 190, 1);
   --jqx-button-primary-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-primary-active-state-background-color-theme: rgba(0, 119, 190, 1);
   --jqx-button-success-text-color-theme: rgba(92, 184, 92, 1);
   --jqx-button-success-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-success-hovered-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-success-hovered-state-background-color-theme: rgba(92, 184, 92, 1);
   --jqx-button-success-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-success-focused-state-background-color-theme: rgba(92, 184, 92, 1);
   --jqx-button-success-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-success-active-state-background-color-theme: rgba(92, 184, 92, 1);
   --jqx-button-warning-text-color-theme: rgba(240, 173, 78, 1);
   --jqx-button-warning-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-warning-hovered-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-warning-hovered-state-background-color-theme: rgba(240, 173, 78, 1);
   --jqx-button-warning-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-warning-focused-state-background-color-theme: rgba(240, 173, 78, 1);
   --jqx-button-warning-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-warning-active-state-background-color-theme: rgba(240, 173, 78, 1);
   --jqx-button-danger-text-color-theme: rgba(217, 83, 79, 1);
   --jqx-button-danger-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-danger-hovered-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-danger-hovered-state-background-color-theme: rgba(217, 83, 79, 1);
   --jqx-button-danger-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-danger-focused-state-background-color-theme: rgba(217, 83, 79, 1);
   --jqx-button-danger-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-danger-active-state-background-color-theme: rgba(217, 83, 79, 1);
   --jqx-button-info-text-color-theme: rgba(91, 192, 222, 1);
   --jqx-button-info-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-info-hovered-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-info-hovered-state-background-color-theme: rgba(91, 192, 222, 1);
   --jqx-button-info-focused-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-info-focused-state-background-color-theme: rgba(91, 192, 222, 1);
   --jqx-button-info-active-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-button-info-active-state-background-color-theme: rgba(91, 192, 222, 1);
   --jqx-editors-text-editors-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-text-editors-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-text-editors-border-color-theme: rgba(221, 221, 221, 1);
   --jqx-editors-text-editors-border-radius-theme: 4px;
   --jqx-editors-text-editors-focused-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-editors-dat-themeеtimeinput-calendar-icon-color: url('images/icon-calendar.png');
   --jqx-editors-dat-themeеtimeinput-calendar-icon-selected-color: url('images/icon-calendar-white.png');
   --jqx-editors-combobox-multi-item-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-combobox-multi-item-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-editors-calendar-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-calendar-header-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-calendar-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-editors-calendar-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-editors-calendar-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-calendar-today-cell-text-color-theme: rgba(137, 137, 137, 1);
   --jqx-editors-calendar-today-cell-background-color-theme: rgba(255, 255, 219, 1);
   --jqx-editors-calendar-today-cell-border-color-theme: rgba(253, 192, 102, 1);
   --jqx-editors-calendar-current-month-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-calendar-other-month-text-color-theme: rgba(137, 137, 137, 1);
   --jqx-editors-calendar-hovered-cell-text-color-theme: rgba(0, 119, 190, 1);
   --jqx-editors-calendar-hovered-cell-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-editors-calendar-selected-cell-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-calendar-selected-cell-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-editors-switch-thumb-color-theme: rgba(249, 9, 217, 1);
   --jqx-editors-switch-on-label-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-switch-on-label-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-switch-off-label-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-editors-switch-off-label-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-file-uploader-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-editors-file-uploader-filename-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-grid-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-header-text-weight-theme: normal;
   --jqx-grid-header-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-grid-row-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-row-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-grid-row-alternation-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-row-alternation-background-color-theme: rgba(249, 249, 249, 1);
   --jqx-grid-hovered-row-text-color-theme: rgba(0, 119, 190, 1);
   --jqx-grid-hovered-row-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-grid-selected-row-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-grid-selected-row-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-grid-selected-row-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-grid-group-row-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-group-row-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-grid-editor-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-grid-editor-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-grid-pinned-cells-background-color-theme: rgba(229, 229, 229, 1);
   --jqx-grid-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-grid-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-grid-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-grid-close-button-color-theme: url('images/close.png');
   --jqx-grid-arrow-down-hovered-color-theme: url('images/metro-icon-down.png');
   --jqx-grid-arrow-right-hovered-color-theme: url('images/metro-icon-right.png');
   --jqx-grid-arrow-left-hovered-color-theme: url('images/metro-icon-left.png');
   --jqx-grid-arrow-down-selected-color-theme: url('images/metro-icon-down-white.png');
   --jqx-grid-arrow-right-selected-color-theme: url('images/metro-icon-right-white.png');
   --jqx-grid-arrow-left-selected-color-theme: url('images/metro-icon-left-white.png');
   --jqx-grid-header-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-grid-header-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-grid-menu-button-color-theme: url('images/icon-menu-small.png');
   --jqx-grid-groups-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-grid-groups-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-grid-groups-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-list-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-list-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-list-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-list-header-background-color-theme: rgba(255, 255, 255, 0.5);
   --jqx-list-hovered-item-state-text-color-theme: rgba(0, 119, 190, 1);
   --jqx-list-hovered-item-state-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-list-selected-item-state-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-list-selected-item-state-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-list-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-list-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-list-arrow-left-selected-color-theme: url('images/metro-icon-left-white.png');
   --jqx-list-arrow-right-selected-color-theme: url('images/metro-icon-right-white.png');
   --jqx-menu-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-menu-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-menu-dropdown-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-menu-dropdown-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-menu-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-menu-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-menu-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-menu-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-menu-hovered-item-color-theme: rgba(0, 119, 190, 1);
   --jqx-menu-hovered-item-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-menu-selected-item-color-theme: rgba(255, 255, 255, 1);
   --jqx-menu-selected-item-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-menu-selected-item-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-menu-arrow-up-selected-color-theme: url('images/metro-icon-up-white.png');
   --jqx-menu-arrow-down-selected-color-theme: url('images/metro-icon-down-white.png');
   --jqx-menu-arrow-left-selected-color-theme: url('images/metro-icon-left-white.png');
   --jqx-menu-arrow-right-selected-color-theme: url('images/metro-icon-right-white.png');
   --jqx-navbar-item-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-navbar-item-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-navbar-hovered-item-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-navbar-hovered-item-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-navbar-selected-item-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-navbar-selected-item-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-overlays-content-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-overlays-content-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-overlays-shader-background-color-theme: rgba(0, 0, 0, 0.6);
   --jqx-overlays-popup-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-overlays-popup-header-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-overlays-tooltip-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-overlays-tooltip-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-overlays-toast-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-overlays-toast-info-background-color-theme: rgba(91, 192, 222, 1);
   --jqx-overlays-toast-warning-background-color-theme: rgba(240, 173, 78, 1);
   --jqx-overlays-toast-error-background-color-theme: rgba(217, 83, 79, 1);
   --jqx-overlays-toast-success-background-color-theme: rgba(92, 184, 92, 1);
   --jqx-pivotgrid-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-pivotgrid-header-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-pivotgrid-cell-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-pivotgrid-cell-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-pivotgrid-selected-cell-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-pivotgrid-selected-cell-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-pivotgrid-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-pivotgrid-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-pivotgrid-menu-button-color-theme: url('images/icon-menu-small.png');
   --jqx-progressbar-bar-background-color-theme: rgba(247, 247, 247, 1);
   --jqx-progressbar-selected-range-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-progressbar-label-color-theme: rgba(85, 85, 85, 1);
   --jqx-scheduler-header-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-scheduler-header-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-scheduler-header-border-color-theme: rgba(221, 221, 221, 1);
   --jqx-scheduler-header-buttons-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-scheduler-header-buttons-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-scheduler-header-buttons-hovered-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-scheduler-header-buttons-hovered-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-scheduler-header-buttons-selected-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-scheduler-header-buttons-selected-background-color-theme: rgba(0, 119, 190, 1);
   --jqx-scheduler-border-color-theme: rgba(221, 221, 221, 1);
   --jqx-scheduler-not-work-time-cells-color-theme: rgba(238, 238, 238, 1);
   --jqx-scheduler-hovered-cell-background-color-theme: rgba(227, 245, 251, 1);
   --jqx-scheduler-hovered-cell-border-color-theme: rgba(227, 245, 251, 1);
   --jqx-scheduler-selected-cell-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-scheduler-selected-cell-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-scheduler-weeks-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-scheduler-weeks-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-scheduler-weeks-arrow-right-selected-color-theme: url('images/metro-icon-right-white.png');
   --jqx-scheduler-weeks-arrow-left-selected-color-theme: url('images/metro-icon-left-white.png');
   --jqx-scheduler-calendar-icon-color-theme: url('images/icon-calendar.png');
   --jqx-scheduler-calendar-icon-selected-color-theme: url('images/icon-calendar-white.png');
   --jqx-scrollbar-scrollbar-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-scrollbar-scrollbar-thumb-background-color-theme: rgba(246, 246, 246, 1);
   --jqx-scrollbar-scrollbar-thumb-hovered-background-color-theme: rgba(230, 230, 230, 1);
   --jqx-scrollbar-scrollbar-thumb-active-background-color-theme: rgba(217, 217, 217, 1);
   --jqx-scrollbar-scrollbar-arrow-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-scrollbar-arrow-up-color-theme: url('images/metro-icon-up.png');
   --jqx-scrollbar-arrow-down-color-theme: url('images/metro-icon-down.png');
   --jqx-scrollbar-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-scrollbar-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-scrollbar-scrollbar-arrow-hovered-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-scrollbar-arrow-up-hovered-color-theme: url('images/metro-icon-up.png');
   --jqx-scrollbar-arrow-down-hovered-color-theme: url('images/metro-icon-down.png');
   --jqx-scrollbar-arrow-left-hovered-color-theme: url('images/metro-icon-left.png');
   --jqx-scrollbar-arrow-right-hovered-color-theme: url('images/metro-icon-right.png');
   --jqx-scrollbar-scrollbar-arrow-active-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-scrollbar-arrow-up-active-color-theme: url('images/metro-icon-up.png');
   --jqx-scrollbar-arrow-down-active-color-theme: url('images/metro-icon-down.png');
   --jqx-scrollbar-arrow-left-active-color-theme: url('images/metro-icon-left.png');
   --jqx-scrollbar-arrow-right-active-color-theme: url('images/metro-icon-right.png');
   --jqx-scrollview-indicator-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-scrollview-selected-indicator-background-color-theme: rgba(0, 119, 190, 1);
   --jqx-slider-slider-bar-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-slider-selected-slider-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-slider-thumb-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-focused-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-tooltip-text-color-theme: rgba(255, 255, 255, 1);
   --jqx-slider-tooltip-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-tooltip-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-arrows-background-color-theme: rgba(255, 255, 255, 0);
   --jqx-slider-arrows-hovered-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-slider-arrows-selected-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-slider-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-slider-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-slider-arrow-left-hovered-color-theme: url('images/metro-icon-left.png');
   --jqx-slider-arrow-right-hovered-color-theme: url('images/metro-icon-right.png');
   --jqx-slider-arrow-left-selected-color-theme: url('images/metro-icon-left-white.png');
   --jqx-slider-arrow-right-selected-color-theme: url('images/metro-icon-right-white.png');
   --jqx-tabs-header-background-color-theme: rgba(248, 248, 248, 1);
   --jqx-tabs-tab-text-color-theme: rgba(51, 51, 51, 1);
   --jqx-tabs-hovered-tab-text-color-theme: rgba(51, 51, 51, 1);
   --jqx-tabs-hovered-tab-background-color-theme: rgba(240, 240, 240, 1);
   --jqx-tabs-selected-tab-text-color-theme: rgba(249, 9, 217, 1);
   --jqx-tabs-selected-tab-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-tabs-selected-tab-bottom-border-color-theme: rgba(249, 9, 217, 1);
   --jqx-tabs-border-color-theme: rgba(221, 221, 221, 1);
   --jqx-tabs-content-text-color-theme: rgba(85, 85, 85, 1);
   --jqx-tabs-content-background-color-theme: rgba(255, 255, 255, 1);
   --jqx-tabs-arrow-left-color-theme: url('images/metro-icon-left.png');
   --jqx-tabs-arrow-right-color-theme: url('images/metro-icon-right.png');
   --jqx-toolbar-background-color-theme: rgba(250, 250, 250, 1);
   --jqx-treeview-hovered-tree-item-color-theme: rgba(0, 119, 190, 1);
   --jqx-treeview-hovered-tree-item-background-color-theme: rgba(225, 245, 254, 1);
   --jqx-treeview-selected-tree-item-color-theme: rgba(255, 255, 255, 1);
   --jqx-treeview-selected-tree-item-background-color-theme: rgba(249, 9, 217, 1);
   --jqx-treeview-arrow-expanded-color-theme: url('images/metro-icon-down.png');
   --jqx-treeview-arrow-collapsed-color-theme: url('images/metro-icon-down.png');
}

.jqx-widget-theme {
    font-family: var(--jqx-font-family-theme);
    font-size: var(--jqx-font-size-theme);
    color: var(--jqx-text-color-theme);
}
.jqx-widget-content-theme {
    font-family: var(--jqx-font-family-theme);
    font-size: var(--jqx-font-size-theme);
    color: var(--jqx-text-color-theme);
}
.jqx-widget-header-theme {
    font-family: var(--jqx-font-family-theme);
    font-size: var(--jqx-font-size-theme);
    color: var(--jqx-header-text-color-theme) !important;
   	background-color: var(--jqx-header-background-color-theme) !important; 
}

/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-theme {
    -moz-border-radius-topleft: var(--jqx-border-radius-theme);
    -webkit-border-top-left-radius: var(--jqx-border-radius-theme);
    border-top-left-radius: var(--jqx-border-radius-theme);
}
/*top-right rounded Corners*/
.jqx-rc-tr-theme {
    -moz-border-radius-topright: var(--jqx-border-radius-theme);
    -webkit-border-top-right-radius: var(--jqx-border-radius-theme);
    border-top-right-radius: var(--jqx-border-radius-theme);
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-theme {
    -moz-border-radius-bottomleft: var(--jqx-border-radius-theme);
    -webkit-border-bottom-left-radius: var(--jqx-border-radius-theme);
    border-bottom-left-radius: var(--jqx-border-radius-theme);
}
/*bottom-right rounded Corners*/
.jqx-rc-br-theme {
    -moz-border-radius-bottomright: var(--jqx-border-radius-theme);
    -webkit-border-bottom-right-radius: var(--jqx-border-radius-theme);
    border-bottom-right-radius: var(--jqx-border-radius-theme);
}
/*top rounded Corners*/
.jqx-rc-t-theme {
    -moz-border-radius-topleft: var(--jqx-border-radius-theme);
    -webkit-border-top-left-radius: var(--jqx-border-radius-theme);
    border-top-left-radius: var(--jqx-border-radius-theme);
    -moz-border-radius-topright: var(--jqx-border-radius-theme);
    -webkit-border-top-right-radius: var(--jqx-border-radius-theme);
    border-top-right-radius: var(--jqx-border-radius-theme);
}
/*bottom rounded Corners*/
.jqx-rc-b-theme {
    -moz-border-radius-bottomleft: var(--jqx-border-radius-theme);
    -webkit-border-bottom-left-radius: var(--jqx-border-radius-theme);
    border-bottom-left-radius: var(--jqx-border-radius-theme);
    -moz-border-radius-bottomright: var(--jqx-border-radius-theme);
    -webkit-border-bottom-right-radius: var(--jqx-border-radius-theme);
    border-bottom-right-radius: var(--jqx-border-radius-theme);
}
/*right rounded Corners*/
.jqx-rc-r-theme {
    -moz-border-radius-topright: var(--jqx-border-radius-theme);
    -webkit-border-top-right-radius: var(--jqx-border-radius-theme);
    border-top-right-radius: var(--jqx-border-radius-theme);
    -moz-border-radius-bottomright: var(--jqx-border-radius-theme);
    -webkit-border-bottom-right-radius: var(--jqx-border-radius-theme);
    border-bottom-right-radius: var(--jqx-border-radius-theme);
}
/*left rounded Corners*/
.jqx-rc-l-theme {
    -moz-border-radius-topleft: var(--jqx-border-radius-theme);
    -webkit-border-top-left-radius: var(--jqx-border-radius-theme);
    border-top-left-radius: var(--jqx-border-radius-theme);
    -moz-border-radius-bottomleft: var(--jqx-border-radius-theme);
    -webkit-border-bottom-left-radius: var(--jqx-border-radius-theme);
    border-bottom-left-radius: var(--jqx-border-radius-theme);
}
/*all rounded Corners*/
.jqx-rc-all-theme {
    -moz-border-radius: var(--jqx-border-radius-theme);
    -webkit-border-radius: var(--jqx-border-radius-theme);
    border-radius: var(--jqx-border-radius-theme);
}

.jqx-widget-theme, .jqx-widget-header-theme, .jqx-fill-state-normal-theme,
.jqx-widget-content-theme, .jqx-fill-state-hover-theme, .jqx-fill-state-pressed-theme {
    font-family: var(--jqx-font-family-theme);
    font-size: var(--jqx-font-size-theme);
}

.jqx-widget-content-theme {
    background-color: var(--jqx-background-color-theme);
    border-color: var(--jqx-border-color-theme);
}
.jqx-widget-header-theme {
    border-color: var(--jqx-border-color-theme);
    *zoom: 1;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.jqx-widget-theme input::selection, input.jqx-input-widget-theme::selection, .jqx-widget-content-theme input::selection {
    background: var(--jqx-active-state-background-color-theme);
    color: var(--jqx-active-state-text-color-theme);
}
.jqx-button-theme {
    transition: color .2s ease-in-out,background-color .2s ease-in-out,border-color .2s ease-in-out,box-shadow .2s ease-in-out;
}
.jqx-button-theme, .jqx-fill-state-normal-theme  {
    color: var(--jqx-button-default-text-color-theme);
  	background: var(--jqx-button-default-background-color-theme);
    border-color: var(--jqx-border-color-theme);
    *zoom: 1;
}

.jqx-fill-state-hover-theme {
    color: var(--jqx-hovered-state-text-color-theme);
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.3);
    background-color: var(--jqx-hovered-state-background-color-theme);
 }
.jqx-fill-state-pressed-theme {
    color: var(--jqx-active-state-text-color-theme) !important;
    background-color: var(--jqx-active-state-background-color-theme); 
    border-color: var(--jqx-active-state-background-color-theme); ;
    *background-color: var(--jqx-active-state-text-color-theme);
}

.jqx-fill-state-hover-theme, .jqx-fill-state-focus-theme {
    color: var(--jqx-hovered-state-text-color-theme);
    text-decoration: none;
}
.jqx-fill-state-focus-theme, .jqx-item-theme.jqx-fill-state-focus {
    border-color: var(--jqx-focused-state-background-color-theme);
}
.jqx-fill-state-pressed-theme.jqx-fill-state-hover-theme, .jqx-dropdownlist-state-selected-theme{
    color: var(--jqx-focused-state-text-color-theme);
}

.jqx-dropdownlist-state-hover-theme {
    background-color: var(--jqx-hovered-state-background-color-theme);
    color: var(--jqx-hovered-state-text-color-theme);
}

.jqx-datetimeinput-theme .jqx-action-button-theme.jqx-fill-state-hover{
    border-color: var(--jqx-border-color-theme);
}
.jqx-datetimeinput-theme.jqx-fill-state-focus .jqx-action-button-theme{
    border-color: var(--jqx-accent-color-theme); 
}
.jqx-filter-input-theme:focus {
    border-color: var(--jqx-accent-color-theme) !important;
}

.jqx-button-theme  {
    color: var(--jqx-button-default-text-color-theme);
    border-color: var(--jqx-border-color-theme);
    *zoom: 1;
}

.jqx-button-theme.jqx-fill-state-hover {
    color: var(--jqx-button-default-hovered-state-text-color-theme);
  	background-color: var(--jqx-button-default-hovered-state-background-color-theme);
    *zoom: 1;
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-button-theme.jqx-fill-state-pressed {
    color: var(--jqx-button-default-active-state-text-color-theme) !important;
  	background-color: var(--jqx-button-default-active-state-background-color-theme) !important;
    border-color: var(--jqx-button-default-active-state-background-color-theme) !important;
    *zoom: 1;
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}

.jqx-button-theme:focus {
    color: var(--jqx-button-default-focused-state-text-color-theme);
    background-color: var(--jqx-button-default-focused-state-background-color-theme);
}

.jqx-dropdownlist-state-normal-theme, .jqx-dropdownlist-state-hover-theme, .jqx-dropdownlist-state-selected-theme,
.jqx-scrollbar-button-state-hover-theme, .jqx-scrollbar-button-state-normal-theme, .jqx-scrollbar-button-state-pressed-theme,
.jqx-scrollbar-thumb-state-normal-horizontal-theme, .jqx-scrollbar-thumb-state-hover-horizontal-theme, .jqx-scrollbar-thumb-state-pressed-horizontal-theme,
.jqx-scrollbar-thumb-state-normal-theme, .jqx-scrollbar-thumb-state-pressed-theme, .jqx-tree-item-hover-theme, .jqx-tree-item-selected-theme,
.jqx-tree-item-theme, .jqx-menu-item-theme, .jqx-menu-item-hover-theme, .jqx-menu-item-selected-theme, .jqx-menu-item-top-theme, .jqx-menu-item-top-hover-theme, 
.jqx-menu-item-top-selected-theme, .jqx-slider-button-theme, .jqx-slider-slider-theme
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-primary-theme
{
  color: var(--jqx-button-primary-text-color-theme) !important;
  background: var(--jqx-button-primary-background-color-theme) !important;
  border-color: var(--jqx-button-primary-text-color-theme) !important;
  text-shadow: none !important;
}
.jqx-primary-theme.jqx-dropdownlist-state-normal-theme,
.jqx-primary-theme.jqx-slider-button-theme,
.jqx-primary-theme.jqx-slider-slider-theme,
.jqx-primary-theme.jqx-combobox-arrow-normal-theme,
.jqx-primary-theme.jqx-combobox-arrow-hover-theme,
.jqx-primary-theme.jqx-action-button-theme,
.jqx-primary-theme:hover,
.jqx-primary-theme:active,
.jqx-primary-theme.active,
.jqx-primary-theme.disabled,
.jqx-primary-theme[disabled] {
  color: var(--jqx-button-primary-hovered-state-text-color-theme) !important;
  background: var(--jqx-button-primary-hovered-state-background-color-theme) !important;
  border-color: var(--jqx-button-primary-hovered-state-background-color-theme) !important;
  text-shadow: none !important;
}

.jqx-primary-theme:focus {
    color: var(--jqx-button-primary-focused-state-text-color-theme) !important;
    background: var(--jqx-button-primary-focused-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme.jqx-primary-theme,
.jqx-primary-theme:active,
.jqx-primary-theme.active {
  color: var(--jqx-button-primary-active-state-text-color-theme) !important;
  background-color: var(--jqx-button-primary-active-state-background-color-theme) !important;
  border-color: var(--jqx-button-primary-active-state-background-color-theme) !important;
  text-shadow: none !important;
}

.jqx-success-theme
{
  color: var(--jqx-button-success-text-color-theme) !important;
  background: var(--jqx-button-success-background-color-theme) !important;
  border-color: var(--jqx-button-success-text-color-theme) !important;
  text-shadow: none !important;
}
.jqx-success-theme.jqx-dropdownlist-state-normal-theme,
.jqx-success-theme.jqx-slider-button-theme,
.jqx-success-theme.jqx-slider-slider-theme,
.jqx-success-theme.jqx-combobox-arrow-normal-theme,
.jqx-success-theme.jqx-combobox-arrow-hover-theme,
.jqx-success-theme.jqx-action-button-theme,
.jqx-success-theme:hover,
.jqx-success-theme:active,
.jqx-success-theme.active,
.jqx-success-theme.disabled,
.jqx-success-theme[disabled] {
  color: var(--jqx-button-success-hovered-state-text-color-theme) !important;
  background: var(--jqx-button-success-hovered-state-background-color-theme) !important;
  border-color: var(--jqx-button-success-hovered-state-background-color-theme) !important;
  text-shadow: none !important;
}

.jqx-success-theme:focus {
    color: var(--jqx-button-success-focused-state-text-color-theme) !important;
    background: var(--jqx-button-success-focused-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme.jqx-success-theme,
.jqx-success-theme:active,
.jqx-success-theme.active {
  text-shadow: none !important;
  color: var(--jqx-button-success-active-state-text-color-theme) !important;
  background: var(--jqx-button-success-active-state-background-color-theme) !important;
  border-color: var(--jqx-button-success-active-state-text-color-theme) !important;
}

.jqx-inverse-theme
{
  text-shadow: none !important;
  color: #666  !important;
  background: #fff  !important;
  border-color: #cccccc!important;
}
.jqx-inverse-theme.jqx-dropdownlist-state-normal-theme,
.jqx-inverse-theme.jqx-slider-button-theme,
.jqx-inverse-theme.jqx-slider-slider-theme,
.jqx-inverse-theme.jqx-combobox-arrow-hover-theme,
.jqx-inverse-theme.jqx-combobox-arrow-normal-theme,
.jqx-inverse-theme.jqx-action-button-theme,
.jqx-inverse-theme:hover,
.jqx-inverse-theme:focus,
.jqx-inverse-theme:active,
.jqx-inverse-theme.active,
.jqx-inverse-theme.disabled,
.jqx-inverse-theme[disabled] {
  text-shadow: none !important;
  color: #666 !important;
  background: #cccccc !important;
  border-color: #cccccc!important;
}

.jqx-fill-state-pressed-theme.jqx-inverse-theme,
.jqx-inverse-theme:active,
.jqx-inverse-theme.active {
  text-shadow: none !important;
 color: #666  !important;
  background: #cccccc  !important;
  border-color: #cccccc!important;
}


.jqx-danger-theme
{
  text-shadow: none !important;
  color: var(--jqx-button-danger-text-color-theme) !important;
  background: var(--jqx-button-danger-background-color-theme) !important;
  border-color: var(--jqx-button-danger-text-color-theme) !important;
}
.jqx-danger-theme.jqx-dropdownlist-state-normal-theme,
.jqx-danger-theme.jqx-slider-button-theme,
.jqx-danger-theme.jqx-slider-slider-theme,
.jqx-danger-theme.jqx-combobox-arrow-hover-theme,
.jqx-danger-theme.jqx-combobox-arrow-normal-theme,
.jqx-danger-theme.jqx-action-button-theme,
.jqx-danger-theme:hover,
.jqx-danger-theme:focus,
.jqx-danger-theme:active,
.jqx-danger-theme.active,
.jqx-danger-theme.disabled,
.jqx-danger-theme[disabled] {
  text-shadow: none !important;
  color: var(--jqx-button-danger-hovered-state-text-color-theme) !important;
  background: var(--jqx-button-danger-hovered-state-background-color-theme) !important;
  border-color: var(--jqx-button-danger-hovered-state-background-color-theme) !important;
}

.jqx-danger-theme:focus {
    color: var(--jqx-button-danger-focused-state-text-color-theme) !important;
    background: var(--jqx-button-danger-focused-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme.jqx-danger-theme,
.jqx-danger-theme:active,
.jqx-danger-theme.active {
  text-shadow: none !important;
  color: var(--jqx-button-danger-active-state-text-color-theme) !important;
  background: var(--jqx-button-danger-active-state-background-color-theme) !important;
  border-color: var(--jqx-button-danger-active-state-background-color-theme) !important;
}


.jqx-warning-theme
{
  text-shadow: none !important;
  color: var(--jqx-button-warning-text-color-theme) !important;
  background: var(--jqx-button-warning-background-color-theme) !important;
  border-color: var(--jqx-button-warning-text-color-theme) !important;
}
.jqx-warning-theme.jqx-dropdownlist-state-normal-theme,
.jqx-warning-theme.jqx-slider-button-theme,
.jqx-warning-theme.jqx-slider-slider-theme,
.jqx-warning-theme.jqx-combobox-arrow-hover-theme,
.jqx-warning-theme.jqx-combobox-arrow-normal-theme,
.jqx-warning-theme.jqx-action-button-theme,
.jqx-warning-theme:hover,
.jqx-warning-theme:focus,
.jqx-warning-theme:active,
.jqx-warning-theme.active,
.jqx-warning-theme.disabled,
.jqx-warning-theme[disabled] {
  text-shadow: none !important;
  color: var(--jqx-button-warning-hovered-state-text-color-theme) !important;
  background: var(--jqx-button-warning-hovered-state-background-color-theme) !important;
  border-color: var(--jqx-button-warning-hovered-state-background-color-theme) !important;
}

.jqx-warning-theme:focus {
    color: var(--jqx-button-warning-focused-state-text-color-theme) !important;
    background: var(--jqx-button-warning-focused-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme.jqx-warning-theme,
.jqx-warning-theme:active,
.jqx-warning-theme.active {
  text-shadow: none !important;
  color: var(--jqx-button-warning-active-state-text-color-theme) !important;
  background: var(--jqx-button-warning-active-state-background-color-theme) !important;
  border-color: var(--jqx-button-warning-active-state-background-color-theme) !important;
}


.jqx-info-theme
{
  text-shadow: none !important;
  color: var(--jqx-button-info-text-color-theme) !important;
  background: var(--jqx-button-info-background-color-theme) !important;
  border-color: var(--jqx-button-info-text-color-theme) !important;
}
.jqx-info-theme.jqx-dropdownlist-state-normal-theme,
.jqx-info-theme.jqx-slider-button-theme,
.jqx-info-theme.jqx-slider-slider-theme,
.jqx-info-theme.jqx-combobox-arrow-hover-theme,
.jqx-info-theme.jqx-combobox-arrow-normal-theme,
.jqx-info-theme.jqx-action-button-theme,
.jqx-info-theme:hover,
.jqx-info-theme:focus,
.jqx-info-theme:active,
.jqx-info-theme.active,
.jqx-info-theme.disabled,
.jqx-info-theme[disabled] {
  color: var(--jqx-button-info-hovered-state-text-color-theme) !important;
  background: var(--jqx-button-info-hovered-state-background-color-theme) !important;
  border-color: var(--jqx-button-info-hovered-state-background-color-theme) !important;
  text-shadow: none !important;
}

.jqx-info-theme:focus {
    color: var(--jqx-button-info-focused-state-text-color-theme) !important;
    background: var(--jqx-button-info-focused-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme.jqx-info-theme,
.jqx-info-theme:active,
.jqx-info-theme.active {
  text-shadow: none !important;
  color: var(--jqx-button-info-active-state-text-color-theme) !important;
  background: var(--jqx-button-info-active-state-background-color-theme) !important;
  border-color: var(--jqx-button-info-active-state-background-color-theme) !important;
}

.jqx-fill-state-pressed-theme {
    background-image: none;
    outline: 0;
}

.jqx-grid-column-header-theme, 
.jqx-grid-groups-header-theme,
.jqx-grid-pager-theme {
    background-color: var(--jqx-grid-header-background-color-theme) !important;
    color: var(--jqx-grid-header-text-color-theme) !important;
    font-weight: var(--jqx-grid-header-text-weight-theme);
}

.jqx-grid-column-header-theme .sorticon {
    background-color: inherit !important;
}
.jqx-grid-column-header-theme .jqx-widget-header-theme {
    background-color: inherit !important;
}
.jqx-widget-header-theme.sortasc, .jqx-grid-column-sortascbutton-theme,
.jqx-widget-header-theme.sortdesc, .jqx-grid-column-sortdescbutton-theme {
    background-color: inherit !important;
}

.jqx-grid-content-theme { /* for the opacity of the alternation rows */
    background-color: rgba(255, 255, 255, 1);
}

.jqx-grid-cell-selected.jqx-grid-cell-edit-theme, 
.jqx-grid-cell-selected.jqx-grid-cell-edit-theme input,
.jqx-cell-editor-theme {
    background-color: var(--jqx-grid-editor-background-color-theme) !important;
    color: var(--jqx-grid-editor-text-color-theme) !important;
}

#tabletreegrid .jqx-cell-editor-theme {
    background-color: var(--jqx-grid-editor-background-color-theme) !important;
    color: var(--jqx-grid-editor-text-color-theme) !important;
}

.jqx-grid-cell-alt-theme {
    background-color: var(--jqx-grid-row-alternation-background-color-theme) !important;
    color: var(--jqx-grid-row-alternation-text-color-theme) !important;
}

.jqx-grid-group-expand-theme + div, .jqx-grid-group-collapse-theme + div  {
    background-color: var(--jqx-grid-group-row-background-color-theme);
    color: var(--jqx-grid-group-row-text-color-theme);
}

.jqx-grid-cell-theme, .jqx-grid-group-cell-theme {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-color: var(--jqx-grid-row-background-color-theme);
    color: var(--jqx-grid-row-text-color-theme);
}

.jqx-grid-cell-pinned-theme {
    background-color: var(--jqx-grid-pinned-cells-background-color-theme) !important;
}

.jqx-grid-column-menubutton-theme {
    background-color: transparent;
    border-color: var(--jqx-border-color-theme) !important;
}

#groupsheadergrid .jqx-fill-state-normal-theme {
    background-color: var(--jqx-grid-background-color-theme) !important;
}

#pagergrid .jqx-input-theme, #pagergrid .jqx-fill-state-normal-theme {
    background-color: var(--jqx-grid-background-color-theme) !important;
}


#pagergrid .jqx-dropdownlist-state-hover-theme{
    background-color: var(--jqx-hovered-state-background-color-theme) !important;
    color: var(--jqx-hovered-state-text-color-theme);
}

#pagergrid .jqx-dropdownlist-state-selected-theme{
    background-color: var(--jqx-active-state-background-color-theme) !important;
}

.jqx-cell-theme {
    font-size: var(--jqx-font-size-theme);
}

.jqx-widget-header-theme.jqx-pivotgrid-content-wrapper {
    background-color: var(--jqx-pivotgrid-header-background-color-theme) !important;
    color: var(--jqx-pivotgrid-header-text-color-theme) !important;
}

.jqx-grid-cell-normal-theme.jqx-pivotgrid-content-wrapper {
    background-color: var(--jqx-pivotgrid-cell-background-color-theme);
    color: var(--jqx-pivotgrid-cell-text-color-theme);
}

.jqx-grid-cell-selected-theme.jqx-pivotgrid-content-wrapper {
    color: var(--jqx-pivotgrid-selected-cell-text-color-theme) !important;
    background-color: var(--jqx-pivotgrid-selected-cell-background-color-theme) !important;
    border-color: var(--jqx-pivotgrid-selected-cell-background-color-theme) !important;
}

.jqx-calendar-theme > div {
    padding: 10px;
    box-sizing: border-box;
}

.jqx-calendar-theme .jqx-widget-header-theme
{
    background-color: var(--jqx-background-color-theme);
    font-size:12px;
    box-shadow:none;
}

.calendar-header-theme td {
    color: var(--jqx-editors-calendar-header-text-color-theme);
}

.jqx-calendar-month-container-theme {
    background-color: var(--jqx-editors-calendar-background-color-theme);
}

.jqx-calendar-cell-month-theme {
    color: var(--jqx-editors-calendar-current-month-text-color-theme) !important;
}

.jqx-calendar-cell-othermonth-theme {
    color: var(--jqx-editors-calendar-other-month-text-color-theme) !important;
}

.jqx-calendar-title-header-theme {
    color: var(--jqx-editors-calendar-header-text-color-theme) !important;
    background-color: var(--jqx-editors-calendar-header-background-color-theme) !important;
}

.jqx-calendar-cell-today-theme {
    color: var(--jqx-editors-calendar-today-cell-text-color-theme) !important;
    background-color: var(--jqx-editors-calendar-today-cell-background-color-theme);
    border-color: var(--jqx-editors-calendar-today-cell-border-color-theme);
}

.jqx-calendar-row-header-theme, .jqx-calendar-top-left-header-theme {
    background-color: #f0f0f0;
    border-color: #f2f2f2;
    box-shadow:none;
}
.jqx-calendar-title-content-theme {
    font-weight:bold;
}
.jqx-calendar-column-header-theme {
    background-color: var(--jqx-editors-calendar-header-background-color-theme);
    border-top-color: var(--jqx-editors-calendar-header-background-color-theme);
    box-shadow:none;
    border-bottom-color: var(--jqx-editors-calendar-header-background-color-theme);
}

.jqx-calendar-title-navigation-theme.jqx-icon-arrow-left-theme {
    background-image: var(--jqx-editors-calendar-arrow-left-color-theme);
}
.jqx-calendar-title-navigation-theme.jqx-icon-arrow-right-theme {
    background-image: var(--jqx-editors-calendar-arrow-right-color-theme);
}

.jqx-calendar-theme > div {
    padding: 10px;
    box-sizing: border-box;
}
.jqx-expander-header-theme {
    padding-top: 10px; padding-bottom: 10px;
}
 .jqx-expander-header.jqx-fill-state-hover-theme,
 .jqx-expander-header.jqx-fill-state-normal-theme
 {
      background-color: var(--jqx-accordion-item-title-background-color-theme) !important;
      border-color: var(--jqx-accordion-item-title-border-color-theme);
      color: var(--jqx-accordion-item-title-text-color-theme) !important;
}

.jqx-expander-header.jqx-fill-state-pressed-theme {
    background-color: var(--jqx-accordion-item-title-selected-background-color-theme) !important;
    border-color: var(--jqx-accordion-item-title-selected-border-color-theme);
    color: var(--jqx-accordion-item-title-selected-text-color-theme) !important;
}

.jqx-expander-header.jqx-fill-state-hover-theme {
    background-color: var(--jqx-accordion-item-title-hovered-background-color-theme) !important;
    color: var(--jqx-accordion-item-title-hovered-text-color-theme) !important;
    border-color: var(--jqx-accordion-item-title-hovered-border-color-theme) !important;
}
.jqx-expander-header.jqx-fill-state-focus-theme {
    border-color: var(--jqx-accordion-item-title-focused-border-color-theme) !important;
}

.jqx-expander-content-theme {
    background-color: var(--jqx-accordion-item-background-color-theme);
    color: var(--jqx-accordion-item-text-color-theme);
}

.jqx-expander-content.jqx-fill-state-focus-theme {
    border-color: var(--jqx-accordion-item-focused-border-color-theme) !important;
    background-color: var(--jqx-accordion-item-focused-background-color-theme);
    color: var(--jqx-accordion-item-focused-text-color-theme);
}
.jqx-expander-header-theme {
    padding:10px;
}

.jqx-ribbon-header-vertical-theme, .jqx-widget-header-vertical-theme {
	background:#f0f0f0;
}

.jqx-scrollbar-state-normal-theme {
    background-color: var(--jqx-scrollbar-scrollbar-background-color-theme);
    border: 1px solid var(--jqx-scrollbar-scrollbar-background-color-theme);
    border-left-color: var(--jqx-border-color-theme);
}

.jqx-scrollbar-thumb-state-normal-theme, .jqx-scrollbar-thumb-state-normal-horizontal-theme {
    background: var(--jqx-scrollbar-scrollbar-thumb-background-color-theme);
    border-color: #b3b3b3;
}

.jqx-scrollbar-thumb-state-hover-theme, .jqx-scrollbar-thumb-state-hover-horizontal-theme {
    background: var(--jqx-scrollbar-scrollbar-thumb-hovered-background-color-theme);
    border-color: #b3b3b3;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-theme {
    background: var(--jqx-progressbar-bar-background-color-theme) !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-theme, .jqx-splitter-collapse-button-horizontal-theme {
    background: var(--jqx-progressbar-selected-range-background-color-theme);
}

.jqx-splitter-collapse-button-vertical-theme, .jqx-progressbar-value-vertical-theme {
    background: var(--jqx-progressbar-selected-range-background-color-theme);
}

.jqx-progressbar-text-theme {
    color: var(--jqx-progressbar-label-color-theme);
}

.jqx-scrollbar-thumb-state-pressed-theme, .jqx-splitter-splitbar-vertical-theme, .jqx-splitter-splitbar-horizontal-theme, .jqx-scrollbar-thumb-state-pressed-horizontal-theme {
    background: var(--jqx-scrollbar-scrollbar-thumb-active-background-color-theme);
    border-color: #b3b3b3;
     box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-scrollbar-button-state-normal-theme {
    background: var(--jqx-scrollbar-scrollbar-arrow-background-color-theme);
}

.jqx-scrollbar-button-state-pressed-theme {
    background: var(--jqx-scrollbar-scrollbar-arrow-active-background-color-theme) !important;
    border-color: var(--jqx-scrollbar-scrollbar-arrow-active-background-color-theme);
}

.jqx-scrollbar-button-state-hover-theme {
    background: var(--jqx-scrollbar-scrollbar-arrow-hovered-background-color-theme);
}

/* scrollbar arrow up */
.jqx-scrollbar-button-state-normal-theme .jqx-icon-arrow-up-theme {
    background-image: var(--jqx-scrollbar-arrow-up-color-theme);
}
.jqx-scrollbar-button-state-hover-theme .jqx-icon-arrow-up-hover-theme {
    background-image: var(--jqx-scrollbar-arrow-up-hovered-color-theme);
}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-up-selected-theme {
    background-image: var(--jqx-scrollbar-arrow-up-active-color-theme);
}

/* scrollbar arrow down */
.jqx-scrollbar-button-state-normal-theme .jqx-icon-arrow-down-theme {
    background-image: var(--jqx-scrollbar-arrow-down-color-theme);
}
.jqx-scrollbar-button-state-hover-theme .jqx-icon-arrow-down-hover-theme {
    background-image: var(--jqx-scrollbar-arrow-down-hovered-color-theme);
}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-down-selected-theme {
    background-image: var(--jqx-scrollbar-arrow-down-active-color-theme);
}

/* scrollbar arrow left */
.jqx-scrollbar-button-state-normal-theme .jqx-icon-arrow-left-theme {
    background-image: var(--jqx-scrollbar-arrow-left-color-theme);
}
.jqx-scrollbar-button-state-hover-theme .jqx-icon-arrow-left-hover-theme {
    background-image: var(--jqx-scrollbar-arrow-left-hovered-color-theme);
}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-left-selected-theme{
    background-image: var(--jqx-scrollbar-arrow-left-active-color-theme);
}

/* scrollbar arrow right */
.jqx-scrollbar-button-state-normal-theme .jqx-icon-arrow-right-theme {
    background-image: var(--jqx-scrollbar-arrow-right-color-theme);
}
.jqx-scrollbar-button-state-hover-theme .jqx-icon-arrow-right-hover-theme {
    background-image: var(--jqx-scrollbar-arrow-right-hovered-color-theme);
}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-right-selected-theme {
    background-image: var(--jqx-scrollbar-arrow-right-active-color-theme);
}

.jqx-grid-column-sortdescbutton-theme, .jqx-grid-column-filterbutton-theme, .jqx-grid-column-sortascbutton-theme {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: var(--jqx-border-color-theme);
}

.jqx-menu-vertical-theme, .jqx-menu-horizontal-theme {
    background-color: var(--jqx-menu-background-color-theme) !important;
    filter: none;
}

.jqx-menu-item-top-theme, .jqx-menu-item-theme {
    color: var(--jqx-menu-text-color-theme);
}

.jqx-menu-dropdown-theme {
    background-color: var(--jqx-menu-dropdown-background-color-theme);
}
.jqx-menu-dropdown-theme li {
    color: var(--jqx-menu-dropdown-text-color-theme);
}

.jqx-navbar-block-theme {
    color: var(--jqx-navbar-item-text-color-theme);
    background: var(--jqx-navbar-item-background-color-theme);
}

.jqx-navbar-block-theme.jqx-fill-state-hover-theme {
    color: var(--jqx-navbar-hovered-item-text-color-theme) !important;
    background-color: var(--jqx-navbar-hovered-item-background-color-theme);
}

.jqx-navbar-block-theme.jqx-fill-state-pressed-theme {
    color: var(--jqx-navbar-selected-item-text-color-theme) !important;
    background-color: var(--jqx-navbar-selected-item-background-color-theme);
}

.jqx-checkbox-check-checked-theme {
    background: transparent url(./images/material_check_black.png) center center no-repeat;
}
.jqx-checkbox-check-indeterminate-theme {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: #0379BF;
}
.jqx-checkbox-hover {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-combobox-content-theme {
    border-color: var(--jqx-border-color-theme);
    border-color: rgba(0, 0, 0, 0.25);
}

.jqx-combobox-multi-item-theme {
    color: var(--jqx-editors-combobox-multi-item-text-color-theme);
    background: var(--jqx-editors-combobox-multi-item-background-color-theme);
}

.jqx-grid-bottomright-theme, .jqx-panel-bottomright-theme, .jqx-listbox-bottomright-theme {
    background-color: var(--jqx-background-color-theme);
}

.jqx-window-theme, .jqx-tooltip-theme {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}

.jqx-docking-theme .jqx-window-theme {
    box-shadow: none;
}

.jqx-docking-panel-theme .jqx-window-theme {
    box-shadow: none;
}

.jqx-radiobutton-theme {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
}

.jqx-radiobutton-theme-theme, .jqx-radiobutton-hover-theme {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
}

.jqx-radiobutton-check-checked-theme {
    filter: none;
    background: var(--jqx-accent-color-theme);
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-theme {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-theme {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.jqx-slider-track-horizontal-theme, .jqx-slider-track-vertical-theme {
    border-color: var(--jqx-border-color-theme);
    background: var(--jqx-slider-slider-bar-background-color-theme);
}

.jqx-slider-button-theme {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-color: var(--jqx-slider-arrows-background-color-theme);
    border-color: transparent !important;
}

.jqx-slider-slider-theme {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-has-value-theme {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-color: var(--jqx-slider-slider-thumb-color-theme);
    border-color: var(--jqx-slider-slider-thumb-color-theme) !important;
}
.jqx-slider-has-value-theme.jqx-fill-state-focus-theme,
.jqx-slider-button-theme.jqx-fill-state-focus-theme {
    border-color: var(--jqx-slider-focused-border-color-theme) !important;
}

.jqx-slider-button-theme.jqx-fill-state-hover-theme {
    background-color: var(--jqx-slider-arrows-hovered-background-color-theme);
}
.jqx-slider-button-theme.jqx-fill-state-pressed-theme {
    background-color: var(--jqx-slider-arrows-selected-background-color-theme) !important;
}

.jqx-slider-slider-theme:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}

.jqx-slider-tooltip-theme, .jqx-slider-tooltip-theme .jqx-fill-state-normal-theme {
    border-radius: var(--jqx-border-radius-theme);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--jqx-slider-tooltip-background-color-theme);     
    color: var(--jqx-slider-tooltip-text-color-theme);
    font-size:11px;
}

.jqx-slider-tooltip-theme {
    border: 1px solid var(--jqx-slider-tooltip-border-color-theme) !important;
}

.jqx-tooltip-main-theme{
    border: none;
}

.jqx-slider-tooltip-theme .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-theme .jqx-tooltip-arrow-l-r {
    background: var(--jqx-slider-tooltip-background-color-theme); 
    border-color: var(--jqx-slider-tooltip-border-color-theme);
}
.jqx-listitem-state-normal-theme,
.jqx-listitem-state-hover-theme,
.jqx-listitem-state-selected-theme
 {
    padding-top:5px;
    padding-bottom:5px;
    margin:0px;
    border-radius: 0px;
}

.jqx-listitem-state-normal-theme, 
.jqx-listmenu-item-theme.jqx-fill-state-normal-theme  {
    color: var(--jqx-list-text-color-theme);
    background-color: var(--jqx-list-background-color-theme);
}

.jqx-listitem-state-normal-theme.checkboxes,
.jqx-listitem-state-hover-theme.checkboxes,
.jqx-listitem-state-selected-theme.checkboxes {
    border-radius: var(--jqx-border-radius-theme);
}

.jqx-listitem-state-group-theme, .jqx-listmenu-header-theme {
    color: var(--jqx-list-header-text-color-theme) !important;
    background-color: var(--jqx-list-header-background-color-theme) !important;
}

/*applied to a list item when the item is selected.*/
.jqx-input-popup-theme .jqx-fill-state-hover-theme,
.jqx-input-popup-theme .jqx-fill-state-pressed-theme {
    color: var(--jqx-hovered-state-content-text-color-theme) !important;
    border-color: var(--jqx-hovered-state-content-background-color-theme);
    text-decoration: none;
    background-color: var(--jqx-hovered-state-content-background-color-theme);
    background-repeat: repeat-x;
    outline: 0;
    background: var(--jqx-hovered-state-content-background-color-theme); /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background-position: 0 0;
}

.jqx-listitem-state-hover-theme {
    color: var(--jqx-list-hovered-item-state-text-color-theme) !important;
    background-color: var(--jqx-list-hovered-item-state-background-color-theme);
    background: var(--jqx-list-hovered-item-state-background-color-theme); /* Old browsers */
}

.jqx-menu-item-hover-theme, 
.jqx-menu-vertical-theme .jqx-menu-item-top-hover-theme,
.jqx-menu-horizontal-theme .jqx-menu-item-top-hover-theme {
    color: var(--jqx-menu-hovered-item-color-theme) !important;
    background-color: var(--jqx-menu-hovered-item-background-color-theme);
}

.jqx-tree-item-hover-theme {
    color: var(--jqx-treeview-hovered-tree-item-color-theme);
    background-color: var(--jqx-treeview-hovered-tree-item-background-color-theme);
}

.jqx-grid-cell-hover-theme {
    color: var(--jqx-grid-hovered-row-text-color-theme) !important;
    background-color: var(--jqx-grid-hovered-row-background-color-theme) !important;
    background: var(--jqx-grid-hovered-row-background-color-theme) !important; /* Old browsers */
}

.jqx-scheduler-theme {
    border-color: var(--jqx-scheduler-header-border-color-theme);
}

.jqx-scheduler-theme .jqx-scrollbar-state-normal-theme {
    border-left-color: var(--jqx-scheduler-header-border-color-theme);
}

.jqx-scheduler-theme .jqx-widget-header-theme{
    color: var(--jqx-scheduler-header-text-color-theme) !important;
    background-color: var(--jqx-scheduler-header-background-color-theme) !important;
    border-color: var(--jqx-scheduler-header-border-color-theme) !important;
}

.jqx-scheduler-all-day-cell-theme {
    border-color: var(--jqx-scheduler-header-border-color-theme) !important;
}

.jqx-scheduler-toolbar-theme .jqx-datetimeinput-theme {
    border-color: var(--jqx-scheduler-header-border-color-theme) !important;
}

.jqx-scheduler-toolbar-theme .jqx-group-button-normal-theme {
    color: var(--jqx-scheduler-header-buttons-text-color-theme);
    border-color: var(--jqx-scheduler-header-border-color-theme);
    background: var(--jqx-scheduler-header-buttons-background-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-group-button-normal-theme.jqx-fill-state-hover-theme {
    color: var(--jqx-scheduler-header-buttons-hovered-text-color-theme);
    background: var(--jqx-scheduler-header-buttons-hovered-background-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-group-button-normal-theme.jqx-fill-state-pressed-theme {
    color: var(--jqx-scheduler-header-buttons-selected-text-color-theme) !important;
    border-color: var(--jqx-scheduler-header-buttons-selected-background-color-theme) !important;
    background-color: var(--jqx-scheduler-header-buttons-selected-background-color-theme) !important;
}

.jqx-scheduler-work-time-cell-theme, .jqx-scheduler-not-work-time-cell-theme {
    border-color: var(--jqx-scheduler-border-color-theme) !important; 
}

.jqx-scheduler-not-work-time-cell-theme {
    background-color: var(--jqx-scheduler-not-work-time-cells-color-theme);
}

.jqx-scheduler-cell-hover-theme {
    border-color: var(--jqx-scheduler-hovered-cell-border-color-theme) !important;
    background: var(--jqx-scheduler-hovered-cell-background-color-theme) !important;
}

.jqx-scheduler-toolbar-theme .jqx-datetimeinput {
    background-color: inherit !important;
}
.jqx-scheduler-toolbar-theme .jqx-datetimeinput .jqx-action-button-theme{
    background-color: inherit !important;
}

.jqx-scheduler-toolbar-theme .jqx-icon-arrow-left-theme {
    background-image: var(--jqx-scheduler-weeks-arrow-left-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-icon-arrow-right-theme {
    background-image: var(--jqx-scheduler-weeks-arrow-right-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-icon-arrow-left-selected-theme {
    background-image: var(--jqx-scheduler-weeks-arrow-left-selected-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-icon-arrow-right-selected-theme {
    background-image: var(--jqx-scheduler-weeks-arrow-right-selected-color-theme);
}

.jqx-scheduler-toolbar-theme .jqx-icon-calendar {
    background-image: var(--jqx-scheduler-calendar-icon-color-theme);
}
.jqx-scheduler-toolbar-theme .jqx-icon-calendar-pressed {
    background-image: var(--jqx-scheduler-calendar-icon-selected-color-theme);
}

.jqx-grid-table-dark {
    font-size: var(--jqx-font-size-theme);
}


.jqx-grid-selectionarea-theme, 
.jqx-input-button-header-theme, .jqx-input-button-innerHeader-theme {
    color: var(--jqx-background-color-theme) !important;
    background-color: var(--jqx-accent-color-theme);
    *background-color: var(--jqx-accent-color-theme);
    background-repeat: repeat-x;
    border-color: var(--jqx-accent-color-theme) !important;
    background: var(--jqx-accent-color-theme); /* Old browsers */
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-listitem-state-selected-theme {
    color: var(--jqx-list-selected-item-state-text-color-theme) !important;
    background-color: var(--jqx-list-selected-item-state-background-color-theme);
    background: var(--jqx-list-selected-item-state-background-color-theme); /* Old browsers */
    border-color: var(--jqx-list-selected-item-state-background-color-theme);
}

 .jqx-listmenu-item-theme.jqx-fill-state-pressed-theme {
    color: var(--jqx-list-selected-item-state-text-color-theme) !important;
    background-color: var(--jqx-list-selected-item-state-background-color-theme) !important;
    background: var(--jqx-list-selected-item-state-background-color-theme); /* Old browsers */
    border-color: var(--jqx-list-selected-item-state-background-color-theme);
 }

.jqx-tree-item-selected-theme {
    color: var(--jqx-treeview-selected-tree-item-color-theme) !important;
    background-color: var(--jqx-treeview-selected-tree-item-background-color-theme);
}

.jqx-menu-item-selected-theme,
.jqx-menu-vertical-theme .jqx-menu-item-top-selected-theme,
.jqx-menu-horizontal-theme .jqx-menu-item-top-selected-theme {
    color: var(--jqx-menu-selected-item-color-theme) !important;
    background-color: var(--jqx-menu-selected-item-background-color-theme);
    border-color: var(--jqx-menu-selected-item-border-color-theme);
}

.jqx-calendar-cell-hover-theme {
    color: var(--jqx-editors-calendar-hovered-cell-text-color-theme) !important;
    background-color: var(--jqx-editors-calendar-hovered-cell-background-color-theme);
    border-color: var(--jqx-editors-calendar-hovered-cell-background-color-theme); 
}
.jqx-calendar-cell-selected-theme {
    color: var(--jqx-editors-calendar-selected-cell-text-color-theme) !important;
    background-color: var(--jqx-editors-calendar-selected-cell-background-color-theme);
    border-color: var(--jqx-editors-calendar-selected-cell-background-color-theme);
}

.jqx-grid-cell-selected-theme {    
    color: var(--jqx-grid-selected-row-text-color-theme) !important;    
    border-color: var(--jqx-grid-selected-row-border-color-theme) !important;
    background-color: var(--jqx-grid-selected-row-background-color-theme) !important;
}

.jqx-scheduler-cell-selected-theme {
    border-color: var(--jqx-scheduler-selected-cell-border-color-theme) !important;
    background: var(--jqx-scheduler-selected-cell-background-color-theme) !important;
}

.jqx-grid-cell-theme .jqx-button-theme, .jqx-grid-cell-theme .jqx-button-theme.jqx-fill-state-hover-theme, .jqx-grid-cell-theme .jqx-button-theme.jqx-fill-state-pressed-theme {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
   -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.jqx-popup-theme {
    border: 1px solid var(--jqx-border-color-theme);
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}
.jqx-window-collapse-button-theme, .jqx-menu-item-arrow-up-theme, .jqx-menu-item-arrow-up-selected-theme, .jqx-menu-item-arrow-top-up-theme, .jqx-icon-arrow-up-theme, .jqx-icon-arrow-up-selected-theme {
    background-image: var(--jqx-arrow-up-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-up-hover-theme {
    background-image: var(--jqx-arrow-up-hovered-color-theme);
}

.jqx-expander-arrow-bottom-theme {
    background-image: var(--jqx-accordion-arrow-down-color-theme);
}

.jqx-widget-theme .jqx-grid-group-expand-theme, .jqx-grid-column-menubutton-theme, .jqx-grid-column-sortdescbutton-theme, .jqx-window-collapse-button-collapsed-theme, .jqx-menu-item-arrow-down-theme, .jqx-menu-item-arrow-down-selected-theme, .jqx-menu-item-arrow-down-theme, .jqx-icon-arrow-down-theme, .jqx-icon-arrow-down-selected-theme {
    background-image: var(--jqx-arrow-down-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-down-hover-theme {
    background-image: var(--jqx-arrow-down-hovered-color-theme);
}

.jqx-grid-cell-hover-theme .jqx-grid-group-expand-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-arrow-down-hovered-color-theme);
}
.jqx-grid-cell-hover-theme .jqx-grid-group-expand-rtl-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-arrow-down-hovered-color-theme);
}
.jqx-grid-cell-hover-theme .jqx-grid-group-collapse-theme.jqx-icon-arrow-right-theme {
    background-image: var(--jqx-grid-arrow-rigth-hovered-color-theme);
}
.jqx-grid-cell-hover-theme .jqx-grid-group-collapse-rtl-theme.jqx-icon-arrow-left-theme {
    background-image: var(--jqx-grid-arrow-left-hovered-color-theme);
}

.jqx-expander-arrow-top-theme {
    background-image: var(--jqx-accordion-arrow-down-color-theme);
}

.jqx-expander-arrow-top-hover-theme {
    background-image: var(--jqx-accordion-arrow-down-hovered-color-theme) !important;
}

.jqx-expander-arrow-expanded-theme {
    background-image: var(--jqx-accordion-arrow-down-selected-color-theme) !important;
}

.jqx-tabs-arrow-left-theme, .jqx-menu-item-arrow-left-selected-theme, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-theme, .jqx-icon-arrow-down-left-theme, .jqx-icon-arrow-left-selected-theme {
    background-image: url('images/metro-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-theme .jqx-grid-group-collapse-theme, .jqx-tabs-arrow-right-theme, .jqx-menu-item-arrow-right-selected-theme, .jqx-menu-item-arrow-top-right-theme, .jqx-icon-arrow-right-theme, .jqx-icon-arrow-right-hover-theme, .jqx-icon-arrow-right-selected-theme {
    background-image: url('images/metro-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-group-collapse-theme.jqx-icon-arrow-right-theme {
    background-image: var(--jqx-grid-groups-arrow-right-color-theme);
}
 .jqx-grid-group-expand-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-groups-arrow-down-color-theme);
}

.jqx-grid-group-collapse-rtl-theme.jqx-icon-arrow-left-theme {
    background-image: var(--jqx-grid-groups-arrow-left-color-theme);
}
.jqx-grid-group-expand-rtl-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-groups-arrow-down-color-theme);
} 


.jqx-tree-grid-collapse-button-theme.jqx-icon-arrow-right-theme{
    background-image: var(--jqx-grid-arrow-right-color-theme);
}
.jqx-tree-grid-collapse-button-theme.jqx-icon-arrow-left-theme{
    background-image: var(--jqx-grid-arrow-left-color-theme);
}
.jqx-tree-grid-expand-button-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-arrow-down-color-theme);
}

.jqx-grid-pager-theme .jqx-icon-arrow-left-theme {
    background-image: var(--jqx-grid-arrow-left-color-theme);
}
.jqx-grid-pager-theme .jqx-icon-arrow-left-hover-theme {
    background-image: var(--jqx-grid-arrow-left-hovered-color-theme);
}
.jqx-grid-pager-theme .jqx-icon-arrow-left-selected-theme {
    background-image: var(--jqx-grid-arrow-left-selected-color-theme);
}
.jqx-grid-pager-theme .jqx-icon-arrow-right-theme {
    background-image: var(--jqx-grid-arrow-right-color-theme);
}
.jqx-grid-pager-theme .jqx-icon-arrow-right-hover-theme {
    background-image: var(--jqx-grid-arrow-right-hovered-color-theme);
}
.jqx-grid-pager-theme .jqx-icon-arrow-right-selected-theme {
    background-image: var(--jqx-grid-arrow-right-selected-color-theme);
}

.jqx-grid-column-sorticon-theme.jqx-icon-arrow-down-theme {
    background-image: var(--jqx-grid-header-arrow-down-color-theme);
}
.jqx-grid-column-sortdescbutton-theme {
    background-image: var(--jqx-grid-header-arrow-down-color-theme);
}
.jqx-grid-column-sortascbutton-theme {
    background-image: var(--jqx-grid-header-arrow-up-color-theme);
}
.jqx-grid-column-menubutton-theme {
    background-image: var(--jqx-grid-menu-button-color-theme) !important;
}

.jqx-grid-group-column-theme .jqx-grid-column-sortdescbutton-theme {
    background-image: var(--jqx-grid-arrow-down-color-theme);
}

.jqx-grid-group-column-theme .jqx-icon-close-theme {
    background-image: var(--jqx-grid-close-button-color-theme);
}

.jqx-pivotgrid-item .jqx-icon-arrow-up-theme {
    background-image: var(--jqx-pivotgrid-arrow-up-color-theme);
}
.jqx-pivotgrid-item .jqx-icon-arrow-down-theme {
    background-image: var(--jqx-pivotgrid-arrow-down-color-theme);
}
.jqx-pivotgrid-menu-button-theme {
    background-image: var(--jqx-pivotgrid-menu-button-color-theme);
}

.jqx-tree-item-arrow-collapse-rtl-theme, .jqx-tree-item-arrow-collapse-hover-rtl-theme {
    background-image: url('images/metro-icon-left.png');
}

.jqx-menu-item-arrow-left-selected-theme {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-menu-item-arrow-right-selected-theme {
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-input-button-content-theme {
    font-size: 10px;
}
.jqx-widget .jqx-grid-cell-theme, .jqx-widget .jqx-grid-column-header-theme, .jqx-widget .jqx-grid-group-cell-theme {border-color: var(--jqx-border-color-theme);}
.jqx-grid-groups-row-theme > span {
    padding-left: 4px;
}
.jqx-combobox-content-theme, .jqx-input-theme, .jqx-input-content-theme, .jqx-combobox-content-theme input  {
    border-color: var(--jqx-editors-text-editors-border-color-theme) !important;
    color: var(--jqx-editors-text-editors-text-color-theme) !important;
    background-color: var(--jqx-editors-text-editors-background-color-theme);
    background: var(--jqx-editors-text-editors-background-color-theme) !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.jqx-combobox-content-theme, .jqx-combobox-theme, .jqx-combobox-state-normal-theme {
    border-color: var(--jqx-editors-text-editors-border-color-theme);
}

.jqx-grid-pager-input-theme {
    border-color: var(--jqx-border-color-theme) !important;
}

.jqx-combobox-content-focus-theme, .jqx-combobox-state-focus-theme, .jqx-fill-state-focus-theme,
.jqx-numberinput-focus-theme {
    outline: none;
    border-color: #959595;
}

input.jqx-input-theme {
    border-radius: var(--jqx-editors-text-editors-border-radius-theme);
}

input[type="text"].jqx-input-theme, input[type="password"].jqx-input-theme, input[type="text"].jqx-widget-content-theme, input[type="textarea"].jqx-widget-content-theme, textarea.jqx-input-theme {
    font-size: var(--jqx-font-size-theme);
    padding-left:3px;
    padding-right: 3px;
    resize: none;
}

input[type="text"].jqx-input-theme:-moz-placeholder, input[type="text"].jqx-widget-content-theme:-moz-placeholder, input[type="textarea"].jqx-widget-content-theme:-moz-placeholder, textarea.jqx-input-theme:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-theme:-webkit-input-placeholder, input[type="text"].jqx-widget-content-theme:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-theme:-webkit-input-placeholder, textarea.jqx-input-theme:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-theme:-ms-input-placeholder, input[type="text"].jqx-widget-content-theme:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-theme:-ms-input-placeholder, textarea.jqx-input-theme:-ms-input-placeholder {
    color: #999999;
}

.jqx-file-upload-theme, .jqx-file-upload-file-row-theme {
    background-color: var(--jqx-editors-file-uploader-background-color-theme);
}
.jqx-file-upload-file-name-theme {
    color: var(--jqx-editors-file-uploader-filename-text-color-theme) !important;
}

.jqx-combobox-content-focus-theme, .jqx-combobox-state-focus-theme, .jqx-fill-state-focus-theme,
.jqx-numberinput-focus-theme {
    outline: none;
    border-color: var(--jqx-editors-text-editors-focused-border-color-theme) !important;
}
.jqx-popup-theme.jqx-fill-state-focus-theme
{
    outline: none;
    border-color: var(--jqx-border-color-theme) !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-slider-rangebar-theme {
    border-color: var(--jqx-slider-selected-slider-background-color-theme);
    background: var(--jqx-slider-selected-slider-background-color-theme);
}

.jqx-slider-button-theme .jqx-icon-arrow-left-theme {
    background-image: var(--jqx-slider-arrow-left-color-theme);
}
.jqx-slider-button-theme .jqx-icon-arrow-right-theme {
    background-image: var(--jqx-slider-arrow-right-color-theme);
}
.jqx-slider-button-theme .jqx-icon-arrow-left-hover-theme {
    background-image: var(--jqx-slider-arrow-left-hovered-color-theme);
}
.jqx-slider-button-theme .jqx-icon-arrow-right-hover-theme {
    background-image: var(--jqx-slider-arrow-right-hovered-color-theme);
}
.jqx-slider-button-theme .jqx-icon-arrow-left-selected-theme {
    background-image: var(--jqx-slider-arrow-left-selected-color-theme);
}
.jqx-slider-button-theme .jqx-icon-arrow-right-selected-theme {
    background-image: var(--jqx-slider-arrow-right-selected-color-theme);
}

.jqx-switchbutton-theme{
    border-radius: 15px;
}
.jqx-switchbutton-theme .jqx-fill-state-normal-theme,
.jqx-switchbutton-theme .jqx-fill-state-hover-theme,
.jqx-switchbutton-theme .jqx-fill-state-pressed-theme {
    border-color: var(--jqx-editors-switch-thumb-color-theme);
    background: var(--jqx-editors-switch-thumb-color-theme);
}

.jqx-switchbutton-label-on-theme {
    color: var(--jqx-editors-switch-on-label-text-color-theme);
    background-color: var(--jqx-editors-switch-on-label-background-color-theme);
}

.jqx-switchbutton-label-off-theme {
    color: var(--jqx-editors-switch-off-label-text-color-theme);
    background-color: var(--jqx-editors-switch-off-label-background-color-theme);
}

.jqx-scrollview-button-theme {
    background-color: var(--jqx-scrollview-indicator-background-color-theme);
}

.jqx-scrollview-button-selected-theme {
    background-color: var(--jqx-scrollview-selected-indicator-background-color-theme);
}

.jqx-loader-theme, .jqx-popover-content-theme {
    color: var(--jqx-overlays-content-text-color-theme);
    background-color: var(--jqx-overlays-content-background-color-theme);
}

.jqx-popover-modal-background-theme {
    background-color: var(--jqx-overlays-shader-background-color-theme);
}

.jqx-popover-title-theme {
    color: var(--jqx-overlays-popup-header-text-color-theme) !important;
    background-color: var(--jqx-overlays-popup-header-background-color-theme) !important;
}

.jqx-loader-text-theme {
    color: var(--jqx-overlays-content-text-color-theme);
}

.jqx-tooltip-text-theme{
    background-color: var(--jqx-overlays-tooltip-background-color-theme);
    color: var(--jqx-overlays-tooltip-text-color-theme);
}

.jqx-notification-theme {
    color: var(--jqx-overlays-toast-text-color-theme) !important;
}

.jqx-notification-info.jqx-notification-theme {
    background-color: var(--jqx-overlays-toast-info-background-color-theme) !important;
    border-color: var(--jqx-overlays-toast-info-background-color-theme) !important;
}  

.jqx-notification-warning.jqx-notification-theme {
    background-color: var(--jqx-overlays-toast-warning-background-color-theme) !important;
    border-color: var(--jqx-overlays-toast-warning-background-color-theme) !important;
}  

.jqx-notification-error.jqx-notification-theme {
    background-color: var(--jqx-overlays-toast-error-background-color-theme) !important;
    border-color: var(--jqx-overlays-toast-error-background-color-theme) !important;
}  

.jqx-notification-success.jqx-notification-theme {
    background-color: var(--jqx-overlays-toast-success-background-color-theme) !important;
    border-color: var(--jqx-overlays-toast-success-background-color-theme) !important;
}  

.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-expand-theme {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-collapse-theme{
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-collapse-rtl-theme{
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-expand-rtl-theme {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-title-selected-top-theme, .jqx-tabs-selection-tracker-top-theme {
    border-color: transparent;
    filter: none;
    background: var(--jqx-tabs-selected-tab-background-color-theme);
    color: #333;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-tabs-arrow-background-theme {
    background: var(--jqx-background-color-theme);
    border:none;
    box-shadow:none;
}
.jqx-tabs-title-theme, .jqx-ribbon-item-theme {
    color: var(--jqx-tabs-tab-text-color-theme);
}
.jqx-tabs-title-selected-bottom-theme,
.jqx-tabs-title-selected-top-theme
 {
    padding-top:5px;
    padding-bottom:5px;
    color: var(--jqx-tabs-selected-tab-text-color-theme) !important;
    font-weight:500;
}
.jqx-tabs-title.jqx-fill-state-hover-theme {
    border-color: transparent;
}

.jqx-tabs-arrow-left-theme {
    background-image: var(--jqx-tabs-arrow-left-color-theme);
}
.jqx-tabs-arrow-right-theme {
    background-image: var(--jqx-tabs-arrow-right-color-theme);
}

.jqx-ribbon-item-theme {
    cursor: pointer;
}

.jqx-toolbar-theme {
    background: var(--jqx-toolbar-background-color-theme);
}

.jqx-ribbon-item-selected-theme {
    color: #1997C6;
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-theme {
    background: transparent;
    border-color: transparent;
}

.jqx-ribbon-header-top-theme {
    border-color: transparent;
    border-bottom-color: #E0E0E0;
}

.jqx-ribbon-header-bottom-theme {
    border-color: transparent;
    border-top-color: #E0E0E0;
}

.jqx-ribbon-header-right-theme {
    border-color: transparent;
    border-left-color:#E0E0E0;
}

.jqx-ribbon-header-left-theme {
    border-color: transparent;
    border-right-color:#E0E0E0;
}

.jqx-tabs-title-selected-bottom-theme, .jqx-tabs-selection-tracker-bottom-theme {
    border-color: transparent;
    border-top: 1px solid var(--jqx-background-color-theme);
    filter: none;
    background: var(--jqx-background-color-theme);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-theme, .jqx-ribbon-theme {
    border-color: transparent;
}

.jqx-tabs-header-theme, .jqx-ribbon-header-theme {
    background: transparent;
}
.jqx-tabs-position-bottom .jqx-tabs-header-theme {
    border-color: transparent;
}
.jqx-layout-theme .jqx-tabs-header-theme, .jqx-layout-theme .jqx-ribbon-header-theme {
    background: var(--jqx-background-color-theme);
    border-color: var(--jqx-border-color-theme);
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-theme, .jqx-tabs-title-hover-bottom-theme, .jqx-tabs-header-theme {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    color: var(--jqx-tabs-hovered-tab-text-color-theme);
    background: var(--jqx-tabs-hovered-tab-background-color-theme);
}

.jqx-tabs-header-theme, .jqx-tabs-arrow-background-theme {
    background-color: var(--jqx-tabs-header-background-color-theme) !important;
    border-bottom: 1px solid var(--jqx-tabs-border-color-theme);
}

.jqx-tabs-content-theme {
    box-sizing: border-box;
    color: var(--jqx-tabs-content-text-color-theme);
    background-color: var(--jqx-tabs-content-background-color-theme);
    border: 1px solid var(--jqx-tabs-border-color-theme);
    border-top-color: transparent;
    padding:5px;
}

.jqx-tabs-bar-theme {
    position: absolute;
    bottom: 0;
    background: var(--jqx-tabs-selected-tab-bottom-border-color-theme);
    height: 2px;
    z-index:20;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-theme.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-theme {
    top: 0;
}

.jqx-window-content-theme {
    box-sizing:border-box;
}

.jqx-layout-theme
{
    background-color: #cccccc;
}
.jqx-kanban-column-header-collapsed-theme {
   background: -moz-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ff3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(248,248,248,1)), color-stop(100%, rgba(234,234,234,1))); /* safari4+,chrome */
    background: -webkit-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* safari5.1+,chrome10+ */
    background: -o-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* ie10+ */
    background: linear-gradient(90deg, rgba(248,248,248,1) 0%, rgba(234,234,234,1) 100%); /* w3c */
}

.jqx-calendar-theme td {
    font-size: 12px;
}
.jqx-grid-column-menubutton-theme {
    background-image: url('images/metro-icon-down.png');
 }
.jqx-grid-pager-top-theme .jqx-grid-pager-number-theme,
.jqx-grid-pager-theme .jqx-grid-pager-number-theme {

    background-color: transparent;
    border-color: transparent;
    color: rgba(0,0,0,.54) !important;
    font-size:12px;
}

.jqx-grid-pager-top-theme .jqx-grid-pager-number-theme:hover,
.jqx-grid-pager-theme .jqx-grid-pager-number-theme:hover {
    font-size:12px;
}

.jqx-grid-pager-top-theme .jqx-grid-pager-number-theme.jqx-fill-state-pressed-theme ,
.jqx-grid-pager-theme .jqx-grid-pager-number-theme.jqx-fill-state-pressed-theme {
    color: var(--jqx-accent-color-theme) !important;
    font-weight: bold !important;
}
.jqx-menu-item-arrow-up-selected-theme, .jqx-icon-arrow-up-selected-theme{background-image:var(--jqx-arrow-up-selected-color-theme);background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-theme, .jqx-icon-arrow-down-selected-theme{background-image:var(--jqx-arrow-down-selected-color-theme);background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-theme, .jqx-icon-arrow-left-selected-theme{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-theme, .jqx-icon-arrow-right-selected-theme{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-theme{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-selected-theme{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-hover-theme{background-image:url(./images/close.png);  background-repeat:no-repeat;  background-position:center;}
/* .jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-up-selected-theme{background-image:url('images/metro-icon-up.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-down-selected-theme{background-image:url('images/metro-icon-down.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-left-selected-theme{background-image:url('images/metro-icon-left.png');background-repeat:no-repeat;background-position:center;}
.jqx-scrollbar-button-state-pressed-theme .jqx-icon-arrow-right-selected-theme{background-image:url('images/metro-icon-right.png');background-repeat:no-repeat;background-position:center;} */

.jqx-menu-item-arrow-top-right-theme {
    background-image: var(--jqx-menu-arrow-right-color-theme);
}
.jqx-menu-item-arrow-right-selected-theme {
    background-image: var(--jqx-menu-arrow-right-selected-color-theme);
}
.jqx-menu-item-arrow-top-left-theme {
    background-image: var(--jqx-menu-arrow-left-color-theme);
}
.jqx-menu-item-arrow-left-selected-theme {
    background-image: var(--jqx-menu-arrow-left-selected-color-theme);
}
.jqx-menu-item-arrow-top-up-theme {
    background-image: var(--jqx-menu-arrow-up-color-theme);
}
.jqx-menu-item-arrow-up-selected-theme {
    background-image: var(--jqx-menu-arrow-up-selected-color-theme);
}
.jqx-menu-item-arrow-top-down-theme {
    background-image: var(--jqx-menu-arrow-down-color-theme);
}
.jqx-menu-item-arrow-down-selected-theme {
    background-image: var(--jqx-menu-arrow-down-selected-color-theme);
}

.jqx-listmenu-arrow-right-theme {
    background-image: var(--jqx-list-arrow-right-color-theme);
}
.jqx-listmenu-arrow-right-pressed-theme {
    background-image: var(--jqx-list-arrow-right-selected-color-theme);
}
.jqx-listmenu-arrow-rtl-theme {
    background-image: var(--jqx-list-arrow-left-color-theme);
}
.jqx-listmenu-arrow-right-pressed-theme.jqx-listmenu-arrow-rtl-theme {
    background-image: var(--jqx-list-arrow-left-selected-color-theme);
}

.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-expand-theme {
    background-image: var(--jqx-grid-arrow-down-selected-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-collapse-theme{
    background-image: var(--jqx-grid-arrow-right-selected-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-collapse-rtl-theme {
    background-image: var(--jqx-grid-arrow-left-selected-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-theme.jqx-grid-cell-selected-theme>.jqx-grid-group-expand-rtl-theme{
    background-image: var(--jqx-grid-arrow-down-selected-color-theme);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-group-collapse-theme {
    background-image: url(./images/metro-icon-right.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-theme
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-left.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-theme, .jqx-grid-group-expand-rtl-theme
{
    padding-right: 0px;
    background-image: url(./images/metro-icon-down.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-icon-arrow-first-theme
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-theme
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-hover-theme
{
    background-image: url('images/metro-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-hover-theme
{
    background-image: url('images/metro-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-first-selected-theme
{
    background-image: url('images/metro-icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-selected-theme
{
    background-image: url('images/metro-icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-calendar-theme {
    background-image: var(--jqx-editors-datetimeinput-calendar-icon-color-theme);
}
.jqx-icon-calendar-pressed-theme {
    background-image: var(--jqx-editors-datetimeinput-calendar-icon-selected-color-theme);
}
.jqx-icon-time-pressed-theme {
    background-image: url('images/icon-time-white.png');
}
.jqx-icon-time-theme{
    margin-left:1px;
}


.sorticon, .filtericon {
    box-shadow:none;
}
.sorticon.descending .jqx-grid-column-sorticon-theme {
    margin-top:-1px;
}
.sorticon.ascending .jqx-grid-column-sorticon-theme {
    margin-top:1px;
}

.jqx-dropdownlist-state-normal-theme .jqx-icon-arrow-down-theme,
.jqx-combobox-state-normal-theme .jqx-icon-arrow-down-theme,
.sorticon.descending .jqx-grid-column-sorticon-theme,
.jqx-tree-item-arrow-expand-theme,
 .jqx-expander-header-theme .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    transition: transform 0.2s ease-out;
}

.jqx-tree-item-arrow-expand-theme {
    background-image: var(--jqx-treeview-arrow-expanded-color-theme) !important;
}

.jqx-expander-header-theme .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
   background-image: var(--jqx-accordion-arrow-up-color-theme);
}

.jqx-tree-item-arrow-collapse-theme
{
    transform: rotate(-90deg);
    background-image: var(--jqx-treeview-arrow-collapsed-color-theme);
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-theme .jqx-icon-arrow-down-theme,
.jqx-combobox-state-selected-theme .jqx-icon-arrow-down-theme,
.sorticon.ascending .jqx-grid-column-sorticon-theme
 {
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
}
.jqx-combobox-state-selected-theme .jqx-icon-arrow-down-theme{
    left:0px;
}

.jqx-primary .jqx-icon-arrow-down-theme, .jqx-warning .jqx-icon-arrow-down-theme, .jqx-danger .jqx-icon-arrow-down-theme, .jqx-success .jqx-icon-arrow-down-theme, .jqx-info .jqx-icon-arrow-down-theme {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-selected-theme, .jqx-warning .jqx-icon-arrow-down-selected-theme, .jqx-danger .jqx-icon-arrow-down-selected-theme, .jqx-success .jqx-icon-arrow-down-selected-theme, .jqx-info .jqx-icon-arrow-down-selected-theme {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-down-hover-theme, .jqx-warning .jqx-icon-arrow-down-hover-theme, .jqx-danger .jqx-icon-arrow-down-hover-theme, .jqx-success .jqx-icon-arrow-down-hover-theme, .jqx-info .jqx-icon-arrow-down-hover-theme {
  background-image: url('images/metro-icon-down-white.png');
}
.jqx-primary .jqx-icon-arrow-up-theme, .jqx-warning .jqx-icon-arrow-up-theme, .jqx-danger .jqx-icon-arrow-up-theme, .jqx-success .jqx-icon-arrow-up-theme, .jqx-info .jqx-icon-arrow-up-theme {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-selected-theme, .jqx-warning .jqx-icon-arrow-up-selected-theme, .jqx-danger .jqx-icon-arrow-up-selected-theme, .jqx-success .jqx-icon-arrow-up-selected-theme, .jqx-info .jqx-icon-arrow-up-selected-theme {
  background-image: url('images/metro-icon-up-white.png');
}
.jqx-primary .jqx-icon-arrow-up-hover-theme, .jqx-warning .jqx-icon-arrow-up-hover-theme, .jqx-danger .jqx-icon-arrow-up-hover-theme, .jqx-success .jqx-icon-arrow-up-hover-theme, .jqx-info .jqx-icon-arrow-up-hover-theme {
  background-image: url('images/metro-icon-up-white.png');
}


.jqx-primary .jqx-icon-arrow-left-theme, .jqx-warning .jqx-icon-arrow-left-theme, .jqx-danger .jqx-icon-arrow-left-theme, .jqx-success .jqx-icon-arrow-left-theme, .jqx-info .jqx-icon-arrow-left-theme {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-left-selected-theme, .jqx-warning .jqx-icon-arrow-left-selected-theme, .jqx-danger .jqx-icon-arrow-left-selected-theme, .jqx-success .jqx-icon-arrow-left-selected-theme, .jqx-info .jqx-icon-arrow-left-selected-theme {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-left-hover-theme, .jqx-warning .jqx-icon-arrow-left-hover-theme, .jqx-danger .jqx-icon-arrow-left-hover-theme, .jqx-success .jqx-icon-arrow-left-hover-theme, .jqx-info .jqx-icon-arrow-left-hover-theme {
  background-image: url('images/metro-icon-left-white.png');
}
.jqx-primary .jqx-icon-arrow-right-theme, .jqx-warning .jqx-icon-arrow-right-theme, .jqx-danger .jqx-icon-arrow-right-theme, .jqx-success .jqx-icon-arrow-right-theme, .jqx-info .jqx-icon-arrow-right-theme {
  background-image: url('images/metro-icon-right-white.png');
}
.jqx-primary .jqx-icon-arrow-right-selected-theme, .jqx-warning .jqx-icon-arrow-right-selected-theme, .jqx-danger .jqx-icon-arrow-right-selected-theme, .jqx-success .jqx-icon-arrow-right-selected-theme, .jqx-info .jqx-icon-arrow-right-selected-theme {
  background-image: url('images/metro-icon-right-white.png');
}
.jqx-primary .jqx-icon-arrow-right-hover-theme, .jqx-warning .jqx-icon-arrow-right-hover-theme, .jqx-danger .jqx-icon-arrow-right-hover-theme, .jqx-success .jqx-icon-arrow-right-hover-theme, .jqx-info .jqx-icon-arrow-right-hover-theme {
  background-image: url('images/metro-icon-right-white.png');
}

.jqx-slider-tooltip-theme.jqx-primary-slider, .jqx-slider-tooltip-theme.jqx-primary-slider .jqx-fill-state-normal-theme {
    border-color: #1ca8dd;
    background: #1ca8dd;
}
.jqx-slider-tooltip-theme.jqx-success-slider, .jqx-slider-tooltip-theme.jqx-success-slider .jqx-fill-state-normal-theme {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-theme.jqx-inverse-slider, .jqx-slider-tooltip-theme.jqx-inverse-slider .jqx-fill-state-normal-theme {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-theme.jqx-danger-slider, .jqx-slider-tooltip-theme.jqx-danger-slider .jqx-fill-state-normal-theme {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-theme.jqx-warning-slider, .jqx-slider-tooltip-theme.jqx-warning-slider .jqx-fill-state-normal-theme {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-theme.jqx-info-slider, .jqx-slider-tooltip-theme.jqx-info-slider .jqx-fill-state-normal-theme {
    border-color: #5bc0de;
    background: #5bc0de;
}




.jqx-fill-state-pressed-theme .jqx-icon-delete-theme
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-theme .jqx-icon-edit-theme
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-theme .jqx-icon-save-theme
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-theme .jqx-icon-cancel-theme
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-theme .jqx-icon-search-theme
{
    background-image: url(./images/search_white.png);
}
.jqx-fill-state-pressed-theme .jqx-icon-plus-theme
{
    background-image: url(./images/plus_white.png);
}
.jqx-fill-state-pressed-theme .jqx-menu-minimized-button-theme {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-fill-state-pressed-theme .jqx-editor-toolbar-icon-theme {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-editor-toolbar-button-theme{
    border-color: var(--jqx-border-color-theme);
}

.jqx-widget-theme #formWrap {
    color: #555 !important;    
}