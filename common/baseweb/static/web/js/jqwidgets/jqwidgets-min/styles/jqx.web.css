﻿/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-web {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-web {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-web {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-web {
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*top rounded Corners*/
.jqx-rc-t-web {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
}
/*bottom rounded Corners*/
.jqx-rc-b-web {
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*right rounded Corners*/
.jqx-rc-r-web {
    -moz-border-radius-topright: 2px;
    -webkit-border-top-right-radius: 2px;
    border-top-right-radius: 2px;
    -moz-border-radius-bottomright: 2px;
    -webkit-border-bottom-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
/*left rounded Corners*/
.jqx-rc-l-web {
    -moz-border-radius-topleft: 2px;
    -webkit-border-top-left-radius: 2px;
    border-top-left-radius: 2px;
    -moz-border-radius-bottomleft: 2px;
    -webkit-border-bottom-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
/*all rounded Corners*/
.jqx-rc-all-web {
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

.jqx-widget-web, .jqx-widget-content, .jqx-widget-header, .jqx-input-web {
    -webkit-transition: box-shadow linear 0.2s;
     -moz-transition: box-shadow linear 0.2s;
       -o-transition: box-shadow linear 0.2s;
          transition: box-shadow linear 0.2s;
}
.jqx-listitem-state-normal-web, .jqx-listitem-state-hover-web,.jqx-listitem-state-selected-web,
.jqx-tabs-title-selected-top-web, .jqx-tabs-title-selected-bottom-web{
    -webkit-transition: none;
     -moz-transition: none;
       -o-transition: none;
          transition: none;   
}
.jqx-fill-state-normal-web {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.75);;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.75);;
  -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.75);;
}

.jqx-widget-header-web, .jqx-fill-state-normal-web {
    text-shadow: 0 1px 0 rgb(240, 240, 240);
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.25);
    color: #444;
    background-color: #f0f0f0;
    background-image: linear-gradient(#ededed, #ededed 38%, #dedede);
    background-image: -webkit-linear-gradient(#ededed, #ededed 38%, #dedede);
    background-image: -moz-linear-gradient(#ededed, #ededed 38%, #dedede);
    background-image: -o-linear-gradient(#ededed, #ededed 38%, #dedede);
    -webkit-transition: color 200ms;
}
.jqx-widget-content-web {
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.25);
}
.jqx-fill-state-hover-web {
    text-shadow: 0 1px 0 rgb(240, 240, 240);
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.3);
    color: black;
    background-color: #f0f0f0;
    background-image: linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -webkit-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -moz-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -o-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12), inset 0 1px 2px rgba(255, 255, 255, 0.95);
    -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12), inset 0 1px 2px rgba(255, 255, 255, 0.95);
    -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12), inset 0 1px 2px rgba(255, 255, 255, 0.95);
    -webkit-transition: color 200ms;
}

.jqx-fill-state-pressed-web {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    text-shadow: none;
    background-color: #f0f0f0;
    background-image: linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -webkit-linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -moz-linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -o-linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
    -webkit-transition: color 200ms;
}

.jqx-grid-column-menubutton-web {
    background-color: transparent;
}

.jqx-calendar-row-header-web, .jqx-calendar-top-left-header-web {
    background-color: #f0f0f0;
    border: 0px solid #f2f2f2;
}

.jqx-calendar-column-header-web {
    background-color: #FFF;
    border-top: 1px solid #FFF;
    border-bottom: 1px solid #e9e9e9;
}

.jqx-scrollbar-state-normal-web {
    background-color: #f0f0f0;
    border: 1px solid #f0f0f0;
}
.jqx-grid-cell-hover-web {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}
.jqx-scrollbar-thumb-state-normal-web {
    background-color: #f0f0f0;
    background-image: linear-gradient(left, #ededed, #ededed 38%, #dedede);
    background-image: -webkit-linear-gradient(left, #ededed, #ededed 38%, #dedede);
    background-image: -moz-linear-gradient(left, #ededed, #ededed 38%, #dedede);
    background-image: -o-linear-gradient(left, #ededed, #ededed 38%, #dedede);
    background-image: -ms-linear-gradient(left, #ededed, #ededed 38%, #dedede);
}

.jqx-scrollbar-thumb-state-hover-web {
    background-color: #f0f0f0;
    background-image: linear-gradient(left,#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -webkit-linear-gradient(left,#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -moz-linear-gradient(left,#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -o-linear-gradient(left,#f0f0f0, #f0f0f0 38%, #e0e0e0);
    background-image: -ms-linear-gradient(left,#f0f0f0, #f0f0f0 38%, #e0e0e0);
}
.jqx-splitter-collapse-button-vertical-web, .jqx-splitter-collapse-button-horizontal-web {
    background: #b2b2b2;
}
.jqx-scrollbar-thumb-state-pressed-web, .jqx-progressbar-value-vertical-web{
    background-color: #f0f0f0;
    background-image: linear-gradient(left, #e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -webkit-linear-gradient(left, #e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -moz-linear-gradient(left, #e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -o-linear-gradient(left, #e7e7e7, #e7e7e7 38%, #d7d7d7);
    background-image: -ms-linear-gradient(left, #e7e7e7, #e7e7e7 38%, #d7d7d7);
}

.jqx-grid-column-sortdescbutton-web, jqx-grid-column-filterbutton-web, .jqx-grid-column-sortascbutton-web {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: #b2b2b2;
}
.jqx-slider-rangebar-web {
    background: #C3C3C3;
}
.jqx-menu-vertical-web {
    background: #f5f5f5;
    filter: none;
}

.jqx-checkbox-check-checked-web {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAcklEQVQY02NgwA/YoJgoEA/Es4DYgJBCJSBeD8SboRinBiYg7kZS2IosyQ/Eakh8LySFq4FYHFlxGRBvBOJYqMRqJMU+yApNkSRAeC0Sux3dfSCTetE0wKyXxOWhMKhTYIr9CAUXyJMzgLgBagBBgDPGAI2LGdNt0T1AAAAAAElFTkSuQmCC');
}

.jqx-combobox-content-web, .jqx-input-web {
    border-color: #b2b2b2;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    -moz-box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}
.jqx-combobox-content-web {
    border-color: #b2b2b2;
    border-color: rgba(0, 0, 0, 0.25);
 }
.jqx-fill-state-focus-web, .jqx-combobox-content-focus-web, .jqx-numberinput-focus-web {
    outline: none;
    border-color: #777777;
}
.jqx-dropdownbutton-popup-web.jqx-fill-state-focus-web {
   
}
.jqx-grid-bottomright-web, .jqx-panel-bottomright-web, .jqx-listbox-bottomright-web {
    background-color: #efefef;
}

.jqx-tabs-title-selected-top-web, .jqx-tabs-selection-tracker-top-web {
    border-color: #b2b2b2;
    border-bottom: 1px solid #fff;
    text-shadow: 0 1px 0 #f2f2f2;
    filter: none;
    color: #222;
    background: #fff;
}

.jqx-tabs-title-selected-bottom-web, .jqx-tabs-selection-tracker-bottom-web {
    border-color: #b2b2b2;
    border-top: 1px solid #fff;
    text-shadow: 0 1px 0 #f2f2f2;
    filter: none;
    color: #222;
    background: #fff;
}

.jqx-popup-web {
  -webkit-box-shadow: 4px 4px 23px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 4px 4px 23px rgba(0, 0, 0, 0.15);
  box-shadow: 4px 4px 23px rgba(0, 0, 0, 0.15);
}
.jqx-menu-popup-web>div:first-child{
    padding-bottom: 15px !important;
    padding-right: 15px !important;
}
.jqx-menu-popup-clear-web>div:first-child{
    padding-bottom: 0px !important;
}
.jqx-window-web, .jqx-tooltip-web {
  -webkit-box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
  -moz-box-shadow:  0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
  box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}
.jqx-docking-web .jqx-window-web {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}
.jqx-docking-panel-web .jqx-window-web {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.jqx-radiobutton-web {
    background-repeat: no-repeat;
    background: none;
}
.jqx-radiobutton-web-web, .jqx-radiobutton-hover-web {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    background-repeat: no-repeat;
}
.jqx-radiobutton-check-checked-web {
    filter: none;
    background: #666;
    background-repeat: no-repeat;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-radiobutton-check-indeterminate-web {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-radiobutton-check-indeterminate-disabled-web {
    filter: none;
    background: #999;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-track-horizontal-web, .jqx-slider-track-vertical-web {
    border-color: #b2b2b2;
    background: #f0f0f0;
}
.jqx-slider-button-web
{
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%; 
}
.jqx-layout-web
{
    background-color: #b2b2b2;
}