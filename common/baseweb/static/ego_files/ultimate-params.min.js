jQuery(document).ready(function(p){let u="",m="",h="",b="",v="",x="";jQuery(".ult-responsive").each(function(e,a){let t=jQuery(this),i=t.attr("data-responsive-json-new"),s=t.data("ultimate-target"),l="",n="",r="",d="",c="",o="";void 0===i&&null==i||p.each(JSON.parse(i),function(e,a){const i=e;void 0!==a&&null!=a&&(a=a.split(";"),jQuery.each(a,function(e,a){if(void 0!==a||null!=a){var t=a.split(":");switch(t[0]){case"large_screen":l+=i+":"+t[1]+";";break;case"desktop":n+=i+":"+t[1]+";";break;case"tablet":r+=i+":"+t[1]+";";break;case"tablet_portrait":d+=i+":"+t[1]+";";break;case"mobile_landscape":c+=i+":"+t[1]+";";break;case"mobile":o+=i+":"+t[1]+";"}}}))}),""!=o&&(x+=s+"{"+o+"}"),""!=c&&(v+=s+"{"+c+"}"),""!=d&&(b+=s+"{"+d+"}"),""!=r&&(h+=s+"{"+r+"}"),""!=n&&(m+=s+"{"+n+"}"),""!=l&&(u+=s+"{"+l+"}")});var e="<style>/** Ultimate: Media Responsive **/ ";e+=m,e+="@media (max-width: 1199px) { "+h+"}",e+="@media (max-width: 991px)  { "+b+"}",e+="@media (max-width: 767px)  { "+v+"}",e+="@media (max-width: 479px)  { "+x+"}",e+="/** Ultimate: Media Responsive - **/</style>",jQuery("head").append(e)});