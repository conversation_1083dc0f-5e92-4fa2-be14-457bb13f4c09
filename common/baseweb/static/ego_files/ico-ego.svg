<?xml version="1.0" encoding="UTF-8"?>
<svg id="Livello_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 166.5 166.9">
  <!-- Generator: Adobe Illustrator 29.0.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 192)  -->
  <defs>
    <style>
      .st0 {
        fill: none;
      }

      .st1 {
        fill: #d6303c;
      }

      .st2 {
        fill: #fff;
      }

      .st3 {
        fill: #abe233;
      }

      .st4 {
        clip-path: url(#clippath);
      }

      .st5 {
        fill: #e44a48;
      }
    </style>
    <clipPath id="clippath">
      <path class="st0" d="M123.2,158.4H43.3c-19.3,0-35-15.6-35-35V43.5c0-19.3,15.6-35,35-35h79.9c19.3,0,35,15.6,35,35v79.9c0,19.3-15.7,35-35,35Z"/>
    </clipPath>
  </defs>
  <path class="st5" d="M123.2,158.4H43.3c-19.3,0-35-15.6-35-35V43.5c0-19.3,15.6-35,35-35h79.9c19.3,0,35,15.6,35,35v79.9c0,19.3-15.7,35-35,35Z"/>
  <g>
    <g class="st4">
      <g>
        <g>
          <polygon class="st3" points="96.9 186.3 -12.7 186.3 -12.7 76.7 96.9 186.3"/>
          <polygon class="st3" points="75.9 -11.9 185.6 -11.9 185.6 97.7 75.9 -11.9"/>
        </g>
        <g>
          <polygon class="st1" points="96.9 186.3 -12.7 186.3 -12.7 76.7 96.9 186.3"/>
          <polygon class="st1" points="75.9 -11.9 185.6 -11.9 185.6 97.7 75.9 -11.9"/>
        </g>
      </g>
    </g>
    <path class="st2" d="M60.3,77.1v23.4c0,7.3,3.9,14.1,10.3,17.8,1.4.8,1.9,2.6,1.1,4-.5.9-1.5,1.5-2.5,1.5s-1-.1-1.5-.4c-8.1-4.7-13.2-13.5-13.2-22.8v-23.4c0-1.6,1.3-2.9,2.9-2.9s2.9,1.3,2.9,2.9h0ZM48.6,91v-12.9c0-15.5,11.6-28.4,26.4-29.3,5.7-.4,11.2.9,16.1,3.7,1.4.8,3.2.3,4-1.1.8-1.4.3-3.2-1.1-4-5.8-3.4-12.6-4.9-19.4-4.5-17.9,1.2-31.9,16.6-31.9,35.2v12.9c0,1.6,1.3,2.9,2.9,2.9s2.9-1.3,2.9-2.9ZM96.5,94.7c0,5.4,4.4,9.8,9.8,9.8s9.8-4.4,9.8-9.8-4.4-9.8-9.8-9.8-9.8,4.4-9.8,9.8ZM116,112.3h-19.5c-4.3,0-7.8,3.5-7.8,7.8v3.9h35.2v-3.9c0-4.3-3.5-7.8-7.8-7.8ZM87,107.7c1.5-1.2,3.2-2.1,5.1-2.6-.3-.5-.6-1-.9-1.5-2.2-1.6-3.6-4.1-3.6-7v-19.1c0-5.5-3.9-10.2-9-11-3.1-.5-6.3.4-8.7,2.4-2.4,2-3.8,5-3.8,8.2v15.6c0,11.2,5.6,21.6,14.7,28v-.6c0-2.1.5-4,1.2-5.8-6.2-5.3-10-13.1-10-21.5v-15.6c0-1.4.6-2.8,1.7-3.7,1.1-.9,2.5-1.3,4-1.1,2.3.4,4,2.6,4,5.2v19.1c0,4.5,2.1,8.4,5.2,11.1h0ZM79.1,54.7c-7.9-.8-15.4,2.6-20.2,8.9-1,1.3-.7,3.1.6,4.1,1.3,1,3.1.7,4.1-.6,3.5-4.7,9.1-7.1,14.9-6.6,8.4.8,15,8.4,15,17.4v4.6c1.7-1.7,3.6-3.2,5.9-4.1v-.5c0-11.9-8.9-22.1-20.3-23.2h0ZM106.2,77.1c1.7,0,3.3.3,4.9.8v-.8c0-6-1.6-11.9-4.6-17.1-.8-1.4-2.6-1.9-4-1.1-1.4.8-1.9,2.6-1.1,4,2.5,4.3,3.8,9.2,3.8,14.2h0c.3,0,.6,0,1,0Z"/>
  </g>
</svg>