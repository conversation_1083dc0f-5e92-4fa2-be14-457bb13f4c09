function Layzr(t){this._lastScroll=0,this._ticking=!1,t=t||{},this._optionsContainer=document.querySelector(t.container)||window,this._optionsSelector=t.selector||"[data-layzr]",this._optionsAttr=t.attr||"data-layzr",this._optionsAttrSrcSet=t.attrSrcSet||"data-layzr-srcset",this._optionsAttrRetina=t.retinaAttr||"data-layzr-retina",this._optionsAttrBg=t.bgAttr||"data-layzr-bg",this._optionsAttrHidden=t.hiddenAttr||"data-layzr-hidden",this._optionsThreshold=t.threshold||0,this._optionsBefore=t.before||null,this._optionsAfter=t.after||null,this._optionsCallback=t.callback||null,this._optionsIsloadedClass=t.isloadedClass||"is-loaded",this._optionsIsloadingClass=t.isloadingClass||"is-loading",this._retina=window.devicePixelRatio>1,this._srcAttr=this._retina?this._optionsAttrRetina:this._optionsAttr,this.updateSelector(),this._handlerBind=this._requestScroll.bind(this),this._create()}function simple_tooltip(t,e){jQuery(t).each((function(t){jQuery("body").append("<div class='"+e+"' id='"+e+t+"'>"+jQuery(this).find("span.tooltip-c").html()+"</div>");var i=jQuery("#"+e+t);jQuery(this).removeAttr("title").mouseover((function(){i.css({opacity:1,display:"none"}).fadeIn(400)})).mousemove((function(t){var e,n,o=jQuery(window).scrollTop(),s=jQuery(window).width(),a=15;e=s-30>=i.width()+t.pageX?t.pageX+a:s-i.width()-a,n=o+30>=t.pageY-i.height()?o+a:t.pageY-i.height()-33,i.css({left:e,top:n})})).mouseout((function(){i.css({left:"-9999px"})}))}))}
/*!
 * jquery.customSelect() - v0.4.1
 * http://adam.co/lab/jquery/customselect/
 * 2013-05-13
 *
 * Copyright 2013 Adam Coulombe
 * @license http://www.opensource.org/licenses/mit-license.html MIT License
 * @license http://www.gnu.org/licenses/gpl.html GPL2 License
 */!function(t){"function"==typeof define&&define.amd?define(["jquery"],(function(e){return t(e)})):"object"==typeof module&&"object"==typeof module.exports?exports=t(require("jquery")):t(jQuery)}((function(t){t.easing.jswing=t.easing.swing;var e=Math.pow,i=Math.sqrt,n=Math.sin,o=Math.cos,s=Math.PI,a=1.70158,r=1.525*a,l=a+1,d=2*s/3,c=2*s/4.5;function h(t){var e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375}t.extend(t.easing,{def:"easeOutQuad",swing:function(e){return t.easing[t.easing.def](e)},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return 1-(1-t)*(1-t)},easeInOutQuad:function(t){return t<.5?2*t*t:1-e(-2*t+2,2)/2},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return 1-e(1-t,3)},easeInOutCubic:function(t){return t<.5?4*t*t*t:1-e(-2*t+2,3)/2},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1-e(1-t,4)},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-e(-2*t+2,4)/2},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1-e(1-t,5)},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1-e(-2*t+2,5)/2},easeInSine:function(t){return 1-o(t*s/2)},easeOutSine:function(t){return n(t*s/2)},easeInOutSine:function(t){return-(o(s*t)-1)/2},easeInExpo:function(t){return 0===t?0:e(2,10*t-10)},easeOutExpo:function(t){return 1===t?1:1-e(2,-10*t)},easeInOutExpo:function(t){return 0===t?0:1===t?1:t<.5?e(2,20*t-10)/2:(2-e(2,-20*t+10))/2},easeInCirc:function(t){return 1-i(1-e(t,2))},easeOutCirc:function(t){return i(1-e(t-1,2))},easeInOutCirc:function(t){return t<.5?(1-i(1-e(2*t,2)))/2:(i(1-e(-2*t+2,2))+1)/2},easeInElastic:function(t){return 0===t?0:1===t?1:-e(2,10*t-10)*n((10*t-10.75)*d)},easeOutElastic:function(t){return 0===t?0:1===t?1:e(2,-10*t)*n((10*t-.75)*d)+1},easeInOutElastic:function(t){return 0===t?0:1===t?1:t<.5?-e(2,20*t-10)*n((20*t-11.125)*c)/2:e(2,-20*t+10)*n((20*t-11.125)*c)/2+1},easeInBack:function(t){return l*t*t*t-a*t*t},easeOutBack:function(t){return 1+l*e(t-1,3)+a*e(t-1,2)},easeInOutBack:function(t){return t<.5?e(2*t,2)*(7.189819*t-r)/2:(e(2*t-2,2)*((r+1)*(2*t-2)+r)+2)/2},easeInBounce:function(t){return 1-h(1-t)},easeOutBounce:h,easeInOutBounce:function(t){return t<.5?(1-h(1-2*t))/2:(1+h(2*t-1))/2}})})),The7Scroll=function(){var t,e,i;function n(n){i=0;const a={root:(e=n).root||null,rootMargin:e.offset||"0px",threshold:s(e.sensitivity)};t=new IntersectionObserver(o,a)}function o(t){const n=t[0].boundingClientRect.y,o=t[0].isIntersecting,s=n<i?"down":"up",a=Math.abs(parseFloat((100*t[0].intersectionRatio).toFixed(2)));e.callback({sensitivity:e.sensitivity,isInViewport:o,scrollPercentage:a,intersectionScrollDirection:s}),i=n}function s(t=0){const e=[];if(t>0&&t<=100){const i=100/t;for(let t=0;t<=100;t+=i)e.push(t/100)}else e.push(0);return e}return n.prototype.getScrollObserver=function(){return t},n}(),function(){for(var t=0,e=["ms","moz","webkit","o"],i=0;i<e.length&&!window.requestAnimationFrame;++i)window.requestAnimationFrame=window[e[i]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[i]+"CancelAnimationFrame"]||window[e[i]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e,i){var n=(new Date).getTime(),o=Math.max(0,16-(n-t)),s=window.setTimeout((function(){e(n+o)}),o);return t=n+o,s}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(t){clearTimeout(t)})}(),Layzr.prototype._requestScroll=function(){this._optionsContainer===window?this._lastScroll=window.pageYOffset:this._lastScroll=this._optionsContainer.scrollTop+this._getOffset(this._optionsContainer),this._requestTick()},Layzr.prototype._requestTick=function(){this._ticking||(requestAnimationFrame(this.update.bind(this)),this._ticking=!0)},Layzr.prototype._getOffset=function(t){return t.getBoundingClientRect().top+window.pageYOffset},Layzr.prototype._getContainerHeight=function(){return this._optionsContainer.innerHeight||this._optionsContainer.offsetHeight},Layzr.prototype._create=function(){this._handlerBind(),this._optionsContainer.addEventListener("scroll",this._handlerBind,!1),this._optionsContainer.addEventListener("resize",this._handlerBind,!1)},Layzr.prototype._destroy=function(){this._optionsContainer.removeEventListener("scroll",this._handlerBind,!1),this._optionsContainer.removeEventListener("resize",this._handlerBind,!1)},Layzr.prototype._inViewport=function(t){if(null==t.offsetParent)return!1;var e=this._lastScroll,i=e+this._getContainerHeight(),n=this._getOffset(t),o=n+this._getContainerHeight(),s=this._optionsThreshold/100*window.innerHeight;return o>=e-s&&n<=i+s&&!t.hasAttribute(this._optionsAttrHidden)},Layzr.prototype._reveal=function(t){var e=t.getAttribute(this._srcAttr)||t.getAttribute(this._optionsAttr),i=this,n=this._optionsIsloadedClass,o=this._optionsIsloadingClass;"function"==typeof this._optionsCallback&&(t.addEventListener?t.addEventListener("load",(function(){i._optionsCallback.call(t),i._nodes=i._nodes.filter((e=>e!==t)),t.classList.add(n),t.classList.remove(o)})):t.attachEvent("onload",(function(){i._optionsCallback.call(t),i._nodes=i._nodes.filter((e=>e!==t)),t.classList.add(n),t.classList.remove(o)})));"function"==typeof this._optionsBefore&&this._optionsBefore.call(t);var s=!1;t.hasAttribute(this._optionsAttrBg)?(t.style.backgroundImage="url("+e+")",s=!0):(e&&(t.removeAttribute("src"),t.setAttribute("src",e),s=!0),t.hasAttribute(this._optionsAttrSrcSet)&&(t.setAttribute("srcset",t.getAttribute(this._optionsAttrSrcSet)),s=!0)),s&&t.classList.add(o),"function"==typeof this._optionsAfter&&this._optionsAfter.call(t),t.removeAttribute(this._optionsAttr),t.removeAttribute(this._optionsAttrSrcSet),t.removeAttribute(this._optionsAttrRetina),t.removeAttribute(this._optionsAttrBg),t.removeAttribute(this._optionsAttrHidden)},Layzr.prototype.updateSelector=function(){this._nodes=Array.from(document.querySelectorAll(this._optionsSelector)),this._nodes=this._prepareItems(this._nodes)},Layzr.prototype.addItems=function(t){var e=Array.from(t.querySelectorAll(this._optionsSelector));e=this._prepareItems(e),this._nodes=this._nodes.concat(e),this.removeDuplicates()},Layzr.prototype.removeDuplicates=function(){var t=Array.from(new Set(this._nodes));t.length!==this._nodes.length&&(this._nodes=t)},Layzr.prototype._prepareItems=function(t){var e=this._optionsSelector.replace(/\./g,""),i=this._optionsIsloadedClass,n=this._optionsIsloadingClass;return t.filter((function(t){if(t.hasAttribute("data-src")){var o=t.getAttribute("data-src"),s=o.substring(o.lastIndexOf(".")+1);"png"!==s&&"svg"!==s||t.parentNode.classList.add("layzr-bg-transparent")}else if(!t.classList.contains(i)&&!t.classList.contains(n))return t.classList.remove(e),!1;return!0}))},Layzr.prototype.update=function(){for(var t=this._nodes.length,e=[],i=0;i<t;i++){var n=this._nodes[i];(n.hasAttribute(this._optionsAttr)||n.hasAttribute(this._optionsAttrSrcSet)||n.hasAttribute(this._optionsAttrRetina))&&this._inViewport(n)&&e.push(n)}var o=this;e.forEach((function(t){o._reveal(t)})),this._ticking=!1},function(t,e){"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():t.PhotoSwipe=e()}(this,(function(){"use strict";return function(t,e,i,n){var o={features:null,bind:function(t,e,i,n){var o=(n?"remove":"add")+"EventListener";e=e.split(" ");for(var s=0;s<e.length;s++)e[s]&&t[o](e[s],i,!1)},isArray:function(t){return t instanceof Array},createEl:function(t,e){var i=document.createElement(e||"div");return t&&(i.className=t),i},getScrollY:function(){var t=window.pageYOffset;return void 0!==t?t:document.documentElement.scrollTop},unbind:function(t,e,i){o.bind(t,e,i,!0)},removeClass:function(t,e){var i=new RegExp("(\\s|^)"+e+"(\\s|$)");t.className=t.className.replace(i," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(t,e){o.hasClass(t,e)||(t.className+=(t.className?" ":"")+e)},hasClass:function(t,e){return t.className&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(t.className)},getChildByClass:function(t,e){for(var i=t.firstChild;i;){if(o.hasClass(i,e))return i;i=i.nextSibling}},arraySearch:function(t,e,i){for(var n=t.length;n--;)if(t[n][i]===e)return n;return-1},extend:function(t,e,i){for(var n in e)if(e.hasOwnProperty(n)){if(i&&t.hasOwnProperty(n))continue;t[n]=e[n]}},easing:{sine:{out:function(t){return Math.sin(t*(Math.PI/2))},inOut:function(t){return-(Math.cos(Math.PI*t)-1)/2}},cubic:{out:function(t){return--t*t*t+1}}},detectFeatures:function(){if(o.features)return o.features;var t=o.createEl().style,e="",i={};if(i.oldIE=document.all&&!document.addEventListener,i.touch="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,window.requestAnimationFrame&&(i.raf=window.requestAnimationFrame,i.caf=window.cancelAnimationFrame),i.pointerEvent=navigator.pointerEnabled||navigator.msPointerEnabled,!i.pointerEvent){var n=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var s=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);s&&s.length>0&&((s=parseInt(s[1],10))>=1&&s<8&&(i.isOldIOSPhone=!0))}var a=n.match(/Android\s([0-9\.]*)/),r=a?a[1]:0;(r=parseFloat(r))>=1&&(r<4.4&&(i.isOldAndroid=!0),i.androidVersion=r),i.isMobileOpera=/opera mini|opera mobi/i.test(n)}for(var l,d,c=["transform","perspective","animationName"],h=["","webkit","Moz","ms","O"],u=0;u<4;u++){e=h[u];for(var p=0;p<3;p++)l=c[p],d=e+(e?l.charAt(0).toUpperCase()+l.slice(1):l),!i[l]&&d in t&&(i[l]=d);e&&!i.raf&&(e=e.toLowerCase(),i.raf=window[e+"RequestAnimationFrame"],i.raf&&(i.caf=window[e+"CancelAnimationFrame"]||window[e+"CancelRequestAnimationFrame"]))}if(!i.raf){var f=0;i.raf=function(t){var e=(new Date).getTime(),i=Math.max(0,16-(e-f)),n=window.setTimeout((function(){t(e+i)}),i);return f=e+i,n},i.caf=function(t){clearTimeout(t)}}return i.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,o.features=i,i}};o.detectFeatures(),o.features.oldIE&&(o.bind=function(t,e,i,n){e=e.split(" ");for(var o,s=(n?"detach":"attach")+"Event",a=function(){i.handleEvent.call(i)},r=0;r<e.length;r++)if(o=e[r])if("object"==typeof i&&i.handleEvent){if(n){if(!i["oldIE"+o])return!1}else i["oldIE"+o]=a;t[s]("on"+o,i["oldIE"+o])}else t[s]("on"+o,i)});var s=this,a={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(t){return"A"===t.tagName},getDoubleTapZoom:function(t,e){return t||e.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};o.extend(a,n);var r,l,d,c,h,u,p,f,m,g,v,w,y,b,C,x,_,S,k,T,I,z,L,E,O,P,D,A,M,F,H,B,R,W,j,$,N,G,Y,q,U,V,X,Z,Q,K,J,tt,et,it,nt,ot,st,at,rt,lt,dt={x:0,y:0},ct={x:0,y:0},ht={x:0,y:0},ut={},pt=0,ft={},mt={x:0,y:0},gt=0,vt=!0,wt=[],yt={},bt=!1,Ct=function(t,e){o.extend(s,e.publicMethods),wt.push(t)},xt=function(t){var e=je();return t>e-1?t-e:t<0?e+t:t},_t={},St=function(t,e){return _t[t]||(_t[t]=[]),_t[t].push(e)},kt=function(t){var e=_t[t];if(e){var i=Array.prototype.slice.call(arguments);i.shift();for(var n=0;n<e.length;n++)e[n].apply(s,i)}},Tt=function(){return(new Date).getTime()},It=function(t){at=t,s.bg.style.opacity=t*a.bgOpacity},zt=function(t,e,i,n,o){(!bt||o&&o!==s.currItem)&&(n/=o?o.fitRatio:s.currItem.fitRatio),t[z]=w+e+"px, "+i+"px"+y+" scale("+n+")"},Lt=function(t){et&&(t&&(g>s.currItem.fitRatio?bt||(Ze(s.currItem,!1,!0),bt=!0):bt&&(Ze(s.currItem),bt=!1)),zt(et,ht.x,ht.y,g))},Et=function(t){t.container&&zt(t.container.style,t.initialPosition.x,t.initialPosition.y,t.initialZoomLevel,t)},Ot=function(t,e){e[z]=w+t+"px, 0px"+y},Pt=function(t,e){if(!a.loop&&e){var i=c+(mt.x*pt-t)/mt.x,n=Math.round(t-ce.x);(i<0&&n>0||i>=je()-1&&n<0)&&(t=ce.x+n*a.mainScrollEndFriction)}ce.x=t,Ot(t,h)},Dt=function(t,e){var i=he[t]-ft[t];return ct[t]+dt[t]+i-i*(e/v)},At=function(t,e){t.x=e.x,t.y=e.y,e.id&&(t.id=e.id)},Mt=function(t){t.x=Math.round(t.x),t.y=Math.round(t.y)},Ft=null,Ht=function(){Ft&&(o.unbind(document,"mousemove",Ht),o.addClass(t,"pswp--has_mouse"),a.mouseUsed=!0,kt("mouseUsed")),Ft=setTimeout((function(){Ft=null}),100)},Bt=function(t,e){var i=qe(s.currItem,ut,t);return e&&(tt=i),i},Rt=function(t){return t||(t=s.currItem),t.initialZoomLevel},Wt=function(t){return t||(t=s.currItem),t.w>0?a.maxSpreadZoom:1},jt=function(t,e,i,n){return n===s.currItem.initialZoomLevel?(i[t]=s.currItem.initialPosition[t],!0):(i[t]=Dt(t,n),i[t]>e.min[t]?(i[t]=e.min[t],!0):i[t]<e.max[t]&&(i[t]=e.max[t],!0))},$t=function(t){var e="";a.escKey&&27===t.keyCode?e="close":a.arrowKeys&&(37===t.keyCode?e="prev":39===t.keyCode&&(e="next")),e&&(t.ctrlKey||t.altKey||t.shiftKey||t.metaKey||(t.preventDefault?t.preventDefault():t.returnValue=!1,s[e]()))},Nt=function(t){t&&(V||U||it||N)&&(t.preventDefault(),t.stopPropagation())},Gt=function(){s.setScrollOffset(0,o.getScrollY())},Yt={},qt=0,Ut=function(t){Yt[t]&&(Yt[t].raf&&P(Yt[t].raf),qt--,delete Yt[t])},Vt=function(t){Yt[t]&&Ut(t),Yt[t]||(qt++,Yt[t]={})},Xt=function(){for(var t in Yt)Yt.hasOwnProperty(t)&&Ut(t)},Zt=function(t,e,i,n,o,s,a){var r,l=Tt();Vt(t);var d=function(){if(Yt[t]){if((r=Tt()-l)>=n)return Ut(t),s(i),void(a&&a());s((i-e)*o(r/n)+e),Yt[t].raf=O(d)}};d()},Qt={shout:kt,listen:St,viewportSize:ut,options:a,isMainScrollAnimating:function(){return it},getZoomLevel:function(){return g},getCurrentIndex:function(){return c},isDragging:function(){return Y},isZooming:function(){return K},setScrollOffset:function(t,e){ft.x=t,F=ft.y=e,kt("updateScrollOffset",ft)},applyZoomPan:function(t,e,i,n){ht.x=e,ht.y=i,g=t,Lt(n)},init:function(){if(!r&&!l){var i;s.framework=o,s.template=t,s.bg=o.getChildByClass(t,"pswp__bg"),D=t.className,r=!0,H=o.detectFeatures(),O=H.raf,P=H.caf,z=H.transform,M=H.oldIE,s.scrollWrap=o.getChildByClass(t,"pswp__scroll-wrap"),s.container=o.getChildByClass(s.scrollWrap,"pswp__container"),h=s.container.style,s.itemHolders=x=[{el:s.container.children[0],wrap:0,index:-1},{el:s.container.children[1],wrap:0,index:-1},{el:s.container.children[2],wrap:0,index:-1}],x[0].el.style.display=x[2].el.style.display="none",function(){if(z){var e=H.perspective&&!E;return w="translate"+(e?"3d(":"("),void(y=H.perspective?", 0px)":")")}z="left",o.addClass(t,"pswp--ie"),Ot=function(t,e){e.left=t+"px"},Et=function(t){var e=t.fitRatio>1?1:t.fitRatio,i=t.container.style,n=e*t.w,o=e*t.h;i.width=n+"px",i.height=o+"px",i.left=t.initialPosition.x+"px",i.top=t.initialPosition.y+"px"},Lt=function(){if(et){var t=et,e=s.currItem,i=e.fitRatio>1?1:e.fitRatio,n=i*e.w,o=i*e.h;t.width=n+"px",t.height=o+"px",t.left=ht.x+"px",t.top=ht.y+"px"}}}(),m={resize:s.updateSize,orientationchange:function(){clearTimeout(B),B=setTimeout((function(){ut.x!==s.scrollWrap.clientWidth&&s.updateSize()}),500)},scroll:Gt,keydown:$t,click:Nt};var n=H.isOldIOSPhone||H.isOldAndroid||H.isMobileOpera;for(H.animationName&&H.transform&&!n||(a.showAnimationDuration=a.hideAnimationDuration=0),i=0;i<wt.length;i++)s["init"+wt[i]]();if(e)(s.ui=new e(s,o)).init();kt("firstUpdate"),c=c||a.index||0,(isNaN(c)||c<0||c>=je())&&(c=0),s.currItem=We(c),(H.isOldIOSPhone||H.isOldAndroid)&&(vt=!1),t.setAttribute("aria-hidden","false"),a.modal&&(vt?t.style.position="fixed":(t.style.position="absolute",t.style.top=o.getScrollY()+"px")),void 0===F&&(kt("initialLayout"),F=A=o.getScrollY());var d="pswp--open ";for(a.mainClass&&(d+=a.mainClass+" "),a.showHideOpacity&&(d+="pswp--animate_opacity "),d+=E?"pswp--touch":"pswp--notouch",d+=H.animationName?" pswp--css_animation":"",d+=H.svg?" pswp--svg":"",o.addClass(t,d),s.updateSize(),u=-1,gt=null,i=0;i<3;i++)Ot((i+u)*mt.x,x[i].el.style);M||o.bind(s.scrollWrap,f,s),St("initialZoomInEnd",(function(){s.setContent(x[0],c-1),s.setContent(x[2],c+1),x[0].el.style.display=x[2].el.style.display="block",a.focus&&t.focus(),o.bind(document,"keydown",s),H.transform&&o.bind(s.scrollWrap,"click",s),a.mouseUsed||o.bind(document,"mousemove",Ht),o.bind(window,"resize scroll orientationchange",s),kt("bindEvents")})),s.setContent(x[1],c),s.updateCurrItem(),kt("afterInit"),vt||(b=setInterval((function(){qt||Y||K||g!==s.currItem.initialZoomLevel||s.updateSize()}),1e3)),o.addClass(t,"pswp--visible")}},close:function(){r&&(r=!1,l=!0,kt("close"),o.unbind(window,"resize scroll orientationchange",s),o.unbind(window,"scroll",m.scroll),o.unbind(document,"keydown",s),o.unbind(document,"mousemove",Ht),H.transform&&o.unbind(s.scrollWrap,"click",s),Y&&o.unbind(window,p,s),clearTimeout(B),kt("unbindEvents"),$e(s.currItem,null,!0,s.destroy))},destroy:function(){kt("destroy"),Fe&&clearTimeout(Fe),t.setAttribute("aria-hidden","true"),t.className=D,b&&clearInterval(b),o.unbind(s.scrollWrap,f,s),o.unbind(window,"scroll",s),fe(),Xt(),_t=null},panTo:function(t,e,i){i||(t>tt.min.x?t=tt.min.x:t<tt.max.x&&(t=tt.max.x),e>tt.min.y?e=tt.min.y:e<tt.max.y&&(e=tt.max.y)),ht.x=t,ht.y=e,Lt()},handleEvent:function(t){t=t||window.event,m[t.type]&&m[t.type](t)},goTo:function(t){var e=(t=xt(t))-c;gt=e,c=t,s.currItem=We(c),pt-=e,Pt(mt.x*pt),Xt(),it=!1,s.updateCurrItem()},next:function(){s.goTo(c+1)},prev:function(){s.goTo(c-1)},updateCurrZoomItem:function(t){if(t&&kt("beforeChange",0),x[1].el.children.length){var e=x[1].el.children[0];et=o.hasClass(e,"pswp__zoom-wrap")?e.style:null}else et=null;tt=s.currItem.bounds,v=g=s.currItem.initialZoomLevel,ht.x=tt.center.x,ht.y=tt.center.y,t&&kt("afterChange")},invalidateCurrItems:function(){C=!0;for(var t=0;t<3;t++)x[t].item&&(x[t].item.needsUpdate=!0)},updateCurrItem:function(t){if(0!==gt){var e,i=Math.abs(gt);if(!(t&&i<2)){s.currItem=We(c),bt=!1,kt("beforeChange",gt),i>=3&&(u+=gt+(gt>0?-3:3),i=3);for(var n=0;n<i;n++)gt>0?(e=x.shift(),x[2]=e,u++,Ot((u+2)*mt.x,e.el.style),s.setContent(e,c-i+n+1+1)):(e=x.pop(),x.unshift(e),u--,Ot(u*mt.x,e.el.style),s.setContent(e,c+i-n-1-1));if(et&&1===Math.abs(gt)){var o=We(_);o.initialZoomLevel!==g&&(qe(o,ut),Ze(o),Et(o))}gt=0,s.updateCurrZoomItem(),_=c,kt("afterChange")}}},updateSize:function(e){if(!vt&&a.modal){var i=o.getScrollY();if(F!==i&&(t.style.top=i+"px",F=i),!e&&yt.x===window.innerWidth&&yt.y===window.innerHeight)return;yt.x=window.innerWidth,yt.y=window.innerHeight,t.style.height=yt.y+"px"}if(ut.x=s.scrollWrap.clientWidth,ut.y=s.scrollWrap.clientHeight,Gt(),mt.x=ut.x+Math.round(ut.x*a.spacing),mt.y=ut.y,Pt(mt.x*pt),kt("beforeResize"),void 0!==u){for(var n,r,l,d=0;d<3;d++)n=x[d],Ot((d+u)*mt.x,n.el.style),l=c+d-1,a.loop&&je()>2&&(l=xt(l)),(r=We(l))&&(C||r.needsUpdate||!r.bounds)?(s.cleanSlide(r),s.setContent(n,l),1===d&&(s.currItem=r,s.updateCurrZoomItem(!0)),r.needsUpdate=!1):-1===n.index&&l>=0&&s.setContent(n,l),r&&r.container&&(qe(r,ut),Ze(r),Et(r));C=!1}v=g=s.currItem.initialZoomLevel,(tt=s.currItem.bounds)&&(ht.x=tt.center.x,ht.y=tt.center.y,Lt(!0)),kt("resize")},zoomTo:function(t,e,i,n,s){e&&(v=g,he.x=Math.abs(e.x)-ht.x,he.y=Math.abs(e.y)-ht.y,At(ct,ht));var a=Bt(t,!1),r={};jt("x",a,r,t),jt("y",a,r,t);var l=g,d=ht.x,c=ht.y;Mt(r);var h=function(e){1===e?(g=t,ht.x=r.x,ht.y=r.y):(g=(t-l)*e+l,ht.x=(r.x-d)*e+d,ht.y=(r.y-c)*e+c),s&&s(e),Lt(1===e)};i?Zt("customZoomTo",0,1,i,n||o.easing.sine.inOut,h):h(1)}},Kt={},Jt={},te={},ee={},ie={},ne=[],oe={},se=[],ae={},re=0,le={x:0,y:0},de=0,ce={x:0,y:0},he={x:0,y:0},ue={x:0,y:0},pe=function(t,e){return ae.x=Math.abs(t.x-e.x),ae.y=Math.abs(t.y-e.y),Math.sqrt(ae.x*ae.x+ae.y*ae.y)},fe=function(){X&&(P(X),X=null)},me=function(){Y&&(X=O(me),Le())},ge=function(t,e){return!(!t||t===document)&&!(t.getAttribute("class")&&t.getAttribute("class").indexOf("pswp__scroll-wrap")>-1)&&(e(t)?t:ge(t.parentNode,e))},ve={},we=function(t,e){return ve.prevent=!ge(t.target,a.isClickableElement),kt("preventDragEvent",t,e,ve),ve.prevent},ye=function(t,e){return e.x=t.pageX,e.y=t.pageY,e.id=t.identifier,e},be=function(t,e,i){i.x=.5*(t.x+e.x),i.y=.5*(t.y+e.y)},Ce=function(){var t=ht.y-s.currItem.initialPosition.y;return 1-Math.abs(t/(ut.y/2))},xe={},_e={},Se=[],ke=function(t){for(;Se.length>0;)Se.pop();return L?(lt=0,ne.forEach((function(t){0===lt?Se[0]=t:1===lt&&(Se[1]=t),lt++}))):t.type.indexOf("touch")>-1?t.touches&&t.touches.length>0&&(Se[0]=ye(t.touches[0],xe),t.touches.length>1&&(Se[1]=ye(t.touches[1],_e))):(xe.x=t.pageX,xe.y=t.pageY,xe.id="",Se[0]=xe),Se},Te=function(t,e){var i,n,o,r,l=ht[t]+e[t],d=e[t]>0,c=ce.x+e.x,h=ce.x-oe.x;return i=l>tt.min[t]||l<tt.max[t]?a.panEndFriction:1,l=ht[t]+e[t]*i,!a.allowPanToNext&&g!==s.currItem.initialZoomLevel||(et?"h"!==nt||"x"!==t||U||(d?(l>tt.min[t]&&(i=a.panEndFriction,tt.min[t]-l,n=tt.min[t]-ct[t]),(n<=0||h<0)&&je()>1?(r=c,h<0&&c>oe.x&&(r=oe.x)):tt.min.x!==tt.max.x&&(o=l)):(l<tt.max[t]&&(i=a.panEndFriction,l-tt.max[t],n=ct[t]-tt.max[t]),(n<=0||h>0)&&je()>1?(r=c,h>0&&c<oe.x&&(r=oe.x)):tt.min.x!==tt.max.x&&(o=l))):r=c,"x"!==t)?void(it||Z||g>s.currItem.fitRatio&&(ht[t]+=e[t]*i)):(void 0!==r&&(Pt(r,!0),Z=r!==oe.x),tt.min.x!==tt.max.x&&(void 0!==o?ht.x=o:Z||(ht.x+=e.x*i)),void 0!==r)},Ie=function(t){if(!("mousedown"===t.type&&t.button>0)){if(Re)return void t.preventDefault();if(!G||"mousedown"!==t.type){if(we(t,!0)&&t.preventDefault(),kt("pointerDown"),L){var e=o.arraySearch(ne,t.pointerId,"id");e<0&&(e=ne.length),ne[e]={x:t.pageX,y:t.pageY,id:t.pointerId}}var i=ke(t),n=i.length;Q=null,Xt(),Y&&1!==n||(Y=ot=!0,o.bind(window,p,s),$=rt=st=N=Z=V=q=U=!1,nt=null,kt("firstTouchStart",i),At(ct,ht),dt.x=dt.y=0,At(ee,i[0]),At(ie,ee),oe.x=mt.x*pt,se=[{x:ee.x,y:ee.y}],W=R=Tt(),Bt(g,!0),fe(),me()),!K&&n>1&&!it&&!Z&&(v=g,U=!1,K=q=!0,dt.y=dt.x=0,At(ct,ht),At(Kt,i[0]),At(Jt,i[1]),be(Kt,Jt,ue),he.x=Math.abs(ue.x)-ht.x,he.y=Math.abs(ue.y)-ht.y,J=pe(Kt,Jt))}}},ze=function(t){if(t.preventDefault(),L){var e=o.arraySearch(ne,t.pointerId,"id");if(e>-1){var i=ne[e];i.x=t.pageX,i.y=t.pageY}}if(Y){var n=ke(t);if(nt||V||K)Q=n;else if(ce.x!==mt.x*pt)nt="h";else{var s=Math.abs(n[0].x-ee.x)-Math.abs(n[0].y-ee.y);Math.abs(s)>=10&&(nt=s>0?"h":"v",Q=n)}}},Le=function(){if(Q){var t=Q.length;if(0!==t)if(At(Kt,Q[0]),te.x=Kt.x-ee.x,te.y=Kt.y-ee.y,K&&t>1){if(ee.x=Kt.x,ee.y=Kt.y,!te.x&&!te.y&&function(t,e){return t.x===e.x&&t.y===e.y}(Q[1],Jt))return;At(Jt,Q[1]),U||(U=!0,kt("zoomGestureStarted"));var e=pe(Kt,Jt),i=Ae(e);i>s.currItem.initialZoomLevel+s.currItem.initialZoomLevel/15&&(rt=!0);var n=1,o=Rt(),r=Wt();if(i<o)if(a.pinchToClose&&!rt&&v<=s.currItem.initialZoomLevel){var l=1-(o-i)/(o/1.2);It(l),kt("onPinchClose",l),st=!0}else(n=(o-i)/o)>1&&(n=1),i=o-n*(o/3);else i>r&&((n=(i-r)/(6*o))>1&&(n=1),i=r+n*o);n<0&&(n=0),e,be(Kt,Jt,le),dt.x+=le.x-ue.x,dt.y+=le.y-ue.y,At(ue,le),ht.x=Dt("x",i),ht.y=Dt("y",i),$=i>g,g=i,Lt()}else{if(!nt)return;if(ot&&(ot=!1,Math.abs(te.x)>=10&&(te.x-=Q[0].x-ie.x),Math.abs(te.y)>=10&&(te.y-=Q[0].y-ie.y)),ee.x=Kt.x,ee.y=Kt.y,0===te.x&&0===te.y)return;if("v"===nt&&a.closeOnVerticalDrag&&"fit"===a.scaleMode&&g===s.currItem.initialZoomLevel){dt.y+=te.y,ht.y+=te.y;var d=Ce();return N=!0,kt("onVerticalDrag",d),It(d),void Lt()}(function(t,e,i){if(t-W>50){var n=se.length>2?se.shift():{};n.x=e,n.y=i,se.push(n),W=t}})(Tt(),Kt.x,Kt.y),V=!0,tt=s.currItem.bounds,Te("x",te)||(Te("y",te),Mt(ht),Lt())}}},Ee=function(t){if(H.isOldAndroid){if(G&&"mouseup"===t.type)return;t.type.indexOf("touch")>-1&&(clearTimeout(G),G=setTimeout((function(){G=0}),600))}var e;if(kt("pointerUp"),we(t,!1)&&t.preventDefault(),L){var i=o.arraySearch(ne,t.pointerId,"id");if(i>-1)if(e=ne.splice(i,1)[0],navigator.pointerEnabled)e.type=t.pointerType||"mouse";else{e.type={4:"mouse",2:"touch",3:"pen"}[t.pointerType],e.type||(e.type=t.pointerType||"mouse")}}var n,r=ke(t),l=r.length;if("mouseup"===t.type&&(l=0),2===l)return Q=null,!0;1===l&&At(ie,r[0]),0!==l||nt||it||(e||("mouseup"===t.type?e={x:t.pageX,y:t.pageY,type:"mouse"}:t.changedTouches&&t.changedTouches[0]&&(e={x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY,type:"touch"})),kt("touchRelease",t,e));var d=-1;if(0===l&&(Y=!1,o.unbind(window,p,s),fe(),K?d=0:-1!==de&&(d=Tt()-de)),de=1===l?Tt():-1,n=-1!==d&&d<150?"zoom":"swipe",K&&l<2&&(K=!1,1===l&&(n="zoomPointerUp"),kt("zoomGestureEnded")),Q=null,V||U||it||N)if(Xt(),j||(j=Oe()),j.calculateSwipeSpeed("x"),N){if(Ce()<a.verticalDragRange)s.close();else{var c=ht.y,h=at;Zt("verticalDrag",0,1,300,o.easing.cubic.out,(function(t){ht.y=(s.currItem.initialPosition.y-c)*t+c,It((1-h)*t+h),Lt()})),kt("onVerticalDrag",1)}}else{if((Z||it)&&0===l){if(De(n,j))return;n="zoomPointerUp"}if(!it)return"swipe"!==n?void Me():void(!Z&&g>s.currItem.fitRatio&&Pe(j))}},Oe=function(){var t,e,i={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(n){se.length>1?(t=Tt()-W+50,e=se[se.length-2][n]):(t=Tt()-R,e=ie[n]),i.lastFlickOffset[n]=ee[n]-e,i.lastFlickDist[n]=Math.abs(i.lastFlickOffset[n]),i.lastFlickDist[n]>20?i.lastFlickSpeed[n]=i.lastFlickOffset[n]/t:i.lastFlickSpeed[n]=0,Math.abs(i.lastFlickSpeed[n])<.1&&(i.lastFlickSpeed[n]=0),i.slowDownRatio[n]=.95,i.slowDownRatioReverse[n]=1-i.slowDownRatio[n],i.speedDecelerationRatio[n]=1},calculateOverBoundsAnimOffset:function(t,e){i.backAnimStarted[t]||(ht[t]>tt.min[t]?i.backAnimDestination[t]=tt.min[t]:ht[t]<tt.max[t]&&(i.backAnimDestination[t]=tt.max[t]),void 0!==i.backAnimDestination[t]&&(i.slowDownRatio[t]=.7,i.slowDownRatioReverse[t]=1-i.slowDownRatio[t],i.speedDecelerationRatioAbs[t]<.05&&(i.lastFlickSpeed[t]=0,i.backAnimStarted[t]=!0,Zt("bounceZoomPan"+t,ht[t],i.backAnimDestination[t],e||300,o.easing.sine.out,(function(e){ht[t]=e,Lt()})))))},calculateAnimOffset:function(t){i.backAnimStarted[t]||(i.speedDecelerationRatio[t]=i.speedDecelerationRatio[t]*(i.slowDownRatio[t]+i.slowDownRatioReverse[t]-i.slowDownRatioReverse[t]*i.timeDiff/10),i.speedDecelerationRatioAbs[t]=Math.abs(i.lastFlickSpeed[t]*i.speedDecelerationRatio[t]),i.distanceOffset[t]=i.lastFlickSpeed[t]*i.speedDecelerationRatio[t]*i.timeDiff,ht[t]+=i.distanceOffset[t])},panAnimLoop:function(){if(Yt.zoomPan&&(Yt.zoomPan.raf=O(i.panAnimLoop),i.now=Tt(),i.timeDiff=i.now-i.lastNow,i.lastNow=i.now,i.calculateAnimOffset("x"),i.calculateAnimOffset("y"),Lt(),i.calculateOverBoundsAnimOffset("x"),i.calculateOverBoundsAnimOffset("y"),i.speedDecelerationRatioAbs.x<.05&&i.speedDecelerationRatioAbs.y<.05))return ht.x=Math.round(ht.x),ht.y=Math.round(ht.y),Lt(),void Ut("zoomPan")}};return i},Pe=function(t){return t.calculateSwipeSpeed("y"),tt=s.currItem.bounds,t.backAnimDestination={},t.backAnimStarted={},Math.abs(t.lastFlickSpeed.x)<=.05&&Math.abs(t.lastFlickSpeed.y)<=.05?(t.speedDecelerationRatioAbs.x=t.speedDecelerationRatioAbs.y=0,t.calculateOverBoundsAnimOffset("x"),t.calculateOverBoundsAnimOffset("y"),!0):(Vt("zoomPan"),t.lastNow=Tt(),void t.panAnimLoop())},De=function(t,e){var i,n,r;if(it||(re=c),"swipe"===t){var l=ee.x-ie.x,d=e.lastFlickDist.x<10;l>30&&(d||e.lastFlickOffset.x>20)?n=-1:l<-30&&(d||e.lastFlickOffset.x<-20)&&(n=1)}n&&((c+=n)<0?(c=a.loop?je()-1:0,r=!0):c>=je()&&(c=a.loop?0:je()-1,r=!0),r&&!a.loop||(gt+=n,pt-=n,i=!0));var h,u=mt.x*pt,p=Math.abs(u-ce.x);return i||u>ce.x==e.lastFlickSpeed.x>0?(h=Math.abs(e.lastFlickSpeed.x)>0?p/Math.abs(e.lastFlickSpeed.x):333,h=Math.min(h,400),h=Math.max(h,250)):h=333,re===c&&(i=!1),it=!0,kt("mainScrollAnimStart"),Zt("mainScroll",ce.x,u,h,o.easing.cubic.out,Pt,(function(){Xt(),it=!1,re=-1,(i||re!==c)&&s.updateCurrItem(),kt("mainScrollAnimComplete")})),i&&s.updateCurrItem(!0),i},Ae=function(t){return 1/J*t*v},Me=function(){var t=g,e=Rt(),i=Wt();g<e?t=e:g>i&&(t=i);var n,a=at;return st&&!$&&!rt&&g<e?(s.close(),!0):(st&&(n=function(t){It((1-a)*t+a)}),s.zoomTo(t,0,200,o.easing.cubic.out,n),!0)};Ct("Gestures",{publicMethods:{initGestures:function(){var t=function(t,e,i,n,o){S=t+e,k=t+i,T=t+n,I=o?t+o:""};(L=H.pointerEvent)&&H.touch&&(H.touch=!1),L?navigator.pointerEnabled?t("pointer","down","move","up","cancel"):t("MSPointer","Down","Move","Up","Cancel"):H.touch?(t("touch","start","move","end","cancel"),E=!0):t("mouse","down","move","up"),p=k+" "+T+" "+I,f=S,L&&!E&&(E=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),s.likelyTouchDevice=E,m[S]=Ie,m[k]=ze,m[T]=Ee,I&&(m[I]=m[T]),H.touch&&(f+=" mousedown",p+=" mousemove mouseup",m.mousedown=m[S],m.mousemove=m[k],m.mouseup=m[T]),E||(a.allowPanToNext=!1)}}});var Fe,He,Be,Re,We,je,$e=function(e,i,n,r){var l;Fe&&clearTimeout(Fe),Re=!0,Be=!0,e.initialLayout?(l=e.initialLayout,e.initialLayout=null):l=a.getThumbBoundsFn&&a.getThumbBoundsFn(c);var h=n?a.hideAnimationDuration:a.showAnimationDuration,u=function(){Ut("initialZoom"),n?(s.template.removeAttribute("style"),s.bg.removeAttribute("style")):(It(1),i&&(i.style.display="block"),o.addClass(t,"pswp--animated-in"),kt("initialZoom"+(n?"OutEnd":"InEnd"))),r&&r(),Re=!1};if(!h||!l||void 0===l.x)return kt("initialZoom"+(n?"Out":"In")),g=e.initialZoomLevel,At(ht,e.initialPosition),Lt(),t.style.opacity=n?0:1,It(1),void(h?setTimeout((function(){u()}),h):u());!function(){var i=d,r=!s.currItem.src||s.currItem.loadError||a.showHideOpacity;e.miniImg&&(e.miniImg.style.webkitBackfaceVisibility="hidden"),n||(g=l.w/e.w,ht.x=l.x,ht.y=l.y-A,s[r?"template":"bg"].style.opacity=.001,Lt()),Vt("initialZoom"),n&&!i&&o.removeClass(t,"pswp--animated-in"),r&&(n?o[(i?"remove":"add")+"Class"](t,"pswp--animate_opacity"):setTimeout((function(){o.addClass(t,"pswp--animate_opacity")}),30)),Fe=setTimeout((function(){if(kt("initialZoom"+(n?"Out":"In")),n){var s=l.w/e.w,a={x:ht.x,y:ht.y},d=g,c=at,p=function(e){1===e?(g=s,ht.x=l.x,ht.y=l.y-F):(g=(s-d)*e+d,ht.x=(l.x-a.x)*e+a.x,ht.y=(l.y-F-a.y)*e+a.y),Lt(),r?t.style.opacity=1-e:It(c-e*c)};i?Zt("initialZoom",0,1,h,o.easing.cubic.out,p,u):(p(1),Fe=setTimeout(u,h+20))}else g=e.initialZoomLevel,At(ht,e.initialPosition),Lt(),It(1),r?t.style.opacity=1:It(1),Fe=setTimeout(u,h+20)}),n?25:90)}()},Ne={},Ge=[],Ye={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return He.length}},qe=function(t,e,i){if(t.src&&!t.loadError){var n=!i;if(n&&(t.vGap||(t.vGap={top:0,bottom:0}),kt("parseVerticalMargin",t)),Ne.x=e.x,Ne.y=e.y-t.vGap.top-t.vGap.bottom,n){var o=Ne.x/t.w,s=Ne.y/t.h;t.fitRatio=o<s?o:s;var r=a.scaleMode;"orig"===r?i=1:"fit"===r&&(i=t.fitRatio),i>1&&(i=1),t.initialZoomLevel=i,t.bounds||(t.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}})}if(!i)return;return function(t,e,i){var n=t.bounds;n.center.x=Math.round((Ne.x-e)/2),n.center.y=Math.round((Ne.y-i)/2)+t.vGap.top,n.max.x=e>Ne.x?Math.round(Ne.x-e):n.center.x,n.max.y=i>Ne.y?Math.round(Ne.y-i)+t.vGap.top:n.center.y,n.min.x=e>Ne.x?0:n.center.x,n.min.y=i>Ne.y?t.vGap.top:n.center.y}(t,t.w*i,t.h*i),n&&i===t.initialZoomLevel&&(t.initialPosition=t.bounds.center),t.bounds}return t.w=t.h=0,t.initialZoomLevel=t.fitRatio=1,t.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}},t.initialPosition=t.bounds.center,t.bounds},Ue=function(t,e,i,n,o,a){e.loadError||n&&(e.imageAppended=!0,Ze(e,n,e===s.currItem&&bt),i.appendChild(n),a&&setTimeout((function(){e&&e.loaded&&e.placeholder&&(e.placeholder.style.display="none",e.placeholder=null)}),500))},Ve=function(t){t.loading=!0,t.loaded=!1;var e=t.img=o.createEl("pswp__img","img"),i=function(){t.loading=!1,t.loaded=!0,t.loadComplete?t.loadComplete(t):t.img=null,e.onload=e.onerror=null,e=null};return e.onload=i,e.onerror=function(){t.loadError=!0,i()},e.src=t.src,e},Xe=function(t,e){if(t.src&&t.loadError&&t.container)return e&&(t.container.innerHTML=""),t.container.innerHTML=a.errorMsg.replace("%url%",t.src),!0},Ze=function(t,e,i){if(t.src){e||(e=t.container.lastChild);var n=i?t.w:Math.round(t.w*t.fitRatio),o=i?t.h:Math.round(t.h*t.fitRatio);t.placeholder&&!t.loaded&&(t.placeholder.style.width=n+"px",t.placeholder.style.height=o+"px"),e.style.width=n+"px",e.style.height=o+"px"}},Qe=function(){if(Ge.length){for(var t,e=0;e<Ge.length;e++)(t=Ge[e]).holder.index===t.index&&Ue(t.index,t.item,t.baseDiv,t.img,0,t.clearPlaceholder);Ge=[]}};Ct("Controller",{publicMethods:{lazyLoadItem:function(t){t=xt(t);var e=We(t);e&&(!e.loaded&&!e.loading||C)&&(kt("gettingData",t,e),e.src&&Ve(e))},initController:function(){o.extend(a,Ye,!0),s.items=He=i,We=s.getItemAt,je=a.getNumItemsFn,a.loop,je()<3&&(a.loop=!1),St("beforeChange",(function(t){var e,i=a.preload,n=null===t||t>=0,o=Math.min(i[0],je()),r=Math.min(i[1],je());for(e=1;e<=(n?r:o);e++)s.lazyLoadItem(c+e);for(e=1;e<=(n?o:r);e++)s.lazyLoadItem(c-e)})),St("initialLayout",(function(){s.currItem.initialLayout=a.getThumbBoundsFn&&a.getThumbBoundsFn(c)})),St("mainScrollAnimComplete",Qe),St("initialZoomInEnd",Qe),St("destroy",(function(){for(var t,e=0;e<He.length;e++)(t=He[e]).container&&(t.container=null),t.placeholder&&(t.placeholder=null),t.img&&(t.img=null),t.preloader&&(t.preloader=null),t.loadError&&(t.loaded=t.loadError=!1);Ge=null}))},getItemAt:function(t){return t>=0&&void 0!==He[t]&&He[t]},allowProgressiveImg:function(){return a.forceProgressiveLoading||!E||a.mouseUsed||screen.width>1200},setContent:function(t,e){a.loop&&(e=xt(e));var i=s.getItemAt(t.index);i&&(i.container=null);var n,l=s.getItemAt(e);if(l){kt("gettingData",e,l),t.index=e,t.item=l;var d=l.container=o.createEl("pswp__zoom-wrap");if(!l.src&&l.html&&(l.html.tagName?d.appendChild(l.html):d.innerHTML=l.html),Xe(l),qe(l,ut),!l.src||l.loadError||l.loaded)l.src&&!l.loadError&&((n=o.createEl("pswp__img","img")).style.opacity=1,n.src=l.src,Ze(l,n),Ue(0,l,d,n));else{if(l.loadComplete=function(i){if(r){if(t&&t.index===e){if(Xe(i,!0))return i.loadComplete=i.img=null,qe(i,ut),Et(i),void(t.index===c&&s.updateCurrZoomItem());i.imageAppended?!Re&&i.placeholder&&(i.placeholder.style.display="none",i.placeholder=null):H.transform&&(it||Re)?Ge.push({item:i,baseDiv:d,img:i.img,index:e,holder:t,clearPlaceholder:!0}):Ue(0,i,d,i.img,0,!0)}i.loadComplete=null,i.img=null,kt("imageLoadComplete",e,i)}},o.features.transform){var h="pswp__img pswp__img--placeholder";h+=l.msrc?"":" pswp__img--placeholder--blank";var u=o.createEl(h,l.msrc?"img":"");l.msrc&&(u.src=l.msrc),Ze(l,u),d.appendChild(u),l.placeholder=u}l.loading||Ve(l),s.allowProgressiveImg()&&(!Be&&H.transform?Ge.push({item:l,baseDiv:d,img:l.img,index:e,holder:t}):Ue(0,l,d,l.img,0,!0))}Be||e!==c?Et(l):(et=d.style,$e(l,n||l.img)),t.el.innerHTML="",t.el.appendChild(d)}else t.el.innerHTML=""},cleanSlide:function(t){t.img&&(t.img.onload=t.img.onerror=null),t.loaded=t.loading=t.img=t.imageAppended=!1}}});var Ke,Je,ti={},ei=function(t,e,i){var n=document.createEvent("CustomEvent"),o={origEvent:t,target:t.target,releasePoint:e,pointerType:i||"touch"};n.initCustomEvent("pswpTap",!0,!0,o),t.target.dispatchEvent(n)};Ct("Tap",{publicMethods:{initTap:function(){St("firstTouchStart",s.onTapStart),St("touchRelease",s.onTapRelease),St("destroy",(function(){ti={},Ke=null}))},onTapStart:function(t){t.length>1&&(clearTimeout(Ke),Ke=null)},onTapRelease:function(t,e){if(e&&!V&&!q&&!qt){var i=e;if(Ke&&(clearTimeout(Ke),Ke=null,function(t,e){return Math.abs(t.x-e.x)<25&&Math.abs(t.y-e.y)<25}(i,ti)))return void kt("doubleTap",i);if("mouse"===e.type)return void ei(t,e,"mouse");if("BUTTON"===t.target.tagName.toUpperCase()||o.hasClass(t.target,"pswp__single-tap"))return void ei(t,e);At(ti,i),Ke=setTimeout((function(){ei(t,e),Ke=null}),300)}}}}),Ct("DesktopZoom",{publicMethods:{initDesktopZoom:function(){M||(E?St("mouseUsed",(function(){s.setupDesktopZoom()})):s.setupDesktopZoom(!0))},setupDesktopZoom:function(e){Je={};var i="wheel mousewheel DOMMouseScroll";St("bindEvents",(function(){o.bind(t,i,s.handleMouseWheel)})),St("unbindEvents",(function(){Je&&o.unbind(t,i,s.handleMouseWheel)})),s.mouseZoomedIn=!1;var n,a=function(){s.mouseZoomedIn&&(o.removeClass(t,"pswp--zoomed-in"),s.mouseZoomedIn=!1),g<1?o.addClass(t,"pswp--zoom-allowed"):o.removeClass(t,"pswp--zoom-allowed"),r()},r=function(){n&&(o.removeClass(t,"pswp--dragging"),n=!1)};St("resize",a),St("afterChange",a),St("pointerDown",(function(){s.mouseZoomedIn&&(n=!0,o.addClass(t,"pswp--dragging"))})),St("pointerUp",r),e||a()},handleMouseWheel:function(t){if(g<=s.currItem.fitRatio)return a.modal&&(!a.closeOnScroll||qt||Y?t.preventDefault():z&&Math.abs(t.deltaY)>2&&(d=!0,s.close())),!0;if(t.stopPropagation(),Je.x=0,"deltaX"in t)1===t.deltaMode?(Je.x=18*t.deltaX,Je.y=18*t.deltaY):(Je.x=t.deltaX,Je.y=t.deltaY);else if("wheelDelta"in t)t.wheelDeltaX&&(Je.x=-.16*t.wheelDeltaX),t.wheelDeltaY?Je.y=-.16*t.wheelDeltaY:Je.y=-.16*t.wheelDelta;else{if(!("detail"in t))return;Je.y=t.detail}Bt(g,!0);var e=ht.x-Je.x,i=ht.y-Je.y;(a.modal||e<=tt.min.x&&e>=tt.max.x&&i<=tt.min.y&&i>=tt.max.y)&&t.preventDefault(),s.panTo(e,i)},toggleDesktopZoom:function(e){e=e||{x:ut.x/2+ft.x,y:ut.y/2+ft.y};var i=a.getDoubleTapZoom(!0,s.currItem),n=g===i;s.mouseZoomedIn=!n,s.zoomTo(n?s.currItem.initialZoomLevel:i,e,333),o[(n?"remove":"add")+"Class"](t,"pswp--zoomed-in")}}});var ii,ni,oi,si,ai,ri,li,di,ci,hi,ui,pi,fi={history:!0,galleryUID:1},mi=function(){return ui.hash.substring(1)},gi=function(){ii&&clearTimeout(ii),oi&&clearTimeout(oi)},vi=function(){var t=mi(),e={};if(t.length<5)return e;var i,n=t.split("&");for(i=0;i<n.length;i++)if(n[i]){var o=n[i].split("=");o.length<2||(e[o[0]]=o[1])}if(a.galleryPIDs){var s=e.pid;for(e.pid=0,i=0;i<He.length;i++)if(He[i].pid===s){e.pid=i;break}}else e.pid=parseInt(e.pid,10)-1;return e.pid<0&&(e.pid=0),e},wi=function(){if(oi&&clearTimeout(oi),qt||Y)oi=setTimeout(wi,500);else{si?clearTimeout(ni):si=!0;var t=c+1,e=We(c);e.hasOwnProperty("pid")&&(t=e.pid);var i=li+"&gid="+a.galleryUID+"&pid="+t;di||-1===ui.hash.indexOf(i)&&(hi=!0);var n=ui.href.split("#")[0]+"#"+i;pi?"#"+i!==window.location.hash&&history[di?"replaceState":"pushState"]("",document.title,n):di?ui.replace(n):ui.hash=i,di=!0,ni=setTimeout((function(){si=!1}),60)}};Ct("History",{publicMethods:{initHistory:function(){if(o.extend(a,fi,!0),a.history){ui=window.location,hi=!1,ci=!1,di=!1,li=mi(),pi="pushState"in history,li.indexOf("gid=")>-1&&(li=(li=li.split("&gid=")[0]).split("?gid=")[0]),St("afterChange",s.updateURL),St("unbindEvents",(function(){o.unbind(window,"hashchange",s.onHashChange)}));var t=function(){ri=!0,ci||(hi?history.back():li?ui.hash=li:pi?history.pushState("",document.title,ui.pathname+ui.search):ui.hash=""),gi()};St("unbindEvents",(function(){d&&t()})),St("destroy",(function(){ri||t()})),St("firstUpdate",(function(){c=vi().pid}));var e=li.indexOf("pid=");e>-1&&("&"===(li=li.substring(0,e)).slice(-1)&&(li=li.slice(0,-1))),setTimeout((function(){r&&o.bind(window,"hashchange",s.onHashChange)}),40)}},onHashChange:function(){return mi()===li?(ci=!0,void s.close()):void(si||(ai=!0,s.goTo(vi().pid),ai=!1))},updateURL:function(){gi(),ai||(di?ii=setTimeout(wi,800):wi())}}}),o.extend(s,Qt)}})),function(t,e){"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():t.PhotoSwipeUI_Default=e()}(this,(function(){"use strict";return function(t,e){var i,n,o,s,a,r,l,d,c,h,u,p,f,m,g,v,w,y,b=this,C=!1,x=!0,_=!0,S={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(t,e){return t.title?(e.children[0].innerHTML=t.title,!0):(e.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return t.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return t.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},k=function(t){if(v)return!0;t=t||window.event,g.timeToIdle&&g.mouseUsed&&!c&&M();for(var i,n,o=(t.target||t.srcElement).getAttribute("class")||"",s=0;s<R.length;s++)(i=R[s]).onTap&&o.indexOf("pswp__"+i.name)>-1&&(i.onTap(),n=!0);if(n){t.stopPropagation&&t.stopPropagation(),v=!0;var a=e.features.isOldAndroid?600:30;setTimeout((function(){v=!1}),a)}},T=function(){return!t.likelyTouchDevice||g.mouseUsed||screen.width>g.fitControlsWidth},I=function(t,i,n){e[(n?"add":"remove")+"Class"](t,"pswp__"+i)},z=function(){var t=1===g.getNumItemsFn();t!==m&&(I(n,"ui--one-slide",t),m=t)},L=function(){I(l,"share-modal--hidden",_)},E=function(){return(_=!_)?(e.removeClass(l,"pswp__share-modal--fade-in"),setTimeout((function(){_&&L()}),300)):(L(),setTimeout((function(){_||e.addClass(l,"pswp__share-modal--fade-in")}),30)),_||P(),!1},O=function(e){var i=(e=e||window.event).target||e.srcElement;return t.shout("shareLinkClick",e,i),!(!i.href||!i.hasAttribute("download")&&(window.open(i.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),_||E(),1))},P=function(){for(var t,e,i,n,o="",s=0;s<g.shareButtons.length;s++)t=g.shareButtons[s],e=g.getImageURLForShare(t),i=g.getPageURLForShare(t),n=g.getTextForShare(t),o+='<a href="'+t.url.replace("{{url}}",encodeURIComponent(i)).replace("{{image_url}}",encodeURIComponent(e)).replace("{{raw_image_url}}",e).replace("{{text}}",encodeURIComponent(n))+'" target="_blank" class="pswp__share--'+t.id+'"'+(t.download?"download":"")+">"+t.label+"</a>",g.parseShareButtonOut&&(o=g.parseShareButtonOut(t,o));l.children[0].innerHTML=o,l.children[0].onclick=O},D=function(t){for(var i=0;i<g.closeElClasses.length;i++)if(e.hasClass(t,"pswp__"+g.closeElClasses[i]))return!0},A=0,M=function(){clearTimeout(y),A=0,c&&b.setIdle(!1)},F=function(t){var e=(t=t||window.event).relatedTarget||t.toElement;e&&"HTML"!==e.nodeName||(clearTimeout(y),y=setTimeout((function(){b.setIdle(!0)}),g.timeToIdleOutside))},H=function(t){p!==t&&(I(u,"preloader--active",!t),p=t)},B=function(t){var i=t.vGap;if(T()){var a=g.barsSize;if(g.captionEl&&"auto"===a.bottom)if(s||((s=e.createEl("pswp__caption pswp__caption--fake")).appendChild(e.createEl("pswp__caption__center")),n.insertBefore(s,o),e.addClass(n,"pswp__ui--fit")),g.addCaptionHTMLFn(t,s,!0)){var r=s.clientHeight;i.bottom=parseInt(r,10)||44}else i.bottom=a.top;else i.bottom="auto"===a.bottom?0:a.bottom;i.top=a.top}else i.top=i.bottom=0},R=[{name:"caption",option:"captionEl",onInit:function(t){o=t}},{name:"share-modal",option:"shareEl",onInit:function(t){l=t},onTap:function(){E()}},{name:"button--share",option:"shareEl",onInit:function(t){r=t},onTap:function(){E()}},{name:"button--zoom",option:"zoomEl",onTap:t.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(t){a=t}},{name:"button--close",option:"closeEl",onTap:t.close},{name:"button--arrow--left",option:"arrowEl",onTap:t.prev},{name:"button--arrow--right",option:"arrowEl",onTap:t.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){i.isFullscreen()?i.exit():i.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(t){u=t}}];b.init=function(){e.extend(t.options,S,!0),g=t.options,n=e.getChildByClass(t.scrollWrap,"pswp__ui"),h=t.listen,function(){var t;h("onVerticalDrag",(function(t){x&&t<.95?b.hideControls():!x&&t>=.95&&b.showControls()})),h("onPinchClose",(function(e){x&&e<.9?(b.hideControls(),t=!0):t&&!x&&e>.9&&b.showControls()})),h("zoomGestureEnded",(function(){(t=!1)&&!x&&b.showControls()}))}(),h("beforeChange",b.update),h("doubleTap",(function(e){var i=t.currItem.initialZoomLevel;t.getZoomLevel()!==i?t.zoomTo(i,e,333):t.zoomTo(g.getDoubleTapZoom(!1,t.currItem),e,333)})),h("preventDragEvent",(function(t,e,i){var n=t.target||t.srcElement;n&&n.getAttribute("class")&&t.type.indexOf("mouse")>-1&&(n.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(n.tagName))&&(i.prevent=!1)})),h("bindEvents",(function(){e.bind(n,"pswpTap click",k),e.bind(t.scrollWrap,"pswpTap",b.onGlobalTap),t.likelyTouchDevice||e.bind(t.scrollWrap,"mouseover",b.onMouseOver)})),h("unbindEvents",(function(){_||E(),w&&clearInterval(w),e.unbind(document,"mouseout",F),e.unbind(document,"mousemove",M),e.unbind(n,"pswpTap click",k),e.unbind(t.scrollWrap,"pswpTap",b.onGlobalTap),e.unbind(t.scrollWrap,"mouseover",b.onMouseOver),i&&(e.unbind(document,i.eventK,b.updateFullscreen),i.isFullscreen()&&(g.hideAnimationDuration=0,i.exit()),i=null)})),h("destroy",(function(){g.captionEl&&(s&&n.removeChild(s),e.removeClass(o,"pswp__caption--empty")),l&&(l.children[0].onclick=null),e.removeClass(n,"pswp__ui--over-close"),e.addClass(n,"pswp__ui--hidden"),b.setIdle(!1)})),g.showAnimationDuration||e.removeClass(n,"pswp__ui--hidden"),h("initialZoomIn",(function(){g.showAnimationDuration&&e.removeClass(n,"pswp__ui--hidden")})),h("initialZoomOut",(function(){e.addClass(n,"pswp__ui--hidden")})),h("parseVerticalMargin",B),function(){var t,i,o,s=function(n){if(n)for(var s=n.length,a=0;a<s;a++){t=n[a],i=t.className;for(var r=0;r<R.length;r++)o=R[r],i.indexOf("pswp__"+o.name)>-1&&(g[o.option]?(e.removeClass(t,"pswp__element--disabled"),o.onInit&&o.onInit(t)):e.addClass(t,"pswp__element--disabled"))}};s(n.children);var a=e.getChildByClass(n,"pswp__top-bar");a&&s(a.children)}(),g.shareEl&&r&&l&&(_=!0),z(),g.timeToIdle&&h("mouseUsed",(function(){e.bind(document,"mousemove",M),e.bind(document,"mouseout",F),w=setInterval((function(){2==++A&&b.setIdle(!0)}),g.timeToIdle/2)})),g.fullscreenEl&&!e.features.isOldAndroid&&(i||(i=b.getFullscreenAPI()),i?(e.bind(document,i.eventK,b.updateFullscreen),b.updateFullscreen(),e.addClass(t.template,"pswp--supports-fs")):e.removeClass(t.template,"pswp--supports-fs")),g.preloaderEl&&(H(!0),h("beforeChange",(function(){clearTimeout(f),f=setTimeout((function(){t.currItem&&t.currItem.loading?(!t.allowProgressiveImg()||t.currItem.img&&!t.currItem.img.naturalWidth)&&H(!1):H(!0)}),g.loadingIndicatorDelay)})),h("imageLoadComplete",(function(e,i){t.currItem===i&&H(!0)})))},b.setIdle=function(t){c=t,I(n,"ui--idle",t)},b.update=function(){x&&t.currItem?(b.updateIndexIndicator(),g.captionEl&&(g.addCaptionHTMLFn(t.currItem,o),I(o,"caption--empty",!t.currItem.title)),C=!0):C=!1,_||E(),z()},b.updateFullscreen=function(n){n&&setTimeout((function(){t.setScrollOffset(0,e.getScrollY())}),50),e[(i.isFullscreen()?"add":"remove")+"Class"](t.template,"pswp--fs")},b.updateIndexIndicator=function(){g.counterEl&&(a.innerHTML=t.getCurrentIndex()+1+g.indexIndicatorSep+g.getNumItemsFn())},b.onGlobalTap=function(i){var n=(i=i||window.event).target||i.srcElement;if(!v)if(i.detail&&"mouse"===i.detail.pointerType){if(D(n))return void t.close();e.hasClass(n,"pswp__img")&&(1===t.getZoomLevel()&&t.getZoomLevel()<=t.currItem.fitRatio?g.clickToCloseNonZoomable&&t.close():t.toggleDesktopZoom(i.detail.releasePoint))}else if(g.tapToToggleControls&&(x?b.hideControls():b.showControls()),g.tapToClose&&(e.hasClass(n,"pswp__img")||D(n)))return void t.close()},b.onMouseOver=function(t){var e=(t=t||window.event).target||t.srcElement;I(n,"ui--over-close",D(e))},b.hideControls=function(){e.addClass(n,"pswp__ui--hidden"),x=!1},b.showControls=function(){x=!0,C||b.update(),e.removeClass(n,"pswp__ui--hidden")},b.supportsFullscreen=function(){var t=document;return!!(t.exitFullscreen||t.mozCancelFullScreen||t.webkitExitFullscreen||t.msExitFullscreen)},b.getFullscreenAPI=function(){var e,i=document.documentElement,n="fullscreenchange";return i.requestFullscreen?e={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:n}:i.mozRequestFullScreen?e={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+n}:i.webkitRequestFullscreen?e={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+n}:i.msRequestFullscreen&&(e={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),e&&(e.enter=function(){return d=g.closeOnScroll,g.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?t.template[this.enterK]():void t.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},e.exit=function(){return g.closeOnScroll=d,document[this.exitK]()},e.isFullscreen=function(){return document[this.elementK]}),e}}})),
/*!-Before After*/
jQuery(document).ready((function(t){t(".twentytwenty-container").length>0&&(t.fn.twentytwenty=function(e){e=t.extend({default_offset_pct:.5,orientation:"horizontal",navigation_follow:!1},e);return this.each((function(){var i=e.default_offset_pct,n=t(this),o=e.orientation,s="vertical"===o?"down":"left",a="vertical"===o?"up":"right",r=e.navigation_follow;n.wrap("<div class='twentytwenty-wrapper twentytwenty-"+o+"'></div>");var l=n.find("img:first"),d=n.find("img:last"),c=l.attr("title"),h=d.attr("title");n.append("<div class='twentytwenty-handle'></div>");var u=n.find(".twentytwenty-handle");u.append("<span class='twentytwenty-"+s+"-arrow'></span>"),u.append("<span class='twentytwenty-"+a+"-arrow'></span>"),n.addClass("twentytwenty-container"),l.addClass("twentytwenty-before"),d.addClass("twentytwenty-after"),void 0!==c&&c&&c.length>0&&n.append("<div class='twentytwenty-before-label'>"+c+"</div>"),void 0!==h&&h&&h.length>0&&n.append("<div class='twentytwenty-after-label'>"+h+"</div>");var p=function(t){var e,i,s,a=(e=t,i=l.width(),s=l.height(),{w:i+"px",h:s+"px",cw:e*i+"px",ch:e*s+"px"});u.css("vertical"===o?"top":"left","vertical"===o?a.ch:a.cw),function(t){"vertical"===o?l.css("clip","rect(0,"+t.w+","+t.ch+",0)"):l.css("clip","rect(0,"+t.cw+","+t.h+",0)"),n.css("height",t.h)}(a)};t(window).on("resize.twentytwenty",(function(t){p(i)}));var f=0,m=0;u.on("movestart",(function(t){((t.distX>t.distY&&t.distX<-t.distY||t.distX<t.distY&&t.distX>-t.distY)&&"vertical"!==o||(t.distX<t.distY&&t.distX<-t.distY||t.distX>t.distY&&t.distX>-t.distY)&&"vertical"===o)&&t.preventDefault(),n.addClass("active"),n.removeClass("active-click"),f=n.offset().left,offsetY=n.offset().top,m=l.width(),imgHeight=l.height()})),u.on("moveend",(function(t){n.removeClass("active")})),u.on("move",(function(t){n.hasClass("active")&&((i="vertical"===o?(t.pageY-offsetY)/imgHeight:(t.pageX-f)/m)<0&&(i=0),i>1&&(i=1),p(i))})),r||n.hasClass("active")||(n.on("mouseup",(function(t){n.removeClass("active-click")})),n.on("mousedown",(function(t){n.addClass("active-click"),f=n.offset().left,offsetY=n.offset().top,m=l.width(),imgHeight=l.height(),(i="vertical"===o?(t.pageY-offsetY)/imgHeight:(t.pageX-f)/m)<0&&(i=0),i>1&&(i=1),p(i)}))),n.find("img").on("mousedown",(function(t){t.preventDefault()})),r&&(n.on("mouseenter",(function(t){n.addClass("active"),f=n.offset().left,offsetY=n.offset().top,m=l.width(),imgHeight=l.height()})),n.on("mouseleave",(function(t){n.removeClass("active")})),n.on("mousemove",(function(t){n.hasClass("active")&&((i="vertical"===o?(t.pageY-offsetY)/imgHeight:(t.pageX-f)/m)<0&&(i=0),i>1&&(i=1),p(i))}))),t(window).trigger("resize.twentytwenty")}))})})),jQuery(document).ready((function(t){!("ontouchstart"in window)&&("on"==dtLocal.themeSettings.smoothScroll||"on_parallax"==dtLocal.themeSettings.smoothScroll&&t(".stripe-parallax-bg").length>0)&&t("body").css({"scroll-behavior":"smooth"})})),jQuery(document).ready((function(t){var e=t(window),i=e.height();e.on("the7-resize-height",(function(){i=e.height()})),t.fn.parallax=function(n,o,s){var a,r,l=t(this);function d(){var e=dtGlobals.winScrollTop;l.each((function(){var s=t(this),r=s.offset().top;r+a(s)<e||r>e+i||l.css("backgroundPosition",n+" "+Math.round((r-e)*o)+"px")}))}l.each((function(){l.offset().top})),a=s?function(t){return t.outerHeight(!0)}:function(t){return t.height()},(arguments.length<1||null===n)&&(n="50%"),(arguments.length<2||null===o)&&(o=.1),(arguments.length<3||null===s)&&(s=!0),e.bind("scroll",d).on("resize",(function(){d()})).bind("debouncedresize",(function(){clearTimeout(r),r=setTimeout((function(){d()}),20)})),d()}})),jQuery(document).ready((function(t){t.fn.extend({customSelect:function(e){if(void 0===document.body.style.maxHeight)return this;var i=(e=t.extend({customClass:"customSelect",mapClass:!0,mapStyle:!0},e)).customClass,n=function(e,i){var n=e.find(":selected"),s=i.children(":first"),a=n.html()||"&nbsp;";s.html(a),n.attr("disabled")?i.addClass(o("DisabledOption")):i.removeClass(o("DisabledOption")),setTimeout((function(){i.removeClass(o("Open")),t(document).off("mouseup."+o("Open"))}),60)},o=function(t){return i+t};return this.each((function(){var s=t(this),a=t("<span />").addClass(o("Inner")),r=t("<span />");s.after(r.append(a)),r.addClass(i),e.mapClass&&r.addClass(s.attr("class")),e.mapStyle&&r.attr("style",s.attr("style")),s.addClass("hasCustomSelect").on("update",(function(){n(s,r);var t=parseInt(s.outerWidth(),10)-(parseInt(r.outerWidth(),10)-parseInt(r.width(),10));r.css({display:"inline-block"});var e=r.outerHeight();s.attr("disabled")?r.addClass(o("Disabled")):r.removeClass(o("Disabled")),a.css({width:t,display:"inline-block"}),s.css({"-webkit-appearance":"menulist-button",width:r.outerWidth(),position:"absolute",opacity:0,height:e,fontSize:r.css("font-size")})})).on("change",(function(){r.addClass(o("Changed")),n(s,r)})).on("keyup",(function(t){r.hasClass(o("Open"))?13!=t.which&&27!=t.which||n(s,r):(s.blur(),s.focus())})).on("mousedown",(function(t){r.removeClass(o("Changed"))})).on("mouseup",(function(e){r.hasClass(o("Open"))||(t("."+o("Open")).not(r).length>0&&"undefined"!=typeof InstallTrigger?s.focus():(r.addClass(o("Open")),e.stopPropagation(),t(document).one("mouseup."+o("Open"),(function(e){e.target!=s.get(0)&&t.inArray(e.target,s.find("*").get())<0?s.blur():n(s,r)}))))})).focus((function(){r.removeClass(o("Changed")).addClass(o("Focus"))})).blur((function(){r.removeClass(o("Focus")+" "+o("Open"))})).hover((function(){r.addClass(o("Hover"))}),(function(){r.removeClass(o("Hover"))})).trigger("update")}))}})})),
/*!
 * Isotope PACKAGED v3.0.0
 *
 * Licensed GPLv3 for open source use
 * or Isotope Commercial License for commercial use
 *
 * http://isotope.metafizzy.co
 * Copyright 2016 Metafizzy
 */
function(t,e){"use strict";"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],(function(i){e(t,i)})):"object"==typeof module&&module.exports?module.exports=e(t,require("jquery")):t.jQueryBridget=e(t,t.jQuery)}(window,(function(t,e){"use strict";var i=Array.prototype.slice,n=t.console,o=void 0===n?function(){}:function(t){n.error(t)};function s(n,s,r){(r=r||e||t.jQuery)&&(s.prototype.option||(s.prototype.option=function(t){r.isPlainObject(t)&&(this.options=r.extend(!0,this.options,t))}),r.fn[n]=function(t){var e;return"string"==typeof t?function(t,e,i){var s,a="$()."+n+'("'+e+'")';return t.each((function(t,l){var d=r.data(l,n);if(d){var c=d[e];if(c&&"_"!=e.charAt(0)){var h=c.apply(d,i);s=void 0===s?h:s}else o(a+" is not a valid method")}else o(n+" not initialized. Cannot call methods, i.e. "+a)})),void 0!==s?s:t}(this,t,i.call(arguments,1)):(e=t,this.each((function(t,i){var o=r.data(i,n);o?(o.option(e),o._init()):(o=new s(i,e),r.data(i,n,o))})),this)},a(r))}function a(t){!t||t&&t.bridget||(t.bridget=s)}return a(e||t.jQuery),s})),function(t,e){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}(this,(function(){function t(){}var e=t.prototype;return e.on=function(t,e){if(t&&e){var i=this._events=this._events||{},n=i[t]=i[t]||[];return-1==n.indexOf(e)&&n.push(e),this}},e.once=function(t,e){if(t&&e){this.on(t,e);var i=this._onceEvents=this._onceEvents||{};return(i[t]=i[t]||{})[e]=!0,this}},e.off=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){var n=i.indexOf(e);return-1!=n&&i.splice(n,1),this}},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){var n=0,o=i[n];e=e||[];for(var s=this._onceEvents&&this._onceEvents[t];o;){var a=s&&s[o];a&&(this.off(t,o),delete s[o]),o.apply(this,e),o=i[n+=a?0:1]}return this}},t})),
/*!
 * getSize v2.0.2
 * measure size of elements
 * MIT license
 */
function(t,e){"use strict";"function"==typeof define&&define.amd?define("get-size/get-size",[],(function(){return e()})):"object"==typeof module&&module.exports?module.exports=e():t.getSize=e()}(window,(function(){"use strict";function t(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}var e="undefined"==typeof console?function(){}:function(t){console.error(t)},i=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],n=i.length;function o(t){var i=getComputedStyle(t);return i||e("Style returned "+i+". Are you running this code in a hidden iframe on Firefox? See http://bit.ly/getsizebug1"),i}var s,a=!1;function r(e){if(function(){if(!a){a=!0;var e=document.createElement("div");e.style.width="200px",e.style.padding="1px 2px 3px 4px",e.style.borderStyle="solid",e.style.borderWidth="1px 2px 3px 4px",e.style.boxSizing="border-box";var i=document.body||document.documentElement;i.appendChild(e);var n=o(e);r.isBoxSizeOuter=s=200==t(n.width),i.removeChild(e)}}(),"string"==typeof e&&(e=document.querySelector(e)),e&&"object"==typeof e&&e.nodeType){var l=o(e);if("none"==l.display)return function(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0;e<n;e++)t[i[e]]=0;return t}();var d={};d.width=e.offsetWidth,d.height=e.offsetHeight;for(var c=d.isBorderBox="border-box"==l.boxSizing,h=0;h<n;h++){var u=i[h],p=l[u],f=parseFloat(p);d[u]=isNaN(f)?0:f}var m=d.paddingLeft+d.paddingRight,g=d.paddingTop+d.paddingBottom,v=d.marginLeft+d.marginRight,w=d.marginTop+d.marginBottom,y=d.borderLeftWidth+d.borderRightWidth,b=d.borderTopWidth+d.borderBottomWidth,C=c&&s,x=t(l.width);!1!==x&&(d.width=x+(C?0:m+y));var _=t(l.height);return!1!==_&&(d.height=_+(C?0:g+b)),d.innerWidth=d.width-(m+y),d.innerHeight=d.height-(g+b),d.outerWidth=d.width+v,d.outerHeight=d.height+w,d}}return r})),function(t,e){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",e):"object"==typeof module&&module.exports?module.exports=e():t.matchesSelector=e()}(window,(function(){"use strict";var t=function(){var t=Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var n=e[i]+"MatchesSelector";if(t[n])return n}}();return function(e,i){return e[t](i)}})),function(t,e){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],(function(i){return e(t,i)})):"object"==typeof module&&module.exports?module.exports=e(t,require("desandro-matches-selector")):t.fizzyUIUtils=e(t,t.matchesSelector)}(window,(function(t,e){var i={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e},makeArray:function(t){var e=[];if(Array.isArray(t))e=t;else if(t&&"number"==typeof t.length)for(var i=0;i<t.length;i++)e.push(t[i]);else e.push(t);return e},removeFrom:function(t,e){var i=t.indexOf(e);-1!=i&&t.splice(i,1)},getParent:function(t,i){for(;t!=document.body;)if(t=t.parentNode,e(t,i))return t},getQueryElement:function(t){return"string"==typeof t?document.querySelector(t):t},handleEvent:function(t){var e="on"+t.type;this[e]&&this[e](t)}};i.filterFindElements=function(t,n){t=i.makeArray(t);var o=[];return t.forEach((function(t){if(function(t){return"object"==typeof HTMLElement?t instanceof HTMLElement:t&&"object"==typeof t&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName}(t))if(n){e(t,n)&&o.push(t);for(var i=t.querySelectorAll(n),s=0;s<i.length;s++)o.push(i[s])}else o.push(t)})),o},i.debounceMethod=function(t,e,i){var n=t.prototype[e],o=e+"Timeout";t.prototype[e]=function(){var t=this[o];t&&clearTimeout(t);var e=arguments,s=this;this[o]=setTimeout((function(){n.apply(s,e),delete s[o]}),i||100)}},i.docReady=function(t){"complete"==document.readyState?t():document.addEventListener("DOMContentLoaded",t)},i.toDashed=function(t){return t.replace(/(.)([A-Z])/g,(function(t,e,i){return e+"-"+i})).toLowerCase()};var n=t.console;return i.htmlInit=function(e,o){i.docReady((function(){var s=i.toDashed(o),a="data-"+s,r=document.querySelectorAll("["+a+"]"),l=document.querySelectorAll(".js-"+s),d=i.makeArray(r).concat(i.makeArray(l)),c=a+"-options",h=t.jQuery;d.forEach((function(t){var i,s=t.getAttribute(a)||t.getAttribute(c);try{i=s&&JSON.parse(s)}catch(e){return void(n&&n.error("Error parsing "+a+" on "+t.className+": "+e))}var r=new e(t,i);h&&h.data(t,o,r)}))}))},i})),function(t,e){"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("ev-emitter"),require("get-size")):(t.Outlayer={},t.Outlayer.Item=e(t.EvEmitter,t.getSize))}(window,(function(t,e){"use strict";var i=document.documentElement.style,n="string"==typeof i.transition?"transition":"WebkitTransition",o="string"==typeof i.transform?"transform":"WebkitTransform",s={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[n],a={transform:o,transition:n,transitionDuration:n+"Duration",transitionProperty:n+"Property",transitionDelay:n+"Delay"};function r(t,e){t&&(this.element=t,this.layout=e,this.position={x:0,y:0},this._create())}var l=r.prototype=Object.create(t.prototype);l.constructor=r,l._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},l.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},l.getSize=function(){this.size=e(this.element)},l.css=function(t){var e=this.element.style;for(var i in t){e[a[i]||i]=t[i]}},l.getPosition=function(){var t=getComputedStyle(this.element),e=this.layout._getOption("originLeft"),i=this.layout._getOption("originTop"),n=t[e?"left":"right"],o=t[i?"top":"bottom"],s=this.layout.size,a=-1!=n.indexOf("%")?parseFloat(n)/100*s.width:parseInt(n,10),r=-1!=o.indexOf("%")?parseFloat(o)/100*s.height:parseInt(o,10);a=isNaN(a)?0:a,r=isNaN(r)?0:r,a-=e?s.paddingLeft:s.paddingRight,r-=i?s.paddingTop:s.paddingBottom,this.position.x=a,this.position.y=r},l.layoutPosition=function(){var t=this.layout.size,e={},i=this.layout._getOption("originLeft"),n=this.layout._getOption("originTop"),o=i?"paddingLeft":"paddingRight",s=i?"left":"right",a=i?"right":"left",r=this.position.x+t[o];e[s]=this.getXValue(r),e[a]="";var l=n?"paddingTop":"paddingBottom",d=n?"top":"bottom",c=n?"bottom":"top",h=this.position.y+t[l];e[d]=this.getYValue(h),e[c]="",this.css(e),this.emitEvent("layout",[this])},l.getXValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!e?t/this.layout.size.width*100+"%":t+"px"},l.getYValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&e?t/this.layout.size.height*100+"%":t+"px"},l._transitionTo=function(t,e){this.getPosition();var i=this.position.x,n=this.position.y,o=parseInt(t,10),s=parseInt(e,10),a=o===this.position.x&&s===this.position.y;if(this.setPosition(t,e),!a||this.isTransitioning){var r=t-i,l=e-n,d={};d.transform=this.getTranslate(r,l),this.transition({to:d,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})}else this.layoutPosition()},l.getTranslate=function(t,e){return"translate3d("+(t=this.layout._getOption("originLeft")?t:-t)+"px, "+(e=this.layout._getOption("originTop")?e:-e)+"px, 0)"},l.goTo=function(t,e){this.setPosition(t,e),this.layoutPosition()},l.moveTo=l._transitionTo,l.setPosition=function(t,e){this.position.x=parseInt(t,10),this.position.y=parseInt(e,10)},l._nonTransition=function(t){for(var e in this.css(t.to),t.isCleaning&&this._removeStyles(t.to),t.onTransitionEnd)t.onTransitionEnd[e].call(this)},l.transition=function(t){if(parseFloat(this.layout.options.transitionDuration)){var e=this._transn;for(var i in t.onTransitionEnd)e.onEnd[i]=t.onTransitionEnd[i];for(i in t.to)e.ingProperties[i]=!0,t.isCleaning&&(e.clean[i]=!0);if(t.from){this.css(t.from);this.element.offsetHeight;null}this.enableTransition(t.to),this.css(t.to),this.isTransitioning=!0}else this._nonTransition(t)};var d="opacity,"+o.replace(/([A-Z])/g,(function(t){return"-"+t.toLowerCase()}));l.enableTransition=function(){if(!this.isTransitioning){var t=this.layout.options.transitionDuration;t="number"==typeof t?t+"ms":t,this.css({transitionProperty:d,transitionDuration:t,transitionDelay:this.staggerDelay||0}),this.element.addEventListener(s,this,!1)}},l.onwebkitTransitionEnd=function(t){this.ontransitionend(t)},l.onotransitionend=function(t){this.ontransitionend(t)};var c={"-webkit-transform":"transform"};l.ontransitionend=function(t){if(t.target===this.element){var e=this._transn,i=c[t.propertyName]||t.propertyName;if(delete e.ingProperties[i],function(t){for(var e in t)return!1;return!0}(e.ingProperties)&&this.disableTransition(),i in e.clean&&(this.element.style[t.propertyName]="",delete e.clean[i]),i in e.onEnd)e.onEnd[i].call(this),delete e.onEnd[i];this.emitEvent("transitionEnd",[this])}},l.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(s,this,!1),this.isTransitioning=!1},l._removeStyles=function(t){var e={};for(var i in t)e[i]="";this.css(e)};var h={transitionProperty:"",transitionDuration:"",transitionDelay:""};return l.removeTransitionStyles=function(){this.css(h)},l.stagger=function(t){t=isNaN(t)?0:t,this.staggerDelay=t+"ms"},l.removeElem=function(){this.element.parentNode.removeChild(this.element),this.css({display:""}),this.emitEvent("remove",[this])},l.remove=function(){n&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",(function(){this.removeElem()})),this.hide()):this.removeElem()},l.reveal=function(){delete this.isHidden,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("visibleStyle")]=this.onRevealTransitionEnd,this.transition({from:t.hiddenStyle,to:t.visibleStyle,isCleaning:!0,onTransitionEnd:e})},l.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},l.getHideRevealTransitionEndProperty=function(t){var e=this.layout.options[t];if(e.opacity)return"opacity";for(var i in e)return i},l.hide=function(){this.isHidden=!0,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("hiddenStyle")]=this.onHideTransitionEnd,this.transition({from:t.visibleStyle,to:t.hiddenStyle,isCleaning:!0,onTransitionEnd:e})},l.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},l.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},r})),
/*!
 * Outlayer v2.1.0
 * the brains and guts of a layout library
 * MIT license
 */
function(t,e){"use strict";"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],(function(i,n,o,s){return e(t,i,n,o,s)})):"object"==typeof module&&module.exports?module.exports=e(t,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):t.Outlayer=e(t,t.EvEmitter,t.getSize,t.fizzyUIUtils,t.Outlayer.Item)}(window,(function(t,e,i,n,o){"use strict";var s=t.console,a=t.jQuery,r=function(){},l=0,d={};function c(t,e){var i=n.getQueryElement(t);if(i){this.element=i,a&&(this.$element=a(this.element)),this.options=n.extend({},this.constructor.defaults),this.option(e);var o=++l;this.element.outlayerGUID=o,d[o]=this,this._create(),this._getOption("initLayout")&&this.layout()}else s&&s.error("Bad element for "+this.constructor.namespace+": "+(i||t))}c.namespace="outlayer",c.Item=o,c.defaults={customSorters:{},containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,equalheight:!1,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}};var h=c.prototype;function u(t){function e(){t.apply(this,arguments)}return e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e}n.extend(h,e.prototype),h.option=function(t){n.extend(this.options,t)},h._getOption=function(t){var e=this.constructor.compatOptions[t];return e&&void 0!==this.options[e]?this.options[e]:this.options[t]},c.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},h._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),n.extend(this.element.style,this.options.containerStyle),this._getOption("resize")&&this.bindResize()},h.reloadItems=function(){this.items=this._itemize(this.element.children)},h._itemize=function(t){for(var e=this._filterFindItemElements(t),i=this.constructor.Item,n=[],o=0;o<e.length;o++){var s=new i(e[o],this);n.push(s)}return n},h._filterFindItemElements=function(t){return n.filterFindElements(t,this.options.itemSelector)},h.getItemElements=function(){return this.items.map((function(t){return t.element}))},h.layout=function(){this._resetLayout(),this._manageStamps();var t=this._getOption("layoutInstant"),e=void 0!==t?t:!this._isLayoutInited;this.layoutItems(this.items,e),this._isLayoutInited=!0},h._init=h.layout,h._resetLayout=function(){this.getSize()},h.getSize=function(){this.size=i(this.element)},h._getMeasurement=function(t,e){var n,o=this.options[t];o?("string"==typeof o?n=this.element.querySelector(o):o instanceof HTMLElement&&(n=o),this[t]=n?i(n)[e]:o):this[t]=0},h.layoutItems=function(t,e){t=this._getItemsForLayout(t),this._layoutItems(t,e),this._postLayout()},h._getItemsForLayout=function(t){return t.filter((function(t){return!t.isIgnored}))},h._layoutItems=function(t,e){if(this._emitCompleteOnItems("layout",t),t&&t.length){var i=[];t.forEach((function(t){var n=this._getItemLayoutPosition(t);n.item=t,n.isInstant=e||t.isLayoutInstant,i.push(n)}),this),this._processLayoutQueue(i)}},h._getItemLayoutPosition=function(){return{x:0,y:0}},h._processLayoutQueue=function(t){this.updateStagger(),t.forEach((function(t,e){this._positionItem(t.item,t.x,t.y,t.isInstant,e)}),this)},h.updateStagger=function(){var t=this.options.stagger;if(null!=t)return this.stagger=function(t){if("number"==typeof t)return t;var e=t.match(/(^\d*\.?\d*)(\w*)/),i=e&&e[1],n=e&&e[2];if(!i.length)return 0;i=parseFloat(i);var o=p[n]||1;return i*o}(t),this.stagger;this.stagger=0},h._positionItem=function(t,e,i,n,o){n?t.goTo(e,i):(t.stagger(o*this.stagger),t.moveTo(e,i))},h._postLayout=function(){this.resizeContainer()},h.resizeContainer=function(){if(this._getOption("resizeContainer")){var t=this._getContainerSize();t&&(this._setContainerMeasure(t.width,!0),this._setContainerMeasure(t.height,!1))}},h._getContainerSize=r,h._setContainerMeasure=function(t,e){if(void 0!==t){var i=this.size;i.isBorderBox&&(t+=e?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth),t=Math.max(t,0),this.element.style[e?"width":"height"]=t+"px"}},h._emitCompleteOnItems=function(t,e){var i=this;function n(){i.dispatchEvent(t+"Complete",null,[e])}var o=e.length;if(e&&o){var s=0;e.forEach((function(e){e.once(t,a)}))}else n();function a(){++s==o&&n()}},h.dispatchEvent=function(t,e,i){var n=e?[e].concat(i):i;if(this.emitEvent(t,n),a)if(this.$element=this.$element||a(this.element),e){var o=a.Event(e);o.type=t,this.$element.trigger(o,i)}else this.$element.trigger(t,i)},h.ignore=function(t){var e=this.getItem(t);e&&(e.isIgnored=!0)},h.unignore=function(t){var e=this.getItem(t);e&&delete e.isIgnored},h.stamp=function(t){(t=this._find(t))&&(this.stamps=this.stamps.concat(t),t.forEach(this.ignore,this))},h.unstamp=function(t){(t=this._find(t))&&t.forEach((function(t){n.removeFrom(this.stamps,t),this.unignore(t)}),this)},h._find=function(t){if(t)return"string"==typeof t&&(t=this.element.querySelectorAll(t)),t=n.makeArray(t)},h._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},h._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),e=this.size;this._boundingRect={left:t.left+e.paddingLeft+e.borderLeftWidth,top:t.top+e.paddingTop+e.borderTopWidth,right:t.right-(e.paddingRight+e.borderRightWidth),bottom:t.bottom-(e.paddingBottom+e.borderBottomWidth)}},h._manageStamp=r,h._getElementOffset=function(t){var e=t.getBoundingClientRect(),n=this._boundingRect,o=i(t);return{left:e.left-n.left-o.marginLeft,top:e.top-n.top-o.marginTop,right:n.right-e.right-o.marginRight,bottom:n.bottom-e.bottom-o.marginBottom}},h.handleEvent=n.handleEvent,h.bindResize=function(){t.addEventListener("resize",this),this.isResizeBound=!0},h.unbindResize=function(){t.removeEventListener("resize",this),this.isResizeBound=!1},h.onresize=function(){this.resize()},n.debounceMethod(c,"onresize",100),h.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},h.needsResizeLayout=function(){var t=i(this.element);return this.size&&t&&t.innerWidth!==this.size.innerWidth},h.addItems=function(t){var e=this._itemize(t);return e.length&&(this.items=this.items.concat(e)),e},h.appended=function(t){var e=this.addItems(t);e.length&&(this.layoutItems(e,!0),this.reveal(e))},h.prepended=function(t){var e=this._itemize(t);if(e.length){var i=this.items.slice(0);this.items=e.concat(i),this._resetLayout(),this._manageStamps(),this.layoutItems(e,!0),this.reveal(e),this.layoutItems(i)}},h.reveal=function(t){if(this._emitCompleteOnItems("reveal",t),t&&t.length){var e=this.updateStagger();t.forEach((function(t,i){t.stagger(i*e),t.reveal()}))}},h.hide=function(t){if(this._emitCompleteOnItems("hide",t),t&&t.length){var e=this.updateStagger();t.forEach((function(t,i){t.stagger(i*e),t.hide()}))}},h.revealItemElements=function(t){var e=this.getItems(t);this.reveal(e)},h.hideItemElements=function(t){var e=this.getItems(t);this.hide(e)},h.getItem=function(t){for(var e=0;e<this.items.length;e++){var i=this.items[e];if(i.element==t)return i}},h.getItems=function(t){t=n.makeArray(t);var e=[];return t.forEach((function(t){var i=this.getItem(t);i&&e.push(i)}),this),e},h.remove=function(t){var e=this.getItems(t);this._emitCompleteOnItems("remove",e),e&&e.length&&e.forEach((function(t){t.remove(),n.removeFrom(this.items,t)}),this)},h.destroy=function(){var t=this.element.style;t.height="",t.position="",t.width="",this.items.forEach((function(t){t.destroy()})),this.unbindResize();var e=this.element.outlayerGUID;delete d[e],delete this.element.outlayerGUID,a&&a.removeData(this.element,this.constructor.namespace)},c.data=function(t){var e=(t=n.getQueryElement(t))&&t.outlayerGUID;return e&&d[e]},c.create=function(t,e){var i=u(c);return i.defaults=n.extend({},c.defaults),n.extend(i.defaults,e),i.compatOptions=n.extend({},c.compatOptions),i.namespace=t,i.data=c.data,i.Item=u(o),n.htmlInit(i,t),a&&a.bridget&&a.bridget(t,i),i};var p={ms:1,s:1e3};return c.Item=o,c})),function(t,e){"function"==typeof define&&define.amd?define("isotope/item",["outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.Item=e(t.Outlayer))}(window,(function(t){"use strict";function e(){t.Item.apply(this,arguments)}var i=e.prototype=Object.create(t.Item.prototype),n=i._create;i._create=function(){this.id=this.layout.itemGUID++,n.call(this),this.sortData={}},i.updateSortData=function(){if(!this.isIgnored){this.sortData.id=this.id,this.sortData["original-order"]=this.id,this.sortData.random=Math.random();var t=this.layout.options.getSortData,e=this.layout._sorters;for(var i in t){var n=e[i];this.sortData[i]=n(this.element,this)}}};var o=i.destroy;return i.destroy=function(){o.apply(this,arguments),this.css({display:""})},e})),function(t,e){"function"==typeof define&&define.amd?define("isotope/layout-mode",["get-size/get-size","outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("get-size"),require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.LayoutMode=e(t.getSize,t.Outlayer))}(window,(function(t,e){"use strict";function i(t){this.isotope=t,t&&(this.options=t.options[this.namespace],this.element=t.element,this.items=t.filteredItems,this.size=t.size)}var n=i.prototype;return["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout","_getOption"].forEach((function(t){n[t]=function(){return e.prototype[t].apply(this.isotope,arguments)}})),n.needsVerticalResizeLayout=function(){var e=t(this.isotope.element);return this.isotope.size&&e&&e.innerHeight!=this.isotope.size.innerHeight},n._getMeasurement=function(){this.isotope._getMeasurement.apply(this,arguments)},n.getColumnWidth=function(){this.getSegmentSize("column","Width")},n.getRowHeight=function(){this.getSegmentSize("row","Height")},n.getSegmentSize=function(t,e){var i=t+e,n="outer"+e;if(this._getMeasurement(i,n),!this[i]){var o=this.getFirstItemSize();this[i]=o&&o[n]||this.isotope.size["inner"+e]}},n.getFirstItemSize=function(){var e=this.isotope.filteredItems[0];return e&&e.element&&t(e.element)},n.layout=function(){this.isotope.layout.apply(this.isotope,arguments)},n.getSize=function(){this.isotope.getSize(),this.size=this.isotope.size},i.modes={},i.create=function(t,e){function o(){i.apply(this,arguments)}return o.prototype=Object.create(n),o.prototype.constructor=o,e&&(o.options=e),o.prototype.namespace=t,i.modes[t]=o,o},i})),
/*!
 * Masonry v4.1.0
 * Cascading grid layout library
 * http://masonry.desandro.com
 * MIT License
 * by David DeSandro
 */
function(t,e){"function"==typeof define&&define.amd?define("masonry/masonry",["outlayer/outlayer","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("get-size")):t.Masonry=e(t.Outlayer,t.getSize)}(window,(function(t,e){var i=t.create("masonry");return i.compatOptions.fitWidth="isFitWidth",i.prototype._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns(),this.colYs=[];for(var t=0;t<this.cols;t++)this.colYs.push(0);this.maxY=0},i.prototype.measureColumns=function(){if(this.getContainerWidth(),!this.columnWidth){var t=this.items[0],i=t&&t.element;this.columnWidth=i&&e(i).outerWidth||this.containerWidth}var n=this.columnWidth+=this.gutter,o=this.containerWidth+this.gutter,s=o/n,a=n-o%n;s=Math[a&&a<1?"round":"floor"](s),this.cols=Math.max(s,1)},i.prototype.getContainerWidth=function(){var t=this._getOption("fitWidth")?this.element.parentNode:this.element,i=e(t);this.containerWidth=i&&i.innerWidth},i.prototype._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth%this.columnWidth,i=Math[e&&e<1?"round":"ceil"](t.size.outerWidth/this.columnWidth);i=Math.min(i,this.cols);for(var n=this._getColGroup(i),o=Math.min.apply(Math,n),s=n.indexOf(o),a={x:this.columnWidth*s,y:o},r=o+t.size.outerHeight,l=this.cols+1-n.length,d=0;d<l;d++)this.colYs[s+d]=r;return a},i.prototype._getColGroup=function(t){if(t<2)return this.colYs;for(var e=[],i=this.cols+1-t,n=0;n<i;n++){var o=this.colYs.slice(n,n+t);e[n]=Math.max.apply(Math,o)}return e},i.prototype._manageStamp=function(t){var i=e(t),n=this._getElementOffset(t),o=this._getOption("originLeft")?n.left:n.right,s=o+i.outerWidth,a=Math.floor(o/this.columnWidth);a=Math.max(0,a);var r=Math.floor(s/this.columnWidth);r-=s%this.columnWidth?0:1,r=Math.min(this.cols-1,r);for(var l=(this._getOption("originTop")?n.top:n.bottom)+i.outerHeight,d=a;d<=r;d++)this.colYs[d]=Math.max(l,this.colYs[d])},i.prototype._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var t={height:this.maxY};return this._getOption("fitWidth")&&(t.width=this._getContainerFitWidth()),t},i.prototype._getContainerFitWidth=function(){for(var t=0,e=this.cols;--e&&0===this.colYs[e];)t++;return(this.cols-t)*this.columnWidth-this.gutter},i.prototype.needsResizeLayout=function(){var t=this.containerWidth;return this.getContainerWidth(),t!=this.containerWidth},i})),
/*!
 * Masonry layout mode
 * sub-classes Masonry
 * http://masonry.desandro.com
 */
function(t,e){"function"==typeof define&&define.amd?define("isotope/layout-modes/masonry",["../layout-mode","masonry/masonry"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode"),require("masonry-layout")):e(t.Isotope.LayoutMode,t.Masonry)}(window,(function(t,e){"use strict";var i=t.create("masonry"),n=i.prototype,o={_getElementOffset:!0,layout:!0,_getMeasurement:!0};for(var s in e.prototype)o[s]||(n[s]=e.prototype[s]);var a=n.measureColumns;n.measureColumns=function(){this.items=this.isotope.filteredItems,a.call(this)};var r=n._getOption;return n._getOption=function(t){return"fitWidth"==t?void 0!==this.options.isFitWidth?this.options.isFitWidth:this.options.fitWidth:r.apply(this.isotope,arguments)},i})),function(t,e){"function"==typeof define&&define.amd?define("isotope/layout-modes/fit-rows",["../layout-mode"],e):"object"==typeof exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)}(window,(function(t){"use strict";var e=t.create("fitRows"),i=e.prototype;return i._resetLayout=function(){this.x=0,this.y=0,this.maxY=0,this._getMeasurement("gutter","outerWidth")},i._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth+this.gutter,i=this.isotope.size.innerWidth+this.gutter;0!==this.x&&e+this.x>i&&(this.x=0,this.y=this.maxY);var n={x:this.x,y:this.y};return this.maxY=Math.max(this.maxY,this.y+t.size.outerHeight),this.x+=e,n},i._getContainerSize=function(){return{height:this.maxY}},e})),function(t,e){"function"==typeof define&&define.amd?define("isotope/layout-modes/vertical",["../layout-mode"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)}(window,(function(t){"use strict";var e=t.create("vertical",{horizontalAlignment:0}),i=e.prototype;return i._resetLayout=function(){this.y=0},i._getItemLayoutPosition=function(t){t.getSize();var e=(this.isotope.size.innerWidth-t.size.outerWidth)*this.options.horizontalAlignment,i=this.y;return this.y+=t.size.outerHeight,{x:e,y:i}},i._getContainerSize=function(){return{height:this.y}},e})),
/*!
 * Isotope v3.0.0
 *
 * Licensed GPLv3 for open source use
 * or Isotope Commercial License for commercial use
 *
 * http://isotope.metafizzy.co
 * Copyright 2016 Metafizzy
 */
function(t,e){"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","desandro-matches-selector/matches-selector","fizzy-ui-utils/utils","./item","./layout-mode","./layout-modes/masonry","./layout-modes/fit-rows","./layout-modes/vertical"],(function(i,n,o,s,a,r){return e(t,i,n,o,s,a,r)})):"object"==typeof module&&module.exports?module.exports=e(t,require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("fizzy-ui-utils"),require("./item"),require("./layout-mode"),require("./layout-modes/masonry"),require("./layout-modes/fit-rows"),require("./layout-modes/vertical")):t.Isotope=e(t,t.Outlayer,t.getSize,t.matchesSelector,t.fizzyUIUtils,t.Isotope.Item,t.Isotope.LayoutMode)}(window,(function(t,e,i,n,o,s,a){var r=t.jQuery,l=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^\s+|\s+$/g,"")},d=e.create("isotope",{layoutMode:"masonry",isJQueryFiltering:!0,sortAscending:!0});d.Item=s,d.LayoutMode=a;var c=d.prototype;c._create=function(){for(var t in this.itemGUID=0,this._sorters={},this._getSorters(),e.prototype._create.call(this),this.modes={},this.filteredItems=this.items,this.sortHistory=["original-order"],a.modes)this._initLayoutMode(t)},c.reloadItems=function(){this.itemGUID=0,e.prototype.reloadItems.call(this)},c._itemize=function(){for(var t=e.prototype._itemize.apply(this,arguments),i=0;i<t.length;i++){t[i].id=this.itemGUID++}return this._updateItemsSortData(t),t},c._initLayoutMode=function(t){var e=a.modes[t],i=this.options[t]||{};this.options[t]=e.options?o.extend(e.options,i):i,this.modes[t]=new e(this)},c.layout=function(){this._isLayoutInited||!this._getOption("initLayout")?this._layout():this.arrange()},c._layout=function(){var t=this._getIsInstant();this._resetLayout(),this._manageStamps(),this.layoutItems(this.filteredItems,t),this._isLayoutInited=!0},c.arrange=function(t){this.option(t),this._getIsInstant();var e=this._filter(this.items);this.filteredItems=e.matches,this._bindArrangeComplete(),this._isInstant?this._noTransition(this._hideReveal,[e]):this._hideReveal(e),this._sort(),this._layout()},c._init=c.arrange,c._hideReveal=function(t){this.reveal(t.needReveal),this.hide(t.needHide)},c._getIsInstant=function(){var t=this._getOption("layoutInstant"),e=void 0!==t?t:!this._isLayoutInited;return this._isInstant=e,e},c._bindArrangeComplete=function(){var t,e,i,n=this;function o(){t&&e&&i&&n.dispatchEvent("arrangeComplete",null,[n.filteredItems])}this.once("layoutComplete",(function(){t=!0,o()})),this.once("hideComplete",(function(){e=!0,o()})),this.once("revealComplete",(function(){i=!0,o()}))},c._filter=function(t){var e=this.options.filter;e=e||"*";for(var i=[],n=[],o=[],s=this._getFilterTest(e),a=0;a<t.length;a++){var r=t[a];if(!r.isIgnored){var l=s(r);l&&i.push(r),l&&r.isHidden?n.push(r):l||r.isHidden||o.push(r)}}return{matches:i,needReveal:n,needHide:o}},c._getFilterTest=function(t){return r&&this.options.isJQueryFiltering?function(e){return r(e.element).is(t)}:"function"==typeof t?function(e){return t(e.element)}:function(e){return n(e.element,t)}},c.updateSortData=function(t){var e;t?(t=o.makeArray(t),e=this.getItems(t)):e=this.items,this._getSorters(),this._updateItemsSortData(e)},c._getSorters=function(){var t=this.options.getSortData;for(var e in t){var i=t[e];this._sorters[e]=h(i)}},c._updateItemsSortData=function(t){for(var e=t&&t.length,i=0;e&&i<e;i++){t[i].updateSortData()}};var h=function(t){if("string"!=typeof t)return t;var e=l(t).split(" "),i=e[0],n=i.match(/^\[(.+)\]$/),o=function(t,e){return t?function(e){return e.getAttribute(t)}:function(t){var i=t.querySelector(e);return i&&i.textContent}}(n&&n[1],i),s=d.sortDataParsers[e[1]];return t=s?function(t){return t&&s(o(t))}:function(t){return t&&o(t)}};d.sortDataParsers={parseInt:function(t){return parseInt(t,10)},parseFloat:function(t){return parseFloat(t)}},c._sort=function(){var t=this.options.sortBy;if(t){var e=function(t,e){return function(i,n){for(var o=0;o<t.length;o++){var s=t[o],a=i.sortData[s],r=n.sortData[s],l=0;if(i.layout.options.customSorters[s]instanceof Function?l=i.layout.options.customSorters[s].call(this,a,r):a!==r&&(l=a>r?1:-1),0!==l)return l*((void 0!==e[s]?e[s]:e)?1:-1)}return 0}}([].concat.apply(t,this.sortHistory),this.options.sortAscending);this.filteredItems.sort(e),t!=this.sortHistory[0]&&this.sortHistory.unshift(t)}},c._mode=function(){var t=this.options.layoutMode,e=this.modes[t];if(!e)throw new Error("No layout mode: "+t);return e.options=this.options[t],e},c._resetLayout=function(){e.prototype._resetLayout.call(this),this._mode()._resetLayout()},c._getItemLayoutPosition=function(t){return this._mode()._getItemLayoutPosition(t)},c._manageStamp=function(t){this._mode()._manageStamp(t)},c._getContainerSize=function(){return this._mode()._getContainerSize()},c.needsResizeLayout=function(){return this._mode().needsResizeLayout()},c.appended=function(t){var e=this.addItems(t);if(e.length){var i=this._filterRevealAdded(e);this.filteredItems=this.filteredItems.concat(i)}},c.prepended=function(t){var e=this._itemize(t);if(e.length){this._resetLayout(),this._manageStamps();var i=this._filterRevealAdded(e);this.layoutItems(this.filteredItems),this.filteredItems=i.concat(this.filteredItems),this.items=e.concat(this.items)}},c._filterRevealAdded=function(t){var e=this._filter(t);return this.hide(e.needHide),this.reveal(e.matches),this.layoutItems(e.matches,!0),e.matches},c.insert=function(t){var e=this.addItems(t);if(e.length){var i,n,o=e.length;for(i=0;i<o;i++)n=e[i],this.element.appendChild(n.element);var s=this._filter(e).matches;for(i=0;i<o;i++)e[i].isLayoutInstant=!0;for(this.arrange(),i=0;i<o;i++)delete e[i].isLayoutInstant;this.reveal(s)}};var u=c.remove;function p(t){var e=t.create("fitRows");return e.prototype._resetLayout=function(){if(this.x=0,this.y=0,this.maxY=0,this.row=0,this.rows=[],this._getMeasurement("gutter","outerWidth"),this.isotope.options.equalheight)for(var t=0;t<this.isotope.items.length;t++)this.isotope.items[t].css({height:"auto"})},e.prototype._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth,i=Math.ceil(this.isotope.size.innerWidth+1);0!==this.x&&e+this.x>i&&(this.x=0,this.y=this.maxY),0==this.x&&0!=this.y&&this.row++;var n={x:this.x,y:this.y};return this.maxY=Math.max(this.maxY,this.y+t.size.outerHeight),this.x+=e,void 0===this.rows[this.row]?(this.rows[this.row]=[],this.rows[this.row].start=this.y,this.rows[this.row].end=this.maxY):this.rows[this.row].end=Math.max(this.rows[this.row].end,this.maxY),t.row=this.row,n},e.prototype._equalHeight=function(){for(var t=0;t<this.isotope.items.length;t++){var e=this.isotope.items[t].row,i=this.rows[e];if(i){var n=i.end-i.start;n-=this.isotope.items[t].size.borderTopWidth+this.isotope.items[t].size.borderBottomWidth,n-=this.isotope.items[t].size.marginTop+this.isotope.items[t].size.marginBottom,n-=this.gutter.height||0,0==this.isotope.items[t].size.isBorderBox&&(n-=this.isotope.items[t].size.paddingTop+this.isotope.items[t].size.paddingBottom),this.isotope.items[t].size.height=n,this.isotope.items[t].css({height:n.toString()+"px"})}}},e.prototype._getContainerSize=function(){return this.isotope.options.equalheight&&this._equalHeight(),{height:this.maxY}},e}return c.remove=function(t){t=o.makeArray(t);var e=this.getItems(t);u.call(this,t);for(var i=e&&e.length,n=0;i&&n<i;n++){var s=e[n];o.removeFrom(this.filteredItems,s)}},c.shuffle=function(){for(var t=0;t<this.items.length;t++){this.items[t].sortData.random=Math.random()}this.options.sortBy="random",this._sort(),this._layout()},c._noTransition=function(t,e){var i=this.options.transitionDuration;this.options.transitionDuration=0;var n=t.apply(this,e);return this.options.transitionDuration=i,n},c.getFilteredItemElements=function(){return this.filteredItems.map((function(t){return t.element}))},"function"==typeof define&&define.amd?define(["../layout-mode"],p):"object"==typeof exports?module.exports=p(require("../layout-mode")):p(t.Isotope.LayoutMode),d})),function(t,e,i,n){t(i);var o=t(e);t.fn.Filterade=function(e){var i,n,s,a,r,l,d,c,h,u,p,f,m,g,v,w,y,b,C,x,_,S,k,T,I=!1;l={useFilters:!1,useSorting:!1,filterControls:".filter-controls",sortControls:".sort-controls",orderControls:".order-controls",controlsSelecter:"input",controlsSelecterChecked:'input[checked="checked"]',defaultFilter:"all",defaultSort:"date",defaultOrder:"desc",selectAll:"all",paginationMode:"pages",pageLimit:15,pageControls:".page-controls",previousButtonClass:"nav-prev",previousButtonLabel:"←",nextButtonClass:"nav-next",nextButtonLabel:"→",loadMoreButtonClass:"button-load-more",loadMoreButtonLabel:"Load more",pagerClass:"page",activeClass:"act",log:!1},r=t(this),k=e.customSorters||{},w=e.paginationMode||l.paginationMode,y=t(e.pageControls||l.pageControls),d=t(e.filterControls||l.filterControls),c=t(e.sortControls||l.sortControls),h=t(e.orderControls||l.orderControls),u=e.controlsSelecter||l.controlsSelecter,p=e.controlsSelecterChecked||l.controlsSelecterChecked,n=1,S=r.parent().hasClass("show-all-pages")?999:5;var z=t(".phantom-sticky").exists(),L=t(".sticky-top-line").exists(),E=t(".phantom-fade").exists(),O=t(".phantom-slide").exists(),P=(t(".split-header").exists(),0);return z||L?P=t(".masthead").height():(E||O)&&(P=t("#phantom").height()),(v=t([])).$nodesCache=t([]),r.find("> article, .wf-cell").each((function(){var e=t(this);v.push({node:this,$node:e,name:e.attr("data-name"),date:new Date(e.attr("data-date"))}),v.$nodesCache.push(this)})),b=Math.ceil(v.length/e.pageLimit),g=function(){var t;"pages"!==w&&"load-more"!==w||(t=0,v.each((function(){if(this.$node.hasClass("visible"))return t++})),b=Math.ceil(t/(e.pageLimit||l.pageLimit)))},C=function(){var i,o;if("pages"===w){if(y.empty(),y.addClass("hidden"),b>1){y.removeClass("hidden"),1!==n&&y.prepend('<a href="#" class="filter-item '+(e.previousButtonClass||l.previousButtonClass)+'">'+(e.previousButtonLabel||l.previousButtonLabel)+"</a>");var s=S-1,a=Math.floor(s/2),r=Math.ceil(s/2),d=Math.max(n-a,1),c=n+r;d<=a&&(c=d+s),c>b&&(d=Math.max(b-s,1),c=b);var h='<a href="javascript:void(0);" class="dots">…</a>',u=t('<div style="display: none;"></div>'),p=t('<div style="display: none;"></div>');for(i=o=1;1<=b?o<=b:o>=b;i=1<=b?++o:--o){var m='<a href="#" class="'+(e.pagerClass||l.pagerClass)+'" data-page="'+ +i+'">'+i+"</a>";i<d&&1!=i?u.append(m):(i==d&&u.children().length&&y.append(u).append(t(h)),i>c&&i!=b?p.append(m):(i==b&&p.children().length&&y.append(p).append(t(h)),y.append(m)))}n<b&&y.append('<a href="#" class="filter-item '+(e.nextButtonClass||l.nextButtonClass)+'">'+(e.nextButtonLabel||l.nextButtonLabel)+"</a>"),y.find('a[data-page="'+n+'"]').addClass(e.activeClass||l.activeClass);var g=e.pagerClass||l.pagerClass;g=g.trim().replace(" ","."),y.find("a.dots").click((function(e){y.find("div:hidden a").unwrap(),t(this).remove()})),y.find("a."+g).click((function(i){i.preventDefault(),n=parseInt(t(this).attr("data-page")),y.find("a."+(e.activeClass||l.activeClass)).removeClass(e.activeClass||l.activeClass),y.find('a[data-page="'+n+'"]').addClass(e.activeClass||l.activeClass),f()})),y.find("a."+(e.previousButtonClass||l.previousButtonClass)).click((function(t){t.preventDefault(),n--,f()})),y.find("a."+(e.nextButtonClass||l.nextButtonClass)).click((function(t){t.preventDefault(),n++,f()}))}}else if("load-more"===w&&(y.addClass("hidden"),b>1)){n<b&&y.removeClass("hidden");var v=e.loadMoreButtonClass||l.loadMoreButtonClass;return v=v.trim().replace(" ","."),y.find("a:not(.filtrade-ready)."+v).click((function(e){return e.preventDefault(),t(this).addClass("filtrade-ready"),!(n>=b)&&(n++,_())}))}},f=function(){var e=r.parent().attr("data-scroll-offset")?parseInt(r.parent().attr("data-scroll-offset")):0,i=r;r.hasClass("dt-css-grid")&&(i=r.parent());var n=i.offset().top-40;!r.parent().hasClass("enable-pagination-scroll")&&r.parent().hasClass("the7-elementor-widget")||t("html, body").animate({scrollTop:n-P+e},400),_()},x=function(){const i=r.attr("data-columns")?JSON.parse(r.attr("data-columns")):{};if("pages"===w||"load-more"===w){var o=0,s=t([]),a=t([]);v.each((function(t){this.$node.hasClass("visible")&&(o++,"pages"===w&&o>n*(e.pageLimit||l.pageLimit)-(e.pageLimit||l.pageLimit)&&o<=(e.pageLimit||l.pageLimit)*n||"load-more"===w&&o<=(e.pageLimit||l.pageLimit)*n?a.push(this.node):s.push(this.node))})),s.removeClass("visible").addClass("hidden"),a.removeClass("hidden").addClass("visible"),a.removeClass("first"),a.first().addClass("first"),i.d&&a.slice(0,i.d).addClass("d-hide-divider"),i.t&&a.slice(0,i.t).addClass("t-hide-divider"),i.p&&a.slice(0,i.p).addClass("m-hide-divider"),i.wd&&a.slice(0,i.wd).addClass("wd-hide-divider"),(I||"pages"===w&&"function"==typeof r.resetEffects)&&r.resetEffects()}},m=function(){var n=t([]),o=t([]);e.useFilters||l.useFilters?i===(e.selectAll||l.selectAll)?o=v.$nodesCache:v.each((function(t){this.$node.hasClass(i.replace(/^\./,""))?o.push(this.node):n.push(this.node)})):o=v.$nodesCache,n.removeClass("visible").addClass("hidden"),o.removeClass("hidden").addClass("visible")},T=function(){var e="asc"===a?1:-1;k[s]instanceof Function?v.sort((function(t,i){return e*k[s].call(this,t,i)})):"date"===s?v.sort((function(t,i){return e*(t.date-i.date)})):"name"===s&&v.sort((function(t,i){var n=t.name.toLowerCase(),o=i.name.toLowerCase(),s=0;return n!==o&&(s=n>o?1:-1),e*s})),v.$nodesCache=t([]),v.each((function(){v.$nodesCache.push(this.node)})),r.find(".paginator").length>0?v.$nodesCache.detach().insertBefore(r.find(".paginator")):v.$nodesCache.detach().appendTo(r)},_=function(){if(m(),g(),x(),C(),r.hasClass("lazy-loading-mode")){var e=r.find(".button-load-more").offset();e&&dtGlobals.winScrollTop>(e.top-o.height())/2&&r.find(".button-load-more").trigger("click")}t(".layzr-loading-on .blog-shortcode.jquery-filter.mode-list .visible:not(.shown)").layzrBlogInitialisation(),t(".layzr-loading-on .jquery-filter.dt-css-grid-wrap .wf-cell.visible").layzrBlogInitialisation(),r.trigger("updateReady")},function(){return(e.useFilters||l.useFilters)&&(i=d.find(p).attr("data-filter")||e.defaultFilter||l.defaultFilter,d.find(u).click((function(e){i=t(this).attr("data-filter"),n=1,I=!0,_(),I=!1}))),(e.useSorting||l.useSorting)&&(s=c.find(p).attr("data-by")||e.defaultSort||l.defaultSort,a=h.find(p).attr("data-sort")||e.defaultOrder||l.defaultOrder,c.find(u).click((function(t){s="date"===this.getAttribute("data-by")?"date":"name",T(),n=1,_()})),h.find(u).click((function(t){a="desc"===this.getAttribute("data-sort")?"desc":"asc",T(),n=1,_()}))),_()}()}}(jQuery,window,document),function(t){var e=function(e,i){var n,o,s,a,r,l,d,c,h,u,p,f,m=!1,g=null,v={},w={},y={isUpdating:!1},b={useFilters:!1,useSorting:!1,nodesSelector:"> article, .wf-cell",filterControls:".filter-controls",sortControls:".sort-controls",orderControls:".order-controls",controlsSelecter:"input",controlsSelecterChecked:'input[checked="checked"]',defaultFilter:"all",defaultSort:"date",defaultOrder:"desc",selectAll:"all",paginationMode:"pages",pageLimit:15,pageControls:".page-controls",previousButtonClass:"nav-prev",previousButtonLabel:"←",nextButtonClass:"nav-next",nextButtonLabel:"→",loadMoreButtonClass:"button-load-more",loadMoreButtonLabel:"Load more",pagerClass:"page",activeClass:"act",pagesToShow:5,usePaginationScroll:!1,scrollPagesOffset:0,infinityScroll:!1,classes:{visible:"visible",hidden:"hidden",first:"first"}},C=function(){return!(o>=p)&&(o++,k(),!0)},x=function(t){r.trigger("beforeSwitchPage"),_(t),k(),r.trigger("afterSwitchPage",[o])},_=function(t){w.usePaginationScroll&&void 0!==window.the7ScrollToTargetEl&&window.the7ScrollToTargetEl(r,"",t,!1,w.scrollPagesOffset)},S=function(){var e="asc"===a?1:-1;w.customSorters[s]instanceof Function?h.sort((function(t,i){return e*w.customSorters[s].call(this,t,i)})):"date"===s?h.sort((function(t,i){return e*(t.date-i.date)})):"name"===s&&h.sort((function(t,i){var n=t.name.toLowerCase(),o=i.name.toLowerCase(),s=0;return n!==o&&(s=n>o?1:-1),e*s})),h.$nodesCache=t([]),h.each((function(){h.$nodesCache.push(this.node)})),r.find(".paginator").length>0?h.$nodesCache.detach().insertBefore(r.find(".paginator")):h.$nodesCache.detach().appendTo(r)},k=function(){var e,i;e=t([]),i=t([]),w.useFilters?n===w.selectAll?i=h.$nodesCache:h.each((function(t){this.$node.hasClass(n.replace(/^\./,""))?i.push(this.node):e.push(this.node)})):i=h.$nodesCache,e.removeClass(w.classes.visible).addClass(w.classes.hidden),i.removeClass(w.classes.hidden).addClass(w.classes.visible),function(){if(p=Math.ceil(h.length/w.pageLimit),"pages"===w.paginationMode||"load-more"===w.paginationMode){var t=0;h.each((function(){if(this.$node.hasClass(w.classes.visible))return t++})),p=Math.ceil(t/w.pageLimit)}}(),function(){if("pages"===w.paginationMode||"load-more"===w.paginationMode){var e=0,i=t([]),n=t([]);h.each((function(t){this.$node.hasClass(w.classes.visible)&&(e++,"pages"===w.paginationMode&&e>o*w.pageLimit-w.pageLimit&&e<=w.pageLimit*o||"load-more"===w.paginationMode&&e<=w.pageLimit*o?n.push(this.node):i.push(this.node))})),i.removeClass(w.classes.visible).addClass(w.classes.hidden),n.removeClass(w.classes.hidden).addClass(w.classes.visible),n.removeClass(w.classes.first),n.first().addClass(w.classes.first),y.isUpdating||(m||"pages"===w.paginationMode&&"function"==typeof r.resetEffects)&&r.resetEffects(),r.trigger("paginateResults",[n])}}(),function(){var e,i;if("pages"===w.paginationMode){if(u.empty(),u.addClass(w.classes.hidden),p>1){u.removeClass(w.classes.hidden),1!==o&&u.prepend('<a href="#" class="filter-item '+w.previousButtonClass+'">'+w.previousButtonLabel+"</a>");var n=w.pagesToShow-1,s=Math.floor(n/2),a=Math.ceil(n/2),r=Math.max(o-s,1),l=o+a;r<=s&&(l=r+n),l>p&&(r=Math.max(p-n,1),l=p);var d='<a href="javascript:void(0);" class="dots">…</a>',c=t('<div style="display: none;"></div>'),h=t('<div style="display: none;"></div>');for(e=i=1;1<=p?i<=p:i>=p;e=1<=p?++i:--i){var m='<a href="#" class="'+w.pagerClass+'" data-page="'+ +e+'">'+e+"</a>";e<r&&1!==e?c.append(m):(e===r&&c.children().length&&u.append(c).append(t(d)),e>l&&e!==p?h.append(m):(e===p&&h.children().length&&u.append(h).append(t(d)),u.append(m)))}o<p&&u.append('<a href="#" class="filter-item '+w.nextButtonClass+'">'+w.nextButtonLabel+"</a>"),u.find('a[data-page="'+o+'"]').addClass(w.activeClass);var g=w.pagerClass;g=g.trim().replace(" ","."),I("pagesDots",u.find("a.dots"),"click",(function(e){u.find("div:hidden a").unwrap(),t(this).remove()})),I("pagesPagerClass",u.find("a."+g),"click",(function(e){e.preventDefault(),o=parseInt(t(this).attr("data-page")),u.find("a."+w.activeClass).removeClass(w.activeClass),u.find('a[data-page="'+o+'"]').addClass(w.activeClass),x(t(this))})),I("pagesPrev",u.find("a."+w.previousButtonClass),"click",(function(e){e.preventDefault(),o--,x(t(this))})),I("pagesNext",u.find("a."+w.nextButtonClass),"click",(function(e){e.preventDefault(),o++,x(t(this))}))}}else"load-more"===w.paginationMode&&(u.addClass(w.classes.hidden),p>1&&(o<p&&u.removeClass(w.classes.hidden),I("paginateLoadMore",f,"click",(function(t){t.preventDefault(),C()}))))}(),r.trigger("updateReady")},T=function(i){r=t(e),function(e){w=t.extend(!0,b,e),u=t(w.pageControls),l=t(w.filterControls),d=t(w.sortControls),c=t(w.orderControls),f=u.find("."+w.loadMoreButtonClass)}(i),o=1,(h=t([])).$nodesCache=t([]),r.find(w.nodesSelector).each((function(){var e=t(this);h.push({node:this,$node:e,name:e.attr("data-name"),date:new Date(e.attr("data-date"))}),h.$nodesCache.push(this)})),w.useFilters&&(n=l.find(w.controlsSelecterChecked).attr("data-filter")||w.defaultFilter,I("filtersFilterControl",l.find(w.controlsSelecter),"click",(function(e){n=t(this).attr("data-filter"),o=1,m=!0,k(),m=!1}))),w.useSorting&&(s=d.find(w.controlsSelecterChecked).attr("data-by")||w.defaultSort,a=c.find(w.controlsSelecterChecked).attr("data-sort")||w.defaultOrder,I("sortSortControls",d.find(w.controlsSelecter),"click",(function(t){s="date"===this.getAttribute("data-by")?"date":"name",S(),o=1,k()})),I("sortOrderControls",c.find(w.controlsSelecter),"click",(function(t){a="desc"===this.getAttribute("data-sort")?"desc":"asc",S(),o=1,k()}))),w.infinityScroll&&(g=new The7Scroll({offset:"0% 0% 25%",callback:t=>{t.isInViewport&&(g.unobserve(f[0]),C()&&g.observe(f[0]))}}).getScrollObserver()).observe(f[0]),k()},I=function(t,e,i,n){e.length&&(z(t),e.on(i,n),v[t]={element:e,eventName:i,callback:n})},z=function(t){if(t in v){var e=v[t];e.element.off(e.eventName,e.callback),delete v[t]}};this.paginationScroll=function(t){_(t)},this.update=function(t){this.destroy(),y.isUpdating=!0,T(t),y.isUpdating=!1},this.destroy=function(){Object.values(v).forEach((t=>{t.element.off(t.eventName,t.callback)})),v={},null!=g&&(g.unobserve(f[0]),g=null)},T(i)};t.fn.The7SimpleFilterade=function(i){var n="string"==typeof i,o=Array.prototype.slice.call(arguments,1);return this.each((function(){var s=t(this);if(n){var a=s.data("the7-simple-filterade");if(!a)throw Error("Trying to perform the `"+i+"` method prior to initialization");if(!a[i])throw ReferenceError("Method `"+i+"` not found in instance");a[i].apply(a,o),"destroy"===i&&s.removeData("the7-simple-filterade")}else s.data("the7-simple-filterade",new e(this,i))})),this}}(jQuery),function(t,e){"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():t.ResizeSensor=e()}("undefined"!=typeof window?window:this,(function(){if("undefined"==typeof window)return null;var t=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(t){return window.setTimeout(t,20)};function e(t,e){var i=Object.prototype.toString.call(t),n="[object Array]"===i||"[object NodeList]"===i||"[object HTMLCollection]"===i||"[object Object]"===i||"undefined"!=typeof jQuery&&t instanceof jQuery||"undefined"!=typeof Elements&&t instanceof Elements,o=0,s=t.length;if(n)for(;o<s;o++)e(t[o]);else e(t)}function i(t){if(!t.getBoundingClientRect)return{width:t.offsetWidth,height:t.offsetHeight};var e=t.getBoundingClientRect();return{width:Math.round(e.width),height:Math.round(e.height)}}var n=function(o,s){function a(){var t,e,i=[];this.add=function(t){i.push(t)},this.call=function(n){for(t=0,e=i.length;t<e;t++)i[t].call(this,n)},this.remove=function(n){var o=[];for(t=0,e=i.length;t<e;t++)i[t]!==n&&o.push(i[t]);i=o},this.length=function(){return i.length}}function r(e,n){if(e)if(e.resizedAttached)e.resizedAttached.add(n);else{e.resizedAttached=new a,e.resizedAttached.add(n),e.resizeSensor=document.createElement("div"),e.resizeSensor.dir="ltr",e.resizeSensor.className="resize-sensor";var o="pointer-events: none; position: absolute; left: 0px; top: 0px; right: 0; bottom: 0; overflow: hidden; z-index: -1; visibility: hidden; max-width: 100%;",s="position: absolute; left: 0; top: 0; transition: 0s;";e.resizeSensor.style.cssText=o,e.resizeSensor.innerHTML='<div class="resize-sensor-expand" style="'+o+'"><div style="'+s+'"></div></div><div class="resize-sensor-shrink" style="'+o+'"><div style="'+s+' width: 200%; height: 200%"></div></div>',e.appendChild(e.resizeSensor);var r=window.getComputedStyle(e),l=r?r.getPropertyValue("position"):null;"absolute"!==l&&"relative"!==l&&"fixed"!==l&&(e.style.position="relative");var d,c,h=e.resizeSensor.childNodes[0],u=h.childNodes[0],p=e.resizeSensor.childNodes[1],f=i(e),m=f.width,g=f.height,v=!0,w=0,y=function(){if(v){if(0===e.offsetWidth&&0===e.offsetHeight)return void(w||(w=t((function(){w=0,y()}))));v=!1}var i,n;i=e.offsetWidth,n=e.offsetHeight,u.style.width=i+10+"px",u.style.height=n+10+"px",h.scrollLeft=i+10,h.scrollTop=n+10,p.scrollLeft=i+10,p.scrollTop=n+10};e.resizeSensor.resetSensor=y;var b=function(){c=0,d&&(m=f.width,g=f.height,e.resizedAttached&&e.resizedAttached.call(f))},C=function(){f=i(e),(d=f.width!==m||f.height!==g)&&!c&&(c=t(b)),y()},x=function(t,e,i){t.attachEvent?t.attachEvent("on"+e,i):t.addEventListener(e,i)};x(h,"scroll",C),x(p,"scroll",C),t(y)}}e(o,(function(t){r(t,s)})),this.detach=function(t){n.detach(o,t)},this.reset=function(){o.resizeSensor.resetSensor()}};if(n.reset=function(t){e(t,(function(t){t.resizeSensor.resetSensor()}))},n.detach=function(t,i){e(t,(function(t){t&&(t.resizedAttached&&"function"==typeof i&&(t.resizedAttached.remove(i),t.resizedAttached.length())||t.resizeSensor&&(t.contains(t.resizeSensor)&&t.removeChild(t.resizeSensor),delete t.resizeSensor,delete t.resizedAttached))}))},"undefined"!=typeof MutationObserver){var o=new MutationObserver((function(t){for(var e in t)if(t.hasOwnProperty(e))for(var i=t[e].addedNodes,o=0;o<i.length;o++)i[o].resizeSensor&&n.reset(i[o])}));document.addEventListener("DOMContentLoaded",(function(t){o.observe(document.body,{childList:!0,subtree:!0})}))}return n})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.StickySidebar={})}(this,(function(t){"use strict";"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var e,i,n=(function(t,e){!function(t){Object.defineProperty(t,"__esModule",{value:!0});var e,i,n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),o=(e=".stickySidebar",i={topSpacing:0,bottomSpacing:0,containerSelector:!1,innerWrapperSelector:".inner-wrapper-sticky",stickyClass:"is-affixed",resizeSensor:!0,minWidth:!1},function(){function t(e){var n=this,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=t.extend(i,o),this.sidebar="string"==typeof e?document.querySelector(e):e,void 0===this.sidebar)throw new Error("There is no specific sidebar element.");this.sidebarInner=!1,this.container=this.sidebar.parentElement,this.affixedType="STATIC",this.direction="down",this.support={transform:!1,transform3d:!1},this._initialized=!1,this._reStyle=!1,this._breakpoint=!1,this.dimensions={translateY:0,maxTranslateY:0,topSpacing:0,lastTopSpacing:0,bottomSpacing:0,lastBottomSpacing:0,sidebarHeight:0,sidebarWidth:0,containerTop:0,containerHeight:0,viewportHeight:0,viewportTop:0,lastViewportTop:0},["handleEvent"].forEach((function(t){n[t]=n[t].bind(n)})),this.initialize()}return n(t,[{key:"initialize",value:function(){var t=this;if(this._setSupportFeatures(),this.options.innerWrapperSelector&&(this.sidebarInner=this.sidebar.querySelector(this.options.innerWrapperSelector),null===this.sidebarInner&&(this.sidebarInner=!1)),!this.sidebarInner){var e=document.createElement("div");for(e.setAttribute("class","inner-wrapper-sticky"),this.sidebar.appendChild(e);this.sidebar.firstChild!=e;)e.appendChild(this.sidebar.firstChild);this.sidebarInner=this.sidebar.querySelector(".inner-wrapper-sticky")}if(this.options.containerSelector){var i=document.querySelectorAll(this.options.containerSelector);if((i=Array.prototype.slice.call(i)).forEach((function(e,i){e.contains(t.sidebar)&&(t.container=e)})),!i.length)throw new Error("The container does not contains on the sidebar.")}"function"!=typeof this.options.topSpacing&&(this.options.topSpacing=parseInt(this.options.topSpacing)||0),"function"!=typeof this.options.bottomSpacing&&(this.options.bottomSpacing=parseInt(this.options.bottomSpacing)||0),this._widthBreakpoint(),this.calcDimensions(),this.stickyPosition(),this.bindEvents(),this._initialized=!0}},{key:"bindEvents",value:function(){window.addEventListener("resize",this,{passive:!0,capture:!1}),window.addEventListener("scroll",this,{passive:!0,capture:!1}),this.sidebar.addEventListener("update"+e,this),this.options.resizeSensor&&"undefined"!=typeof ResizeSensor&&(new ResizeSensor(this.sidebarInner,this.handleEvent),new ResizeSensor(this.container,this.handleEvent))}},{key:"handleEvent",value:function(t){this.updateSticky(t)}},{key:"calcDimensions",value:function(){if(!this._breakpoint){var e=this.dimensions;e.containerTop=t.offsetRelative(this.container).top,e.containerHeight=this.container.clientHeight,e.containerBottom=e.containerTop+e.containerHeight,e.sidebarHeight=this.sidebarInner.offsetHeight,e.sidebarWidth=this.sidebarInner.offsetWidth,e.viewportHeight=window.innerHeight,e.maxTranslateY=e.containerHeight-e.sidebarHeight,this._calcDimensionsWithScroll()}}},{key:"_calcDimensionsWithScroll",value:function(){var e=this.dimensions;e.sidebarLeft=t.offsetRelative(this.sidebar).left,e.viewportTop=document.documentElement.scrollTop||document.body.scrollTop,e.viewportBottom=e.viewportTop+e.viewportHeight,e.viewportLeft=document.documentElement.scrollLeft||document.body.scrollLeft,e.topSpacing=this.options.topSpacing,e.bottomSpacing=this.options.bottomSpacing,"function"==typeof e.topSpacing&&(e.topSpacing=parseInt(e.topSpacing(this.sidebar))||0),"function"==typeof e.bottomSpacing&&(e.bottomSpacing=parseInt(e.bottomSpacing(this.sidebar))||0),"VIEWPORT-TOP"===this.affixedType?e.topSpacing<e.lastTopSpacing&&(e.translateY+=e.lastTopSpacing-e.topSpacing,this._reStyle=!0):"VIEWPORT-BOTTOM"===this.affixedType&&e.bottomSpacing<e.lastBottomSpacing&&(e.translateY+=e.lastBottomSpacing-e.bottomSpacing,this._reStyle=!0),e.lastTopSpacing=e.topSpacing,e.lastBottomSpacing=e.bottomSpacing}},{key:"isSidebarFitsViewport",value:function(){var t=this.dimensions,e="down"===this.scrollDirection?t.lastBottomSpacing:t.lastTopSpacing;return this.dimensions.sidebarHeight+e<this.dimensions.viewportHeight}},{key:"observeScrollDir",value:function(){var t=this.dimensions;if(t.lastViewportTop!==t.viewportTop){var e="down"===this.direction?Math.min:Math.max;t.viewportTop===e(t.viewportTop,t.lastViewportTop)&&(this.direction="down"===this.direction?"up":"down")}}},{key:"getAffixType",value:function(){this._calcDimensionsWithScroll();var t=this.dimensions,e=t.viewportTop+t.topSpacing,i=this.affixedType;return e<=t.containerTop||t.containerHeight<=t.sidebarHeight?(t.translateY=0,i="STATIC"):i="up"===this.direction?this._getAffixTypeScrollingUp():this._getAffixTypeScrollingDown(),t.translateY=Math.max(0,t.translateY),t.translateY=Math.min(t.containerHeight,t.translateY),t.translateY=Math.round(t.translateY),t.lastViewportTop=t.viewportTop,i}},{key:"_getAffixTypeScrollingDown",value:function(){var t=this.dimensions,e=t.sidebarHeight+t.containerTop,i=t.viewportTop+t.topSpacing,n=t.viewportBottom-t.bottomSpacing,o=this.affixedType;return this.isSidebarFitsViewport()?t.sidebarHeight+i>=t.containerBottom?(t.translateY=t.containerBottom-e,o="CONTAINER-BOTTOM"):i>=t.containerTop&&(t.translateY=i-t.containerTop,o="VIEWPORT-TOP"):t.containerBottom<=n?(t.translateY=t.containerBottom-e,o="CONTAINER-BOTTOM"):e+t.translateY<=n?(t.translateY=n-e,o="VIEWPORT-BOTTOM"):t.containerTop+t.translateY<=i&&0!==t.translateY&&t.maxTranslateY!==t.translateY&&(o="VIEWPORT-UNBOTTOM"),o}},{key:"_getAffixTypeScrollingUp",value:function(){var t=this.dimensions,e=t.sidebarHeight+t.containerTop,i=t.viewportTop+t.topSpacing,n=t.viewportBottom-t.bottomSpacing,o=this.affixedType;return i<=t.translateY+t.containerTop?(t.translateY=i-t.containerTop,o="VIEWPORT-TOP"):t.containerBottom<=n?(t.translateY=t.containerBottom-e,o="CONTAINER-BOTTOM"):this.isSidebarFitsViewport()||t.containerTop<=i&&0!==t.translateY&&t.maxTranslateY!==t.translateY&&(o="VIEWPORT-UNBOTTOM"),o}},{key:"_getStyle",value:function(e){if(void 0!==e){var i={inner:{},outer:{}},n=this.dimensions;switch(e){case"VIEWPORT-TOP":i.inner={position:"fixed",top:n.topSpacing,left:n.sidebarLeft-n.viewportLeft,width:n.sidebarWidth};break;case"VIEWPORT-BOTTOM":i.inner={position:"fixed",top:"auto",left:n.sidebarLeft,bottom:n.bottomSpacing,width:n.sidebarWidth};break;case"CONTAINER-BOTTOM":case"VIEWPORT-UNBOTTOM":var o=this._getTranslate(0,n.translateY+"px");i.inner=o?{transform:o}:{position:"absolute",top:n.translateY,width:n.sidebarWidth}}switch(e){case"VIEWPORT-TOP":case"VIEWPORT-BOTTOM":case"VIEWPORT-UNBOTTOM":case"CONTAINER-BOTTOM":i.outer={height:n.sidebarHeight,position:"relative"}}return i.outer=t.extend({height:"",position:""},i.outer),i.inner=t.extend({position:"relative",top:"",left:"",bottom:"",width:"",transform:""},i.inner),i}}},{key:"stickyPosition",value:function(i){if(!this._breakpoint){i=this._reStyle||i||!1,this.options.topSpacing,this.options.bottomSpacing;var n=this.getAffixType(),o=this._getStyle(n);if((this.affixedType!=n||i)&&n){var s="affix."+n.toLowerCase().replace("viewport-","")+e;for(var a in t.eventTrigger(this.sidebar,s),"STATIC"===n?t.removeClass(this.sidebar,this.options.stickyClass):t.addClass(this.sidebar,this.options.stickyClass),o.outer){var r="number"==typeof o.outer[a]?"px":"";this.sidebar.style[a]=o.outer[a]+r}for(var l in o.inner){var d="number"==typeof o.inner[l]?"px":"";this.sidebarInner.style[l]=o.inner[l]+d}var c="affixed."+n.toLowerCase().replace("viewport-","")+e;t.eventTrigger(this.sidebar,c)}else this._initialized&&(this.sidebarInner.style.left=o.inner.left);this.affixedType=n}}},{key:"_widthBreakpoint",value:function(){window.innerWidth<=this.options.minWidth?(this._breakpoint=!0,this.affixedType="STATIC",this.sidebar.removeAttribute("style"),t.removeClass(this.sidebar,this.options.stickyClass),this.sidebarInner.removeAttribute("style")):this._breakpoint=!1}},{key:"updateSticky",value:function(){var t,e=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this._running||(this._running=!0,t=i.type,requestAnimationFrame((function(){if("scroll"===t)e._calcDimensionsWithScroll(),e.observeScrollDir(),e.stickyPosition();else e._widthBreakpoint(),e.calcDimensions(),e.stickyPosition(!0);e._running=!1})))}},{key:"_setSupportFeatures",value:function(){var e=this.support;e.transform=t.supportTransform(),e.transform3d=t.supportTransform(!0)}},{key:"_getTranslate",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0;return this.support.transform3d?"translate3d("+t+", "+e+", "+i+")":!!this.support.translate&&"translate("+t+", "+e+")"}},{key:"destroy",value:function(){window.removeEventListener("resize",this,{capture:!1}),window.removeEventListener("scroll",this,{capture:!1}),this.sidebar.classList.remove(this.options.stickyClass),this.sidebar.style.minHeight="",this.sidebar.removeEventListener("update"+e,this);var t={inner:{},outer:{}};for(var i in t.inner={position:"",top:"",left:"",bottom:"",width:"",transform:""},t.outer={height:"",position:""},t.outer)this.sidebar.style[i]=t.outer[i];for(var n in t.inner)this.sidebarInner.style[n]=t.inner[n];this.options.resizeSensor&&"undefined"!=typeof ResizeSensor&&(ResizeSensor.detach(this.sidebarInner,this.handleEvent),ResizeSensor.detach(this.container,this.handleEvent))}}],[{key:"supportTransform",value:function(t){var e=!1,i=t?"perspective":"transform",n=i.charAt(0).toUpperCase()+i.slice(1),o=document.createElement("support").style;return(i+" "+["Webkit","Moz","O","ms"].join(n+" ")+n).split(" ").forEach((function(t,i){if(void 0!==o[t])return e=t,!1})),e}},{key:"eventTrigger",value:function(t,e,i){try{var n=new CustomEvent(e,{detail:i})}catch(t){(n=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i)}t.dispatchEvent(n)}},{key:"extend",value:function(t,e){var i={};for(var n in t)void 0!==e[n]?i[n]=e[n]:i[n]=t[n];return i}},{key:"offsetRelative",value:function(t){var e={left:0,top:0};do{var i=t.offsetTop,n=t.offsetLeft;isNaN(i)||(e.top+=i),isNaN(n)||(e.left+=n),t="BODY"===t.tagName?t.parentElement:t.offsetParent}while(t);return e}},{key:"addClass",value:function(e,i){t.hasClass(e,i)||(e.classList?e.classList.add(i):e.className+=" "+i)}},{key:"removeClass",value:function(e,i){t.hasClass(e,i)&&(e.classList?e.classList.remove(i):e.className=e.className.replace(new RegExp("(^|\\b)"+i.split(" ").join("|")+"(\\b|$)","gi")," "))}},{key:"hasClass",value:function(t,e){return t.classList?t.classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(t.className)}},{key:"defaults",get:function(){return i}}]),t}());t.default=o,window.StickySidebar=o}(e)}(e={exports:{}},e.exports),e.exports),o=(i=n)&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i;t.default=o,t.__moduleExports=n,Object.defineProperty(t,"__esModule",{value:!0})}));var the7Utils={};function showLazyImg(t){t.hasClass("is-loaded")||setTimeout((function(){t.parent().removeClass("layzr-bg")}),350)}the7Utils.parseIntParam=function(t,e){return e=void 0!==e?e:0,t?parseInt(t):e},the7Utils.parseFloatParam=function(t,e){return e=void 0!==e?e:0,t?parseFloat(t):e},the7Utils.parseParam=function(t,e){return e=void 0!==e?e:"",void 0!==t?t:e},the7Utils.isFunction=function(t){return"function"==typeof t},function(t,e,i,n){"use strict";var o="rcrumbs",s={version:"1.1.0",callback:{preCrumbsListDisplay:t.noop,preCrumbDisplay:t.noop,postCrumbsListDisplay:t.noop,postCrumbDisplay:t.noop},ellipsis:!0,windowResize:!0,nbUncollapsableCrumbs:2,nbFixedCrumbs:0,animation:{activated:!0,speed:400}};function a(e,i){this.element=e,this.$element=t(e),this.options=t.extend(!0,{},s,i),this._defaults=s,this._name=o,a.prototype.plugin=this,this._init()}a.prototype={version:function(){return this.options.version},_init:function(){if(this.$element.hasClass("rcrumbs")||this.$element.addClass("rcrumbs"),this.nbCrumbDisplayed=0,this.$crumbsList=t(this.element),this.$crumbs=t("li",this.$crumbsList),this.$lastCrumb=this.$crumbs.last(),this.reversedCrumbs=t("li",this.$crumbsList).get().reverse(),this.lastNbCrumbDisplayed=0,this.totalCrumbsWidth=0,this.fixedCrumbsWidth=0,this._initCrumbs(),this.options.nbFixedCrumbs>0){var e=this.$crumbs.length;this.$crumbs=t("li",this.$crumbsList).slice(this.options.nbFixedCrumbs,e),this.reversedCrumbs=t("li",this.$crumbsList).slice(this.options.nbFixedCrumbs,e).get().reverse();var i=this;t("li",this.$crumbsList).slice(0,this.options.nbFixedCrumbs).each((function(e,n){i.totalCrumbsWidth+=t(n).data("width"),t(n).addClass("show")}))}this._showOrHideCrumbsList(!0),this.options.windowResize&&this._showOrHideCrumbsListOnWindowResize()},_getHiddenElWidth:function(e){var i,n=t(e).clone(!1);return n.css({visibility:"hidden",position:"absolute"}),n.appendTo(this.$crumbsList),i=n.width(),n.remove(),i},_initCrumbs:function(){var e=this;t(this.$crumbsList).contents().filter((function(){return 3===this.nodeType})).remove(),t.each(this.$crumbs,(function(i,n){var o=t(this);e._storeCrumbWidth(o)})),this.options.nbFixedCrumbs>0&&t(this.$crumbs).slice(0,this.options.nbFixedCrumbs).each((function(i,n){e.fixedCrumbsWidth+=t(n).data("width")}))},_storeCrumbWidth:function(t){var e=this._getHiddenElWidth(t);return t.data("width",e),e},_showOrHideCrumbsList:function(e){var i=this;this.remainingSpaceToDisplayCrumbs=this.$element.width(),this.nbCrumbDisplayed=0,this.totalCrumbsWidth=0,this.options.nbFixedCrumbs>0&&(this.remainingSpaceToDisplayCrumbs-=this.fixedCrumbsWidth,t("li",this.$crumbsList).slice(0,this.options.nbFixedCrumbs).each((function(e,n){i.totalCrumbsWidth+=t(n).data("width")}))),this.nextCrumbToShowWidth=undefined,this.options.callback.preCrumbsListDisplay(this),t.each(this.reversedCrumbs,(function(n,o){var s=t(this),a=t(i.reversedCrumbs[n+1]);i._showOrHideCrumb(s,a,n,e)})),this.lastNbCrumbDisplayed=this.nbCrumbDisplayed,this.options.callback.postCrumbsListDisplay(this)},_showOrHideCrumb:function(t,e,i,n){this.options.callback.preCrumbDisplay(t);var o=this;function s(){t.addClass("show"),o.lastNbCrumbDisplayed<o.nbCrumbDisplayed+1&&o.options.animation.activated&&!n?(t.width(0),t.animate({width:t.data("width")},o.options.animation.speed,(function(){o.options.callback.postCrumbDisplay(t)}))):o.options.callback.postCrumbDisplay(t),o.nbCrumbDisplayed+=1}this.remainingSpaceToDisplayCrumbs-=t.data("width"),i<this.options.nbUncollapsableCrumbs?(s(),this.remainingSpaceToDisplayCrumbs<0&&function(t){t.css({width:o.remainingSpaceToDisplayCrumbs+t.data("width")+"px"}),t.addClass("ellipsis")}(this.$lastCrumb),this.totalCrumbsWidth+=t.data("width")):this.remainingSpaceToDisplayCrumbs>=0?(s(),this.totalCrumbsWidth+=t.data("width")):(this.lastNbCrumbDisplayed>this.nbCrumbDisplayed-1&&this.options.animation.activated?t.animate({width:0},o.options.animation.speed,(function(){t.removeClass("show")})):t.removeClass("show"),this.nextCrumbToShowWidth||(this.nextCrumbToShowWidth=t.data("width")))},_showOrHideCrumbsListOnWindowResize:function(){var i=this;t(e).resize((function(){var e=i.$element.width();(e<i.totalCrumbsWidth||i.totalCrumbsWidth+i.nextCrumbToShowWidth<e)&&(t.each(i.reversedCrumbs,(function(e,i){t(this).stop(!0,!0)})),i._showOrHideCrumbsList()),e>=i.totalCrumbsWidth&&i.$lastCrumb.hasClass("ellipsis")&&i._disableEllipsis(i.$lastCrumb)}))},_disableEllipsis:function(t){t.css({width:"auto"}),t.removeClass("ellipsis")}},t.fn[o]=function(e){if(a.prototype[e]&&-1===e.indexOf("_")){var i=t.data(this[0],"plugin_"+o);if(i)return a.prototype[e].apply(i,Array.prototype.slice.call(arguments,1));t.error("jquery."+o+" plugin must be initialized first on the element")}else{if("object"==typeof e||!e)return this.each((function(){t.data(this,"plugin_"+o)?t.error("jquery."+o+" plugin cannot be instantiated multiple times on same element"):t.data(this,"plugin_"+o,new a(this,e))}));t.error("Method "+e+" does not exist on jquery."+o)}}}(jQuery,window,document),function(t,e){var i,n;"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):(t="undefined"!=typeof globalThis?globalThis:t||self,i=t.tabbable,n=t.tabbable={},e(n),n.noConflict=function(){return t.tabbable=i,n})}(this,(function(t){"use strict";var e=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],i=e.join(","),n="undefined"==typeof Element,o=n?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,s=!n&&Element.prototype.getRootNode?function(t){var e;return null==t||null===(e=t.getRootNode)||void 0===e?void 0:e.call(t)}:function(t){return null==t?void 0:t.ownerDocument},a=function t(e,i){var n;void 0===i&&(i=!0);var o=null==e||null===(n=e.getAttribute)||void 0===n?void 0:n.call(e,"inert");return""===o||"true"===o||i&&e&&t(e.parentNode)},r=function(t,e,n){if(a(t))return[];var s=Array.prototype.slice.apply(t.querySelectorAll(i));return e&&o.call(t,i)&&s.unshift(t),s.filter(n)},l=function t(e,n,s){for(var r=[],l=Array.from(e);l.length;){var d=l.shift();if(!a(d,!1))if("SLOT"===d.tagName){var c=d.assignedElements(),h=t(c.length?c:d.children,!0,s);s.flatten?r.push.apply(r,h):r.push({scopeParent:d,candidates:h})}else{o.call(d,i)&&s.filter(d)&&(n||!e.includes(d))&&r.push(d);var u=d.shadowRoot||"function"==typeof s.getShadowRoot&&s.getShadowRoot(d),p=!a(u,!1)&&(!s.shadowRootFilter||s.shadowRootFilter(d));if(u&&p){var f=t(!0===u?d.children:u.children,!0,s);s.flatten?r.push.apply(r,f):r.push({scopeParent:d,candidates:f})}else l.unshift.apply(l,d.children)}}return r},d=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},c=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||function(t){var e,i=null==t||null===(e=t.getAttribute)||void 0===e?void 0:e.call(t,"contenteditable");return""===i||"true"===i}(t))&&!d(t)?0:t.tabIndex},h=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},u=function(t){return"INPUT"===t.tagName},p=function(t){var e=t.getBoundingClientRect(),i=e.width,n=e.height;return 0===i&&0===n},f=function(t,e){return!(e.disabled||a(e)||function(t){return u(t)&&"hidden"===t.type}(e)||function(t,e){var i=e.displayCheck,n=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var a=o.call(t,"details>summary:first-of-type")?t.parentElement:t;if(o.call(a,"details:not([open]) *"))return!0;if(i&&"full"!==i&&"legacy-full"!==i){if("non-zero-area"===i)return p(t)}else{if("function"==typeof n){for(var r=t;t;){var l=t.parentElement,d=s(t);if(l&&!l.shadowRoot&&!0===n(l))return p(t);t=t.assignedSlot?t.assignedSlot:l||d===t.ownerDocument?l:d.host}t=r}if(function(t){var e,i,n,o,a=t&&s(t),r=null===(e=a)||void 0===e?void 0:e.host,l=!1;if(a&&a!==t)for(l=!!(null!==(i=r)&&void 0!==i&&null!==(n=i.ownerDocument)&&void 0!==n&&n.contains(r)||null!=t&&null!==(o=t.ownerDocument)&&void 0!==o&&o.contains(t));!l&&r;){var d,c,h;l=!(null===(c=r=null===(d=a=s(r))||void 0===d?void 0:d.host)||void 0===c||null===(h=c.ownerDocument)||void 0===h||!h.contains(r))}return l}(t))return!t.getClientRects().length;if("legacy-full"!==i)return!0}return!1}(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var i=0;i<e.children.length;i++){var n=e.children.item(i);if("LEGEND"===n.tagName)return!!o.call(e,"fieldset[disabled] *")||!n.contains(t)}return!0}e=e.parentElement}return!1}(e))},m=function(t,e){return!(function(t){return function(t){return u(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,i=t.form||s(t),n=function(t){return i.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=n(window.CSS.escape(t.name));else try{e=n(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var o=function(t,e){for(var i=0;i<t.length;i++)if(t[i].checked&&t[i].form===e)return t[i]}(e,t.form);return!o||o===t}(t)}(e)||c(e)<0||!f(t,e))},g=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},v=function t(e){var i=[],n=[];return e.forEach((function(e,o){var s=!!e.scopeParent,a=s?e.scopeParent:e,r=function(t,e){var i=c(t);return i<0&&e&&!d(t)?0:i}(a,s),l=s?t(e.candidates):a;0===r?s?i.push.apply(i,l):i.push(a):n.push({documentOrder:o,tabIndex:r,item:e,isScope:s,content:l})})),n.sort(h).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(i)},w=e.concat("iframe").join(",");t.focusable=function(t,e){return(e=e||{}).getShadowRoot?l([t],e.includeContainer,{filter:f.bind(null,e),flatten:!0,getShadowRoot:e.getShadowRoot}):r(t,e.includeContainer,f.bind(null,e))},t.getTabIndex=c,t.isFocusable=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==o.call(t,w)&&f(e,t)},t.isTabbable=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==o.call(t,i)&&m(e,t)},t.tabbable=function(t,e){var i;return i=(e=e||{}).getShadowRoot?l([t],e.includeContainer,{filter:m.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:g}):r(t,e.includeContainer,m.bind(null,e)),v(i)},Object.defineProperty(t,"__esModule",{value:!0})})),function(t,e){var i,n;"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("tabbable")):"function"==typeof define&&define.amd?define(["exports","tabbable"],e):(t="undefined"!=typeof globalThis?globalThis:t||self,i=t.focusTrap,n=t.focusTrap={},e(n,t.tabbable),n.noConflict=function(){return t.focusTrap=i,n})}(this,(function(t,e){"use strict";function i(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function n(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function o(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?n(Object(o),!0).forEach((function(e){i(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}var s=function(t){return"Tab"===(null==t?void 0:t.key)||9===(null==t?void 0:t.keyCode)},a=function(t){return s(t)&&!t.shiftKey},r=function(t){return s(t)&&t.shiftKey},l=function(t){return setTimeout(t,0)},d=function(t,e){var i=-1;return t.every((function(t,n){return!e(t)||(i=n,!1)})),i},c=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return"function"==typeof t?t.apply(void 0,i):t},h=function(t){return t.target.shadowRoot&&"function"==typeof t.composedPath?t.composedPath()[0]:t.target},u=[];t.createFocusTrap=function(t,i){var n,p=(null==i?void 0:i.document)||document,f=(null==i?void 0:i.trapStack)||u,m=o({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:a,isKeyBackward:r},i),g={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},v=function(t,e,i){return t&&void 0!==t[e]?t[e]:m[i||e]},w=function(t,e){var i="function"==typeof(null==e?void 0:e.composedPath)?e.composedPath():void 0;return g.containerGroups.findIndex((function(e){var n=e.container,o=e.tabbableNodes;return n.contains(t)||(null==i?void 0:i.includes(n))||o.find((function(e){return e===t}))}))},y=function(t){var e=m[t];if("function"==typeof e){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];e=e.apply(void 0,n)}if(!0===e&&(e=void 0),!e){if(void 0===e||!1===e)return e;throw new Error("`".concat(t,"` was specified but was not a node, or did not return a node"))}var s=e;if("string"==typeof e&&!(s=p.querySelector(e)))throw new Error("`".concat(t,"` as selector refers to no known node"));return s},b=function(){var t=y("initialFocus");if(!1===t)return!1;if(void 0===t||!e.isFocusable(t,m.tabbableOptions))if(w(p.activeElement)>=0)t=p.activeElement;else{var i=g.tabbableGroups[0];t=i&&i.firstTabbableNode||y("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},C=function(){if(g.containerGroups=g.containers.map((function(t){var i=e.tabbable(t,m.tabbableOptions),n=e.focusable(t,m.tabbableOptions),o=i.length>0?i[0]:void 0,s=i.length>0?i[i.length-1]:void 0,a=n.find((function(t){return e.isTabbable(t)})),r=n.slice().reverse().find((function(t){return e.isTabbable(t)})),l=!!i.find((function(t){return e.getTabIndex(t)>0}));return{container:t,tabbableNodes:i,focusableNodes:n,posTabIndexesFound:l,firstTabbableNode:o,lastTabbableNode:s,firstDomTabbableNode:a,lastDomTabbableNode:r,nextTabbableNode:function(t){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s=i.indexOf(t);return s<0?o?n.slice(n.indexOf(t)+1).find((function(t){return e.isTabbable(t)})):n.slice(0,n.indexOf(t)).reverse().find((function(t){return e.isTabbable(t)})):i[s+(o?1:-1)]}}})),g.tabbableGroups=g.containerGroups.filter((function(t){return t.tabbableNodes.length>0})),g.tabbableGroups.length<=0&&!y("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(g.containerGroups.find((function(t){return t.posTabIndexesFound}))&&g.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},x=function(t){var e=t.activeElement;if(e)return e.shadowRoot&&null!==e.shadowRoot.activeElement?x(e.shadowRoot):e},_=function(t){!1!==t&&t!==x(document)&&(t&&t.focus?(t.focus({preventScroll:!!m.preventScroll}),g.mostRecentlyFocusedNode=t,function(t){return t.tagName&&"input"===t.tagName.toLowerCase()&&"function"==typeof t.select}(t)&&t.select()):_(b()))},S=function(t){var e=y("setReturnFocus",t);return e||!1!==e&&t},k=function(t){var i=t.target,n=t.event,o=t.isBackward,a=void 0!==o&&o;i=i||h(n),C();var r=null;if(g.tabbableGroups.length>0){var l=w(i,n),c=l>=0?g.containerGroups[l]:void 0;if(l<0)r=a?g.tabbableGroups[g.tabbableGroups.length-1].lastTabbableNode:g.tabbableGroups[0].firstTabbableNode;else if(a){var u=d(g.tabbableGroups,(function(t){var e=t.firstTabbableNode;return i===e}));if(u<0&&(c.container===i||e.isFocusable(i,m.tabbableOptions)&&!e.isTabbable(i,m.tabbableOptions)&&!c.nextTabbableNode(i,!1))&&(u=l),u>=0){var p=0===u?g.tabbableGroups.length-1:u-1,f=g.tabbableGroups[p];r=e.getTabIndex(i)>=0?f.lastTabbableNode:f.lastDomTabbableNode}else s(n)||(r=c.nextTabbableNode(i,!1))}else{var v=d(g.tabbableGroups,(function(t){var e=t.lastTabbableNode;return i===e}));if(v<0&&(c.container===i||e.isFocusable(i,m.tabbableOptions)&&!e.isTabbable(i,m.tabbableOptions)&&!c.nextTabbableNode(i))&&(v=l),v>=0){var b=v===g.tabbableGroups.length-1?0:v+1,x=g.tabbableGroups[b];r=e.getTabIndex(i)>=0?x.firstTabbableNode:x.firstDomTabbableNode}else s(n)||(r=c.nextTabbableNode(i))}}else r=y("fallbackFocus");return r},T=function(t){var e=h(t);w(e,t)>=0||(c(m.clickOutsideDeactivates,t)?n.deactivate({returnFocus:m.returnFocusOnDeactivate}):c(m.allowOutsideClick,t)||t.preventDefault())},I=function(t){var i=h(t),n=w(i,t)>=0;if(n||i instanceof Document)n&&(g.mostRecentlyFocusedNode=i);else{var o;t.stopImmediatePropagation();var s=!0;if(g.mostRecentlyFocusedNode)if(e.getTabIndex(g.mostRecentlyFocusedNode)>0){var a=w(g.mostRecentlyFocusedNode),r=g.containerGroups[a].tabbableNodes;if(r.length>0){var l=r.findIndex((function(t){return t===g.mostRecentlyFocusedNode}));l>=0&&(m.isKeyForward(g.recentNavEvent)?l+1<r.length&&(o=r[l+1],s=!1):l-1>=0&&(o=r[l-1],s=!1))}}else g.containerGroups.some((function(t){return t.tabbableNodes.some((function(t){return e.getTabIndex(t)>0}))}))||(s=!1);else s=!1;s&&(o=k({target:g.mostRecentlyFocusedNode,isBackward:m.isKeyBackward(g.recentNavEvent)})),_(o||g.mostRecentlyFocusedNode||b())}g.recentNavEvent=void 0},z=function(t){(m.isKeyForward(t)||m.isKeyBackward(t))&&function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];g.recentNavEvent=t;var i=k({event:t,isBackward:e});i&&(s(t)&&t.preventDefault(),_(i))}(t,m.isKeyBackward(t))},L=function(t){var e;"Escape"!==(null==(e=t)?void 0:e.key)&&"Esc"!==(null==e?void 0:e.key)&&27!==(null==e?void 0:e.keyCode)||!1===c(m.escapeDeactivates,t)||(t.preventDefault(),n.deactivate())},E=function(t){var e=h(t);w(e,t)>=0||c(m.clickOutsideDeactivates,t)||c(m.allowOutsideClick,t)||(t.preventDefault(),t.stopImmediatePropagation())},O=function(){if(g.active)return function(t,e){if(t.length>0){var i=t[t.length-1];i!==e&&i.pause()}var n=t.indexOf(e);-1===n||t.splice(n,1),t.push(e)}(f,n),g.delayInitialFocusTimer=m.delayInitialFocus?l((function(){_(b())})):_(b()),p.addEventListener("focusin",I,!0),p.addEventListener("mousedown",T,{capture:!0,passive:!1}),p.addEventListener("touchstart",T,{capture:!0,passive:!1}),p.addEventListener("click",E,{capture:!0,passive:!1}),p.addEventListener("keydown",z,{capture:!0,passive:!1}),p.addEventListener("keydown",L),n},P=function(){if(g.active)return p.removeEventListener("focusin",I,!0),p.removeEventListener("mousedown",T,!0),p.removeEventListener("touchstart",T,!0),p.removeEventListener("click",E,!0),p.removeEventListener("keydown",z,!0),p.removeEventListener("keydown",L),n},D="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(t){t.some((function(t){return Array.from(t.removedNodes).some((function(t){return t===g.mostRecentlyFocusedNode}))}))&&_(b())})):void 0,A=function(){D&&(D.disconnect(),g.active&&!g.paused&&g.containers.map((function(t){D.observe(t,{subtree:!0,childList:!0})})))};return(n={get active(){return g.active},get paused(){return g.paused},activate:function(t){if(g.active)return this;var e=v(t,"onActivate"),i=v(t,"onPostActivate"),n=v(t,"checkCanFocusTrap");n||C(),g.active=!0,g.paused=!1,g.nodeFocusedBeforeActivation=p.activeElement,null==e||e();var o=function(){n&&C(),O(),A(),null==i||i()};return n?(n(g.containers.concat()).then(o,o),this):(o(),this)},deactivate:function(t){if(!g.active)return this;var e=o({onDeactivate:m.onDeactivate,onPostDeactivate:m.onPostDeactivate,checkCanReturnFocus:m.checkCanReturnFocus},t);clearTimeout(g.delayInitialFocusTimer),g.delayInitialFocusTimer=void 0,P(),g.active=!1,g.paused=!1,A(),function(t,e){var i=t.indexOf(e);-1!==i&&t.splice(i,1),t.length>0&&t[t.length-1].unpause()}(f,n);var i=v(e,"onDeactivate"),s=v(e,"onPostDeactivate"),a=v(e,"checkCanReturnFocus"),r=v(e,"returnFocus","returnFocusOnDeactivate");null==i||i();var d=function(){l((function(){r&&_(S(g.nodeFocusedBeforeActivation)),null==s||s()}))};return r&&a?(a(S(g.nodeFocusedBeforeActivation)).then(d,d),this):(d(),this)},pause:function(t){if(g.paused||!g.active)return this;var e=v(t,"onPause"),i=v(t,"onPostPause");return g.paused=!0,null==e||e(),P(),A(),null==i||i(),this},unpause:function(t){if(!g.paused||!g.active)return this;var e=v(t,"onUnpause"),i=v(t,"onPostUnpause");return g.paused=!1,null==e||e(),C(),O(),A(),null==i||i(),this},updateContainerElements:function(t){var e=[].concat(t).filter(Boolean);return g.containers=e.map((function(t){return"string"==typeof t?p.querySelector(t):t})),g.active&&C(),A(),this}}).updateContainerElements(t),n},Object.defineProperty(t,"__esModule",{value:!0})})),function(t){function e(){return t('<span class="avatar no-avatar"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16;" xml:space="preserve"><path d="M8,8c2.2,0,4-1.8,4-4s-1.8-4-4-4S4,1.8,4,4S5.8,8,8,8z M8,10c-2.7,0-8,1.3-8,4v1c0,0.5,0.4,1,1,1h14c0.5,0,1-0.5,1-1v-1 C16,11.3,10.7,10,8,10z"/></svg></span>')}var i;function n(e,i,n){e.addClass("animation-triggered"),setTimeout((function(){e.hasClass("animation-triggered")&&(e.removeClass("animation-triggered").addClass("shown"),t().layzrInitialisation(),"function"==typeof n&&n.call(e))}),i)}t.fn.exists=function(){return t(this).length>0},t.fn.loaded=function(e,i,n){var o=this.length;return o>0?this.each((function(){var n=this,s=t(n);s.on("load.dt",(function(n){t(this).off("load.dt"),"function"==typeof e&&e.call(this),--o<=0&&"function"==typeof i&&i.call(this)})),n.complete&&void 0!==n.complete&&s.trigger("load.dt")})):n?("function"==typeof i&&i.call(this),this):void 0},dtGlobals.isInViewport=function(e){var i=t(window),n=e,o=i.scrollTop(),s=i.scrollTop()+i.height(),a=n.offset().top+20;return s>=a&&o<=a},t.fn.layzrInitialisation=function(){void 0!==i?(this.each((function(){var e=t(this);i.addItems(e[0])})),i.update()):i=new Layzr({selector:".lazy-load",attr:"data-src",attrSrcSet:"data-srcset",retinaAttr:"data-src-retina",hiddenAttr:"data-src-hidden",threshold:0,callback:function(){var i=t(this);if(i.hasClass("the7-avatar")&&1===this.naturalWidth){const t=e();i.replaceWith(t),i=t}showLazyImg(i),i.parents(".fancy-media-wrap.photoswipe-wrapper").initPhotoswipe()},before:function(){var i=t(this);i.hasClass("the7-avatar")&&i.on("error",(function(){const t=e();i.replaceWith(t),showLazyImg(t)}))},after:function(){var e=t(this);this.complete&&showLazyImg(e)}})},dtGlobals.addOnloadEvent=function(e){if(("object"==typeof t.ready||"function"==typeof t.ready)&&"function"==typeof t.ready.then)t.ready.then((function(){e()}));else if(void 0!==window.addEventListener)window.addEventListener("load",e,!1);else if(void 0!==window.attachEvent)window.attachEvent("onload",e);else if(null!=window.onload){var i=window.onload;window.onload=function(t){i(t),window[e]()}}else window.onload=e},t.fn.the7ImageRatio=function(e){var i=function(){return!(void 0!==Modernizr.objectfit&&Modernizr.objectfit||void 0===Modernizr.objectfit)},n={init:function(){return i()?this.each((function(){var e=t(this),i=e.parent(),n=e.prop("src");n&&!e.hasClass("compat-object-fit")&&i.css("backgroundImage","url("+n+")").addClass("compat-object-fit")})):this},update:function(e){var n=i();return this.each((function(){var i=t(this),o=i.find("img").first();if(!n){var s={width:"",height:""};if(e){var a=i.width(),r=i.height(),l=o.width()/o.height(),d=a/r;s={width:"auto",height:"auto"},l<d?s.height="100%":l>d&&(s.width="100%")}o.css(s)}}))}};return n[e]?n[e].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof e&&e?void 0:n.init.apply(this,arguments)},t.fn.the7WidgetImageRatio=function(e){var i=t(this),n=i.find(".img-ratio-wrapper"),o={init:function(){n.find("img").the7ImageRatio(),o.refresh(),t(window).on("debouncedresize",(function(){o.refresh()}))},refresh:function(){var t=i.hasClass("preserve-img-ratio-y");n.the7ImageRatio("update",t)}};return o[e]?o[e].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof e&&e||o.init.apply(this,arguments),i},window.the7ApplyWidgetImageRatio=function(t){t.the7WidgetImageRatio()},window.the7GetHiddenHeight=function(t,e){if(!t.length)return 0;var i=t.attr("style");t.css({visibility:"hidden",display:"block"});var n=t;""!==e&&(n=t.find(e));var o=n.height();return t.attr("style",i||""),o},window.the7LocaleSensitiveStringsCompare=function(t,e){return new Intl.Collator(document.documentElement.lang,{sensitivity:"base"}).compare(t,e)},window.the7ProcessEffects=function(e,i){var o=0;0!==e.length?e.each((function(){var e=t(this);t(".mobile-true").length>0||e.parents(".loading-effect-none").length>0?e.hasClass("shown")||e.hasClass("animation-triggered")||n(e,200,i):e.hasClass("shown")||e.hasClass("animation-triggered")||!dtGlobals.isInViewport(e)||e.hasClass("hidden")||n(e,100*++o,i)})):t().layzrInitialisation()}}(jQuery),jQuery((function(t){var i=t(document),n=t(window),o=t("html"),s=t("body"),a=t("#page"),r=function(t,e,i=!1){let n;return function(){const o=this,s=arguments;clearTimeout(n),n=setTimeout((()=>{n=null,t.apply(o,s)}),e),i&&t.apply(o,s)}};
/*!- Custom resize function*/
!dtGlobals.isMobile||dtGlobals.isWindowsPhone||dtGlobals.isAndroid?n.on("resize",(function(){l()})):n.bind("orientationchange",(function(){l()}));var l=r((function(){t(window).trigger("debouncedresize"),t(window).trigger("the7_widget_resize")}),200);n.trigger("debouncedresize");var d,c,h=window.innerWidth,u=window.innerHeight;function p(){if(dtGlobals.isMobile)t(".skills").length>0&&"undefined"!=typeof animateSkills&&the7Utils.isFunction(animateSkills)&&t(".skills").animateSkills();else if(t(".animation-at-the-same-time").length>0||t(".animate-element").length>0){var e=-1;t(".animation-at-the-same-time:in-viewport").each((function(){var e=t(this);e.find(".animate-element").addClass("animation-triggered"),e.find(".animate-element:not(.start-animation)").addClass("start-animation")})),t(".animate-element:not(.start-animation):in-viewport").each((function(){var i=t(this);!i.parents(".animation-at-the-same-time").length>0&&(i.hasClass("start-animation")||i.hasClass("animation-triggered")||(i.addClass("animation-triggered"),e++,setTimeout((function(){i.addClass("start-animation"),i.hasClass("skills")&&i.animateSkills()}),200*e)))}))}}onWindowWidthResizeDebounced=r((function(){n.trigger("the7-resize-width-debounce")}),250),onWindowHeightResizeDebounced=r((function(){n.trigger("the7-resize-height-debounce")}),250),n.on("resize",(function(){var t=window.innerWidth,e=window.innerHeight;h!==t&&(h=t,n.trigger("the7-resize-width"),onWindowWidthResizeDebounced()),u!==e&&(u=e,n.trigger("the7-resize-height"),onWindowHeightResizeDebounced())})),t.fn.layzrCarouselUpdate=function(){var e=t(this),i=e.find(".dt-owl-item.active"),n=".owl-thumb-lazy-load-show",o=!1,s=i.last().next().find("img").not(n);(s=(s=(s=s.add(i.first().prev().find("img").not(n))).add(i.find("img").not(n))).filter(":not(.is-loaded)")).each((function(){var i=t(this).attr("data-src");if(void 0!==i){o=!0;var s=e.find('.dt-owl-item img[data-src="'+i+'"]').not(n);s.addClass("owl-thumb-lazy-load-show"),s.attr("loading","eager")}})),o&&(d.updateSelector(),d.update())},t(".dt-owl-carousel-call img.lazy-load, .related-projects img.lazy-load, .elementor-owl-carousel-call img.lazy, .elementor-owl-carousel-call img.lazy-load, .nsContent img.lazy-load").removeClass("lazy-load").addClass("lazy-scroll"),t(".layzr-loading-on, .vc_single_image-img").layzrInitialisation(),window.vc_rowBehaviour=function(){function t(){a('[data-vc-full-width="true"]').each((function(){var t,e,i=a(this),o=i.next(".vc_row-full-width"),r=i.parent(),l=parseInt(i.css("margin-left"),10),d=parseInt(i.css("margin-right"),10),c=window.innerWidth,h=n.width(),u=a("#content").width(),p=c>dtLocal.themeSettings.mobileHeader.firstSwitchPoint,f=Math.max(u,h),m=a("#main > .wf-wrap"),g=parseInt(m.css("width")),v=parseInt(m.css("padding-left")),w=s.hasClass("responsive-off"),y=s.hasClass("sticky-header"),b=s.hasClass("header-side-left"),C=s.hasClass("header-side-right"),x="rtl"===jQuery(document).attr("dir")?"right":"left";if(i.addClass("vc_hidden"),o.length||(o=i.closest(".vc_ie-flexbox-fixer").next(".vc_row-full-width")),o.length){r.hasClass("vc_section")&&(l=parseInt(r.css("margin-left"),10),d=parseInt(r.css("margin-right"),10)),a(".boxed").length>0?f=a("#main").width():w||p&&a(".side-header-v-stroke").length&&"none"!==a(".side-header-v-stroke").css("display")?f=c<=u?u:h-a(".side-header-v-stroke").width():!y&&(b||C)&&p&&a(".side-header").length&&"none"!==a(".side-header").css("display")&&(f=c<=u?u:h-a(".side-header").width()),t=Math.ceil((f-g+2*v)/2),a(".sidebar-left").length>0||a(".sidebar-right").length>0?(e=a("#content").width(),t=0):e=a("#main").innerWidth();var _=0-t-l;if(i.css(x,_),i.css({position:"relative","box-sizing":"border-box",width:e}),!i.data("vcStretchContent")){var S=-1*_;0>S&&(S=0);var k=e-S-o.width()+l+d;0>k&&(k=0),i.css({"padding-left":S+"px","padding-right":k+"px"})}i.data("vcStretchContent")&&i.find(".upb_row_bg").length>0&&i.find(".upb_row_bg").each((function(){var t=a(this);"full"===t.data("bg-override")&&(t.css({"min-width":e+"px"}),t.css(x,0))})),i.attr("data-vc-full-width-init","true"),i.removeClass("vc_hidden"),i.find(".ts-wrap").each((function(){var t=a(this).data("thePhotoSlider");void 0!==t&&t.update()}))}}))}function e(){a(".vc_row-o-full-height:first").each((function(){var t,e,i;(t=a(window).height())>(e=a(this).offset().top)&&(i=100-e/(t/100),a(this).css("min-height",i+"vh"))}))}var i,o,a=window.jQuery;a(window).off("resize.vcRowBehaviour").on("resize.vcRowBehaviour",t).on("resize.vcRowBehaviour",e),t(),e(),(window.navigator.userAgent.indexOf("MSIE ")>0||navigator.userAgent.match(/Trident.*rv\:11\./))&&a(".vc_row-o-full-height").each((function(){var t=a(this);t.data("the7VCRowFixedInIE")||"flex"!==t.css("display")||(t.wrap('<div class="vc_ie-flexbox-fixer"></div>'),t.data("the7VCRowFixedInIE",!0))})),vc_initVideoBackgrounds(),o=!1,window.vcParallaxSkroll&&window.vcParallaxSkroll.destroy(),a(".vc_parallax-inner").remove(),a("[data-5p-top-bottom]").removeAttr("data-5p-top-bottom data-30p-top-bottom"),a("[data-vc-parallax]").each((function(){var t,e,i,n,s;o=!0,"on"===a(this).data("vcParallaxOFade")&&a(this).children().attr("data-5p-top-bottom","opacity:0;").attr("data-30p-top-bottom","opacity:1;"),t=100*a(this).data("vcParallax"),(i=a("<div />").addClass("vc_parallax-inner").appendTo(a(this))).height(t+"%"),n=a(this).data("vcParallaxImage"),(s=vcExtractYoutubeId(n))?insertYoutubeVideoAsBackground(i,s):void 0!==n&&i.css("background-image","url("+n+")"),e=-(t-100),i.attr("data-bottom-top","top: "+e+"%;").attr("data-top-bottom","top: 0%;")})),o&&window.skrollr&&(i={forceHeight:!1,smoothScrolling:!1,mobileCheck:function(){return!1}},window.vcParallaxSkroll=skrollr.init(i),window.vcParallaxSkroll)},t('div[data-vc-full-width="true"][data-vc-full-width-init="false"]').length>0&&vc_rowBehaviour(),t.fn.clickOverlayGradient=function(){return this.each((function(){var e=t(this),i=0,n=e.find(".entry-excerpt"),o=e.find(".post-details, .box-button");n.exists()&&(i+=n.height()),o.exists()&&(i+=o.innerHeight()),e.data("the7OverlayLayoutContentOffset",i),e.css({transform:"translateY("+i+"px)"}),e.data("overlayLayoutEventsWasAdded")||dtGlobals.isMobile||(e.data("overlayLayoutEventsWasAdded",!0),e.parents(".post").first().on("mouseenter tap",(function(){e.css("transform","translateY(0px)")})).on("mouseleave tap",(function(){e.css("transform","translateY("+e.data("the7OverlayLayoutContentOffset")+"px)")})))}))},dtGlobals.addOnloadEvent((function(){t(".content-rollover-layout-list:not(.disable-layout-hover) .dt-css-grid .post-entry-wrapper").clickOverlayGradient()})),n.on("elementor/frontend/init",(function(){"undefined"!==elementorFrontend&&elementorFrontend.hooks.addAction("search:results-updated",(function(){t(".e-search-results").layzrInitialisation()}))})),dtGlobals.touches={},dtGlobals.touches.touching=!1,dtGlobals.touches.touch=!1,dtGlobals.touches.currX=0,dtGlobals.touches.currY=0,dtGlobals.touches.cachedX=0,dtGlobals.touches.cachedY=0,dtGlobals.touches.count=0,dtGlobals.resizeCounter=0,i.on("touchstart",(function(e){1==e.originalEvent.touches.length&&(dtGlobals.touches.touch=e.originalEvent.touches[0],dtGlobals.touches.cachedX=dtGlobals.touches.touch.pageX,dtGlobals.touches.cachedY=dtGlobals.touches.touch.pageY,dtGlobals.touches.touching=!0,setTimeout((function(){dtGlobals.touches.currX=dtGlobals.touches.touch.pageX,dtGlobals.touches.currY=dtGlobals.touches.touch.pageY,dtGlobals.touches.cachedX!==dtGlobals.touches.currX||dtGlobals.touches.touching||dtGlobals.touches.cachedY!==dtGlobals.touches.currY||(dtGlobals.touches.count++,t(e.target).trigger("tap"))}),200))})),i.on("touchend touchcancel",(function(t){dtGlobals.touches.touching=!1})),i.on("touchmove",(function(t){dtGlobals.touches.touch=t.originalEvent.touches[0],dtGlobals.touches.touching})),i.on("tap",(function(e){t(".dt-hovered").trigger("mouseout")})),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}((function(t,e){var i=6,n=t.event.add,o=t.event.remove,s=function(e,i,n){t.event.trigger(i,n,e)},a=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t,e){return window.setTimeout((function(){t()}),25)},r={textarea:!0,input:!0,select:!0,button:!0},l={move:"mousemove",cancel:"mouseup dragstart",end:"mouseup"},d="touchmove",c="touchend",h="touchend";function u(t){var e=t,i=!1,n=!1;function o(t){i?(e(),a(o),n=!0,i=!1):n=!1}this.kick=function(t){i=!0,n||o()},this.end=function(t){var o=e;t&&(n?(e=i?function(){o(),t()}:t,i=!0):t())}}function p(){return!0}function f(){return!1}function m(t){t.preventDefault()}function g(t){r[t.target.tagName.toLowerCase()]||t.preventDefault()}function v(t,e){var i,n;if(t.identifiedTouch)return t.identifiedTouch(e);for(i=-1,n=t.length;++i<n;)if(t[i].identifier===e)return t[i]}function w(t,e){var i=v(t.changedTouches,e.identifier);if(i&&(i.pageX!==e.pageX||i.pageY!==e.pageY))return i}function y(t){k(t,t.data,t,C)}function b(t){C()}function C(){o(document,l.move,y),o(document,l.cancel,b)}function x(t){var e=t.data,i=w(t,e);i&&k(t,e,i,S)}function _(t){var e=t.data;v(t.changedTouches,e.identifier)&&S(e.identifier)}function S(t){o(document,"."+t,x),o(document,"."+t,_)}function k(t,e,n,o){var a=n.pageX-e.startX,r=n.pageY-e.startY;a*a+r*r<i*i||function(t,e,i,n,o,a){var r,l;e.target;r=t.targetTouches,l=t.timeStamp-e.timeStamp,e.type="movestart",e.distX=n,e.distY=o,e.deltaX=n,e.deltaY=o,e.pageX=i.pageX,e.pageY=i.pageY,e.velocityX=n/l,e.velocityY=o/l,e.targetTouches=r,e.finger=r?r.length:1,e._handled=T,e._preventTouchmoveDefault=function(){t.preventDefault()},s(e.target,e),a(e.identifier)}(t,e,n,a,r,o)}function T(){return this._handled=p,!1}function I(t){t._handled()}function z(t){var e=t.data.timer;t.data.touch=t,t.data.timeStamp=t.timeStamp,e.kick()}function L(t){var e=t.data.event,i=t.data.timer;o(document,l.move,z),o(document,l.end,L),P(e,i,(function(){setTimeout((function(){o(e.target,"click",f)}),0)}))}function E(t){var e=t.data.event,i=t.data.timer,n=w(t,e);n&&(t.preventDefault(),e.targetTouches=t.targetTouches,t.data.touch=n,t.data.timeStamp=t.timeStamp,i.kick())}function O(t){var e=t.data.event,i=t.data.timer;v(t.changedTouches,e.identifier)&&(!function(t){o(document,"."+t.identifier,E),o(document,"."+t.identifier,O)}(e),P(e,i))}function P(t,e,i){e.end((function(){return t.type="moveend",s(t.target,t),i&&i()}))}t.event.special.movestart={setup:function(t,e,i){return n(this,"movestart.move",I),!0},teardown:function(t){return o(this,"dragstart drag",m),o(this,"mousedown touchstart",g),o(this,"movestart",I),!0},add:function(t){"move"!==t.namespace&&"moveend"!==t.namespace&&(n(this,"dragstart."+t.guid+" drag."+t.guid,m,e,t.selector),n(this,"mousedown."+t.guid,g,e,t.selector))},remove:function(t){"move"!==t.namespace&&"moveend"!==t.namespace&&(o(this,"dragstart."+t.guid+" drag."+t.guid),o(this,"mousedown."+t.guid))},_default:function(t){var i,o;t._handled()&&(i={target:t.target,startX:t.startX,startY:t.startY,pageX:t.pageX,pageY:t.pageY,distX:t.distX,distY:t.distY,deltaX:t.deltaX,deltaY:t.deltaY,velocityX:t.velocityX,velocityY:t.velocityY,timeStamp:t.timeStamp,identifier:t.identifier,targetTouches:t.targetTouches,finger:t.finger},o={event:i,timer:new u((function(e){!function(t,e,i,n){var o=i-t.timeStamp;t.type="move",t.distX=e.pageX-t.startX,t.distY=e.pageY-t.startY,t.deltaX=e.pageX-t.pageX,t.deltaY=e.pageY-t.pageY,t.velocityX=.3*t.velocityX+.7*t.deltaX/o,t.velocityY=.3*t.velocityY+.7*t.deltaY/o,t.pageX=e.pageX,t.pageY=e.pageY}(i,o.touch,o.timeStamp),s(t.target,i)})),touch:e,timeStamp:e},t.identifier===e?(n(t.target,"click",f),n(document,l.move,z,o),n(document,l.end,L,o)):(t._preventTouchmoveDefault(),n(document,d+"."+t.identifier,E,o),n(document,h+"."+t.identifier,O,o)))}},t.event.special.move={setup:function(){n(this,"movestart.move",t.noop)},teardown:function(){o(this,"movestart.move",t.noop)}},t.event.special.moveend={setup:function(){n(this,"movestart.moveend",t.noop)},teardown:function(){o(this,"movestart.moveend",t.noop)}},n(document,"mousedown.move",(function(t){var e;(function(t){return 1===t.which&&!t.ctrlKey&&!t.altKey})(t)&&(e={target:t.target,startX:t.pageX,startY:t.pageY,timeStamp:t.timeStamp},n(document,l.move,y,e),n(document,l.cancel,b,e))})),n(document,"touchstart.move",(function(t){var e,i;r[t.target.tagName.toLowerCase()]||(i={target:(e=t.changedTouches[0]).target,startX:e.pageX,startY:e.pageY,timeStamp:t.timeStamp,identifier:e.identifier},n(document,d+"."+e.identifier,x,i),n(document,c+"."+e.identifier,_,i))})),"function"==typeof Array.prototype.indexOf&&function(t,e){for(var i=["changedTouches","targetTouches"],n=i.length;n--;)-1===t.event.props.indexOf(i[n])&&t.event.props.push(i[n])}(t)})),t.belowthefold=function(e,i){return n.height()+n.scrollTop()<=t(e).offset().top-i.threshold},t.abovethetop=function(e,i){return n.scrollTop()>=t(e).offset().top+t(e).height()-i.threshold},t.rightofscreen=function(e,i){return n.width()+n.scrollLeft()<=t(e).offset().left-i.threshold},t.leftofscreen=function(e,i){return n.scrollLeft()>=t(e).offset().left+t(e).width()-i.threshold},t.inviewport=function(e,i){return!(t.rightofscreen(e,i)||t.leftofscreen(e,i)||t.belowthefold(e,i)||t.abovethetop(e,i))},t.extend(t.expr.pseudos,{"below-the-fold":function(e,i,n){return t.belowthefold(e,{threshold:0})},"above-the-top":function(e,i,n){return t.abovethetop(e,{threshold:0})},"left-of-screen":function(e,i,n){return t.leftofscreen(e,{threshold:0})},"right-of-screen":function(e,i,n){return t.rightofscreen(e,{threshold:0})},"in-viewport":function(e,i,n){return t.inviewport(e,{threshold:-30})}}),t.fn.checkInViewport=function(){if(!dtGlobals.isMobile){var e=-1;return this.each((function(){var i=t(this);i.hasClass("animation-ready")||(i.parents(".animation-at-the-same-time").length>0?($thisElem=i.find(".animate-element"),$thisElem.addClass("animation-triggered"),i.find(".animate-element:not(.start-animation)").addClass("start-animation")):i.hasClass("start-animation")||i.hasClass("animation-triggered")||(i.addClass("animation-triggered"),e++,setTimeout((function(){i.addClass("start-animation"),i.hasClass("skills")&&i.animateSkills()}),200*e)),i.addClass("animation-ready"))}))}"undefined"!=typeof animateSkills&&the7Utils.isFunction(animateSkills)&&t(".skills").animateSkills()},clearTimeout(c),c=setTimeout((function(){p()}),50),dtGlobals.isMobile||n.on("scroll",(function(){p()}));var f=t(".dt-owl-carousel-call, .related-projects, .elementor-owl-carousel-call");n.on("scroll",(function(){f.each((function(){var e=t(this);if("true"===e.attr("data-autoplay"))if(dtGlobals.isInViewport(e)){var i=e.attr("data-autoplay_speed")?parseInt(e.attr("data-autoplay_speed")):6e3;e.trigger("play.owl.autoplay",[i])}else e.trigger("stop.owl.autoplay")}))})),t.fn.addPhotoswipeWrap=function(){return this.each((function(e,i){var n=t(this);n.on("click",(function(t){t.preventDefault()})),n.parents("figure").first().addClass("photoswipe-item"),n.hasClass("pspw-wrap-ready")||(n.parents().hasClass("dt-gallery-container")||n.parent().addClass("photoswipe-wrapper"),n.addClass("pspw-wrap-ready"))}))},t(".dt-pswp-item, figure .dt-gallery-container a").addPhotoswipeWrap();var m=[{id:"facebook",label:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16"><path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/></svg> '+dtShare.shareButtonText.facebook,url:"https://www.facebook.com/sharer/sharer.php?u={{url}}&picture={{raw_image_url}}&description={{text}}"},{id:"twitter",label:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16"><path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/></svg>'+dtShare.shareButtonText.twitter,url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pinterest" viewBox="0 0 16 16"><path d="M8 0a8 8 0 0 0-2.915 15.452c-.07-.633-.134-1.606.027-2.297.146-.625.938-3.977.938-3.977s-.239-.479-.239-1.187c0-1.113.645-1.943 1.448-1.943.682 0 1.012.512 1.012 1.127 0 .686-.437 1.712-.663 2.663-.188.796.4 1.446 1.185 1.446 1.422 0 2.515-1.5 2.515-3.664 0-1.915-1.377-3.254-3.342-3.254-2.276 0-3.612 1.707-3.612 3.471 0 .688.265 1.425.595 1.826a.24.24 0 0 1 .056.23c-.061.252-.196.796-.222.907-.035.146-.116.177-.268.107-1-.465-1.624-1.926-1.624-3.1 0-2.523 1.834-4.84 5.286-4.84 2.775 0 4.932 1.977 4.932 4.62 0 2.757-1.739 4.976-4.151 4.976-.811 0-1.573-.421-1.834-.919l-.498 1.902c-.181.695-.669 1.566-.995 2.097A8 8 0 1 0 8 0z"/></svg> '+dtShare.shareButtonText.pinterest,url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"linkedin",label:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-linkedin" viewBox="0 0 16 16"><path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/></svg> '+dtShare.shareButtonText.linkedin,url:"http://www.linkedin.com/shareArticle?mini=true&url={{url}}&title={{text}}"},{id:"whatsapp",label:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16"><path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z"/></svg>'+dtShare.shareButtonText.whatsapp,url:"whatsapp://send?text={{url}}"}];dtShare.shareButtonText.download&&m.push({id:"download",label:'<i class="dt-icon-the7-misc-006-244" aria-hidden="true"></i> '+dtShare.shareButtonText.download,url:"{{raw_image_url}}",download:!0});var g={youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%",type:"youtube"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%",type:"vimeo"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}};function v(e){for(var i in e){var n=e[i],o=t(".pswp__share-tooltip");switch(n){case"facebook":o.addClass("show-share-fb");break;case"twitter":o.addClass("show-share-tw");break;case"pinterest":o.addClass("show-share-pin");break;case"linkedin":o.addClass("show-share-in");break;case"whatsapp":o.addClass("show-share-wp");break;case"download":o.addClass("show-share-d");break;default:o.removeClass("show-share-in show-share-pin show-share-tw show-share-fb show-share-g show-share-wp")}}}function w(e,i){var n=[],o=e;o=t.extend({target:".dt-pswp-item",embedType:""},o);var s=function(e,i,s){var a=document.querySelectorAll(".pswp")[0],r=n[e-1].items,l={index:i,galleryUID:e,shareEl:!0,closeOnScroll:!1,history:!1,shareButtons:m,getImageURLForShare:function(t){return c.currItem.src||""},getPageURLForShare:function(t){return c.currItem.shareLink||window.location.href},getTextForShare:function(t){var e=c.currItem.title;return(void 0!==e?e.replace(/<[^>]+>/g,""):"")||""},parseShareButtonOut:function(t,e){return e}},d="";void 0!==t(s).next(".dt-gallery-container").attr("data-pretty-share")&&(d=t(s).next(".dt-gallery-container").attr("data-pretty-share").split(",")),void 0!==t(s).parents(".dt-gallery-container").attr("data-pretty-share")?d=t(s).parents(".dt-gallery-container").attr("data-pretty-share").split(","):void 0!==t(s).parents(".shortcode-single-image-wrap").attr("data-pretty-share")?d=t(s).parents(".shortcode-single-image-wrap").attr("data-pretty-share").split(","):void 0!==t(s).attr("data-pretty-share")&&(d=t(s).attr("data-pretty-share").split(",")),d.length<=0||void 0===d?t(".pswp__scroll-wrap").addClass("hide-pspw-share"):d.push("download"),v(d),t.extend(l,o);var c=new PhotoSwipe(a,PhotoSwipeUI_Default,r,l);c.init(),t(".pswp__video").removeClass("active");var h=t(c.currItem.container);h.find(".pswp__video").length>0&&h.parents(".pswp").addClass("video-active"),t(".pswp__zoom-wrap").removeClass("active-item"),h.addClass("active-item");var u,p,f=h.find(".pswp__video");if(f.length>0)if(f.addClass("active"),h.parents(".pswp").addClass("video-active"),"hosted"==f.parents(".pswp-video-wrap ").attr("data-type"))f.get(0).play();else{var g=f.prop("src");g+="?autoplay=1",f.prop("src",g)}c.listen("beforeChange",(function(){var e=t(c.currItem.container);t(".pswp__zoom-wrap").removeClass("active-item"),e.addClass("active-item"),t(".pswp__video").removeClass("active"),t(".pswp").removeClass("video-active");e.find(".pswp__video").addClass("active");e.find(".pswp__video").length>0&&e.parents(".pswp").addClass("video-active"),t(".pswp__video").each((function(){var e=t(this);if(e.hasClass("active"))"hosted"==e.parents(".pswp-video-wrap ").attr("data-type")&&e.get(0).play();else if("hosted"==e.parents(".pswp-video-wrap ").attr("data-type"))e.get(0).pause();else{var i=e.prop("src");i=i.replace("?autoplay=1","?enablejsapi=1"),e.prop("src",""),e.prop("src",i),t(".pswp__video").removeClass("active");var n=t(this)[0].contentWindow;e.hasClass("active")?func="playVideo":func="pauseVideo",n.postMessage('{"event":"command","func":"'+func+'","args":""}',"*")}}))})),c.listen("close",(function(){t(".pswp__video").each((function(){var e=t(this);if("hosted"==e.parents(".pswp-video-wrap ").attr("data-type"))e.get(0).pause();else{e.attr("src",e.attr("src"));var i=e.prop("src");i=i.replace("?autoplay=1","?enablejsapi=1"),e.prop("src",""),e.prop("src",i),t(".pswp__video").removeClass("active");var n=t(this)[0].contentWindow;e.hasClass("active")?func="playVideo":func="stopVideo",n.postMessage('{"event":"command","func":"'+func+'","args":""}',"*")}clearTimeout(u),u=setTimeout((function(){t(".pswp-video-wrap").remove()}),200)}))})),c.listen("destroy",(function(){clearTimeout(p),p=setTimeout((function(){t(".pswp").removeClass().addClass("pswp")}),100)}))};!function(e,i){n=[],e.each((function(e,i){n.push({id:e,items:[]}),t(i).find(o.target).each((function(i,s){var a,r=t(s),l=r.attr("data-large_image_width"),d=void 0!==l&&l.length>0&&""!=l&&0!=l?l:r.find("img").attr("width"),c=r.attr("data-large_image_height"),h=void 0!==c&&c.length>0&&""!=c&&0!=c?c:r.find("img").attr("height"),u=(void 0!==r.attr("title")&&r.attr("title").length>0?"<h5>"+r.attr("title")+"</h5>":"")+(void 0!==r.attr("data-dt-img-description")?r.attr("data-dt-img-description"):"")||"";if(r.data("gallery-id",e+1),r.data("photo-id",i),void 0===d&&(d=r.find("img").width()),void 0===h&&(h=r.find("img").height()),t(s).hasClass("pswp-video")){var p=s.href,f=o.embedType;r.find("video")&&(a=r.find("video").attr("poster")),t.each(g,(function(){if(p.indexOf(this.index)>-1)return this.id&&(p="string"==typeof this.id?p.substr(p.lastIndexOf(this.id)+this.id.length,p.length):this.id.call(this,p),f=this.type),p=this.src.replace("%id%",p),!1}));var m={html:'<div class="pswp-video-wrap " data-type="'+f+'"><div class="video-wrapper"><iframe allow="autoplay; fullscreen" class="pswp__video"src="'+p+' " frameborder="0" allowfullscreen></iframe></div></div>',title:u,shareLink:r.attr("data-dt-location")||r.parents(".fancy-media-wrap").find("img").attr("data-dt-location")||""};"hosted"==f&&(m.html='<div class="pswp-video-wrap " data-type="'+f+'"><div class="video-wrapper"><video controls playsinline="yes" poster="'+a+' " class="pswp__video" src="'+p+'" type="video/mp4"></video ></div></div>')}else m={src:s.href,w:d,h:h,title:u,shareLink:r.attr("data-dt-location")||r.find("img").attr("data-dt-location")||""};n[e].items.push(m)})),t(i).prev().hasClass("dt-gallery-pswp")?t(i).prev(".dt-gallery-pswp").on("click",(function(e){e.preventDefault();var n=t(this),a=t(this).next(t(i)).find(o.target),r=a.data("gallery-id"),l=a.data("photo-id");n.parents(".ts-wrap").hasClass("ts-interceptClicks")||n.hasClass("dt-pswp-item-no-click")||s(r,l,n)})):t(i).on("click",o.target,(function(e){var i=t(this);e.preventDefault();var n=t(this).data("gallery-id"),o=t(this).data("photo-id");i.parents(".ts-wrap").hasClass("ts-interceptClicks")||i.hasClass("dt-pswp-item-no-click")||s(n,o,i)}))}))}(i);var a=function(){var t=window.location.hash.substring(1),e={};if(t.length<5)return e;for(var i=t.split("&"),n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(e[o[0]]=o[1])}return e.gid&&(e.gid=parseInt(e.gid,10)),e.hasOwnProperty("pid")?(e.pid=parseInt(e.pid,10),e):e}();return a.pid>0&&a.gid>0&&s(a.gid,a.pid),this}function y(e,i){if(void 0!==t.fn[i])return e.each((function(){t(this)[i]({bgOpacity:dtShare.overlayOpacity/100,loop:!0,showHideOpacity:!0})}))}
/**
 * Owl carousel
 * @version 2.3.4
 * <AUTHOR> Wojciechowski
 * <AUTHOR> Deutsch
 * @license The MIT License (MIT)
 * @todo Lazy Load Icon
 * @todo prevent animationend bubling
 * @todo itemsScaleUp
 * @todo Test Zepto
 * @todo stagePadding calculate wrong active classes
 */
if(t.fn.photoswipeGallery=function(e){var i=function t(e,i){return e&&(i(e)?e:t(e.parentNode,i))};for(var n=function(e){var n=e.target||e.srcElement,s=i(n,(function(t){return e="photoswipe-item",(" "+t.className+" ").indexOf(" "+e+" ")>-1;var e}));if(s){for(var a,r=t(s).closest(".dt-gallery-container")[0],l=t(t(s).closest(".dt-gallery-container")[0]).find(".photoswipe-item").get(),d=l.length,c=0,h=0;h<d;h++)if(1===l[h].nodeType){if(l[h]===s){a=c;break}c++}return a>=0&&o(a,r),!1}},o=function(e,i,n,o){var s,a,r,l=document.querySelectorAll(".pswp")[0];r=function(e){for(var i,n,o=t(e).find(".photoswipe-item").get(),s=o.length,a=[],r=0;r<s;r++)if(1===(i=o[r]).nodeType){n=i.children[0];var l=void 0!==t(n).attr("data-large_image_width")&&t(n).attr("data-large_image_width").length>0&&""!=t(n).attr("data-large_image_width")?t(n).attr("data-large_image_width"):t(n).find("img").attr("width"),d=void 0!==t(n).attr("data-large_image_height")&&t(n).attr("data-large_image_height").length>0&&""!=t(n).attr("data-large_image_height")?t(n).attr("data-large_image_height"):t(n).find("img").attr("height"),c=(void 0!==t(n).attr("title")&&t(n).attr("title").length>0?"<h5>"+t(n).attr("title")+"</h5>\n":"")+(void 0!==t(n).attr("data-dt-img-description")?t(n).attr("data-dt-img-description"):"")||"";if(t(n).hasClass("pswp-video")){var h,u=n.href;t.each(g,(function(){if(u.indexOf(this.index)>-1)return this.id&&(u="string"==typeof this.id?u.substr(u.lastIndexOf(this.id)+this.id.length,u.length):this.id.call(this,u),h=this.type),u=this.src.replace("%id%",u),!1}));var p={html:'<div class="pswp-video-wrap " data-type="'+h+'"><div class="video-wrapper"><iframe allow="autoplay; fullscreen" class="pswp__video"src=" '+u+' " frameborder="0" allowfullscreen></iframe></div></div>',title:c}}else p={src:n.getAttribute("href"),w:l,h:d,title:c};n.children.length>0&&(p.msrc=n.children[0].getAttribute("src")),p.el=i,a.push(p)}return a}(i);var d=t(i).attr("data-pretty-share")?t(i).attr("data-pretty-share").split(","):new Array;if(d.length<=0||void 0===d?t(".pswp__scroll-wrap").addClass("hide-pspw-share"):d.push("download"),v(d),a={closeOnScroll:!1,galleryUID:i.getAttribute("data-pswp-uid"),bgOpacity:dtShare.overlayOpacity/100,loop:!0,history:!1,showHideOpacity:!0,showAnimationDuration:0,shareButtons:m,getImageURLForShare:function(e){return s.currItem.src||t(s.currItem.el).find("a").attr("data-dt-location")||""},getPageURLForShare:function(e){return t(s.currItem.el).find("a").attr("data-dt-location")||window.location.href},getTextForShare:function(t){var e=s.currItem.title;return(void 0!==e?e.replace(/<[^>]+>/g,""):"")||""},parseShareButtonOut:function(t,e){return e}},o)if(a.galleryPIDs){for(var c=0;c<r.length;c++)if(r[c].pid==e){a.index=c;break}}else a.index=parseInt(e,10)-1;else a.index=parseInt(e,10);if(!isNaN(a.index)){n&&(a.showAnimationDuration=0),(s=new PhotoSwipe(l,PhotoSwipeUI_Default,r,a)).init(),t(".pswp__zoom-wrap").removeClass("active-item"),t(".pswp__video").removeClass("active");var h,u,p=t(s.currItem.container);if(p.addClass("active-item"),p.find(".pswp__video").length>0){p.find(".pswp__video").addClass("active"),p.parents(".pswp").addClass("video-active");var f=p.find(".pswp__video").prop("src");f+="?autoplay=1",p.find(".pswp__video").prop("src",f)}s.listen("beforeChange",(function(){var e=t(s.currItem.container);t(".pswp__zoom-wrap").removeClass("active-item"),e.addClass("active-item"),t(".pswp__video").removeClass("active"),t(".pswp").removeClass("video-active"),e.find(".pswp__video").addClass("active");e.find(".pswp__video");e.find(".pswp__video").length>0&&(e.parents(".pswp").addClass("video-active"),$runVideo=0),t(".pswp__video").each((function(){var e=t(this);if(!e.hasClass("active")){var i=e.prop("src");i="youtube"==e.parents(".pswp-video-wrap ").attr("data-type")||"vimeo"==e.parents(".pswp-video-wrap ").attr("data-type")?i.replace("?autoplay=1","?enablejsapi=1"):i.replace("?autoplay=1",""),e.prop("src",""),e.prop("src",i);var n=e[0].contentWindow;e.hasClass("active")?func="playVideo":func="pauseVideo",n.postMessage('{"event":"command","func":"'+func+'","args":""}',"*")}}))})),s.listen("close",(function(){t(".pswp__video").each((function(){var e=t(this);e.attr("src",t(this).attr("src"));var i=e.prop("src");i="youtube"==e.parents(".pswp-video-wrap ").attr("data-type")||"vimeo"==e.parents(".pswp-video-wrap ").attr("data-type")?i.replace("?autoplay=1","?enablejsapi=1"):i.replace("?autoplay=1",""),e.prop("src",""),e.prop("src",i),t(".pswp__video").removeClass("active");var n=e[0].contentWindow;t(this).hasClass("active")?func="playVideo":func="pauseVideo",n.postMessage('{"event":"command","func":"'+func+'","args":""}',"*"),clearTimeout(h),h=setTimeout((function(){t(".pswp-video-wrap").remove()}),200)}))})),s.listen("destroy",(function(){clearTimeout(u),u=setTimeout((function(){t(".pswp").removeClass().addClass("pswp")}),100)}))}},s=document.querySelectorAll(e),a=0,r=s.length;a<r;a++)s[a].setAttribute("data-pswp-uid",a+1),s[a].onclick=n;var l=function(){var t=window.location.hash.substring(1),e={};if(t.length<5)return e;for(var i=t.split("&"),n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(e[o[0]]=o[1])}return e.gid&&(e.gid=parseInt(e.gid,10)),e}();l.pid&&l.gid&&o(l.pid,s[l.gid-1],!0,!0)},t(".dt-gallery-container.wf-container").photoswipeGallery(".dt-gallery-container.wf-container"),t.fn.photoswipe=function(t){return t.embedType="hosted",w(t,this)},dtGlobals.addOnloadEvent((function(){t(".photoswipe-wrapper, .photoswipe-item .dt-gallery-container, .shortcode-gallery.dt-gallery-container:not(.owl-carousel), .dt-gallery-container.gallery, .instagram-photos.dt-gallery-container, .images-container .dt-gallery-container, .shortcode-instagram.dt-gallery-container, .gallery-shortcode:not(.owl-carousel)").initPhotoswipe()})),t.fn.photoswipeCarousel=function(t){return t.target=".dt-owl-item.cloned .dt-pswp-item",w(t,this)},t.fn.initCarouselClonedPhotoswipe=function(){return y(this,"photoswipeCarousel")},t.fn.initPhotoswipe=function(){return y(this,"photoswipe")},t(".shortcode-gallery.dt-gallery-container:not(.owl-loaded), .gallery-shortcode:not(.owl-loaded)").initPhotoswipe(),t(".dt-trigger-first-pswp").addClass("pspw-ready").on("click",(function(e){var i=t(this),n=i.parents("article.post").first();if(!i.parents(".ts-wrap").hasClass("ts-interceptClicks")){if(n.length>0)(n.find(".dt-gallery-container").length>0?n.find(".dt-gallery-container a.dt-pswp-item"):n.find("a.dt-pswp-item")).length>0&&n.find(".rollover-click-target").trigger("click");return!1}})),t(".dt-owl-carousel-call").length>0||t(".slider-content").length>0||t(".dt-owl-carousel-init").length>0||t("body").is('[class*="elementor-page"]')){var b=t.fn.owlCarousel;!function(t,e,i,n){function o(e,i){this.settings=null,this.options=t.extend({},o.Defaults,i),this.$element=t(e),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},t.each(["onResize","onThrottledResize"],t.proxy((function(e,i){this._handlers[i]=t.proxy(this[i],this)}),this)),t.each(o.Plugins,t.proxy((function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)}),this)),t.each(o.Workers,t.proxy((function(e,i){this._pipe.push({filter:i.filter,run:t.proxy(i.run,this)})}),this)),this.setup(),this.initialize()}o.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:e,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"dt-owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},o.Width={Default:"default",Inner:"inner",Outer:"outer"},o.Type={Event:"event",State:"state"},o.Plugins={},o.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",i=!this.settings.autoWidth,n=this.settings.rtl,o={width:"auto","margin-left":n?e:"","margin-right":n?"":e};!i&&this.$stage.children().css(o),t.css=o}},{filter:["width","items","settings"],run:function(t){var e=parseInt(getComputedStyle(this.$stage.get(0)).getPropertyValue("--stage-left-gap")),i=parseInt(getComputedStyle(this.$stage.get(0)).getPropertyValue("--stage-right-gap")),n=((this.width()-e-i)/this.settings.items).toFixed(3)-this.settings.margin,o=null,s=this._items.length,a=!this.settings.autoWidth,r=[];for(t.items={merge:!1,width:n};s--;)o=this._mergers[s],o=this.settings.mergeFit&&Math.min(o,this.settings.items)||o,t.items.merge=o>1||t.items.merge,r[s]=a?n*o:this._items[s].width();this._widths=r}},{filter:["items","settings"],run:function(){var e=[],i=this._items,n=this.settings,o=Math.max(2*n.items,4),s=2*Math.ceil(i.length/2),a=n.loop&&i.length?n.rewind?o:Math.max(o,s):0,r="",l="";for(a/=2;a>0;)e.push(this.normalize(e.length/2,!0)),r+=i[e[e.length-1]][0].outerHTML,e.push(this.normalize(i.length-1-(e.length-1)/2,!0)),l=i[e[e.length-1]][0].outerHTML+l,a-=1;this._clones=e,t(r).addClass("cloned").appendTo(this.$stage),t(l).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t=this.settings.rtl?1:-1,e=this._clones.length+this._items.length,i=-1,n=0,o=0,s=[];++i<e;)n=s[i-1]||0,o=this._widths[this.relative(i)]+this.settings.margin,s.push(n+o*t);this._coordinates=s}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,i={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(i)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,i=!this.settings.autoWidth,n=this.$stage.children();if(i&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],n.eq(e).css(t.css);else i&&(t.css.width=t.items.width,n.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var t,e,i,n,o=this.settings.rtl?1:-1,s=2*this.settings.stagePadding,a=this.coordinates(this.current())+s,r=a+this.width()*o,l=[];for(i=0,n=this._coordinates.length;i<n;i++)t=this._coordinates[i-1]||0,e=Math.abs(this._coordinates[i])+s*o,(this.op(t,"<=",a)&&this.op(t,">",r)||this.op(e,"<",a)&&this.op(e,">",r))&&l.push(i);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+l.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],o.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=t("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(t("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},o.prototype.initializeItems=function(){var e=this.$element.find(".dt-owl-item");if(e.length)return this._items=e.get().map((function(e){return t(e)})),this._mergers=this._items.map((function(){return 1})),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)},o.prototype.initialize=function(){var t,e,i;(this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading"))&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:n,i=this.$element.children(e).width(),t.length&&i<=0&&this.preloadAutoWidthImages(t));this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},o.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},o.prototype.setup=function(){var e=this.viewport(),i=this.options.responsive,n=-1,o=null;i?(t.each(i,(function(t){t<=e&&t>n&&(n=Number(t))})),"function"==typeof(o=t.extend({},this.options,i[n])).stagePadding&&(o.stagePadding=o.stagePadding()),delete o.responsive,o.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+n))):o=t.extend({},this.options),this.trigger("change",{property:{name:"settings",value:o}}),this._breakpoint=n,this.settings=o,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},o.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},o.prototype.prepare=function(e){var i=this.trigger("prepare",{content:e});return i.data||(i.data=t("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(e)),this.trigger("prepared",{content:i.data}),i.data},o.prototype.update=function(){for(var e=0,i=this._pipe.length,n=t.proxy((function(t){return this[t]}),this._invalidated),o={};e<i;)(this._invalidated.all||t.grep(this._pipe[e].filter,n).length>0)&&this._pipe[e].run(o),e++;this._invalidated={},!this.is("valid")&&this.enter("valid")},o.prototype.width=function(t){switch(t=t||o.Width.Default){case o.Width.Inner:case o.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},o.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},o.prototype.onThrottledResize=function(){e.clearTimeout(this.resizeTimer),this.resizeTimer=e.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},o.prototype.onResize=function(){return!!this._items.length&&(this._width!==this.$element.width()&&(!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))))},o.prototype.registerEventHandlers=function(){t.support.transition&&this.$stage.on(t.support.transition.end+".owl.core",t.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(e,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",t.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",(function(){return!1}))),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",t.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",t.proxy(this.onDragEnd,this)))},o.prototype.onDragStart=function(e){var n=null;3!==e.which&&(t.support.transform?n={x:(n=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===n.length?12:4],y:n[16===n.length?13:5]}:(n=this.$stage.position(),n={x:this.settings.rtl?n.left+this.$stage.width()-this.width()+this.settings.margin:n.left,y:n.top}),this.is("animating")&&(t.support.transform?this.animate(n.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===e.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=t(e.target),this._drag.stage.start=n,this._drag.stage.current=n,this._drag.pointer=this.pointer(e),t(i).on("mouseup.owl.core touchend.owl.core",t.proxy(this.onDragEnd,this)),t(i).one("mousemove.owl.core touchmove.owl.core",t.proxy((function(e){var n=this.difference(this._drag.pointer,this.pointer(e));t(i).on("mousemove.owl.core touchmove.owl.core",t.proxy(this.onDragMove,this)),Math.abs(n.x)<Math.abs(n.y)&&this.is("valid")||(e.preventDefault(),this.enter("dragging"),this.trigger("drag"))}),this)))},o.prototype.onDragMove=function(t){var e=null,i=null,n=null,o=this.difference(this._drag.pointer,this.pointer(t)),s=this.difference(this._drag.stage.start,o);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-e,s.x=((s.x-e)%i+i)%i+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),n=this.settings.pullDrag?-1*o.x/5:0,s.x=Math.max(Math.min(s.x,e+n),i+n)),this._drag.stage.current=s,this.animate(s.x))},o.prototype.onDragEnd=function(e){var n=this.difference(this._drag.pointer,this.pointer(e)),o=this._drag.stage.current,s=n.x>0^this.settings.rtl?"left":"right";t(i).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==n.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(o.x,0!==n.x?s:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=s,(Math.abs(n.x)>3||(new Date).getTime()-this._drag.time>300)&&this._drag.target.one("click.owl.core",(function(){return!1}))),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},o.prototype.closest=function(e,i){var o=-1,s=this.width(),a=this.coordinates();return this.settings.freeDrag||t.each(a,t.proxy((function(t,r){return"left"===i&&e>r-30&&e<r+30?o=t:"right"===i&&e>r-s-30&&e<r-s+30?o=t+1:this.op(e,"<",r)&&this.op(e,">",a[t+1]!==n?a[t+1]:r-s)&&(o="left"===i?t+1:t),-1===o}),this)),this.settings.loop||(this.op(e,">",a[this.minimum()])?o=e=this.minimum():this.op(e,"<",a[this.maximum()])&&(o=e=this.maximum())),o},o.prototype.animate=function(e){var i=this.speed()>0;this.is("animating")&&this.onTransitionEnd(),i&&(this.enter("animating"),this.trigger("translate")),t.support.transform3d&&t.support.transition?this.$stage.css({transform:"translate3d("+e+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):i?this.$stage.animate({left:e+"px"},this.speed(),this.settings.fallbackEasing,t.proxy(this.onTransitionEnd,this)):this.$stage.css({left:e+"px"})},o.prototype.is=function(t){return this._states.current[t]&&this._states.current[t]>0},o.prototype.current=function(t){if(t===n)return this._current;if(0===this._items.length)return n;if(t=this.normalize(t),this._current!==t){var e=this.trigger("change",{property:{name:"position",value:t}});e.data!==n&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current},o.prototype.invalidate=function(e){return"string"===t.type(e)&&(this._invalidated[e]=!0,this.is("valid")&&this.leave("valid")),t.map(this._invalidated,(function(t,e){return e}))},o.prototype.reset=function(t){(t=this.normalize(t))!==n&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},o.prototype.normalize=function(t,e){var i=this._items.length,o=e?0:this._clones.length;return!this.isNumeric(t)||i<1?t=n:(t<0||t>=i+o)&&(t=((t-o/2)%i+i)%i+o/2),t},o.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},o.prototype.maximum=function(t){var e,i,n,o=this.settings,s=this._coordinates.length;if(o.loop)s=this._clones.length/2+this._items.length-1;else if(o.autoWidth||o.merge){if(e=this._items.length)for(i=this._items[--e].width(),n=this.$element.width();e--&&!((i+=this._items[e].width()+this.settings.margin)>n););s=e+1}else s=o.center?this._items.length-1:this._items.length-o.items;return t&&(s-=this._clones.length/2),Math.max(s,0)},o.prototype.minimum=function(t){return t?0:this._clones.length/2},o.prototype.items=function(t){return t===n?this._items.slice():(t=this.normalize(t,!0),this._items[t])},o.prototype.mergers=function(t){return t===n?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},o.prototype.clones=function(e){var i=this._clones.length/2,o=i+this._items.length,s=function(t){return t%2==0?o+t/2:i-(t+1)/2};return e===n?t.map(this._clones,(function(t,e){return s(e)})):t.map(this._clones,(function(t,i){return t===e?s(i):null}))},o.prototype.speed=function(t){return t!==n&&(this._speed=t),this._speed},o.prototype.coordinates=function(e){var i,o=1,s=e-1;return e===n?t.map(this._coordinates,t.proxy((function(t,e){return this.coordinates(e)}),this)):(this.settings.center?(this.settings.rtl&&(o=-1,s=e+1),i=this._coordinates[e],i+=(this.width()-i+(this._coordinates[s]||0))/2*o):i=this._coordinates[s]||0,i=Math.ceil(i))},o.prototype.duration=function(t,e,i){return 0===i?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(i||this.settings.smartSpeed)},o.prototype.to=function(t,e){var i=this.current(),n=null,o=t-this.relative(i),s=(o>0)-(o<0),a=this._items.length,r=this.minimum(),l=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(o)>a/2&&(o+=-1*s*a),(n=(((t=i+o)-r)%a+a)%a+r)!==t&&n-o<=l&&n-o>0&&(i=n-o,t=n,this.reset(i))):t=this.settings.rewind?(t%(l+=1)+l)%l:Math.max(r,Math.min(l,t)),this.speed(this.duration(i,t,e)),this.current(t),this.isVisible()&&this.update()},o.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},o.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},o.prototype.onTransitionEnd=function(t){if(t!==n&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},o.prototype.viewport=function(){var n;return this.options.responsiveBaseElement!==e?n=t(this.options.responsiveBaseElement).width():e.innerWidth?n=e.innerWidth:i.documentElement&&i.documentElement.clientWidth?n=i.documentElement.clientWidth:console.warn("Can not detect viewport width."),n},o.prototype.replace=function(e){this.$stage.empty(),this._items=[],e&&(e=e instanceof jQuery?e:t(e)),this.settings.nestedItemSelector&&(e=e.find("."+this.settings.nestedItemSelector)),e.filter((function(){return 1===this.nodeType})).each(t.proxy((function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)}),this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},o.prototype.add=function(e,i){var o=this.relative(this._current);i=i===n?this._items.length:this.normalize(i,!0),e=e instanceof jQuery?e:t(e),this.trigger("add",{content:e,position:i}),e=this.prepare(e),0===this._items.length||i===this._items.length?(0===this._items.length&&this.$stage.append(e),0!==this._items.length&&this._items[i-1].after(e),this._items.push(e),this._mergers.push(1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[i].before(e),this._items.splice(i,0,e),this._mergers.splice(i,0,1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[o]&&this.reset(this._items[o].index()),this.invalidate("items"),this.trigger("added",{content:e,position:i})},o.prototype.remove=function(t){(t=this.normalize(t,!0))!==n&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},o.prototype.preloadAutoWidthImages=function(e){e.each(t.proxy((function(e,i){this.enter("pre-loading"),i=t(i),t(new Image).one("load",t.proxy((function(t){i.attr("src",t.target.src),i.css("opacity",1),this.leave("pre-loading"),!this.is("pre-loading")&&!this.is("initializing")&&this.refresh()}),this)).attr("src",i.attr("src")||i.attr("data-src")||i.attr("data-src-retina"))}),this))},o.prototype.destroy=function(){for(var n in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),t(i).off(".owl.core"),!1!==this.settings.responsive&&(e.clearTimeout(this.resizeTimer),this.off(e,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[n].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},o.prototype.op=function(t,e,i){var n=this.settings.rtl;switch(e){case"<":return n?t>i:t<i;case">":return n?t<i:t>i;case">=":return n?t<=i:t>=i;case"<=":return n?t>=i:t<=i}},o.prototype.on=function(t,e,i,n){t.addEventListener?t.addEventListener(e,i,n):t.attachEvent&&t.attachEvent("on"+e,i)},o.prototype.off=function(t,e,i,n){t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent&&t.detachEvent("on"+e,i)},o.prototype.trigger=function(e,i,n,s,a){var r={item:{count:this._items.length,index:this.current()}},l=t.camelCase(t.grep(["on",e,n],(function(t){return t})).join("-").toLowerCase()),d=t.Event([e,"owl",n||"carousel"].join(".").toLowerCase(),t.extend({relatedTarget:this},r,i));return this._supress[e]||(t.each(this._plugins,(function(t,e){e.onTrigger&&e.onTrigger(d)})),this.register({type:o.Type.Event,name:e}),this.$element.trigger(d),this.settings&&"function"==typeof this.settings[l]&&this.settings[l].call(this,d)),d},o.prototype.enter=function(e){t.each([e].concat(this._states.tags[e]||[]),t.proxy((function(t,e){this._states.current[e]===n&&(this._states.current[e]=0),this._states.current[e]++}),this))},o.prototype.leave=function(e){t.each([e].concat(this._states.tags[e]||[]),t.proxy((function(t,e){this._states.current[e]--}),this))},o.prototype.register=function(e){if(e.type===o.Type.Event){if(t.event.special[e.name]||(t.event.special[e.name]={}),!t.event.special[e.name].owl){var i=t.event.special[e.name]._default;t.event.special[e.name]._default=function(t){return!i||!i.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&t.namespace.indexOf("owl")>-1:i.apply(this,arguments)},t.event.special[e.name].owl=!0}}else e.type===o.Type.State&&(this._states.tags[e.name]?this._states.tags[e.name]=this._states.tags[e.name].concat(e.tags):this._states.tags[e.name]=e.tags,this._states.tags[e.name]=t.grep(this._states.tags[e.name],t.proxy((function(i,n){return t.inArray(i,this._states.tags[e.name])===n}),this)))},o.prototype.suppress=function(e){t.each(e,t.proxy((function(t,e){this._supress[e]=!0}),this))},o.prototype.release=function(e){t.each(e,t.proxy((function(t,e){delete this._supress[e]}),this))},o.prototype.pointer=function(t){var i={x:null,y:null};return(t=(t=t.originalEvent||t||e.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(i.x=t.pageX,i.y=t.pageY):(i.x=t.clientX,i.y=t.clientY),i},o.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},o.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},t.fn.owlCarousel=function(e){var i=Array.prototype.slice.call(arguments,1);return this.each((function(){var n=t(this),s=n.data("owl.carousel");s||(s=new o(this,"object"==typeof e&&e),n.data("owl.carousel",s),t.each(["next","prev","to","destroy","refresh","replace","add","remove"],(function(e,i){s.register({type:o.Type.Event,name:i}),s.$element.on(i+".owl.carousel.core",t.proxy((function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([i]),s[i].apply(this,[].slice.call(arguments,1)),this.release([i]))}),s))}))),"string"==typeof e&&"_"!==e.charAt(0)&&s[e].apply(s,i)}))},t.fn.owlCarousel.Constructor=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(e){this._core=e,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers)};o.Defaults={autoRefresh:!0,autoRefreshInterval:500},o.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=e.setInterval(t.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},o.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())},o.prototype.destroy=function(){var t,i;for(t in e.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},t.fn.owlCarousel.Constructor.Plugins.AutoRefresh=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(e){this._core=e,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":t.proxy((function(e){if(e.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(e.property&&"position"==e.property.name||"initialized"==e.type)){var i=this._core.settings,n=i.center&&Math.ceil(i.items/2)||i.items,o=i.center&&-1*n||0,s=(e.property&&undefined!==e.property.value?e.property.value:this._core.current())+o,a=this._core.clones().length,r=t.proxy((function(t,e){this.load(e)}),this);for(i.lazyLoadEager>0&&(n+=i.lazyLoadEager,i.loop&&(s-=i.lazyLoadEager,n++));o++<n;)this.load(a/2+this._core.relative(s)),a&&t.each(this._core.clones(this._core.relative(s)),r),s++}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers)};o.Defaults={lazyLoad:!1,lazyLoadEager:0},o.prototype.load=function(i){var n=this._core.$stage.children().eq(i),o=n&&n.find(".owl-lazy");!o||t.inArray(n.get(0),this._loaded)>-1||(o.each(t.proxy((function(i,n){var o,s=t(n),a=e.devicePixelRatio>1&&s.attr("data-src-retina")||s.attr("data-src")||s.attr("data-srcset");this._core.trigger("load",{element:s,url:a},"lazy"),s.is("img")?s.one("load.owl.lazy",t.proxy((function(){s.css("opacity",1),this._core.trigger("loaded",{element:s,url:a},"lazy")}),this)).attr("src",a):s.is("source")?s.one("load.owl.lazy",t.proxy((function(){this._core.trigger("loaded",{element:s,url:a},"lazy")}),this)).attr("srcset",a):((o=new Image).onload=t.proxy((function(){s.css({"background-image":'url("'+a+'")',opacity:"1"}),this._core.trigger("loaded",{element:s,url:a},"lazy")}),this),o.src=a)}),this)),this._loaded.push(n.get(0)))},o.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Lazy=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(i){this._core=i,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&this.update()}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()}),this),"loaded.owl.lazy":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var n=this;t(e).on("load",(function(){n._core.settings.autoHeight&&n.update()})),t(e).on("resize",(function(){n._core.settings.autoHeight&&(null!=n._intervalId&&clearTimeout(n._intervalId),n._intervalId=setTimeout((function(){n.update()}),250))}))};o.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},o.prototype.update=function(){var e=this._core._current,i=e+this._core.settings.items,n=this._core.settings.lazyLoad,o=this._core.$stage.children().toArray().slice(e,i),s=[],a=0;t.each(o,(function(e,i){s.push(t(i).height())})),(a=Math.max.apply(null,s))<=1&&n&&this._previousHeight&&(a=this._previousHeight),this._previousHeight=a,this._core.$stage.parent().height(a).addClass(this._core.settings.autoHeightClass)},o.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.AutoHeight=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(e){this._core=e,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})}),this),"resize.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()}),this),"refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()}),this),"prepared.owl.carousel":t.proxy((function(e){if(e.namespace){var i=t(e.content).find(".owl-video");i.length&&(i.css("display","none"),this.fetch(i,t(e.content)))}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",t.proxy((function(t){this.play(t)}),this))};o.Defaults={video:!1,videoHeight:!1,videoWidth:!1},o.prototype.fetch=function(t,e){var i=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",n=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),o=t.attr("data-width")||this._core.settings.videoWidth,s=t.attr("data-height")||this._core.settings.videoHeight,a=t.attr("href");if(!a)throw new Error("Missing video URL.");if((n=a.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu")>-1)i="youtube";else if(n[3].indexOf("vimeo")>-1)i="vimeo";else{if(!(n[3].indexOf("vzaar")>-1))throw new Error("Video URL not supported.");i="vzaar"}n=n[6],this._videos[a]={type:i,id:n,width:o,height:s},e.attr("data-video",a),this.thumbnail(t,this._videos[a])},o.prototype.thumbnail=function(e,i){var n,o,s=i.width&&i.height?"width:"+i.width+"px;height:"+i.height+"px;":"",a=e.find("img"),r="src",l="",d=this._core.settings,c=function(i){'<div class="owl-video-play-icon"></div>',n=d.lazyLoad?t("<div/>",{class:"owl-video-tn "+l,srcType:i}):t("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+i+")"}),e.after(n),e.after('<div class="owl-video-play-icon"></div>')};if(e.wrap(t("<div/>",{class:"owl-video-wrapper",style:s})),this._core.settings.lazyLoad&&(r="data-src",l="owl-lazy"),a.length)return c(a.attr(r)),a.remove(),!1;"youtube"===i.type?(o="//img.youtube.com/vi/"+i.id+"/hqdefault.jpg",c(o)):"vimeo"===i.type?t.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t[0].thumbnail_large,c(o)}}):"vzaar"===i.type&&t.ajax({type:"GET",url:"//vzaar.com/api/videos/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t.framegrab_url,c(o)}})},o.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},o.prototype.play=function(e){var i,n=t(e.target).closest("."+this._core.settings.itemClass),o=this._videos[n.attr("data-video")],s=o.width||"100%",a=o.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),n=this._core.items(this._core.relative(n.index())),this._core.reset(n.index()),(i=t('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",a),i.attr("width",s),"youtube"===o.type?i.attr("src","//www.youtube.com/embed/"+o.id+"?autoplay=1&rel=0&v="+o.id):"vimeo"===o.type?i.attr("src","//player.vimeo.com/video/"+o.id+"?autoplay=1"):"vzaar"===o.type&&i.attr("src","//view.vzaar.com/"+o.id+"/player?autoplay=true"),t(i).wrap('<div class="owl-video-frame" />').insertAfter(n.find(".owl-video")),this._playing=n.addClass("owl-video-playing"))},o.prototype.isInFullScreen=function(){var e=i.fullscreenElement||i.mozFullScreenElement||i.webkitFullscreenElement;return e&&t(e).parent().hasClass("owl-video-frame")},o.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Video=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(e){this.core=e,this.core.options=t.extend({},o.Defaults,this.core.options),this.swapping=!0,this.previous=n,this.next=n,this.handlers={"change.owl.carousel":t.proxy((function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)}),this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":t.proxy((function(t){t.namespace&&(this.swapping="translated"==t.type)}),this),"translate.owl.carousel":t.proxy((function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()}),this)},this.core.$element.on(this.handlers)};o.Defaults={animateOut:!1,animateIn:!1},o.prototype.swap=function(){if(1===this.core.settings.items&&t.support.animation&&t.support.transition){this.core.speed(0);var e,i=t.proxy(this.clear,this),n=this.core.$stage.children().eq(this.previous),o=this.core.$stage.children().eq(this.next),s=this.core.settings.animateIn,a=this.core.settings.animateOut;this.core.current()!==this.previous&&(a&&(e=this.core.coordinates(this.previous)-this.core.coordinates(this.next),n.one(t.support.animation.end,i).css({left:e+"px"}).addClass("animated owl-animated-out").addClass(a)),s&&o.one(t.support.animation.end,i).addClass("animated owl-animated-in").addClass(s))}},o.prototype.clear=function(e){t(e.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},o.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Animate=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=function(e){this._core=e,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":t.proxy((function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)}),this),"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoplay&&this.play()}),this),"play.owl.autoplay":t.proxy((function(t,e,i){t.namespace&&this.play(e,i)}),this),"stop.owl.autoplay":t.proxy((function(t){t.namespace&&this.stop()}),this),"mouseover.owl.autoplay":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()}),this),"mouseleave.owl.autoplay":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()}),this),"touchstart.owl.core":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()}),this),"touchend.owl.core":t.proxy((function(){this._core.settings.autoplayHoverPause&&this.play()}),this)},this._core.$element.on(this._handlers),this._core.options=t.extend({},o.Defaults,this._core.options)};o.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},o.prototype._next=function(n){this._call=e.setTimeout(t.proxy(this._next,this,n),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||i.hidden||this._core.next(n||this._core.settings.autoplaySpeed)},o.prototype.read=function(){return(new Date).getTime()-this._time},o.prototype.play=function(i,n){var o;this._core.is("rotating")||this._core.enter("rotating"),i=i||this._core.settings.autoplayTimeout,o=Math.min(this._time%(this._timeout||i),i),this._paused?(this._time=this.read(),this._paused=!1):e.clearTimeout(this._call),this._time+=this.read()%i-o,this._timeout=i,this._call=e.setTimeout(t.proxy(this._next,this,n),i-o)},o.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,e.clearTimeout(this._call),this._core.leave("rotating"))},o.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,e.clearTimeout(this._call))},o.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.autoplay=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){"use strict";var o=function(e){this._core=e,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":t.proxy((function(e){e.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+t(e.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")}),this),"added.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())}),this),"remove.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&"position"==t.property.name&&this.draw()}),this),"initialized.owl.carousel":t.proxy((function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))}),this),"refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this.$element.on(this._handlers)};o.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},o.prototype.initialize=function(){var e,i=this._core.settings;for(e in this._controls.$relative=(i.navContainer?t(i.navContainer):t("<div>").addClass(i.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=t("<"+i.navElement+">").addClass(i.navClass[0]).html(i.navText[0]).prependTo(this._controls.$relative).on("click",t.proxy((function(t){this.prev(i.navSpeed)}),this)),this._controls.$next=t("<"+i.navElement+">").addClass(i.navClass[1]).html(i.navText[1]).appendTo(this._controls.$relative).on("click",t.proxy((function(t){this.next(i.navSpeed)}),this)),i.dotsData||(this._templates=[t('<button role="button">').addClass(i.dotClass).append(t("<span>")).prop("outerHTML")]),this._controls.$absolute=(i.dotsContainer?t(i.dotsContainer):t("<div>").addClass(i.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",t.proxy((function(e){var n=t(e.target).parent().is(this._controls.$absolute)?t(e.target).index():t(e.target).parent().index();e.preventDefault(),this.to(n,i.dotsSpeed)}),this)),this._overrides)this._core[e]=t.proxy(this[e],this)},o.prototype.destroy=function(){var t,e,i,n,o;for(t in o=this._core.settings,this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&o.navContainer?this._controls[e].html(""):this._controls[e].remove();for(n in this.overides)this._core[n]=this._overrides[n];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},o.prototype.update=function(){var t,e,i=this._core.clones().length/2,n=i+this._core.items().length,o=this._core.maximum(!0),s=this._core.settings,a=s.center||s.autoWidth||s.dotsData?1:s.dotsEach||s.items;if("page"!==s.slideBy&&(s.slideBy=Math.min(s.slideBy,s.items)),s.dots||"page"==s.slideBy)for(this._pages=[],t=i,e=0,0;t<n;t++){if(e>=a||0===e){if(this._pages.push({start:Math.min(o,t-i),end:t-i+a-1}),Math.min(o,t-i)===o)break;e=0}e+=this._core.mergers(this._core.relative(t))}},o.prototype.draw=function(){var e,i=this._core.settings,n=this._core.items().length<=i.items,o=this._core.relative(this._core.current()),s=i.loop||i.rewind;this._controls.$relative.toggleClass("disabled",!i.nav||n),i.nav&&(this._controls.$previous.toggleClass("disabled",!s&&o<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!s&&o>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!i.dots||n),i.dots&&(e=this._pages.length-this._controls.$absolute.children().length,i.dotsData&&0!==e?this._controls.$absolute.html(this._templates.join("")):e>0?this._controls.$absolute.append(new Array(e+1).join(this._templates[0])):e<0&&this._controls.$absolute.children().slice(e).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(t.inArray(this.current(),this._pages)).addClass("active"))},o.prototype.onTrigger=function(e){var i=this._core.settings;e.page={index:t.inArray(this.current(),this._pages),count:this._pages.length,size:i&&(i.center||i.autoWidth||i.dotsData?1:i.dotsEach||i.items)}},o.prototype.current=function(){var e=this._core.relative(this._core.current());return t.grep(this._pages,t.proxy((function(t,i){return t.start<=e&&t.end>=e}),this)).pop()},o.prototype.getPosition=function(e){var i,n,o=this._core.settings;return"page"==o.slideBy?(i=t.inArray(this.current(),this._pages),n=this._pages.length,e?++i:--i,i=this._pages[(i%n+n)%n].start):(i=this._core.relative(this._core.current()),n=this._core.items().length,e?i+=o.slideBy:i-=o.slideBy),i},o.prototype.next=function(e){t.proxy(this._overrides.to,this._core)(this.getPosition(!0),e)},o.prototype.prev=function(e){t.proxy(this._overrides.to,this._core)(this.getPosition(!1),e)},o.prototype.to=function(e,i,n){var o;!n&&this._pages.length?(o=this._pages.length,t.proxy(this._overrides.to,this._core)(this._pages[(e%o+o)%o].start,i)):t.proxy(this._overrides.to,this._core)(e,i)},t.fn.owlCarousel.Constructor.Plugins.Navigation=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){"use strict";var o=function(i){this._core=i,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":t.proxy((function(i){i.namespace&&"URLHash"===this._core.settings.startPosition&&t(e).trigger("hashchange.owl.navigation")}),this),"prepared.owl.carousel":t.proxy((function(e){if(e.namespace){var i=t(e.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!i)return;this._hashes[i]=e.content}}),this),"changed.owl.carousel":t.proxy((function(i){if(i.namespace&&"position"===i.property.name){var n=this._core.items(this._core.relative(this._core.current())),o=t.map(this._hashes,(function(t,e){return t===n?e:null})).join();if(!o||e.location.hash.slice(1)===o)return;e.location.hash=o}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this.$element.on(this._handlers),t(e).on("hashchange.owl.navigation",t.proxy((function(t){var i=e.location.hash.substring(1),n=this._core.$stage.children(),o=this._hashes[i]&&n.index(this._hashes[i]);undefined!==o&&o!==this._core.current()&&this._core.to(this._core.relative(o),!1,!0)}),this))};o.Defaults={URLhashListener:!1},o.prototype.destroy=function(){var i,n;for(i in t(e).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(i,this._handlers[i]);for(n in Object.getOwnPropertyNames(this))"function"!=typeof this[n]&&(this[n]=null)},t.fn.owlCarousel.Constructor.Plugins.Hash=o}(window.Zepto||window.jQuery,window,document),function(t,e,i,n){var o=t("<support>").get(0).style,s="Webkit Moz O ms".split(" "),a={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},r=function(){return!!c("transform")},l=function(){return!!c("perspective")},d=function(){return!!c("animation")};function c(e,i){var a=!1,r=e.charAt(0).toUpperCase()+e.slice(1);return t.each((e+" "+s.join(r+" ")+r).split(" "),(function(t,e){if(o[e]!==n)return a=!i||e,!1})),a}function h(t){return c(t,!0)}(function(){return!!c("transition")})()&&(t.support.transition=new String(h("transition")),t.support.transition.end=a.transition.end[t.support.transition]),d()&&(t.support.animation=new String(h("animation")),t.support.animation.end=a.animation.end[t.support.animation]),r()&&(t.support.transform=new String(h("transform")),t.support.transform3d=l())}(window.Zepto||window.jQuery,window,document)}t.fn.scrollerSlideSize=function(){return this.each((function(){var e=t(this),i=e.find("img").eq(0),n=parseInt(i.attr("width")),o=e.parents(".slider-wrapper"),s=o.width(),a=o.attr("data-max-width"),r=parseInt(o.attr("data-padding-side")),l=parseInt(i.parents(".wf-td").eq(0).css("paddingLeft")),d=parseInt(i.parents(".wf-td").eq(0).css("paddingRight")),c=0;if(l>0&&d>0&&(c=l+d),void 0!==a)var h=s*parseFloat(a)/100-c-r;if(n>h)var u=h;else{u=parseInt(i.attr("width"));i.exists()||(u=280)}e.parents(".slider-wrapper").attr("data-width",u+c),e.css({width:u+c})}))},t(".slider-wrapper.description-under-image:not(.related-projects) article").scrollerSlideSize();var C=t(".slider-wrapper.owl-carousel:not(.related-projects)");C.each((function(){var e,i=t(this),n=i.attr("data-padding-side")?parseInt(i.attr("data-padding-side")):0,o="true"===i.attr("data-autoslide"),s=i.attr("data-delay")?parseInt(i.attr("data-delay")):6e3,a="true"===i.attr("data-arrows"),r=i.attr("data-width")?C.width()/parseInt(i.attr("data-width")):C.width()/C.find("article img").attr("width"),l="rtl"==jQuery(document).attr("dir"),d=i.attr("data-next-icon")?i.attr("data-next-icon"):"icon-ar-018-r",c=i.attr("data-prev-icon")?i.attr("data-prev-icon"):"icon-ar-018-l";function h(t){var e,n=i.find(".owl-stage"),o=n.width(),s=i.find(".dt-owl-item");(e=(s.width()+parseInt(s.css("margin-right")))*t.item.count)>o&&n.width(e)}r=i.attr("data-width")?C.width()/parseInt(i.attr("data-width")):i.attr("data-max-width")?C.width()/parseInt(i.attr("data-max-width")):C.width()/C.find("article img").attr("width"),i.owlCarousel({rtl:l,items:r,autoHeight:!1,margin:n,loadedClass:"owl-loaded",slideBy:"page",loop:!1,smartSpeed:600,merge:!0,autoWidth:!0,responsive:{678:{mergeFit:!0},1e3:{mergeFit:!1}},autoplay:o,autoplayTimeout:s,nav:a,navElement:"div",navText:['<i class="'+c+'"></i>','<i class="'+d+'"></i>'],dots:!1,onInitialize:function(t){var e=parseInt(i.attr("data-max-width")),o=C.width()*parseFloat(e)/100-n,s=parseInt(i.find("img").attr("width")),a=parseInt(i.find("img").attr("height"));e&&o<s&&(i.find("article").css({"max-width":o+"px"}),i.find("img").css({"max-width":o+"px",height:o*a/s}))},onInitialized:h,onRefreshed:h}).trigger("refresh.owl.carousel"),i.on("drag.owl.carousel translate.owl.carousel",(function(t){i.addClass("ts-interceptClicks")})),i.on("dragged.owl.carousel translated.owl.carousel",(function(t){clearTimeout(e),e=setTimeout((function(){i.removeClass("ts-interceptClicks")}),400)})),i.on("changed.owl.carousel",(function(e){t(".dt-owl-item.cloned .is-loaded",i).parents().hasClass("layzr-bg")&&t(".dt-owl-item.cloned .is-loaded",i).parents().removeClass("layzr-bg"),t(".dt-owl-item.cloned .photoswipe-wrapper, .dt-owl-item.cloned .photoswipe-item .dt-gallery-container",i).initPhotoswipe(),t(".animate-element:not(.start-animation):in-viewport",i).checkInViewport()})),i.find(".dt-owl-item").on("mouseenter",(function(t){o&&i.trigger("stop.owl.autoplay")})),i.find(".dt-owl-item").on("mouseleave",(function(t){o&&i.trigger("play.owl.autoplay",[s])})),i.find(".owl-nav div").on("mouseenter",(function(t){o&&i.trigger("stop.owl.autoplay")})),i.find(".owl-nav div").on("mouseleave",(function(t){o&&i.trigger("play.owl.autoplay",[s])})),i.on("mouseenter",(function(t){i.addClass("show-arrows")})),i.on("mouseleave",(function(t){i.removeClass("show-arrows")}))})),t.fn.postTypeScroller=function(){var e=t(this),i="rtl"==jQuery(document).attr("dir"),o=e.attr("data-next-icon")?e.attr("data-next-icon"):"icon-ar-018-r",s=e.attr("data-prev-icon")?e.attr("data-prev-icon"):"icon-ar-018-l",a=(e.attr("data-padding-side")&&parseInt(e.attr("data-padding-side")),"true"!=e.attr("data-paused")&&void 0!==e.attr("data-autoslide")),r=(e.attr("data-paused"),e.attr("data-autoslide")&&parseInt(e.attr("data-autoslide"))>999?parseInt(e.attr("data-autoslide")):5e3),l=(e.attr("data-autoslide"),e.attr("data-width")?parseInt(e.attr("data-width")):800),d=e.attr("data-height")?parseInt(e.attr("data-height")):400,c=e.attr("data-img-mode")?e.attr("data-img-mode"):"fill";e.owlCarousel({rtl:i,items:1,autoHeight:!1,center:!1,margin:0,loadedClass:"owl-loaded",slideBy:1,loop:!0,smartSpeed:600,autoplay:a,autoplayTimeout:r,nav:!0,navElement:"div",navText:['<i class="'+s+'"></i>','<i class="'+o+'"></i>'],dots:!1}),n.on("debouncedresize",(function(){e.find(".dt-owl-item").each((function(i){var n=t(this),o=n.find("img");if(o.css({opacity:0}),!o)return!1;var s;s=d/l,"fit"==c?n.css({height:s*n.width()}):n.css({height:s*e.width()});var a,r,h,u,p,f=parseInt(o.attr("width")),m=parseInt(o.attr("height")),g={};a=n.width()/f,r=s*n.width()/m,h="fill"==e.attr("data-img-mode")?a>r?a:r:"fit"==e.attr("data-img-mode")?a<r?a:r:a>r?a:r,u=Math.ceil(f*h,10),p=Math.ceil(m*h,10),g.width=u,g.height=p,g.opacity=1,o.css(g)}))})),void 0!==e.attr("data-autoslide")&&t('<div class="psPlay"></div>').appendTo(e),"true"===e.attr("data-paused")&&(t(".psPlay",e).addClass("paused"),e.trigger("stop.owl.autoplay")),t(".psPlay",e).on("click",(function(e){e.preventDefault();var i=t(this);i.hasClass("paused")?(i.removeClass("paused"),a=!0,i.trigger("play.owl.autoplay",[r,600])):(i.addClass("paused"),i.trigger("stop.owl.autoplay"))}))},t(".slider-simple:not(.slider-masonry)").each((function(){t(this).postTypeScroller()}));var x,_=t("#main .slider-content, #footer .slider-content, .side-header:not(.sub-downwards) .mega-full-width > .dt-mega-menu-wrap  .slider-content, .side-header:not(.sub-downwards) .mega-auto-width > .dt-mega-menu-wrap  .slider-content");t.fn.widgetScroller=function(){return this.each((function(){var e=t(this),i=void 0!==e.attr("data-autoslide"),n=e.attr("data-autoslide")?parseInt(e.attr("data-autoslide")):6e3,o="rtl"==jQuery(document).attr("dir");e.owlCarousel({rtl:o,items:1,autoHeight:!0,margin:0,loadedClass:"owl-loaded",slideBy:"page",loop:e.children().length>1,smartSpeed:600,autoplay:i,autoplayTimeout:n,autoplayHoverPause:!1,nav:!1,dots:!0,dotsEach:!0})}))},_.widgetScroller().css("visibility","visible");var S;function k(){clearTimeout(S),S=setTimeout((function(){t(".dt-owl-carousel-call, .elementor-owl-carousel-call, .related-projects, .slider-simple:not(.slider-masonry)").trigger("refresh.owl.carousel")}),200)}t.fn.the7OwlCarousel=function(){var e=t(this);if(e.length){var i,n=e.attr("data-col-gap")?parseInt(e.attr("data-col-gap")):0,o="true"===e.attr("data-auto-height"),s=e.attr("data-speed")?parseInt(e.attr("data-speed")):600,a="true"===e.attr("data-autoplay"),r=e.attr("data-autoplay_speed")?parseInt(e.attr("data-autoplay_speed")):6e3,l="true"===e.attr("data-arrows"),d="true"===e.attr("data-bullet"),c=e.attr("data-wide-col-num")?parseInt(e.attr("data-wide-col-num")):3,h=e.attr("data-col-num")?parseInt(e.attr("data-col-num")):3,u=e.attr("data-laptop-col")?parseInt(e.attr("data-laptop-col")):3,p=e.attr("data-h-tablet-columns-num")?parseInt(e.attr("data-h-tablet-columns-num")):3,f=e.attr("data-v-tablet-columns-num")?parseInt(e.attr("data-v-tablet-columns-num")):2,m=e.attr("data-phone-columns-num")?parseInt(e.attr("data-phone-columns-num")):1,g="rtl"==jQuery(document).attr("dir"),v="1"==e.attr("data-scroll-mode")?parseInt(e.attr("data-scroll-mode")):"page",w=e.attr("data-next-icon")?e.attr("data-next-icon"):"icon-ar-002-r",y=e.attr("data-prev-icon")?e.attr("data-prev-icon"):"icon-ar-001-l",b=!("1"!=e.attr("data-scroll-mode")||!d);if(void 0!==e.attr("data-stage-padding"))var C=e.hasClass("enable-img-shadow")?parseInt(e.attr("data-stage-padding"))+parseInt(e.attr("data-col-gap"))/2:parseInt(e.attr("data-stage-padding"));else C=0;n=e.attr("data-col-gap")?parseInt(e.attr("data-col-gap")):e.attr("data-padding-side")?parseInt(e.attr("data-padding-side")):0,e.on("initialize.owl.carousel",(function(i){t(e[0]).find("script, style").each((function(){var e=t(this);e.siblings().first();t(e).prev().length>0?t(e).prev().addBack().wrapAll("<div class='carousel-item-wrap' />"):t(e).next().length>0&&t(e).next().addBack().wrapAll("<div class='carousel-item-wrap' />")}))}));var _={};t(this).hasClass("products-carousel-shortcode")&&e.parent(".elementor-widget-container").length&&elementorFrontendConfig?(_[0]={items:m,loop:e.children().length>m,stagePadding:0},_[elementorFrontendConfig.breakpoints.md]={loop:e.children().length>p,items:p,stagePadding:0},_[elementorFrontendConfig.breakpoints.lg]={loop:e.children().length>h,items:h}):_={0:{items:m,loop:e.children().length>m,stagePadding:0},481:{loop:e.children().length>f,items:f,stagePadding:0},769:{loop:e.children().length>p,items:p,stagePadding:0},992:{loop:e.children().length>u,items:u},1199:{loop:e.children().length>h,items:h},1450:{loop:e.children().length>c,items:c}},e.owlCarousel({rtl:g,items:c,autoHeight:o,margin:n,stagePadding:C,loadedClass:"owl-loaded",slideBy:v,loop:!0,smartSpeed:s,responsive:_,autoplay:a,autoplayTimeout:r,nav:l,navElement:"div",navText:['<i class="'+y+'" ></i>','<i class="'+w+'"></i>'],dots:d,dotsEach:b}),e.hasClass("content-rollover-layout-list")&&!e.hasClass("disable-layout-hover")&&e.find(".post-entry-wrapper").clickOverlayGradient(),dtGlobals.addOnloadEvent((function(){!function(e){if(e.hasClass("refreshed")||(e.addClass("refreshed"),e.trigger("refresh.owl.carousel")),e.hasClass("content-rollover-layout-list")&&!e.hasClass("disable-layout-hover")&&e.find(".post-entry-wrapper").clickOverlayGradient(),clearTimeout(x),x=setTimeout((function(){t(".dt-owl-item.cloned .animate-element.animation-triggered:not(.start-animation)").addClass("start-animation")}),50),"true"===e.attr("data-autoplay"))if(dtGlobals.isInViewport(e)){const t=e.attr("data-autoplay_speed")?parseInt(e.attr("data-autoplay_speed")):6e3;e.trigger("play.owl.autoplay",[t])}else e.trigger("stop.owl.autoplay")}(e)})),e.on("changed.owl.carousel",(function(i){t(".dt-owl-item.cloned .photoswipe-wrapper, .dt-owl-item.cloned .photoswipe-item .dt-gallery-container",e).initPhotoswipe(),the7Utils.isFunction(t.fn.triggerClonedHoverClick)&&t(" .dt-owl-item.cloned .buttons-on-img:not(.rollover-active) .rollover-content",e).triggerClonedHoverClick();var n=t(" .dt-owl-item.cloned .post-thumbnail-wrap",e);e.hasClass("albums-shortcode")&&(e.hasClass("gradient-overlay-layout-list")||e.hasClass("content-rollover-layout-list")?the7Utils.isFunction(t.fn.triggerClonedOverlayAlbumsClick)&&t(" .dt-owl-item.cloned .post-entry-content",e).triggerClonedOverlayAlbumsClick():the7Utils.isFunction(t.fn.triggerClonedAlbumsClick)&&n.triggerClonedAlbumsClick()),e.hasClass("gallery-shortcode")&&t(e).initCarouselClonedPhotoswipe()})),e.on("change.owl.carousel",(function(n){clearTimeout(i),i=setTimeout((function(){e.layzrCarouselUpdate(),t(".dt-owl-item.cloned .lazy-load",e).parent().removeClass("layzr-bg")}),20)})),e.on("resized.owl.carousel",(function(t){e.hasClass("content-rollover-layout-list")&&!e.hasClass("disable-layout-hover")&&e.find(".post-entry-wrapper").clickOverlayGradient()})),e.find(".dt-owl-item").on("mouseenter",(function(t){a&&e.trigger("stop.owl.autoplay")})),e.find(".dt-owl-item").on("mouseleave",(function(t){a&&e.trigger("play.owl.autoplay",[r])})),e.find(".owl-nav div").on("mouseenter",(function(t){a&&e.trigger("stop.owl.autoplay")})),e.find(".owl-nav div").on("mouseleave",(function(t){a&&e.trigger("play.owl.autoplay",[r])}))}},t(".dt-owl-carousel-call, .related-projects").each((function(){t(this).the7OwlCarousel()})),t.fn.the7ElementorOwlCarousel=function(){var e=t(this);if(!e.length||e.hasClass("owl-loaded"))return;var i,n="true"===e.attr("data-auto-height"),o=e.attr("data-speed")?parseInt(e.attr("data-speed")):600,s="true"===e.attr("data-autoplay"),a=e.attr("data-autoplay_speed")?parseInt(e.attr("data-autoplay_speed")):6e3,r="rtl"==jQuery(document).attr("dir"),l="1"==e.attr("data-scroll-mode")?parseInt(e.attr("data-scroll-mode")):"page",d=(e.attr("data-next-icon")&&e.attr("data-next-icon"),e.attr("data-prev-icon")&&e.attr("data-prev-icon"),"1"==e.attr("data-scroll-mode")),c=e.next(".owl-nav").find(".owl-prev").html(),h=e.next(".owl-nav").find(".owl-next").html();const u=e.children().length;var p=function(){var t=e.find(".dt-owl-item img");t.length>0&&e.css("--dynamic-img-height",t.height()+"px")},f=function(t){s&&(t?e.trigger("play.owl.autoplay",[a]):e.trigger("stop.owl.autoplay"))},m=function(){clearTimeout(i),i=setTimeout((function(){e.layzrCarouselUpdate()}),20)},g=function(){m(),p(),e.hasClass("content-rollover-layout-list")&&!e.hasClass("disable-layout-hover")&&e.find(".post-entry-wrapper").clickOverlayGradient(),t(".dt-owl-item.cloned .photoswipe-wrapper, .dt-owl-item.cloned .photoswipe-item .dt-gallery-container",e).initPhotoswipe();var i=t(" .dt-owl-item.cloned .post-thumbnail-wrap",e);e.hasClass("albums-shortcode")&&(e.hasClass("gradient-overlay-layout-list")||e.hasClass("content-rollover-layout-list")?the7Utils.isFunction(t.fn.triggerClonedOverlayAlbumsClick)&&t(" .dt-owl-item.cloned .post-entry-content",e).triggerClonedOverlayAlbumsClick():the7Utils.isFunction(t.fn.triggerClonedAlbumsClick)&&i.triggerClonedAlbumsClick()),e.hasClass("gallery-shortcode")&&t(e).initCarouselClonedPhotoswipe()};e.on("initialized.owl.carousel",(function(t){g()}));const v=e.closest(".elementor-widget"),w=new The7ElementorSettings(v);$spg=new class{constructor(t){this.config=Object.assign({widgetSettingsObj:null,columnsKey:"widget_columns",gapKey:"gap_between_posts",itemsLen:0,the7WideDesktopColKey:"wide_desk_columns",the7WideDesktopBreakpointKey:"widget_columns_wide_desktop_breakpoint"},t),this.switchPoints={},this.widgetSettings={},this.config.widgetSettingsObj&&(this.widgetSettings=this.config.widgetSettingsObj.getSettings())}generateSwitchPointData(t){const e=The7ElementorSettings.getResponsiveControlValue(this.widgetSettings,this.config.columnsKey,"",t)||3,i=The7ElementorSettings.getResponsiveControlValue(this.widgetSettings,this.config.gapKey,"size",t)||15;return{loop:this.config.itemsLen>e,items:e,margin:the7Utils.parseIntParam(i)}}getSwitchPoints(){return 0===Object.keys(this.switchPoints).length&&this.generateSwitchPoints(),this.switchPoints}generateSwitchPoints(){if(!this.generateSwitchPointData("desktop"))return;const t=elementorFrontend.config.responsive.activeBreakpoints;let e=0;for(const[i,n]of Object.entries(t))"widescreen"!==i&&(this.switchPoints[e]=this.generateSwitchPointData(i),e=n.value);if(this.switchPoints[e]=this.generateSwitchPointData("desktop"),t.widescreen)this.switchPoints[t.widescreen.value]=this.generateSwitchPointData("widescreen");else{const t=this.config.widgetSettingsObj.getSettings(this.config.the7WideDesktopColKey);if(t){const e=this.getThe7WideDesktopBreakpointWidth();this.switchPoints[e]=Object.assign(this.generateSwitchPointData("desktop"),{loop:this.config.itemsLen>t,items:t})}}}getThe7WideDesktopBreakpointWidth(){let t=void 0!==dtLocal.elementor.settings.container_width?dtLocal.elementor.settings.container_width+1:1450;const e=this.config.widgetSettingsObj.getSettings(this.config.the7WideDesktopBreakpointKey);return e&&(t=e+1),t}}({widgetSettingsObj:w,the7WideDesktopColKey:w.getSettings("widget_columns_wide_desktop")?"widget_columns_wide_desktop":"wide_desk_columns",itemsLen:u});const y=$spg.getSwitchPoints(),b=y[Object.keys(y).pop()];e.owlCarousel({rtl:r,items:b.items,autoHeight:n,margin:b.margin,loadedClass:"owl-loaded",slideBy:l,loop:!0,smartSpeed:o,autoplay:s,autoplayTimeout:a,nav:!0,responsive:y,navElement:"div",navText:[c,h],dots:!0,dotsEach:d,autoRefreshInterval:180}),dtGlobals.addOnloadEvent((function(){f(dtGlobals.isInViewport(e))})),e.on("refreshed.owl.carousel",(function(t){g()})),e.on("change.owl.carousel",(function(i){"position"===i.property.name&&(m(),t(".animate-element:not(.start-animation):in-viewport",e).checkInViewport())})),e.on("resized.owl.carousel",(function(t){p()}));var C=e.find(".dt-owl-item");C.on("mouseenter",(function(t){f(!1)})),C.on("mouseleave",(function(t){f(!0)}));var x=e.find(".owl-nav div");x.on("mouseenter",(function(t){f(!1)})),x.on("mouseleave",(function(t){f(!0)}))},n.on("elementor/frontend/init",(function(){var e=1e3,o=e;t(".elementor-owl-carousel-call:not(.the7-woocommerce-loop-product-image)").each((function(){var i=this,s=t(this),a=s.closest(".elementor-widget-container");if(a.length){var r=n.height();if(n.scrollTop()+r+r>a.offset().top)s.the7ElementorOwlCarousel();else{var l=elementorModules.utils.Scroll.scrollObserver({offset:"0% 0% 100%",callback:t=>{t.isInViewport&&(l.unobserve(i),s.the7ElementorOwlCarousel())}});s.addClass("owl-loading"),setTimeout((function(){l.unobserve(i),s.the7ElementorOwlCarousel(),o+=500}),e),e+=500,l.observe(i)}}else s.the7ElementorOwlCarousel()}));i.one("click",'a[href^="#"]',(function(i){if(e!==o){i.preventDefault(),i.stopImmediatePropagation(),t(".elementor-owl-carousel-call:not(.the7-woocommerce-loop-product-image)").each((function(){t(this).the7ElementorOwlCarousel()}));var n=t(this);return setTimeout((function(){n.click()}),100),!1}}))})),dtGlobals.isMobile&&!dtGlobals.isWindowsPhone?n.bind("orientationchange",(function(t){k()})):dtGlobals.isAndroid&&n.bind("debouncedresize",(function(t){k()})),void 0===d&&(d=new Layzr({selector:".owl-thumb-lazy-load-show",attr:"data-src",attrSrcSet:"data-srcset",retinaAttr:"data-src-retina",hiddenAttr:"data-src-hidden",threshold:30,callback:function(){showLazyImg(t(this))}}));var T=t(".top-bar"),I=t(".masthead"),z=t(".overlay-navigation"),L=t(".sticky-header"),E=t(".sticky-top-line"),O=t("#main-slideshow, .photo-scroller"),P=(t(".header-side-left").length,t(".header-side-right").length,t("#main, #main-slideshow, .photo-scroller, .page-title, .fancy-header, .footer"),t(".floating-logo.side-header-menu-icon .branding, .side-header-h-stroke, #phantom"),t(".side-header")),D=(t(".page-template-template-microsite").length,s.hasClass("transparent")),A=t(".floating-navigation-below-slider").exists();if(t(".side-header-v-stroke").length>0)P.width(),t(".side-header-v-stroke").width();else P.width();
/*!-Show Hide side header*/
if(t.closeSideHeader=function(){a.removeClass("show-header"),a.addClass("closed-header"),t(".sticky-header-overlay").removeClass("active")},t.closeMobileHeader=function(){a.removeClass("show-mobile-header"),a.addClass("closed-mobile-header"),s.removeClass("show-sticky-mobile-header show-overlay-mobile-header").addClass("closed-overlay-mobile-header"),t(".mobile-sticky-header-overlay, .dt-mobile-menu-icon, .menu-toggle, .menu-close-toggle").removeClass("active")},L.length>0||z.length>0){t('<div class="lines-button x"><span class="menu-line"></span><span class="menu-line"></span><span class="menu-line"></span></div>').appendTo(".menu-toggle");var M=dtLocal.themeSettings.ToggleCaptionEnabled;ToggleCaption="disabled"!=M?"<span class='menu-toggle-caption'>"+dtLocal.themeSettings.ToggleCaption+"</span>":"",L.length>0&&s.append('<div class="sticky-header-overlay"></div>');var F=t(".menu-toggle"),H=t(".menu-close-toggle"),B=t(".menu-toggle:not(.active), .menu-close-toggle:not(.active)"),R=t(".sticky-header-overlay");F.on("click",(function(){if(!t(".header-under-side-line").length>0)var e=t(".side-header .menu-toggle");else e=t(".menu-toggle");e.hasClass("active")?(e.removeClass("active"),a.removeClass("show-header").addClass("closed-header"),R.removeClass("active"),t(".hide-overlay").removeClass("active")):(B.removeClass("active"),e.addClass("active").css({left:"",right:""}),a.addClass("show-header").removeClass("closed-header"),H.addClass("active"),R.addClass("active"),t(".hide-overlay").addClass("active"))})),H.on("click",(function(){var e=t(this);e.hasClass("active")?(e.removeClass("active"),a.removeClass("show-header").addClass("closed-header"),R.removeClass("active"),t(".hide-overlay").removeClass("active")):(B.removeClass("active"),e.addClass("active").css({left:"",right:""}),a.addClass("show-header").removeClass("closed-header"),R.addClass("active"),t(".hide-overlay").addClass("active"))})),R.on("click",(function(){t(this).hasClass("active")&&(B.removeClass("active"),a.removeClass("show-header").addClass("closed-header"),R.removeClass("active"))})),t(".hide-overlay").on("click",(function(){t(this).hasClass("active")&&(B.removeClass("active"),a.removeClass("show-header"),a.addClass("closed-header"),R.removeClass("active"))}))}function W(){var e=n.width(),i="",o=t(".side-header-menu-icon").length>0;if((e-a.innerWidth())/2>0&&(i=(e-a.innerWidth())/2),s.hasClass("header-side-right")&&a.hasClass("boxed")&&!L.length>0&&P.css({right:i}),navigator.userAgent.match(/Trident.*rv\:11\./)&&A&&D){I.insertAfter(O);var r=I.attr("style");I.not(".sticky-on").attr("style",r+"; top:"+O.height()+"px !important;")}var l=t(".floating-menu-icon-right");l.length>0&&a.hasClass("boxed")&&(B.css({right:i}),t(".branding").css({left:i})),a.hasClass("boxed")&&o&&!l.length>0&&(t(".floating-logo .branding").css({right:i}),B.css({left:i})),z.length>0&&o&&a.hasClass("boxed")&&(B.css({right:i}),t(".floating-logo .branding").css({left:i}))}if(W(),n.on("the7-resize-width-debounce",(function(){W()})),A&&s.hasClass("footer-overlap")&&I.insertAfter(O),navigator.userAgent.match(/Trident.*rv\:11\./)&&A&&D){I.insertAfter(O);var j=I.attr("style");I.attr("style",j+"; top:"+O.height()+"px !important;")}var $=I;if(t(".mixed-header").length>0&&($=t(".mixed-header")),t(".side-header .header-bar").wrap("<div class='header-scrollbar-wrap'></div>"),P.length>0&&"function"==typeof t.fn.mCustomScrollbar&&!dtGlobals.isMobile&&(t(".header-scrollbar-wrap").mCustomScrollbar({scrollInertia:150,callbacks:{whileScrolling:function(){t(".header-scrollbar-wrap").layzrInitialisation()}}}),t(".sub-downwards .main-nav").find(".slider-content").widgetScroller().css("visibility","visible")),P.length>0&&!t(".mCSB_container").length>0&&t(".side-header .header-scrollbar-wrap .header-bar").wrap("<div class='mCSB_container'></div>"),!s.hasClass("responsive-off")){var N=t(".mixed-header"),G=t(".masthead .near-logo-first-switch").clone(!0).addClass("show-on-first-switch"),Y=t(".masthead .near-logo-second-switch").clone(!0).addClass("show-on-second-switch"),q=I.find(".in-menu-first-switch").clone(!0).addClass("hide-on-desktop hide-on-second-switch show-on-first-switch"),U=I.find(".in-menu-second-switch").clone(!0).addClass("hide-on-desktop hide-on-first-switch show-on-second-switch"),V=I.find(".in-top-bar").clone(!0).addClass("hide-on-desktop hide-on-first-switch show-on-second-switch"),X=I.find(".in-top-bar-left").clone(!0).addClass("hide-on-desktop show-on-first-switch"),Z=I.find(".in-top-bar-right").clone(!0).addClass("hide-on-desktop  show-on-first-switch");if(N.length>0){var Q=N.find(".branding > a, .branding > img").clone(!0);$=N}else Q=t(".masthead:not(.mixed-header)").find(".branding > a, .branding > img").clone(!0),$=I;if(T.length>0&&"none"!=T.css("display"))var K=T.innerHeight();else K=0;var J=dtLocal.themeSettings.mobileHeader.mobileToggleCaptionEnabled;mobileToggleCaption="disabled"!=J?"<span class='menu-toggle-caption'>"+dtLocal.themeSettings.mobileHeader.mobileToggleCaption+"</span>":"",t("<div class='mobile-header-bar'><div class='mobile-navigation'></div><div class='mobile-mini-widgets'></div><div class='mobile-branding'></div></div>").appendTo(".masthead"),t(".mobile-header-bar .mobile-navigation").append("<a href='#' class='dt-mobile-menu-icon' aria-label='Mobile menu icon' aria-haspopup='true' aria-controls='menu'>"+mobileToggleCaption+"<div class='lines-button '><span class='menu-line'></span><span class='menu-line'></span><span class='menu-line'></span></div></a>"),t(G).appendTo(".mobile-header-bar .mobile-mini-widgets"),t(Y).appendTo(".mobile-header-bar .mobile-mini-widgets"),t(".left-widgets",T).append(V),t(".left-widgets",T).append(X),t(".right-widgets",T).append(Z).removeClass("select-type-menu list-type-menu select-type-menu-second-switch list-type-menu-second-switch"),t(".right-widgets",T).append(Z).removeClass("select-type-menu list-type-menu select-type-menu-second-switch list-type-menu-second-switch"),t(q).appendTo(".mobile-mini-widgets-in-menu"),t(U).appendTo(".mobile-mini-widgets-in-menu"),q.removeClass("select-type-menu list-type-menu select-type-menu-second-switch list-type-menu-second-switch"),U.removeClass("select-type-menu list-type-menu select-type-menu-first-switch list-type-menu-first-switch"),t(".mobile-header-bar .mobile-branding").append(Q);var tt=t(".dt-mobile-header");
/*!-Show Hide mobile header*/
if(tt.siblings().hasClass("dt-parent-menu-clickable")&&tt.addClass("dt-parent-menu-clickable"),!t(".mobile-mini-widgets-in-menu").find(".in-menu-first-switch ").length>0&&t(".mobile-mini-widgets-in-menu").addClass("first-switch-no-widgets"),!t(".mobile-mini-widgets-in-menu").find(".in-menu-second-switch ").length>0&&t(".mobile-mini-widgets-in-menu").addClass("second-switch-no-widgets"),G.removeClass("select-type-menu list-type-menu select-type-menu-second-switch list-type-menu-second-switch"),Y.removeClass("select-type-menu list-type-menu select-type-menu-first-switch list-type-menu-first-switch"),V.removeClass("show-on-desktop select-type-menu list-type-menu select-type-menu-first-switch list-type-menu-first-switch in-top-bar-left").addClass("hide-on-desktop hide-on-first-switch"),X.removeClass("show-on-desktop select-type-menu list-type-menu select-type-menu-second-switch list-type-menu-second-switch in-top-bar").addClass("hide-on-desktop hide-on-second-switch"),Z.removeClass("show-on-desktop select-type-menu list-type-menu  select-type-menu-second-switch list-type-menu-second-switch").addClass("hide-on-desktop"),t(".header-bar .mini-widgets > .mini-nav ").removeClass("select-type-menu-second-switch list-type-menu-second-switch select-type-menu-first-switch list-type-menu-first-switch"),t(".mini-nav.show-on-desktop:not(.show-on-first-switch):not(.show-on-second-switch)",T).removeClass("select-type-menu-second-switch list-type-menu-second-switch select-type-menu-first-switch list-type-menu-first-switch"),t(".masthead .hide-on-desktop").addClass("display-none"),t(".mobile-main-nav ").find("li").each((function(){var e=t(this),i=e.find(" > .dt-mega-menu-wrap > .sub-nav");if(e.hasClass("new-column")){var n=e.prev().find(" > .sub-nav");t(" > .sub-nav > *",e).appendTo(n)}i.unwrap()})).removeClass("dt-mega-menu dt-mega-parent hide-mega-title").find(" > .sub-nav").removeClass(" hover-style-bg"),tt.length>0){dtGlobals.mobileMenuPoint=50;var et=t(".dt-mobile-menu-icon"),it=!1,nt=$.offset().top;if(!t(".floating-btn").length>0&&t(".floating-mobile-menu-icon").length>0)et.first().clone(!0).insertBefore(et).addClass("floating-btn");var ot=t(".floating-btn");n.scroll((function(){dtGlobals.mobileMenuPoint=nt+$.height()+50,dtGlobals.winScrollTop>dtGlobals.mobileMenuPoint&&!1===it?(ot.parents(".masthead").addClass("show-floating-icon"),it=!0):dtGlobals.winScrollTop<=dtGlobals.mobileMenuPoint&&!0===it&&(ot.parents(".masthead").removeClass("show-floating-icon"),it=!1)}));et=t(".dt-mobile-menu-icon");!t(".mobile-sticky-header-overlay").length>0&&s.append('<div class="mobile-sticky-header-overlay"></div>');var st=t(".mobile-sticky-header-overlay");const fi=t(".dt-close-mobile-menu-icon"),mi=window.focusTrap.createFocusTrap(".dt-mobile-header",{escapeDeactivates:!1,clickOutsideDeactivates:!1,allowOutsideClick:!0,onDeactivate:function(){wi()},onActivate:function(){vi()}}),gi=function(t){"Escape"===t.key&&a.hasClass("show-mobile-header")&&fi.trigger("click")},vi=function(){document.addEventListener("keydown",gi)},wi=function(){document.removeEventListener("keydown",gi)};et.on("click",(function(e){e.preventDefault();var i=t(this);i.hasClass("active")?(i.removeClass("active"),a.removeClass("show-mobile-header").addClass("closed-mobile-header"),s.removeClass("show-mobile-overlay-header").addClass("closed-overlay-mobile-header"),i.parents("body").removeClass("show-sticky-mobile-header"),st.removeClass("active"),mi.deactivate()):(et.removeClass("active"),i.addClass("active"),a.addClass("show-mobile-header").removeClass("closed-mobile-header"),s.removeClass("closed-overlay-mobile-header").addClass("show-overlay-mobile-header"),st.removeClass("active"),i.parents("body").addClass("show-sticky-mobile-header"),st.addClass("active"),mi.activate())})),st.on("click",(function(){t(this).hasClass("active")&&(et.removeClass("active"),a.removeClass("show-mobile-header").addClass("closed-mobile-header"),s.removeClass("show-sticky-mobile-header").addClass("closed-overlay-mobile-header").addClass("closed-overlay-mobile-header"),st.removeClass("active"),a.removeClass("show-mobile-sidebar"),mi.deactivate())})),fi.on("click",(function(){a.removeClass("show-mobile-header"),a.addClass("closed-mobile-header"),s.removeClass("show-sticky-mobile-header"),s.removeClass("show-overlay-mobile-header").addClass("closed-overlay-mobile-header"),st.removeClass("active"),et.removeClass("active"),mi.deactivate()})),fi.on("keydown",(function(e){"Enter"===e.key&&t(this).trigger("click")})),t(".dt-mobile-header").wrapInner("<div class='mobile-header-scrollbar-wrap'></div>"),t(".mobile-header-scrollbar-wrap").layzrInitialisation(),tt.find(".slider-content").widgetScroller().css("visibility","visible"),!t(".touchevents").length>0&&(tt.on("mouseenter",(function(t){tt.css("overflow-y","auto")})),tt.on("mouseleave",(function(t){tt.css("overflow-y","hidden")})))}}t(".l-to-r-line > li:not(.menu-item-language) > a > span:last-child").not(".l-to-r-line > li > a > span.mega-icon").append("<i class='underline'></i>"),t(".not-clickable-item").on("click",(function(t){t.preventDefault(),t.stopPropagation()})),(t(".active-line-decoration").length>0||t(".hover-line-decoration").length>0)&&t(".main-nav > .menu-item > a").append("<span class='decoration-line'></span>");var at=t(".main-nav, .mini-nav, .mini-wpml .wpml-ls-item-legacy-dropdown"),rt=t(".masthead:not(.sub-downwards) .main-nav, .mini-nav, .mini-wpml .wpml-ls-item-legacy-dropdown"),lt=t(".main-nav");P=t(".side-header");t(".menu-item-language").each((function(){var e=t(this);e.children(".submenu-languages").length>0&&e.addClass("has-children")}));lt.clone(),t(".mini-nav").clone();t(".mini-nav select").change((function(){window.location.href=t(this).val()})),dtGlobals.isHovering=!1,t(".main-nav li",P).each((function(){var e=t(this);if(e.hasClass("new-column")){var i=e.prev().find(" > .sub-nav");t(" > .sub-nav > *",e).appendTo(i)}})),t(".sub-downwards .main-nav > li").each((function(){t(this).find(" > .dt-mega-menu-wrap > .sub-nav").unwrap()}));var dt,ct,ht=t(".select-type-menu, .select-type-menu-first-switch, .select-type-menu-second-switch, .mini-wpml .wpml-ls-item-legacy-dropdown"),ut="mini-sub-nav";ht.find("> ul").addClass(ut),t(".mini-wpml .wpml-ls-item-legacy-dropdown").find("> ul").addClass(ut),rt.each((function(){var e=t(this);t(".act",e).parents("li").addClass("act"),t(" li.has-children ",e).each((function(){var e,i,o=t(this),r=o.find("> a");if(o.parent().hasClass("main-nav")&&!o.parents().hasClass("side-header")&&t(".masthead").hasClass("show-sub-menu-on-hover"))r=o.find("> a");else if(o.parent().hasClass("main-nav")&&o.parents().hasClass("side-header")&&t(".masthead").hasClass("show-sub-menu-on-hover"))r=o;else if((o.parent().hasClass("sub-nav")||o.parents().hasClass("mini-nav"))&&t(".masthead").hasClass("show-sub-menu-on-hover"))r=o;if((dtGlobals.isMobile||dtGlobals.isWindowsPhone)&&o.find("> a").on("click",(function(e){t(this).hasClass("dt-clicked")?e.stopPropagation():(e.preventDefault(),at.find(".dt-clicked").removeClass("dt-clicked"),t(this).addClass("dt-clicked"))})),at.parents().hasClass("full-width")&&o.hasClass("mega-full-width")){if(at.parents(".header-bar").length>0)var l=at.parents(".header-bar").innerWidth();else l=at.parents(".ph-wrap").innerWidth();!P.length>0&&o.find(".sub-nav-widgets").css({width:l})}function d(o){if(o.parent("li").length>0)var s=o.parent(),r=o.siblings("div.dt-mega-menu-wrap, ul");else s=o,r=(d=o.find("> a")).siblings("div.dt-mega-menu-wrap, ul");var l=o.offset().left,d=o.offset().left,c=o.parents(".masthead");if(s.addClass("dt-hovered"),s.hasClass("dt-mega-menu")&&s.addClass("show-mega-menu"),dtGlobals.isHovering=!0,r.length>0&&(a.width()-(r.offset().left-a.offset().left)-r.width()<0&&r.addClass("right-overflow"),n.height()-(r.offset().top-dtGlobals.winScrollTop)-r.innerHeight()<0&&!r.parents().hasClass("sub-sideways")&&r.addClass("bottom-overflow"),n.height()-(r.offset().top-dtGlobals.winScrollTop)-r.innerHeight()<0&&!s.hasClass("dt-mega-menu")&&r.addClass("bottom-overflow")),(s.find(".dt-mega-menu-wrap").length>0&&s.find(".dt-mega-menu-wrap").offset().top+s.find(".dt-mega-menu-wrap").innerHeight())>n.height()&&r.parents().hasClass("sub-sideways")&&s.hasClass("dt-mega-menu")&&(o.find(".dt-mega-menu-wrap").height()<=n.height()?s.find(".dt-mega-menu-wrap").css({top:-(s.position().top+s.height()+o.find(".dt-mega-menu-wrap").height()-n.height())}):s.find(".dt-mega-menu-wrap").css({top:-(o.position().top-5)})),!P.length>0&&r.not(".right-overflow").css({left:d-l}),o.parents(".dt-mobile-header").length>0&&r.css({top:o.position().top-13-r.height()}),s.hasClass("mega-auto-width")){s.width();var h=c.offset().left,u=s.offset().left;if($_this_parents_ofs=s.offset().left-h,!P.length){var p=a.width();if(t(".boxed").length>0){u=s.position().left;r.innerWidth()>p-s.position().left&&r.css({left:-(r.innerWidth()-(p-u)+20)})}else{u=s.offset().left;r.innerWidth()>p-s.offset().left&&r.css({left:-(r.innerWidth()-(p-u)+20)})}r.innerWidth()>p&&(t(".boxed").length>0?r.css({width:c.width()-40,left:-(s.position().left+20)}):r.css({width:c.width()-40,left:-(u-h+20)}))}null!=typeof r.find(".slider-content")&&r.find(".slider-content").widgetScroller().css("visibility","visible"),r.layzrInitialisation()}if(s.hasClass("mega-full-width")){u=s.offset().left;if(o.parents(".header-bar").length>0)if(o.parents(".masthead").hasClass("full-width")){var f=o.parents(".header-bar").innerWidth()-40;h=o.parents(".header-bar").offset().left+20}else f=o.parents(".header-bar").innerWidth(),h=o.parents(".header-bar").offset().left;else if(o.parents(".masthead").hasClass("full-width"))f=o.parents(".ph-wrap").innerWidth()-40,h=o.parents(".ph-wrap").offset().left+20;else f=o.parents(".ph-wrap").innerWidth(),h=o.parents(".ph-wrap").offset().left;!P.length>0&&r.css({width:f,left:-(u-h)}),null!=typeof r.find(".slider-content")&&r.find(".slider-content").widgetScroller().css("visibility","visible"),r.layzrInitialisation()}clearTimeout(e),clearTimeout(i),e=setTimeout((function(){s.hasClass("dt-hovered")&&(r.stop().css("visibility","visible").animate({opacity:1},150,(function(){s.addClass("show-mega-menu-content")})),t(".searchform .submit",le).removeClass("act"),t(".mini-search").removeClass("act"),t(".mini-search.popup-search .popup-search-wrap",le).stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})))}),100)}function c(n){var o=n.find("> a").siblings("div.dt-mega-menu-wrap, ul");n.removeClass("dt-hovered"),dtGlobals.isHovering=!1,clearTimeout(e),clearTimeout(i),i=setTimeout((function(){n.hasClass("dt-hovered")||(o.stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),n.removeClass("show-mega-menu"),n.removeClass("show-mega-menu-content"),setTimeout((function(){n.hasClass("dt-hovered")||(o.removeClass("right-overflow"),o.removeClass("bottom-overflow"),n.find(".dt-mega-menu-wrap").css({top:""}),n.hasClass("mega-auto-width")&&o.css({width:"",left:""}))}),400))}),150),n.find("> a").removeClass("dt-clicked")}o.find("> a").on("focus",(function(e){"tap"==e.type&&e.stopPropagation();var i=t(this);d(i),t(" li.has-children").removeClass("parent-clicked"),i.parent().addClass("parent-clicked"),t(e.target).parents().hasClass("sub-nav")||(t(" li.has-children").removeClass("dt-hovered"),i.parent().addClass("dt-hovered")),t(".main-nav > li:not(.dt-hovered) > .sub-nav, .main-nav >  li:not(.dt-hovered) > .dt-mega-menu-wrap").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),t(" .main-nav .sub-nav li:not(.parent-clicked) .sub-nav").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")}))})),o.find("> a").on("focusout",(function(n){var o=t(this),s=t("this").siblings("div.dt-mega-menu-wrap, ul");dtGlobals.isHovering=!1,clearTimeout(e),clearTimeout(i),i=setTimeout((function(){o.parent().hasClass("dt-hovered")||(s.stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),o.parent().removeClass("show-mega-menu"),o.parent().removeClass("show-mega-menu-content"),setTimeout((function(){o.parent().hasClass("dt-hovered")||(s.removeClass("right-overflow"),s.removeClass("bottom-overflow"),o.parent().find(".dt-mega-menu-wrap").css({top:""}),o.parent().hasClass("mega-auto-width")&&s.css({width:"",left:""}))}),400))}),150),o.parent().removeClass("parent-clicked")})),t(".masthead").hasClass("show-sub-menu-on-hover")?(r.on("mouseenter tap",(function(e){"tap"==e.type&&e.stopPropagation(),d(t(this))})),o.on("mouseleave",(function(e){c(t(this))}))):(s.on("click",(function(e){t(e.target).hasClass("sub-nav")&&t(e.target).hasClass("dt-mega-menu-wrap")||(t(" #primary-menu li.dt-hovered:not(.dt-mega-menu) > .sub-nav").animate({opacity:0},100,(function(){t(this).css("visibility","hidden")})),t(" #primary-menu li.dt-mega-menu > .is-clicked").siblings(" .dt-mega-menu-wrap").animate({opacity:0},100,(function(){t(this).css("visibility","hidden")})),t("#primary-menu  li.has-children").removeClass("dt-hovered"),t("#primary-menu  li.has-children > a").removeClass("is-clicked"))})),r.on("click",(function(e){if(!t(this).parents().hasClass("mobile-main-nav")){"tap"==e.type&&e.stopPropagation();var i=t(this),n=i.parent("li");if(!i.hasClass("is-clicked"))return d(i),t(" li.has-children > a").removeClass("is-clicked"),t(" li.has-children").removeClass("parent-clicked"),i.parent().addClass("parent-clicked"),t(e.target).parents().hasClass("sub-nav")||(t(" li.has-children").removeClass("dt-hovered"),i.parent().addClass("dt-hovered")),t(".main-nav > li:not(.dt-hovered) > .sub-nav, .main-nav >  li:not(.dt-hovered) > .dt-mega-menu-wrap").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),t(".main-nav .sub-nav li:not(.parent-clicked) .sub-nav").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),i.addClass("is-clicked"),!1;c(n),i.removeClass("is-clicked"),i.parent().removeClass("parent-clicked")}})))}))})),t.fn.touchMenuItem=function(){return this.each((function(){var e=t(this);if(!e.hasClass("item-ready")){s.on("touchend",(function(e){t(".mobile-true .has-children > a").removeClass("is-clicked")}));var i=t(this),n=i.attr("target")?i.attr("target"):"_self";i.on("touchstart",(function(t){origY=t.originalEvent.touches[0].pageY,origX=t.originalEvent.touches[0].pageX})),i.on("touchend",(function(e){var o=e.originalEvent.changedTouches[0].pageX,s=e.originalEvent.changedTouches[0].pageY;if(origY==s||origX==o)if(i.hasClass("is-clicked"));else if(i.parent().hasClass("dt-hovered"))return e.preventDefault(),t(".mobile-true .has-children > a").removeClass("is-clicked"),i.addClass("is-clicked"),window.open(i.attr("href"),n),!1})),e.addClass("item-ready")}}))},t(".mobile-true .main-nav .has-children > a").touchMenuItem(),ht.on("mouseenter tap",(function(e){"tap"==e.type&&e.stopPropagation();var i=t(this);i.addClass("dt-hovered"),a.width()-(i.children(".mini-sub-nav").offset().left-a.offset().left)-i.find(" > .mini-sub-nav").width()<0&&i.children(".mini-sub-nav").addClass("right-overflow"),n.height()-(i.children(".mini-sub-nav").offset().top-dtGlobals.winScrollTop)-i.children(".mini-sub-nav").height()<0&&!i.parents(".dt-mobile-header").length>0&&i.children(".mini-sub-nav").addClass("bottom-overflow"),i.parents(".dt-mobile-header").length>0&&i.children(".mini-sub-nav").css({top:i.position().top-13-i.children(".mini-sub-nav").height()}),dtGlobals.isHovering=!0,clearTimeout(dt),clearTimeout(ct),dt=setTimeout((function(){i.hasClass("dt-hovered")&&(t(".mini-sub-nav").stop().animate({opacity:0},50,(function(){t(this).css("visibility","hidden")})),i.children(".mini-sub-nav").stop().css("visibility","visible").animate({opacity:1},150))}),100)})),ht.on("mouseleave",(function(e){var i=t(this);i.removeClass("dt-hovered"),dtGlobals.isHovering=!1,clearTimeout(dt),clearTimeout(ct),ct=setTimeout((function(){i.hasClass("dt-hovered")||(i.parents().hasClass("dt-mega-menu")||i.children(".mini-sub-nav").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),setTimeout((function(){i.hasClass("dt-hovered")||(i.children(".mini-sub-nav").removeClass("right-overflow"),i.children(".mini-sub-nav").removeClass("bottom-overflow"))}),400))}),150)})),dtGlobals.desktopProcessed=!1,dtGlobals.mobileProcessed=!1;var pt=t(".sticky-mobile-header").exists();window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")&&(t(".masthead:not(.side-header):not(#phantom)").addClass("masthead-mobile-header"),t("body:not(.overlay-navigation):not(.sticky-header) .side-header:not(#phantom)").addClass("masthead-mobile-header").addClass("desktop-side-header")),window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&window.innerWidth>dtLocal.themeSettings.mobileHeader.secondSwitchPoint&&!s.hasClass("responsive-off")?t(".left-widgets",T).find(".in-top-bar-left").length>0||t(".right-widgets",T).find(".in-top-bar-right").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"):window.innerWidth<=dtLocal.themeSettings.mobileHeader.secondSwitchPoint&&!s.hasClass("responsive-off")?t(".left-widgets",T).find(".in-top-bar").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"):!t(".mini-widgets",T).find(".show-on-desktop").length>0?T.addClass("top-bar-empty"):T.removeClass("top-bar-empty");var ft=t(".floating-navigation-below-slider").exists();if(t(".sticky-mobile-header").exists()&&!s.hasClass("responsive-off")){var mt=t(".masthead:not(.side-header):not(#phantom), body:not(.overlay-navigation):not(.sticky-header) .side-header:not(#phantom)"),gt=mt.find(".mobile-header-bar");$stickyMobileLogo=mt.find(".mobile-branding"),mobileLogoURL=t(".mobile-branding a").attr("href"),t(".sticky-mobile-logo-first-switch").exists()||dtLocal.themeSettings.stickyMobileHeaderFirstSwitch.logo.html&&(null==mobileLogoURL?t(dtLocal.themeSettings.stickyMobileHeaderFirstSwitch.logo.html).addClass("sticky-mobile-logo-first-switch").prependTo($stickyMobileLogo):t('<a class="sticky-mobile-logo-first-switch" href="'+mobileLogoURL+'">'+dtLocal.themeSettings.stickyMobileHeaderFirstSwitch.logo.html+" </a>").prependTo($stickyMobileLogo)),t(".sticky-mobile-logo-second-switch").exists()||dtLocal.themeSettings.stickyMobileHeaderSecondSwitch.logo.html&&(null==mobileLogoURL?t(dtLocal.themeSettings.stickyMobileHeaderSecondSwitch.logo.html).addClass("sticky-mobile-logo-second-switch").prependTo($stickyMobileLogo):t('<a class="sticky-mobile-logo-second-switch" href="'+mobileLogoURL+'">'+dtLocal.themeSettings.stickyMobileHeaderSecondSwitch.logo.html+" </a>").prependTo($stickyMobileLogo));var vt=0,wt=0,yt=O.height(),bt=0,Ct=0,xt=!1;if(!D){t("<div class='mobile-header-space'></div>").insertBefore(mt);var _t=t(".mobile-header-space")}t(".no-cssgridlegacy.no-cssgrid").length>0&&ft&&(D?(mt.css({top:yt}),_t.css({top:yt})):(_t.insertAfter(O),mt.insertAfter(O))),dtGlobals.resetMobileSizes=function(e){if(window.innerWidth>dtLocal.themeSettings.mobileHeader.firstSwitchPoint)return t(".is-safari").length>0&&mt.css({width:"","max-width":""}),mt.removeClass("sticky-mobile-off sticky-mobile-on"),xt=!1,!1;wt=t("#wpadminbar").exists()&&!Modernizr.mq("only screen and (max-width:600px)")?t("#wpadminbar").height():0,window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&window.innerWidth>dtLocal.themeSettings.mobileHeader.secondSwitchPoint&&!s.hasClass("responsive-off")?t(".left-widgets",T).find(".in-top-bar-left").length>0||t(".top-bar .right-widgets").find(".in-top-bar-right").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"):t(".left-widgets",T).find(".in-top-bar").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"),vt=!T.exists()||T.is(":hidden")||T.hasClass("top-bar-empty")||T.hasClass("hide-top-bar")?0:T.innerHeight(),window.innerWidth<dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&window.innerWidth>dtLocal.themeSettings.mobileHeader.secondSwitchPoint?(bt=dtLocal.themeSettings.mobileHeader.firstSwitchPointHeight+vt,dtLocal.themeSettings.mobileHeader.firstSwitchPointHeight):(bt=dtLocal.themeSettings.mobileHeader.secondSwitchPointHeight+vt,dtLocal.themeSettings.mobileHeader.secondSwitchPointHeight),ft?ft&&!D?Ct=e:ft&&D?Ct=e-bt+vt:gt.offset().top:Ct=vt,mt.hasClass("sticky-mobile-on")&&mt.css({top:wt-vt}),D||(_t.css({height:bt}),_t.css({top:e})),t(".is-safari").length>0&&mt.css({width:document.documentElement.clientWidth,"max-width":document.documentElement.clientWidth})},dtGlobals.resetMobileSizes(O.height()),n.on("resize debouncedresize",(function(){dtGlobals.resetMobileSizes(O.height())})),n.on("scroll",(function(){if(window.innerWidth>dtLocal.themeSettings.mobileHeader.firstSwitchPoint)return!1;var e=dtGlobals.winScrollTop;e>Ct&&!xt&&t(document).height()>t(window).height()?(mt.removeClass("sticky-mobile-off").addClass("sticky-mobile-on"),A&&pt&&mt.addClass("fixed-mobile-header"),mt.css({top:wt-vt}),xt=!0):e<=Ct&&xt&&(mt.removeClass("sticky-mobile-on").addClass("sticky-mobile-off"),A&&pt&&mt.removeClass("fixed-mobile-header"),mt.css({top:0}),t(".no-cssgridlegacy.no-cssgrid").length>0&&ft&&mt.css({top:yt}),xt=!1)}))}dtGlobals.isMobile&&window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!dtGlobals.isiPad&&!s.hasClass("responsive-off")&&(dtLocal.themeSettings.floatingHeader.showMenu&&t(".phantom-sticky").exists()&&D&&I.addClass("fixed-masthead"),dtLocal.themeSettings.floatingHeader.showMenu=!1);D=s.hasClass("transparent");var St=t(".phantom-sticky").exists();t(".side-header").exists();if(dtLocal.themeSettings.floatingHeader.showMenu&&St){var kt=t(".branding a",I).attr("href"),Tt=(L=I).find(".header-bar");(Bt=L.find(".branding")).find("img");Bt.find("a.same-logo").length>0||t(".sticky-logo").exists()||dtLocal.themeSettings.floatingHeader.logo.html&&dtLocal.themeSettings.floatingHeader.logo.showLogo&&(null==kt?t(dtLocal.themeSettings.floatingHeader.logo.html).addClass("sticky-logo").prependTo(Bt):t('<a class="sticky-logo" href="'+kt+'">'+dtLocal.themeSettings.floatingHeader.logo.html+" </a>").prependTo(Bt));K=0;var It=0,zt=0,Lt=0,Et=0,Ot=!1,Pt="";if(!D){t("<div class='header-space'></div>").insertAfter(L);var Dt=t(".header-space")}L.addClass("sticky-off fixed-masthead");var At=!1;function Mt(){if(window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off"))return!1;var t=dtGlobals.winScrollTop;if(t>Et+1&&!Ot&&!dtGlobals.isHovering){if(L.removeClass("sticky-off").addClass("sticky-on"),D||Dt.removeClass("sticky-space-off").addClass("sticky-space-on"),s.hasClass("floating-top-bar")?L.css({top:It}):L.css({top:It-K}),Ot=!0,ft&&D&&(L.css({transform:"translateY(0)"}),navigator.userAgent.match(/Trident.*rv\:11\./))){var e=L.attr("style");L.attr("style",e+"; top:"+K+"px !important;")}}else if(t<=Et+1&&Ot&&(L.removeClass("sticky-on").addClass("sticky-off"),D||Dt.removeClass("sticky-space-on").addClass("sticky-space-off"),L.css({top:0}),Ot=!1,ft&&D&&(L.css({transform:"translateY(-100%)"}),navigator.userAgent.match(/Trident.*rv\:11\./)))){e=L.attr("style");L.not(".sticky-on").attr("style",e+"; top:"+O.height()+"px !important;")}t>Et+1&&t<=Et+1+Lt-dtLocal.themeSettings.floatingHeader.height?(Pt="changing",Tt.css({transition:"none",height:Et+Lt-t})):t>Et+1+dtLocal.themeSettings.floatingHeader.height&&"end"!==Pt?(Pt="end",Tt.css({height:dtLocal.themeSettings.floatingHeader.height,transition:"all 0.3s ease"})):t<=Et+1&&"start"!==Pt&&(Pt="start",Tt.css({height:Lt,transition:"all 0.1s ease"}))}dtGlobals.resetSizes=function(e){if(window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off"))return At||(At=!0,L.removeClass("sticky-off sticky-on"),D||Dt.removeClass("sticky-space-off sticky-space-on"),L.css({top:"",transform:""}),Ot=!1,Pt="",t(".is-safari").length>0&&L.css({width:"","max-width":""})),!1;At&&(At=!1),Ot||""!==Pt||(L.addClass("sticky-off"),D||Dt.addClass("sticky-space-off")),It=t("#wpadminbar").exists()?t("#wpadminbar").height():0,K=!T.exists()||T.is(":hidden")||T.hasClass("top-bar-empty")||T.hasClass("hide-top-bar")?0:T.innerHeight(),zt=dtLocal.themeSettings.desktopHeader.height+K,Lt=dtLocal.themeSettings.desktopHeader.height,ft?ft&&!D?Et=s.hasClass("floating-top-bar")?e-K:e:ft&&D?Et=s.hasClass("floating-top-bar")?e-Lt-K:e-Lt:Tt.offset().top:Et=s.hasClass("floating-top-bar")?0:K,D||Dt.css({height:zt}),t(".is-safari").length>0&&(a.hasClass("boxed")?L.css({width:a.width(),"max-width":a.width()}):L.css({width:document.documentElement.clientWidth,"max-width":document.documentElement.clientWidth}))},dtGlobals.resetSizes(O.height()),n.on(" debouncedresize",(function(){dtGlobals.resetSizes(O.height())})),Mt(),n.on("scroll",(function(){Mt()}))}if(E.exists()){var Ft=0,Ht=(It=0,K=0,!1);E.addClass("sticky-top-line-off"),t(".top-line-space").exists()||D||t("<div class='top-line-space'></div>").insertBefore(E);var Bt;kt=t(".branding a",E).attr("href"),(Bt=E.find(".branding")).find("img");Bt.find("a.same-logo").length>0||t(".sticky-logo").exists()||dtLocal.themeSettings.topLine.floatingTopLine.logo.html&&dtLocal.themeSettings.topLine.floatingTopLine.logo.showLogo&&(null==kt?t(dtLocal.themeSettings.topLine.floatingTopLine.logo.html).addClass("sticky-logo").prependTo(Bt):t('<a class="sticky-logo" href="'+kt+'">'+dtLocal.themeSettings.topLine.floatingTopLine.logo.html+" </a>").prependTo(Bt));At=!1;dtGlobals.resetTopLineSizes=function(e){if(window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off"))return At||(At=!0,E.removeClass("sticky-top-line-on"),E.css({top:""}),Ht=!1),!1;At&&(At=!1),It=t("#wpadminbar").exists()?t("#wpadminbar").height():0,K=!T.exists()||T.is(":hidden")||T.hasClass("top-bar-empty")||T.hasClass("hide-top-bar")?0:T.innerHeight(),Ft=E.find(".header-bar").height()+K,t(".top-line-space").css({height:Ft}),t(".is-safari").length>0&&(a.hasClass("boxed")?E.css({width:a.width(),"max-width":a.width()}):E.css({width:document.documentElement.clientWidth,"max-width":document.documentElement.clientWidth}))},dtGlobals.resetTopLineSizes(O.height()),n.on("resize debouncedresize",(function(){dtGlobals.resetTopLineSizes(O.height())})),n.on("scroll",(function(){if(window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off"))return!1;var t=dtGlobals.winScrollTop>E.height();t&&!Ht?(E.removeClass("sticky-top-line-off").addClass("sticky-top-line-on"),E.hasClass("mixed-floating-top-bar")?E.css({top:It}):E.css({top:It-K}),Ht=!0):!t&&Ht&&(E.removeClass("sticky-top-line-on").addClass("sticky-top-line-off"),E.css({top:It}),Ht=!1)}))}if(dtLocal.themeSettings.floatingHeader.showMenu&&dtLocal.themeSettings.floatingHeader.showMenu&&!St){var Rt=t(".phantom-fade").exists(),Wt=t(".phantom-slide").exists(),jt=t(".split-header").exists();if(Rt||Wt){var $t=t(".masthead:not(#phantom) .main-nav").clone(!0).removeAttr("id"),Nt=(kt=t(".branding a",I).attr("href"),s.hasClass("floating-top-bar")?t(".masthead:not(#phantom) .top-bar").clone(!0):""),Gt=!1;if(jt){var Yt=I.attr("class"),qt=t(".side-header-h-stroke, .split-header"),Ut=(Fe=t('<div id="phantom" class="'+Yt+'"><div class="ph-wrap"></div></div>').appendTo("body")).find(".ph-wrap"),Vt=Fe.find(".widget-box"),Xt=$t.find(".mini-widgets"),Zt=qt.find(".branding");t(".phantom-custom-logo-on").length>0&&dtLocal.themeSettings.floatingHeader.logo.html&&dtLocal.themeSettings.floatingHeader.logo.showLogo&&(null==kt?t(dtLocal.themeSettings.floatingHeader.logo.html).prependTo(Zt):t('<a class="phantom-top-line-logo" href="'+kt+'">'+dtLocal.themeSettings.floatingHeader.logo.html+" </a>").prependTo(Zt)),($t=t(".split-header .header-bar").clone(!0)).appendTo(Ut).find(".main-nav").removeAttr("id"),s.hasClass("floating-top-bar")&&Nt.insertBefore(Ut)}else{Yt=I.attr("class"),Ut=(Fe=t('<div id="phantom" class="'+Yt+'"><div class="ph-wrap"><div class="logo-box"></div><div class="menu-box"></div><div class="widget-box"></div></div></div>').appendTo("body")).find(".menu-box"),Vt=Fe.find(".widget-box");if(t(".classic-header").length>0)Xt=t(".header-bar .navigation .mini-widgets").clone(!0);else if(jt);else Xt=t(".header-bar .mini-widgets").clone(!0);$t.appendTo(Ut),Xt.appendTo(Vt),s.hasClass("floating-top-bar")&&Nt.prependTo(Fe),dtLocal.themeSettings.floatingHeader.logo.html&&dtLocal.themeSettings.floatingHeader.logo.showLogo&&(Fe.find(".ph-wrap").addClass("with-logo"),null==kt?Fe.find(".logo-box").html('<a href="'+dtLocal.themeSettings.floatingHeader.logo.url+'">'+dtLocal.themeSettings.floatingHeader.logo.html+" </a>"):Fe.find(".logo-box").html('<a href="'+kt+'">'+dtLocal.themeSettings.floatingHeader.logo.html+" </a>"))}var Qt,Kt=t("#phantom");a.hasClass("boxed")&&Fe.addClass("boxed").find(".ph-wrap").addClass("boxed"),Kt.removeClass("show-phantom").addClass("hide-phantom").css("visibility","hidden"),dtGlobals.addOnloadEvent((function(){clearTimeout(Qt),Qt=setTimeout((function(){Kt.css("visibility","")}),150)})),Ut.find(".is-loading").removeClass("is-loading"),Ut.layzrInitialisation();var Jt=!1,te=dtGlobals.winScrollTop;yt=O.height(),I.height();if(ft&&D);else if(ft);else dtLocal.themeSettings.floatingHeader.showAfter;n.on("scroll",(function(){if(window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off"))return!1;var t=dtGlobals.winScrollTop,e=O.height(),i=I.height();if(ft&&D)var n=t>e&&!1===Gt,a=t<=e&&!0===Gt;else if(ft)n=t>e+i&&!1===Gt,a=t<=e+i&&!0===Gt;else n=t>dtLocal.themeSettings.floatingHeader.showAfter&&!1===Gt,a=t<=dtLocal.themeSettings.floatingHeader.showAfter&&!0===Gt;n?o.hasClass("menu-open")||dtGlobals.isHovering||Jt||(Jt=!0,Kt.removeClass("hide-phantom").addClass("show-phantom"),Gt=!0):a&&Jt&&(o.hasClass("menu-open")||(Jt=!1,Kt.removeClass("show-phantom").addClass("hide-phantom"),Gt=!1))}))}}var ee=t(".side-header:not(.sub-sideways ) .main-nav li.has-children > a:not(.not-clickable-item), .side-header:not(.sub-sideways ) .level-arrows-on > li.has-children > a, .mobile-main-nav li.has-children > a");t('<i class="next-level-button"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16;" xml:space="preserve"><path d="M13.5,7H9V2.5c0-0.6-0.4-1-1-1s-1,0.4-1,1V7H2.5c-0.6,0-1,0.4-1,1s0.4,1,1,1H7v4.5c0,0.6,0.4,1,1,1s1-0.4,1-1V9h4.5c0.6,0,1-0.4,1-1S14.1,7,13.5,7z"/></svg></i>').insertAfter(ee),t(".sub-downwards .main-nav li.has-children, .mobile-main-nav li.has-children").each((function(){var e=t(this),i=e.find(" > .sub-nav, .sub-menu");e.find(".sub-nav li, .sub-menu li").hasClass("act")&&e.addClass("active"),e.find(".sub-nav li.act, .sub-menu li.act").hasClass("act")&&(e.addClass("open-sub"),i.stop(!0,!0).slideDown(100),i.layzrInitialisation()),e.find(" > .next-level-button").on("click",(function(e){var n=t(this).parent();n.hasClass("active")?(i.stop(!0,!0).slideUp(500,(function(){t(" .main-nav").layzrInitialisation()})),n.removeClass("active"),n.removeClass("open-sub"),n.find("a").removeClass("act")):(n.siblings().find(" .sub-nav, .dt-mega-menu-wrap, .sub-menu").stop(!0,!0).slideUp(400),i.stop(!0,!0).slideDown(500),n.siblings().removeClass("active"),n.addClass("active"),n.siblings().removeClass("open-sub"),n.addClass("open-sub"),n.siblings().find("> a").removeClass("act"),n.find("a").addClass("act"),t(" .main-nav").layzrInitialisation())}))})),!t(".dt-parent-menu-clickable").length>0&&t(".sub-downwards .main-nav li > a, .mobile-main-nav li.has-children > a").each((function(){var e=t(this);e.parent("li").find(".sub-nav li, .sub-menu li").hasClass("act")&&e.addClass("act"),e.parent("li").find(".sub-nav li.act, .sub-menu li.act").hasClass("act")&&(e.parent("li").addClass("open-sub"),e.siblings(".sub-nav, .sub-menu").stop(!0,!0).slideDown(100,(function(){e.siblings(".sub-nav, .sub-menu").layzrInitialisation()}))),e.on("click",(function(i){$menuItem=e.parent(),$menuItem.hasClass("has-children menu-item-language")&&i.preventDefault(),e.hasClass("act")?(e.siblings(".sub-nav, .sub-menu").stop(!0,!0).slideUp(500),e.removeClass("act"),e.parent("li").removeClass("open-sub")):(e.parent().siblings().find(".sub-nav, .dt-mega-menu-wrap, .sub-menu").stop(!0,!0).slideUp(400),e.siblings(".sub-nav, .sub-menu").stop(!0,!0).slideDown(500),e.parent().siblings().find("> a").removeClass("act"),e.addClass("act"),e.parent("li").siblings().removeClass("open-sub active"),e.parent("li").addClass("open-sub active")),"function"==typeof t.fn.mCustomScrollbar&&t(".header-bar").mCustomScrollbar("update")}))})),t(".custom-nav > li > a").click((function(e){var i=t(this).parent(),n=t(this);i.hasClass("has-children")&&e.preventDefault(),n.hasClass("active")?(n.next().stop(!0).slideUp(500),n.removeClass("active")):(t(".custom-nav > li > ul").stop(!0).slideUp(400),n.next().stop(!0).slideDown(500),t(".custom-nav > li > a").removeClass("active"),n.addClass("active")),i.siblings().removeClass("act"),i.addClass("act")})),t(".custom-nav > li > ul").each((function(){clearTimeout(undefined),$this=t(this),$thisChildren=$this.find("li"),$thisChildren.hasClass("act")&&($this.prev().addClass("active"),$this.parent().siblings().removeClass("act"),$this.parent().addClass("act"),$this.slideDown(500))})),t.HoverDir=function(e,i){this.$el=t(i),this._init(e)},t.HoverDir.defaults={speed:300,easing:"ease",hoverDelay:0,inverse:!1},t.HoverDir.prototype={_init:function(e){this.options=t.extend(!0,{},t.HoverDir.defaults,e),this.transitionProp="all "+this.options.speed+"ms "+this.options.easing,this.support=Modernizr.csstransitions,this._loadEvents()},_loadEvents:function(){var e=this;this.$el.on("mouseenter.hoverdir, mouseleave.hoverdir",(function(i){var n=t(this),o=n.find(".rollover-content, .gallery-rollover, .post-entry-content"),s=e._getDir(n,{x:i.pageX,y:i.pageY}),a=e._getStyle(s);"mouseenter"===i.type?(o.hide().css(a.from),clearTimeout(e.tmhover),e.tmhover=setTimeout((function(){o.show(0,(function(){var i=t(this);e.support&&i.css("transition",e.transitionProp),e._applyAnimation(i,a.to,e.options.speed)}))}),e.options.hoverDelay)):(e.support&&o.css("transition",e.transitionProp),clearTimeout(e.tmhover),e._applyAnimation(o,a.from,e.options.speed))}))},_getDir:function(t,e){var i=t.width(),n=t.height(),o=(e.x-t.offset().left-i/2)*(i>n?n/i:1),s=(e.y-t.offset().top-n/2)*(n>i?i/n:1);return Math.round((Math.atan2(s,o)*(180/Math.PI)+180)/90+3)%4},_getStyle:function(t){var e,i,n={left:"0px",top:"-100%"},o={left:"0px",top:"100%"},s={left:"-100%",top:"0px"},a={left:"100%",top:"0px"},r={top:"0px"},l={left:"0px"};switch(t){case 0:e=this.options.inverse?o:n,i=r;break;case 1:e=this.options.inverse?s:a,i=l;break;case 2:e=this.options.inverse?n:o,i=r;break;case 3:e=this.options.inverse?a:s,i=l}return{from:e,to:i}},_applyAnimation:function(e,i,n){t.fn.applyStyle=this.support?t.fn.css:t.fn.animate,e.stop().applyStyle(i,t.extend(!0,[],{duration:n+"ms"}))}};var ie=function(t){window.console&&window.console.error(t)};t.fn.hoverdir=function(e){var i=t.data(this,"hoverdir");if("string"==typeof e){var n=Array.prototype.slice.call(arguments,1);this.each((function(){i?the7Utils.isFunction(i[e])&&"_"!==e.charAt(0)?i[e].apply(i,n):ie("no such method '"+e+"' for hoverdir instance"):ie("cannot call methods on hoverdir prior to initialization; attempted to call method '"+e+"'")}))}else this.each((function(){i?i._init():i=t.data(this,"hoverdir",new t.HoverDir(e,this))}));return i},t.fn.addRollover=function(){return this.each((function(){var e=t(this);e.hasClass("this-ready")||(e.append("<i></i>"),e.find(".rollover-thumbnails").length&&e.addClass("rollover-thumbnails-on"),e.parent().find(".links-container").length&&e.addClass("rollover-buttons-on"),e.addClass("this-ready"))}))},
/*!-Scale in hover*/
t.fn.scaleInHover=function(){return this.each((function(){var e=t(this);if(!e.hasClass("scale-ready")){var i=e.find("img.preload-me"),n=parseInt(i.attr("width"))/parseInt(i.attr("height"));n<2&&n>=1.5?e.addClass("ratio_3-2"):n<1.5&&n>=1?e.addClass("ratio_4-3"):n<1&&n>=.75?e.addClass("ratio_3-4"):n<.75&&n>=.6?e.addClass("ratio_2-3"):e.removeClass("ratio_2-3").removeClass("ratio_3-2").removeClass("ratio-2").removeClass("ratio_4-3").removeClass("ratio_3-4"),n>=2&&e.addClass("ratio-2"),.5==n&&e.addClass("ratio_0-5"),1==n&&e.removeClass("ratio_2-3").removeClass("ratio-2").removeClass("ratio_3-2").removeClass("ratio_4-3").removeClass("ratio_3-4"),e.addClass("scale-ready")}}))},t.fn.touchNewHover=function(){return this.each((function(){var e=t(this);e.hasClass("this-ready")||(t(".rollover-content",this).length>0&&(s.on("touchend",(function(e){t(".mobile-true .rollover-content, .mobile-true .rollover-project, .mobile-true .woocom-project").removeClass("is-clicked")})),e.on("touchstart",(function(t){origY=t.originalEvent.touches[0].pageY,origX=t.originalEvent.touches[0].pageX})),e.on("touchend",(function(i){var n=i.originalEvent.changedTouches[0].pageX,o=i.originalEvent.changedTouches[0].pageY;if(origY==o||origX==n){if(!e.hasClass("is-clicked"))return t(".links-container > a",e).on("touchend",(function(t){t.stopPropagation(),e.addClass("is-clicked")})),i.preventDefault(),t(".mobile-true .rollover-content, .mobile-true .rollover-project,.mobile-true .woocom-project").removeClass("is-clicked"),e.addClass("is-clicked"),e.find(".rollover-content").addClass("is-clicked"),!1;if(e.find(".dt-gallery-container").length>0&&e.find(".rollover-content").on("click.dtAlbums",(function(i){e.find(".rollover-content").off("click.dtAlbums"),t(this).find("a.dt-gallery-pspw, .dt-trigger-first-pspw, .dt-pswp-item").first().trigger("click")})),t(this).find(".rollover-click-target.go-to").length>0)window.location.href=t(this).find(".rollover-click-target.go-to").attr("href");else if(t(this).hasClass("woocom-project")){if(t(i.target).is(".add_to_cart_button"))return!0;window.location.href=t(this).find(" > a").attr("href")}}}))),e.addClass("this-ready"))}))},
/*!Trigger post click for blog Overlay (background)/Overlay (gradient) layouts */
t.fn.triggerPostClick=function(){return this.each((function(){var e=t(this);if(!e.hasClass("blog-post-ready")){var i=e.siblings().find(".post-thumbnail-rollover").first(),n=e.find(".entry-meta a, .fancy-date a, .fancy-categories a");if(i.length>0){i.on("click",(function(t){t.preventDefault(),t.stopPropagation(),i.parents(".ts-wrap").hasClass("ts-interceptClicks")}));var o=!1;e.on("click",(function(t){if(!e.parents(".ts-wrap").hasClass("ts-interceptClicks"))return o||(o=!0,window.location.href=i.attr("href"),o=!1),!1})),e.find(n).click((function(t){t.stopPropagation(),window.location.href=n.attr("href")}))}e.addClass("blog-post-ready")}}))},t.fn.touchTriggerPostClick=function(){return this.each((function(){var e=t(this);if(!e.hasClass("touch-post-ready")){var i=e.find(".post-thumbnail-rollover").first();e.find(".entry-meta a, .fancy-date a, .fancy-categories a"),e.find(".entry-excerpt").height(),e.find(".post-details").height();s.on("touchend",(function(e){t(".mobile-true .post").removeClass("is-clicked")})),e.on("touchstart",(function(t){origY=t.originalEvent.touches[0].pageY,origX=t.originalEvent.touches[0].pageX})),e.on("touchend",(function(n){var o=n.originalEvent.changedTouches[0].pageX,s=n.originalEvent.changedTouches[0].pageY;if(origY==s||origX==o)if(e.parents().hasClass("disable-layout-hover"))"a"===n.target.tagName.toLowerCase()?t(n.target).trigger("click"):window.location.href=i.attr("href");else{if(!e.hasClass("is-clicked"))return n.preventDefault(),"a"===n.target.tagName.toLowerCase()&&t(n.target).trigger("click"),t(".mobile-ture .post").removeClass("is-clicked"),e.addClass("is-clicked"),e.parent().siblings().find(".post").removeClass("is-clicked"),!1;window.location.href=i.attr("href")}})),e.addClass("touch-post-ready")}}))},t.fn.triggerPostClickOnBefore=function(){return this.each((function(){var e=t(this),i=e.parents(".post");if(!e.hasClass("post-before-ready")){var n=i.find(".post-thumbnail-rollover").first(),o=i.find(".entry-meta a, .fancy-date a, .fancy-categories a");if(n.length>0){n.on("click",(function(t){n.parents(".ts-wrap").hasClass("ts-interceptClicks")}));var s=!1;e.on("mouseenter mousemove",(function(t){var n=e.offset().top,o=t.pageY;n-10<=o&&n+125>=o?i.hasClass("on-hover")||i.addClass("on-hover"):i.removeClass("on-hover")})),e.on("mouseleave",(function(t){e.offset().top,t.pageY;i.removeClass("on-hover")})),e.on("click",(function(){if(i.hasClass("on-hover"))return s||(s=!0,window.location.href=n.attr("href"),s=!1),!1})),e.find(o).click((function(t){i.hasClass("on-hover")&&(t.stopPropagation(),window.location.href=o.attr("href"))}))}e.addClass("post-before-ready")}}))},
/*!Trigger click (direct to post) */
t.fn.forwardToPost=function(){return this.each((function(){var i=t(this);i.hasClass("this-ready")||(i.on("click tap",(function(){var i=t(this),n=i.find("a").first(),o=n.attr("href");if(!i.parents(".ts-wrap").hasClass("ts-interceptClicks")){if("a"!==e.target.tagName.toLowerCase()){if("_blank"===n.attr("target"))return window.open(o,"_blank"),!1;window.location.href=o}return!1}})),i.addClass("this-ready"))}))},window.the7AddHovers=function(e){t(".rollover, .rollover-video, .post-rollover, .rollover-project .show-content, .vc-item .vc-inner > a",e).addRollover(),t(".filter-grayscale .slider-masonry",e).on("mouseenter tap",(function(e){"tap"==e.type&&e.stopPropagation(),t(this).addClass("dt-hovered")})),t(".filter-grayscale .slider-masonry",e).on("mouseleave",(function(e){t(this).removeClass("dt-hovered")})),t(".hover-scale .rollover-project, .hover-scale .post",e).scaleInHover()},window.the7AddMobileHovers=function(e){
/*!Description on hover show content on click(albums projects touch device)*/
t(".rollover-project, .woocom-project",e).touchNewHover(),t(".content-rollover-layout-list:not(.portfolio-shortcode):not(.albums-shortcode) .post, .gradient-overlay-layout-list:not(.portfolio-shortcode):not(.albums-shortcode)  .post",e).touchTriggerPostClick()},window.the7AddDesktopHovers=function(e){
/*!-Hover Direction aware init*/
t(".hover-grid.gallery-shortcode figure, .hover-grid .rollover-project, .hover-grid.portfolio-shortcode .post",e).each((function(){t(this).hoverdir()})),t(".hover-grid-reverse.gallery-shortcode figure, .hover-grid-reverse .rollover-project, .hover-grid-reverse.portfolio-shortcode .post",e).each((function(){t(this).hoverdir({inverse:!0})})),t(".albums .rollover-content a:not(.portfolio-categories a), .media .rollover-content, .dt-gallery-container .rollover-content",e).on("click",(function(e){if(t(e.target).is("a"))return!0;t(this).siblings("a.dt-pswp-item").first().click()})),t(".content-rollover-layout-list:not(.portfolio-shortcode):not(.albums-shortcode) .post,  .gradient-overlay-layout-list:not(.portfolio-shortcode):not(.albums-shortcode) .post",e).triggerPostClick(),t(".gradient-overlap-layout-list:not(.portfolio-shortcode):not(.albums-shortcode)  .post-entry-content",e).triggerPostClickOnBefore(),t(".the7-elementor-widget .forward-post",e).forwardToPost()},the7AddMobileHovers(t("html.mobile-true")),the7AddDesktopHovers(t("html.mobile-false")),the7AddHovers(document);var ne=t("#commentform");function oe(){t(".full-width-wrap").length>0&&t(".full-width-wrap").each((function(){var e,i,o=t(this),a=window.innerWidth,r=n.width(),l=t(".content").width();if(t(".boxed").length>0)e=(parseInt(t("#main").width())-parseInt(l))/2;else if(t(".side-header-v-stroke").length&&a>dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")||t(".side-header-v-stroke").length&&s.hasClass("responsive-off")){var d=a<=parseInt(l)?parseInt(l):r-t(".side-header-v-stroke").width();e=Math.ceil((d-parseInt(l))/2)}else if(t(".sticky-header .side-header").length&&a>dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")||t(".sticky-header .side-header").length&&s.hasClass("responsive-off")){d=r<=parseInt(l)?parseInt(l):r;e=Math.ceil((r-parseInt(l))/2)}else if((t(".header-side-left").length&&a||t(".header-side-right").length&&a)>dtLocal.themeSettings.mobileHeader.firstSwitchPoint){d=a<=parseInt(l)?parseInt(l):r-t(".side-header").width();e=Math.ceil((d-parseInt(l))/2)}else{d=r<=parseInt(l)?parseInt(l):r;e=Math.ceil((r-parseInt(l))/2)}t(".sidebar-left").length>0||t(".sidebar-right").length>0?(i=t(".content").width(),e=0):i=t("#main").innerWidth();var c=s.hasClass("rtl")?"margin-right":"margin-left";o.css({width:i,opacity:1}),o.css(c,-e),o.find(".full-width-wrap").css({width:"",opacity:1,"padding-left":e}),o.find(".full-width-wrap").css(c,""),o.find(".ts-wrap").each((function(){var e=t(this).data("thePhotoSlider");void 0!==e&&e.update()}))}))}ne.on("click","a.clear-form",(function(t){return t.preventDefault(),ne.find('input[type="text"], textarea').val(""),!1})),ne.on("click"," a.dt-btn.dt-btn-m",(function(t){return t.preventDefault(),ne.find("#submit").trigger("click"),!1})),t(".full-width-wrap").length>0&&(dtGlobals.isiOS?n.bind("orientationchange",(function(){oe()})).trigger("orientationchange"):(n.on("resize",(function(){oe()})),oe())),n.trigger("dt.removeLoading");var se=t("#main-slideshow");!se.find("> div").length>0&&se.addClass("empty-slider"),
/*!-Revolution slider*/
se.is(":parent")||se.siblings(".masthead").addClass("no-slider"),t(".rev_slider_wrapper").length>0&&(se.find("> .rev_slider_wrapper")&&se.addClass("fix rv-slider"),(t(".rev_slider_wrapper").hasClass("fullscreen-container")||t(".rev_slider_wrapper").hasClass("fullwidthbanner-container"))&&se.removeClass("fix"));
/*!-Search*/
var ae,re,le=t(".masthead, .dt-mobile-header"),de=t(".popup-search",le);if(de.length>0&&(s.on("click",(function(e){t(e.target).is(".field",de)||(t(".searchform .submit",le).removeClass("act"),de.removeClass("act"),t(".popup-search-wrap",de).stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),setTimeout((function(){t(".popup-search-wrap",de).removeClass("right-overflow bottom-overflow left-overflow").css({right:"",left:"","max-width":""})}),400))})),t(".searchform .submit",le).on("click",(function(e){e.preventDefault(),e.stopPropagation();var i=t(this);i.hasClass("act")?(i.removeClass("act"),i.parents(".mini-search").removeClass("act"),i.siblings(".popup-search-wrap").stop().animate({opacity:0},150,(function(){t(this).css("visibility","hidden")})),setTimeout((function(){i.siblings(".popup-search-wrap").removeClass("right-overflow bottom-overflow left-overflow").css({right:"",left:"","max-width":""})}),400)):(i.addClass("act"),i.parents(".mini-search").addClass("act"),i.parents(".dt-mobile-header").length>0&&i.siblings(".popup-search-wrap").css({top:i.parents(".mini-search").position().top-i.siblings(".popup-search-wrap").innerHeight()}),i.parents(".searchform").offset().left-i.siblings(".popup-search-wrap").innerWidth()<0&&i.siblings(".popup-search-wrap").addClass("left-overflow"),a.width()-(i.parents(".searchform").offset().left-a.offset().left)-i.siblings(".popup-search-wrap").innerWidth()<0&&(i.siblings(".popup-search-wrap").addClass("right-overflow"),i.siblings(".popup-search-wrap").removeClass("left-overflow")),a.width()-(i.parents(".searchform").offset().left-a.offset().left)-i.siblings(".popup-search-wrap").innerWidth()<0&&i.parents(".searchform").offset().left-i.siblings(".popup-search-wrap").innerWidth()<0&&i.siblings(".popup-search-wrap").css({"max-width":i.parents(".searchform").offset().left}),n.height()-(i.siblings(".popup-search-wrap").offset().top-dtGlobals.winScrollTop)-i.siblings(".popup-search-wrap").innerHeight()<0&&i.siblings(".popup-search-wrap").addClass("bottom-overflow"),i.siblings(".popup-search-wrap").stop().css("visibility","visible").animate({opacity:1},150),setTimeout((function(){i.siblings(".popup-search-wrap").find("input.searchform-s").focus()}),100))}))),t(".overlay-search").length>0){var ce=t(".overlay-search .searchform").first().clone();t("body").append("<div class='overlay-search-microwidget'><i class='overlay-close icomoon-the7-font-the7-cross-01'></i></div>");var he=t(".overlay-search-microwidget");he.append(ce),t(".overlay-search").hasClass("default-icon")&&he.addClass("default-icon"),t(".mini-search .submit").on("click",(function(e){e.preventDefault(),he.addClass("open"),t("#page").addClass("overlay-open"),setTimeout((function(){he.find("input.searchform-s").focus()}),100)})),t(".overlay-close",he).on("click",(function(){t("#page").removeClass("overlay-open"),t(this).parent(he).removeClass("open")}))}
/*!-Before After*/
/*!-Isotope fix for tabs*/
(dtGlobals.addOnloadEvent((function(){t(".twentytwenty-container").each((function(){var e=t(this),i=e.attr("data-orientation").length>0?e.attr("data-orientation"):"horizontal",n=void 0!==e.attr("data-offset")&&e.attr("data-offset").length>0?e.attr("data-offset"):.5,o=!!e.attr("data-navigation");e.twentytwenty({default_offset_pct:n,orientation:i,navigation_follow:o})}))})),t(".wpb_tabs .iso-container").length>0)&&t(".wpb_tour_tabs_wrapper").each((function(){var e=t(this),i=e.parents(".wpb_tabs").find(".iso-container");e.tabs({activate:function(t,e){i.isotope("layout")}}),e.find("li").each((function(){t(this).on("click",(function(){clearTimeout(undefined),n.trigger("debouncedresize"),t(this).parents(".wpb_tabs").find(".iso-container").isotope("layout")}))}))}));t.fn.calcPics=function(){return!(t(".instagram-photos").length<1)&&this.each((function(){var e=e||parseInt(t(this).attr("data-image-max-width"));parseInt(t(this).find("> a").css("margin-left"));t(this).find(" > a").css({"max-width":e,opacity:1});var i=t(this),n=i.width(),o=100/Math.ceil(n/e);i.find("a").css({width:o+"%"})}))},t(".instagram-photos").calcPics(),t(".st-accordion").each((function(){var e=t(this);e.find("ul > li > a").on("click",(function(i){i.preventDefault();var n=t(this).next();t(".st-content",e).not(n).slideUp("fast"),n.slideToggle("fast")}))})),simple_tooltip(".shortcode-tooltip","shortcode-tooltip-content"),
/*!-search widget*/
t(".widget .searchform .submit, .search-icon, form.searchform:not(.mini-widget-searchform) .submit").on("click",(function(e){return e.preventDefault(),t(this).closest("form").find("input.searchsubmit").click(),!1})),t.fn.animateSkills=function(){t(".skill-value",this).each((function(){var e=t(this),i=e.data("width");e.css({width:i+"%"})}))},dtGlobals.isMobile&&t(".skills").animateSkills(),
/*!-Show share buttons*/
t(".project-share-overlay.allways-visible-icons .share-button").on("click",(function(t){t.preventDefault()})),dtGlobals.addOnloadEvent((function(){t(".album-share-overlay, .project-share-overlay:not(.allways-visible-icons)").each((function(){var e=t(this);e.find(".share-button").on("click",(function(t){t.preventDefault()})),e.on("mouseover tap",(function(e){"tap"==e.type&&e.stopPropagation();var i=t(this);i.addClass("dt-hovered"),clearTimeout(ae),clearTimeout(re),ae=setTimeout((function(){i.hasClass("dt-hovered")&&(i.find(".soc-ico a").css("display","inline-flex"),i.find(".soc-ico").stop().css("visibility","visible").animate({opacity:1},200))}),100)})),e.on("mouseleave ",(function(e){var i=t(this);i.removeClass("dt-hovered"),clearTimeout(ae),clearTimeout(re),re=setTimeout((function(){i.hasClass("dt-hovered")||i.find(".soc-ico").stop().animate({opacity:0},150,(function(){i.find(".soc-ico a").css("display","none"),t(this).css("visibility","hidden")}))}),50)}))}))}));var ue=t(".transparent #fancy-header").exists(),pe=t(".transparent .page-title").exists();t(".transparent .checkout-page-title").exists();t.fancyFeaderCalc=function(){t(".branding .preload-me").loaded(null,(function(){ue&&t(".transparent #fancy-header").css({"padding-top":t(".masthead:not(.side-header)").height()}),pe&&(t(".transparent .page-title").css({"padding-top":t(".masthead:not(.side-header)").height()}),t(".transparent .page-title").css("visibility","visible"))}),!0)};
/*!-Paginator*/
var fe=t('.paginator[role="navigation"]'),me=fe.find("a.dots");me.on("click",(function(){fe.find("div:hidden").show().find("a").unwrap(),me.remove()})),t(".share-buttons a.pinit-marklet").click((function(e){e.preventDefault(),t("#pinmarklet").remove();var i=document.createElement("script");i.setAttribute("type","text/javascript"),i.setAttribute("charset","UTF-8"),i.setAttribute("id","pinmarklet"),i.setAttribute("async","async"),i.setAttribute("defer","defer"),i.setAttribute("src","//assets.pinterest.com/js/pinmarklet.js?r="+99999999*Math.random()),document.body.appendChild(i)})),
/*!-Scroll to Top*/
n.on("debouncedresize",(function(){window.innerWidth>dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")||s.hasClass("responsive-off")?t(".masthead:not(.side-header):not(.mixed-header)").length>0?dtGlobals.showTopBtn=t(".masthead:not(.side-header):not(.mixed-header)").height()+150:t(".masthead.side-header-h-stroke").length>0?dtGlobals.showTopBtn=t(".side-header-h-stroke").height()+150:dtGlobals.showTopBtn=500:dtGlobals.showTopBtn=500})),n.scroll((function(){dtGlobals.winScrollTop>dtGlobals.showTopBtn?t(".scroll-top").removeClass("off").addClass("on"):t(".scroll-top").removeClass("on").addClass("off")})),t(".scroll-top").click((function(t){return t.preventDefault(),$e("up"),!1}));var ge=t(".woocommerce-NoticeGroup-updateOrderReview, .woocommerce-NoticeGroup-checkout");!ge.length&&t("form.checkout").exists()&&(ge=t("form.checkout").parents(".content").offset().top),t(document.body).on("checkout_error",(function(){t("html, body").animate({scrollTop:ge-Fe.height()},"slow")})),
/*!-Custom select*/
t('<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16;" xml:space="preserve"><path class="st0" d="M2.5,12c0-0.3,0.2-0.5,0.5-0.5h10c0.3,0,0.5,0.2,0.5,0.5s-0.2,0.5-0.5,0.5H3C2.7,12.5,2.5,12.3,2.5,12z M2.5,8c0-0.3,0.2-0.5,0.5-0.5h10c0.3,0,0.5,0.2,0.5,0.5c0,0.3-0.2,0.5-0.5,0.5H3C2.7,8.5,2.5,8.3,2.5,8z M2.5,4c0-0.3,0.2-0.5,0.5-0.5h10c0.3,0,0.5,0.2,0.5,0.5S13.3,4.5,13,4.5H3C2.7,4.5,2.5,4.3,2.5,4z"/></svg><select aria-label="Dropdown menu"/>').prependTo("#bottom-bar .mini-nav .menu-select"),t("<option />",{selected:"selected",value:"",text:"———"}).appendTo(".mini-nav .menu-select select"),t("#bottom-bar .mini-nav").each((function(){var e=t(this),i=e.find("select");t("a",e).each((function(){var e=t(this);t("<option />",{value:e.attr("href"),text:e.text(),"data-level":e.attr("data-level")}).appendTo(i)}))})),t(".mini-nav select").change((function(){window.location=t(this).find("option:selected").val()})),t(".mini-nav select option").each((function(){var e=t(this),i=window.location.href;e.attr("value")==i&&e.attr("selected","selected")}))
/*!-Appearance for custom select*/,t(" #bottom-bar .mini-nav select").each((function(){t(this).customSelect()})),t(".menu-select select, .mini-nav .customSelect1, .vc_pie_chart .vc_pie_wrapper").css("visibility","visible"),t(".mini-nav option").each((function(){var e=t(this),i=e.text(),n="";switch(parseInt(e.attr("data-level"))){case 1:n="";break;case 2:n="— ";break;case 3:n="—— ";break;case 4:n="——— ";break;case 5:n="———— "}e.text(n+i)}));
/*!-Material click for menu and buttons*/
var ve=navigator.userAgent.match(/iPhone/i)?"touchstart":"click";if(t(".project-navigation a, .mobile-sticky-header-overlay").bind(ve,(function(t){})),t((function(){t.fn.clickMaterialEffect=function(){return this.each((function(){var e,i,n,o,s=t(this);0===s.find(".ink").length&&s.prepend("<span class='ink'></span>"),s.addClass("ripplelink"),(e=s.find(".ink")).removeClass("animate"),e.height()||e.width()||(i=Math.max(t(this).outerWidth(),s.outerHeight()),e.css({height:i,width:i})),s.bind("mousedown",(function(t){clearTimeout(null),n=t.pageX-s.offset().left-e.width()/2,o=t.pageY-s.offset().top-e.height()/2,e.css({top:o+"px",left:n+"px"}).addClass("animate")})),s.bind("mouseup mouseleave",(function(t){clearTimeout(null),clearTimeout(null),s._timer=setTimeout((function(){e.removeClass("animate")}),400)}))}))},t(".rollover.material-click-effect, .post-rollover.material-click-effect, .rollover-video.material-click-effect").clickMaterialEffect()})),!dtGlobals.isMobile){
/*!-parallax initialisation*/
t(".stripe-parallax-bg, .fancy-parallax-bg, .page-title-parallax-bg").each((function(){var e=t(this),i=e.data("prlx-speed");e.parallax("50%",i),e.addClass("parallax-bg-done"),e.css("opacity","1")}));
/*!-Animate fancy header elements*/
var we=-1;t("#fancy-header .fancy-title:not(.start-animation), #fancy-header .fancy-subtitle:not(.start-animation), #fancy-header .breadcrumbs:not(.start-animation)").each((function(){var e=t(this);e.hasClass("start-animation")||e.hasClass("start-animation-done")||(e.addClass("start-animation-done"),we++,setTimeout((function(){e.addClass("start-animation")}),300*we))}))}if(jQuery(".wpcf7").each((function(){var e=t(this);e.on("wpcf7submit",(function(t){e.find(".wpcf7-response-output").wrapInner("<div class='wpcf7-not-valid-tip-text'></div>").addClass("run-animation"),setTimeout((function(){e.find(".wpcf7-response-output").removeClass("run-animation")}),12e3)})),e.on("wpcf7invalid",(function(t){setTimeout((function(){e.find(".wpcf7-response-output").wrapInner("<div class='wpcf7-not-valid-tip-text'></div>")}),100)})),e.on("wpcf7mailsent",(function(t){setTimeout((function(){e.find(".wpcf7-response-output").wrapInner("<div class='wpcf7-valid-tip-text'></div>").addClass("wpcf7-mail-sent-ok")}),100),setTimeout((function(){e.find(".wpcf7-response-output").removeClass("wpcf7-mail-sent-ok")}),12e3)})),e.on("invalid.wpcf7",(function(t){setTimeout((function(){e.find(".wpcf7-validation-errors").wrapInner("<div class='wpcf7-not-valid-tip-text'></div>")}),100)})),e.on("mailsent.wpcf7",(function(t){setTimeout((function(){e.find(".wpcf7-mail-sent-ok").wrapInner("<div class='wpcf7-valid-tip-text'></div>")}),100)}))})),t(".dt-sticky-sidebar").length>0){if(Ot)var ye=t(".sticky-on");else ye=t(".masthead:not(.side-header):not(.side-header-v-stroke) .header-bar");if(K=!T.exists()||T.is(":hidden")||T.hasClass("top-bar-empty")||T.hasClass("hide-top-bar")?0:T.innerHeight(),Rt||Wt)var be=t(Fe).height()+20;else if(dtLocal.themeSettings.floatingHeader.showMenu&&St)if(s.hasClass("floating-top-bar"))be=dtLocal.themeSettings.floatingHeader.height+K+20;else be=dtLocal.themeSettings.floatingHeader.height+20;else if(E.exists())be=E.find(".header-bar").height()+K+20;else be=0;new StickySidebar("#sidebar",{topSpacing:be,bottomSpacing:20,viewportTop:0,containerSelector:".wf-container-main",innerWrapperSelector:".sidebar-content",minWidth:dtLocal.themeSettings.sidebar.switchPoint})}t("#mphb-booking-details").find(".mphb-booking-details-title, .mphb-check-in-date, .mphb-check-out-date").wrapAll('<div class="mphb-details-sidebar"></div>'),t("#mphb-price-details").appendTo(".mphb-details-sidebar"),!t(".footer .widget").length>0&&t(".footer").addClass("empty-footer"),dtGlobals.addOnloadEvent((function(){t(".the7-elementor-widget:not(.the7-elementor-product-comments)").each((function(){var e=t(this),i=e.find(".stars a");i.length?(i.length>5&&(i.slice(5,i.length).remove(),i=e.find(".stars a")),e.find(".stars span").append(i.get().reverse())):e.find("#rating").hide().before('<p class="stars">                            <span>                                <a class="star-5" href="#">5</a>                                <a class="star-4" href="#">4</a>                                <a class="star-3" href="#">3</a>                                <a class="star-2" href="#">2</a>                                <a class="star-1" href="#">1</a>                            </span>                        </p>'),i.on("click",(function(){var e=t(this),i=t(this).closest("#respond").find("#rating"),n=t(this).closest(".stars");return i.val(e.text()),e.siblings("a").removeClass("active"),e.addClass("active"),n.addClass("selected"),!1}))})),t(".dt-tab-accordion-title").each((function(){var e=t(this);e.parents().hasClass("hide-tab-description")&&!e.parents().hasClass("hide-tab-additional")?t("#tab-title-additional_information.dt-tab-accordion-title").addClass("first"):e.parents().hasClass("hide-tab-additional")&&!e.parents().hasClass("hide-tab-description")||e.parents().hasClass("hide-tab-additional")&&e.parents().hasClass("hide-tab-description")?t("#tab-title-reviews.dt-tab-accordion-title").addClass("first"):t("#tab-title-description.dt-tab-accordion-title").addClass("first"),t(".dt-tab-accordion-title.first").next().css("display","block"),e.on("click",(function(e){var i=t(this),n=i.next();t(".dt-tab-accordion-title").removeClass("active"),i.hasClass("active")?t(".woocommerce-Tabs-panel").slideUp("fast"):(i.addClass("active"),t(".woocommerce-Tabs-panel").not(n).hide(),je(i.parent()),n.slideDown("fast"))}))})),t(".the7-elementor-widget.elementor-widget-tabs").each((function(){var e=t(this),i=e.find(".wc-tabs li:visible").first().attr("aria-controls");e.find(".wc-tabs li").removeClass("active"),e.find(".wc-tabs li:visible").first().addClass("active"),"none"!==e.find(".wc-tabs").css("display")&&(e.find(".panel").css("display","none"),e.find("#"+i).css("display","block")),window.location.hash&&!t(".hide-tab-eviews").length>0&&(e.find(".wc-tabs li").removeClass("active"),e.find(".wc-tabs li.reviews_tab").addClass("active"),e.find(".panel").css("display","none"),e.find(".woocommerce-Tabs-panel--reviews").css("display","block")),e.find(".comment-form-rating .stars").length>1&&e.find(".comment-respond .stars").not(":first").remove(),e.find(".wc-tabs li").on("click",(function(){var i=t(this).attr("aria-controls");t(this).siblings().removeClass("active"),t(this).addClass("active"),e.find("> .woocommerce-tabs > .panel").css("display","none"),e.find("#"+i).css("display","block")}))}))}));var Ce=t(".elementor-popup-modal .dialog-message");i.on("elementor/popup/show",Ce,(function(e,i,n){"undefined"===elementorFrontend||elementorFrontend.isEditMode()||"yes"==n.getDocumentSettings("the7_scrollbar")&&n.$element.closest(".dialog-message").addClass("the7-custom-scroll"),t(document.body).trigger("init_price_filter"),n.$element.find(" .dt-sub-menu-display-on_click li.has-children, .dt-sub-menu-display-on_item_click li.has-children").each((function(){"undefined"!=typeof the7ElementorMenu&&the7Utils.isFunction(the7ElementorMenu)&&t(this).the7ElementorMenu()})),n.$element.find(".dt-css-grid .wf-cell img, .menu-item img, .lazy-load").each((function(){var e=t(this);e.attr("data-src")&&e.prop("src",e.attr("data-src")),e.attr("data-srcset")&&e.prop("srcset",e.attr("data-srcset")),e.removeAttr("data-src"),e.removeAttr("data-srcset"),e.removeClass("lazy-load"),setTimeout((function(){e.parent().removeClass("layzr-bg")}),200)}))})),window.the7GetMasonryColumnsConfig=function(t){var e=t.parent().hasClass("mode-masonry")?t.parent():t,i=t.width()-1,n={mobile:parseInt(e.attr("data-phone-columns-num")),desktop:parseInt(e.attr("data-desktop-columns-num")),tabletV:parseInt(e.attr("data-v-tablet-columns-num")),tabletH:parseInt(e.attr("data-h-tablet-columns-num"))};return Modernizr.mq("only screen and (max-width:767px)")?(singleWidth=Math.floor(i/n.mobile)+"px",doubleWidth=2*Math.floor(i/n.mobile)+"px",columnsNum=n.mobile):Modernizr.mq("(min-width:768px) and (max-width:991px)")?(singleWidth=Math.floor(i/n.tabletV)+"px",doubleWidth=2*Math.floor(i/n.tabletV)+"px",columnsNum=n.tabletV):Modernizr.mq("(min-width:992px) and (max-width:1199px)")?(singleWidth=Math.floor(i/n.tabletH)+"px",doubleWidth=2*Math.floor(i/n.tabletH)+"px",columnsNum=n.tabletH):(singleWidth=Math.floor(i/n.desktop)+"px",doubleWidth=2*Math.floor(i/n.desktop)+"px",columnsNum=n.desktop),{singleWidth:singleWidth,doubleWidth:doubleWidth,columnsNum:columnsNum}},t.fn.simpleCalculateColumns=function(t,e,i){var n=parseInt(t.attr("data-width")),o=parseInt(t.attr("data-columns")),s=parseInt(t.attr("data-padding"));void 0===i&&(i="px"),this.calculateColumns(n,o,s,null,null,null,null,i,e)},t.fn.calculateColumns=function(e,i,n,o,s,a,r,l,d){return this.each((function(){var o,s,a,r,c,h="",u=t(this),p=u,f=u.attr("data-cont-id"),m=t(".cont-id-"+f),g=!1!==n?n:20,v=-g,w=g-10,y=g-5;g<10&&(w=0,y=0),0===g&&(v=0),u.parent().hasClass("products-shortcode")&&(w=g),u.parent().hasClass("mode-masonry")&&(p=p.parent(),m=m.parent());var b=m.not(".bg-under-post, .content-bg-on").hasClass("description-under-image");if(t("#col-style-id-"+f).exists())var C=document.getElementById("col-style-id-"+f);else(C=document.createElement("style")).id="col-style-id-"+f,C.appendChild(document.createTextNode("")),document.head.appendChild(C);var x=t("#col-style-id-"+f);if(h=b?" \t\t\t\t\t\t\t.cont-id-"+f+" { margin: -"+y+"px  -"+g+"px -"+w+"px ; } \t\t\t\t\t\t\t.full-width-wrap .cont-id-"+f+" { margin: "+-y+"px "+g+"px "+-w+"px ; }":" \t\t\t\t\t\t\t.cont-id-"+f+" { margin: -"+g+"px; } \t\t\t\t\t\t\t.full-width-wrap .cont-id-"+f+" { margin: "+v+"px  "+g+"px; } \t\t\t\t\t\t",x.html(h),p.hasClass("resize-by-browser-width"))void 0===d&&(d=the7GetMasonryColumnsConfig),a=(o=d.call(this,u)).singleWidth,r=o.doubleWidth,c=o.columnsNum;else{for(s=u.width()-1,e=e||200,i=i||6;Math.floor(s/i)<e&&!(--i<=1););"px"===l?(a=Math.floor(s/i)+"px",r=2*Math.floor(s/i)+"px",c=i):(a=Math.floor(1e5/i)/1e3+"%",r=2*Math.floor(1e5/i)/1e3+"%")}h+=b?c>1?" \t\t\t\t\t\t\t.cont-id-"+f+"  .wf-cell { width: "+a+"; padding: "+y+"px "+g+"px "+w+"px; } \t\t\t\t\t\t\t.cont-id-"+f+"  .wf-cell.double-width { width: "+r+"; } \t\t\t\t\t\t":" \t\t\t\t\t\t\t.cont-id-"+f+"  .wf-cell { width: "+a+"; padding: "+y+"px "+w+"px "+g+"px; } \t\t\t\t\t\t":c>1?" \t\t\t\t\t\t\t.cont-id-"+f+" .wf-cell { width: "+a+";  padding: "+g+"px; } \t\t\t\t\t\t\t.cont-id-"+f+" .wf-cell.double-width { width: "+r+"; } \t\t\t\t\t\t":" \t\t\t\t\t\t\t.cont-id-"+f+" .wf-cell { width: "+a+"; padding: "+g+"px; } \t\t\t\t\t\t",x.html(h);var _=C.sheet.cssRules.length;C.sheet.insertRule(".webkit-hack { }",_),C.sheet.deleteRule(_),u.trigger("columnsReady")}))},t.fn.initSlider=function(){return this.each((function(){var e=t(this);e.data("width"),e.data("height");e.hasClass("royalReady")||(e.postTypeScroller(),e.addClass("royalReady"))}))};var xe=Isotope.prototype._positionItem;function _e(t,e,i,n){t.find(i).addClass("lazy-load").addClass(n),t.layzrInitialisation()}Isotope.prototype._positionItem=function(t,e,i,n){xe(t,e,i,!0)},t.fn.IsoLayzrInitialisation=function(t){return _e(this,t,"img[class*=iso-]","iso-item-lazy-load")},t.fn.IsoLayzrJqInitialisation=function(t){return _e(this,t,"img","thumb-lazy-load-show")},t.fn.layzrBlogInitialisation=function(t){return _e(this,t,"img","blog-thumb-lazy-load-show")},t(".layzr-loading-on .blog-shortcode.jquery-filter.mode-list .visible").layzrBlogInitialisation();var Se=t(".iso-container"),ke=t(".iso-grid:not(.jg-container, .iso-container), .blog.layout-grid .wf-container.description-under-image:not(.jg-container, .iso-container), .grid-masonry:not(.iso-container), .shortcode-blog-posts.iso-grid"),Te=Se.add(ke),Ie=dtGlobals.isoPreloader=t('<div class="iso-preloader dt-posts-preload dt-posts-preload-active"><div class="dt-posts-preload-activity"></div></div>').appendTo("body").hide();Te.not(".blog-grid-shortcode").addClass("dt-isotope"),window.the7ApplyColumns=function(t,e,i){var o,s;e&&e.length&&(t=t||0,o=e.parent().hasClass("mode-masonry")?e.parent():e,s=parseInt(o.attr("data-padding")),e.addClass("cont-id-"+t).attr("data-cont-id",t),e.simpleCalculateColumns(o,i),s>10&&e.addClass("mobile-paddings"),n.on("debouncedresize",(function(){e.simpleCalculateColumns(o,i),s>10&&e.addClass("mobile-paddings")})))},Te.exists()&&Te.not(".custom-iso-columns").each((function(e){the7ApplyColumns(e,t(this),the7GetMasonryColumnsConfig)})),t.fn.heightHack=function(){return this.each((function(){var e,i=t(this).not(".back-image");i.exists()&&(i.hasClass("height-ready")||i.parents(".testimonial-thumb").exists()||i.parents(".post-rollover").exists()||i.parents(".slider-masonry").exists()||i.parents(".rollover-thumbnails").exists()||(e=parseInt(i.attr("width"))/parseInt(i.attr("height")),i.parents(".testimonial-vcard, .dt-format-gallery, .shortcode-blog-posts.iso-grid ").exists()&&i.wrap("<div />"),isNaN(e)&&i[0]&&(e=i[0].naturalWidth/i[0].naturalHeight),i.parent().not(".img-ratio-wrapper").css({"padding-bottom":100/e+"%",height:0,display:"block"}),i.parents(".dt-team-masonry-shortcode").exists()&&"none"!==i.parent().css("max-width")&&i.parents(".team-media").addClass("apply-max-width"),i.attr("data-ratio",e).addClass("height-ready")))}))},t.fn.IsoInitialisation=function(e,i,n,o){return this.each((function(){var n=t(this);n.hasClass("iso-item-ready")||(n.isotope({itemSelector:e,layoutMode:i,stagger:30,resize:!1,transitionDuration:0,equalheight:o,hiddenStyle:{opacity:0},visibleStyle:{opacity:1},masonry:{columnWidth:1},getSortData:{date:function(e){return t(e).attr("data-date")},name:function(e){return t(e).attr("data-name")}},customSorters:{name:window.the7LocaleSensitiveStringsCompare}}),n.addClass("iso-item-ready"))}))},t(".iso-container, .portfolio-grid").each((function(){if(t(this).parent().hasClass("mode-masonry"))var e=null;else e=t(this);t(".filter:not(.iso-filter):not(.without-isotope):not(.with-ajax) .filter-categories a").on("click.presscorFilterCategories",(function(i){var n=t(this).attr("data-filter");return null!=e&&e.isotope({filter:n}),!1})),t(".filter:not(.iso-filter):not(.without-isotope):not(.with-ajax) .filter-extras .filter-by a").on("click",(function(i){var n=t(this).attr("data-by"),o=t(this).parents(".filter-extras").find(".filter-sorting > a.act").first().attr("data-sort");return null!=e&&e.isotope({sortBy:n,sortAscending:"asc"==o}),!1})),t(".filter:not(.iso-filter):not(.without-isotope):not(.with-ajax) .filter-extras .filter-sorting a").on("click",(function(i){var n=t(this).attr("data-sort"),o=t(this).parents(".filter-extras").find(".filter-by > a.act").first().attr("data-by");return null!=e&&e.isotope({sortBy:o,sortAscending:"asc"==n}),!1}))})),t(".dt-css-grid .wf-cell.visible").IsoLayzrJqInitialisation(),(Se.exists()||ke.exists())&&(Ie.fadeIn(50),Te.not(".blog-grid-shortcode").each((function(){var e=t(this),i=e;e.hasClass("mode-masonry")&&(i=e.find(".dt-isotope")),i.parent().hasClass("the7-elementor-widget")||t(".preload-me",i).heightHack(),t(".slider-masonry",i).initSlider(),i.one("columnsReady",(function(){if(i.hasClass("iso-container"))i.IsoInitialisation(".iso-item","masonry",400);else{var e=!0;(i.parent().hasClass("gradient-overlay-layout-list")||i.parent().hasClass("content-rollover-layout-list"))&&(e=!1),i.IsoInitialisation(".wf-cell","fitRows",400,e)}i.isotope("on","layoutComplete",(function(e){for(var n=0;n<e.length;n++){e[n],t(this);i.trigger("IsoReady")}})),i.parent(".content-rollover-layout-list:not(.disable-layout-hover)").find(".post-entry-wrapper").clickOverlayGradient(),i.one("IsoReady",(function(){var t=i;i.parent().hasClass("jquery-filter")&&"none"!=i.parent().attr("data-pagination-mode")&&(t=i.find(".wf-cell.visible")),t.IsoLayzrJqInitialisation(),setTimeout((function(){i.isotope("layout")}),350)}))})),i.on("columnsReady",(function(){t(".slider-masonry",i).hasClass("royalReady")&&t(".slider-masonry",i).each((function(){var e=t(this).parents(".ts-wrap").data("thePhotoSlider");void 0!==e&&e.update()})),i.parent(".content-rollover-layout-list:not(.disable-layout-hover)").find(".post-entry-wrapper").clickOverlayGradient(),i.isotope("layout")}))})),Ie.stop().fadeOut(300))
/*!-categories filter*/,window.the7ApplyGeneralFilterHandlers=function(e){e.exists()&&e.find("> a").on("click",(function(e){var i=t(this);if(void 0===arguments.callee.dtPreventD){var n=i.parents(".filter").first();arguments.callee.dtPreventD=!0,n.hasClass("without-isotope")&&(arguments.callee.dtPreventD=n.hasClass("with-ajax"))}e.preventDefault(),i.trigger("mouseleave"),i.hasClass("act")&&!i.hasClass("show-all")?(e.stopImmediatePropagation(),i.removeClass("act"),i.siblings("a.show-all").trigger("click")):(i.siblings().removeClass("act"),i.addClass("act"),arguments.callee.dtPreventD||(window.location.href=i.attr("href")))}))},window.the7ApplyGeneralOrderingSwitchHandlers=function(t){if(t.exists()){t.prev(".act").length>0?t.addClass("left-act"):t.next(".act").length>0?t.addClass("right-act"):(t.removeClass("right-act"),t.removeClass("left-act"));var e=t.parents(".filter").first();t.on("click",(function(){e.hasClass("without-isotope")?t.hasClass("right-act")?t.prev("a")[0].click():t.hasClass("left-act")&&t.next("a")[0].click():t.hasClass("right-act")?t.prev("a").trigger("click"):t.hasClass("left-act")&&t.next("a").trigger("click")}))}},window.the7ApplyGeneralOrderingSwitchEffects=function(e){if(e.exists()){var i=t(".filter-switch",e);i.append("<span class='filter-switch-toggle'></span>"),i.each((function(){the7ApplyGeneralOrderingSwitchHandlers(t(this))})),t(".filter-switch .filter-switch-toggle",e).on("animationend webkitAnimationEnd oanimationend MSAnimationEnd",(function(e){t(this).parent().removeClass("pressed")})),t(".filter-extras a",e).on("animationend webkitAnimationEnd oanimationend MSAnimationEnd",(function(e){t(this).removeClass("pressed")})),Modernizr.touch?(i.on("touchstart",(function(e){t(".filter-switch").removeClass("pressed"),t(this).addClass("pressed")})),t(".filter-extras a",e).on("touchstart",(function(e){t(".filter-extras").removeClass("pressed"),t(this).parent(".filter-extras").addClass("pressed")}))):(i.on("mousedown",(function(e){t(".filter-switch").removeClass("pressed"),t(this).addClass("pressed"),setTimeout((function(){t(this).removeClass("pressed")}),600)})),t(".filter-extras a",e).each((function(){t(this).on("mousedown",(function(e){t(".filter-extras").removeClass("pressed"),t(this).addClass("pressed"),setTimeout((function(){t(this).removeClass("pressed")}),600)}))}))),t(".filter-extras a",e).on("click",(function(e){var n=t(this);if(void 0===arguments.callee.dtPreventD){var o=n.parents(".filter").first();arguments.callee.dtPreventD=!0,o.hasClass("without-isotope")&&(arguments.callee.dtPreventD=o.hasClass("with-ajax"))}arguments.callee.dtPreventD&&e.preventDefault(),n.siblings().removeClass("act"),n.addClass("act"),i.each((function(){var e=t(this);e.prev(n).hasClass("act")?(e.addClass("left-act"),e.removeClass("right-act")):e.next(n).hasClass("act")?(e.addClass("right-act"),e.removeClass("left-act")):(e.removeClass("right-act"),e.removeClass("left-act"))}))}))}},t(".filter-categories").each((function(){the7ApplyGeneralFilterHandlers(t(this))})),t(".filter").each((function(){the7ApplyGeneralOrderingSwitchEffects(t(this))})),t(".mode-list .filter-categories > a:not(.show-all), .dt-css-grid-wrap .filter-categories > a:not(.show-all)").each((function(){$this=t(this),$dataFiltr=$this.attr("data-filter"),$newDataFilter=$dataFiltr.substring(1,$dataFiltr.length),$this.attr("data-filter",$newDataFilter),$this.parents().removeClass("iso-filter")})),window.the7ShortcodesFilterConfig=function(t){var e={},i=t;return t.hasClass("dt-css-grid")&&(i=t.parent()),e.filterControls=i.find(".filter-categories"),e.pageControls=i.find(".paginator"),e.sortControls=i.find(".filter-by"),e.orderControls=i.find(".filter-sorting"),e.defaultSort=i.find(".filter-by .act").attr("data-by"),e.defaultOrder=i.find(".filter-sorting .act").attr("data-sort"),e.paginationMode=i.attr("data-pagination-mode"),e.pageLimit=i.attr("data-post-limit"),e.useFilters=!0,e.useSorting=!0,e.controlsSelecter="a",e.controlsSelecterChecked="a.act",e.defaultFilter="*",e.selectAll="*",e.loadMoreButtonLabel=dtLocal.moreButtonText.loadMore,e.customSorters={name:function(t,e){return window.the7LocaleSensitiveStringsCompare(t.name.toLowerCase(),e.name.toLowerCase())}},e},t.fn.shortcodesFilter=function(e){var i=t(this);function o(){if(i.hasClass("dt-css-grid")?$element=i.parent():$element=i,$element.length&&$element.hasClass("lazy-loading-mode")){var t=$element.find(".button-load-more");if(!t.parent().hasClass("hidden")){var e=t.offset();e&&n.scrollTop()+n.height()>e.top-2*n.height()&&t.trigger("click")}}}i.Filterade(e),i.on("updateReady",(function(){ti(),i.parent(".content-rollover-layout-list:not(.disable-layout-hover)").find(".post-entry-wrapper").clickOverlayGradient()})),n.on("scroll",(function(){o()})),o()},t(".blog-shortcode.mode-list.jquery-filter, .jquery-filter .dt-css-grid:not(.custom-pagination-handler)").each((function(){var e=t(this);e.shortcodesFilter(the7ShortcodesFilterConfig(e))})),window.the7ApplyMasonryWidgetCSSGridFiltering=function(t){var e;t&&t.length&&((e=the7ShortcodesFilterConfig(t)).pagerClass="page-numbers filter-item",e.previousButtonLabel="←",e.nextButtonLabel="→",t.shortcodesFilter(e))};var ze=t(".content-rollover-layout-list:not(.disable-layout-hover) .dt-css-grid .post-entry-wrapper");function Le(){var t=.01*window.innerHeight;document.documentElement.style.setProperty("--the7-vh",t+"px")}n.on("debouncedresize",(function(e){if(dtGlobals.resizeCounter++,the7Utils.isFunction(t.fn.calcPics)&&t(".instagram-photos").calcPics(),a.hasClass("boxed")){var i=a.css("maxWidth"),r=i.indexOf("px")>=0,l=i.indexOf("%")>=0;r&&(I.addClass("width-in-pixel"),void 0!==Fe&&Fe.addClass("width-in-pixel")),l&&(t(".masthead.full-width:not(.side-header)").css({width:a.width()}),void 0!==Fe&&Fe.find(".top-bar-bg").length>0&&Fe.find(".top-bar-bg").css({width:a.width()}),s.hasClass("phantom-sticky")&&t(".top-bar-bg").length>0&&t(".top-bar-bg").css({width:a.width()}))}var d;ze.clickOverlayGradient(),window.innerWidth>=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&(a.removeClass("show-mobile-header"),a.addClass("closed-mobile-header"),s.removeClass("show-sticky-mobile-header"),s.removeClass("show-overlay-mobile-header").addClass("closed-overlay-mobile-header"),t(".mobile-sticky-header-overlay").removeClass("active"),t(".dt-mobile-menu-icon").removeClass("active"),o.removeClass("menu-open")),window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")?(t(".masthead").hasClass("masthead-mobile")||t(".masthead:not(.mixed-header):not(#phantom)").addClass("masthead-mobile"),t(".masthead").hasClass("masthead-mobile-header")||(t(".masthead:not(.side-header):not(#phantom)").addClass("masthead-mobile-header"),t("body:not(.overlay-navigation):not(.sticky-header) .side-header:not(#phantom)").addClass("masthead-mobile-header")),E.exists()&&(E.removeClass("sticky-top-line-on sticky-top-line-on"),Ht=!1),t(".mobile-header-scrollbar-wrap").css({"max-width":t(".dt-mobile-header ").width()-13})):(t(".masthead:not(.mixed-header):not(#phantom)").removeClass("masthead-mobile"),t(".masthead:not(.side-header):not(#phantom)").removeClass("masthead-mobile-header"),t("body:not(.overlay-navigation):not(.sticky-header) .side-header:not(#phantom)").removeClass("masthead-mobile-header"),t(".masthead").hasClass("desktop-side-header")||t("body:not(.overlay-navigation):not(.sticky-header) .side-header:not(#phantom)").addClass("desktop-side-header")),window.innerWidth<=dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&window.innerWidth>dtLocal.themeSettings.mobileHeader.secondSwitchPoint&&!s.hasClass("responsive-off")?t(".left-widgets",T).find(".in-top-bar-left").length>0||t(".top-bar .right-widgets").find(".in-top-bar-right").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"):window.innerWidth<=dtLocal.themeSettings.mobileHeader.secondSwitchPoint&&!s.hasClass("responsive-off")?t(".left-widgets",T).find(".in-top-bar").length>0?T.removeClass("top-bar-empty"):T.addClass("top-bar-empty"):!t(".mini-widgets",T).find(".show-on-desktop").length>0?T.addClass("top-bar-empty"):T.removeClass("top-bar-empty"),t(".mini-nav select").trigger("render"),t.fancyFeaderCalc(),t(".dt-default").each((function(){var e=t(this),i=e.attr("data-min-height");t.isNumeric(i)?e.css({minHeight:i+"px"}):i?i.search("%")>0?e.css({minHeight:n.height()*(parseInt(i)/100)+"px"}):e.css({minHeight:i}):e.css({minHeight:0})})),(d=t(".floating-content")).exists()&&t(".preload-me").loaded(null,(function(){var e=d.siblings(".project-wide-col").height(),i=d.height(),o=d.offset(),s=0;T.length>0&&t(".phantom-sticky").length>0&&(s=T.height());var a=t(".project-post").offset();n.on("scroll",(function(){var t="0";window.innerWidth>1050&&dtGlobals.winScrollTop+Fe.height()>o.top&&(t=dtGlobals.winScrollTop+Fe.height()+i+40<a.top+e?dtGlobals.winScrollTop-o.top+Fe.height()+5-s+"px)":e-i-40-s+"px)"),d.css({transform:"translateY("+t+")"})}))}),!0);var c=t(".boxed");c.length>0&&t(".header-side-left.footer-overlap:not(.sticky-header) .boxed .footer, .left-side-line.footer-overlap .boxed .footer").css({right:n.width()-(c.offset().left+c.outerWidth())}),t(".footer-overlap .footer").css({opacity:1}),t(".mobile-false .footer-overlap .page-inner").css({"min-height":window.innerHeight-t(".footer").innerHeight(),"margin-bottom":t(".footer").innerHeight()}),t(".mobile-false .footer-overlap .footer").css({bottom:parseInt(s.css("padding-bottom"))+parseInt(s.css("margin-bottom"))})})).trigger("debouncedresize"),t(window).trigger("the7_widget_resize"),Le(),n.on("the7-resize-height-debounce",Le),window.addEventListener("orientationchange",Le);var Ee=t("html, body"),Oe=t(".phantom-sticky"),Pe=t(".sticky-top-line"),De=(St=Oe.exists(),Pe.exists()),Ae=t(".sticky-mobile-header").exists(),Me=St||De,Fe=null;ye=null;function He(){window.innerWidth<dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")?Ae&&(Fe=t(".masthead-mobile-header")):Fe=Me?t(".masthead"):Ae&&!t(".mobile-false").length>0?t(".masthead-mobile-header"):t("#phantom");var e=t(".mobile-header-bar");e.length>0&&"none"!==e.css("display")?(ye=e,De&&(ye=t(".sticky-top-line.masthead-mobile-header .mobile-header-bar")),(St||De)&&(Fe=t(".sticky-header .masthead.side-header").length>0||t(".overlay-navigation .masthead.side-header").length>0?e.parent(".masthead:not(.side-header)"):e.parent())):(ye=t(".masthead:not(.side-header):not(.side-header-v-stroke)"),s.hasClass("floating-top-bar")||(ye=ye.find(".header-bar")))}He();var Be=function(t,e,i,n){void 0===n&&(n="easeInOutQuad"),Ee.stop().animate({scrollTop:t},{duration:e,easing:n,complete:i})},Re=function(){var e=function(){var t=NaN;try{t=parseInt(s.css("--the7-anchor-scroll-offset"))}catch(t){}return t}();if(!isNaN(e))return e;var i=null,n=0,o=s.hasClass("overlay-navigation");window.innerWidth<dtLocal.themeSettings.mobileHeader.firstSwitchPoint&&!s.hasClass("responsive-off")&&Ae&&(xt?i=t(".masthead-mobile-header .mobile-header-bar"):Ht?i=Pe:(i=t(ye,Fe),Me&&"solid"===Fe.css("border-bottom-style")&&(n=parseInt(Fe.css("border-bottom-width"))))),o?null==i&&De&&(i=Pe):null==i&&(i=St&&Ot?s.hasClass("floating-top-bar")?t(".masthead"):t(".header-bar"):De?t(".masthead").hasClass("mixed-floating-top-bar")?Pe:Pe.find(".header-bar"):Fe);var a=0;return i&&i.length&&(a=i.height()),a+n},We=function(t,e=0){return t.offset().top+1+e-Re()},je=function(e,i,o,a=!1,r=0){void 0!==i&&""!==i&&(location.hash=i),He(),t(".elementor-owl-carousel-call").each((function(){t(this).the7ElementorOwlCarousel()}));var l,d=0,c=s.hasClass("overlay-navigation"),h="easeInOutQuad";a||(d=We(e,r)),l=void 0===o?n.scrollTop():o.offset().top;var u,p=d-l;Math.abs(p)>window.innerHeight&&(u=p>0?d-window.innerHeight:d+window.innerHeight,n.scrollTop(u),h="easeOutQuart");t.closeMobileHeader(),c&&t.closeSideHeader(),Be(d,600,(function(){if(c||setTimeout((function(){t.closeSideHeader()}),50),!a){var i=We(e,r);d.toFixed()!==i.toFixed()&&(!function(t,e,i,n){t.one("animationend",(function(){var o=i(t,n);o!==e&&Be(o)}))}(e,i,We,r),Be(i))}}),h)};window.the7ScrollToTargetEl=je;var $e=function(e,i,n){if(!e)return!1;var o=t("#"+e),s=!1;if(!o.length){if("up"!==e)return!1;s=!0}return je(o,i,n,s),!0},Ne=function(t){return t.match("^#!")?t.substring(3):t.match("^#")?t.substring(1):t.substring(t.indexOf("#")).substring(3)};function Ge(e){e.on("click",(function(e){if(!t(e.target).parent().hasClass("next-level-button")&&clickAnchorLink(t(this)))return e.preventDefault(),!1}))}dtGlobals.addOnloadEvent((function(){var e=window.location.hash,i=Ne(e);setTimeout((function(){if(i){t(".menu-item a").parent("li").removeClass("act"),t(".dt-nav-menu a").removeClass("active-item");var n=t(".menu-item a[href='"+e+"']"),o=t(".dt-nav-menu a[href='"+e+"']");n.parent("li").addClass("act"),o.addClass("active-item"),t(".menu-item a[href*='"+e+"']").parent("li").addClass("act"),t(".dt-nav-menu a[href*='"+e+"']").addClass("active-item");var s=n.parents("li");if(s.find(".sub-nav li.act, .sub-menu li.act, .vertical-sub-nav li.act").length){var a=s.find(" > .sub-nav, .sub-menu, .vertical-sub-nav");s.addClass("open-sub"),a.stop(!0,!0).slideDown(100)}setTimeout((function(){$e(i)}),300)}else{var r=t(".menu-item > a[href='#!/up']");r.length>0&&r.parent("li").addClass("act")}}),300)})),n.on("the7-resize-width",(function(){He()})),
/*!-scroll to anchor*/
window.clickAnchorLink=function(e){const i=e.attr("href"),n=Ne(i),o=$e(n,i,e);if(o&&t(window).trigger("the7.anchorScrolling",[n,i,e]),e.parents().hasClass("elementor-popup-modal")){const t=e.parents(".elementor-location-popup").attr("data-elementor-id");elementorFrontend.documentsManager.documents[t].getModal().hide()}return t(".menu-item a").parent("li").removeClass("act"),t(".dt-nav-menu a").removeClass("active-item"),e.parents("li.menu-item ").addClass("act"),e.parents().hasClass("dt-nav-menu")&&e.addClass("active-item"),o},s.on("click",'.anchor-link[href^="#!"], .anchor-link a[href^="#!"], .logo-box a[href^="#!"], .branding a[href^="#!"], #branding-bottom a[href^="#!"], .mobile-branding a[href^="#!"],  .woocommerce-product-rating a.woocommerce-review-link[href^="#"]',(function(e){if(clickAnchorLink(t(this)))return e.preventDefault(),!1})),Ge(t('.menu-item > a[href*="#!"]')),i.on("elementor/popup/show",t(".elementor-popup-modal .dialog-message"),(function(t,e,i){Ge(i.$element.find('.menu-item > a[href*="#!"]'))}));Ce=t(".elementor-popup-modal .dialog-message");i.on("elementor/popup/show",Ce,(function(e,i,n){t("#elementor-popup-modal-"+i+" .elementor-location-popup");n.$element.find('.menu-item > a[href*="#!"]').on("click",(function(e){if(!t(e.target).parent().hasClass("next-level-button")&&clickAnchorLink(t(this)))return e.preventDefault(),!1}))})),i.on("elementor/popup/show",(function(e,i,n){var o=window.location.hash;if(Ne(o)){t(".menu-item a").parent("li").removeClass("act"),t(".dt-nav-menu a").removeClass("active-item");var s=t(".menu-item a[href='"+o+"']"),a=t(".dt-nav-menu a[href='"+o+"']");s.parent("li").addClass("act"),a.addClass("active-item"),t(".menu-item a[href*='"+o+"']").parent("li").addClass("act"),t(".dt-nav-menu a[href*='"+o+"']").addClass("active-item");var r=s.parents("li");if(r.find(".sub-nav li.act, .sub-menu li.act, .vertical-sub-nav li.act").length){var l=r.find(" > .sub-nav, .sub-menu, .vertical-sub-nav");r.addClass("open-sub"),l.stop(!0,!0).slideDown(100)}elementorFrontend.elements.$window.one("the7.anchorScrolling.elementorPopup",(function(){n.getModal()&&n.getModal().hide()}))}else{var d=t(".menu-item > a[href='#!/up']");d.length>0&&d.parent("li").addClass("act")}})),i.on("elementor/popup/hide",(function(){elementorFrontend.elements.$window.off("the7.anchorScrolling.elementorPopup")}));var Ye=t(".one-page-row div[data-anchor^='#']"),qe=t(".one-page-row .vc_row[id], .one-page-row .vc_section[id], .elementor-element[id], .elementor-menu-anchor[id]"),Ue=t.merge(Ye,qe),Ve=null;function Xe(e){if(t('.menu-item a[href^="#!"]').parents("li").removeClass("act"),t('.dt-nav-menu a[href^="#!"]').removeClass("active-item"),e){var i=t('.menu-item a[href="'+e+'"]'),n=t('.dt-nav-menu a[href="'+e+'"]');i.parents("li").addClass("act"),n.addClass("active-item")}}Ye.length>0?Ve=Ye.first():qe.length>0&&(Ve=qe.first()),Ye=null,qe=null;var Ze=t('.menu-item > a[href="#!/up"]'),Qe=s.hasClass("is-scroll"),Ke=null;function Je(t,e){var i;return t.is(":visible")&&dtGlobals.winScrollTop>=Math.floor(t.offset().top-e)&&(i="#!/"+t.attr("id")),i}
/*!-set active menu item on scroll*/function ti(){var e=t(".dt-isotope"),i=t(".iso-grid .wf-cell:not(.shown), .dt-css-grid .wf-cell:not(.shown)");if(i.exists()&&window.the7ProcessEffects(i),e.exists()){e.each((function(){0;var e=t(this).find(".wf-cell");e.exists()&&window.the7ProcessEffects(e)}))}else{var n=t(".iso-item:not(.shown)");if(!n.exists())return;window.the7ProcessEffects(n)}}n.scroll((function(e){if(!Qe){var i,n=Re();Ue.each((function(){var e=t(this);e.hasClass("wpb_animate_when_almost_visible")&&e.one("webkitAnimationEnd oanimationend msAnimationEnd animationend",(function(t){Xe(Je(e,n))}));var o=Je(e,n);o&&(i=o)})),Ze.length>0&&null!==Ve&&dtGlobals.winScrollTop<Ve.offset().top-n&&(i="#!/up"),i!==Ke&&(Ke=i,Xe(i))}})),t.fn.resetEffects=function(e){return this.each((function(){t(this).find(".iso-item.shown, .wf-cell.shown").removeClass("start-animation").removeClass("animation-triggered").removeClass("shown")}))};var ei={xhr:!1,settings:!1,launch:function(e){var i=this;e&&(this.settings=e),this.xhr&&this.xhr.abort();var o="presscore_template_ajax";"action"in this.settings&&(o=this.settings.action),this.xhr=t.post(e.ajaxurl,{action:o,postID:e.postID,paged:e.paged,targetPage:e.targetPage,term:e.term,orderby:e.orderBy,order:e.order,nonce:e.nonce,visibleItems:e.visibleItems,contentType:e.contentType,pageData:e.pageData,sender:e.sender},(function(o){if(o.success){var s=jQuery(o.html),a=e.targetContainer;parseInt(a.attr("data-width")),parseInt(a.attr("data-max-width")),parseInt(a.attr("data-padding"));if(isIsotope="grid"==e.layout||"masonry"==e.layout,itemsToDeleteLength=0,trashItems=new Array,sortBy=o.orderby.replace("title","name"),sortAscending="asc"==o.order.toString(),o.newNonce&&(dtLocal.ajaxNonce=o.newNonce),void 0!==o.itemsToDelete&&(itemsToDeleteLength=o.itemsToDelete.length),isIsotope&&itemsToDeleteLength>0){for(let t=0;t<o.itemsToDelete.length;t++)trashItems.push('.wf-cell[data-post-id="'+o.itemsToDelete[t]+'"]');a.isotope("remove",a.find(trashItems.join(",")))}else isIsotope||"filter"!=e.sender&&"paginator"!=e.sender||a.find(".wf-cell, article").remove();s.length>0?(a.append(s),dtGlobals.ajaxContainerItems=a.find("div.wf-cell, .project-even, .project-odd").not(".animation-triggered"),isIsotope?(t(".preload-me",a).heightHack(),t(".slider-masonry",a).initSlider(),t(".slider-masonry",a).css("visibility","visible"),a.isotope("addItems",s),"media"!=e.contentType?a.isotope({sortBy:sortBy,sortAscending:sortAscending}):a.isotope({sortBy:"original-order"}),a.isotope("layout"),i.init(),a.IsoLayzrInitialisation(),a.layzrInitialisation()):(t(".slider-masonry",a).initSlider(),t("ul.photoSlider:not(.slider-masonry)").each((function(){t(this).postTypeScroller()})),t("ul.photoSlider").css("visibility","visible"),"jgrid"==e.layout&&a.collagePlus(dtGlobals.jGrid),i.init(),a.IsoLayzrInitialisation(".mobile-true"),a.layzrInitialisation()),void 0!==e.afterSuccessInit&&e.afterSuccessInit(o),n.trigger("dt.ajax.content.appended")):isIsotope&&a.isotope({sortBy:sortBy,sortAscending:sortAscending})}void 0!==e.afterResponce&&e.afterResponce(o),ti()}))},init:function(){switch(this.settings.contentType){case"portfolio":this.initPortfolio();break;case"albums":this.initAlbums();break;case"media":this.initMedia();break;case"blog":case"testimonials":this.basicInit()}},initPortfolio:function(){this.basicInit()},initAlbums:function(){this.basicInit()},initMedia:function(){this.basicInit()},basicInit:function(){var e=this.settings.targetContainer;t(".dt-pswp-item, .dt-gallery-container a",e).addPhotoswipeWrap(),t(".dt-gallery-container",e).initPhotoswipe(),t(".photoswipe-wrapper",e).initPhotoswipe(),e.photoswipeGallery(".dt-gallery-container.wf-container"),t(".rollover, .rollover-video, .post-rollover, .rollover-project .show-content",e).addRollover(),the7Utils.isFunction(t.fn.hoverdir)&&(t(".mobile-false .hover-grid .rollover-project").each((function(){t(this).hoverdir()})),t(".mobile-false .hover-grid-reverse .rollover-project ").each((function(){t(this).hoverdir({inverse:!0})}))),t(".mobile-true .rollover-project a.link.show-content, .hover-style-one article:not(.description-off) .rollover-project > a, .hover-style-two article:not(.description-off) .rollover-project > a, .hover-style-three article:not(.description-off) .rollover-project > a").on("click",(function(t){t.preventDefault()})),t(".dt-trigger-first-pswp",e).not(".pspw-ready").on("click",(function(){var e=t(this).parents("article.post");if(e.length>0){var i=e.find("a.dt-pswp-item");i.length>0&&i.first().trigger("click")}return!1})).addClass("mfp-ready"),t(".mobile-true .rollover-project").touchNewHover(),the7Utils.isFunction(t.fn.triggerHoverClick)&&t(".mobile-false .rollover-project:not(.rollover-active) .rollover-content, .buttons-on-img:not(.rollover-active) .rollover-content").triggerHoverClick(),the7Utils.isFunction(t.fn.triggerHoverClick)&&t(".mobile-false .rollover-project.forward-post").triggerHoverClick(),the7Utils.isFunction(t.fn.triggerHoverClick)&&t(".mobile-false .rollover-project.rollover-active, .mobile-false .buttons-on-img.rollover-active").followCurentLink(),the7Utils.isFunction(t.fn.triggerAlbumsClick)&&t(".mobile-false .albums .rollover-project, .mobile-false .albums .buttons-on-img, .mobile-false .archive .type-dt_gallery .buttons-on-img").triggerAlbumsClick(),the7Utils.isFunction(t.fn.triggerAlbumsTouch)&&t(".mobile-true .albums .rollover-project, .mobile-true .albums .buttons-on-img, .mobile-true .archive .type-dt_gallery .buttons-on-img").triggerAlbumsTouch(),the7Utils.isFunction(t.fn.triggerPostClick)&&t(".mobile-false .content-rollover-layout-list:not(.portfolio-shortcode):not(.albums-shortcode) .post, .mobile-false .gradient-overlay-layout-list:not(.portfolio-shortcode):not(.albums-shortcode) .post").triggerPostClick(),the7Utils.isFunction(t.fn.touchforwardToPost)&&t(".mobile-true .rollover-project.forward-post").touchforwardToPost(),the7Utils.isFunction(t.fn.touchHoverImage)&&t(".mobile-true .buttons-on-img").touchHoverImage(),t(".hover-scale .rollover-project").scaleInHover(),the7Utils.isFunction(t.fn.addIconToLinks)&&t(".links-container a").addIconToLinks()}};function ii(e){var i=e.find(".filter.with-ajax").first(),n=e.find(".wf-container.with-ajax, .articles-list.with-ajax").first(),o=i.find(".filter-categories a.act"),s=i.find(".filter-by a.act"),a=i.find(".filter-sorting a.act"),r=parseInt(n.attr("data-cur-page")),l=new Array,d=o.length>0?o.attr("data-filter").replace(".category-","").replace("*",""):"";return"0"==d&&(d="none"),n.hasClass("dt-isotope")&&t(".wf-cell",n).each((function(){l.push(t(this).attr("data-post-id"))})),{visibleItems:l,postID:dtLocal.postID,paged:r,term:d,orderBy:s.length>0?s.attr("data-by"):"",order:a.length>0?a.attr("data-sort"):"",ajaxurl:dtLocal.ajaxurl,nonce:dtLocal.ajaxNonce,pageData:dtLocal.pageData,layout:dtLocal.pageData.layout,targetContainer:n,isPhone:dtGlobals.isPhone}}function ni(){if(dtGlobals.loadMoreButton&&dtGlobals.loadMoreButton.exists()){var t=dtGlobals.loadMoreButton.offset();t&&n.scrollTop()>(t.top-n.height())/2&&!dtGlobals.loadMoreButton.hasClass("animate-load")&&dtGlobals.loadMoreButton.trigger("click")}}t("#content").on("click",".paginator.with-ajax a",(function(e){if(e.preventDefault(),!t(e.target).hasClass("dots")&&!t(e.target).hasClass("disabled")){var i=t(this),o=i.closest(".paginator"),s=o.parent(),a=s.find(".wf-container.with-ajax, .articles-list.with-ajax").first(),r=t(".button-load-more"),l=r.find(".button-caption").text(),d=o.hasClass("paginator-more-button")?"more":"paginator",c="more"==d,h=ii(s),u=c?h.paged+1:i.attr("data-page-num"),p=dtGlobals.isoPreloader;if(r.addClass("animate-load").find(".button-caption").text(dtLocal.moreButtonText.loading),p&&!t(".paginator-more-button").length&&dtGlobals.isoPreloader.fadeIn(50),c)t("html, body").scrollTop(n.scrollTop()+1);else{var f=s.find(".filter.with-ajax").first(),m=44;f.exists()||(f=a,m=50),t("html, body").animate({scrollTop:f.offset().top-t("#phantom").height()-m},400)}ei.launch(t.extend({},h,{contentType:h.pageData.template,targetPage:u,sender:d,visibleItems:c?new Array:h.visibleItems,afterResponce:function(e){o.length>0?(e.paginationHtml?o.html(t(e.paginationHtml).html()).show():o.html("").hide(),setTimeout((function(){t(".button-load-more").removeClass("animate-load").find(".button-caption").text(l)}),200)):e.paginationHtml&&a.parent().append(t(e.paginationHtml)),o.find(".dots").on("click",(function(){o.find("div:hidden").show().find("a").unwrap(),t(this).remove()})),a.attr("data-cur-page",e.currentPage),dtGlobals.isoPreloader.stop().fadeOut(300),dtGlobals.loadMoreButton=t(".button-load-more")}}))}})),t(".filter.with-ajax .filter-categories a, .filter.with-ajax .filter-extras a").on("click",(function(e){e.preventDefault();var i=t(this).closest(".filter").parent(),n=i.find(".wf-container.with-ajax").first(),o=i.find(".paginator").first(),s=ii(i),a=dtGlobals.isoPreloader;n.resetEffects(),a&&dtGlobals.isoPreloader.fadeIn(50),ei.launch(t.extend({},s,{contentType:s.pageData.template,targetPage:1,paged:1,sender:"filter",afterResponce:function(e){o.length>0?e.paginationHtml?o.html(t(e.paginationHtml).html()).show():o.html("").hide():e.paginationHtml&&n.parent().append(t(e.paginationHtml)),o.find(".dots").on("click",(function(){o.find("div:hidden").show().find("a").unwrap(),t(this).remove()})),n.attr("data-cur-page",e.currentPage),dtGlobals.isoPreloader.stop().fadeOut(300),dtGlobals.loadMoreButton=t(".button-load-more")}}))})),void 0!==dtLocal.themeSettings.lazyLoading&&dtLocal.themeSettings.lazyLoading&&(dtGlobals.loadMoreButton=t(".button-load-more"),n.on("scroll",(function(){ni()})),ni());var oi,si=(oi={},function(t,e,i){i||(i="Don't call this twice without a uniqueId"),oi[i]&&clearTimeout(oi[i]),oi[i]=setTimeout(t,e)});n.on("the7-resize-width",(function(){var e=t(".iso-item, .iso-grid .wf-cell");e.addClass("animate-position"),si((function(){e.removeClass("animate-position")}),2500,"")}));var ai=t(".dt-isotope"),ri=t(".iso-item:not(.shown):not(.is-visible)"),li=t(".iso-grid .wf-cell:not(.shown):not(.is-visible), .dt-css-grid .wf-cell:not(.shown):not(.is-visible)");(ai.exists()||ri.exists()||li.exists())&&(setTimeout((function(){ti()}),100),n.on("scroll",(function(){ti()})));var di=function(){function e(e){this.config=t.extend({paginatorContainer:null,postLimit:1,curPage:1,items:[],filter:null,onPaginate:function(){}},e)}return e.prototype.setCurPage=function(t){this.config.curPage=parseInt(t)},e.prototype.getCurPage=function(){return this.config.curPage},e.prototype.reset=function(t){this.config.items=t,this.setCurPage(1),this.appendControls(),this._filterByCurPage()},e.prototype.appendControls=function(){},e.prototype._filterByCurPage=function(){this.showItem(this.config.items)},e.prototype.hideItem=function(t){t.removeClass("visible").addClass("hidden").hide()},e.prototype.showItem=function(t){t.addClass("visible").removeClass("hidden").show()},e.prototype.applyLoadingEffects=function(){this.lazyLoadImages(),ti()},e.prototype.lazyLoadImages=function(){this.config.items&&this.config.items.filter(".visible").IsoLayzrJqInitialisation()},e.prototype.layoutItems=function(){this._filterByCurPage(),this.config.filter&&"function"==typeof this.config.filter.layoutItems&&this.config.filter.layoutItems()},e}(),ci=function(){function e(e){di.call(this,e);this.config=t.extend({previousButtonClass:"",previousButtonLabel:"",pagerClass:"",nextButtonClass:"",nextButtonLabel:"",activeClass:"act",pagesToShow:5},this.config),this.appendControls(),t("a.act",this.config.paginatorContainer).trigger("click.dtPostsPaginationFilter",{onSetup:!0})}return e.prototype=new di,e.prototype.addEvents=function(){var e=this;t("a",this.config.paginatorContainer).not(".dots").on("click.dtPostsPaginationFilter",{self:this},(function(t,i){e.config.onPaginate.call(this,t,i)})),t("a.dots",this.config.paginatorContainer).on("click.dtPostsPaginationDots",{self:this},(function(t){t.preventDefault(),t.data.self.config.paginatorContainer.find("div:hidden a").unwrap(),t.data.self.config.paginatorContainer.find("a.dots").remove()}))},e.prototype.appendControls=function(){var e=this.config.paginatorContainer,i=Math.ceil(this.config.items.length/this.config.postLimit),n=this.config.curPage;if(e.empty(),i<=1)e.addClass("hidden");else{var o,s;e.removeClass("hidden"),1!==n&&e.prepend('<a href="#" class="'+this.config.previousButtonClass+'" data-page-num="'+(n-1)+'">'+this.config.previousButtonLabel+"</a>");var a=(5|this.config.pagesToShow)-1,r=Math.floor(a/2),l=Math.ceil(a/2),d=Math.max(n-r,1),c=n+l;d<=r&&(c=d+a),c>i&&(d=Math.max(i-a,1),c=i);var h='<a href="javascript:void(0);" class="dots">…</a>',u=t('<div style="display: none;"></div>'),p=t('<div style="display: none;"></div>');for(o=s=1;1<=i?s<=i:s>=i;o=1<=i?++s:--s)o<d&&1!=o?u.append('<a href="#" class="'+this.config.pagerClass+'" data-page-num="'+ +o+'">'+o+"</a>"):(o==d&&u.children().length&&e.append(u).append(t(h)),o>c&&o!=i?p.append('<a href="#" class="'+this.config.pagerClass+'" data-page-num="'+ +o+'">'+o+"</a>"):(o==i&&p.children().length&&e.append(p).append(t(h)),e.append('<a href="#" class="'+this.config.pagerClass+'" data-page-num="'+ +o+'">'+o+"</a>")));n<i&&e.append('<a href="#" class="'+this.config.nextButtonClass+'" data-page-num="'+(n+1)+'">'+this.config.nextButtonLabel+"</a>"),e.find('a[data-page-num="'+n+'"]').addClass(this.config.activeClass),this.addEvents()}},e.prototype._filterByCurPage=function(){var e=this;this.config.items.get().map((function(i,n){e._showOnCurPage(n+1)?e.showItem(t(i)):e.hideItem(t(i))}))},e.prototype._showOnCurPage=function(t){return this.config.postLimit<=0||this.config.postLimit*(this.getCurPage()-1)<t&&t<=this.config.postLimit*this.getCurPage()},e.prototype._setAsActive=function(t){t.addClass("act").siblings().removeClass("act")},e}(),hi=function(){function e(t){di.call(this,t),this.appendControls(),this.addEvents(),this.layoutItems(),this.applyLoadingEffects()}return e.prototype=new di,e.prototype.addEvents=function(){t("a",this.config.paginatorContainer).on("click.dtPostsPaginationFilter",{self:this},this.config.onPaginate)},e.prototype.appendControls=function(){var t=this.config.paginatorContainer,e=Math.ceil(this.config.items.length/this.config.postLimit);this.config.curPage<e?t.removeClass("hidden"):t.addClass("hidden")},e.prototype._filterByCurPage=function(){var e=this,i=e.getCurPage()*e.config.postLimit;this.config.items.get().map((function(n,o){o<i?e.showItem(t(n)):e.hideItem(t(n))}))},e}(),ui=function(){function e(e){this.config=t.extend({onCategoryFilter:function(){},onOrderFilter:function(){},onOrderByFilter:function(){},categoryContainer:null,orderContainer:null,orderByContainer:null,postsContainer:null,order:"desc",orderBy:"date",curCategory:"*"},e),this.addEvents(),"*"!==this.config.curCategory&&this._filterPosts()}return e.prototype.addEvents=function(){var e=this;this.config.categoryContainer&&t("a",this.config.categoryContainer).on("click.dtPostsCategoryFilter",{self:this},(function(t){e.config.onCategoryFilter.call(this,t)})),this.config.orderContainer&&t("a",this.config.orderContainer).on("click.dtPostsOrderFilter",{self:this},(function(t){e.config.onOrderFilter.call(this,t)})),this.config.orderByContainer&&t("a",this.config.orderByContainer).on("click.dtPostsOrderByFilter",{self:this},(function(t){e.config.onOrderByFilter.call(this,t)}))},e.prototype.setOrder=function(t){this.config.order=t},e.prototype.setOrderBy=function(t){this.config.orderBy=t},e.prototype.setCurCategory=function(t){this.config.curCategory=t},e.prototype.getFilteredItems=function(){return t(this.config.postsContainer.isotope("getFilteredItemElements"))},e.prototype.getItems=function(){return t(this.config.postsContainer.isotope("getItemElements"))},e.prototype.layoutItems=function(){this.layout(),this.config.postsContainer.trigger("updateReady")},e.prototype.layout=function(){this.config.postsContainer.isotope("layout")},e.prototype.scrollToTopOfContainer=function(e,i){var n=this.config.postsContainer.parent(),o=t(".phantom-sticky").exists(),s=t(".sticky-top-line").exists(),a=n.attr("data-scroll-offset")?parseInt(n.attr("data-scroll-offset")):0;if(o||s)var r=t(".masthead:not(.side-header)").height();else if(t(".phantom-fade").exists()||t(".phantom-slide").exists())r=t("#phantom").height();else r=0;!n.hasClass("enable-pagination-scroll")&&n.hasClass("the7-elementor-widget")||t("html, body").animate({scrollTop:n.offset().top-r-50+a},400,e?e.bind(i|this):void 0)},e.prototype._filterPosts=function(){this.config.postsContainer&&this.config.postsContainer.isotope({filter:this.config.curCategory,sortAscending:"asc"==this.config.order,sortBy:this.config.orderBy})},e.prototype._setAsActive=function(t){t.addClass("act").siblings().removeClass("act")},e}(),pi=function(){function e(e){ui.call(this,e);this.config=t.extend({showOnCurPage:function(){}},this.config),this.items=this.config.postsContainer.find(".wf-cell"),this.filteredItems=this.items}return e.prototype=new ui,e.prototype.getFilteredItems=function(){return this.filteredItems},e.prototype.getItems=function(){return this.items},e.prototype.layout=function(){var e=this;e.items.css("display","none");var i=0,n=[];e.filteredItems.each((function(){e.config.showOnCurPage(++i)&&(t(this).css("display","block"),n.push(this))})),n=t(n),e.config.postsContainer.data("visibleItems",n),e.config.postsContainer.collage({images:n})},e.prototype._filterPosts=function(){var t=this;t.filteredItems=t.items.filter(t.config.curCategory)},e}();!function(){function e(t){ui.call(this,t),this.items=this.config.postsContainer.find(".wf-cell"),this.filteredItems=this.items}e.prototype=new ui,e.prototype.getFilteredItems=function(){return this.filteredItems},e.prototype.getItems=function(){return this.items},e.prototype.layout=function(){},e.prototype._filterPosts=function(){this.items.hide(),this.filteredItems=this._sortItems(this.items.filter(this.config.curCategory)),this.filteredItems.detach().prependTo(this.config.postsContainer),this.filteredItems.show()},e.prototype._sortItems=function(e){var i=this.config.orderBy,n=this.config.order,o=t([]);return o.$nodesCache=t([]),e.each((function(){var e=t(this);o.push({node:this,$node:e,name:e.attr("data-name"),date:new Date(e.attr("data-date"))})})),"date"===i&&"desc"===n?o.sort((function(t,e){return e.date-t.date})):"date"===i&&"asc"===n?o.sort((function(t,e){return t.date-e.date})):"name"===i&&"desc"===n?o.sort((function(t,e){var i=t.name.toLowerCase(),n=e.name.toLowerCase();return i>n?-1:i<n?1:0})):"name"===i&&"asc"===n&&o.sort((function(t,e){var i=t.name.toLowerCase(),n=e.name.toLowerCase();return i<n?-1:i>n?1:0})),o.each((function(){o.$nodesCache.push(this.node)})),o.$nodesCache}}();t(".dt-shortcode.with-isotope").each((function(){var e=t(this),i=e.find(".iso-grid, .iso-container"),n=i.hasClass("dt-isotope"),o={postsContainer:i,categoryContainer:e.find(".filter-categories"),curCategory:e.find(".filter-categories a.act").attr("data-filter")};if(n){var s=e.find(".filter-extras .filter-sorting a.act").attr("data-sort");s||(s=e.find(".filter-categories").attr("data-default-order"));var a=e.find(".filter-extras .filter-by a.act").attr("data-by");a||(a=e.find(".filter-categories").attr("data-default-orderby")),t.extend(o,{order:s,orderBy:a,orderByContainer:e.find(".filter-extras .filter-by"),orderContainer:e.find(".filter-extras .filter-sorting"),onCategoryFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setCurCategory(i.attr("data-filter")),n._filterPosts(),l.hideItem(n.getItems()),l.reset(n.getFilteredItems()),n.layout(),n.config.postsContainer.IsoLayzrInitialisation(),ni(),ti()},onOrderFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setOrder(i.attr("data-sort")),n._filterPosts(),l.hideItem(n.getItems()),l.reset(n.getFilteredItems()),n.layout(),n.config.postsContainer.IsoLayzrInitialisation(),ni(),ti()},onOrderByFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setOrderBy(i.attr("data-by")),n._filterPosts(),l.hideItem(n.getItems()),l.reset(n.getFilteredItems()),n.layout(),n.config.postsContainer.IsoLayzrInitialisation(),ni(),ti()}});var r=new ui(o),l=new ci({previousButtonClass:"nav-prev filter-item",previousButtonLabel:"←",nextButtonClass:"nav-next filter-item",nextButtonLabel:"→",postLimit:i.attr("data-posts-per-page"),curPage:1,pagesToShow:i.hasClass("show-all-pages")?999:5,items:r.getFilteredItems(),paginatorContainer:e.find(".paginator"),onPaginate:function(e,i){e.preventDefault();var n=t(this),o=e.data.self;o._setAsActive(n),o.setCurPage(n.attr("data-page-num")),o._filterByCurPage(),r.layout(),i||(o.appendControls(),r.scrollToTopOfContainer())}})}else{r=new pi(o),l=new ci({previousButtonClass:"nav-prev filter-item",previousButtonLabel:"←",nextButtonClass:"nav-next filter-item",nextButtonLabel:"→",postLimit:i.attr("data-posts-per-page"),curPage:1,pagesToShow:i.hasClass("show-all-pages")?999:5,items:r.getFilteredItems(),paginatorContainer:e.find(".paginator")});r.config.onCategoryFilter=function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setCurCategory(i.attr("data-filter")),n._filterPosts(),l.hideItem(n.getItems()),l.reset(n.getFilteredItems()),n.layout(),ni(),ti()},r.config.showOnCurPage=function(t){return l._showOnCurPage(t)},l.config.onPaginate=function(e,i){e.preventDefault();var n=t(this),o=e.data.self;o._setAsActive(n),o.setCurPage(n.attr("data-page-num")),o._filterByCurPage(),r.layout(),i||(o.appendControls(),r.scrollToTopOfContainer())},t("a.act",l.config.paginatorContainer).trigger("click.dtPostsPaginationFilter",{onSetup:!0})}})),window.the7ApplyMasonryJsFiltering=function(e){var i=e.find(".iso-grid, .iso-container"),o=e.find(".paginator"),s=e.hasClass("lazy-loading-mode"),a=e.find(".filter-extras .filter-sorting a.act").attr("data-sort");a||(a=e.find(".filter-categories").attr("data-default-order"));var r=e.find(".filter-extras .filter-by a.act").attr("data-by");r||(r=e.find(".filter-categories").attr("data-default-orderby")),e.is(".content-rollover-layout-list:not(.disable-layout-hover)")&&i.on("updateReady",(function(){t(this).find(".wf-cell.visible .post-entry-wrapper").clickOverlayGradient()}));var l={order:a,orderBy:r,curCategory:e.find(".filter-categories a.act").attr("data-filter"),postsContainer:i,categoryContainer:e.find(".filter-categories"),orderByContainer:e.find(".filter-extras .filter-by"),orderContainer:e.find(".filter-extras .filter-sorting"),onCategoryFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setCurCategory(i.attr("data-filter")),n._filterPosts(),c.hideItem(n.getItems()),c.reset(n.getFilteredItems()),n.layoutItems(),n.config.postsContainer.IsoLayzrInitialisation(),g(),ti()},onOrderFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setOrder(i.attr("data-sort")),n._filterPosts(),c.hideItem(n.getItems()),c.reset(n.getFilteredItems()),n.layoutItems(),n.config.postsContainer.IsoLayzrInitialisation(),g(),ti()},onOrderByFilter:function(e){e.preventDefault();var i=t(this),n=e.data.self;n.config.postsContainer.resetEffects(),n._setAsActive(i),n.setOrderBy(i.attr("data-by")),n._filterPosts(),c.hideItem(n.getItems()),c.reset(n.getFilteredItems()),n.layoutItems(),n.config.postsContainer.IsoLayzrInitialisation(),g(),ti()}},d=new ui(l);switch(e.attr("data-pagination-mode")){case"load-more":var c=new hi({postLimit:e.attr("data-post-limit"),curPage:1,items:d.getFilteredItems(),filter:d,paginatorContainer:o,onPaginate:function(t){var e=t.data.self;t.preventDefault(),e.setCurPage(e.getCurPage()+1),e.layoutItems(),e.applyLoadingEffects(),e.appendControls()}});break;case"pages":var h="←",u="→",p="page";e.is("[class*='the7_elements-']")&&(h="←",u="→",p="page-numbers filter-item");c=new ci({previousButtonClass:"nav-prev filter-item",previousButtonLabel:h,nextButtonClass:"nav-next filter-item",nextButtonLabel:u,pagerClass:p,postLimit:e.attr("data-post-limit"),curPage:1,pagesToShow:e.hasClass("show-all-pages")?999:5,items:d.getFilteredItems(),filter:d,paginatorContainer:o,onPaginate:function(e,i){var n=t(this),o=e.data.self;e.preventDefault(),o._setAsActive(n),o.setCurPage(n.attr("data-page-num")),o.layoutItems(),i||(o.appendControls(),d.scrollToTopOfContainer()),o.applyLoadingEffects()}});break;default:c=new di}function f(){var t=o.find(".button-load-more"),e=t.offset();o.hasClass("hidden")&&m(),e&&n.scrollTop()>(e.top-n.height())/2&&t.trigger("click")}function m(){n.off("scroll",f)}function g(){s&&(m(),n.on("scroll",f),f())}g()},t(".mode-masonry.jquery-filter, .mode-grid.jquery-filter:not(.dt-css-grid-wrap)").one("IsoReady",(function(){the7ApplyMasonryJsFiltering(t(this))})),void 0!==b&&(t.fn.owlCarousel=b)}));