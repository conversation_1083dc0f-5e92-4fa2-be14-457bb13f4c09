!function(p){p.fn.bsf_appear=function(s,e){const b=p.extend({data:void 0,one:!0,accX:0,accY:0},e);return this.each(function(){const i=p(this);if(i.bsf_appeared=!1,s){const l=p(window),a=function(){var e,a,s,p,f,t,r,n,c,o;i.is(":visible")?(e=l.scrollLeft(),a=l.scrollTop(),s=(o=i.offset()).left,p=o.top,f=b.accX,t=b.accY,r=i.height(),n=l.height(),c=i.width(),o=l.width(),a<=p+r+t&&p<=a+n+t&&e<=s+c+f&&s<=e+o+f?i.bsf_appeared||i.trigger("bsf_appear",b.data):i.bsf_appeared=!1):i.bsf_appeared=!1};function e(){var e;i.bsf_appeared=!0,b.one&&(l.off("scroll",a),0<=(e=p.inArray(a,p.fn.bsf_appear.checks))&&p.fn.bsf_appear.checks.splice(e,1)),s.apply(this,arguments)}b.one?i.one("bsf_appear",b.data,e):i.bind("bsf_appear",b.data,e),l.on("scroll",a),p.fn.bsf_appear.checks.push(a),a()}else i.trigger("bsf_appear",b.data)})},p.extend(p.fn.bsf_appear,{checks:[],timeout:null,checkAll(){let e=p.fn.bsf_appear.checks.length;if(0<e)for(;e--;)p.fn.bsf_appear.checks[e]()},run(){p.fn.bsf_appear.timeout&&clearTimeout(p.fn.bsf_appear.timeout),p.fn.bsf_appear.timeout=setTimeout(p.fn.bsf_appear.checkAll,20)}}),p.each(["append","prepend","after","before","attr","removeAttr","addClass","removeClass","toggleClass","remove","css","show","hide"],function(e,a){const s=p.fn[a];s&&(p.fn[a]=function(){var e=s.apply(this,arguments);return p.fn.bsf_appear.run(),e})})}(jQuery);