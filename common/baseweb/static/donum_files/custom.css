@charset "utf-8";
.vertical-centering {
  top: 50%;
  transform: translateY(-50%);
}
.horizontal-centering {
  left: 50%;
  transform: translateX(-50%);
}
.centering-mixin {
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
.masthead:not(.side-header) {
  max-width: 100%;
}
.transparent:not(.photo-scroller-album):not(.phantom-sticky) .masthead:not(.side-header) {
  position: absolute;
  width: 100%;
  z-index: 102;
}
.masthead:not(.side-header):not(.side-header-v-stroke):not(.side-header-menu-icon) {
  box-sizing: border-box;
}
@media all and (-ms-high-contrast: none),(-ms-high-contrast: active) {
  .masthead:not(.side-header):not(.mixed-header) {
    display: -ms-flexbox;
    -ms-flex-direction: column;
    -ms-align-content: space-between;
    align-content: space-between;
    -ms-flex-pack: center;
    -ms-justify-content: center;
    justify-content: center;
  }
  .masthead:not(.side-header):not(.mixed-header) .top-bar,
  .masthead:not(.side-header):not(.mixed-header) .header-bar,
  .masthead:not(.side-header):not(.mixed-header) .ph-wrap {
    width: 100%;
  }
  .masthead .header-bar {
    box-sizing: border-box;
  }
  .phantom-sticky.floating-navigation-below-slider .fixed-masthead.masthead:not(.sticky-on) {
    top: auto !important;
  }
}
.masthead:not(.side-header) .header-bar {
  position: relative;
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -ms-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -ms-align-content: stretch;
  align-content: stretch;
  -ms-flex-line-pack: stretch;
  -ms-flex-pack: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -ms-flex-pack: justify;
}
.masthead:not(.side-header) .main-nav {
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -ms-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.masthead:not(.side-header).full-height .main-nav {
  -ms-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -ms-align-content: stretch;
  align-content: stretch;
}
.masthead:not(.side-header).full-height .header-bar .main-nav > li > a,
.masthead:not(.side-header) .header-bar .main-nav > li > a {
  display: flex;
  display: -ms-flexbox;
  display: -ms-flex;
  -ms-align-content: center;
  align-content: center;
  -ms-flex-line-pack: stretch;
}
.masthead:not(.side-header).dividers.justify .main-nav > li {
  -ms-flex-positive: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  -ms-flex-pack: center;
  -ms-justify-content: center;
  justify-content: center;
}
.masthead:not(.side-header).dividers .main-nav > li:not(.dt-mega-menu) {
  position: relative;
}
.masthead:not(.side-header).dividers .main-nav > li:before,
.masthead:not(.side-header).dividers.surround .main-nav > li:last-child:after {
  content: "";
  position: absolute;
  left: -0.5px;
  width: 0;
  border-right: solid 1px yellow;
  height: 100%;
  max-height: 100%;
  top: 50%;
  transform: translateY(-50%);
}
.masthead:not(.side-header).dividers .main-nav > li:first-child:before {
  display: none;
}
.masthead:not(.side-header).dividers.surround .main-nav > li:first-child:before {
  display: block;
}
.masthead:not(.side-header).dividers.surround .main-nav > li:last-child:after {
  left: auto;
  right: -0.5px;
}
.masthead:not(.side-header).full-height .main-nav > li > a {
  -ms-flex-positive: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  -ms-flex-pack: center;
  -ms-justify-content: center;
  justify-content: center;
}
.masthead.inline-header:not(.widgets) .header-bar .mini-widgets {
  display: none;
}
.inline-header .header-bar {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
}
.inline-header .branding,
.inline-header .header-bar > .mini-widgets {
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
  -ms-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.inline-header.left .main-nav,
.inline-header.right .main-nav,
.inline-header.center .main-nav {
  -ms-flex-positive: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
}
.inline-header.left .main-nav {
  -ms-flex-pack: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
  -ms-flex-pack: start;
}
.inline-header.right .main-nav {
  -ms-flex-pack: flex-end;
  -ms-justify-content: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
}
.inline-header.center .main-nav {
  -ms-flex-pack: center;
  -ms-justify-content: center;
  justify-content: center;
}
.inline-header.justify .main-nav {
  -ms-flex-positive: 10;
  -ms-flex-grow: 10;
  flex-grow: 10;
  -ms-flex-pack: space-around;
  -ms-justify-content: space-around;
  justify-content: space-around;
  -ms-flex-pack: distribute;
}
.inline-header.justify .main-nav > li {
  -ms-flex-positive: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  -ms-flex-pack: flex-end;
  -ms-justify-content: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
}
.inline-header.justify .main-nav > li > a {
  display: -ms-flexbox;
  display: -ms-flex;
  display: flex;
}
.inline-header.justify.widgets .main-nav > li {
  -ms-flex-pack: center;
  -ms-justify-content: center;
  justify-content: center;
}
:root {
  --the7-h1-spacing: var(--the7-p-spacing,10px);
  --the7-h2-spacing: var(--the7-p-spacing,10px);
  --the7-h3-spacing: var(--the7-p-spacing,10px);
  --the7-h4-spacing: var(--the7-p-spacing,10px);
  --the7-h5-spacing: var(--the7-p-spacing,10px);
  --the7-h6-spacing: var(--the7-p-spacing,10px);
  --the7-form-border: solid;
  --the7-btn-shadow: 0px 0px 10px 0px rgba(0,0,0,0) ;
  --the7-btn-shadow-hover: 0px 0px 10px 0px rgba(0,0,0,0) ;
}
#the7-body {
  --the7-h1-color: var(--the7-title-color);
  --the7-h2-color: var(--the7-title-color);
  --the7-h3-color: var(--the7-title-color);
  --the7-h4-color: var(--the7-title-color);
  --the7-h5-color: var(--the7-title-color);
  --the7-h6-color: var(--the7-title-color);
  --the7-btn-s-font: var(--the7-btn-s-font-style,normal) normal var(--the7-btn-s-font-weight,normal) var(--the7-btn-s-font-size) / var(--the7-btn-s-line-height) var(--the7-btn-s-font-family);
  --the7-btn-m-font: var(--the7-btn-m-font-style,normal) normal var(--the7-btn-m-font-weight,normal) var(--the7-btn-m-font-size) / var(--the7-btn-m-line-height) var(--the7-btn-m-font-family);
  --the7-btn-l-font: var(--the7-btn-l-font-style,normal) normal var(--the7-btn-l-font-weight,normal) var(--the7-btn-l-font-size) / var(--the7-btn-l-line-height) var(--the7-btn-l-font-family);
  --the7-btn-lg-font: var(--the7-btn-lg-font-style,normal) normal var(--the7-btn-lg-font-weight,normal) var(--the7-btn-lg-font-size) / var(--the7-btn-lg-line-height) var(--the7-btn-lg-font-family);
  --the7-btn-xl-font: var(--the7-btn-xl-font-style,normal) normal var(--the7-btn-xl-font-weight,normal) var(--the7-btn-xl-font-size) / var(--the7-btn-xl-line-height) var(--the7-btn-xl-font-family);
  --the7-woo-title-font: var(--the7-woo-title-font-style,normal) normal var(--the7-woo-title-font-weight,normal) var(--the7-woo-title-font-size) / var(--the7-woo-title-line-height) var(--the7-woo-title-font-family);
  --the7-woo-content-font: var(--the7-woo-content-font-style,normal) normal var(--the7-woo-content-font-weight,normal) var(--the7-woo-content-font-size) / var(--the7-woo-content-line-height) var(--the7-woo-content-font-family);
  --the7-base-font: var(--the7-base-font-style,normal) normal var(--the7-base-font-weight,normal) var(--the7-base-font-size) / var(--the7-base-line-height) var(--the7-base-font-family);
  --the7-base-font-big: var(--the7-base-font-style,normal) normal var(--the7-base-font-weight,normal) var(--the7-text-big-font-size) / var(--the7-text-big-line-height) var(--the7-base-font-family);
  --the7-widget-title-font: var(--the7-widget-title-font-style,normal) normal var(--the7-widget-title-font-weight,normal) var(--the7-widget-title-font-size) / var(--the7-widget-title-line-height) var(--the7-widget-title-font-family);
  --the7-widget-content-font: var(--the7-widget-content-font-style,normal) normal var(--the7-widget-content-font-weight,normal) var(--the7-widget-content-font-size) / var(--the7-widget-content-line-height) var(--the7-widget-content-font-family);
  --the7-h1-font: var(--the7-h1-font-style,normal) normal var(--the7-h1-font-weight,normal) var(--the7-h1-font-size) / var(--the7-h1-line-height) var(--the7-h1-font-family);
  --the7-h2-font: var(--the7-h2-font-style,normal) normal var(--the7-h2-font-weight,normal) var(--the7-h2-font-size) / var(--the7-h2-line-height) var(--the7-h2-font-family);
  --the7-h3-font: var(--the7-h3-font-style,normal) normal var(--the7-h3-font-weight,normal) var(--the7-h3-font-size) / var(--the7-h3-line-height) var(--the7-h3-font-family);
  --the7-h4-font: var(--the7-h4-font-style,normal) normal var(--the7-h4-font-weight,normal) var(--the7-h4-font-size) / var(--the7-h4-line-height) var(--the7-h4-font-family);
  --the7-h5-font: var(--the7-h5-font-style,normal) normal var(--the7-h5-font-weight,normal) var(--the7-h5-font-size) / var(--the7-h5-line-height) var(--the7-h5-font-family);
  --the7-h6-font: var(--the7-h6-font-style,normal) normal var(--the7-h6-font-weight,normal) var(--the7-h6-font-size) / var(--the7-h6-line-height) var(--the7-h6-font-family);
  --the7-form-md-font-family: var(--the7-base-font-family);
  --the7-form-md-font-style: var(--the7-base-font-style,normal);
  --the7-form-md-font-weight: var(--the7-base-font-weight,normal);
  --the7-form-md-font-size: var(--the7-base-font-size);
  --the7-form-md-line-height: var(--the7-base-line-height);
  --the7-form-md-font: var(--the7-form-md-font-style) normal var(--the7-form-md-font-weight) var(--the7-form-md-font-size,--the7-base-font-size) / var(--the7-form-md-line-height,--the7-base-line-height) var(--the7-form-md-font-family,--the7-base-font-family);
  --the7-fit-height: calc(var(--the7-vh,1vh) * 100);
}
#the7-body.admin-bar {
  --the7-fit-height: calc(var(--the7-vh,1vh) * 100 - 32px);
}
@media screen and (max-width: 782px) {
  #the7-body.admin-bar {
    --the7-fit-height: calc(var(--the7-vh,1vh) * 100);
  }
}
.transparent-border-mixin {
  border-color: var(--the7-divider-color);
}
.sidebar .transparent-border-mixin,
.sidebar-content .transparent-border-mixin {
  border-color: rgba(133,134,140,0.15);
}
.footer .transparent-border-mixin {
  border-color: rgba(255,255,255,0.15);
}
.solid-bg-mixin {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .outline-decoration {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .shadow-decoration {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.accent-bg-mixin {
  color: #fff;
  background-color: var(--the7-accent-color);
}
html,
body,
body.page,
.wf-container > * {
  font: var(--the7-base-font-big);
  letter-spacing: var(--the7-base-letter-spacing);
  text-transform: var(--the7-base-text-transform);
  text-decoration: var(--the7-base-text-decoration);
  word-spacing: normal;
  color: var(--the7-base-color);
}
a.dt-owl-item-wrap,
a.dt-owl-item-wrap:hover {
  color: var(--the7-base-color);
}
.elementor-widget[class*='elementor-widget-wp-widget-'] a:not(:hover) {
  color: var(--the7-base-color);
}
#main .wf-wrap,
.page-title .wf-wrap,
.fancy-header .wf-wrap {
  padding: 0 50px 0 50px;
}
.side-header .mobile-header-bar {
  box-sizing: border-box;
}
.no-cssgridlegacy.no-cssgrid .wf-container,
.no-cssgridlegacy.no-cssgrid .wf-container-main {
  margin: 0 -50px 0 -50px;
}
#main-slideshow.fixed > .royalSlider {
  max-width: 1300px;
}
.masthead:not(.side-header):not(.side-header-menu-icon) .header-bar {
  max-width: calc(1300px - 20px - 20px);
  margin: 0 auto;
}
.masthead:not(.side-header).full-width .header-bar,
.header-bar .masthead.side-header-menu-icon {
  max-width: 100%;
}
.page-title .wf-wrap:after {
  width: calc(1300px - 50px - 50px);
  max-width: calc(100% - 50px - 50px);
}
.boxed .page-title .wf-wrap:after {
  left: 50px;
}
.no-cssgridlegacy.no-cssgrid .wf-container,
.no-cssgridlegacy.no-cssgrid .wf-container-main {
  margin: 0 -50px 0 -50px;
}
.no-cssgridlegacy.no-cssgrid .sidebar-right .content {
  padding-left: 50px;
}
.no-cssgridlegacy.no-cssgrid .sidebar-left .content {
  padding-right: 50px;
}
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-divider-off.sidebar-right .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar.solid-bg,
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar.bg-under-widget {
  padding-right: 50px;
}
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-divider-off.sidebar-left .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar.solid-bg,
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar.bg-under-widget {
  padding-left: 50px;
}
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-divider-off.sidebar-right .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar.solid-bg,
.no-cssgridlegacy.no-cssgrid .sidebar-right .sidebar.bg-under-widget {
  padding-left: 0;
}
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-divider-off.sidebar-left .sidebar,
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar.solid-bg,
.no-cssgridlegacy.no-cssgrid .sidebar-left .sidebar.bg-under-widget {
  padding-right: 0;
}
.no-cssgridlegacy.no-cssgrid .content,
.no-cssgridlegacy.no-cssgrid .sidebar {
  padding: 0 50px 0 50px;
}
.sidebar-right .sidebar,
.sidebar-divider-off.sidebar-right .sidebar,
.sidebar-right .sidebar.solid-bg,
.sidebar-right .sidebar.bg-under-widget {
  margin-right: 0;
  margin-left: 0;
}
.sidebar-left .sidebar,
.sidebar-divider-off.sidebar-left .sidebar,
.sidebar-left .sidebar.solid-bg,
.sidebar-left .sidebar.bg-under-widget {
  margin-right: 0;
  margin-left: 0;
}
#main-slideshow.fixed {
  padding: 0px 50px 0px 50px;
}
.wf-container {
  margin: 0 -50px 0 -50px;
}
.wf-cell,
.wf-usr-cell {
  padding: 0 50px 0 50px;
}
#footer .wf-wrap {
  padding: 0 50px 0 50px;
}
.text-big,
.dt-accordion-text-big .wpb_accordion_header > a {
  font-size: var(--the7-text-big-font-size);
  line-height: var(--the7-text-big-line-height);
}
.text-normal,
.dt-accordion-text-normal .wpb_accordion_header > a,
.icon-with-text-shortcode .dt-text-desc {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.text-small,
.dt-accordion-text-small .wpb_accordion_header > a {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.wp-caption-text {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.text-uppercase {
  text-transform: uppercase;
}
h1,
h2,
h3,
h4,
h5,
h6,
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
h1 a:hover,
h2 a:hover,
h3 a:hover,
h4 a:hover,
h5 a:hover,
h6 a:hover,
.h1-size,
.entry-title.h1-size,
.h2-size,
.entry-title.h2-size,
h1.entry-title,
.h3-size,
.entry-title.h3-size,
h2.entry-title,
.h4-size,
.entry-title.h4-size,
h3.entry-title,
.h5-size,
.entry-title.h5-size,
.h6-size,
.entry-title.h6-size,
.header-color,
.entry-meta .header-color,
.color-title {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h1,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h2,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h3,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h4,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h5,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h6,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h1 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h2 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h3 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h4 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h5 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h6 a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h1 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h2 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h3 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h4 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h5 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h6 a:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h1-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h1-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h2-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h2-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h1.entry-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h3-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h3-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h2.entry-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h4-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h4-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h3.entry-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h5-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h5-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .h6-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-title.h6-size,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .header-color,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .entry-meta .header-color,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .color-title {
  color: #ffffff;
}
.sidebar-content h1,
.sidebar-content h2,
.sidebar-content h3,
.sidebar-content h4,
.sidebar-content h5,
.sidebar-content h6,
.sidebar-content h1 a,
.sidebar-content h2 a,
.sidebar-content h3 a,
.sidebar-content h4 a,
.sidebar-content h5 a,
.sidebar-content h6 a,
.sidebar-content h1 a:hover,
.sidebar-content h2 a:hover,
.sidebar-content h3 a:hover,
.sidebar-content h4 a:hover,
.sidebar-content h5 a:hover,
.sidebar-content h6 a:hover,
.sidebar-content .h1-size,
.sidebar-content .entry-title.h1-size,
.sidebar-content .h2-size,
.sidebar-content .entry-title.h2-size,
.sidebar-content h1.entry-title,
.sidebar-content .h3-size,
.sidebar-content .entry-title.h3-size,
.sidebar-content h2.entry-title,
.sidebar-content .h4-size,
.sidebar-content .entry-title.h4-size,
.sidebar-content h3.entry-title,
.sidebar-content .h5-size,
.sidebar-content .entry-title.h5-size,
.sidebar-content .h6-size,
.sidebar-content .entry-title.h6-size,
.sidebar-content .header-color,
.sidebar-content .entry-meta .header-color,
.sidebar-content .color-title {
  color: #333333;
}
.footer h1,
.footer h2,
.footer h3,
.footer h4,
.footer h5,
.footer h6,
.footer h1 a,
.footer h2 a,
.footer h3 a,
.footer h4 a,
.footer h5 a,
.footer h6 a,
.footer h1 a:hover,
.footer h2 a:hover,
.footer h3 a:hover,
.footer h4 a:hover,
.footer h5 a:hover,
.footer h6 a:hover,
.footer .h1-size,
.footer .entry-title.h1-size,
.footer .h2-size,
.footer .entry-title.h2-size,
.footer h1.entry-title,
.footer .h3-size,
.footer .entry-title.h3-size,
.footer h2.entry-title,
.footer .h4-size,
.footer .entry-title.h4-size,
.footer h3.entry-title,
.footer .h5-size,
.footer .entry-title.h5-size,
.footer .h6-size,
.footer .entry-title.h6-size,
.footer .header-color,
.footer .entry-meta .header-color,
.footer .color-title {
  color: #ffffff;
}
h1,
.h1-size,
.entry-title.h1-size,
.dt-accordion-h1-size .wpb_accordion_header > a {
  color: var(--the7-h1-color);
  font: var(--the7-h1-font);
  text-transform: var(--the7-h1-text-transform);
  letter-spacing: var(--the7-h1-letter-spacing);
  word-spacing: var(--the7-h1-word-spacing);
  text-decoration: var(--the7-h1-text-decoration);
}
h2,
.h2-size,
.entry-title.h2-size,
h1.entry-title,
.dt-accordion-h2-size .wpb_accordion_header > a {
  color: var(--the7-h2-color);
  font: var(--the7-h2-font);
  text-transform: var(--the7-h2-text-transform);
  letter-spacing: var(--the7-h2-letter-spacing);
  word-spacing: var(--the7-h2-word-spacing);
  text-decoration: var(--the7-h2-text-decoration);
}
h3,
.h3-size,
.entry-title.h3-size,
h2.entry-title,
.dt-accordion-h3-size .wpb_accordion_header > a {
  color: var(--the7-h3-color);
  font: var(--the7-h3-font);
  text-transform: var(--the7-h3-text-transform);
  letter-spacing: var(--the7-h3-letter-spacing);
  word-spacing: var(--the7-h3-word-spacing);
  text-decoration: var(--the7-h3-text-decoration);
}
h4,
.h4-size,
.entry-title.h4-size,
h3.entry-title,
.dt-accordion-h4-size .wpb_accordion_header > a,
.vc_separator h4 {
  color: var(--the7-h4-color);
  font: var(--the7-h4-font);
  text-transform: var(--the7-h4-text-transform);
  letter-spacing: var(--the7-h4-letter-spacing);
  word-spacing: var(--the7-h4-word-spacing);
  text-decoration: var(--the7-h4-text-decoration);
}
h5,
.h5-size,
h4.entry-title,
.entry-title.h5-size,
.dt-accordion-h5-size .wpb_accordion_header > a {
  color: var(--the7-h5-color);
  font: var(--the7-h5-font);
  text-transform: var(--the7-h5-text-transform);
  letter-spacing: var(--the7-h5-letter-spacing);
  word-spacing: var(--the7-h5-word-spacing);
  text-decoration: var(--the7-h5-text-decoration);
}
h6,
.h6-size,
.entry-title.h6-size,
.dt-accordion-h6-size .wpb_accordion_header > a {
  color: var(--the7-h6-color);
  font: var(--the7-h6-font);
  text-transform: var(--the7-h6-text-transform);
  letter-spacing: var(--the7-h6-letter-spacing);
  word-spacing: var(--the7-h6-word-spacing);
}
.text-primary {
  font-size: var(--the7-text-big-font-size);
  line-height: var(--the7-text-big-line-height);
}
.content .text-primary {
  color: var(--the7-h4-color);
}
.footer .text-primary {
  color: #ffffff;
}
a.text-primary {
  text-decoration: none;
}
a.text-primary:hover {
  text-decoration: underline;
}
.color-primary .sidebar,
.sidebar-content .color-primary {
  color: #333333;
}
.footer .color-primary {
  color: #ffffff;
}
a.color-primary {
  text-decoration: none;
}
a.color-primary:hover {
  text-decoration: underline;
}
.text-secondary {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.color-secondary,
.text-secondary {
  color: var(--the7-accent-color);
}
.stripe .color-secondary,
.stripe .text-secondary {
  color: var(--the7-accent-color);
}
a,
a:hover {
  color: var(--the7-links-color);
}
a:hover {
  color: var(--the7-links-hover-color,var(--the7-links-color));
}
#page .color-accent {
  color: var(--the7-accent-color);
}
.paint-accent-color {
  color: var(--the7-accent-color) !important;
}
.color-secondary,
.color-base-transparent {
  color: var(--the7-secondary-text-color);
}
.sidebar-content .color-secondary,
.sidebar-content .color-base-transparent {
  color: rgba(133,134,140,0.5);
}
.footer .color-secondary,
.footer .color-base-transparent {
  color: rgba(255,255,255,0.5);
}
#main > .wf-wrap,
#bottom-bar > .wf-wrap,
#footer > .wf-wrap {
  width: 1300px;
}
#phantom.full-width .ph-wrap.boxed {
  max-width: 100%;
}
#page.boxed {
  max-width: 1340px;
}
.footer-overlap .boxed .footer,
.footer-overlap .boxed #bottom-bar {
  max-width: 1340px;
}
#phantom .ph-wrap.boxed,
.boxed .top-bar.line-content:before,
.boxed .classic-header.content-width-line .navigation:before {
  max-width: calc(1300px - 20px - 20px);
}
.boxed .full-width .top-bar.line-content:before,
.boxed .classic-header.full-width.content-width-line .navigation:before {
  max-width: 100%;
}
#main {
  padding: 0px 0 0px 0;
}
#main > .wf-wrap {
  padding: 0 50px 0 50px;
}
body {
  background: #f7f7f7 none repeat  left top;
  background-size: auto;
}
#page {
  background: #ffffff none repeat  center top;
  background-size: auto;
}
.fixed-page-bg #page {
  background: none;
}
.fixed-page-bg #page:after {
  content: "";
  position: fixed;
  top: 0;
  height: 100vh;
  left: 0;
  right: 0;
  z-index: -1;
  background: #ffffff none repeat center top;
  background-size: auto;
}
.fixed-page-bg #page.boxed:after {
  max-width: 1340px;
  margin: 0 auto;
}
.footer-overlap .page-inner {
  background: #ffffff none repeat  center top;
  background-size: auto;
}
textarea,
select,
blockquote,
.entry-author,
.format-aside-content,
.format-status-content,
.format-link-content,
.dt-form button,
.dt-btn,
#main-slideshow .tp-button,
.wpcf7-submit.dt-btn-m,
.shortcode-action-bg,
.shortcode-action-bg:before,
.shortcode-teaser.frame-on,
.testimonial-item  .ts-viewport,
.footer .testimonials.slider-content,
.shortcode-code,
#main .vc_text_separator div,
#main .vc_progress_bar .vc_single_bar,
#main .vc_progress_bar .vc_single_bar .vc_label,
#main .vc_progress_bar .vc_single_bar .vc_bar,
#main .flexslider,
.nsu-submit,
.mc4wp-form input[type="url"],
.mc4wp-form input[type="date"],
#main #content .rotatingtweets,
#main #content .norotatingtweets,
.mc4wp-form form select,
#megaMenu ul.megaMenu > li.menu-item,
#megaMenu ul.megaMenu > li.menu-item > a,
.customSelect,
.loading-label,
.widget .testimonial-content,
.skill,
.skill-value,
.widget .team-container,
.widget .logo-items li,
.comment-list .comment > article,
.bg-on:not(.fullwidth-img),
.blog-content .block-style-widget,
#mobile-menu,
.testimonial-content,
.wpb_content_element .wpb_accordion_header > a:before,
.wpb_content_element.dt-accordion-bg-on .wpb_accordion_wrapper .wpb_accordion_header,
.twentytwenty-before-label,
.twentytwenty-after-label,
.btn-cntr a,
.photo-scroller .album-share-overlay .share-button.entry-share,
.project-navigation,
.album-share-overlay,
.st-accordion li > a:before,
.arrows-accent .prev i,
.arrows-accent .next i,
.arrows-accent .owl-prev i,
.arrows-accent .owl-next i,
.round-images .bg-on.team-container:not(.fullwidth-img),
.solid-bg .sidebar-content,
.solid-bg.sidebar-content .widget,
.bg-under-widget .sidebar-content .widget,
.ls-container.ls-carousel .ls-nav-prev,
.ls-container.ls-carousel .ls-nav-next,
.menu-btn,
.custom-menu a:before,
.small-fancy-datas .fancy-date a {
  border-radius: 1px;
}
.wpb_tabs.tab-style-two,
.wpb_tour.tab-style-two {
  border-radius: 1px;
}
.top-bar-bg {
  background: #000061 none repeat center center;
}
#phantom .top-bar-bg,
.sticky-on .top-bar-bg,
.sticky-top-line-on .top-bar-bg {
  background: #000061 none repeat center center !important;
}
.top-bar.line-content:after,
.top-bar.full-width-line:after {
  border-bottom: 1px solid rgba(51,51,51,0.12);
}
.top-bar.line-content:after {
  width: calc(100% - 20px - 20px);
  left: auto;
  right: auto;
}
.boxed .top-bar.line-content:after {
  left: 50%;
}
.top-bar {
  min-height: 100px;
  padding: 5px 20px 5px 20px;
}
.masthead:not(.side-header):not(.side-header-menu-icon) .top-bar {
  max-width: calc(1300px);
  margin: 0 auto;
}
.masthead:not(.side-header).full-width .top-bar,
.top-bar .masthead.side-header-menu-icon {
  max-width: 100%;
}
.top-bar,
.top-bar a:not(.wpml-ls-link):not(.wpml-ls-item-toggle),
.top-bar .mini-nav .customSelect,
.top-bar .mini-nav a:hover,
.header-bottom-bar a {
  color: #b2b3b9;
}
.masthead .top-bar .mini-contacts,
.masthead .top-bar .mini-nav > ul:not(.mini-sub-nav) > li > a,
.masthead .top-bar .mini-login,
.masthead .top-bar .mini-login .submit,
.masthead .top-bar .mini-search .submit,
.masthead .top-bar .mini-search,
.masthead .top-bar .wc-ico-cart,
.masthead .top-bar .text-area,
.masthead .top-bar .customSelectInner {
  font:  500 13px / 19px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.masthead .top-bar .shopping-cart,
.masthead .top-bar .soc-ico a,
.masthead .top-bar .soc-ico {
  line-height: 19px;
}
.branding > a,
.branding > img {
  padding: 0px 50px 0px 0px;
}
.transparent .masthead:not(.side-header-h-stroke) .branding > a,
.transparent .masthead:not(.side-header-h-stroke) .branding > img {
  padding: 0px 50px 0px 0px;
}
.side-header-h-stroke .branding > a,
.side-header-v-stroke .branding > a,
.side-header-h-stroke .branding > img,
.side-header-v-stroke .branding > img {
  padding: 0px 50px 0px 0px;
}
.mini-search input.field,
.overlay-search-microwidget input[type=text] {
  font:    14px / 18px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  min-height: 34px;
  height: auto;
  line-height: 34px;
  width: 200px;
  border-width: 0px;
  border-color: #e2e2e2;
  border-radius: 0px;
  background: #f4f4f4;
  padding: 0 12px 0 12px;
}
.overlay-search-microwidget .searchform:not(.search-icon-disabled) input.field,
.mini-search .searchform:not(.search-icon-disabled) input.field {
  padding-right: 12px;
}
.animate-search-width .search-icon {
  width: 16px;
}
.animate-search-width input.field:focus,
.animate-search-width input.field:active,
.animate-search-width:hover input.field {
  width: 200px;
}
.mini-search .search-icon i,
.overlay-search-microwidget .search-icon i {
  color: #aaaaaa;
}
.searchform input::-moz-placeholder {
  color: #aaaaaa !important;
  opacity: 1;
}
.searchform input[type=text],
.mini-search .field::placeholder,
.overlay-search-microwidget .field::placeholder {
  color: #aaaaaa;
}
.popup-search .field::placeholder {
  color: #aaaaaa !important;
}
.overlay-search-microwidget .search-icon,
.mini-search .search-icon {
  right: 12px;
}
.overlay-search-microwidget .search-icon i,
.mini-search .search-icon i {
  font-size: 16px;
}
.popup-search .search-icon {
  right: 22px;
}
.branding .popup-search .searchform .submit > span,
.branding .overlay-search .searchform .submit > span {
  margin-left: 7.1428571428571px;
}
.mixed-header .header-bar .popup-search .searchform .submit > span,
.mixed-header .header-bar .overlay-search .searchform .submit > span {
  margin-left: 7.1428571428571px;
}
.popup-search .submit i,
.overlay-search .submit i {
  font-size: 16px;
  color: #ffffff;
}
.branding .popup-search .submit i,
.branding .overlay-search .submit i {
  font-size: 0px;
}
.mixed-header .popup-search .submit i,
.mixed-header .overlay-search .submit i {
  font-size: 16px;
  color: #888888;
}
.masthead .top-bar .popup-search .submit i,
.masthead .top-bar .overlay-search .submit i {
  font-size: 16px;
  color: #b2b3b9;
}
.dt-mobile-header .popup-search .submit i,
.dt-mobile-header .overlay-search .submit i {
  font-size: 16px;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .popup-search .submit i,
.mobile-header-bar .mobile-mini-widgets .overlay-search .submit i {
  font-size: 16px;
  color: #ffffff;
}
.overlay-search-microwidget {
  background: rgba(0,0,0,0.9);
}
.masthead .popup-search .popup-search-wrap {
  top: calc(100% + 8px);
}
.dt-mobile-header .popup-search .popup-search-wrap {
  width: 100%;
}
.masthead.side-header .mini-widgets .popup-search .popup-search-wrap {
  top: auto;
  bottom: calc(100% + 8px);
}
.masthead .mini-widgets .popup-search .popup-search-wrap.bottom-overflow {
  top: auto;
  bottom: calc(100% + 8px);
}
.masthead .mini-search input[type="text"]::-moz-placeholder {
  color: #ffffff;
}
.mini-search .submit {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mini-search .submit:hover {
  opacity: 0.7;
}
.branding .mini-search .submit {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.mixed-header .mini-search .submit {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.dt-mobile-header .mini-search .submit {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-search .submit {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.dt-mobile-header .mini-search .popup-search-wrap {
  top: auto;
  bottom: auto;
}
.overlay-search-microwidget .search-icon,
.mini-search .search-icon {
  color: #aaaaaa;
}
.popup-search .submit.default-icon:before,
.overlay-search .submit.default-icon:before {
  color: #ffffff;
}
.mixed-header .popup-search .submit.default-icon:before,
.mixed-header .overlay-search .submit.default-icon:before {
  color: #888888;
}
.dt-mobile-header .popup-search .submit.default-icon:before,
.dt-mobile-header .overlay-search .submit.default-icon:before {
  color: #000061;
}
.mobile-header-bar .popup-search .submit.default-icon:before,
.mobile-header-bar .overlay-search .submit.default-icon:before {
  color: #ffffff;
}
.top-bar .mini-search .submit {
  color: #b2b3b9;
}
.top-bar .mini-search .submit:hover {
  opacity: 0.7;
}
.top-bar .popup-search .submit:before,
.top-bar .overlay-search .submit:before {
  color: rgba(255,255,255,0);
}
.login-remember {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.mini-login,
.mini-login .submit {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mini-login i,
.mini-login .submit i {
  font-size: 16px;
  color: #ffffff;
}
.branding .mini-login,
.branding .mini-login .submit {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.branding .mini-login i,
.branding .mini-login .submit i {
  font-size: 0px;
  margin-right: 7.1428571428571px;
}
.mixed-header .mini-login,
.mixed-header .mini-login .submit {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.mixed-header .mini-login i,
.mixed-header .mini-login .submit i {
  font-size: 16px;
  color: #888888;
}
.masthead .top-bar .mini-login,
.masthead .top-bar .mini-login .submit {
  color: #b2b3b9;
}
.masthead .top-bar .mini-login i,
.masthead .top-bar .mini-login .submit i {
  font-size: 16px;
  color: #b2b3b9;
}
.mixed-header .header-bar .mini-login i,
.mixed-header .header-bar .mini-login .submit i {
  margin-right: 7.1428571428571px;
}
.dt-mobile-header .mini-login,
.dt-mobile-header .mini-login .submit {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.dt-mobile-header .mini-login i,
.dt-mobile-header .mini-login .submit i {
  font-size: 16px;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-login,
.mobile-header-bar .mobile-mini-widgets .mini-login .submit {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mobile-header-bar .mobile-mini-widgets .mini-login i,
.mobile-header-bar .mobile-mini-widgets .mini-login .submit i {
  font-size: 16px;
  color: #ffffff;
}
a.mini-contacts:hover {
  opacity: 0.7;
}
.mini-contacts {
  text-decoration: none;
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mini-contacts i {
  font-size: 16px;
  color: #ffffff;
}
.branding .mini-contacts {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.branding .mini-contacts i {
  font-size: 0px;
}
.mixed-header .mini-contacts {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.mixed-header .mini-contacts i {
  font-size: 16px;
  color: #888888;
}
.mixed-header .header-bar .mini-contacts i {
  margin-right: 7.1428571428571px;
}
.masthead .top-bar .mini-contacts {
  color: #b2b3b9;
}
.masthead .top-bar .mini-contacts i {
  font-size: 16px;
  color: #b2b3b9;
}
.dt-mobile-header .mini-contacts {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.dt-mobile-header .mini-contacts i {
  font-size: 16px;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-contacts {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mobile-header-bar .mobile-mini-widgets .mini-contacts i {
  font-size: 16px;
  color: #ffffff;
}
.masthead .mini-widgets .soc-ico a,
.masthead .mobile-mini-widgets .soc-ico a,
.dt-mobile-header .soc-ico a {
  width: 28px;
  height: 28px;
  margin: 0 2px;
  line-height: 28px;
  border-radius: 100px;
}
.masthead .mini-widgets .soc-ico a:before,
.masthead .mini-widgets .soc-ico a:after,
.masthead .mobile-mini-widgets .soc-ico a:before,
.masthead .mobile-mini-widgets .soc-ico a:after,
.dt-mobile-header .soc-ico a:before,
.dt-mobile-header .soc-ico a:after {
  width: 28px;
  height: 28px;
}
.masthead .mini-widgets .soc-ico a:first-child,
.masthead .mobile-mini-widgets .soc-ico a:first-child,
.dt-mobile-header .soc-ico a:first-child {
  margin-left: 0;
}
.masthead .mini-widgets .soc-ico a:last-child,
.masthead .mobile-mini-widgets .soc-ico a:last-child,
.dt-mobile-header .soc-ico a:last-child {
  margin-right: 0;
}
.masthead .soc-ico .soc-font-icon,
.dt-mobile-header .soc-ico .soc-font-icon {
  font-size: 16px;
  line-height: 28px;
}
.masthead .soc-ico.border-on a:before,
.dt-mobile-header .soc-ico.border-on a:before {
  box-shadow: inset 0px 0px 0px 2px #000061;
}
.masthead .soc-ico.hover-border-on a:hover:after,
.dt-mobile-header .soc-ico.hover-border-on a:hover:after {
  box-shadow: inset 0px 0px 0px 2px #000061;
}
.masthead .soc-ico.custom-bg a:before,
.masthead .soc-ico.accent-bg a:before,
.dt-mobile-header .soc-ico.custom-bg a:before,
.dt-mobile-header .soc-ico.accent-bg a:before {
  background-color: rgba(255,255,255,0);
}
.masthead .soc-ico.hover-custom-bg a:after,
.masthead .soc-ico.accent-bg.hover-custom-bg a:after,
.masthead .soc-ico.hover-custom-bg a:after,
.accent-gradient .masthead .soc-ico.gradient-bg.hover-custom-bg a:after,
.masthead .soc-ico.hover-accent-bg a:after,
.dt-mobile-header .soc-ico.hover-custom-bg a:after,
.dt-mobile-header .soc-ico.accent-bg.hover-custom-bg a:after,
.dt-mobile-header .soc-ico.hover-custom-bg a:after,
.accent-gradient .dt-mobile-header .soc-ico.gradient-bg.hover-custom-bg a:after,
.dt-mobile-header .soc-ico.hover-accent-bg a:after {
  background-color: #000061;
  background-image: none;
}
.masthead .mini-widgets .soc-ico a:not(:hover) .soc-font-icon,
.masthead .mobile-mini-widgets .soc-ico a:not(:hover) .soc-font-icon,
.dt-mobile-header .soc-ico a:not(:hover) .soc-font-icon {
  color: var(--the7-accent-color);
  color: #f3615a !important;
  background: none !important;
}
.masthead .mini-widgets .soc-ico a:hover .soc-font-icon,
.masthead .mobile-mini-widgets .soc-ico a:hover .soc-font-icon,
.dt-mobile-header .soc-ico a:hover .soc-font-icon {
  color: var(--the7-accent-color);
  color: #ffffff !important;
  background: none !important;
}
.text-area {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.branding .text-area {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.mixed-header .text-area {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.top-bar .text-area {
  color: #b2b3b9;
}
.dt-mobile-header .text-area {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .text-area {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mini-nav .mini-sub-nav > li:not(.wpml-ls-item) > a .menu-text,
.mini-nav .mini-sub-nav li.has-children > a:after,
.footer-sub-nav > li a .subtitle-text,
.mini-nav .mini-sub-nav li a .subtitle-text {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.footer-sub-nav > li a .subtitle-text,
.mini-nav .mini-sub-nav li a .subtitle-text {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
  font-size: calc(var(--the7-text-small-font-size) - 2);
}
.mini-sub-nav > li.act:not(.wpml-ls-item) > a .menu-text,
.mini-sub-nav > li:not(.act):not(.wpml-ls-item):hover > a .menu-text,
.mini-nav .mini-sub-nav > li.act:not(.wpml-ls-item) > a .subtitle-text,
.mini-nav .mini-sub-nav > li:not(.act):not(.wpml-ls-item):hover > a .subtitle-text {
  color: var(--the7-accent-color);
}
.masthead .menu-select,
.masthead .mini-nav > ul:not(.mini-sub-nav) > li > a {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.masthead .branding .menu-select,
.masthead .branding .mini-nav > ul:not(.mini-sub-nav) > li > a {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.masthead.mixed-header .menu-select,
.masthead.mixed-header .mini-nav > ul:not(.mini-sub-nav) > li > a {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.masthead .top-bar .menu-select,
.masthead .top-bar .mini-nav > ul:not(.mini-sub-nav) > li > a {
  color: #b2b3b9;
  font:  500 13px / 19px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.dt-mobile-header .menu-select,
.dt-mobile-header .mini-nav > ul:not(.mini-sub-nav) > li > a {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .menu-select,
.mobile-header-bar .mobile-mini-widgets .mini-nav > ul:not(.mini-sub-nav) > li > a {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .mobile-header-bar .mobile-mini-widgets .mini-nav .mini-sub-nav > li > a * {
  color: #222222 !important;
}
.masthead .mini-nav .customSelectInner {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.branding .mini-nav .customSelectInner {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.mixed-header .mini-nav .customSelectInner {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
  color: #888888;
}
.top-bar .mini-nav .customSelectInner {
  color: #b2b3b9;
  font:  500 13px / 19px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.dt-mobile-header .mini-nav .customSelectInner {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-nav .customSelectInner {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
#bottom-bar .mini-nav .customSelectInner {
  color: #ffffff;
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
  line-height: 17px;
}
#bottom-bar .mini-nav .customSelectInner i {
  color: #ffffff;
}
.dt-mobile-header .mini-nav {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-nav {
  font:   700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
}
.mini-nav .customSelectInner i,
.list-type-menu.mini-nav > ul > li > a i,
.list-type-menu-first-switch.mini-nav > ul > li > a i,
.list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 16px;
  color: #ffffff;
}
.branding .mini-nav .customSelectInner i,
.branding .list-type-menu.mini-nav > ul > li > a i,
.branding .list-type-menu-first-switch.mini-nav > ul > li > a i,
.branding .list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 0px;
  margin-right: 7.1428571428571px;
}
.mixed-header .mini-nav .customSelectInner i,
.mixed-header .list-type-menu.mini-nav > ul > li > a i,
.mixed-header .list-type-menu-first-switch.mini-nav > ul > li > a i,
.mixed-header .list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 16px;
  color: #888888;
}
.mixed-header .header-bar .mini-nav .customSelectInner i i,
.mixed-header .header-bar .list-type-menu.mini-nav > ul > li > a i i,
.mixed-header .header-bar .list-type-menu-first-switch.mini-nav > ul > li > a i i,
.mixed-header .header-bar .list-type-menu-second-switch.mini-nav > ul > li > a i i {
  margin-right: 7.1428571428571px;
}
.masthead .top-bar .mini-nav .customSelectInner i,
.masthead .top-bar .list-type-menu.mini-nav > ul > li > a i,
.masthead .top-bar .list-type-menu-first-switch.mini-nav > ul > li > a i,
.masthead .top-bar .list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 16px;
  color: #b2b3b9;
}
.dt-mobile-header .mini-nav .customSelectInner i,
.dt-mobile-header .list-type-menu.mini-nav > ul > li > a i,
.dt-mobile-header .list-type-menu-first-switch.mini-nav > ul > li > a i,
.dt-mobile-header .list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 16px;
  color: #333333;
}
.mobile-header-bar .mobile-mini-widgets .mini-nav .customSelectInner i,
.mobile-header-bar .mobile-mini-widgets .list-type-menu.mini-nav > ul > li > a i,
.mobile-header-bar .mobile-mini-widgets .list-type-menu-first-switch.mini-nav > ul > li > a i,
.mobile-header-bar .mobile-mini-widgets .list-type-menu-second-switch.mini-nav > ul > li > a i {
  font-size: 16px;
  color: #ffffff;
}
#bottom-bar .mini-nav .menu-select:hover .customSelectInner i {
  color: inherit;
}
.header-elements-button-1 {
  font:   700 14px / 18px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  padding: 10px 20px 10px 20px;
  border-radius: 0px;
}
.header-elements-button-1:not(:hover) span {
  color: #ffffff;
}
.header-elements-button-1.microwidget-btn-bg-on:before {
  background: #000061;
}
.header-elements-button-1.border-on:before {
  box-shadow: inset 0px 0px 0px 1px #000061;
}
.header-elements-button-1 i {
  font-size: 14px;
}
.header-elements-button-1.btn-icon-align-left i {
  margin-right: 5px;
}
.header-elements-button-1.btn-icon-align-right i {
  margin-left: 5px;
}
.header-elements-button-1.btn-icon-align-right:not(:hover) i {
  color: #ffffff;
}
.header-elements-button-1.btn-icon-align-left:not(:hover) i {
  color: #ffffff;
}
.header-elements-button-1:hover span {
  color: #ffffff;
}
.header-elements-button-1.btn-icon-align-right:hover i {
  color: #ffffff;
}
.header-elements-button-1.btn-icon-align-left:hover i {
  color: #ffffff;
}
.header-elements-button-1.microwidget-btn-hover-bg-on:after {
  background: #000061;
}
.header-elements-button-1.hover-border-on:after {
  box-shadow: inset 0px 0px 0px 1px #000061;
}
.header-elements-button-2 {
  font:   700 14px / 18px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  padding: 10px 20px 10px 20px;
  border-radius: 0px;
}
.header-elements-button-2:not(:hover) span {
  color: #ffffff;
}
.header-elements-button-2.microwidget-btn-bg-on:before {
  background: #000061;
}
.header-elements-button-2.border-on:before {
  box-shadow: inset 0px 0px 0px 1px #000061;
}
.header-elements-button-2 i {
  font-size: 14px;
}
.header-elements-button-2.btn-icon-align-left i {
  margin-right: 5px;
}
.header-elements-button-2.btn-icon-align-right i {
  margin-left: 5px;
}
.header-elements-button-2:not(:hover) i {
  color: #ffffff;
}
.header-elements-button-2:hover span {
  color: #ffffff;
}
.header-elements-button-2.btn-icon-align-right:hover i {
  color: #ffffff;
}
.header-elements-button-2.btn-icon-align-left:hover i {
  color: #ffffff;
}
.header-elements-button-2.btn-icon-align-right:not(:hover) i {
  color: #ffffff;
}
.header-elements-button-2.btn-icon-align-left:not(:hover) i {
  color: #ffffff;
}
.header-elements-button-2.microwidget-btn-hover-bg-on:after {
  background: #000061;
}
.header-elements-button-2.hover-border-on:after {
  box-shadow: inset 0px 0px 0px 1px #000061;
}
.masthead:not(.side-header).dividers .main-nav > li:before,
.masthead:not(.side-header).dividers.surround .main-nav > li:last-child:after {
  border-color: rgba(17,17,17,0.14);
  height: 24px;
  border-width: 1px;
}
.side-header.dividers .main-nav > li:before,
.side-header.dividers.surround .main-nav > li:last-child:after {
  border-color: rgba(17,17,17,0.14);
  width: 24px;
  border-width: 1px;
}
.main-nav > li > a {
  margin: 18px 14px 18px 14px;
  padding: 4px 4px 4px 4px;
}
.main-nav > li > a .animate-wrap {
  margin: -4px -4px -4px -4px;
  padding: 4px 4px 4px 4px;
}
.side-header.dividers:not(.surround) .main-nav {
  margin: -22px 0 -22px 0;
}
.side-header:not(.dividers) .main-nav {
  margin: -22px 0 -22px 0;
}
.top-header .outside-item-double-margin.main-nav > li:first-child > a {
  margin-left: 28px;
}
.top-header .outside-item-custom-margin.main-nav > li:first-child > a {
  margin-left: 30px;
}
.top-header .outside-item-remove-margin.main-nav > li:first-child > a {
  margin-left: 0;
}
.top-header .outside-item-double-margin.main-nav > li:last-child > a {
  margin-right: 28px;
}
.top-header .outside-item-custom-margin.main-nav > li:last-child > a {
  margin-right: 30px;
}
.top-header .outside-item-remove-margin.main-nav > li:last-child > a {
  margin-right: 0;
}
.sub-downwards .main-nav > li > .next-level-button {
  left: -14px;
  margin-top: 18px;
  margin-bottom: 18px;
}
.sub-downwards .main-nav > li > .sub-nav:last-child {
  margin-bottom: 22px;
}
.sub-downwards .main-nav > li:last-child > .sub-nav:last-child {
  margin-bottom: 0;
}
.side-header.dividers .main-nav > li:before,
.side-header.dividers.surround .main-nav > li:last-child:after {
  left: 18px;
}
.main-nav > li > a .menu-text {
  font:  normal 700 18px / 22px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.main-nav.level-arrows-on > li.has-children > a .menu-item-text {
  line-height: 22px;
}
.main-nav > li:not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.sticky-on .main-nav > li:not(.wpml-ls-item) > a .menu-text,
#phantom .main-nav > li:not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.main-nav > li:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.sticky-on .main-nav > li:not(.wpml-ls-item) > a,
#phantom .main-nav > li:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.main-nav > li.act:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.sticky-on .main-nav > li.act:not(.wpml-ls-item) > a,
#phantom .main-nav > li.act:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.main-nav > li.act:not(.wpml-ls-item) > a .menu-text,
.main-nav > li.act:not(.wpml-ls-item) > a .subtitle-text {
  color: #ffffff;
}
.sticky-on .main-nav > li.act:not(.wpml-ls-item) > a .menu-text,
#phantom .main-nav > li.act:not(.wpml-ls-item) > a .menu-text,
.sticky-on .main-nav > li.act:not(.wpml-ls-item) > a .subtitle-text,
#phantom .main-nav > li.act:not(.wpml-ls-item) > a .subtitle-text {
  color: #ffffff;
}
.main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .subtitle-text,
.main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .menu-text,
.main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .subtitle-text,
.main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.sticky-on .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .subtitle-text,
#phantom .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .subtitle-text,
.sticky-on .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .menu-text,
#phantom .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover .menu-text,
.sticky-on .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .subtitle-text,
#phantom .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .subtitle-text,
.sticky-on .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .menu-text,
#phantom .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.main-nav > li:not(.act):not(.wpml-ls-item) > a:hover,
.main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a {
  color: #ffffff;
}
.sticky-on .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover,
#phantom .main-nav > li:not(.act):not(.wpml-ls-item) > a:hover,
.sticky-on .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a,
#phantom .main-nav > li.dt-hovered:not(.act):not(.wpml-ls-item) > a {
  color: #ffffff;
}
.top-header .main-nav.level-arrows-on > li.has-children > a .menu-text:after {
  background: #ffffff;
}
.sticky-on .main-nav.level-arrows-on > li.has-children > a .menu-text:after,
#phantom .main-nav.level-arrows-on > li.has-children > a .menu-text:after {
  background: #ffffff;
}
.sub-sideways .main-nav.level-arrows-on > li.has-children > a span:after {
  background: rgba(255,255,255,0.2);
}
.sub-downwards .main-nav.level-arrows-on > li.has-children > a:after,
.sub-downwards .next-level-button svg,
.sub-downwards .main-nav > li.menu-item-language > a:after {
  color: rgba(255,255,255,0.2);
  fill: rgba(255,255,255,0.2);
}
.top-header .main-nav.level-arrows-on > li.has-children:not(.act) > a:hover .menu-text:after,
.top-header .main-nav.level-arrows-on > li.dt-hovered.has-children:not(.act) > a .menu-text:after {
  background: #ffffff;
}
.sticky-on .main-nav.level-arrows-on > li.has-children:not(.act) > a:hover .menu-text:after,
#phantom .main-nav.level-arrows-on > li.has-children:not(.act) > a:hover .menu-text:after,
.sticky-on .main-nav.level-arrows-on > li.dt-hovered.has-children:not(.act) > a .menu-text:after,
#phantom .main-nav.level-arrows-on > li.dt-hovered.has-children:not(.act) > a .menu-text:after {
  background: #ffffff;
}
.top-header .main-nav.level-arrows-on > li.has-children.act > a .menu-text:after {
  background: #ffffff;
}
.sticky-on .main-nav.level-arrows-on > li.has-children.act > a .menu-text:after,
#phantom .main-nav.level-arrows-on > li.has-children.act > a .menu-text:after {
  background: #ffffff;
}
.side-header.sub-downwards .main-nav.level-arrows-on li.has-children > a:after {
  line-height: 22px;
}
.main-nav > li > a .subtitle-text {
  font: italic normal 400 11px / 15px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.main-nav > .menu-item > a > i {
  font-size: 14px;
}
#main-nav > .menu-item > a > i {
  line-height: 14px;
}
.main-nav .sub-nav > .menu-item > a i {
  font-size: 14px;
}
.hover-outline-decoration > li > a,
.active-outline-decoration > li > a {
  border: 2px solid transparent;
}
.top-header .hover-outline-decoration > li:not(.act) > a:hover,
.top-header .hover-outline-decoration > li.dt-hovered:not(.act) > a {
  border: 2px solid #ffffff;
}
.top-header .active-outline-decoration > li.act > a {
  border: 2px solid rgba(0,0,97,0.15);
}
.top-header .active-bg-decoration > li.act > a {
  background-color: rgba(0,0,97,0.15);
}
.top-header .hover-bg-decoration > li:not(.act) > a:hover,
.top-header .hover-bg-decoration > li.dt-hovered:not(.act) > a {
  background-color: #ffffff;
}
.hover-outline-decoration > li > a,
.active-outline-decoration > li > a,
.active-bg-decoration > li > a,
.hover-bg-decoration > li > a {
  border-radius: 3px;
}
.bg-outline-decoration > li > a,
.main-nav > li > a .animate-wrap,
.hover-line-decoration > li:not(.act) > a:hover .decoration-line,
.hover-line-decoration > li.dt-hovered:not(.act) > a .decoration-line,
.active-line-decoration > li > a .decoration-line {
  border-radius: 3px;
}
.top-header .active-line-decoration > li.act > a:after {
  background-color: rgba(130,36,227,0.3);
}
.top-header .hover-line-decoration > li > a:hover .decoration-line,
.top-header .hover-line-decoration > li.dt-hovered > a .decoration-line {
  height: 2px;
  background-color: rgba(255,255,255,0.23);
  background: rgba(255,255,255,0.23);
  background: -webkit-linear-gradient(to left, rgba(255,255,255,0.23) 30%, rgba(0,0,0,0.23) 100%);
  background: linear-gradient(to left, rgba(255,255,255,0.23) 30%, rgba(0,0,0,0.23) 100%);
}
.top-header .active-line-decoration > li.act > a .decoration-line {
  height: 2px;
  background-color: rgba(130,36,227,0.3);
}
.l-to-r-line > li > a i.underline {
  bottom: -6px;
  background-color: #ffffff;
  height: 2px;
}
.from-centre-line > li > a .menu-item-text:before {
  bottom: -6px;
  height: 2px;
  background-color: #ffffff;
}
.upwards-line > li > a .menu-item-text:before {
  bottom: -6px;
  height: 2px;
  background-color: #ffffff;
}
.downwards-line > li > a .menu-item-text:before {
  bottom: -6px;
  background-color: #ffffff;
}
.top-header .masthead .downwards-line > li > a .menu-item-text:before {
  height: 2px;
}
li:not(.dt-mega-menu) .sub-nav {
  width: 280px;
}
.sub-nav {
  background-color: #000061;
  padding: 10px 10px 10px 10px;
}
.top-header .sub-nav .sub-nav,
.sub-sideways .sub-nav .sub-nav {
  top: -10px;
}
.main-nav .sub-nav > li > a .menu-text {
  font:   500 16px / 20px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.sub-downwards .sub-nav .next-level-button:before,
.side-header.sub-downwards .main-nav.level-arrows-on .sub-nav > li.has-children > a:after {
  line-height: 20px;
}
.main-nav .sub-nav > li:not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.main-nav .sub-nav > li:not(.wpml-ls-item) > a,
#bottom-bar .sub-nav > li:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.sub-nav.level-arrows-on > li.has-children:not(.dt-mega-parent) > a:after,
#bottom-bar .sub-nav li.has-children > a:after {
  background: #ffffff;
}
.main-nav .sub-nav > li > a .subtitle-text {
  font: italic normal 400 10px / 14px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.main-nav .sub-nav > li > a,
.sub-downwards .sub-nav.sub-nav-widgets > li {
  margin: 0px 0px 0px 0px;
  padding: 10px 10px 10px 10px;
}
.top-header .main-nav .sub-nav.level-arrows-on > li.has-children > a {
  padding-right: 18px;
}
.sub-nav li:not(:first-child) .sub-nav {
  top: -10px;
}
.masthead:not(.sub-downwards) .sub-nav li.has-children a:after {
  right: 2px;
}
.top-header .masthead:not(.sub-downwards) .sub-nav li.has-children a:after {
  right: 10px;
}
.rtl .masthead:not(.sub-downwards) .sub-nav li.has-children a:after {
  right: auto;
  left: 10px;
}
.sub-downwards .sub-nav > li > .next-level-button {
  left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
}
.sub-downwards.dt-parent-menu-clickable .sub-nav > li:not(.has-children) > a {
  margin-right: 30px;
}
.masthead:not(.sub-downwards) .main-nav .sub-nav > li:not(.act):not(.dt-mega-parent):not(.no-link):not(.wpml-ls-item):hover > a,
#bottom-bar .sub-nav li:not(.act):not(.dt-mega-parent):not(.no-link):not(.wpml-ls-item):hover > a,
.masthead:not(.sub-downwards) .main-nav .sub-nav li.dt-mega-parent:not(.act):not(.no-link):not(.wpml-ls-item) > a:hover,
.sub-downwards .sub-nav li:not(.act):not(.wpml-ls-item):not(.dt-mega-parent) > a:hover {
  color: #ffffff;
}
.masthead:not(.sub-downwards) .main-nav .sub-nav > li:not(.act):not(.dt-mega-parent):not(.no-link):not(.wpml-ls-item):hover > a .menu-text {
  color: #ffffff;
}
.sub-downwards .sub-nav li:not(.act):not(.wpml-ls-item):not(.dt-mega-parent) > a:hover .menu-text {
  color: #ffffff;
}
.sub-nav.level-arrows-on li.has-children:not(.dt-mega-parent):not(.act):hover > a:after,
#bottom-bar .sub-nav li.has-children:not(.act):hover > a:after {
  background: #ffffff;
}
.top-header .sub-nav.hover-style-bg > li:not(.dt-mega-parent):not(.act):hover > a,
.top-header .sub-nav.gradient-hover.hover-style-bg > li:not(.dt-mega-parent):not(.act):hover > a {
  background-color: rgba(0,0,97,0.07);
}
.top-header .sub-nav.hover-style-bg > li:not(.dt-mega-parent).act > a,
.top-header .sub-nav.gradient-hover.hover-style-bg > li:not(.dt-mega-parent).act > a {
  background-color: rgba(0,0,97,0.07);
}
.main-nav .sub-nav > li.act:not(.dt-mega-parent):not(.wpml-ls-item):not(.wpml-ls-item) > a,
.main-nav .sub-nav > li.act:not(.dt-mega-parent):not(.wpml-ls-item) > a i[class^="fa"],
.main-nav .sub-nav > li.act:not(.dt-mega-parent):not(.wpml-ls-item) > a i[class^="dt-icon"],
.main-nav .sub-nav > li.dt-mega-parent.current-menu-item:not(.wpml-ls-item) > a {
  color: #ffffff;
}
.main-nav .sub-nav > li.act:not(.dt-mega-parent):not(.wpml-ls-item) > a .menu-text {
  color: #ffffff;
}
.sub-nav.level-arrows-on li.has-children:not(.dt-mega-parent).act > a:after,
#bottom-bar .sub-nav li.has-children.act > a:after {
  background: #ffffff;
}
.masthead:not(.sub-downwards) .sub-nav .sub-nav {
  left: 271px !important;
}
.rtl .masthead:not(.sub-downwards) .sub-nav .sub-nav {
  right: 271px;
  left: auto !important;
}
.masthead:not(.sub-downwards) .sub-nav .sub-nav.right-overflow {
  left: -291px !important;
}
.rtl .masthead:not(.sub-downwards) .sub-nav .sub-nav.right-overflow {
  right: -289px;
  left: auto !important;
}
.sub-downwards .sub-nav.level-arrows-on li.has-children > a svg,
.sub-downwards .sub-nav.level-arrows-on li.has-children:not(.act):hover > a svg,
.sub-downwards .sub-nav.level-arrows-on li.has-children.act > a svg,
.sub-downwards .sub-nav .next-level-button {
  fill: rgba(255,255,255,0.27);
  color: rgba(255,255,255,0.27);
}
.sub-downwards .main-nav.level-arrows-on > li.has-children > a {
  max-width: calc(100% - 30px - 14px - 14px);
}
.sub-downwards .sub-nav.level-arrows-on > li.has-children > a {
  max-width: calc(100% - 30px - 0px - 0px);
}
.masthead.masthead-mobile-header,
.masthead.masthead-mobile-header:not(.mixed-header) {
  background: #000061;
}
.mobile-sticky-header-overlay,
.mobile-sticky-sidebar-overlay {
  background-color: rgba(17,17,17,0.5);
}
.dt-mobile-header {
  background-color: #ffffff;
  width: 300px;
}
.right-mobile-menu .show-mobile-header .dt-mobile-header {
  right: 300px;
}
.dt-mobile-header .mobile-header-scrollbar-wrap {
  width: calc(300px - 13px);
  padding: 45px 15px 30px 30px;
  box-sizing: border-box;
}
.admin-bar .dt-mobile-header .mobile-header-scrollbar-wrap {
  padding-top: 77px;
}
@media screen and (max-width: 782px) {
  .admin-bar .dt-mobile-header .mobile-header-scrollbar-wrap {
    padding-top: 91px;
  }
}
html:not(.touchevents) .dt-mobile-header::-webkit-scrollbar-thumb {
  background: rgba(0,0,97,0.2);
  border-color: #ffffff;
}
html:not(.touchevents) .dt-mobile-header::-webkit-scrollbar-track {
  background-color: #ffffff;
}
.masthead:not(.side-header):not(.side-header-menu-icon) .mobile-header-bar {
  margin: 0 auto;
  box-sizing: border-box;
}
.masthead:not(.side-header).full-width .mobile-header-bar,
.mobile-header-bar .masthead.side-header-menu-icon {
  max-width: 100%;
}
.dt-mobile-menu-icon {
  padding: 4px 1px 4px 1px;
}
.dt-mobile-menu-icon .menu-toggle-caption {
  font:    16px / 20px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
  word-spacing: normal;
}
.mobile-left-caption .dt-mobile-menu-icon .menu-toggle-caption {
  margin-right: 10px;
}
.mobile-right-caption .dt-mobile-menu-icon .menu-toggle-caption {
  margin-left: 10px;
}
.dt-mobile-menu-icon:not(.floating-btn) {
  margin: 0px 0px 0px 0px;
  border-radius: 0px;
}
.mobile-menu-icon-bg-on .dt-mobile-menu-icon:not(.floating-btn):before {
  background-color: var(--the7-accent-color);
  background: rgba(17,17,17,0) !important;
}
.mobile-menu-icon-border-enable .dt-mobile-menu-icon:not(.floating-btn):before {
  border: 0px solid var(--the7-accent-color);
}
.mobile-menu-icon-hover-bg-on .dt-mobile-menu-icon:not(.floating-btn):after {
  background-color: var(--the7-accent-color);
  background: rgba(17,17,17,0) !important;
}
.mobile-menu-icon-hover-border-enable .dt-mobile-menu-icon:not(.floating-btn):after {
  border: 0px solid var(--the7-accent-color);
}
.dt-mobile-menu-icon:not(.floating-btn):not(:hover) .menu-toggle-caption {
  color: var(--the7-accent-color);
  color: #ffffff !important;
  background: none;
}
.animate-color-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):not(:hover) .menu-line,
.two-line-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):not(:hover) .menu-line {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.dt-mobile-menu-icon:not(.floating-btn) .menu-line:before {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.dt-mobile-menu-icon:not(.floating-btn) .menu-line:after {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.dt-mobile-menu-icon:not(.floating-btn):hover .menu-toggle-caption {
  color: var(--the7-accent-color);
  color: #ffffff !important;
  background: none;
}
.masthead:not(.animate-color-mobile-menu-icon):not(.two-line-mobile-menu-icon) .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.animate-color-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line,
.two-line-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.animate-color-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line:after,
.dots-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line:after,
.h-dots-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line:after,
.two-line-mobile-menu-icon .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line:after {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.floating-mobile-menu-icon .dt-mobile-menu-icon.floating-btn {
  padding-top: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
}
.floating-mobile-menu-icon .dt-mobile-menu-icon.floating-btn {
  border-radius: 1px;
}
.dt-close-mobile-menu-icon .mobile-menu-close-caption {
  font:    16px / 20px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
  word-spacing: normal;
}
.mobile-close-left-caption .dt-close-mobile-menu-icon .mobile-menu-close-caption {
  margin-right: 10px;
}
.mobile-close-right-caption .dt-close-mobile-menu-icon .mobile-menu-close-caption {
  margin-left: 10px;
}
.dt-close-mobile-menu-icon:not(:hover) .mobile-menu-close-caption {
  color: var(--the7-accent-color);
  color: #ffffff !important;
  background: none;
}
.dt-close-mobile-menu-icon:hover .mobile-menu-close-caption {
  color: var(--the7-accent-color);
  color: #ffffff !important;
  background: none;
}
.left-mobile-menu-close-icon .dt-close-mobile-menu-icon {
  left: 0;
}
.right-mobile-menu-close-icon .dt-close-mobile-menu-icon {
  right: 0;
}
.center-mobile-menu-close-icon .dt-close-mobile-menu-icon {
  left: 50%;
  transform: translateX(-50%);
}
.ouside-mobile-menu-close-icon .dt-close-mobile-menu-icon {
  left: 300px;
}
.right-mobile-menu.ouside-mobile-menu-close-icon .dt-close-mobile-menu-icon {
  right: 300px;
  left: auto;
}
@media screen and (max-width: 300px) {
  .show-mobile-header .dt-close-mobile-menu-icon {
    left: calc(100% - 30px);
  }
  .right-mobile-menu .show-mobile-header .dt-close-mobile-menu-icon {
    left: auto;
    right: 0;
  }
  .right-mobile-menu .show-mobile-header .dt-mobile-header {
    right: 100%;
  }
}
.mobile-branding > a,
.mobile-branding > img {
  padding: 0px 0px 0px 0px;
}
.transparent .masthead:not(.mixed-header) .mobile-branding > a,
.transparent .masthead:not(.mixed-header) .mobile-branding > img {
  padding: 0px 0px 0px 0px;
}
.sticky-mobile-on.masthead:not(.mixed-header) .mobile-branding > a,
.sticky-mobile-on.masthead:not(.mixed-header) .mobile-branding > img {
  padding: 0px 0px 0px 0px;
}
.sticky-mobile-on.masthead.masthead-mobile-header .sticky-mobile-logo-first-switch,
.sticky-mobile-on.masthead.masthead-mobile-header .sticky-mobile-logo-second-switch {
  padding: 0px 0px 0px 0px;
}
.mobile-main-nav > li > a {
  padding: 10.4px 25px 10.4px 0;
}
.mobile-menu-show-divider .mobile-main-nav > li > a {
  padding: 15.2px 25px 15.2px 0;
}
.dt-mobile-header .next-level-button {
  padding: 10.4px 0;
  top: 0;
  line-height: 20px;
}
.mobile-menu-show-divider.dt-mobile-header .next-level-button {
  padding: 15.2px 0;
}
.dt-mobile-header .next-level-button:before {
  line-height: 20px;
}
.mobile-main-nav > li > .sub-nav:last-child {
  margin-bottom: 16px;
}
.mobile-main-nav li > a .menu-text {
  font:   700 16px / 20px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #000061;
  text-transform: none;
}
.mobile-main-nav > .menu-item > a i[class^="fa"],
.mobile-main-nav > .menu-item > a i[class^="dt-icon"] {
  font-size: 16px;
}
.mobile-main-nav li > a {
  color: #000061;
}
.mobile-main-nav li.act > a {
  color: #000061;
}
.mobile-main-nav li.act > a .menu-text,
.mobile-main-nav li.act > a .subtitle-text,
.dt-mobile-header .mobile-main-nav .sub-nav > li.act > a .subtitle-text,
.dt-mobile-header .mobile-main-nav .sub-nav > li.act > a .menu-text {
  color: #000061;
}
.mobile-main-nav li:not(.act) > a:hover .subtitle-text,
.mobile-main-nav li:not(.act) > a:hover .menu-text,
.dt-mobile-header .mobile-main-nav .sub-nav > li:not(.act) > a:hover .menu-text {
  color: #000061;
}
.mobile-main-nav li:not(.act) > a:hover,
.dt-mobile-header .mobile-main-nav .sub-nav > li:not(.act) > a:hover {
  color: #000061;
}
.mobile-main-nav > li.has-children > a:after,
.mobile-main-nav .next-level-button svg,
.mobile-main-nav > li.menu-item-language > a:after {
  color: rgba(0,0,97,0.17);
  fill: rgba(0,0,97,0.17);
}
.mobile-menu-show-divider .mobile-main-nav > li:before {
  border-bottom: 1px solid rgba(51,51,51,0.12);
}
.mobile-main-nav .sub-nav > li > a,
#page .mobile-main-nav .sub-nav li {
  padding: 5.2px 0px 5.2px 0;
}
.dt-mobile-header .mobile-main-nav li .sub-nav > li > a .menu-text {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  color: #000061;
  text-transform: none;
}
.mobile-main-nav .sub-nav > .menu-item > a i[class^="fa"],
.mobile-main-nav .sub-nav > .menu-item > a i[class^="dt-icon"] {
  font-size: 13px;
}
.mobile-main-nav .sub-nav li > a .menu-text,
.mobile-main-nav .sub-nav li > a i[class^="fa"],
.mobile-main-nav .sub-nav li > a i[class^="dt-icon"] {
  font-size: 9px;
  line-height: 13px;
}
.mobile-main-nav .sub-nav.level-arrows-on li.has-children > a:after,
.mobile-main-nav .sub-nav.level-arrows-on li.has-children:not(.act):hover > a:after,
.mobile-main-nav .sub-nav.level-arrows-on li.has-children.act > a:after,
.mobile-main-nav .sub-nav li.has-children a:after,
.mobile-main-nav .sub-nav .next-level-button {
  color: rgba(0,0,97,0.17);
}
.mobile-main-nav .sub-nav .next-level-button {
  padding: 6.5px 0;
  top: 0;
  line-height: 17px;
}
.mobile-main-nav .sub-nav .next-level-button:before {
  line-height: 17px;
}
.mobile-main-nav  > li.has-children > a {
  max-width: calc(100% - 55px);
}
.mobile-main-nav .sub-nav > li.has-children > a {
  max-width: calc(100% - 30px);
}
.dt-close-mobile-menu-icon {
  padding: 5px 5px 5px 5px;
  margin: 15px 0px 0px 0px;
  border-radius: 0px;
}
.mobile-hamburger-close-bg-enable .dt-close-mobile-menu-icon:before {
  background-color: var(--the7-accent-color);
}
.mobile-hamburger-close-border-enable .dt-close-mobile-menu-icon:before {
  border: 0px solid var(--the7-accent-color);
}
.mobile-hamburger-close-bg-hover-enable .dt-close-mobile-menu-icon:after {
  background-color: var(--the7-accent-color);
}
.mobile-hamburger-close-border-hover-enable .dt-close-mobile-menu-icon:after {
  border: 0px solid var(--the7-accent-color);
}
.dt-close-mobile-menu-icon span:after {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.dt-close-mobile-menu-icon span:before {
  background-color: var(--the7-accent-color);
  background: #ffffff !important;
}
.top-bar .sub-nav li.has-children.act:after,
#bottom-bar .sub-nav li.has-children.act:after {
  color: var(--the7-accent-color);
}
.top-bar .sub-nav > ul > li > a,
#bottom-bar .sub-nav > ul > li > a {
  color: #ffffff;
  font:   500 16px / 20px "Overpass", Helvetica, Arial, Verdana, sans-serif;
}
#page .masthead.sticky-on .header-bar,
#phantom.masthead .header-bar,
#phantom .ph-wrap {
  min-height: 70px;
}
#phantom .logo-box,
#phantom .phantom-top-line-logo,
.phantom-sticky .sticky-on.masthead:not(.side-header) .branding > a,
.phantom-sticky .sticky-on.masthead:not(.side-header) .branding > img {
  padding: 0px 50px 0px 0px;
}
.transparent.phantom-sticky .sticky-on.masthead .branding > a,
.transparent.phantom-sticky .sticky-on.masthead .branding > img {
  padding: 0px 50px 0px 0px;
}
#phantom {
  background: #000061 none repeat center center;
  background-size: auto;
}
.phantom-shadow-decoration #phantom {
  box-shadow: 0 0 15px 1px rgba(0,0,0,0.07);
}
#phantom.boxed {
  max-width: 1340px;
}
.phantom-line-decoration #phantom {
  border-bottom: 2px solid rgba(255,255,255,0.15);
  box-shadow: none !important;
}
.phantom-content-width-line-decoration #phantom .ph-wrap {
  position: relative;
  box-shadow: none !important;
}
.phantom-content-width-line-decoration #phantom .ph-wrap:after {
  position: absolute;
  bottom: 0;
  left: auto;
  right: auto;
  content: "";
  width: calc(100% - 20px - 20px);
  height: 2px;
  background: rgba(255,255,255,0.15);
}
.phantom-shadow-decoration #phantom {
  box-shadow: 0 0 15px 1px rgba(0,0,0,0.07);
  border-bottom: none;
}
.phantom-disable-decoration #phantom {
  box-shadow: none !important;
  border-bottom: none;
}
.responsive-off.phantom-sticky .sticky-on.masthead:not(.masthead-mobile) {
  background: #000061 !important;
}
.responsive-off.phantom-line-decoration.phantom-sticky .sticky-on.masthead {
  border-bottom: 2px solid rgba(255,255,255,0.15);
  box-shadow: none !important;
}
.masthead:not(.side-header):not(.side-header-v-stroke):not(.side-header-menu-icon) .header-bar,
.ph-wrap {
  padding: 0 20px 0 20px;
}
.top-header .mega-full-width > .dt-mega-menu-wrap {
  width: calc(1300px - 20px - 20px);
}
.boxed .masthead:not(.full-width):not(.side-header):not(.side-header-menu-icon):not(.side-header-v-stroke) .header-bar,
.boxed .ph-wrap {
  max-width: 100%;
  width: calc(1300px - 20px - 20px);
}
#phantom .ph-wrap .header-bar {
  padding: 0;
}
.boxed .masthead:not(.width-in-pixel):not(#phantom) .top-bar.full-width-line:after,
.boxed .masthead:not(.width-in-pixel):not(.sticky-on) .top-bar-bg,
.boxed.masthead:not(.width-in-pixel):not(#phantom) .top-bar-bg,
.boxed .classic-header:not(.width-in-pixel) .navigation:before {
  margin: 0 -20px 0 -20px;
  padding: 0 1000px;
}
.ph-wrap,
#phantom .ph-wrap.boxed,
.boxed .top-bar.line-content:before,
.boxed .classic-header.content-width-line .navigation:before {
  max-width: calc(1300px - 20px - 20px);
}
.masthead:not(.mixed-header) {
  background: #000061 none repeat center center;
  background-size: auto;
}
.masthead.line-decoration:not(.side-header):not(.mixed-header):not(.masthead-mobile-header) {
  border-bottom: 1px solid rgba(51,51,51,0.11);
}
.header-side-left .masthead.line-decoration:not(.mixed-header):not(.masthead-mobile-header) {
  border-right: 1px solid rgba(51,51,51,0.11);
}
.header-side-right .masthead.line-decoration:not(.mixed-header):not(.masthead-mobile-header) {
  border-left: 1px solid rgba(51,51,51,0.11);
}
.masthead.content-width-line-decoration:not(.mixed-header):not(.side-header):not(.masthead-mobile-header):not(#phantom) .header-bar {
  padding-bottom: 1px;
}
.masthead.content-width-line-decoration:not(.mixed-header):not(.side-header):not(.masthead-mobile-header):not(#phantom) .header-bar:after {
  position: absolute;
  bottom: 0;
  left: auto;
  right: auto;
  content: "";
  width: calc(100% - 20px - 20px);
  height: 1px;
  background: rgba(51,51,51,0.11);
}
.header-side-left .masthead.content-width-line-decoration:not(.mixed-header):not(.masthead-mobile-header) .header-bar:after {
  position: absolute;
  content: "";
  right: 0;
  bottom: auto;
  top: auto;
  height: calc(100% - 0px - 0px);
  width: 1px;
  background: rgba(51,51,51,0.11);
}
.header-side-right .masthead.content-width-line-decoration:not(.mixed-header):not(.masthead-mobile-header) .header-bar:after {
  position: absolute;
  content: "";
  left: 0;
  bottom: auto;
  top: auto;
  height: calc(100% - 0px - 0px);
  width: 1px;
  background: rgba(51,51,51,0.11);
}
.transparent .masthead:not(.side-header),
.overlay-navigation .masthead.shadow-decoration:not(.mixed-header) {
  box-shadow: none;
}
.masthead.shadow-decoration:not(.side-header-menu-icon):not(#phantom):not(.masthead-mobile-header) {
  box-shadow: 0 0 15px 1px rgba(0,0,0,0.07);
}
.transparent .masthead:not(.side-header) {
  background: rgba(0,0,0,0.3);
}
.transparent .top-bar-bg {
  background: rgba(255,255,255,0);
}
#main-slideshow:not(.rv-slider):not(.empty-slider) {
  min-height: 70px;
}
#main-slideshow.fixed,
#main-slideshow.fix,
#main-slideshow.layer-fixed {
  background-color: rgba(82,82,87,0.08);
}
#main-slideshow.fixed > .royalSlider,
#main-slideshow.fixed .ts-wrap {
  max-width: 1212px;
}
.outline-element-decoration #main-slideshow.fixed:after,
.outline-element-decoration #main-slideshow.layer-fixed:after,
.outline-element-decoration #main-slideshow.fix:after {
  background-color: rgba(82,82,87,0.15);
}
.page-title .wf-wrap {
  min-height: 100px;
  padding-top: 25px;
  padding-bottom: 20px;
}
.page-title .wf-wrap:after {
  border-bottom: 5px dashed rgba(51,51,51,0.11);
}
.page-title.solid-bg {
  background-color: #000061;
}
.page-title.solid-bg.bg-img-enabled {
  background-image: none;
  background-repeat: repeat;
  background-position: center center;
  background-size: auto auto;
}
.page-title.overlay-bg:after {
  background: rgba(0,0,0,0.5);
}
.page-title.gradient-bg {
  background: #000061;
}
.page-title.title-outline-decoration {
  border-bottom: 1px solid rgba(30,115,190,0);
}
.page-title > .wf-wrap,
#fancy-header > .wf-wrap {
  width: 1300px;
}
.page-title h1,
.page-title h1 *,
.page-title h1 a:hover,
#page .page-title .entry-title {
  font:  normal 700 54px / 64px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  color: #ffffff;
  text-transform: none;
}
.page-title .breadcrumbs li:before,
.page-title .breadcrumbs li:before {
  color: rgba(82,82,87,0.35);
}
.page-title.breadcrumbs-bg .breadcrumbs {
  background: rgba(255,255,255,0.2);
  border-radius: 2px;
  border: 0px solid rgba(130,36,227,0.2);
}
.page-title .breadcrumbs {
  padding: 0px 0px 0px 0px;
  margin: 8px 0px 0px 0px;
}
.page-title .breadcrumbs,
.page-title .breadcrumbs a {
  color: #f3615a;
  font: italic normal 400 14px / 24px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.page-title .breadcrumbs li:before,
.page-title .breadcrumbs li:before {
  color: rgba(243,97,90,0.5);
}
.fancy-header h1,
.fancy-header h2,
.fancy-header .fancy-subtitle {
  font:  normal 700 54px / 64px "Roboto", Helvetica, Arial, Verdana, sans-serif;
}
#page .fancy-header .color-accent span {
  color: var(--the7-accent-color);
}
.dt-breadcrumbs-shortcode .breadcrumbs * {
  color: var(--the7-secondary-text-color);
}
.responsive-off #main:not(.sidebar-none) .wf-container-main {
  grid-column-gap: 50px;
}
.responsive-off .sidebar-right .wf-container-main {
  -ms-grid-columns: calc(100% - 350px - 25px) 50px calc(350px - 25px);
  grid-template-columns: calc(100% - 350px - 25px) calc(350px - 25px);
}
.responsive-off .sidebar-left .wf-container-main {
  -ms-grid-columns: calc(350px - 25px) 50px calc(100% - 350px - 25px);
  grid-template-columns: calc(350px - 25px) calc(100% - 350px - 25px);
}
.responsive-off .sidebar-divider-vertical.sidebar-left .sidebar {
  padding-right: 50px;
}
.responsive-off .sidebar-divider-vertical.sidebar-right .sidebar {
  padding-left: 50px;
}
.sidebar-right .sidebar {
  border-left: 1px solid var(--the7-divider-color);
}
.sidebar-left .sidebar {
  border-right: 1px solid var(--the7-divider-color);
}
.solid-bg .sidebar-content {
  background: #f7f7f8 none repeat center center;
}
.solid-bg.sidebar-outline-decoration .sidebar-content,
.bg-under-widget.sidebar-outline-decoration .widget {
  border: 1px solid rgba(0,0,0,0.06);
}
.bg-under-widget .sidebar-content .widget,
.solid-bg.sidebar-content .widget {
  background: #f7f7f8 none repeat center center;
}
.solid-bg.sidebar-content.sidebar-outline-decoration .widget {
  border: 1px solid rgba(0,0,0,0.06);
}
.solid-bg.sidebar-shadow-decoration .sidebar-content,
.bg-under-widget.sidebar-shadow-decoration .sidebar-content .widget,
.solid-bg.sidebar-content.sidebar-shadow-decoration .widget {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.sidebar .widget,
.sidebar-content .widget,
.elementor-widget-sidebar .widget {
  border-top: 1px solid var(--the7-divider-color);
  color: #85868c;
  padding-top: 30px;
  padding-bottom: 30px;
}
.sidebar .widget,
.widget_product_categories li a,
.widget .st-accordion li > a,
ul.cart_list li a,
ul.product_list_widget li a,
.sidebar-content .widget,
.widget .testimonial-vcard .text-primary,
.widget .testimonial-vcard .text-secondary,
div[class*='elementor-widget-wp-widget-'],
.elementor-widget-sidebar .widget,
.elementor-widget-wp-widget-woocommerce_recently_viewed_products,
.elementor-widget-wp-widget-woocommerce_recent_reviews,
.elementor-widget-wp-widget-woocommerce_product_categories,
.elementor-widget-wp-widget-woocommerce_top_rated_products,
.elementor-widget-wp-widget-woocommerce_widget_cart,
.elementor-widget-wp-widget-woocommerce_price_filter,
.elementor-widget-wp-widget-woocommerce_layered_nav,
.elementor-widget-wp-widget-woocommerce_products,
.elementor-widget-woocommerce-products,
.elementor-widget-wp-widget-woocommerce_layered_nav_filters,
.elementor-widget-wp-widget-woocommerce_rating_filter,
.the7-product-filter {
  font: var(--the7-widget-content-font);
  text-transform: var(--the7-widget-content-text-transform);
  letter-spacing: var(--the7-widget-content-letter-spacing);
  text-decoration: var(--the7-widget-content-text-decoration);
}
.sidebar.bg-under-widget .widget,
.bg-under-widget .sidebar-content .widget,
.solid-bg.sidebar-content .widget,
.mec-wrap .col-md-4 .widget {
  margin-top: 60px;
}
.widget-title,
div[class*='elementor-widget-wp-widget-'] h5,
.elementor-widget .woocommerce h5 {
  font: var(--the7-widget-title-font);
  text-transform: var(--the7-widget-title-text-transform);
  letter-spacing: var(--the7-widget-title-letter-spacing);
  text-decoration: var(--the7-widget-title-text-decoration);
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget div[class*='elementor-widget-wp-widget-'] h5,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .elementor-widget .woocommerce h5 {
  color: #ffffff;
}
.sidebar-content .widget-title,
.sidebar-content div[class*='elementor-widget-wp-widget-'] h5,
.sidebar-content .elementor-widget .woocommerce h5 {
  color: #333333;
}
.footer .widget-title,
.footer div[class*='elementor-widget-wp-widget-'] h5,
.footer .elementor-widget .woocommerce h5 {
  color: #ffffff;
}
.widget-title,
body .elementor-widget .woocommerce h5 {
  margin-bottom: var(--the7-widget-gap);
}
.widgettitle {
  font: var(--the7-h4-font);
  text-transform: var(--the7-h4-text-transform);
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widgettitle {
  color: #ffffff;
}
.sidebar-content .widgettitle {
  color: #333333;
}
.footer .widgettitle {
  color: #ffffff;
}
.sidebar-content .widget:not(.widget_icl_lang_sel_widget) a:not(.elementor-button),
.sidebar-content.solid-bg .widget:not(.widget_icl_lang_sel_widget) a:not(.elementor-button) * {
  color: #85868c;
}
.sidebar-content .widget .post-content .text-secondary {
  color: rgba(133,134,140,0.5);
}
.footer .widget a,
.footer .widget .post-content .text-secondary {
  color: #ffffff;
}
.footer .widget .post-content .text-secondary {
  color: rgba(255,255,255,0.5);
}
.widget_categories li a,
.widget_meta a,
.blogroll a,
.widget_archive li a,
.widget_recent_entries a,
.widget_recent_comments a,
.widget_links a,
.items-grid a,
.recent-posts a,
.widget:not(.widget_icl_lang_sel_widget) .recent-posts a,
.contact-info .secondary-color,
.widget_nav_menu a,
.tagcloud a,
.widget_pages a,
a.rsswidget,
.textwidget a {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_categories li a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_meta a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .blogroll a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_archive li a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_recent_entries a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_recent_comments a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_links a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .items-grid a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .recent-posts a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget:not(.widget_icl_lang_sel_widget) .recent-posts a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .contact-info .secondary-color,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_nav_menu a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .tagcloud a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .widget_pages a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget a.rsswidget,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .textwidget a {
  color: #ffffff;
}
.sidebar-content .widget_categories li a,
.sidebar-content .widget_meta a,
.sidebar-content .blogroll a,
.sidebar-content .widget_archive li a,
.sidebar-content .widget_recent_entries a,
.sidebar-content .widget_recent_comments a,
.sidebar-content .widget_links a,
.sidebar-content .items-grid a,
.sidebar-content .recent-posts a,
.sidebar-content .widget:not(.widget_icl_lang_sel_widget) .recent-posts a,
.sidebar-content .contact-info .secondary-color,
.sidebar-content .widget_nav_menu a,
.sidebar-content .tagcloud a,
.sidebar-content .widget_pages a,
.sidebar-content a.rsswidget,
.sidebar-content .textwidget a {
  color: #333333;
}
.footer .widget_categories li a,
.footer .widget_meta a,
.footer .blogroll a,
.footer .widget_archive li a,
.footer .widget_recent_entries a,
.footer .widget_recent_comments a,
.footer .widget_links a,
.footer .items-grid a,
.footer .recent-posts a,
.footer .widget:not(.widget_icl_lang_sel_widget) .recent-posts a,
.footer .contact-info .secondary-color,
.footer .widget_nav_menu a,
.footer .tagcloud a,
.footer .widget_pages a,
.footer a.rsswidget,
.footer .textwidget a {
  color: #ffffff;
}
.widget_categories li,
.widget_archive li {
  color: rgba(133,134,140,0.5);
}
.sidebar-content .custom-categories a,
.sidebar-content .cat-item a,
.sidebar-content .widget_recent_comments a,
.sidebar-content .widget_tag_cloud a:hover {
  color: #333333;
}
.footer .custom-categories a,
.footer .cat-item a,
.footer .widget_recent_comments a,
.footer .widget_tag_cloud a:hover {
  color: #ffffff;
}
.sidebar-content .custom-categories a span.item-num,
.sidebar-content .cat-item a span.item-num {
  color: rgba(133,134,140,0.5);
}
.footer .custom-categories a span.item-num,
.footer .cat-item a span.item-num {
  color: rgba(255,255,255,0.5);
}
.sidebar-content .custom-categories a:hover span.item-name,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .custom-categories a:hover span.item-name,
.sidebar-content .cat-item a:hover span.item-name,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .cat-item a:hover span.item-name,
.sidebar-content .custom-menu a:hover,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .custom-menu a:hover,
.sidebar-content .custom-menu li.act > a,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .custom-menu li.act > a,
.sidebar-content .widget_recent_comments a:hover,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .widget_recent_comments a:hover {
  color: var(--the7-accent-color);
}
.footer .custom-categories a:hover span.item-name,
.footer .cat-item a:hover span.item-name,
.footer .custom-menu a:hover,
.footer .custom-menu li.act > a,
.footer .widget_recent_comments a:hover {
  color: var(--the7-accent-color);
}
.sidebar-content .custom-menu a:hover svg,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .custom-menu a:hover svg,
.sidebar-content .custom-menu li.act > a svg,
.sidebar-content:not(.solid-bg) .widget:not(.widget_icl_lang_sel_widget) .custom-menu li.act > a svg {
  fill: var(--the7-accent-color);
  color: var(--the7-accent-color);
}
.footer .custom-menu a:hover svg,
.footer .custom-menu li.act > a svg {
  fill: var(--the7-accent-color);
  color: var(--the7-accent-color);
}
.widget .menu li,
.widget_pages li,
.widget .custom-menu.dividers-on li,
div[class*='elementor-widget-wp-widget-'] .custom-menu.dividers-on li,
div[class*='elementor-widget-wp-widget-'] .menu li,
.widget .blogroll li,
.widget .qts-lang-menu li {
  border-style: solid;
  border-width: 1px;
  border-color: var(--the7-divider-color);
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.sidebar-content .widget .menu li,
.sidebar-content .widget .custom-menu.dividers-on li,
.sidebar-content .widget_pages li,
.sidebar-content .blogroll li,
.sidebar-content .widget .qts-lang-menu li {
  border-style: solid;
  border-width: 1px;
  border-color: rgba(133,134,140,0.15);
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.footer .menu li,
.footer .widget_pages li,
.footer .custom-menu.dividers-on li,
.footer .blogroll li,
.footer .qts-lang-menu li {
  border-style: solid;
  border-width: 1px;
  border-color: rgba(255,255,255,0.15);
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.menu .current-menu-parent > a,
.menu .current-menu-item > a,
.custom-nav > li > a:hover span,
.custom-nav li.act > a span,
.custom-nav > li > ul a:hover span,
.st-accordion li > a:hover,
.st-accordion > ul li > a:hover *,
.widget .custom-categories a:hover span.item-name,
.widget_categories li a:hover,
.widget_categories li a:not(.class-1):hover,
.widget_meta a:hover,
.blogroll a:hover,
.widget_archive li a:hover,
.widget_archive li a:not(.class-1):hover,
.widget_recent_entries a:hover,
.widget_recent_entries a:not(.class-1):hover,
.widget_links a:hover,
.widget_pages a:hover,
.recent-posts a:not(.post-rollover):hover,
.items-grid .post-content > a:hover,
#wp-calendar td a:hover,
.tagcloud a:hover,
.tagcloud a:not(.class-1):hover,
.widget_nav_menu a:hover,
a.rsswidget:hover {
  color: var(--the7-accent-color);
}
.sidebar-content .menu .current-menu-parent > a,
.sidebar-content.solid-bg .menu .current-menu-parent > a,
.sidebar-content .menu .current-menu-item > a,
.sidebar-content.solid-bg .menu .current-menu-item > a,
.sidebar-content .custom-nav > li > a:hover span,
.sidebar-content.solid-bg .custom-nav > li > a:hover span,
.sidebar-content .custom-nav li.act > a span,
.sidebar-content.solid-bg .custom-nav li.act > a span,
.sidebar-content .custom-nav > li > ul a:hover span,
.sidebar-content.solid-bg .custom-nav > li > ul a:hover span,
.sidebar-content .st-accordion li > a:hover,
.sidebar-content.solid-bg .st-accordion li > a:hover,
.sidebar-content .st-accordion > ul li > a:hover *,
.sidebar-content.solid-bg .st-accordion > ul li > a:hover *,
.sidebar-content .widget .custom-categories a:hover span.item-name,
.sidebar-content.solid-bg .widget .custom-categories a:hover span.item-name,
.sidebar-content .widget_categories li a:hover,
.sidebar-content.solid-bg .widget_categories li a:hover,
.sidebar-content .widget_categories li a:not(.class-1):hover,
.sidebar-content.solid-bg .widget_categories li a:not(.class-1):hover,
.sidebar-content .widget_meta a:hover,
.sidebar-content.solid-bg .widget_meta a:hover,
.sidebar-content .blogroll a:hover,
.sidebar-content.solid-bg .blogroll a:hover,
.sidebar-content .widget_archive li a:hover,
.sidebar-content.solid-bg .widget_archive li a:hover,
.sidebar-content .widget_archive li a:not(.class-1):hover,
.sidebar-content.solid-bg .widget_archive li a:not(.class-1):hover,
.sidebar-content .widget_recent_entries a:hover,
.sidebar-content.solid-bg .widget_recent_entries a:hover,
.sidebar-content .widget_recent_entries a:not(.class-1):hover,
.sidebar-content.solid-bg .widget_recent_entries a:not(.class-1):hover,
.sidebar-content .widget_links a:hover,
.sidebar-content.solid-bg .widget_links a:hover,
.sidebar-content .widget_pages a:hover,
.sidebar-content.solid-bg .widget_pages a:hover,
.sidebar-content .recent-posts a:not(.post-rollover):hover,
.sidebar-content.solid-bg .recent-posts a:not(.post-rollover):hover,
.sidebar-content .items-grid .post-content > a:hover,
.sidebar-content.solid-bg .items-grid .post-content > a:hover,
.sidebar-content #wp-calendar td a:hover,
.sidebar-content.solid-bg #wp-calendar td a:hover,
.sidebar-content .tagcloud a:hover,
.sidebar-content.solid-bg .tagcloud a:hover,
.sidebar-content .tagcloud a:not(.class-1):hover,
.sidebar-content.solid-bg .tagcloud a:not(.class-1):hover,
.sidebar-content .widget_nav_menu a:hover,
.sidebar-content.solid-bg .widget_nav_menu a:hover,
.sidebar-content a.rsswidget:hover,
.sidebar-content.solid-bg a.rsswidget:hover {
  color: var(--the7-accent-color);
}
.footer .menu .current-menu-parent > a,
.footer .menu .current-menu-item > a,
.footer .custom-nav > li > a:hover span,
.footer .custom-nav li.act > a span,
.footer .custom-nav > li > ul a:hover span,
.footer .st-accordion li > a:hover,
.footer .st-accordion > ul li > a:hover *,
.footer .widget .custom-categories a:hover span.item-name,
.footer .widget_categories li a:hover,
.footer .widget_categories li a:not(.class-1):hover,
.footer .widget_meta a:hover,
.footer .blogroll a:hover,
.footer .widget_archive li a:hover,
.footer .widget_archive li a:not(.class-1):hover,
.footer .widget_recent_entries a:hover,
.footer .widget_recent_entries a:not(.class-1):hover,
.footer .widget_links a:hover,
.footer .widget_pages a:hover,
.footer .recent-posts a:not(.post-rollover):hover,
.footer .items-grid .post-content > a:hover,
.footer #wp-calendar td a:hover,
.footer .tagcloud a:hover,
.footer .tagcloud a:not(.class-1):hover,
.footer .widget_nav_menu a:hover,
.footer a.rsswidget:hover {
  color: var(--the7-accent-color);
}
.textwidget a:not(.dt-btn):hover {
  color: var(--the7-accent-color);
}
.sidebar-content .textwidget a:not(.dt-btn):hover,
.stripe .sidebar-content.solid-bg .textwidget a:not(.dt-btn):hover {
  color: var(--the7-accent-color);
}
.footer .textwidget a:not(.dt-btn):hover {
  color: var(--the7-accent-color);
}
#wp-calendar,
#wp-calendar td {
  background-color: rgba(0,0,97,0.04);
}
.sidebar-content #wp-calendar,
.sidebar-content #wp-calendar td {
  background-color: rgba(51,51,51,0.04);
}
#wp-calendar,
#wp-calendar caption,
#wp-calendar td {
  border-color: var(--the7-divider-color);
}
.sidebar-content #wp-calendar,
.sidebar-content #wp-calendar caption,
.sidebar-content #wp-calendar td {
  border-color: rgba(133,134,140,0.15);
}
#wp-calendar td:hover {
  background-color: rgba(0,0,97,0.08);
}
.sidebar-content #wp-calendar td:hover {
  background-color: rgba(51,51,51,0.08);
}
.footer #wp-calendar,
.footer #wp-calendar td {
  background-color: rgba(255,255,255,0.04);
}
.footer #wp-calendar td:hover {
  background-color: rgba(255,255,255,0.08);
}
.footer #wp-calendar,
.footer #wp-calendar caption,
.footer #wp-calendar td {
  border-color: rgba(255,255,255,0.15);
}
#wp-calendar td.act {
  color: #fff;
  background-color: var(--the7-accent-color);
}
#wp-calendar th,
#wp-calendar caption,
#wp-calendar tfoot td,
#wp-calendar tfoot td a {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget #wp-calendar th,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget #wp-calendar caption,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget #wp-calendar tfoot td,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget #wp-calendar tfoot td a {
  color: #ffffff;
}
.sidebar-content #wp-calendar th,
.sidebar-content #wp-calendar caption,
.sidebar-content #wp-calendar tfoot td,
.sidebar-content #wp-calendar tfoot td a {
  color: #333333;
}
.footer #wp-calendar th,
.footer #wp-calendar caption,
.footer #wp-calendar tfoot td,
.footer #wp-calendar tfoot td a {
  color: #ffffff;
}
#page .widget .searchform .submit svg,
#page .content .searchform .submit svg {
  fill: var(--the7-input-color);
  color: var(--the7-input-color);
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 0;
  border-color: var(--the7-input-border-color);
  background-color: var(--the7-input-bg-color);
  border-radius: var(--the7-input-border-radius);
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input {
  background: none;
  margin: 0;
}
.wp-block-search .wp-block-search__button {
  height: var(--the7-input-height);
}
.wp-block-search .wp-block-search__label {
  display: block;
  color: #000061;
  margin-bottom: 8px;
  font:   700 16px / 26px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.widget_recent_comments a:hover {
  color: var(--the7-accent-color) !important;
}
.custom-menu a svg {
  fill: var(--the7-secondary-text-color);
  color: var(--the7-secondary-text-color);
}
.sidebar-content .custom-menu a svg {
  fill: rgba(133,134,140,0.5);
  color: rgba(133,134,140,0.5);
}
.footer .custom-menu a svg {
  fill: rgba(255,255,255,0.5);
  color: rgba(255,255,255,0.5);
}
.custom-nav a svg {
  fill: #85868c;
  color: #85868c;
}
.footer .custom-nav a svg {
  fill: #ffffff;
  color: #ffffff;
}
.custom-menu a:hover svg,
.custom-menu .act > a svg {
  fill: var(--the7-secondary-text-color);
  color: var(--the7-secondary-text-color);
}
.sidebar-content .custom-menu a:hover svg,
.sidebar-content .custom-menu .act > a svg {
  fill: rgba(0,0,97,0.999);
  color: rgba(0,0,97,0.999);
}
.footer .custom-menu a:hover svg,
.footer .custom-menu .act > a svg {
  fill: rgba(0,0,97,0.999);
  color: rgba(0,0,97,0.999);
}
.sidebar .skill,
.sidebar-content .skill {
  background-color: rgba(133,134,140,0.15);
}
.footer .skill {
  background-color: rgba(255,255,255,0.15);
}
.skill-value {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.skill-name,
.skill-name *,
.custom-nav > li > a span,
.custom-nav > li > ul a span,
.st-accordion li a,
.st-accordion > ul li > a * {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .skill-name,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .skill-name *,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .custom-nav > li > a span,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .custom-nav > li > ul a span,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .st-accordion li a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .st-accordion > ul li > a * {
  color: #ffffff;
}
.sidebar-content .skill-name,
.sidebar-content .skill-name *,
.sidebar-content .custom-nav > li > a span,
.sidebar-content .custom-nav > li > ul a span,
.sidebar-content .st-accordion li a,
.sidebar-content .st-accordion > ul li > a * {
  color: #333333;
}
.footer .skill-name,
.footer .skill-name *,
.footer .custom-nav > li > a span,
.footer .custom-nav > li > ul a span,
.footer .st-accordion li a,
.footer .st-accordion > ul li > a * {
  color: #ffffff;
}
.st-accordion li > a {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.custom-menu.show-arrow a:after {
  top: calc(var(--the7-text-big-line-height) - 13 / 2);
}
.footer .custom-menu.show-arrow a:after {
  top: calc(var(--the7-base-line-height) - 13 / 2);
}
.custom-menu a:not(:hover) {
  color: var(--the7-base-color);
}
.dt-mega-menu .sidebar-content .custom-menu a:not(:hover) {
  color: #ffffff;
}
.sidebar-content .custom-menu a:not(:hover) {
  color: #85868c;
}
.footer .custom-menu a:not(:hover) {
  color: #ffffff;
}
.widget_presscore-custom-menu-one .custom-menu.show-arrow a:after {
  background-color: var(--the7-base-color);
}
.dt-mega-menu .sidebar-content .widget_presscore-custom-menu-one .custom-menu.show-arrow a:after {
  background-color: #ffffff;
}
.sidebar-content .widget_presscore-custom-menu-one .custom-menu.show-arrow a:after {
  background-color: #85868c;
}
.footer .widget_presscore-custom-menu-one .custom-menu.show-arrow a:after {
  background-color: #ffffff;
}
.sidebar .st-accordion li > a:before,
.sidebar-content .st-accordion li > a:before,
.sidebar .custom-menu a:before,
.sidebar-content .custom-menu a:before {
  background-color: rgba(133,134,140,0.15);
}
.footer .st-accordion li > a:before,
.footer .custom-menu a:before {
  background-color: rgba(255,255,255,0.15);
}
.st-accordion li > a:after {
  color: rgba(133,134,140,0.5);
}
.footer .st-accordion li > a:after {
  color: rgba(255,255,255,0.5);
}
.st-accordion li > a:hover:after {
  color: var(--the7-accent-color);
}
.dt-posts-preload .dt-posts-preload-activity {
  background-color: rgba(0,0,97,0.9);
}
.format-link-content,
.format-aside-content,
.format-status-content {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .format-link-content {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .format-link-content {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.post.bg-on,
.post.bg-on.fullwidth-img.format-quote,
.post.bg-on.fullwidth-img.format-link {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .post.bg-on,
.outline-element-decoration .post.bg-on.fullwidth-img.format-quote,
.outline-element-decoration .post.bg-on.fullwidth-img.format-link {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .post.bg-on,
.shadow-element-decoration .post.bg-on.fullwidth-img.format-quote,
.shadow-element-decoration .post.bg-on.fullwidth-img.format-link {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.layout-list .post {
  border-top: 1px solid;
  border-color: var(--the7-divider-color);
}
.sidebar .layout-list .post,
.sidebar-content .layout-list .post {
  border-color: rgba(133,134,140,0.15);
}
.footer .layout-list .post {
  border-color: rgba(255,255,255,0.15);
}
.dividers-on.classic-layout-list .post {
  border-bottom: 1px solid;
  border-color: var(--the7-divider-color);
}
.sidebar .dividers-on.classic-layout-list .post,
.sidebar-content .dividers-on.classic-layout-list .post {
  border-color: rgba(133,134,140,0.15);
}
.footer .dividers-on.classic-layout-list .post {
  border-color: rgba(255,255,255,0.15);
}
.content-bg-on.centered-layout-list article,
.content-bg-on.classic-layout-list article {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .content-bg-on.centered-layout-list article,
.outline-element-decoration .content-bg-on.classic-layout-list article {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .content-bg-on.centered-layout-list article,
.shadow-element-decoration .content-bg-on.classic-layout-list article {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.centered-layout-list.dividers-on .post-entry-content:after {
  background-color: rgba(82,82,87,0.15);
}
.sidebar .centered-layout-list.dividers-on .post-entry-content:after,
.sidebar-content .centered-layout-list.dividers-on .post-entry-content:after {
  background-color: rgba(133,134,140,0.15);
}
.footer .centered-layout-list.dividers-on .post-entry-content:after {
  background-color: rgba(255,255,255,0.15);
}
.content-bg-on:not(.classic-layout-list):not(.centered-layout-list):not(.gradient-overlay-layout-list) .post-entry-content {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .content-bg-on:not(.classic-layout-list):not(.centered-layout-list):not(.gradient-overlap-layout-list):not(.gradient-overlay-layout-list):not(.content-rollover-layout-list) .post-entry-content {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .content-bg-on:not(.classic-layout-list):not(.centered-layout-list):not(.gradient-overlap-layout-list):not(.gradient-overlay-layout-list):not(.content-rollover-layout-list) .post-entry-content {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.content-bg-on.content-rollover-layout-list .post-entry-content:before {
  background: #f7f7f7;
  background: -moz-linear-gradient(top,rgba(247,247,247,0) 0%,#f7f7f7 65%,#f7f7f7 100%);
  background: -webkit-linear-gradient(top,rgba(247,247,247,0) 0%,#f7f7f7 65%,#f7f7f7 100%);
  background: linear-gradient(to bottom,rgba(247,247,247,0) 0%,#f7f7f7 65%,#f7f7f7 100%);
}
#page .content-bg-on.gradient-overlap-layout-list .post-entry-content {
  background: linear-gradient(to bottom,#f7f7f7,#f7f7f7) no-repeat 0px 150px;
}
.content-bg-on.gradient-overlap-layout-list .post-entry-content:before {
  background: #f7f7f7;
  background: -moz-linear-gradient(top,rgba(247,247,247,0) 0%,rgba(247,247,247,0.9) 64%,#f7f7f7 83%,#f7f7f7 100%);
  background: -webkit-linear-gradient(top,rgba(247,247,247,0) 0%,rgba(247,247,247,0.9) 64%,#f7f7f7 83%,#f7f7f7 100%);
  background: linear-gradient(to bottom,rgba(247,247,247,0) 0%,rgba(247,247,247,0.9) 64%,#f7f7f7 83%,#f7f7f7 100%);
}
#page .content-bg-on.gradient-overlap-layout-list .no-img .post-entry-content {
  background: var(--the7-content-boxes-bg);
}
.content-bg-on.gradient-overlap-layout-list .no-img .post-entry-content:before {
  display: none;
}
#page .enable-bg-rollover.gradient-overlay-layout-list .post-entry-content {
  background-color: rgba(38,6,51,0.8);
  background: rgba(38,6,51,0.8);
  background: -webkit-linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
  background: linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
}
.blog-shortcode .entry-title,
.portfolio-shortcode .entry-title,
.albums-shortcode .entry-title,
.owl-carousel.blog-carousel-shortcode .entry-title {
  font: normal normal normal var(--the7-h3-font-size) / var(--the7-h3-line-height) var(--the7-h3-font-family);
}
.mode-masonry.blog-shortcode .entry-title,
.mode-grid.blog-shortcode .entry-title,
.blog-carousel-shortcode.blog-shortcode .entry-title,
.portfolio-carousel-shortcode.blog-shortcode .entry-title,
.albums-carousel-shortcode.blog-shortcode .entry-title,
.mode-masonry.portfolio-shortcode .entry-title,
.mode-grid.portfolio-shortcode .entry-title,
.blog-carousel-shortcode.portfolio-shortcode .entry-title,
.portfolio-carousel-shortcode.portfolio-shortcode .entry-title,
.albums-carousel-shortcode.portfolio-shortcode .entry-title,
.mode-masonry.albums-shortcode .entry-title,
.mode-grid.albums-shortcode .entry-title,
.blog-carousel-shortcode.albums-shortcode .entry-title,
.portfolio-carousel-shortcode.albums-shortcode .entry-title,
.albums-carousel-shortcode.albums-shortcode .entry-title,
.mode-masonry.owl-carousel.blog-carousel-shortcode .entry-title,
.mode-grid.owl-carousel.blog-carousel-shortcode .entry-title,
.blog-carousel-shortcode.owl-carousel.blog-carousel-shortcode .entry-title,
.portfolio-carousel-shortcode.owl-carousel.blog-carousel-shortcode .entry-title,
.albums-carousel-shortcode.owl-carousel.blog-carousel-shortcode .entry-title {
  font: normal normal normal var(--the7-h4-font-size) / var(--the7-h4-line-height) var(--the7-h4-font-family);
}
.blog-shortcode .entry-title a,
.portfolio-shortcode .entry-title a,
.albums-shortcode .entry-title a,
.owl-carousel.blog-carousel-shortcode .entry-title a {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .blog-shortcode .entry-title a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .portfolio-shortcode .entry-title a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .albums-shortcode .entry-title a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .owl-carousel.blog-carousel-shortcode .entry-title a {
  color: #ffffff;
}
.sidebar-content .blog-shortcode .entry-title a,
.sidebar-content .portfolio-shortcode .entry-title a,
.sidebar-content .albums-shortcode .entry-title a,
.sidebar-content .owl-carousel.blog-carousel-shortcode .entry-title a {
  color: #333333;
}
.footer .blog-shortcode .entry-title a,
.footer .portfolio-shortcode .entry-title a,
.footer .albums-shortcode .entry-title a,
.footer .owl-carousel.blog-carousel-shortcode .entry-title a {
  color: #ffffff;
}
#page .blog-shortcode .entry-title a:hover,
#page .portfolio-shortcode:not(.gradient-overlay-layout-list):not(.content-rollover-layout-list) .entry-title a:hover,
#page .albums-shortcode .entry-title a:hover,
#page .owl-carousel.blog-carousel-shortcode .entry-title a:hover {
  color: var(--the7-accent-color);
}
.blog-shortcode .entry-meta,
.portfolio-shortcode .entry-meta,
.albums-shortcode .entry-meta,
.owl-carousel .entry-meta {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
  color: var(--the7-secondary-text-color);
}
.sidebar-content .blog-shortcode .entry-meta,
.sidebar-content .portfolio-shortcode .entry-meta,
.sidebar-content .albums-shortcode .entry-meta,
.sidebar-content .owl-carousel .entry-meta {
  color: rgba(133,134,140,0.5);
}
.footer .blog-shortcode .entry-meta,
.footer .portfolio-shortcode .entry-meta,
.footer .albums-shortcode .entry-meta,
.footer .owl-carousel .entry-meta {
  color: rgba(255,255,255,0.5);
}
.blog-shortcode .entry-excerpt,
.portfolio-shortcode .entry-excerpt,
.albums-shortcode .entry-excerpt,
.owl-carousel .entry-excerpt {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.blog-shortcode.mode-list .entry-excerpt {
  font-size: var(--the7-text-big-font-size);
  line-height: var(--the7-text-big-line-height);
}
.post-details.details-type-link {
  font-size: var(--the7-text-small-font-size);
  line-height: calc(var(--the7-text-small-font-size) + 4px);
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .post-details.details-type-link {
  color: #ffffff;
}
.sidebar-content .post-details.details-type-link {
  color: #333333;
}
.footer .post-details.details-type-link {
  color: #ffffff;
}
.post-details.details-type-link * {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .post-details.details-type-link * {
  color: #ffffff;
}
.sidebar-content .post-details.details-type-link * {
  color: #333333;
}
.footer .post-details.details-type-link * {
  color: #ffffff;
}
.post-details.details-type-link:after {
  background-color: var(--the7-accent-color);
}
.details {
  font-size: var(--the7-text-small-font-size);
  line-height: calc(var(--the7-text-small-font-size) + 4px);
  color: var(--the7-h1-color);
}
.details:hover {
  color: var(--the7-h1-color);
}
.details:before {
  background-color: var(--the7-accent-color);
}
.layout-masonry .post h2.entry-title,
.layout-grid .post h2.entry-title,
.shortcode-blog-posts .post h2.entry-title,
.dt-blog-shortcode .post h2.entry-title,
.slider-wrapper .post h2.entry-title,
.rollover-content h2.entry-title {
  color: var(--the7-h4-color);
  font: var(--the7-h4-font);
  text-transform: var(--the7-h4-text-transform);
}
.post h2.entry-title,
.post h2.entry-title {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .post h2.entry-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .post h2.entry-title {
  color: #ffffff;
}
.sidebar-content .post h2.entry-title,
.sidebar-content .post h2.entry-title {
  color: #333333;
}
.footer .post h2.entry-title,
.footer .post h2.entry-title {
  color: #ffffff;
}
.description-under-image .post .entry-title a:hover,
.post .blog-content .entry-title a:hover,
.project-list-content .entry-title a:hover {
  color: var(--the7-accent-color);
}
article.product-category .woocom-list-content .entry-title a:hover .count {
  color: var(--the7-title-color);
  background: none;
}
.fancy-date .entry-month:after,
.fancy-date .entry-date:after {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.portfolio-shortcode .project-links-container a:not(:hover) span {
  color: var(--the7-accent-color);
}
.dt-icon-hover-on.portfolio-shortcode .project-links-container a:hover span {
  color: var(--the7-accent-color);
}
.portfolio-shortcode .project-links-container:before,
.portfolio-shortcode .project-links-container:after {
  border-color: var(--the7-accent-color);
}
.dt-icon-bg-on.portfolio-shortcode .project-links-container a:before {
  background-color: var(--the7-accent-color);
}
.dt-icon-hover-bg-on.portfolio-shortcode .project-links-container a:after {
  background-color: var(--the7-accent-color);
}
.comment-list .comment-body {
  background-color: var(--the7-content-boxes-bg);
}
.shadow-element-decoration .comment-list .comment-body {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.outline-element-decoration .comment-list .comment-body {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.comment-content {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.reply,
.comment-meta,
#reply-title small {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.comment-author,
.pingback-title {
  font-size: var(--the7-text-big-font-size);
  line-height: var(--the7-text-big-line-height);
}
.comment-author a,
.comment-author .fn {
  color: var(--the7-h5-color);
  font: var(--the7-h5-font);
  text-transform: var(--the7-h5-text-transform);
}
.comments-title {
  color: var(--the7-h3-color);
  font: var(--the7-h3-font);
  text-transform: var(--the7-h3-text-transform);
}
#reply-title small {
  font: var(--the7-base-font);
}
.comments-title,
.comments-title a,
.comment-author a,
.comment-author .fn,
#reply-title,
.fn,
.fn a,
.pingback-title {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .comments-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .comments-title a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .comment-author a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .comment-author .fn,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget #reply-title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .fn,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .fn a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .pingback-title {
  color: #ffffff;
}
.sidebar-content .comments-title,
.sidebar-content .comments-title a,
.sidebar-content .comment-author a,
.sidebar-content .comment-author .fn,
.sidebar-content #reply-title,
.sidebar-content .fn,
.sidebar-content .fn a,
.sidebar-content .pingback-title {
  color: #333333;
}
.footer .comments-title,
.footer .comments-title a,
.footer .comment-author a,
.footer .comment-author .fn,
.footer #reply-title,
.footer .fn,
.footer .fn a,
.footer .pingback-title {
  color: #ffffff;
}
#page .fn a:hover {
  color: var(--the7-accent-color);
}
.entry-author {
  background-color: var(--the7-content-boxes-bg);
}
.shadow-element-decoration .entry-author {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.outline-element-decoration .entry-author {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.entry-author .text-primary {
  font-size: var(--the7-base-font-size);
}
.author-link,
.author-link:hover,
.meta-nav {
  color: var(--the7-secondary-text-color);
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.sidebar-content .author-link,
.sidebar-content .author-link:hover,
.sidebar-content .meta-nav {
  color: rgba(133,134,140,0.5);
}
.footer .author-link,
.footer .author-link:hover,
.footer .meta-nav {
  color: rgba(255,255,255,0.5);
}
.author-description h4,
.comment-author-name {
  color: var(--the7-title-color);
  color: var(--the7-h5-color);
  font: var(--the7-h5-font);
  text-transform: var(--the7-h5-text-transform);
  letter-spacing: var(--the7-h5-letter-spacing);
  word-spacing: var(--the7-h5-word-spacing);
  text-decoration: var(--the7-h5-text-decoration);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .author-description h4,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .comment-author-name {
  color: #ffffff;
}
.sidebar-content .author-description h4,
.sidebar-content .comment-author-name {
  color: #333333;
}
.footer .author-description h4,
.footer .comment-author-name {
  color: #ffffff;
}
.entry-meta,
.entry-tags,
.portfolio-categories,
.author-link {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.share-link-description,
.author-bio {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.single .entry-tags a {
  border-color: var(--the7-divider-color);
}
.sidebar .single .entry-tags a,
.sidebar-content .single .entry-tags a {
  border-color: rgba(133,134,140,0.15);
}
.footer .single .entry-tags a {
  border-color: rgba(255,255,255,0.15);
}
.entry-meta:before {
  width: var(--the7-base-line-height);
  height: var(--the7-base-line-height);
}
.portfolio-categories > a:after,
.portfolio-categories > span:after,
.entry-meta > a:after,
.entry-meta > span:after {
  background-color: var(--the7-secondary-text-color);
}
.sidebar-content .portfolio-categories > a:after,
.sidebar-content .portfolio-categories > span:after,
.sidebar-content .entry-meta > a:after,
.sidebar-content .entry-meta > span:after {
  background-color: rgba(133,134,140,0.5);
}
.footer .portfolio-categories > a:after,
.footer .portfolio-categories > span:after,
.footer .entry-meta > a:after,
.footer .entry-meta > span:after {
  background-color: rgba(255,255,255,0.5);
}
.comment-metadata,
.comment-metadata a,
.entry-meta a,
.entry-meta span,
.entry-meta a *,
.comment-reply-link,
.single .entry-tags,
.single .entry-tags a {
  color: var(--the7-secondary-text-color);
}
.single .entry-tags a:hover {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .single .entry-tags a:hover {
  color: #ffffff;
}
.sidebar-content .single .entry-tags a:hover {
  color: #333333;
}
.footer .single .entry-tags a:hover {
  color: #ffffff;
}
.share-button.entry-share,
.btn-project-link,
.btn-project-link:hover,
.share-link-description {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .share-button.entry-share,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-project-link,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-project-link:hover,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .share-link-description {
  color: #ffffff;
}
.sidebar-content .share-button.entry-share,
.sidebar-content .btn-project-link,
.sidebar-content .btn-project-link:hover,
.sidebar-content .share-link-description {
  color: #333333;
}
.footer .share-button.entry-share,
.footer .btn-project-link,
.footer .btn-project-link:hover,
.footer .share-link-description {
  color: #ffffff;
}
.show-on-hover .share-link-description .share-link-icon {
  border-color: rgba(0,0,97,0.1);
}
.show-on-hover:hover .share-link-description .share-link-icon {
  border-color: rgba(0,0,97,0.21);
}
.project-post .btn-project-link {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
  border-color: rgba(0,0,97,0.1);
}
.project-post .btn-project-link:hover {
  border-color: rgba(0,0,97,0.21);
}
.project-navigation span {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
  line-height: 22px;
}
.rsSlide .slider-post-caption,
.images-list-caption {
  line-height: var(--the7-base-line-height);
}
.post-navigation .nav-links {
  border-color: var(--the7-divider-color);
}
.sidebar .post-navigation .nav-links,
.sidebar-content .post-navigation .nav-links {
  border-color: rgba(133,134,140,0.15);
}
.footer .post-navigation .nav-links {
  border-color: rgba(255,255,255,0.15);
}
.post-navigation .nav-links svg {
  fill: rgba(0,0,97,0.12);
  color: rgba(0,0,97,0.12);
}
.post-navigation .nav-links a svg:hover {
  fill: rgba(0,0,97,0.23);
  color: rgba(0,0,97,0.23);
}
#page .post-navigation .nav-links a .post-title:hover {
  color: var(--the7-accent-color);
}
.single-related-posts {
  border-color: var(--the7-divider-color);
}
.sidebar .single-related-posts,
.sidebar-content .single-related-posts {
  border-color: rgba(133,134,140,0.15);
}
.footer .single-related-posts {
  border-color: rgba(255,255,255,0.15);
}
.dt-btn,
.widget .dt-form .dt-btn,
.nsu-submit,
.give-btn.give-btn-reveal,
.give-submit.give-btn,
.wp-block-search .wp-block-search__button,
.wc-block-product-search .wc-block-product-search__button {
  font: var(--the7-btn-s-font);
  text-transform: var(--the7-btn-s-text-transform);
  letter-spacing: var(--the7-btn-s-letter-spacing);
  word-spacing: var(--the7-btn-s-word-spacing);
  border-radius: var(--the7-btn-s-border-radius);
  border-width: var(--the7-btn-s-border-width);
  border-style: var(--the7-btn-s-border-style);
  border-color: var(--the7-btn-border-color);
  padding: var(--the7-btn-s-padding);
  min-width: var(--the7-btn-s-min-width);
  min-height: var(--the7-btn-s-min-height);
}
.dt-btn.ico-right-side > i,
.widget .dt-form .dt-btn.ico-right-side > i,
.nsu-submit.ico-right-side > i,
.give-btn.give-btn-reveal.ico-right-side > i,
.give-submit.give-btn.ico-right-side > i,
.wp-block-search .wp-block-search__button.ico-right-side > i,
.wc-block-product-search .wc-block-product-search__button.ico-right-side > i {
  margin-left: calc(var(--the7-btn-s-font-size) * 8 / 12);
  margin-right: 0 !important;
}
.dt-btn > i,
.dt-btn > i[class^="fa"],
.dt-btn > i[class^="dt-icon"],
.widget .dt-form .dt-btn > i,
.widget .dt-form .dt-btn > i[class^="fa"],
.widget .dt-form .dt-btn > i[class^="dt-icon"],
.nsu-submit > i,
.nsu-submit > i[class^="fa"],
.nsu-submit > i[class^="dt-icon"],
.give-btn.give-btn-reveal > i,
.give-btn.give-btn-reveal > i[class^="fa"],
.give-btn.give-btn-reveal > i[class^="dt-icon"],
.give-submit.give-btn > i,
.give-submit.give-btn > i[class^="fa"],
.give-submit.give-btn > i[class^="dt-icon"],
.wp-block-search .wp-block-search__button > i,
.wp-block-search .wp-block-search__button > i[class^="fa"],
.wp-block-search .wp-block-search__button > i[class^="dt-icon"],
.wc-block-product-search .wc-block-product-search__button > i,
.wc-block-product-search .wc-block-product-search__button > i[class^="fa"],
.wc-block-product-search .wc-block-product-search__button > i[class^="dt-icon"] {
  font-size: var(--the7-btn-s-icon-size);
  margin-right: var(--the7-btn-s-icon-gap,calc(var(--the7-btn-s-font-size) * 8 / 12));
}
.btn-link.dt-btn-s {
  font: var(--the7-btn-s-font);
}
button.button,
a.button,
input[type="reset"],
.wpcf7-submit,
.dt-btn-m,
input.dt-btn-m[type="submit"],
#main .gform_wrapper .gform_footer input.button,
#main .gform_wrapper .gform_footer input[type="submit"],
#main-slideshow .tp-button,
.woocommerce-widget-layered-nav-dropdown__submit {
  font: var(--the7-btn-m-font);
  text-transform: var(--the7-btn-m-text-transform);
  letter-spacing: var(--the7-btn-m-letter-spacing);
  word-spacing: var(--the7-btn-m-word-spacing);
  border-radius: var(--the7-btn-m-border-radius);
  border-width: var(--the7-btn-m-border-width);
  border-style: var(--the7-btn-m-border-style);
  border-color: var(--the7-btn-border-color);
  padding: var(--the7-btn-m-padding);
  min-width: var(--the7-btn-m-min-width);
  min-height: var(--the7-btn-m-min-height);
}
.dt-btn-m.ico-right-side > i {
  margin-left: calc(var(--the7-btn-m-font-size) * 8 / 14);
  margin-right: 0 !important;
}
.dt-btn-m > i,
.dt-btn-m > i[class^="fa"],
.dt-btn-m > i[class^="dt-icon-"] {
  font-size: var(--the7-btn-m-icon-size);
  margin-right: var(--the7-btn-m-icon-gap,calc(var(--the7-btn-m-font-size) * 8 / 14));
}
.dt-btn-l {
  font: var(--the7-btn-l-font);
  text-transform: var(--the7-btn-l-text-transform);
  letter-spacing: var(--the7-btn-l-letter-spacing);
  word-spacing: var(--the7-btn-l-word-spacing);
  border-radius: var(--the7-btn-l-border-radius);
  border-width: var(--the7-btn-l-border-width);
  border-style: var(--the7-btn-l-border-style);
  border-color: var(--the7-btn-border-color);
  padding: var(--the7-btn-l-padding);
  min-width: var(--the7-btn-l-min-width);
  min-height: var(--the7-btn-l-min-height);
}
.dt-btn-l.ico-right-side > i {
  margin-left: calc(var(--the7-btn-l-font-size) * 8 / 18);
  margin-right: 0 !important;
}
.dt-btn-l > i,
.dt-btn-l > i[class^="fa"],
.dt-btn-l > i[class^="dt-icon-"] {
  font-size: var(--the7-btn-l-icon-size);
  margin-right: var(--the7-btn-l-icon-gap,calc(var(--the7-btn-l-font-size) * 8 / 18));
}
.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration),
.mec-event-footer .mec-booking-button,
button.button,
a.button:not(.edd-submit),
.dt-form button,
.widget .dt-form .dt-btn,
input[type="reset"],
.wpcf7-submit,
.nsu-submit,
.dt-wc-btn,
.checkout-button,
input#place_order,
input[name="apply_coupon"],
input[name="login"],
button[name="calc_shipping"],
button[name="calc_shipping"]:hover,
.single_add_to_cart_button.button,
.button.wc-backward,
.woocommerce-Button.button,
.woocommerce-Reviews .submit,
.woocommerce-Button[name="register"],
.track_order input[name="track"],
.cart-btn-below-img .woo-buttons a,
input[name="save_address"],
.wc-layout-list .woo-buttons a,
.post-password-form input[type="submit"],
.mc4wp-form input[type="submit"],
div.mc4wp-form button[type="submit"],
.tml-submit-wrap input[type="submit"],
.wpcf7-form input[type="submit"],
input[type="submit"]:not([name="update_cart"]),
.woocommerce-widget-layered-nav-dropdown__submit,
.give-btn.give-btn-reveal,
.give-submit.give-btn,
.wp-block-search .wp-block-search__button,
.wc-block-product-search .wc-block-product-search__button {
  transition: all 0.3s ease-out;
  box-shadow: var(--the7-btn-shadow);
}
.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration):hover,
.mec-event-footer .mec-booking-button:hover,
button.button:hover,
a.button:not(.edd-submit):hover,
.dt-form button:hover,
.widget .dt-form .dt-btn:hover,
input[type="reset"]:hover,
.wpcf7-submit:hover,
.nsu-submit:hover,
.dt-wc-btn:hover,
.checkout-button:hover,
input#place_order:hover,
input[name="apply_coupon"]:hover,
input[name="login"]:hover,
button[name="calc_shipping"]:hover,
button[name="calc_shipping"]:hover:hover,
.single_add_to_cart_button.button:hover,
.button.wc-backward:hover,
.woocommerce-Button.button:hover,
.woocommerce-Reviews .submit:hover,
.woocommerce-Button[name="register"]:hover,
.track_order input[name="track"]:hover,
.cart-btn-below-img .woo-buttons a:hover,
input[name="save_address"]:hover,
.wc-layout-list .woo-buttons a:hover,
.post-password-form input[type="submit"]:hover,
.mc4wp-form input[type="submit"]:hover,
div.mc4wp-form button[type="submit"]:hover,
.tml-submit-wrap input[type="submit"]:hover,
.wpcf7-form input[type="submit"]:hover,
input[type="submit"]:not([name="update_cart"]):hover,
.woocommerce-widget-layered-nav-dropdown__submit:hover,
.give-btn.give-btn-reveal:hover,
.give-submit.give-btn:hover,
.wp-block-search .wp-block-search__button:hover,
.wc-block-product-search .wc-block-product-search__button:hover {
  box-shadow: var(--the7-btn-shadow-hover);
}
.btn-shadow.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration) {
  box-shadow: 0 1px 6px rgba(0,0,0,0.12);
}
.btn-shadow.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration):hover {
  box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
}
.btn-3d.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration) {
  box-shadow: 0px 2px 0px 0px #040420;
  transition: none;
}
.btn-3d.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-no-decoration):hover {
  box-shadow: 0px 2px 0px 0px #121241;
}
.mec-event-footer .mec-booking-button,
.dt-form button,
#page .widget .dt-form .dt-btn,
.widget .dt-form .dt-btn,
input[type="reset"],
.wpcf7-submit,
.nsu-submit,
.dt-wc-btn,
input#place_order,
.woocommerce-Reviews .submit:not(.box-button),
input.button,
input[name="save_address"],
.wc-layout-list .woo-buttons a,
.post-password-form input[type="submit"],
.mc4wp-form input[type="submit"],
div.mc4wp-form button[type="submit"],
.tml-submit-wrap input[type="submit"],
.wpcf7-form input[type="submit"],
input[type="submit"]:not([name="update_cart"]),
.woocommerce-widget-layered-nav-dropdown__submit,
.give-btn.give-btn-reveal,
.give-submit.give-btn,
.wc-block-filter-submit-button {
  color: var(--the7-btn-color);
  background: var(--the7-btn-bg,#000061);
}
.stripe .mec-event-footer .mec-booking-button,
.mec-event-footer .mec-booking-button *,
.sidebar .mec-event-footer .mec-booking-button,
.sidebar .widget .mec-event-footer .mec-booking-button,
.sidebar-content .widget .mec-event-footer .mec-booking-button,
.footer .mec-event-footer .mec-booking-button,
.stripe .dt-form button,
.dt-form button *,
.sidebar .dt-form button,
.sidebar .widget .dt-form button,
.sidebar-content .widget .dt-form button,
.footer .dt-form button,
.stripe #page .widget .dt-form .dt-btn,
#page .widget .dt-form .dt-btn *,
.sidebar #page .widget .dt-form .dt-btn,
.sidebar .widget #page .widget .dt-form .dt-btn,
.sidebar-content .widget #page .widget .dt-form .dt-btn,
.footer #page .widget .dt-form .dt-btn,
.stripe .widget .dt-form .dt-btn,
.widget .dt-form .dt-btn *,
.sidebar .widget .dt-form .dt-btn,
.sidebar .widget .widget .dt-form .dt-btn,
.sidebar-content .widget .widget .dt-form .dt-btn,
.footer .widget .dt-form .dt-btn,
.stripe input[type="reset"],
input[type="reset"] *,
.sidebar input[type="reset"],
.sidebar .widget input[type="reset"],
.sidebar-content .widget input[type="reset"],
.footer input[type="reset"],
.stripe .wpcf7-submit,
.wpcf7-submit *,
.sidebar .wpcf7-submit,
.sidebar .widget .wpcf7-submit,
.sidebar-content .widget .wpcf7-submit,
.footer .wpcf7-submit,
.stripe .nsu-submit,
.nsu-submit *,
.sidebar .nsu-submit,
.sidebar .widget .nsu-submit,
.sidebar-content .widget .nsu-submit,
.footer .nsu-submit,
.stripe .dt-wc-btn,
.dt-wc-btn *,
.sidebar .dt-wc-btn,
.sidebar .widget .dt-wc-btn,
.sidebar-content .widget .dt-wc-btn,
.footer .dt-wc-btn,
.stripe input#place_order,
input#place_order *,
.sidebar input#place_order,
.sidebar .widget input#place_order,
.sidebar-content .widget input#place_order,
.footer input#place_order,
.stripe .woocommerce-Reviews .submit:not(.box-button),
.woocommerce-Reviews .submit:not(.box-button) *,
.sidebar .woocommerce-Reviews .submit:not(.box-button),
.sidebar .widget .woocommerce-Reviews .submit:not(.box-button),
.sidebar-content .widget .woocommerce-Reviews .submit:not(.box-button),
.footer .woocommerce-Reviews .submit:not(.box-button),
.stripe input.button,
input.button *,
.sidebar input.button,
.sidebar .widget input.button,
.sidebar-content .widget input.button,
.footer input.button,
.stripe input[name="save_address"],
input[name="save_address"] *,
.sidebar input[name="save_address"],
.sidebar .widget input[name="save_address"],
.sidebar-content .widget input[name="save_address"],
.footer input[name="save_address"],
.stripe .wc-layout-list .woo-buttons a,
.wc-layout-list .woo-buttons a *,
.sidebar .wc-layout-list .woo-buttons a,
.sidebar .widget .wc-layout-list .woo-buttons a,
.sidebar-content .widget .wc-layout-list .woo-buttons a,
.footer .wc-layout-list .woo-buttons a,
.stripe .post-password-form input[type="submit"],
.post-password-form input[type="submit"] *,
.sidebar .post-password-form input[type="submit"],
.sidebar .widget .post-password-form input[type="submit"],
.sidebar-content .widget .post-password-form input[type="submit"],
.footer .post-password-form input[type="submit"],
.stripe .mc4wp-form input[type="submit"],
.mc4wp-form input[type="submit"] *,
.sidebar .mc4wp-form input[type="submit"],
.sidebar .widget .mc4wp-form input[type="submit"],
.sidebar-content .widget .mc4wp-form input[type="submit"],
.footer .mc4wp-form input[type="submit"],
.stripe div.mc4wp-form button[type="submit"],
div.mc4wp-form button[type="submit"] *,
.sidebar div.mc4wp-form button[type="submit"],
.sidebar .widget div.mc4wp-form button[type="submit"],
.sidebar-content .widget div.mc4wp-form button[type="submit"],
.footer div.mc4wp-form button[type="submit"],
.stripe .tml-submit-wrap input[type="submit"],
.tml-submit-wrap input[type="submit"] *,
.sidebar .tml-submit-wrap input[type="submit"],
.sidebar .widget .tml-submit-wrap input[type="submit"],
.sidebar-content .widget .tml-submit-wrap input[type="submit"],
.footer .tml-submit-wrap input[type="submit"],
.stripe .wpcf7-form input[type="submit"],
.wpcf7-form input[type="submit"] *,
.sidebar .wpcf7-form input[type="submit"],
.sidebar .widget .wpcf7-form input[type="submit"],
.sidebar-content .widget .wpcf7-form input[type="submit"],
.footer .wpcf7-form input[type="submit"],
.stripe input[type="submit"]:not([name="update_cart"]),
input[type="submit"]:not([name="update_cart"]) *,
.sidebar input[type="submit"]:not([name="update_cart"]),
.sidebar .widget input[type="submit"]:not([name="update_cart"]),
.sidebar-content .widget input[type="submit"]:not([name="update_cart"]),
.footer input[type="submit"]:not([name="update_cart"]),
.stripe .woocommerce-widget-layered-nav-dropdown__submit,
.woocommerce-widget-layered-nav-dropdown__submit *,
.sidebar .woocommerce-widget-layered-nav-dropdown__submit,
.sidebar .widget .woocommerce-widget-layered-nav-dropdown__submit,
.sidebar-content .widget .woocommerce-widget-layered-nav-dropdown__submit,
.footer .woocommerce-widget-layered-nav-dropdown__submit,
.stripe .give-btn.give-btn-reveal,
.give-btn.give-btn-reveal *,
.sidebar .give-btn.give-btn-reveal,
.sidebar .widget .give-btn.give-btn-reveal,
.sidebar-content .widget .give-btn.give-btn-reveal,
.footer .give-btn.give-btn-reveal,
.stripe .give-submit.give-btn,
.give-submit.give-btn *,
.sidebar .give-submit.give-btn,
.sidebar .widget .give-submit.give-btn,
.sidebar-content .widget .give-submit.give-btn,
.footer .give-submit.give-btn,
.stripe .wc-block-filter-submit-button,
.wc-block-filter-submit-button *,
.sidebar .wc-block-filter-submit-button,
.sidebar .widget .wc-block-filter-submit-button,
.sidebar-content .widget .wc-block-filter-submit-button,
.footer .wc-block-filter-submit-button {
  color: var(--the7-btn-color);
}
.btn-bg-off .mec-event-footer .mec-booking-button:not(:hover),
.btn-bg-off .dt-form button:not(:hover),
.btn-bg-off #page .widget .dt-form .dt-btn:not(:hover),
.btn-bg-off .widget .dt-form .dt-btn:not(:hover),
.btn-bg-off input[type="reset"]:not(:hover),
.btn-bg-off .wpcf7-submit:not(:hover),
.btn-bg-off .nsu-submit:not(:hover),
.btn-bg-off .dt-wc-btn:not(:hover),
.btn-bg-off input#place_order:not(:hover),
.btn-bg-off .woocommerce-Reviews .submit:not(.box-button):not(:hover),
.btn-bg-off input.button:not(:hover),
.btn-bg-off input[name="save_address"]:not(:hover),
.btn-bg-off .wc-layout-list .woo-buttons a:not(:hover),
.btn-bg-off .post-password-form input[type="submit"]:not(:hover),
.btn-bg-off .mc4wp-form input[type="submit"]:not(:hover),
.btn-bg-off div.mc4wp-form button[type="submit"]:not(:hover),
.btn-bg-off .tml-submit-wrap input[type="submit"]:not(:hover),
.btn-bg-off .wpcf7-form input[type="submit"]:not(:hover),
.btn-bg-off input[type="submit"]:not([name="update_cart"]):not(:hover),
.btn-bg-off .woocommerce-widget-layered-nav-dropdown__submit:not(:hover),
.btn-bg-off .give-btn.give-btn-reveal:not(:hover),
.btn-bg-off .give-submit.give-btn:not(:hover),
.btn-bg-off .wc-block-filter-submit-button:not(:hover) {
  background: none;
}
.mec-event-footer .mec-booking-button:hover > *,
.mec-event-footer .mec-booking-button:hover,
.dt-form button:hover > *,
.dt-form button:hover,
#page .widget .dt-form .dt-btn:hover > *,
#page .widget .dt-form .dt-btn:hover,
.widget .dt-form .dt-btn:hover > *,
.widget .dt-form .dt-btn:hover,
input[type="reset"]:hover > *,
input[type="reset"]:hover,
.wpcf7-submit:hover > *,
.wpcf7-submit:hover,
.nsu-submit:hover > *,
.nsu-submit:hover,
.dt-wc-btn:hover > *,
.dt-wc-btn:hover,
input#place_order:hover > *,
input#place_order:hover,
.woocommerce-Reviews .submit:not(.box-button):hover > *,
.woocommerce-Reviews .submit:not(.box-button):hover,
input.button:hover > *,
input.button:hover,
input[name="save_address"]:hover > *,
input[name="save_address"]:hover,
.wc-layout-list .woo-buttons a:hover > *,
.wc-layout-list .woo-buttons a:hover,
.post-password-form input[type="submit"]:hover > *,
.post-password-form input[type="submit"]:hover,
.mc4wp-form input[type="submit"]:hover > *,
.mc4wp-form input[type="submit"]:hover,
div.mc4wp-form button[type="submit"]:hover > *,
div.mc4wp-form button[type="submit"]:hover,
.tml-submit-wrap input[type="submit"]:hover > *,
.tml-submit-wrap input[type="submit"]:hover,
.wpcf7-form input[type="submit"]:hover > *,
.wpcf7-form input[type="submit"]:hover,
input[type="submit"]:not([name="update_cart"]):hover > *,
input[type="submit"]:not([name="update_cart"]):hover,
.woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.woocommerce-widget-layered-nav-dropdown__submit:hover,
.give-btn.give-btn-reveal:hover > *,
.give-btn.give-btn-reveal:hover,
.give-submit.give-btn:hover > *,
.give-submit.give-btn:hover,
.wc-block-filter-submit-button:hover > *,
.wc-block-filter-submit-button:hover {
  color: var(--the7-btn-hover-color);
}
.stripe .mec-event-footer .mec-booking-button:hover > *,
.mec-event-footer .mec-booking-button:hover > * *,
.sidebar .mec-event-footer .mec-booking-button:hover > *,
.sidebar .widget .mec-event-footer .mec-booking-button:hover > *,
.sidebar-content .widget .mec-event-footer .mec-booking-button:hover > *,
.footer .mec-event-footer .mec-booking-button:hover > *,
.stripe .mec-event-footer .mec-booking-button:hover,
.mec-event-footer .mec-booking-button:hover *,
.sidebar .mec-event-footer .mec-booking-button:hover,
.sidebar .widget .mec-event-footer .mec-booking-button:hover,
.sidebar-content .widget .mec-event-footer .mec-booking-button:hover,
.footer .mec-event-footer .mec-booking-button:hover,
.stripe .dt-form button:hover > *,
.dt-form button:hover > * *,
.sidebar .dt-form button:hover > *,
.sidebar .widget .dt-form button:hover > *,
.sidebar-content .widget .dt-form button:hover > *,
.footer .dt-form button:hover > *,
.stripe .dt-form button:hover,
.dt-form button:hover *,
.sidebar .dt-form button:hover,
.sidebar .widget .dt-form button:hover,
.sidebar-content .widget .dt-form button:hover,
.footer .dt-form button:hover,
.stripe #page .widget .dt-form .dt-btn:hover > *,
#page .widget .dt-form .dt-btn:hover > * *,
.sidebar #page .widget .dt-form .dt-btn:hover > *,
.sidebar .widget #page .widget .dt-form .dt-btn:hover > *,
.sidebar-content .widget #page .widget .dt-form .dt-btn:hover > *,
.footer #page .widget .dt-form .dt-btn:hover > *,
.stripe #page .widget .dt-form .dt-btn:hover,
#page .widget .dt-form .dt-btn:hover *,
.sidebar #page .widget .dt-form .dt-btn:hover,
.sidebar .widget #page .widget .dt-form .dt-btn:hover,
.sidebar-content .widget #page .widget .dt-form .dt-btn:hover,
.footer #page .widget .dt-form .dt-btn:hover,
.stripe .widget .dt-form .dt-btn:hover > *,
.widget .dt-form .dt-btn:hover > * *,
.sidebar .widget .dt-form .dt-btn:hover > *,
.sidebar .widget .widget .dt-form .dt-btn:hover > *,
.sidebar-content .widget .widget .dt-form .dt-btn:hover > *,
.footer .widget .dt-form .dt-btn:hover > *,
.stripe .widget .dt-form .dt-btn:hover,
.widget .dt-form .dt-btn:hover *,
.sidebar .widget .dt-form .dt-btn:hover,
.sidebar .widget .widget .dt-form .dt-btn:hover,
.sidebar-content .widget .widget .dt-form .dt-btn:hover,
.footer .widget .dt-form .dt-btn:hover,
.stripe input[type="reset"]:hover > *,
input[type="reset"]:hover > * *,
.sidebar input[type="reset"]:hover > *,
.sidebar .widget input[type="reset"]:hover > *,
.sidebar-content .widget input[type="reset"]:hover > *,
.footer input[type="reset"]:hover > *,
.stripe input[type="reset"]:hover,
input[type="reset"]:hover *,
.sidebar input[type="reset"]:hover,
.sidebar .widget input[type="reset"]:hover,
.sidebar-content .widget input[type="reset"]:hover,
.footer input[type="reset"]:hover,
.stripe .wpcf7-submit:hover > *,
.wpcf7-submit:hover > * *,
.sidebar .wpcf7-submit:hover > *,
.sidebar .widget .wpcf7-submit:hover > *,
.sidebar-content .widget .wpcf7-submit:hover > *,
.footer .wpcf7-submit:hover > *,
.stripe .wpcf7-submit:hover,
.wpcf7-submit:hover *,
.sidebar .wpcf7-submit:hover,
.sidebar .widget .wpcf7-submit:hover,
.sidebar-content .widget .wpcf7-submit:hover,
.footer .wpcf7-submit:hover,
.stripe .nsu-submit:hover > *,
.nsu-submit:hover > * *,
.sidebar .nsu-submit:hover > *,
.sidebar .widget .nsu-submit:hover > *,
.sidebar-content .widget .nsu-submit:hover > *,
.footer .nsu-submit:hover > *,
.stripe .nsu-submit:hover,
.nsu-submit:hover *,
.sidebar .nsu-submit:hover,
.sidebar .widget .nsu-submit:hover,
.sidebar-content .widget .nsu-submit:hover,
.footer .nsu-submit:hover,
.stripe .dt-wc-btn:hover > *,
.dt-wc-btn:hover > * *,
.sidebar .dt-wc-btn:hover > *,
.sidebar .widget .dt-wc-btn:hover > *,
.sidebar-content .widget .dt-wc-btn:hover > *,
.footer .dt-wc-btn:hover > *,
.stripe .dt-wc-btn:hover,
.dt-wc-btn:hover *,
.sidebar .dt-wc-btn:hover,
.sidebar .widget .dt-wc-btn:hover,
.sidebar-content .widget .dt-wc-btn:hover,
.footer .dt-wc-btn:hover,
.stripe input#place_order:hover > *,
input#place_order:hover > * *,
.sidebar input#place_order:hover > *,
.sidebar .widget input#place_order:hover > *,
.sidebar-content .widget input#place_order:hover > *,
.footer input#place_order:hover > *,
.stripe input#place_order:hover,
input#place_order:hover *,
.sidebar input#place_order:hover,
.sidebar .widget input#place_order:hover,
.sidebar-content .widget input#place_order:hover,
.footer input#place_order:hover,
.stripe .woocommerce-Reviews .submit:not(.box-button):hover > *,
.woocommerce-Reviews .submit:not(.box-button):hover > * *,
.sidebar .woocommerce-Reviews .submit:not(.box-button):hover > *,
.sidebar .widget .woocommerce-Reviews .submit:not(.box-button):hover > *,
.sidebar-content .widget .woocommerce-Reviews .submit:not(.box-button):hover > *,
.footer .woocommerce-Reviews .submit:not(.box-button):hover > *,
.stripe .woocommerce-Reviews .submit:not(.box-button):hover,
.woocommerce-Reviews .submit:not(.box-button):hover *,
.sidebar .woocommerce-Reviews .submit:not(.box-button):hover,
.sidebar .widget .woocommerce-Reviews .submit:not(.box-button):hover,
.sidebar-content .widget .woocommerce-Reviews .submit:not(.box-button):hover,
.footer .woocommerce-Reviews .submit:not(.box-button):hover,
.stripe input.button:hover > *,
input.button:hover > * *,
.sidebar input.button:hover > *,
.sidebar .widget input.button:hover > *,
.sidebar-content .widget input.button:hover > *,
.footer input.button:hover > *,
.stripe input.button:hover,
input.button:hover *,
.sidebar input.button:hover,
.sidebar .widget input.button:hover,
.sidebar-content .widget input.button:hover,
.footer input.button:hover,
.stripe input[name="save_address"]:hover > *,
input[name="save_address"]:hover > * *,
.sidebar input[name="save_address"]:hover > *,
.sidebar .widget input[name="save_address"]:hover > *,
.sidebar-content .widget input[name="save_address"]:hover > *,
.footer input[name="save_address"]:hover > *,
.stripe input[name="save_address"]:hover,
input[name="save_address"]:hover *,
.sidebar input[name="save_address"]:hover,
.sidebar .widget input[name="save_address"]:hover,
.sidebar-content .widget input[name="save_address"]:hover,
.footer input[name="save_address"]:hover,
.stripe .wc-layout-list .woo-buttons a:hover > *,
.wc-layout-list .woo-buttons a:hover > * *,
.sidebar .wc-layout-list .woo-buttons a:hover > *,
.sidebar .widget .wc-layout-list .woo-buttons a:hover > *,
.sidebar-content .widget .wc-layout-list .woo-buttons a:hover > *,
.footer .wc-layout-list .woo-buttons a:hover > *,
.stripe .wc-layout-list .woo-buttons a:hover,
.wc-layout-list .woo-buttons a:hover *,
.sidebar .wc-layout-list .woo-buttons a:hover,
.sidebar .widget .wc-layout-list .woo-buttons a:hover,
.sidebar-content .widget .wc-layout-list .woo-buttons a:hover,
.footer .wc-layout-list .woo-buttons a:hover,
.stripe .post-password-form input[type="submit"]:hover > *,
.post-password-form input[type="submit"]:hover > * *,
.sidebar .post-password-form input[type="submit"]:hover > *,
.sidebar .widget .post-password-form input[type="submit"]:hover > *,
.sidebar-content .widget .post-password-form input[type="submit"]:hover > *,
.footer .post-password-form input[type="submit"]:hover > *,
.stripe .post-password-form input[type="submit"]:hover,
.post-password-form input[type="submit"]:hover *,
.sidebar .post-password-form input[type="submit"]:hover,
.sidebar .widget .post-password-form input[type="submit"]:hover,
.sidebar-content .widget .post-password-form input[type="submit"]:hover,
.footer .post-password-form input[type="submit"]:hover,
.stripe .mc4wp-form input[type="submit"]:hover > *,
.mc4wp-form input[type="submit"]:hover > * *,
.sidebar .mc4wp-form input[type="submit"]:hover > *,
.sidebar .widget .mc4wp-form input[type="submit"]:hover > *,
.sidebar-content .widget .mc4wp-form input[type="submit"]:hover > *,
.footer .mc4wp-form input[type="submit"]:hover > *,
.stripe .mc4wp-form input[type="submit"]:hover,
.mc4wp-form input[type="submit"]:hover *,
.sidebar .mc4wp-form input[type="submit"]:hover,
.sidebar .widget .mc4wp-form input[type="submit"]:hover,
.sidebar-content .widget .mc4wp-form input[type="submit"]:hover,
.footer .mc4wp-form input[type="submit"]:hover,
.stripe div.mc4wp-form button[type="submit"]:hover > *,
div.mc4wp-form button[type="submit"]:hover > * *,
.sidebar div.mc4wp-form button[type="submit"]:hover > *,
.sidebar .widget div.mc4wp-form button[type="submit"]:hover > *,
.sidebar-content .widget div.mc4wp-form button[type="submit"]:hover > *,
.footer div.mc4wp-form button[type="submit"]:hover > *,
.stripe div.mc4wp-form button[type="submit"]:hover,
div.mc4wp-form button[type="submit"]:hover *,
.sidebar div.mc4wp-form button[type="submit"]:hover,
.sidebar .widget div.mc4wp-form button[type="submit"]:hover,
.sidebar-content .widget div.mc4wp-form button[type="submit"]:hover,
.footer div.mc4wp-form button[type="submit"]:hover,
.stripe .tml-submit-wrap input[type="submit"]:hover > *,
.tml-submit-wrap input[type="submit"]:hover > * *,
.sidebar .tml-submit-wrap input[type="submit"]:hover > *,
.sidebar .widget .tml-submit-wrap input[type="submit"]:hover > *,
.sidebar-content .widget .tml-submit-wrap input[type="submit"]:hover > *,
.footer .tml-submit-wrap input[type="submit"]:hover > *,
.stripe .tml-submit-wrap input[type="submit"]:hover,
.tml-submit-wrap input[type="submit"]:hover *,
.sidebar .tml-submit-wrap input[type="submit"]:hover,
.sidebar .widget .tml-submit-wrap input[type="submit"]:hover,
.sidebar-content .widget .tml-submit-wrap input[type="submit"]:hover,
.footer .tml-submit-wrap input[type="submit"]:hover,
.stripe .wpcf7-form input[type="submit"]:hover > *,
.wpcf7-form input[type="submit"]:hover > * *,
.sidebar .wpcf7-form input[type="submit"]:hover > *,
.sidebar .widget .wpcf7-form input[type="submit"]:hover > *,
.sidebar-content .widget .wpcf7-form input[type="submit"]:hover > *,
.footer .wpcf7-form input[type="submit"]:hover > *,
.stripe .wpcf7-form input[type="submit"]:hover,
.wpcf7-form input[type="submit"]:hover *,
.sidebar .wpcf7-form input[type="submit"]:hover,
.sidebar .widget .wpcf7-form input[type="submit"]:hover,
.sidebar-content .widget .wpcf7-form input[type="submit"]:hover,
.footer .wpcf7-form input[type="submit"]:hover,
.stripe input[type="submit"]:not([name="update_cart"]):hover > *,
input[type="submit"]:not([name="update_cart"]):hover > * *,
.sidebar input[type="submit"]:not([name="update_cart"]):hover > *,
.sidebar .widget input[type="submit"]:not([name="update_cart"]):hover > *,
.sidebar-content .widget input[type="submit"]:not([name="update_cart"]):hover > *,
.footer input[type="submit"]:not([name="update_cart"]):hover > *,
.stripe input[type="submit"]:not([name="update_cart"]):hover,
input[type="submit"]:not([name="update_cart"]):hover *,
.sidebar input[type="submit"]:not([name="update_cart"]):hover,
.sidebar .widget input[type="submit"]:not([name="update_cart"]):hover,
.sidebar-content .widget input[type="submit"]:not([name="update_cart"]):hover,
.footer input[type="submit"]:not([name="update_cart"]):hover,
.stripe .woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.woocommerce-widget-layered-nav-dropdown__submit:hover > * *,
.sidebar .woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.sidebar .widget .woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.sidebar-content .widget .woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.footer .woocommerce-widget-layered-nav-dropdown__submit:hover > *,
.stripe .woocommerce-widget-layered-nav-dropdown__submit:hover,
.woocommerce-widget-layered-nav-dropdown__submit:hover *,
.sidebar .woocommerce-widget-layered-nav-dropdown__submit:hover,
.sidebar .widget .woocommerce-widget-layered-nav-dropdown__submit:hover,
.sidebar-content .widget .woocommerce-widget-layered-nav-dropdown__submit:hover,
.footer .woocommerce-widget-layered-nav-dropdown__submit:hover,
.stripe .give-btn.give-btn-reveal:hover > *,
.give-btn.give-btn-reveal:hover > * *,
.sidebar .give-btn.give-btn-reveal:hover > *,
.sidebar .widget .give-btn.give-btn-reveal:hover > *,
.sidebar-content .widget .give-btn.give-btn-reveal:hover > *,
.footer .give-btn.give-btn-reveal:hover > *,
.stripe .give-btn.give-btn-reveal:hover,
.give-btn.give-btn-reveal:hover *,
.sidebar .give-btn.give-btn-reveal:hover,
.sidebar .widget .give-btn.give-btn-reveal:hover,
.sidebar-content .widget .give-btn.give-btn-reveal:hover,
.footer .give-btn.give-btn-reveal:hover,
.stripe .give-submit.give-btn:hover > *,
.give-submit.give-btn:hover > * *,
.sidebar .give-submit.give-btn:hover > *,
.sidebar .widget .give-submit.give-btn:hover > *,
.sidebar-content .widget .give-submit.give-btn:hover > *,
.footer .give-submit.give-btn:hover > *,
.stripe .give-submit.give-btn:hover,
.give-submit.give-btn:hover *,
.sidebar .give-submit.give-btn:hover,
.sidebar .widget .give-submit.give-btn:hover,
.sidebar-content .widget .give-submit.give-btn:hover,
.footer .give-submit.give-btn:hover,
.stripe .wc-block-filter-submit-button:hover > *,
.wc-block-filter-submit-button:hover > * *,
.sidebar .wc-block-filter-submit-button:hover > *,
.sidebar .widget .wc-block-filter-submit-button:hover > *,
.sidebar-content .widget .wc-block-filter-submit-button:hover > *,
.footer .wc-block-filter-submit-button:hover > *,
.stripe .wc-block-filter-submit-button:hover,
.wc-block-filter-submit-button:hover *,
.sidebar .wc-block-filter-submit-button:hover,
.sidebar .widget .wc-block-filter-submit-button:hover,
.sidebar-content .widget .wc-block-filter-submit-button:hover,
.footer .wc-block-filter-submit-button:hover {
  color: var(--the7-btn-hover-color);
}
.mec-event-footer .mec-booking-button:hover,
.dt-form button:hover,
#page .widget .dt-form .dt-btn:hover,
.widget .dt-form .dt-btn:hover,
input[type="reset"]:hover,
.wpcf7-submit:hover,
.nsu-submit:hover,
.dt-wc-btn:hover,
input#place_order:hover,
.woocommerce-Reviews .submit:not(.box-button):hover,
input.button:hover,
input[name="save_address"]:hover,
.wc-layout-list .woo-buttons a:hover,
.post-password-form input[type="submit"]:hover,
.mc4wp-form input[type="submit"]:hover,
div.mc4wp-form button[type="submit"]:hover,
.tml-submit-wrap input[type="submit"]:hover,
.wpcf7-form input[type="submit"]:hover,
input[type="submit"]:not([name="update_cart"]):hover,
.woocommerce-widget-layered-nav-dropdown__submit:hover,
.give-btn.give-btn-reveal:hover,
.give-submit.give-btn:hover,
.wc-block-filter-submit-button:hover {
  border-color: var(--the7-btn-border-hover-color);
}
#page .mec-event-footer .mec-booking-button:hover,
.tinv-modal .mec-event-footer .mec-booking-button:hover,
#page .dt-form button:hover,
.tinv-modal .dt-form button:hover,
#page #page .widget .dt-form .dt-btn:hover,
.tinv-modal #page .widget .dt-form .dt-btn:hover,
#page .widget .dt-form .dt-btn:hover,
.tinv-modal .widget .dt-form .dt-btn:hover,
#page input[type="reset"]:hover,
.tinv-modal input[type="reset"]:hover,
#page .wpcf7-submit:hover,
.tinv-modal .wpcf7-submit:hover,
#page .nsu-submit:hover,
.tinv-modal .nsu-submit:hover,
#page .dt-wc-btn:hover,
.tinv-modal .dt-wc-btn:hover,
#page input#place_order:hover,
.tinv-modal input#place_order:hover,
#page .woocommerce-Reviews .submit:not(.box-button):hover,
.tinv-modal .woocommerce-Reviews .submit:not(.box-button):hover,
#page input.button:hover,
.tinv-modal input.button:hover,
#page input[name="save_address"]:hover,
.tinv-modal input[name="save_address"]:hover,
#page .wc-layout-list .woo-buttons a:hover,
.tinv-modal .wc-layout-list .woo-buttons a:hover,
#page .post-password-form input[type="submit"]:hover,
.tinv-modal .post-password-form input[type="submit"]:hover,
#page .mc4wp-form input[type="submit"]:hover,
.tinv-modal .mc4wp-form input[type="submit"]:hover,
#page div.mc4wp-form button[type="submit"]:hover,
.tinv-modal div.mc4wp-form button[type="submit"]:hover,
#page .tml-submit-wrap input[type="submit"]:hover,
.tinv-modal .tml-submit-wrap input[type="submit"]:hover,
#page .wpcf7-form input[type="submit"]:hover,
.tinv-modal .wpcf7-form input[type="submit"]:hover,
#page input[type="submit"]:not([name="update_cart"]):hover,
.tinv-modal input[type="submit"]:not([name="update_cart"]):hover,
#page .woocommerce-widget-layered-nav-dropdown__submit:hover,
.tinv-modal .woocommerce-widget-layered-nav-dropdown__submit:hover,
#page .give-btn.give-btn-reveal:hover,
.tinv-modal .give-btn.give-btn-reveal:hover,
#page .give-submit.give-btn:hover,
.tinv-modal .give-submit.give-btn:hover,
#page .wc-block-filter-submit-button:hover,
.tinv-modal .wc-block-filter-submit-button:hover {
  background: var(--the7-btn-hover-bg,#11117f);
}
.btn-hover-bg-off #page .mec-event-footer .mec-booking-button:hover,
.btn-hover-bg-off #page .dt-form button:hover,
.btn-hover-bg-off #page #page .widget .dt-form .dt-btn:hover,
.btn-hover-bg-off #page .widget .dt-form .dt-btn:hover,
.btn-hover-bg-off #page input[type="reset"]:hover,
.btn-hover-bg-off #page .wpcf7-submit:hover,
.btn-hover-bg-off #page .nsu-submit:hover,
.btn-hover-bg-off #page .dt-wc-btn:hover,
.btn-hover-bg-off #page input#place_order:hover,
.btn-hover-bg-off #page .woocommerce-Reviews .submit:not(.box-button):hover,
.btn-hover-bg-off #page input.button:hover,
.btn-hover-bg-off #page input[name="save_address"]:hover,
.btn-hover-bg-off #page .wc-layout-list .woo-buttons a:hover,
.btn-hover-bg-off #page .post-password-form input[type="submit"]:hover,
.btn-hover-bg-off #page .mc4wp-form input[type="submit"]:hover,
.btn-hover-bg-off #page div.mc4wp-form button[type="submit"]:hover,
.btn-hover-bg-off #page .tml-submit-wrap input[type="submit"]:hover,
.btn-hover-bg-off #page .wpcf7-form input[type="submit"]:hover,
.btn-hover-bg-off #page input[type="submit"]:not([name="update_cart"]):hover,
.btn-hover-bg-off #page .woocommerce-widget-layered-nav-dropdown__submit:hover,
.btn-hover-bg-off #page .give-btn.give-btn-reveal:hover,
.btn-hover-bg-off #page .give-submit.give-btn:hover,
.btn-hover-bg-off #page .wc-block-filter-submit-button:hover {
  background: none;
}
.mec-event-footer .mec-booking-button:hover .text-wrap,
.dt-form button:hover .text-wrap,
#page .widget .dt-form .dt-btn:hover .text-wrap,
.widget .dt-form .dt-btn:hover .text-wrap,
input[type="reset"]:hover .text-wrap,
.wpcf7-submit:hover .text-wrap,
.nsu-submit:hover .text-wrap,
.dt-wc-btn:hover .text-wrap,
input#place_order:hover .text-wrap,
.woocommerce-Reviews .submit:not(.box-button):hover .text-wrap,
input.button:hover .text-wrap,
input[name="save_address"]:hover .text-wrap,
.wc-layout-list .woo-buttons a:hover .text-wrap,
.post-password-form input[type="submit"]:hover .text-wrap,
.mc4wp-form input[type="submit"]:hover .text-wrap,
div.mc4wp-form button[type="submit"]:hover .text-wrap,
.tml-submit-wrap input[type="submit"]:hover .text-wrap,
.wpcf7-form input[type="submit"]:hover .text-wrap,
input[type="submit"]:not([name="update_cart"]):hover .text-wrap,
.woocommerce-widget-layered-nav-dropdown__submit:hover .text-wrap,
.give-btn.give-btn-reveal:hover .text-wrap,
.give-submit.give-btn:hover .text-wrap,
.wc-block-filter-submit-button:hover .text-wrap {
  color: var(--the7-btn-hover-color);
}
.wc-block-product-search .wc-block-product-search__button,
.wp-block-search .wp-block-search__button,
button.button,
.cart-btn-below-img .woo-buttons a,
a.button:not(.edd-submit) {
  color: var(--the7-btn-color);
  background: var(--the7-btn-bg,#000061);
}
.stripe .wc-block-product-search .wc-block-product-search__button,
.wc-block-product-search .wc-block-product-search__button *,
.sidebar .wc-block-product-search .wc-block-product-search__button,
.sidebar .widget .wc-block-product-search .wc-block-product-search__button,
.sidebar-content .widget .wc-block-product-search .wc-block-product-search__button,
.footer .wc-block-product-search .wc-block-product-search__button,
.stripe .wp-block-search .wp-block-search__button,
.wp-block-search .wp-block-search__button *,
.sidebar .wp-block-search .wp-block-search__button,
.sidebar .widget .wp-block-search .wp-block-search__button,
.sidebar-content .widget .wp-block-search .wp-block-search__button,
.footer .wp-block-search .wp-block-search__button,
.stripe button.button,
button.button *,
.sidebar button.button,
.sidebar .widget button.button,
.sidebar-content .widget button.button,
.footer button.button,
.stripe .cart-btn-below-img .woo-buttons a,
.cart-btn-below-img .woo-buttons a *,
.sidebar .cart-btn-below-img .woo-buttons a,
.sidebar .widget .cart-btn-below-img .woo-buttons a,
.sidebar-content .widget .cart-btn-below-img .woo-buttons a,
.footer .cart-btn-below-img .woo-buttons a,
.stripe a.button:not(.edd-submit),
a.button:not(.edd-submit) *,
.sidebar a.button:not(.edd-submit),
.sidebar .widget a.button:not(.edd-submit),
.sidebar-content .widget a.button:not(.edd-submit),
.footer a.button:not(.edd-submit) {
  color: var(--the7-btn-color);
}
.btn-bg-off .wc-block-product-search .wc-block-product-search__button:not(:hover),
.btn-bg-off .wp-block-search .wp-block-search__button:not(:hover),
.btn-bg-off button.button:not(:hover),
.btn-bg-off .cart-btn-below-img .woo-buttons a:not(:hover),
.btn-bg-off a.button:not(.edd-submit):not(:hover) {
  background: none;
}
.wc-block-product-search .wc-block-product-search__button:hover > *,
.wc-block-product-search .wc-block-product-search__button:hover,
.wp-block-search .wp-block-search__button:hover > *,
.wp-block-search .wp-block-search__button:hover,
button.button:hover > *,
button.button:hover,
.cart-btn-below-img .woo-buttons a:hover > *,
.cart-btn-below-img .woo-buttons a:hover,
a.button:not(.edd-submit):hover > *,
a.button:not(.edd-submit):hover {
  color: var(--the7-btn-hover-color);
}
.wc-block-product-search .wc-block-product-search__button:hover,
.wp-block-search .wp-block-search__button:hover,
button.button:hover,
.cart-btn-below-img .woo-buttons a:hover,
a.button:not(.edd-submit):hover {
  border-color: var(--the7-btn-border-hover-color);
  background: var(--the7-btn-hover-bg,#11117f);
}
.stripe .wc-block-product-search .wc-block-product-search__button:hover,
.wc-block-product-search .wc-block-product-search__button:hover *,
.sidebar .wc-block-product-search .wc-block-product-search__button:hover,
.sidebar .widget .wc-block-product-search .wc-block-product-search__button:hover,
.sidebar-content .widget .wc-block-product-search .wc-block-product-search__button:hover,
.footer .wc-block-product-search .wc-block-product-search__button:hover,
.stripe .wp-block-search .wp-block-search__button:hover,
.wp-block-search .wp-block-search__button:hover *,
.sidebar .wp-block-search .wp-block-search__button:hover,
.sidebar .widget .wp-block-search .wp-block-search__button:hover,
.sidebar-content .widget .wp-block-search .wp-block-search__button:hover,
.footer .wp-block-search .wp-block-search__button:hover,
.stripe button.button:hover,
button.button:hover *,
.sidebar button.button:hover,
.sidebar .widget button.button:hover,
.sidebar-content .widget button.button:hover,
.footer button.button:hover,
.stripe .cart-btn-below-img .woo-buttons a:hover,
.cart-btn-below-img .woo-buttons a:hover *,
.sidebar .cart-btn-below-img .woo-buttons a:hover,
.sidebar .widget .cart-btn-below-img .woo-buttons a:hover,
.sidebar-content .widget .cart-btn-below-img .woo-buttons a:hover,
.footer .cart-btn-below-img .woo-buttons a:hover,
.stripe a.button:not(.edd-submit):hover,
a.button:not(.edd-submit):hover *,
.sidebar a.button:not(.edd-submit):hover,
.sidebar .widget a.button:not(.edd-submit):hover,
.sidebar-content .widget a.button:not(.edd-submit):hover,
.footer a.button:not(.edd-submit):hover {
  color: var(--the7-btn-hover-color);
}
.btn-hover-bg-off .wc-block-product-search .wc-block-product-search__button:hover,
.btn-hover-bg-off .wp-block-search .wp-block-search__button:hover,
.btn-hover-bg-off button.button:hover,
.btn-hover-bg-off .cart-btn-below-img .woo-buttons a:hover,
.btn-hover-bg-off a.button:not(.edd-submit):hover {
  background: none;
}
.wc-block-product-search .wc-block-product-search__button:hover .text-wrap,
.wp-block-search .wp-block-search__button:hover .text-wrap,
button.button:hover .text-wrap,
.cart-btn-below-img .woo-buttons a:hover .text-wrap,
a.button:not(.edd-submit):hover .text-wrap {
  color: var(--the7-btn-hover-color);
}
button.button.tinvwl-add-to-cart:hover > * {
  color: inherit;
}
.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style),
.mec-event-footer .mec-booking-button,
.give-btn.give-btn-reveal,
.give-submit.give-btn {
  background: var(--the7-btn-bg,#000061);
}
.btn-bg-off .dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style):not(:hover),
.btn-bg-off .mec-event-footer .mec-booking-button:not(:hover),
.btn-bg-off .give-btn.give-btn-reveal:not(:hover),
.btn-bg-off .give-submit.give-btn:not(:hover) {
  background: none;
}
.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style):hover,
.mec-event-footer .mec-booking-button:hover,
.give-btn.give-btn-reveal:hover,
.give-submit.give-btn:hover {
  background: none;
  background: var(--the7-btn-hover-bg,#11117f);
  border-color: var(--the7-btn-border-hover-color);
}
.btn-hover-bg-off .dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style):hover,
.btn-hover-bg-off .mec-event-footer .mec-booking-button:hover,
.btn-hover-bg-off .give-btn.give-btn-reveal:hover,
.btn-hover-bg-off .give-submit.give-btn:hover {
  background: none;
}
.dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style).accent-btn-bg-color,
.mec-event-footer .mec-booking-button.accent-btn-bg-color,
.give-btn.give-btn-reveal.accent-btn-bg-color,
.give-submit.give-btn.accent-btn-bg-color {
  background: var(--the7-accent-color);
}
#page .dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style).accent-btn-bg-hover-color:hover,
#page .mec-event-footer .mec-booking-button.accent-btn-bg-hover-color:hover,
#page .give-btn.give-btn-reveal.accent-btn-bg-hover-color:hover,
#page .give-submit.give-btn.accent-btn-bg-hover-color:hover {
  background: var(--the7-accent-color);
}
#page .dt-btn:not(.btn-light):not(.light-bg-btn):not(.outline-btn):not(.outline-bg-btn):not(.btn-custom-style).default-btn-bg-hover-color:hover,
#page .mec-event-footer .mec-booking-button.default-btn-bg-hover-color:hover,
#page .give-btn.give-btn-reveal.default-btn-bg-hover-color:hover,
#page .give-submit.give-btn.default-btn-bg-hover-color:hover {
  background: none;
  background: var(--the7-btn-hover-bg,#11117f);
}
.btn-light:hover,
.outline-bg-btn:hover {
  background: var(--the7-btn-hover-bg,#11117f);
}
.dt-btn.btn-hover-off:hover {
  background: var(--the7-btn-bg,#000061) !important;
}
.dt-btn:not(.btn-custom-style),
.btn-link {
  color: var(--the7-btn-color);
}
.dt-btn:not(.btn-custom-style) *,
.sidebar .dt-btn:not(.btn-custom-style),
.content .sidebar-content .dt-btn:not(.btn-custom-style),
.footer .dt-btn:not(.btn-custom-style),
.content .shortcode-banner-inside .dt-btn:not(.btn-custom-style) *,
.content .dt-btn:not(.btn-custom-style),
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .dt-btn:not(.btn-custom-style),
.btn-link *,
.sidebar .btn-link,
.content .sidebar-content .btn-link,
.footer .btn-link,
.content .shortcode-banner-inside .btn-link *,
.content .btn-link,
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .btn-link {
  color: var(--the7-btn-color);
}
.dt-btn:not(.btn-custom-style):hover,
.dt-btn:not(.btn-custom-style):hover > *,
.btn-link:hover,
.btn-link:hover > * {
  color: var(--the7-btn-hover-color);
}
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .dt-btn:not(.btn-custom-style):hover,
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .dt-btn:not(.btn-custom-style):hover > *,
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .btn-link:hover,
.content .elementor-widget[class*='elementor-widget-wp-widget-'] .btn-link:hover > * {
  color: var(--the7-btn-hover-color);
}
.dt-btn:not(.btn-custom-style).accent-btn-color > span,
.dt-btn:not(.btn-custom-style).accent-btn-color > .text-wrap *,
.dt-btn:not(.btn-custom-style).accent-btn-color > i[class^="fa"],
.dt-btn:not(.btn-custom-style).accent-btn-color > i[class^="dt-icon-"],
.btn-link.accent-btn-color > span,
.btn-link.accent-btn-color > .text-wrap *,
.btn-link.accent-btn-color > i[class^="fa"],
.btn-link.accent-btn-color > i[class^="dt-icon-"] {
  color: var(--the7-accent-color);
}
.dt-btn:not(.btn-custom-style).title-btn-color,
.dt-btn:not(.btn-custom-style).title-btn-color > span,
.dt-btn:not(.btn-custom-style).title-btn-color > .text-wrap *,
.dt-btn:not(.btn-custom-style).title-btn-color > i[class^="fa"],
.dt-btn:not(.btn-custom-style).title-btn-color > i[class^="dt-icon-"],
.btn-link.title-btn-color,
.btn-link.title-btn-color > span,
.btn-link.title-btn-color > .text-wrap *,
.btn-link.title-btn-color > i[class^="fa"],
.btn-link.title-btn-color > i[class^="dt-icon-"] {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .dt-btn:not(.btn-custom-style).title-btn-color,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .dt-btn:not(.btn-custom-style).title-btn-color > span,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .dt-btn:not(.btn-custom-style).title-btn-color > .text-wrap *,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="fa"],
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="dt-icon-"],
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-link.title-btn-color,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-link.title-btn-color > span,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-link.title-btn-color > .text-wrap *,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-link.title-btn-color > i[class^="fa"],
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .btn-link.title-btn-color > i[class^="dt-icon-"] {
  color: #ffffff;
}
.sidebar-content .dt-btn:not(.btn-custom-style).title-btn-color,
.sidebar-content .dt-btn:not(.btn-custom-style).title-btn-color > span,
.sidebar-content .dt-btn:not(.btn-custom-style).title-btn-color > .text-wrap *,
.sidebar-content .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="fa"],
.sidebar-content .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="dt-icon-"],
.sidebar-content .btn-link.title-btn-color,
.sidebar-content .btn-link.title-btn-color > span,
.sidebar-content .btn-link.title-btn-color > .text-wrap *,
.sidebar-content .btn-link.title-btn-color > i[class^="fa"],
.sidebar-content .btn-link.title-btn-color > i[class^="dt-icon-"] {
  color: #333333;
}
.footer .dt-btn:not(.btn-custom-style).title-btn-color,
.footer .dt-btn:not(.btn-custom-style).title-btn-color > span,
.footer .dt-btn:not(.btn-custom-style).title-btn-color > .text-wrap *,
.footer .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="fa"],
.footer .dt-btn:not(.btn-custom-style).title-btn-color > i[class^="dt-icon-"],
.footer .btn-link.title-btn-color,
.footer .btn-link.title-btn-color > span,
.footer .btn-link.title-btn-color > .text-wrap *,
.footer .btn-link.title-btn-color > i[class^="fa"],
.footer .btn-link.title-btn-color > i[class^="dt-icon-"] {
  color: #ffffff;
}
.dt-btn:not(.btn-custom-style).title-btn-hover-color:hover > span,
.dt-btn:not(.btn-custom-style).title-btn-hover-color:hover > .text-wrap *,
.dt-btn:not(.btn-custom-style).title-btn-hover-color:hover > i[class^="fa"],
.dt-btn:not(.btn-custom-style).title-btn-hover-color:hover > i[class^="dt-icon-"],
.btn-link.title-btn-hover-color:hover > span,
.btn-link.title-btn-hover-color:hover > .text-wrap *,
.btn-link.title-btn-hover-color:hover > i[class^="fa"],
.btn-link.title-btn-hover-color:hover > i[class^="dt-icon-"] {
  color: var(--the7-h1-color) !important;
}
.dt-btn:not(.btn-custom-style).default-btn-hover-color:hover > span,
.dt-btn:not(.btn-custom-style).default-btn-hover-color:hover > .text-wrap *,
.dt-btn:not(.btn-custom-style).default-btn-hover-color:hover > i[class^="fa"],
.dt-btn:not(.btn-custom-style).default-btn-hover-color:hover > i[class^="dt-icon-"],
.btn-link.default-btn-hover-color:hover > span,
.btn-link.default-btn-hover-color:hover > .text-wrap *,
.btn-link.default-btn-hover-color:hover > i[class^="fa"],
.btn-link.default-btn-hover-color:hover > i[class^="dt-icon-"] {
  color: var(--the7-btn-hover-color) !important;
}
#page .dt-btn:not(.btn-custom-style).accent-btn-hover-color:hover > span,
#page .dt-btn:not(.btn-custom-style).accent-btn-hover-color:hover > .text-wrap *,
#page .dt-btn:not(.btn-custom-style).accent-btn-hover-color:hover > i[class^="fa"],
#page .dt-btn:not(.btn-custom-style).accent-btn-hover-color:hover > i[class^="dt-icon-"],
#page .btn-link.accent-btn-hover-color:hover > span,
#page .btn-link.accent-btn-hover-color:hover > .text-wrap *,
#page .btn-link.accent-btn-hover-color:hover > i[class^="fa"],
#page .btn-link.accent-btn-hover-color:hover > i[class^="dt-icon-"] {
  color: var(--the7-accent-color) !important;
}
.accent-btn-color .dt-btn:not(.custom-btn-color):not(.btn-shortcode):not(.btn-custom-style) > span,
.accent-btn-color .dt-btn:not(.custom-btn-color):not(.btn-shortcode):not(.btn-custom-style) > .text-wrap * {
  color: var(--the7-accent-color);
}
.custom-btn-hover-color .dt-btn:not(.custom-btn-color):not(.accent-btn-hover-color):not(.btn-shortcode):hover > span,
.custom-btn-hover-color .dt-btn:not(.custom-btn-color):not(.accent-btn-hover-color):not(.btn-shortcode):hover > .text-wrap * {
  background: none;
  color: var(--the7-btn-hover-color);
}
.custom-btn-hover-color .dt-btn:hover > .text-wrap * {
  color: inherit;
}
.accent-btn-hover-color .dt-btn:not(.custom-btn-hover-color):not(.btn-shortcode):not(.btn-custom-style):hover > span,
.accent-btn-hover-color .dt-btn:not(.custom-btn-hover-color):not(.btn-shortcode):not(.btn-custom-style):hover > .text-wrap * {
  color: var(--the7-accent-color);
}
.light-bg-btn {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .light-bg-btn {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .light-bg-btn {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.light-bg-btn:hover {
  background: var(--the7-btn-hover-bg,#11117f);
}
#page .light-bg-btn.accent-btn-bg-hover-color:hover {
  background: var(--the7-accent-color);
}
.btn-light {
  background-color: var(--the7-content-boxes-bg);
  background-image: none;
}
.outline-element-decoration .btn-light {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .btn-light {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.btn-light:hover {
  background-image: none;
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .btn-light:hover {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .btn-light:hover {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.dt-btn.outline-bg-btn {
  background: none;
}
.accent-gradient .dt-btn.outline-bg-btn {
  background: none;
}
.dt-btn.outline-bg-btn:hover {
  border-color: transparent;
}
.outline-bg-btn {
  border-color: var(--the7-btn-bg-color);
}
.outline-bg-btn:hover {
  border-color: var(--the7-btn-hover-bg-color);
  background: var(--the7-btn-hover-bg,#11117f);
}
.outline-bg-btn.accent-btn-bg-color {
  border-color: var(--the7-accent-color);
}
#page .outline-bg-btn.accent-btn-bg-hover-color:hover {
  background: var(--the7-accent-color);
  border-color: transparent;
}
.outline-btn {
  border-color: var(--the7-btn-bg-color);
}
.outline-btn:hover {
  border-color: var(--the7-btn-hover-bg-color);
  background: none;
}
.outline-btn.accent-btn-bg-color {
  border-color: var(--the7-accent-color);
}
#page .outline-btn.accent-btn-bg-hover-color:hover {
  border-color: var(--the7-accent-color);
}
.dt-btn-link,
.dt-btn-link > span,
.dt-btn-link > i {
  color: var(--the7-accent-color);
}
.dt-btn-link:after {
  background: var(--the7-accent-color);
}
.hr-thick {
  border-color: rgba(82,82,87,0.1);
}
.hr-thin {
  border-style: solid;
  border-width: 1px;
  border-color: var(--the7-divider-color);
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.hr-thin.style-dashed {
  border-top-style: dashed;
  border-bottom: none;
}
.hr-thin.style-dotted {
  border-top-style: dotted;
  border-bottom: none;
}
.hr-thin.style-double {
  border-top-style: double;
  border-top-width: 3px;
  border-bottom: none;
}
.accent-border-color .separator-holder,
.accent-border-color.hr-thin,
.accent-border-color.hr-thick {
  border-color: var(--the7-accent-color);
}
.accent-gradient .accent-border-color.hr-thin.style-line,
.accent-gradient .accent-border-color.hr-thick {
  border-width: 0;
  border-color: var(--the7-accent-color);
}
.dt-fancy-title.bg-on,
hr.gf_rule {
  background-color: rgba(82,82,87,0.08);
}
.sidebar .dt-fancy-title.bg-on,
.sidebar-content .dt-fancy-title.bg-on,
.sidebar hr.gf_rule,
.sidebar-content hr.gf_rule {
  background-color: rgba(133,134,140,0.08);
}
.footer .dt-fancy-title.bg-on,
.footer hr.gf_rule {
  background-color: rgba(255,255,255,0.08);
}
.accent-border-color .dt-fancy-title.bg-on {
  color: #fff;
  background-color: var(--the7-accent-color);
}
#page .accent-title-color .dt-fancy-title {
  color: var(--the7-accent-color);
}
.title-color .dt-fancy-title {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .title-color .dt-fancy-title {
  color: #ffffff;
}
.sidebar-content .title-color .dt-fancy-title {
  color: #333333;
}
.footer .title-color .dt-fancy-title {
  color: #ffffff;
}
.dt-fancy-separator,
.dt-fancy-separator * {
  color: var(--the7-secondary-text-color);
}
.sidebar-content .dt-fancy-separator,
.sidebar-content .dt-fancy-separator * {
  color: rgba(133,134,140,0.5);
}
.footer .dt-fancy-separator,
.footer .dt-fancy-separator * {
  color: rgba(255,255,255,0.5);
}
.separator-holder {
  border-color: var(--the7-divider-color);
}
.separator-holder,
.accent-border-color .bg-on .separator-holder {
  border-color: var(--the7-divider-color);
}
.sidebar .separator-holder,
.sidebar-content .separator-holder,
.sidebar .accent-border-color .bg-on .separator-holder,
.sidebar-content .accent-border-color .bg-on .separator-holder {
  border-color: rgba(133,134,140,0.15);
}
.footer .separator-holder,
.footer .accent-border-color .bg-on .separator-holder {
  border-color: rgba(255,255,255,0.15);
}
.dt-fancy-title.bg-on {
  min-height: calc(var(--the7-base-font-size) * 2);
  line-height: calc(var(--the7-base-font-size) * 2);
}
.text-small .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-text-small-font-size) * 2);
  line-height: calc(var(--the7-text-small-font-size) * 2);
}
.text-big .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-text-big-font-size) * 2);
  line-height: calc(var(--the7-text-big-font-size) * 2);
}
.h1-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h1-font-size)*2);
  line-height: calc(var(--the7-h1-font-size)*2);
}
.h2-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h2-font-size)*2);
  line-height: calc(var(--the7-h2-font-size)*2);
}
.h3-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h3-font-size)*2);
  line-height: calc(var(--the7-h3-font-size)*2);
}
.h4-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h4-font-size)*2);
  line-height: calc(var(--the7-h4-font-size)*2);
}
.h5-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h5-font-size)*2);
  line-height: calc(var(--the7-h5-font-size)*2);
}
.h6-size .dt-fancy-title.bg-on {
  min-height: calc(var(--the7-h6-font-size)*2);
  line-height: calc(var(--the7-h6-font-size)*2);
}
.shortcode-tooltip {
  color: var(--the7-links-color);
  border-color: var(--the7-links-color);
}
.borderframe .shortcode-single-image {
  border-color: var(--the7-divider-color);
}
.sidebar .borderframe .shortcode-single-image,
.sidebar-content .borderframe .shortcode-single-image {
  border-color: rgba(133,134,140,0.15);
}
.footer .borderframe .shortcode-single-image {
  border-color: rgba(255,255,255,0.15);
}
.shortcode-single-caption {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
  color: var(--the7-secondary-text-color);
}
.sidebar-content .shortcode-single-caption {
  color: rgba(133,134,140,0.5);
}
.footer .shortcode-single-caption {
  color: rgba(255,255,255,0.5);
}
.borderframe.br-standard .shortcode-single-image {
  background-color: var(--the7-content-boxes-bg);
}
.shadow-element-decoration .borderframe.br-standard .shortcode-single-image {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.outline-element-decoration .borderframe.br-standard .shortcode-single-image {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.frame-on {
  border-color: var(--the7-divider-color);
}
.sidebar .frame-on,
.sidebar-content .frame-on {
  border-color: rgba(133,134,140,0.15);
}
.footer .frame-on {
  border-color: rgba(255,255,255,0.15);
}
.frame-on.frame-fancy {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .frame-on.frame-fancy {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .frame-on.frame-fancy {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.shortcode-single-image-wrap .rollover-icon {
  color: var(--the7-accent-color);
  border-color: var(--the7-accent-color);
}
.dt-icon-bg-on.shortcode-single-image-wrap .rollover-icon {
  background-color: var(--the7-accent-color);
}
.list-divider li {
  border-color: var(--the7-divider-color);
}
.sidebar .list-divider li,
.sidebar-content .list-divider li {
  border-color: rgba(133,134,140,0.15);
}
.footer .list-divider li {
  border-color: rgba(255,255,255,0.15);
}
.standard-arrow.list-divider.bullet-top li:not(:first-child):before,
.standard-arrow.list-divider.bullet-top li:not(:first-child):after {
  margin-top: 14px;
}
.standard-arrow.bullet-top li:before,
.standard-arrow.bullet-top li:after {
  margin-top: 5px;
}
.standard-number-list li:before,
.standard-arrow li:before {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.content .soc-ico a:before {
  background-color: rgba(82,82,87,0.15);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .soc-ico a:before {
  background-color: rgba(255,255,255,0.15);
}
.sidebar .soc-ico a:before,
.sidebar-content .soc-ico a:before {
  background-color: rgba(133,134,140,0.15);
}
.footer .soc-ico a:before {
  background-color: rgba(255,255,255,0.15);
}
.dt-shortcode-soc-icons a.dt-icon-bg-on:before,
.dt-shortcode-icon.dt-icon-bg-on .icon-inner:before,
.text-icon.dt-icon-bg-on .icon-inner:before {
  border-color: var(--the7-accent-color);
  background-color: var(--the7-accent-color);
}
.content .dt-icon-bg-on.dt-team-shortcode.dt-icon-bg-on .soc-ico a:before {
  background-color: var(--the7-accent-color);
}
.content .dt-icon-border-on.dt-team-shortcode.dt-icon-bg-on .soc-ico a:before {
  border-color: var(--the7-accent-color);
}
.dt-shortcode-soc-icons a.dt-icon-border-on:before,
.dt-shortcode-soc-icons a.dt-icon-hover-border-on:after,
.dt-shortcode-icon.dt-icon-border-on:before,
.dt-shortcode-icon.dt-icon-hover-border-on:after,
.text-icon.dt-icon-border-on:before,
.text-icon.dt-icon-hover-border-on:after {
  border-color: var(--the7-accent-color);
}
#main .soc-ico a:hover,
#footer .soc-ico a:hover {
  background-color: transparent;
  box-shadow: none;
}
#main .soc-ico a:after,
#footer .soc-ico a:after {
  background-color: var(--the7-accent-color);
  box-shadow: none;
}
.accent-gradient #main .soc-ico a:hover,
.accent-gradient #footer .soc-ico a:hover {
  background: none;
}
.dt-shortcode-soc-icons a.dt-icon-hover-bg-on:after,
.dt-shortcode-icon.dt-icon-hover-bg-on .icon-inner:after,
.text-icon.dt-icon-hover-bg-on .icon-inner:after {
  border-color: var(--the7-accent-color);
  background-color: var(--the7-accent-color);
}
.content .dt-icon-hover-bg-on.dt-team-shortcode.dt-icon-bg-on .soc-ico a:after {
  background-color: var(--the7-accent-color);
}
.content .dt-icon-border-hover-on.dt-team-shortcode.dt-icon-bg-on .soc-ico a:after {
  border-color: var(--the7-accent-color);
}
.dt-shortcode-soc-icons a .soc-font-icon,
#main .dt-team-shortcode .soc-ico a .soc-font-icon,
.dt-shortcode-icon .soc-icon,
.text-icon .soc-icon {
  color: var(--the7-accent-color);
}
.content .soc-ico a:not(:hover) .soc-font-icon {
  color: var(--the7-base-color);
}
.sidebar .soc-ico a:not(:hover) .soc-font-icon,
.sidebar-content .soc-ico a:not(:hover) .soc-font-icon {
  color: #85868c;
}
.footer .soc-ico a:not(:hover) .soc-font-icon {
  color: #ffffff;
}
blockquote:not(.shortcode-blockquote):not(.elementor-blockquote),
blockquote.shortcode-blockquote.block-style-widget,
.block-style-widget {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration blockquote:not(.shortcode-blockquote):not(.elementor-blockquote),
.outline-element-decoration .block-style-widget {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration blockquote:not(.shortcode-blockquote):not(.elementor-blockquote),
.shadow-element-decoration .block-style-widget {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.shortcode-pullquote:after,
.shortcode-action-bg:before {
  color: #fff;
  background-color: var(--the7-accent-color);
}
blockquote.shortcode-blockquote {
  border-color: var(--the7-divider-color);
}
.sidebar blockquote.shortcode-blockquote,
.sidebar-content blockquote.shortcode-blockquote {
  border-color: rgba(133,134,140,0.15);
}
.footer blockquote.shortcode-blockquote {
  border-color: rgba(255,255,255,0.15);
}
blockquote:not(.elementor-blockquote),
blockquote:not(.elementor-blockquote) *,
.shortcode-pullquote,
.wp-block-quote cite {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget blockquote:not(.elementor-blockquote),
.dt-mega-menu .dt-mega-parent .sidebar-content .widget blockquote:not(.elementor-blockquote) *,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .shortcode-pullquote,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .wp-block-quote cite {
  color: #ffffff;
}
.sidebar-content blockquote:not(.elementor-blockquote),
.sidebar-content blockquote:not(.elementor-blockquote) *,
.sidebar-content .shortcode-pullquote,
.sidebar-content .wp-block-quote cite {
  color: #333333;
}
.footer blockquote:not(.elementor-blockquote),
.footer blockquote:not(.elementor-blockquote) *,
.footer .shortcode-pullquote,
.footer .wp-block-quote cite {
  color: #ffffff;
}
blockquote:not(.wp-block-quote):not(.elementor-blockquote) {
  color: var(--the7-h5-color);
  font: var(--the7-h5-font);
  text-transform: var(--the7-h5-text-transform);
}
.plain-bg {
  border-color: var(--the7-divider-color);
}
.sidebar .plain-bg,
.sidebar-content .plain-bg {
  border-color: rgba(133,134,140,0.15);
}
.footer .plain-bg {
  border-color: rgba(255,255,255,0.15);
}
.slider-wrapper.arrows-accent .owl-prev i,
.slider-wrapper.arrows-accent .owl-next i {
  background-color: rgba(0,0,97,0.9);
}
.elementor-widget-the7_photo-scroller .scroller-arrow i,
.elementor-widget-the7_photo-scroller .scroller-arrow:hover i {
  color: var(--the7-accent-color);
}
.elementor-widget-the7_photo-scroller .scroller-arrow svg,
.elementor-widget-the7_photo-scroller .scroller-arrow:hover svg {
  color: var(--the7-accent-color);
  fill: var(--the7-accent-color);
}
.slider-wrapper.arrows-accent .owl-prev:hover i,
.slider-wrapper.arrows-accent .owl-next:hover i {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.dt-owl-item .post.bg-on {
  box-shadow: none;
}
.dt-owl-item .post.bg-on:before {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .dt-owl-item .post.bg-on:before {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .dt-owl-item .post.bg-on:before {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
input::-webkit-input-placeholder {
  color: var(--the7-input-color);
  opacity: var(--the7-form-placeholder-opacity,0.5);
}
input:-moz-placeholder {
  color: var(--the7-input-color);
  opacity: var(--the7-form-placeholder-opacity,0.5);
}
textarea::-webkit-input-placeholder {
  color: var(--the7-input-color);
  opacity: var(--the7-form-placeholder-opacity,0.5);
}
textarea:-moz-placeholder {
  color: var(--the7-input-color);
  opacity: var(--the7-form-placeholder-opacity,0.5);
}
input:focus::-webkit-input-placeholder {
  opacity: var(--the7-form-placeholder-opacity-focus,0.15);
}
input:focus:-moz-placeholder {
  opacity: var(--the7-form-placeholder-opacity-focus,0.15);
}
textarea:focus::-webkit-input-placeholder {
  opacity: var(--the7-form-placeholder-opacity-focus,0.15);
}
textarea:focus:-moz-placeholder {
  opacity: var(--the7-form-placeholder-opacity-focus,0.15);
}
.form-fields label,
.comment-form label:not([for="wp-comment-cookies-consent"]) {
  color: var(--the7-input-color);
}
input[type="text"],
.widget_search input[type="text"].searchform-s,
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="email"],
input[type="number"],
input[type="date"],
input[type="range"],
input[type="password"],
select,
textarea,
.wpcf7-number,
.woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select,
.the7-elementor-product-comments .elementor-field-textual,
.elementor-field-group .elementor-field-textual,
.elementor-field-group .elementor-select-wrapper select {
  color: var(--the7-input-color);
  font: var(--the7-form-md-font);
  border-style: var(--the7-form-border);
  border-top-width: var(--the7-top-input-border-width);
  border-right-width: var(--the7-right-input-border-width);
  border-bottom-width: var(--the7-bottom-input-border-width);
  border-left-width: var(--the7-left-input-border-width);
  padding: var(--the7-top-input-padding) var(--the7-right-input-padding) var(--the7-bottom-input-padding) var(--the7-left-input-padding);
  border-color: var(--the7-input-border-color);
  background-color: var(--the7-input-bg-color);
  border-radius: var(--the7-input-border-radius);
  box-shadow: var(--the7-form-shadow);
  transition: all 0.3s ease;
}
input[type="text"]:focus,
.widget_search input[type="text"].searchform-s:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="range"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus,
.wpcf7-number:focus,
.woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select:focus,
.the7-elementor-product-comments .elementor-field-textual:focus,
.elementor-field-group .elementor-field-textual:focus,
.elementor-field-group .elementor-select-wrapper select:focus {
  box-shadow: var(--the7-form-shadow-focus);
}
.content input[type="text"],
.content .widget_search input[type="text"].searchform-s,
.content input[type="search"],
.content input[type="tel"],
.content input[type="url"],
.content input[type="email"],
.content input[type="number"],
.content input[type="date"],
.content input[type="range"],
.content input[type="password"],
.content select,
.content textarea,
.content .wpcf7-number,
.content .woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select,
.content .the7-elementor-product-comments .elementor-field-textual,
.content .elementor-field-group .elementor-field-textual,
.content .elementor-field-group .elementor-select-wrapper select {
  font-size: var(--the7-form-md-font-size);
  line-height: var(--the7-form-md-line-height);
}
.content .elementor-field-group .elementor-select-wrapper select {
  font-size: inherit;
  line-height: inherit;
}
input:-webkit-autofill {
  transition: background-color 99999s ease-in-out 0s;
}
input[type="text"],
.widget_search input[type="text"].searchform-s,
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="email"],
input[type="number"],
input[type="date"],
input[type="range"],
input[type="password"],
select,
.wpcf7-number,
.tinvwl-input-group .form-control,
.woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select {
  min-height: var(--the7-input-height);
}
.formError + input,
.formError + textarea {
  border-color: #f55b5f;
}
select:focus,
.woocommerce div.product.elementor form.cart table.variations td.value:focus:before {
  outline: 0px solid transparent;
  box-shadow: 0 0 0 1px rgba(0,0,0,0.1) inset;
}
.widget .dt-form .dt-btn,
.dt-form .dt-btn,
.comment-form .dt-btn {
  font: var(--the7-btn-m-font);
  text-transform: var(--the7-btn-m-text-transform);
  letter-spacing: var(--the7-btn-m-letter-spacing);
  word-spacing: var(--the7-btn-m-word-spacing);
  border-radius: var(--the7-btn-m-border-radius);
  padding: var(--the7-btn-m-padding);
}
input[type="submit"]:not([name="update_cart"]):not(.woocommerce-widget-layered-nav-dropdown__submit),
.post-password-form input[type="submit"],
.mc4wp-form input[type="submit"],
div.mc4wp-form button[type="submit"],
.tml-submit-wrap input[type="submit"],
.wpcf7-form input[type="submit"] {
  font: var(--the7-btn-m-font);
  text-transform: var(--the7-btn-m-text-transform);
  letter-spacing: var(--the7-btn-m-letter-spacing);
  word-spacing: var(--the7-btn-m-word-spacing);
  border-radius: var(--the7-btn-m-border-radius);
  padding: var(--the7-btn-m-padding);
  min-width: var(--the7-btn-m-min-width);
  min-height: var(--the7-btn-m-min-height);
}
.popup-message-style .parentFormundefined,
.inline-message-style .parentFormundefined,
.popup-message-style .wpcf7-mail-sent-ok,
.inline-message-style .wpcf7-mail-sent-ok,
.popup-message-style .wpcf7-validation-errors,
.inline-message-style .wpcf7-validation-errors,
.popup-message-style .wpcf7-response-output,
.inline-message-style .wpcf7-response-output {
  background: var(--the7-accent-color);
}
.parentFormundefined .formErrorContent,
.wpcf7-mail-sent-ok .formErrorContent,
.wpcf7-validation-errors .formErrorContent,
.wpcf7-response-output .formErrorContent {
  font: var(--the7-base-font-big);
}
#page .parentFormundefined,
.popup-message-style .parentFormundefined,
.inline-message-style .parentFormundefined,
#page .wpcf7-mail-sent-ok,
.popup-message-style .wpcf7-mail-sent-ok,
.inline-message-style .wpcf7-mail-sent-ok,
#page .wpcf7-validation-errors,
.popup-message-style .wpcf7-validation-errors,
.inline-message-style .wpcf7-validation-errors,
#page .wpcf7-response-output,
.popup-message-style .wpcf7-response-output,
.inline-message-style .wpcf7-response-output {
  color: #ffffff !important;
  border: none;
}
#page .parentFormundefined .formErrorContent,
#page .parentFormundefined .formErrorContent a,
#page .parentFormundefined .close-message
		#page .parentFormundefined a,
.popup-message-style .parentFormundefined .formErrorContent,
.popup-message-style .parentFormundefined .formErrorContent a,
.popup-message-style .parentFormundefined .close-message
		.popup-message-style .parentFormundefined a,
.inline-message-style .parentFormundefined .formErrorContent,
.inline-message-style .parentFormundefined .formErrorContent a,
.inline-message-style .parentFormundefined .close-message
		.inline-message-style .parentFormundefined a,
#page .wpcf7-mail-sent-ok .formErrorContent,
#page .wpcf7-mail-sent-ok .formErrorContent a,
#page .wpcf7-mail-sent-ok .close-message
		#page .wpcf7-mail-sent-ok a,
.popup-message-style .wpcf7-mail-sent-ok .formErrorContent,
.popup-message-style .wpcf7-mail-sent-ok .formErrorContent a,
.popup-message-style .wpcf7-mail-sent-ok .close-message
		.popup-message-style .wpcf7-mail-sent-ok a,
.inline-message-style .wpcf7-mail-sent-ok .formErrorContent,
.inline-message-style .wpcf7-mail-sent-ok .formErrorContent a,
.inline-message-style .wpcf7-mail-sent-ok .close-message
		.inline-message-style .wpcf7-mail-sent-ok a,
#page .wpcf7-validation-errors .formErrorContent,
#page .wpcf7-validation-errors .formErrorContent a,
#page .wpcf7-validation-errors .close-message
		#page .wpcf7-validation-errors a,
.popup-message-style .wpcf7-validation-errors .formErrorContent,
.popup-message-style .wpcf7-validation-errors .formErrorContent a,
.popup-message-style .wpcf7-validation-errors .close-message
		.popup-message-style .wpcf7-validation-errors a,
.inline-message-style .wpcf7-validation-errors .formErrorContent,
.inline-message-style .wpcf7-validation-errors .formErrorContent a,
.inline-message-style .wpcf7-validation-errors .close-message
		.inline-message-style .wpcf7-validation-errors a,
#page .wpcf7-response-output .formErrorContent,
#page .wpcf7-response-output .formErrorContent a,
#page .wpcf7-response-output .close-message
		#page .wpcf7-response-output a,
.popup-message-style .wpcf7-response-output .formErrorContent,
.popup-message-style .wpcf7-response-output .formErrorContent a,
.popup-message-style .wpcf7-response-output .close-message
		.popup-message-style .wpcf7-response-output a,
.inline-message-style .wpcf7-response-output .formErrorContent,
.inline-message-style .wpcf7-response-output .formErrorContent a,
.inline-message-style .wpcf7-response-output .close-message
		.inline-message-style .wpcf7-response-output a {
  color: #ffffff;
}
#page .parentFormundefined:before,
.popup-message-style .parentFormundefined:before,
.inline-message-style .parentFormundefined:before,
#page .wpcf7-mail-sent-ok:before,
.popup-message-style .wpcf7-mail-sent-ok:before,
.inline-message-style .wpcf7-mail-sent-ok:before,
#page .wpcf7-validation-errors:before,
.popup-message-style .wpcf7-validation-errors:before,
.inline-message-style .wpcf7-validation-errors:before,
#page .wpcf7-response-output:before,
.popup-message-style .wpcf7-response-output:before,
.inline-message-style .wpcf7-response-output:before {
  color: #ffffff;
}
.comment-form-cookies-consent label,
.form-terms-text {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
select:not(.elementor-field),
.woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select:not(.elementor-field) {
  -webkit-appearance: none !important;
  -moz-appearance: none;
  background-image: linear-gradient(45deg,transparent 50%,var(--the7-input-color) 50%), linear-gradient(135deg,var(--the7-input-color) 50%,transparent 50%);
  background-position: calc(100% - var(--the7-right-input-padding) - 5px) 50%, calc(100% - var(--the7-right-input-padding)) 50%, 100% 0;
  background-size: 5px 5px, 5px 5px, 2.5em 2.5em;
  background-repeat: no-repeat;
  padding-right: calc(var(--the7-right-input-padding) + 15px);
}
.woocommerce div.elementor .the7-elementor-widget form.cart table.variations td.value select:not(.elementor-field) {
  background-image: linear-gradient(45deg,transparent 50%,var(--the7-input-color) 50%), linear-gradient(135deg,var(--the7-input-color) 50%,transparent 50%);
  background-position: calc(100% - var(--the7-right-input-padding) - 4px) 50%, calc(100% - var(--the7-right-input-padding)) 50%, 100% 0;
  background-size: 4px 4px, 4px 4px, 2.5em 2.5em;
  background-repeat: no-repeat;
}
.dt-arrow-border-on .owl-carousel .owl-nav div:not(:hover):before {
  border-color: var(--the7-accent-color);
}
.dt-arrow-hover-border-on.owl-carousel .owl-nav div:hover:after,
.dt-arrow-border-on.owl-carousel .owl-nav div:hover:after {
  border-color: var(--the7-accent-color);
}
.arrows-bg-on.owl-carousel .owl-nav div:before {
  background-color: var(--the7-accent-color);
  border-color: var(--the7-accent-color);
}
.arrows-hover-bg-on.owl-carousel .owl-nav div:after {
  background-color: var(--the7-accent-color);
}
.owl-carousel .owl-nav svg {
  fill: var(--the7-accent-color);
  color: var(--the7-accent-color);
}
.owl-carousel .owl-nav i,
.owl-carousel .owl-nav i:before {
  color: var(--the7-accent-color);
}
.owl-carousel .owl-nav div:hover svg {
  fill: var(--the7-accent-color);
  color: var(--the7-accent-color);
}
.owl-carousel .owl-nav div:hover i {
  color: var(--the7-accent-color);
}
.owl-carousel.bullets-scale-up .owl-dot span,
.owl-carousel.bullets-scale-up .owl-dot:not(.active):hover span,
.owl-carousel.bullets-scale-up .owl-dot.active span {
  background: var(--the7-accent-color);
}
.owl-carousel.bullets-stroke .owl-dot:not(.active) span,
.owl-carousel.bullets-stroke .owl-dot:not(.active):hover span {
  background: var(--the7-accent-color);
}
.owl-carousel.bullets-stroke .owl-dot.active span {
  box-shadow: 0 0 0 2px var(--the7-accent-color);
}
.owl-carousel.bullets-fill-in .owl-dot span,
.owl-carousel.bullets-fill-in .owl-dot:not(.active):hover span,
.owl-carousel.bullets-fill-in .owl-dot.active span {
  box-shadow: inset 0 0 0 2px var(--the7-accent-color);
}
.owl-carousel.bullets-small-dot-stroke .owl-dot span,
.owl-carousel.bullets-small-dot-stroke .owl-dot:not(.active):hover span,
.owl-carousel.bullets-small-dot-stroke .owl-dot.active span {
  background: var(--the7-accent-color);
}
.owl-carousel.bullets-small-dot-stroke .owl-dot.active {
  box-shadow: 0 0 0 2px var(--the7-accent-color);
}
.owl-carousel.bullets-ubax .owl-dot span,
.owl-carousel.bullets-ubax .owl-dot:not(.active):hover span {
  background: var(--the7-accent-color);
}
.owl-carousel.bullets-ubax .owl-dot.active span {
  border-color: var(--the7-accent-color);
}
.owl-carousel.bullets-etefu .owl-dot span,
.owl-carousel.bullets-etefu .owl-dot:not(.active):hover span,
.owl-carousel.bullets-etefu .owl-dot span:before {
  background: var(--the7-accent-color);
}
.dt-owl-item-icon i {
  color: var(--the7-accent-color);
}
.dt-owl-item-icon svg {
  fill: var(--the7-accent-color);
  color: var(--the7-accent-color);
}
.text-and-icon-carousel .owl-nav i,
.testimonials-carousel .owl-nav i,
.text-and-icon-carousel .owl-nav a:hover i,
.testimonials-carousel .owl-nav a:hover i,
.text-and-icon-carousel .owl-nav i:before,
.testimonials-carousel .owl-nav i:before {
  background: none;
  color: var(--the7-accent-color);
}
.dt-owl-item-icon:before,
.dt-owl-item-icon:after,
.text-and-icon-carousel .owl-nav a,
.text-and-icon-carousel .owl-nav a:hover,
.testimonials-carousel .owl-nav a,
.testimonials-carousel .owl-nav a:hover {
  border-color: var(--the7-accent-color);
}
.full-width-wrap .dt-shortcode .filter {
  width: 1300px;
  margin-left: auto;
  margin-right: auto;
}
.filter {
  margin-bottom: 50px;
}
.paginator {
  margin-top: 50px;
}
.filter-categories a,
.filter-categories .customSelect {
  font:   700 13px / 17px "Roboto", Helvetica, Arial, Verdana, sans-serif;
  text-transform: uppercase;
}
.filter a,
.filter a *,
.filter .customSelect {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .filter a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .filter a *,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .filter .customSelect {
  color: #ffffff;
}
.sidebar-content .filter a,
.sidebar-content .filter a *,
.sidebar-content .filter .customSelect {
  color: #333333;
}
.footer .filter a,
.footer .filter a *,
.footer .filter .customSelect {
  color: #ffffff;
}
.filter svg {
  fill: var(--the7-h5-color);
  color: var(--the7-h5-color);
}
.filter-categories a,
.filter-extras > div {
  padding: 8px 14px 7px 14px;
  margin: 0px 3px 0px 3px;
}
.filter:not(.filter-bg-decoration):not(.filter-underline-decoration) .filter-categories a:hover,
.filter:not(.filter-bg-decoration):not(.filter-underline-decoration) .filter-categories a.act {
  color: var(--the7-accent-color);
}
.filter-bg-decoration .filter-categories a {
  border-radius: 1px;
}
.filter-bg-decoration .filter-categories a:not(.act):hover {
  color: var(--the7-accent-color);
}
.filter-bg-decoration .filter-categories a:after {
  display: none;
}
.filter-bg-decoration .filter-categories a.act {
  color: #fff;
}
.filter-bg-decoration .filter-categories a.act {
  color: #fff;
  background-color: var(--the7-accent-color);
  border-radius: 1px;
}
.filter-underline-decoration .filter-categories a:after {
  color: #fff;
  background-color: var(--the7-accent-color);
  height: 2px;
}
.filter-switch {
  background-color: rgba(0,0,97,0.2);
}
.filter:not(.filter-bg-decoration) .filter-categories a.act:after,
.filter-switch-toggle {
  background-color: var(--the7-accent-color);
}
.paginator a,
.paginator a * {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .paginator a,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .paginator a * {
  color: #ffffff;
}
.sidebar-content .paginator a,
.sidebar-content .paginator a * {
  color: #333333;
}
.footer .paginator a,
.footer .paginator a * {
  color: #ffffff;
}
.paginator .button-caption:before {
  background: var(--the7-title-color);
}
.paginator .button-load-more,
.paginator .loading-ready {
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.paginator .button-load-more {
  border-color: rgba(0,0,97,0.1);
  font-size: var(--the7-base-font-size);
  line-height: var(--the7-base-line-height);
}
.paginator .button-load-more.animate-load,
.paginator .button-load-more:hover {
  border-color: rgba(0,0,97,0.21);
}
.paginator .button-load-more.animate-load .stick,
.paginator .button-load-more.button-lazy-loading .stick {
  border-top-color: var(--the7-h5-color);
  border-right-color: var(--the7-h5-color);
}
.paginator:not(.paginator-more-button) a {
  font-size: var(--the7-text-big-font-size);
  line-height: var(--the7-text-big-line-height);
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .paginator:not(.paginator-more-button) a {
  color: #ffffff;
}
.sidebar-content .paginator:not(.paginator-more-button) a {
  color: #333333;
}
.footer .paginator:not(.paginator-more-button) a {
  color: #ffffff;
}
.paginator:not(.paginator-more-button) a:after {
  background-color: var(--the7-accent-color);
}
#footer.solid-bg {
  background: #000061 none repeat center top;
}
#footer.footer-outline-decoration {
  border-top: 1px solid rgba(129,215,66,0.96);
}
.wf-container-footer,
.footer.full-width-line {
  border-top: 1px solid #000061;
}
.header-side-left.footer-overlap.boxed-layout:not(.sticky-header) #footer,
.header-side-right.footer-overlap.boxed-layout:not(.sticky-header) #footer {
  max-width: 1640px;
}
#footer .wf-container-footer {
  padding-top: 60px;
  padding-bottom: 25px;
}
#footer .wf-container {
  margin: 0 -25px;
}
.footer .widget {
  color: #ffffff;
  padding: 0 25px;
}
.footer .widget,
.footer .tp_recent_tweets li {
  font: var(--the7-widget-content-font);
  text-transform: var(--the7-widget-content-text-transform);
  letter-spacing: var(--the7-widget-content-letter-spacing);
  text-decoration: var(--the7-widget-content-text-decoration);
}
#footer.full-width > .wf-wrap,
#footer.full-width #bottom-bar > .wf-wrap {
  width: 100%;
}
#footer > .wf-wrap,
#footer #bottom-bar > .wf-wrap {
  padding: 0 50px 0 50px;
}
#bottom-bar.solid-bg {
  background: rgba(255,255,255,0.12) none repeat center top;
}
.wf-container-bottom,
#bottom-bar.full-width-line {
  border-top: 1px solid rgba(255,255,255,0.12);
}
.wf-container-bottom {
  padding: 10px 0 10px 0;
  min-height: 60px;
}
#bottom-bar {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
  color: #ffffff;
}
#bottom-bar .mini-nav li:before {
  border-left-color: #38393a;
}
.footer-sub-nav > li.act > a .menu-text,
.footer-sub-nav > li:not(.act):hover > a .menu-text,
.footer-sub-nav > li.act > a .subtitle-text,
.footer-sub-nav > li:not(.act):hover > a .subtitle-text {
  color: var(--the7-accent-color);
}
#bottom-bar a,
#bottom-bar .mini-nav .customSelect,
#bottom-bar .menu-select {
  color: #ffffff;
}
#bottom-bar .menu-select svg {
  fill: #ffffff;
  color: #ffffff;
}
#bottom-bar .mini-search,
#bottom-bar .mini-contacts:before {
  background-color: rgba(255,255,255,0);
}
#branding-bottom {
  padding: 0px 10px 0px 0px;
}
.no-avatar,
.testim-no-avatar {
  border: 1px solid;
  border-color: var(--the7-divider-color);
}
.sidebar .no-avatar,
.sidebar-content .no-avatar,
.sidebar .testim-no-avatar,
.sidebar-content .testim-no-avatar {
  border-color: rgba(133,134,140,0.15);
}
.footer .no-avatar,
.footer .testim-no-avatar {
  border-color: rgba(255,255,255,0.15);
}
.testim-no-avatar svg {
  fill: var(--the7-divider-color);
  color: var(--the7-divider-color);
}
.no-avatar,
.no-avatar svg,
.comment-list .no-avatar:after {
  color: var(--the7-divider-color);
  fill: var(--the7-divider-color);
}
.sidebar .no-avatar,
.sidebar-content .no-avatar,
.sidebar .no-avatar svg,
.sidebar-content .no-avatar svg,
.sidebar .comment-list .no-avatar:after,
.sidebar-content .comment-list .no-avatar:after {
  color: rgba(133,134,140,0.15);
}
.footer .no-avatar,
.footer .no-avatar svg,
.footer .comment-list .no-avatar:after {
  color: rgba(255,255,255,0.15);
}
.scroll-top {
  border-radius: 1px;
}
input[type="text"],
input[type="tel"],
input[type="url"],
input[type="email"],
input[type="number"],
input[type="date"],
input[type="range"],
input[type="password"],
select,
textarea {
  font: var(--the7-form-md-font);
  letter-spacing: var(--the7-form-md-letter-spacing);
  text-transform: var(--the7-form-md-text-transform);
  text-decoration: var(--the7-base-text-decoration);
}
.content .wpcf7-select {
  line-height: calc(var(--the7-input-height) - 14px);
  min-width: 200px;
}
a.clear-form,
#cancel-comment-reply-link {
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
  color: var(--the7-secondary-text-color);
}
.sidebar-content a.clear-form,
.sidebar-content #cancel-comment-reply-link {
  color: rgba(133,134,140,0.5);
}
.footer a.clear-form,
.footer #cancel-comment-reply-link {
  color: rgba(255,255,255,0.5);
}
.customSelect {
  font: var(--the7-base-font);
  background-color: rgba(82,82,87,0.08);
}
.sidebar .customSelect,
.sidebar-content .customSelect {
  background-color: rgba(133,134,140,0.08);
}
.footer .customSelect {
  background-color: rgba(255,255,255,0.08);
}
.shortcode-code {
  background-color: var(--the7-content-boxes-bg);
}
.shadow-element-decoration .shortcode-code {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.outline-element-decoration .shortcode-code {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.content table,
.content td,
.content th {
  border-color: var(--the7-divider-color);
}
.dt-highlight {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.post-content .text-secondary {
  color: rgba(82,82,87,0.5);
}
.sidebar-content .post-content .text-secondary {
  color: rgba(133,134,140,0.5);
}
.footer .post-content .text-secondary {
  color: rgba(255,255,255,0.5);
}
#page .items-grid .post-content a:hover,
#page .post-content a:hover {
  color: var(--the7-accent-color);
}
.shortcode-banner-bg > * {
  line-height: var(--the7-base-line-height);
}
.layzr-bg {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(82,82,87,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
}
.sidebar .layzr-bg,
.sidebar-content .layzr-bg {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(133,134,140,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
}
.footer .layzr-bg {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(255,255,255,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
}
.layzr-bg:not(.layzr-bg-transparent) {
  background-color: rgba(82,82,87,0.05);
}
.sidebar .layzr-bg:not(.layzr-bg-transparent),
.sidebar-content .layzr-bg:not(.layzr-bg-transparent) {
  background-color: rgba(133,134,140,0.05);
}
.footer .layzr-bg:not(.layzr-bg-transparent) {
  background-color: rgba(255,255,255,0.05);
}
#page .woocom-project .layzr-bg:not(.layzr-bg-transparent) {
  background-color: transparent;
}
.pswp button.pswp__button--arrow--left,
.pswp button.pswp__button--arrow--right,
.pswp .pswp__button--arrow--left:before,
.pswp .pswp__button--arrow--right:before {
  font-size: 62px;
  line-height: 62px;
  min-height: 62px;
}
.rollover i,
.post-rollover i,
.rollover-video i,
.enable-bg-rollover .post-thumbnail-rollover:after,
.enable-bg-rollover .gallery-rollover,
.enable-bg-rollover.hover-scale figure:after {
  background-color: rgba(38,6,51,0.35);
  background: rgba(38,6,51,0.35);
  background: -webkit-linear-gradient(135deg, rgba(38,6,51,0.35) 30%, rgba(236,97,93,0.35) 100%);
  background: linear-gradient(135deg, rgba(38,6,51,0.35) 30%, rgba(236,97,93,0.35) 100%);
}
.hover-style-two:not(.effect-layla):not(.effect-bubba):not(.effect-sarah) .rollover-content,
.buttons-on-img .rollover-content,
.albums .rollover-thumbnails-on i,
.dt-albums-shortcode .rollover-thumbnails-on i,
.rollover-buttons-on i,
.hover-scale .rollover-project:after,
.hover-style-two.content-align-bottom .rollover-project:after,
.portfolio-shortcode.enable-bg-rollover .post-thumbnail-rollover:after,
.albums-shortcode.enable-bg-rollover .post-thumbnail-rollover:after,
.enable-bg-rollover.hover-scale article:after {
  background-color: rgba(38,6,51,0.8);
  background: rgba(38,6,51,0.8);
  background: -webkit-linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
  background: linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
}
.effect-bubba .rollover-project,
.effect-layla .rollover-project,
.effect-sarah .rollover-project {
  background-color: rgba(38,6,51,0.8);
  background: rgba(38,6,51,0.8);
  background: -webkit-linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
  background: linear-gradient(135deg, rgba(38,6,51,0.8) 30%, rgba(236,97,93,0.8) 100%);
}
.effect-bubba:not(.hover-color-static) .rollover-project:hover > a > img,
.effect-layla:not(.hover-color-static) .rollover-project:hover > a > img,
.effect-sarah:not(.hover-color-static) .rollover-project:hover > a > img {
  opacity: 0.3;
}
.hover-color-static:not(.effect-layla):not(.effect-bubba):not(.effect-sarah) .rollover-content,
.accent-gradient .hover-color-static:not(.effect-layla):not(.effect-bubba):not(.effect-sarah) .rollover-content,
.hover-color-static.hover-scale .rollover-project:after,
.hover-color-static.hover-style-two.content-align-bottom .rollover-project:after {
  background-color: rgba(0,0,0,0.65);
  background-image: none;
}
.hover-color-static.effect-bubba .rollover-project,
.hover-color-static.effect-layla .rollover-project,
.hover-color-static.effect-sarah .rollover-project {
  background-color: #000000;
  background-image: none;
}
.hover-color-static .buttons-on-img i,
.hover-color-static .rollover i,
.hover-color-static .rollover-video i,
.hover-color-static .rollover.rollover-thumbnails-on i,
#page .hover-color-static .buttons-on-img .rollover-content {
  background-color: rgba(0,0,0,0.35);
  background-image: none;
}
.rollover-thumbnails span:first-child i {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.gallery-shortcode .gallery-zoom-ico,
.blog-shortcode .gallery-zoom-ico,
.blog-carousel-shortcode .gallery-zoom-ico {
  border-color: var(--the7-accent-color);
}
.gallery-shortcode .gallery-zoom-ico span,
.gallery-shortcode .gallery-zoom-ico:before,
.blog-shortcode .gallery-zoom-ico span,
.blog-shortcode .gallery-zoom-ico:before,
.blog-carousel-shortcode .gallery-zoom-ico span,
.blog-carousel-shortcode .gallery-zoom-ico:before {
  color: var(--the7-accent-color);
}
.dt-icon-bg-on.gallery-shortcode .gallery-zoom-ico,
.dt-icon-bg-on.blog-shortcode .gallery-zoom-ico,
.dt-icon-bg-on.blog-carousel-shortcode .gallery-zoom-ico {
  background-color: var(--the7-accent-color);
}
.albums-shortcode .album-zoom-ico {
  border-color: var(--the7-accent-color);
}
.albums-shortcode .album-zoom-ico span {
  color: var(--the7-accent-color);
}
.dt-icon-bg-on.albums-shortcode .album-zoom-ico {
  background-color: var(--the7-accent-color);
}
.rollover-content {
  line-height: var(--the7-base-line-height);
}
.rollover-content h2.entry-title {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .rollover-content h2.entry-title {
  color: #ffffff;
}
.sidebar-content .rollover-content h2.entry-title {
  color: #333333;
}
.footer .rollover-content h2.entry-title {
  color: #ffffff;
}
.portfolio-categories a,
.portfolio-categories a *,
.portfolio-categories span {
  color: var(--the7-secondary-text-color);
}
.gform_wrapper .gsection {
  border-bottom-style: solid !important;
  border-bottom-width: var(--the7-bottom-input-border-width);
  border-bottom-color: var(--the7-divider-color);
}
.gform_wrapper .ginput_complex label,
.gform_wrapper .gfield_time_hour label,
.gform_wrapper .gfield_time_minute label,
.gform_wrapper .gfield_date_month label,
.gform_wrapper .gfield_date_day label,
.gform_wrapper .gfield_date_year label,
.gform_wrapper .instruction {
  font-size: var(--the7-text-small-font-size);
  color: rgba(82,82,87,0.45);
}
#main .gform_wrapper span.ginput_total,
#main .gform_wrapper .gfield_required,
#main .gform_wrapper .ginput_left input:focus + label,
#main .gform_wrapper .ginput_right input:focus + label,
#main .gform_wrapper .ginput_full input:focus + label {
  color: var(--the7-accent-color);
}
.gform_wrapper h3.gform_title,
.gform_wrapper .gsection .gfield_label,
.gform_wrapper h2.gsection_title,
h3.gform_title,
form.mc4wp-form label {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .gform_wrapper h3.gform_title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .gform_wrapper .gsection .gfield_label,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .gform_wrapper h2.gsection_title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget h3.gform_title,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget form.mc4wp-form label {
  color: #ffffff;
}
.sidebar-content .gform_wrapper h3.gform_title,
.sidebar-content .gform_wrapper .gsection .gfield_label,
.sidebar-content .gform_wrapper h2.gsection_title,
.sidebar-content h3.gform_title,
.sidebar-content form.mc4wp-form label {
  color: #333333;
}
.footer .gform_wrapper h3.gform_title,
.footer .gform_wrapper .gsection .gfield_label,
.footer .gform_wrapper h2.gsection_title,
.footer h3.gform_title,
.footer form.mc4wp-form label {
  color: #ffffff;
}
h3.gform_title,
.gform_wrapper h3.gform_title {
  color: var(--the7-h3-color);
  font: var(--the7-h3-font);
  text-transform: var(--the7-h3-text-transform);
}
.gform_wrapper .gsection .gfield_label,
.gform_wrapper h2.gsection_title {
  color: var(--the7-h4-color);
  font: var(--the7-h4-font);
  text-transform: var(--the7-h4-text-transform);
}
#main .gform_wrapper textarea,
.gform_wrapper .top_label .gfield_label {
  font: var(--the7-base-font);
}
.ngg-albumoverview .ngg-album,
.wpb_separator.wpb_content_element,
.vc_text_separator {
  border-color: var(--the7-divider-color);
}
.sidebar .ngg-albumoverview .ngg-album,
.sidebar-content .ngg-albumoverview .ngg-album,
.sidebar .wpb_separator.wpb_content_element,
.sidebar-content .wpb_separator.wpb_content_element,
.sidebar .vc_text_separator,
.sidebar-content .vc_text_separator {
  border-color: rgba(133,134,140,0.15);
}
.footer .ngg-albumoverview .ngg-album,
.footer .wpb_separator.wpb_content_element,
.footer .vc_text_separator {
  border-color: rgba(255,255,255,0.15);
}
.mini-wpml {
  font:  normal 700 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
}
.branding .mini-wpml,
.mixed-header .mini-wpml,
.classic-header .mobile-mini-widgets .mini-wpml {
  font:    20px / 24px "Arial", Helvetica, Arial, Verdana, sans-serif;
}
.top-bar .mini-wpml {
  font:  500 13px / 19px "Overpass", Helvetica, Arial, Verdana, sans-serif;
}
.dt-mobile-header .mini-wpml {
  font:   500 13px / 17px "Overpass", Helvetica, Arial, Verdana, sans-serif;
}
.popup-message-style .wpcf7-validation-errors,
.popup-message-style .wpcf7-mail-sent-ok,
.popup-message-style .wpcf7-response-output {
  background-color: var(--the7-accent-color);
}
.wpcf7-validation-errors .wpcf7-not-valid-tip-text,
.wpcf7-validation-errors .wpcf7-valid-tip-text,
.wpcf7-mail-sent-ok .wpcf7-not-valid-tip-text,
.wpcf7-mail-sent-ok .wpcf7-valid-tip-text,
.wpcf7-response-output .wpcf7-not-valid-tip-text,
.wpcf7-response-output .wpcf7-valid-tip-text {
  font: var(--the7-base-font-big);
}
.asp_product_name {
  color: var(--the7-h3-color);
  font: var(--the7-h3-font);
  text-transform: var(--the7-h3-text-transform);
}
.transparent:not(.photo-scroller-album):not(.phantom-sticky) .masthead.line-decoration:not(.masthead-mobile-header) {
  border-bottom: 1px solid rgba(255,255,255,0.25);
}
.transparent:not(.photo-scroller-album) .masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on),
.transparent .sticky-on.masthead.masthead-mobile {
  border: none;
  box-shadow: none !important;
}
.transparent .masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .top-bar.line-content:after,
.transparent .masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .top-bar.full-width-line:after {
  border-bottom-color: rgba(255,255,255,0.25);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets > *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets .mini-nav > ul:not(.mini-sub-nav) > li > a *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mobile-mini-widgets .mini-nav > ul:not(.mini-sub-nav) > li > a *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets .text-area a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-nav > ul:not(.mini-sub-nav) > li > a:hover,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .submit,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .searchform > .search-icon i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-login .submit,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-login .submit i {
  color: #fff;
}
.dt-wpml.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-wpml .wpml-ls-current-language > a,
.dt-wpml.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-wpml .wpml-ls-legacy-list-horizontal a,
.dt-wpml.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-wpml .wpml-ls-legacy-list-vertical a {
  color: #fff;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .branding,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .branding .mini-widgets > *:not(.sub-nav),
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets > *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .submit,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .submit i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-nav .customSelectInner,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-nav .customSelectInner i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets .text-area a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-widgets .text-area *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mobile-mini-widgets > *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mobile-branding *,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-contacts * {
  color: #fff;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .submit:hover {
  color: rgba(255,255,255,0.7) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on:not(.sticky-top-line-on)) .popup-search .submit:hover i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on:not(.sticky-top-line-on)) .overlay-search .submit:hover i {
  color: rgba(255,255,255,0.7) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .classic-search input[type="text"] {
  background: rgba(255,255,255,0.25);
  border-color: rgba(255,255,255,0.3);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .animate-search-width input.field {
  background: rgba(255,255,255,0.25);
  border-color: rgba(255,255,255,0.3);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .animate-search-width input,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .classic-search input[type=text],
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .classic-search input::placeholder,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .animate-search-width input::placeholder {
  color: #fff !important;
}
.transparent .classic-header.content-width-line:not(.sticky-on) .navigation:before,
.transparent .classic-header.full-width-line:not(.sticky-on) .navigation:before,
.transparent .classic-header.bg-behind-menu:not(.sticky-on) .navigation:before {
  background-color: rgba(255,255,255,0.25);
  background-image: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li > a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li > a .menu-text,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li > a .subtitle-text,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li > a:hover span,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li.act > a > span {
  color: #fff !important;
  background: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li > a > span > span.menu-subtitle,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav > li:hover > a > span > span.menu-subtitle {
  color: rgba(255,255,255,0.5);
}
.light-preset-color.masthead:not(.side-header):not(#phantom):not(.sticky-on).dividers .main-nav > li:before,
.light-preset-color.masthead:not(.side-header):not(#phantom):not(.sticky-on).dividers.surround .main-nav > li:before,
.light-preset-color.masthead:not(.side-header):not(#phantom):not(.sticky-on).dividers .main-nav > li:last-child:after,
.light-preset-color.masthead:not(.side-header):not(#phantom):not(.sticky-on).dividers.surround .main-nav > li:last-child:after {
  border-color: rgba(255,255,255,0.25);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .hover-outline-decoration > li:not(.act):hover > a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .active-outline-decoration > li.act > a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .hover-outline-decoration > li.dt-hovered:not(.act) > a {
  border-color: rgba(255,255,255,0.25);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .hover-bg-decoration > li:not(.act) > a:hover,
.light-preset-color.accent-gradient.masthead:not(#phantom):not(.sticky-on) .hover-bg-decoration > li:not(.act) > a:hover,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .hover-bg-decoration > li.dt-hovered:not(.act) > a,
.light-preset-color.accent-gradient.masthead:not(#phantom):not(.sticky-on) .hover-bg-decoration > li.dt-hovered:not(.act) > a,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .active-bg-decoration > li.act > a,
.light-preset-color.accent-gradient.masthead:not(#phantom):not(.sticky-on) .active-bg-decoration > li.act > a {
  background-color: rgba(255,255,255,0.25);
  background-image: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .l-to-r-line > li > a i.underline,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .from-centre-line > li > a .menu-item-text:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .upwards-line > li > a .menu-item-text:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .downwards-line > li > a .menu-item-text:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .active-line-decoration > li.act > a .decoration-line,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .hover-line-decoration > li > a:hover .decoration-line,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .hover-line-decoration > li.dt-hovered:not(.act) > a .decoration-line,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .dt-mobile-menu-icon .lines,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .dt-mobile-menu-icon .lines:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .dt-mobile-menu-icon .lines:after {
  background-color: #ffffff;
  background-image: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .menu-line:after,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on) .menu-line:before {
  background: #ffffff !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).mobile-menu-icon-bg-on .dt-mobile-menu-icon:not(.floating-btn):before {
  background: rgba(255,255,255,0.3) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).mobile-menu-icon-hover-bg-on .dt-mobile-menu-icon:not(.floating-btn):after {
  background: rgba(255,255,255,0.25) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).mobile-menu-icon-hover-border-enable .dt-mobile-menu-icon:not(.floating-btn):after {
  border-color: rgba(255,255,255,0.25) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on):not(.animate-color-mobile-menu-icon):not(.two-line-mobile-menu-icon) .dt-mobile-menu-icon:not(.floating-btn):hover .menu-line {
  background: #ffffff !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).hamburger-bg-enable .menu-toggle:before {
  background: rgba(255,255,255,0.3) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).mobile-menu-icon-border-enable .dt-mobile-menu-icon:not(.floating-btn):before {
  border-color: rgba(255,255,255,0.3) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).hamburger-bg-hover-enable .menu-toggle:after {
  background: rgba(255,255,255,0.25) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).hamburger-border-enable .menu-toggle:before {
  border-color: rgba(255,255,255,0.3) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on).hamburger-border-hover-enable .menu-toggle:after {
  border-color: rgba(255,255,255,0.25) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav.level-arrows-on > li.has-children > a .menu-text:after,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav.level-arrows-on > li.has-children:not(.act) > a:hover .menu-text:after,
.light-preset-color.masthead:not(#phantom):not(.sticky-on) .main-nav.level-arrows-on > li.has-children.act > a .menu-text:after {
  background: white !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico.disabled-bg a:before {
  background-color: transparent !important;
}
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.custom-bg a,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.accent-bg a,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.gradient-bg a {
  box-shadow: none !important;
}
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.custom-bg a:before,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.accent-bg a:before,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.gradient-bg a:before {
  background: rgba(255,255,255,0.25) !important;
  background-image: none !important;
}
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-gradient-bg a,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-custom-bg a,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-accent-bg a {
  box-shadow: none !important;
}
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-gradient-bg a:after,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-custom-bg a:after,
.light-preset-color:not(.mobile-sticky-on).masthead:not(#phantom):not(.sticky-on):not(.sticky-top-line-on) .soc-ico.hover-accent-bg a:after {
  background: rgba(255,255,255,0.45) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico.hover-disabled-bg a:after {
  background-color: transparent !important;
  box-shadow: none !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico a .soc-font-icon,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico a:hover .soc-font-icon {
  color: #fff !important;
  background: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico.border-on a:before {
  box-shadow: inset 0px 0px 0px 2px rgba(255,255,255,0.25);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico.hover-border-on a:hover:after,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .soc-ico.hover-border-on a:hover:after {
  box-shadow: inset 0px 0px 0px 2px rgba(255,255,255,0.45) !important;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .submit:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-search .searchform > .search-icon,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-login .submit:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-contacts,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .mini-nav .customSelectInner:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart:before {
  color: white;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart {
  color: #fff;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart i {
  color: #fff;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart:hover,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart:hover {
  opacity: 1;
  color: rgba(255,255,255,0.7);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart:hover i,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart:hover i {
  color: rgba(255,255,255,0.7);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart > .counter,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart > .counter {
  background-color: rgba(255,255,255,0.25);
  background-image: none;
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .rectangular-counter-style .wc-ico-cart > .counter:before,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .rectangular-counter-style .edd-ico-cart > .counter:before {
  border-right: 4px solid rgba(255,255,255,0.25);
}
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .wc-ico-cart > .counter.custom-bg,
.light-preset-color.masthead:not(#phantom):not(.sticky-on):not(.sticky-mobile-on):not(.sticky-top-line-on) .edd-ico-cart > .counter.custom-bg {
  background-color: rgba(255,255,255,0.25) !important;
  background-image: none !important;
}
.phantom-sticky .boxed .masthead:not(.side-header).sticky-on {
  max-width: 1340px;
}
.boxed .top-bar-bg,
.boxed .classic-header .navigation:before {
  width: 100%;
}
.boxed .width-in-pixel .top-bar-bg,
.boxed .width-in-pixel .top-bar.full-width-line:after,
.boxed .width-in-pixel.classic-header.full-width-line .navigation:before,
.boxed .width-in-pixel.classic-header.bg-behind-menu .navigation:before {
  width: 1340px;
}
#phantom.boxed.width-in-pixel .top-bar-bg {
  width: 1340px;
}
.boxed .masthead.full-width:not(.side-header):not(.masthead-mobile) {
  width: 1340px;
}
.masthead:not(.side-header):not(.mixed-header) .header-bar,
.header-space {
  min-height: 70px;
}
.inline-header .header-bar > .mini-widgets,
.inline-header .widget-box .mini-widgets {
  padding: 0px 0px 0px 5px;
}
.wpb_content_element .wpb_tabs_nav > li > a {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .wpb_content_element .wpb_tabs_nav > li > a {
  color: #ffffff;
}
.sidebar-content .wpb_content_element .wpb_tabs_nav > li > a {
  color: #333333;
}
.footer .wpb_content_element .wpb_tabs_nav > li > a {
  color: #ffffff;
}
.wpb_tabs.wpb_content_element:not(.tab-style-four) .wpb_tabs_nav > li:not(.ui-state-active):hover > a,
.wpb_tour.wpb_content_element:not(.tab-style-four) .wpb_tabs_nav > li:not(.ui-state-active):hover > a,
.wpb_content_element.tab-style-three .wpb_tabs_nav > li.ui-tabs-active > a,
.wpb_content_element.tab-style-two.wpb_tabs .wpb_tabs_nav > li.ui-tabs-active > a {
  color: #000061;
}
.tab-style-one.wpb_tabs .wpb_tabs_nav li {
  border-radius: 1px;
}
.tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active),
.tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active),
.outline-element-decoration .tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active),
.shadow-element-decoration .tab-style-one.wpb_tabs .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.wpb_tabs.tab-style-one .wpb_tabs_nav > li.ui-state-active {
  color: #fff;
  background-color: var(--the7-accent-color);
}
.wpb_tabs.tab-style-two {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .wpb_tabs.tab-style-two {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .wpb_tabs.tab-style-two {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.wpb_tabs.tab-style-two .wpb_tabs_nav li.ui-tabs-active:before {
  background-color: #000061;
}
.tab-style-four.wpb_tabs {
  border-radius: 1px;
}
.tab-style-four.wpb_tabs {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .tab-style-four.wpb_tabs {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .tab-style-four.wpb_tabs {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.tab-style-four.wpb_tabs .wpb_tabs_nav {
  background-color: #000061;
}
.wpb_tour.tab-style-one,
.tab-style-one.wpb_tour .wpb_tabs_nav li {
  border-radius: 1px;
}
.tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.outline-element-decoration .tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.shadow-element-decoration .tab-style-one.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active):hover {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.wpb_tour.tab-style-one .wpb_tabs_nav > li.ui-state-active,
.wpb_tour.tab-style-one .wpb_tabs_nav > li.ui-state-active:hover {
  color: #fff;
}
#page .wpb_tour.tab-style-one .wpb_tabs_nav > li.ui-state-active,
#page .wpb_tour.tab-style-one .wpb_tabs_nav > li.ui-state-active:hover {
  background-color: #000061;
}
.wpb_tour.tab-style-two .wpb_tabs_nav li.ui-tabs-active:before {
  background-color: #000061;
}
.tab-style-two.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.wpb_tour.tab-style-two .wpb_tab {
  background-color: var(--the7-content-boxes-bg);
  border-radius: 1px;
}
.outline-element-decoration .tab-style-two.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.outline-element-decoration .wpb_tour.tab-style-two .wpb_tab {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .tab-style-two.wpb_tour .wpb_tabs_nav li:not(.ui-tabs-active),
.shadow-element-decoration .wpb_tour.tab-style-two .wpb_tab {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.tab-style-two.wpb_tour .wpb_tabs_nav li.ui-tabs-active {
  border-radius: 1px;
}
.wpb_tour.tab-style-two .wpb_tabs_nav > li.ui-state-active,
.wpb_tour.tab-style-two .wpb_tabs_nav > li.ui-state-active:hover {
  color: #fff;
}
#page .wpb_tour.tab-style-two .wpb_tabs_nav > li.ui-state-active,
#page .wpb_tour.tab-style-two .wpb_tabs_nav > li.ui-state-active:hover {
  background-color: #000061;
}
.wpb_tour.tab-style-two .wpb_tabs_nav > li.ui-state-active a {
  color: #fff;
}
.wpb_tour.tab-style-three .wpb_tabs_nav li,
.wpb_tour.tab-style-three .wpb_tabs_nav {
  border-color: var(--the7-divider-color);
}
.sidebar .wpb_tour.tab-style-three .wpb_tabs_nav li,
.sidebar-content .wpb_tour.tab-style-three .wpb_tabs_nav li,
.sidebar .wpb_tour.tab-style-three .wpb_tabs_nav,
.sidebar-content .wpb_tour.tab-style-three .wpb_tabs_nav {
  border-color: rgba(133,134,140,0.15);
}
.footer .wpb_tour.tab-style-three .wpb_tabs_nav li,
.footer .wpb_tour.tab-style-three .wpb_tabs_nav {
  border-color: rgba(255,255,255,0.15);
}
.tab-style-four.wpb_tour {
  border-radius: 1px;
}
.tab-style-four.wpb_tour {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .tab-style-four.wpb_tour {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .tab-style-four.wpb_tour {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.tab-style-four.wpb_tour .wpb_tabs_nav {
  background-color: #000061;
}
.wpb_content_element .wpb_accordion_header > a span {
  color: var(--the7-title-color);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .wpb_content_element .wpb_accordion_header > a span {
  color: #ffffff;
}
.sidebar-content .wpb_content_element .wpb_accordion_header > a span {
  color: #333333;
}
.footer .wpb_content_element .wpb_accordion_header > a span {
  color: #ffffff;
}
#page .wpb_accordion .wpb_accordion_wrapper > a:hover span,
#page .wpb_content_element .wpb_accordion_header a:hover span {
  color: var(--the7-accent-color);
}
.wpb_content_element.dt-accordion-line-on .wpb_accordion_wrapper .wpb_accordion_header {
  border-color: var(--the7-divider-color);
}
.sidebar .wpb_content_element.dt-accordion-line-on .wpb_accordion_wrapper .wpb_accordion_header,
.sidebar-content .wpb_content_element.dt-accordion-line-on .wpb_accordion_wrapper .wpb_accordion_header {
  border-color: rgba(133,134,140,0.15);
}
.footer .wpb_content_element.dt-accordion-line-on .wpb_accordion_wrapper .wpb_accordion_header {
  border-color: rgba(255,255,255,0.15);
}
.dt-accordion-bg-on.wpb_content_element .wpb_accordion_wrapper .wpb_accordion_header {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .dt-accordion-bg-on.wpb_content_element .wpb_accordion_wrapper .wpb_accordion_header {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .dt-accordion-bg-on.wpb_content_element .wpb_accordion_wrapper .wpb_accordion_header {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.content .wpb_content_element.dt-style:not(.dt-accordion-bg-on) .wpb_accordion_header > a:before {
  background-color: rgba(82,82,87,0.15);
}
.wpb_content_element.dt-style:not(.dt-accordion-bg-on) .wpb_accordion_header > a:hover:after,
.wpb_content_element.dt-style:not(.dt-accordion-bg-on) .ui-state-active > a:hover:after {
  color: #000061;
}
.vc_pie_chart .vc_pie_chart_value {
  font: var(--the7-h3-font);
  text-transform: var(--the7-h3-text-transform);
}
.vc_pie_wrapper .vc_pie_chart_back {
  border-color: rgba(0,0,97,0.1) !important;
}
.dt-style.vc_progress_bar.default-bg .vc_single_bar {
  background-color: var(--the7-content-boxes-bg);
}
.outline-element-decoration .dt-style.vc_progress_bar.default-bg .vc_single_bar {
  box-shadow: inset 0px 0px 0px 1px rgba(0,0,0,0);
}
.shadow-element-decoration .dt-style.vc_progress_bar.default-bg .vc_single_bar {
  box-shadow: 0 6px 18px rgba(0,0,0,0.1);
}
.dt-style.vc_progress_bar.outline-bg .vc_single_bar {
  box-shadow: inset 0px 0px 0px 1px rgba(82,82,87,0.2);
  background: none;
}
.dt-style.vc_progress_bar.transparent-bg .vc_single_bar {
  background-color: rgba(82,82,87,0.15);
}
.pb-style-two .vc_label,
.pb-style-three .vc_label {
  color: var(--the7-title-color);
  font-size: var(--the7-text-small-font-size);
  line-height: var(--the7-text-small-line-height);
}
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .pb-style-two .vc_label,
.dt-mega-menu .dt-mega-parent .sidebar-content .widget .pb-style-three .vc_label {
  color: #ffffff;
}
.sidebar-content .pb-style-two .vc_label,
.sidebar-content .pb-style-three .vc_label {
  color: #333333;
}
.footer .pb-style-two .vc_label,
.footer .pb-style-three .vc_label {
  color: #ffffff;
}
#page .accent-bg:not([class*='vc_progress-bar-color']) .vc_bar {
  color: #fff;
  background-color: #000061;
}
.accent-icon-bg .aio-icon {
  background: #000061 !important;
}
.accent-icon-color .aio-icon > i {
  color: #000061 !important;
}
.accent-border-color .aio-icon {
  border-color: #000061 !important;
}
.accent-title-color .uvc-main-heading > *,
.accent-title-color .aio-icon-title {
  color: #000061 !important;
}
.accent-subtitle-color .uvc-sub-heading,
.accent-subtitle-color .aio-icon-description {
  color: #000061 !important;
}
.accent-subtitle-color .uvc-main-heading > *,
.accent-title-color .uvc-main-heading > *,
.accent-subtitle-color .uvc-sub-heading,
.accent-title-color .uvc-sub-heading {
  display: inline-block;
}
.accent-border-color .uvc-headings-line {
  border-color: #000061 !important;
  border-top: 0;
  border-right: 0;
  border-left: 0;
}
.accent-gradient .accent-border-color .uvc-headings-line {
  border-color: transparent;
}
.accent-arrow-bg .slick-arrow {
  background: #000061 !important;
}
#main .wpb_alert strong {
  font:  normal 700 16px / 26px "Overpass", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h1 {
  line-height: 60px;
}
@media (max-width: 1200px) {
  .uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h1 {
    line-height: 48px;
  }
}
@media (max-width: 1200px) {
  .uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h1 {
    line-height: 48px;
  }
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h2 {
  line-height: 48px;
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h3 {
  line-height: 34px;
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h4 {
  line-height: 30px;
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h5 {
  line-height: 26px;
}
.uvc-heading.uvc-heading-default-font-sizes .uvc-main-heading h6 {
  line-height: 24px;
}
