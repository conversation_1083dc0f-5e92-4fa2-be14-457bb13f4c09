jQuery(document).ready((function(e){e("body");var t=e(window);e("#wpadminbar").height(),e(".masthead:not(.side-header):not(.side-header-v-stroke)").height();e.fn.smartGrid=function(){return this.each((function(){for(var t=e(this),a=parseInt(t.attr("data-columns")),i=parseInt(t.attr("data-width")),r=t.width();Math.floor(r/a)<i&&!(--a<=1););e("> .wf-cell",t).css({width:(100/a).toFixed(6)+"%",display:"inline-block"})}))};var a=e(".benefits-grid, .logos-grid");a.smartGrid(),t.on("debouncedresize",(function(){a.smartGrid()}));e(".project-post");if(e(".phantom-sticky").length>0)e(".masthead:not(.side-header):not(.side-header-v-stroke)");else e("#phantom").css("display");if("none"!==e(".mobile-header-bar").css("display")){e(".mobile-header-bar");if(e(".phantom-sticky").length>0)if(e(".sticky-header .masthead.side-header").length>0||e(".overlay-navigation .masthead.side-header").length>0)e(".mobile-header-bar").parent(".masthead:not(.side-header)");else e(".mobile-header-bar").parent()}else e(".masthead:not(.side-header):not(.side-header-v-stroke) .header-bar");t.on("debouncedresize",(function(a){e(".stripe").each((function(){var a=e(this),i=a.attr("data-min-height");e.isNumeric(i)?a.css({minHeight:i+"px"}):i?i.search("%")>0?a.css({minHeight:t.height()*(parseInt(i)/100)+"px"}):a.css({minHeight:i}):a.css({minHeight:0})}))})).trigger("debouncedresize")}));