import boto3

from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'create media bucket'
    requires_model_validation = False

    def handle(self, *args, **options):
        s3 = boto3.resource(
            's3',
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        )
        esiste = False
        for bucket in s3.buckets.all():
            print('trovato bucket %s' % bucket.name)
            if bucket.name == settings.AWS_STORAGE_BUCKET_NAME:
                esiste = True
        if not esiste:
            print('bucket %s non presente!' % settings.AWS_STORAGE_BUCKET_NAME)
            s3.create_bucket(Bucket=settings.AWS_STORAGE_BUCKET_NAME)
            print('Creato nuovo bucket %s' % settings.AWS_STORAGE_BUCKET_NAME)
