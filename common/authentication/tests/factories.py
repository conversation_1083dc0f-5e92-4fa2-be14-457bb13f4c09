import uuid
import factory
from factory.django import DjangoModelFactory
from .. import models

from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory


class UtenteMagisterFactory(DjangoModelFactory):
    class Meta:
        model = models.UtenteMagister

    username = factory.LazyAttribute(lambda x: str(uuid.uuid4()))
    email = factory.Sequence(lambda n: 'user{0}@example.com'.format(n))
    # password: pass
    password = 'pbkdf2_sha256$12000$GMh486z94kmq$yaxEmZjcLvlnBoKWNG2Y926givWTo739b6tqWnw8eBM='
    is_staff = True
    is_superuser = True
    area_corrente = factory.SubFactory(AreaFactory)
    esercizio_corrente = factory.SubFactory(EsercizioFactory)


class ImpostazioniUtenteFactory(UtenteMagisterFactory):
    class Meta:
        model = models.ImpostazioniUtente

    is_superuser = False
