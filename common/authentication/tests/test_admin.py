from django.urls import reverse
from django.test import TestCase
from django import setup
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')
setup()

from . import factories


class AppAdminTest(TestCase):
    def setUp(self):
        factories.UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_app_index(self):
        url = reverse('admin:app_list', args=('authentication',))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class UtenteMatthaeusAdminTest(TestCase):
    def setUp(self):
        factories.UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:authentication_utentemagister_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:authentication_utentemagister_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.UtenteMagisterFactory()
        url = reverse('admin:authentication_utentemagister_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.UtenteMagisterFactory()
        url = reverse('admin:authentication_utentemagister_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class ImpostazioniUtenteAdminTest(TestCase):
    def setUp(self):
        factories.UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:authentication_impostazioniutente_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:authentication_impostazioniutente_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        obj = factories.ImpostazioniUtenteFactory()
        url = reverse('admin:authentication_impostazioniutente_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.ImpostazioniUtenteFactory()
        url = reverse('admin:authentication_impostazioniutente_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)


class LanguageTest(TestCase):
    def setUp(self):
        self.login_url = reverse('admin:login')
        self.url = reverse('admin:index')

    def test_user_en(self):
        factories.UtenteMagisterFactory(username='test', language='en')
        response = self.client.post(self.login_url, dict(username='test', password='pass'))
        self.assertEqual(response.status_code, 302)
        response = self.client.get(self.url, HTTP_ACCEPT_LANGUAGE='it')
        self.assertContains(response, 'Accounting period')

    def test_user_fr(self):
        factories.UtenteMagisterFactory(username='test', language='fr')
        response = self.client.post(self.login_url, dict(username='test', password='pass'))
        self.assertEqual(response.status_code, 302)
        response = self.client.get(self.url, HTTP_ACCEPT_LANGUAGE='it')
        self.assertContains(response, 'Exercice')


class LanguageNoAuthTest(TestCase):
    def setUp(self):
        self.url = reverse('admin:login')

    def test_default_it(self):
        response = self.client.get(self.url)
        self.assertContains(response, 'value="Accedi"')

    def test_header_en(self):
        response = self.client.get(self.url, HTTP_ACCEPT_LANGUAGE='en')
        self.assertContains(response, 'value="Log in"')
