from django.test import TestCase

from rest_framework.test import APIClient

from . import factories
from django.urls import reverse
from common.authentication.tests.factories import UtenteMagisterFactory

# aggiungi un test per verificare che la risposta della vista API /api/token/ sia corretta
# la risposta deve essere un oggetto JSON
# l'oggetto JSON deve contenere i campi:
# - access
# - refresh
# utilizza il metodo assertEqual per verificare che la risposta della vista sia uguale all'oggetto JSON atteso

class TestTokenObtainPairView(TestCase):
    def setUp(self):
        # crea un utente
        self.utente = UtenteMagisterFactory()

    def test_token_obtain_pair_view(self):
        # crea un client API
        client = APIClient()
        # invia una richiesta POST alla vista API /api/token/
        response = client.post(reverse('token_obtain_pair'), {'username': self.utente.username, 'password': 'pass'})
        # verifica che la risposta sia 200 OK
        self.assertEqual(response.status_code, 200)
        # verifica che la risposta sia un oggetto JSON
        self.assertEqual(set(response.data.keys()), {'access', 'refresh'})
        # verifica che i campi access e refresh siano stringhe
        self.assertIsInstance(response.data['access'], str)
        self.assertIsInstance(response.data['refresh'], str)
        # verifica che i campi access e refresh siano non vuoti
        self.assertNotEqual(response.data['access'], '')
        self.assertNotEqual(response.data['refresh'], '')
        # verifica che i campi access e refresh siano token JWT validi
        self.assertTrue(response.data['access'].count('.') == 2)
        self.assertTrue(response.data['refresh'].count('.') == 2)

# aggiungi un test per verificare che la risposta della vista API /api/token/refresh/ sia corretta
# la risposta deve essere un oggetto JSON
# l'oggetto JSON deve contenere il campo:
# - access
# utilizza il metodo assertEqual per verificare che la risposta della vista sia uguale all'oggetto JSON atteso
class TestTokenRefreshView(TestCase):
    def setUp(self):
        # crea un utente
        self.utente = UtenteMagisterFactory()
        # crea un client API
        self.client = APIClient()
        # invia una richiesta POST alla vista API /api/token/
        self.response = self.client.post(reverse('token_obtain_pair'), {'username': self.utente.username, 'password': 'pass'})
        # verifica che la risposta sia 200 OK
        self.assertEqual(self.response.status_code, 200)
        # verifica che la risposta sia un oggetto JSON
        self.assertEqual(set(self.response.data.keys()), {'access', 'refresh'})
        # verifica che i campi access e refresh siano stringhe
        self.assertIsInstance(self.response.data['access'], str)
        self.assertIsInstance(self.response.data['refresh'], str)
        # verifica che i campi access e refresh siano non vuoti
        self.assertNotEqual(self.response.data['access'], '')
        self.assertNotEqual(self.response.data['refresh'], '')
        # verifica che i campi access e refresh siano token JWT validi
        self.assertTrue(self.response.data['access'].count('.') == 2)
        self.assertTrue(self.response.data['refresh'].count('.') == 2)

    def test_token_refresh_view(self):
        # invia una richiesta POST alla vista API /api/token/refresh/
        response = self.client.post(reverse('token_refresh'), {'refresh': self.response.data['refresh']})
        # verifica che la risposta sia 200 OK
        self.assertEqual(response.status_code, 200)
        # verifica che la risposta sia un oggetto JSON
        self.assertEqual(set(response.data.keys()), {'access'})
        # verifica che il campo access sia una stringa
        self.assertIsInstance(response.data['access'], str)
        # verifica che il campo access sia non vuoto
        self.assertNotEqual(response.data['access'], '')
        # verifica che il campo access sia un token JWT valido
        self.assertTrue(response.data['access'].count('.') == 2)
        self.assertTrue(response.data['access'].count('.') == 2)
        # verifica che il campo access sia diverso dal token access originale
        self.assertNotEqual(response.data['access'], self.response.data['access'])

# aggiungi un test per verificare che la risposta della vista API /api/token/verify/ sia corretta
# la risposta deve essere un oggetto JSON
# l'oggetto JSON deve contenere il campo:
# - code
# utilizza il metodo assertEqual per verificare che la risposta della vista sia uguale all'oggetto JSON atteso

class TestTokenVerifyView(TestCase):
    def setUp(self):
        # crea un utente
        self.utente = UtenteMagisterFactory()
        # crea un client API
        self.client = APIClient()
        # invia una richiesta POST alla vista API /api/token/
        self.response = self.client.post(reverse('token_obtain_pair'), {'username': self.utente.username, 'password': 'pass'})
        # verifica che la risposta sia 200 OK
        self.assertEqual(self.response.status_code, 200)
        # verifica che la risposta sia un oggetto JSON
        self.assertEqual(set(self.response.data.keys()), {'access', 'refresh'})
        # verifica che i campi access e refresh siano stringhe
        self.assertIsInstance(self.response.data['access'], str)
        self.assertIsInstance(self.response.data['refresh'], str)
        # verifica che i campi access e refresh siano non vuoti
        self.assertNotEqual(self.response.data['access'], '')
        self.assertNotEqual(self.response.data['refresh'], '')
        # verifica che i campi access e refresh siano token JWT validi
        self.assertTrue(self.response.data['access'].count('.') == 2)
        self.assertTrue(self.response.data['refresh'].count('.') == 2)

