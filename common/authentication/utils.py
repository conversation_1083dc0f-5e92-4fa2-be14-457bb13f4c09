import secrets
import string


def genera_password_casuale(lunghezza=16):
    if lunghezza < 16:
        raise ValueError("La lunghezza della password deve essere almeno 16 caratteri")
    # Assicurati di includere almeno una lettera maiuscola e un carattere speciale
    caratteri = string.ascii_letters + string.digits + string.punctuation
    password = [
        secrets.choice(string.ascii_uppercase),  # Almeno una lettera maiuscola
        secrets.choice(string.punctuation)       # Almeno un carattere speciale
    ]    
    # Riempire il resto della password
    password += [secrets.choice(caratteri) for _ in range(lunghezza - 2)]
    # Mescolare i caratteri per evitare che i primi due siano sempre maiuscola e speciale
    secrets.SystemRandom().shuffle(password)
    return ''.join(password)
