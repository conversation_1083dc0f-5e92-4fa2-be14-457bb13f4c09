from __future__ import unicode_literals
from django.contrib import messages
from django.contrib.auth.backends import ModelBackend
from django.utils.translation import ugettext as _
from django.utils import translation
from django.utils.translation import LANGUAGE_SESSION_KEY


class MagisterBackend(ModelBackend):

    def authenticate(self, request, username=None, password=None, **kwargs):
        utente_autenticato = super(MagisterBackend, self).authenticate(request, username, password, **kwargs)
        if utente_autenticato:
            messaggio_errore = _('Attenzione! L\'utente corrente non ha i permessi per accedere all\' applicazione scelta.')
            if not utente_autenticato.is_superuser:
                if hasattr(request, 'current_app'):
                    if request.current_app == 'matthaeus':
                        if not utente_autenticato.matthaeus_abilitato:
                            messages.error(request, messaggio_errore)
                            return None
                    if request.current_app == 'cyrenaeus':
                        if not utente_autenticato.cyrenaeus_abilitato:
                            messages.error(request, messaggio_errore)
                            return None
                    if request.current_app == 'publius':
                        if not utente_autenticato.publius_abilitato:
                            messages.error(request, messaggio_errore)
                            return None
                    if request.current_app == 'saulo':
                        if not utente_autenticato.saulo_abilitato:
                            messages.error(request, messaggio_errore)
                            return None
            translation.activate(utente_autenticato.language)
            if hasattr(request, 'session'):
                request.session[LANGUAGE_SESSION_KEY] = utente_autenticato.language
        return utente_autenticato
