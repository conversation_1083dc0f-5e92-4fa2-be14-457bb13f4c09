# Generated by Django 2.2.28 on 2024-11-29 11:40

import common.authentication.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0007_utentemagister_tipo_utente_martinus'),
    ]

    operations = [
        migrations.CreateModel(
            name='UtenteEgo',
            fields=[
            ],
            options={
                'verbose_name': 'Utente Ego',
                'verbose_name_plural': 'Utenti Ego',
                'abstract': False,
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('authentication.utentemagister',),
            managers=[
                ('objects', common.authentication.models.UtenteEgoManager()),
            ],
        ),
    ]
