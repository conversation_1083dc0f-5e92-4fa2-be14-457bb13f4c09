# Generated by Django 2.2.11 on 2021-03-25 12:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0001_initial'),
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='areaabilitata',
            name='area',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='utentemagister',
            name='area_corrente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagraficabase.Area', verbose_name='area corrente'),
        ),
        migrations.AddField(
            model_name='utentemagister',
            name='esercizio_corrente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagraficabase.Esercizio', verbose_name='esercizio corrente'),
        ),
    ]
