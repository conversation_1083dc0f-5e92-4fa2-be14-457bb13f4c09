# Generated by Django 2.2.11 on 2021-03-25 12:46

from django.conf import settings
import django.contrib.auth.models
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0011_update_proxy_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='UtenteMagister',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(db_index=True, error_messages={'unique': 'A user with that username already exists.'}, max_length=200, unique=True, validators=[django.core.validators.RegexValidator('^[\\w.@+-]+$', 'Enter a valid username. This value may contain only letters, numbers and @/./+/-/_ characters.')], verbose_name='username')),
                ('first_name', models.CharField(blank=True, db_index=True, max_length=200, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, db_index=True, max_length=200, verbose_name='last name')),
                ('email', models.EmailField(blank=True, db_index=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(db_index=True, default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('uuid', models.CharField(blank=True, max_length=200, null=True, verbose_name='uuid')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('tipo', models.CharField(choices=[('admin', 'Admin'), ('amministratore_area', 'Amministratore Area')], db_index=True, default='amministratore_area', max_length=50, verbose_name='tipo')),
                ('language', models.CharField(choices=[('it', 'Italiano'), ('en', 'Inglese'), ('es', 'Spagnolo'), ('fr', 'Francese'), ('pt', 'Portoghese')], default='it', max_length=10, verbose_name='lingua')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Utente',
                'verbose_name_plural': 'Utenti',
                'abstract': False,
                'swappable': 'AUTH_USER_MODEL',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AreaAbilitata',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permessi', models.CharField(choices=[('readonly', 'Sola Lettura'), ('readwrite', 'Leggere/Scrivere/Eliminare')], default='readonly', max_length=200, verbose_name='permessi')),
                ('utente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='utente')),
            ],
            options={
                'verbose_name': 'Area Abilitata',
                'verbose_name_plural': 'Aree Abilitate',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ImpostazioniUtente',
            fields=[
            ],
            options={
                'verbose_name': 'Impostazioni Utente',
                'verbose_name_plural': 'Impostazioni Utenti',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('authentication.utentemagister',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
