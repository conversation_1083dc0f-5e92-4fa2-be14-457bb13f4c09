from django.contrib.auth.models import User<PERSON>anager, AbstractBaseUser, PermissionsMixin
from django.core import validators
from django.core.mail import send_mail
from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _

from common.anagraficabase.models import Area, Esercizio
from common.authentication.utils import genera_password_casuale

TIPO_UTENTE = (
    ('admin', _('Admin')),
    ('amministratore_area', _('Amministratore Area')),
    ('religioso', _('Religioso')),
)

TIPO_UTENTE_MARTINUS = (
    ('amministratore', _('Amministratore')),
    ('referente', _('Referente')),
    ('benefattore', _('Benefattore')),
)

LIVELLO_PERMESSI = (
    ('readonly', _('Sola Lettura')),
    ('readwrite', _('Leggere/Scrivere/Eliminare')),
)


class AbstractUser(AbstractBaseUser, PermissionsMixin):
    """
    An abstract base class implementing a fully featured User model with
    admin-compliant permissions.

    Username and password are required. Other fields are optional.
    """
    username = models.CharField(
        _('username'),
        max_length=200,
        unique=True,
        validators=[
            validators.RegexValidator(
                r'^[\w.@+-]+$',
                _('Enter a valid username. This value may contain only '
                  'letters, numbers ' 'and @/./+/-/_ characters.')
            ),
        ],
        error_messages={
            'unique': _("A user with that username already exists."),
        },
        db_index=True,
    )
    first_name = models.CharField(_('first name'), max_length=200, blank=True, db_index=True)
    last_name = models.CharField(_('last name'), max_length=200, blank=True, db_index=True)
    email = models.EmailField(_('email address'), blank=True, db_index=True)
    is_staff = models.BooleanField(
        _('staff status'),
        default=False,
        help_text=_('Designates whether the user can log into this admin site.'),
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_(
            'Designates whether this user should be treated as active. '
            'Unselect this instead of deleting accounts.'
        ),
        db_index=True,
    )
    uuid = models.CharField(_('uuid'), max_length=200, null=True, blank=True)
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    objects = UserManager()

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        abstract = True

    def get_full_name(self):
        """
        Returns the first_name plus the last_name, with a space in between.
        """
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        "Returns the short name for the user."
        return self.first_name

    def email_user(self, subject, message, from_email=None, **kwargs):
        """
        Sends an email to this User.
        """
        send_mail(subject, message, from_email, [self.email], **kwargs)


class UtenteMagister(AbstractUser):
    tipo = models.CharField(_('tipo'), max_length=50, choices=TIPO_UTENTE, default='amministratore_area', db_index=True)
    area_corrente = models.ForeignKey(
        Area, verbose_name=_('area corrente'), on_delete=models.SET_NULL, null=True, blank=True
    )
    esercizio_corrente = models.ForeignKey(
        Esercizio, verbose_name=_('esercizio corrente'), on_delete=models.SET_NULL, null=True, blank=True
    )
    language = models.CharField(
        _('lingua'), max_length=10, choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE
    )
    matthaeus_abilitato = models.BooleanField(_('matthaeus abilitato'), default=True)
    cyrenaeus_abilitato = models.BooleanField(_('cyrenaeus abilitato'), default=True)
    publius_abilitato = models.BooleanField(_('publius abilitato'), default=True)
    saulo_abilitato = models.BooleanField(_('saulo abilitato'), default=True)
    petrus_abilitato = models.BooleanField(_('petrus abilitato'), default=True)
    martinus_abilitato = models.BooleanField(_('martinus abilitato'), default=True)
    tipo_utente_martinus = models.CharField(
        _('Tipo utente (Martinus)'), max_length=50, choices=TIPO_UTENTE_MARTINUS, 
        null=True, blank=True,
    )
    impostazioni_treeview_saulo = models.TextField(_('impostazioni treeview saulo'), null=True, blank=True)
    password_temporanea = models.CharField(
        _('password temporanea'), max_length=50, null=True, blank=True,
    )
    
    class Meta(AbstractUser.Meta):
        swappable = 'AUTH_USER_MODEL'
        verbose_name = _('Utente')
        verbose_name_plural = _('Utenti')
        app_label = 'authentication'

    def get_area_corrente_display(self):
        if self.area_corrente:
            if len(self.area_corrente.nome) > 38:
                return '%s...' % self.area_corrente.nome[0:36]
            else:
                return '%s' % self.area_corrente


class ImpostazioniUtente(UtenteMagister):

    class Meta():
        proxy = True
        verbose_name = _('Impostazioni Utente')
        verbose_name_plural = _('Impostazioni Utenti')
        app_label = 'authentication'


class AreaAbilitata(models.Model):
    utente = models.ForeignKey(
        UtenteMagister, verbose_name=_('utente'), on_delete=models.CASCADE,
    )
    area = models.ForeignKey(
        Area, verbose_name=_('area'), on_delete=models.CASCADE,
    )
    permessi = models.CharField(
        _('permessi'), max_length=200, choices=LIVELLO_PERMESSI, default='readonly'
    )

    class Meta(UtenteMagister.Meta):
        verbose_name = _('Area Abilitata')
        verbose_name_plural = _('Aree Abilitate')


class UtenteEgoManager(UserManager):
    def get_queryset(self):
        return super(UtenteEgoManager, self).get_queryset().filter(tipo='religioso')


class UtenteEgo(UtenteMagister):
    objects = UtenteEgoManager()

    class Meta(UtenteMagister.Meta):
        verbose_name = _('Utente Ego')
        verbose_name_plural = _('Utenti Ego')
        app_label = 'authentication'
        proxy = True

    def save(self, *args, **kwargs):
        self.tipo = 'religioso'
        return super(UtenteEgo, self).save(*args, **kwargs)
        
    def reimposta_password_temporanea(self):
        nuova_pwd = genera_password_casuale()
        self.set_password(nuova_pwd)
        self.password_temporanea = nuova_pwd
        self.save()
