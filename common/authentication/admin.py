from django.contrib.auth.admin import UserAdmin as DjangoUserAdmin
from django.utils.translation import ugettext_lazy as _
from django.utils import translation
from django.contrib import admin
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.utils.translation import LANGUAGE_SESSION_KEY
from django.utils.html import format_html

from common.authentication import models
from publius.persone.models import Religioso


class AreaAbilitataInline(admin.TabularInline):
    model = models.AreaAbilitata
    extra = 1


class UtenteMagisterAdmin(DjangoUserAdmin):
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'email')}),
        (
            _('IMPOSTAZIONI MAGISTER'), {
                'fields': (
                    'tipo',
                    'area_corrente',
                    'esercizio_corrente',
                    'language',
                )
            }
        ),
        (
            _('ABILITAZIONI SUITE'), {
                'fields': (
                    'matthaeus_abilitato',
                    'cyrenaeus_abilitato',
                    'publius_abilitato',
                    'saulo_abilitato',
                    ('martinus_abilitato', 'tipo_utente_martinus'),
                    'petrus_abilitato',
                )
            }
        ),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser',
                                       'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    list_display = ('username', 'email', 'first_name', 'last_name', 'tipo')
    list_filter = ('tipo', 'is_staff', 'is_superuser', 'is_active', 'groups')
    search_fields = ('username', 'first_name', 'last_name', 'email')
    ordering = ('username',)
    inlines = [AreaAbilitataInline, ]


class UtenteEgoAdmin(DjangoUserAdmin):
    list_display = ('username', 'last_name', 'first_name', 'language', 'is_active')
    search_fields = ('username', 'first_name', 'last_name', 'email')
    list_filter = ('is_active', 'language')
    readonly_fields = ('display_link_religioso', 'password_temporanea')
    ordering = ('username',)
    actions = ['reset_password', ]
    fieldsets = (
        (None, {'fields': ('username', 'password_temporanea')}),
        (
            _('Personal info'), {
                'fields': (
                    'first_name', 
                    'last_name', 
                    'email',
                    'language',
                    'is_active',
                )
            }
        ),
        (None, {'fields': ('display_link_religioso', )}),
    )

    def display_link_religioso(self, obj):
        try:
            religioso = Religioso.objects.get(utente=obj)
            return format_html(
                '<a href="%s" target="">%s</a>' % (religioso.get_url(), religioso)
            )
        except Religioso.DoesNotExist:
            return ''
    display_link_religioso.short_description = _('Link Religioso')
    display_link_religioso.allow_tags = True

    def reset_password(self, request, queryset):
        for user in queryset:
            user.reimposta_password_temporanea()
            messages.success(request, _('Password temporanea reimpostata per l\'utente %s') % user)
    reset_password.short_description = _('Reimposta password temporanea')
    

class ImpostazioniUtenteAdmin(DjangoUserAdmin):
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'email')}),
        (
            _('MAGISTER'), {
                'fields': (
                    'area_corrente',
                    'esercizio_corrente',
                    'language',
                )
            }
        ),
    )
    list_display = ('username', 'email', 'first_name', 'last_name', 'tipo')
    ordering = ('username',)
    save_on_top = False
    readonly_fields = ('username', 'area_corrente', 'esercizio_corrente')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def response_change(self, request, obj):
        if obj.language:
            if not request.user.language == obj.language:
                translation.activate(obj.language)
                if hasattr(request, 'session'):
                    request.session[LANGUAGE_SESSION_KEY] = obj.language
                messaggio = _('Impostata nuova lingua per l\'utente corrente. Cliccare di nuovo sul tasto "Inizio" per proseguire.')
                messages.success(request, messaggio)
        # response = HttpResponseRedirect('/')
        # return response
        return super().response_change(request, obj)
