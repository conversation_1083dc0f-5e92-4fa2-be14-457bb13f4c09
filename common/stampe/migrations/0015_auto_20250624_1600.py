# Generated by Django 2.2.28 on 2025-06-24 16:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stampe', '0014_auto_20250409_1629'),
    ]

    operations = [
        migrations.AlterField(
            model_name='modello',
            name='tipo',
            field=models.CharField(choices=[('cir', '------------- CYRENAEUS ---------------------'), ('case_elenco_case_semplice', 'Cyrenaeus - Case: Elenco case semplice'), ('immobili_elenco_schede_immobili', 'Cyrenaeus - Immobili: elenco schede immobili'), ('inventario_scheda_beni_culturali', 'Cyrenaeus - Inventario: scheda beni culturali'), ('inventario_scheda_veicoli', 'Cyrenaeus - Inventario: scheda veicoli'), ('martinus', '------------- MARTINUS ---------------------'), ('donazioni_sollecito', 'Martin<PERSON> - lettera sollecito donazioni'), ('donazioni_ringraziamenti', '<PERSON>us - lettera ringraziamento donazioni'), ('donazioni_ricevute', 'Martinus - lettera ricevuta donazioni'), ('matthaeus', '------------- MATTHAEUS ---------------------'), ('libro_cassa', 'Matthaeus - libro cassa'), ('estratto_conto', 'Matthaeus - estratto conto'), ('libro_giornale', 'Matthaeus - libro giornale'), ('elenco_partitari', 'Matthaeus - elenco partitari'), ('piano_dei_conti', 'Matthaeus - Piano dei conti'), ('bilancio_conti', 'Matthaeus - Bilancio ai conti'), ('bilancio_conti_aggregazioni', 'Matthaeus - Bilancio ai conti (con aggregazioni)'), ('bilancio_sottoconti', 'Matthaeus - Bilancio ai sottoconti'), ('bilancio_sottoconti_aggregazioni', 'Matthaeus - bilancio ai sottoconti (con aggregazioni)'), ('primanota_analitica', 'Matthaeus - primanota analitica'), ('primanota_sintetica', 'Matthaeus - primanota sintetica'), ('stato_patrimoniale', 'Matthaeus - stato patrimoniale'), ('stato_patrimoniale_parziali', 'Matthaeus - stato patrimoniale (parziali per aree)'), ('stato_patrimoniale_clienti_fornitori', 'Matthaeus - stato patrimoniale dett.clienti/fornitori)'), ('bilancio_comparativo', 'Matthaeus - bilancio comparativo (esercizio corrente vs precedente)'), ('bilancio_riclassificato', 'Matthaeus - bilancio riclassificato'), ('rendiconto_finanziario', 'Matthaeus - rendiconto finanziario'), ('elenco_partitari_anagrafica', 'Matthaeus - elenco partitari anagrafica'), ('elenco_mastrini', 'Matthaeus - elenco mastrini (sottoconti)'), ('elenco_mastrini_centri_di_costo', 'Matthaeus - elenco mastrini (centri di costo'), ('petrus', '------------- PETRUS ---------------------'), ('nota_spese_elenco', 'Petrus - Elenco Note spese'), ('publius', '------------- PUBLIUS ---------------------'), ('scheda_completa_religiosi', 'Publius - scheda completa religiosi'), ('religiosi_annuario_familiari', 'Publius - elenco annuario familiari'), ('religiosi_annuario_defunti', 'Publius - elenco annuario defunti'), ('religiosi_annuario_componenti_case', 'Publius - elenco annuario componenti case'), ('religiosi_annuario_nazionalita', 'Publius - elenco annuario nazionalita'), ('publius', '------------- ALTRO ---------------------'), ('scadenziario_elenco_scadenze', 'Magister: Scadenziario: elenco scadenze')], max_length=200, verbose_name='Tipo'),
        ),
    ]
