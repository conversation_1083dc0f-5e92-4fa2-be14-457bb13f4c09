import json
import tempfile
import subprocess

from django import forms
from django.utils.translation import ugettext_lazy as _
from django.conf import settings
from django.contrib import messages
from django.utils import timezone
from django.db import IntegrityError

from djmoney.money import Money
from preferences import preferences

from common.anagraficabase import models
from common.anagraficabase.models import PianoDeiConti
from matthaeus.movimenti.models import MovimentoPrimaNota, DettaglioMovimentoPrimaNota

from matthaeus.anagrafica.models import Benefattore, Tipologia
from martinus.adesioni.models import Adesione

# MATTHAEUS
NOME_TABELLA_PIANO_DEI_CONTI = 'PianoDeiConti'
NOME_TABELLA_DETTAGLI_MOVIMENTI = 'MovimDettagli'
NOME_TABELLA_MOVIMENTI = 'MovimIntesta'

TIPI_IMPORTAZIONI_MATTHAEUS = [
    ('piano_dei_conti', _('Piano dei conti')),
    ('movimenti_prima_nota', _('Movimenti prima nota')),
]

TIPI_IMPORTAZIONI_PUBLIUS = [
    ('case', _('Case')),
    ('religiosi', _('Religiosi')),
    ('assenze', _('Assenze')),
    ('curriculum', _('Curriculum')),
    ('documenti', _('Documenti')),
    ('familiari', _('Familiari')),
    ('lingue_conosciute', _('Lingue Conosciute')),
    ('servizi', _('Servizi')),
    ('studi', _('Studi')),
    ('trasferimenti', _('Trasferimenti')),
    ('viaggi', _('Viaggi')),
]

TIPI_IMPORTAZIONI_MARTINUS = [
    ('benefattori', _('Benefattori')),
    ('adesioni', _('Adesioni')),
    ('donazioni', _('Donazioni')),
]


class ImpostazioniMagisterForm(forms.ModelForm):

    class Meta:
        model = models.ImpostazioniMagister
        fields = '__all__'
        widgets = dict(
            appellativo_predefinito=forms.TextInput(attrs={'style': 'width:250px;'}),
        )


def get_livello_conto(conto):
    if conto:
        codici_conto = conto.split('.')
        numero_livelli = len(codici_conto)
        if numero_livelli > 0:
            codice_sottoconto = int(codici_conto[-1])
        if numero_livelli > 1:
            codice_conto = int(codici_conto[-2])
        if numero_livelli > 2:
            codice_mastro = int(codici_conto[-3])
        if numero_livelli > 3:
            codice_gruppo = int(codici_conto[-4])
        if codice_sottoconto:
            return 'sottoconto'
        elif codice_conto:
            return 'conto'
        elif codice_mastro:
            return 'mastro'
        elif codice_gruppo:
            return 'gruppo'


def crea_nuovo_piano(piano_dei_conti, livello):
    nuovo_piano = None
    if piano_dei_conti:
        nuovo_piano = PianoDeiConti()
        conto = piano_dei_conti.get('Conto')
        nuovo_piano.livello = livello
        if piano_dei_conti['Descrizione']:
            nuovo_piano.descrizione = piano_dei_conti['Descrizione']
        if piano_dei_conti['Imputabile'] == 1:
            nuovo_piano.conto_imputabile = True
        separatore_codice = preferences.ImpostazioniMagister.separatore_codice_conti
        codici_conto = conto.split(separatore_codice)
        numero_livelli = len(codici_conto)
        nuovo_piano.codice = conto
        if numero_livelli > 0:
            codice_sottoconto = int(codici_conto[-1])
            nuovo_piano.codice_sottoconto = codice_sottoconto
        if numero_livelli > 1:
            codice_conto = int(codici_conto[-2])
            nuovo_piano.codice_conto = codice_conto
        if numero_livelli > 2:
            codice_mastro = int(codici_conto[-3])
            nuovo_piano.codice_mastro = codice_mastro
        if numero_livelli > 3:
            codice_gruppo = int(codici_conto[-4])
            nuovo_piano.codice_gruppo = codice_gruppo
        if piano_dei_conti['PatrimEconom'] == 'E':
            nuovo_piano.tipologia = 'economico'
        elif piano_dei_conti['PatrimEconom'] == 'P':
            nuovo_piano.tipologia = 'patrimoniale'
        elif piano_dei_conti['PatrimEconom'] == 'C':
            nuovo_piano.tipologia = 'ordine'
        if 'ValutaProposta' in piano_dei_conti:
            if piano_dei_conti['ValutaProposta']:
                nuovo_piano.valuta_default = piano_dei_conti['ValutaProposta']
    return nuovo_piano


def crea_gruppi(elenco_piani):
    if elenco_piani:
        elenco_record = []
        for riga_piano in elenco_piani:
            if riga_piano:
                piano_dei_conti = json.loads(riga_piano)
                if piano_dei_conti['Conto']:
                    print('CREAZIONE GRUPPO: %s' % piano_dei_conti['Conto'])
                    conto = piano_dei_conti['Conto']
                    livello = get_livello_conto(conto)
                    if livello == 'gruppo':
                        nuovo_piano = crea_nuovo_piano(piano_dei_conti, livello)
                        # nuovo_piano.save()
                        nuovo_piano.lft = 0
                        nuovo_piano.rght = 0
                        nuovo_piano.tree_id = 0
                        nuovo_piano.level = 0
                        elenco_record.append(nuovo_piano)
        if elenco_record:
            PianoDeiConti.objects.bulk_create(elenco_record)
            # PianoDeiConti.objects.rebuild()


def crea_mastri(elenco_piani):
    if elenco_piani:
        elenco_record = []
        for riga_piano in elenco_piani:
            if riga_piano:
                piano_dei_conti = json.loads(riga_piano)
                if piano_dei_conti['Conto']:
                    print('CREAZIONE MASTRO: %s' % piano_dei_conti['Conto'])
                    conto = piano_dei_conti['Conto']
                    livello = get_livello_conto(conto)
                    if livello == 'mastro':
                        nuovo_piano = crea_nuovo_piano(piano_dei_conti, livello)
                        try:
                            padre = PianoDeiConti.objects.get(
                                livello='gruppo',
                                codice_gruppo=nuovo_piano.codice_gruppo,
                            )
                            nuovo_piano.parent = padre
                        except PianoDeiConti.DoesNotExist:
                            pass
                        # nuovo_piano.save()
                        nuovo_piano.lft = 0
                        nuovo_piano.rght = 0
                        nuovo_piano.tree_id = 0
                        nuovo_piano.level = 0
                        elenco_record.append(nuovo_piano)
        if elenco_record:
            PianoDeiConti.objects.bulk_create(elenco_record)
            # PianoDeiConti.objects.rebuild()


def crea_conti(elenco_piani):
    if elenco_piani:
        elenco_record = []
        for riga_piano in elenco_piani:
            if riga_piano:
                piano_dei_conti = json.loads(riga_piano)
                if piano_dei_conti['Conto']:
                    print('CREAZIONE CONTO: %s' % piano_dei_conti['Conto'])
                    conto = piano_dei_conti['Conto']
                    livello = get_livello_conto(conto)
                    if livello == 'conto':
                        nuovo_piano = crea_nuovo_piano(piano_dei_conti, livello)
                        try:
                            padre = PianoDeiConti.objects.get(
                                livello='mastro',
                                codice_gruppo=nuovo_piano.codice_gruppo,
                                codice_mastro=nuovo_piano.codice_mastro,
                            )
                            nuovo_piano.parent = padre
                        except PianoDeiConti.DoesNotExist:
                            pass
                        # nuovo_piano.save()
                        nuovo_piano.lft = 0
                        nuovo_piano.rght = 0
                        nuovo_piano.tree_id = 0
                        nuovo_piano.level = 0
                        elenco_record.append(nuovo_piano)
        if elenco_record:
            PianoDeiConti.objects.bulk_create(elenco_record)
            # PianoDeiConti.objects.rebuild()


def crea_sottoconti(elenco_piani):
    if elenco_piani:
        elenco_record = []
        for riga_piano in elenco_piani:
            if riga_piano:
                piano_dei_conti = json.loads(riga_piano)
                if piano_dei_conti['Conto']:
                    print('CREAZIONE SOTTOCONTO: %s' % piano_dei_conti['Conto'])
                    conto = piano_dei_conti['Conto']
                    livello = get_livello_conto(conto)
                    if livello == 'sottoconto':
                        nuovo_piano = crea_nuovo_piano(piano_dei_conti, livello)
                        try:
                            padre = PianoDeiConti.objects.get(
                                livello='conto',
                                codice_gruppo=nuovo_piano.codice_gruppo,
                                codice_mastro=nuovo_piano.codice_mastro,
                                codice_conto=nuovo_piano.codice_conto,
                            )
                            nuovo_piano.parent = padre
                        except PianoDeiConti.DoesNotExist:
                            pass
                        # nuovo_piano.save()
                        nuovo_piano.lft = 0
                        nuovo_piano.rght = 0
                        nuovo_piano.tree_id = 0
                        nuovo_piano.level = 0
                        elenco_record.append(nuovo_piano)
                        print('SOTTOCONTO CREATO: %s' % nuovo_piano)
        if elenco_record:
            PianoDeiConti.objects.bulk_create(elenco_record)
            # PianoDeiConti.objects.rebuild()


def importa_piano_dei_conti(dati_file_mdb):
    messaggi = []
    if dati_file_mdb:
        elenco_piani = dati_file_mdb.decode('utf-8').split('\n')
        if elenco_piani:
            primo_piano = elenco_piani[0]
            conto_primo_piano = json.loads(primo_piano)
            primo_conto = conto_primo_piano['Conto']
            separatore_codice = preferences.ImpostazioniMagister.separatore_codice_conti
            messaggi.append(
                'Utilizzo separatore <%s> per caricare piano dei conti - Es: %s' % (separatore_codice, primo_conto)
            )
            codici_conto = primo_conto.split(separatore_codice)
            numero_livelli = len(codici_conto)
            messaggi.append(
                'Rilevati %s livelli nel piano dei conti' % numero_livelli
            )
            if not settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == numero_livelli:
                messaggi.append(
                    'ERRORE_ numero livelli piano dei conti (%s) non corrisponde ai livelli rilevati nel file (%s)' % (
                        settings.NUMERO_LIVELLI_PIANO_DEI_CONTI,
                        numero_livelli
                    )
                )
                return messaggi
            if numero_livelli > 3:
                print('INIZIO CREAZIONE GRUPPI')
                messaggi_gruppi = crea_gruppi(elenco_piani)
                if messaggi_gruppi:
                    for messaggio_mastro in messaggi_gruppi:
                        messaggi.append(messaggio_mastro)
                print('FINE CREAZIONE GRUPPI')
            if numero_livelli > 2:
                print('INIZIO CREAZIONE MASTRI')
                lunghezza_codice_mastro = len(codici_conto[-3])
                preferences.ImpostazioniMagister.numero_cifre_mastro = lunghezza_codice_mastro
                preferences.ImpostazioniMagister.save()
                messaggi_mastri = crea_mastri(elenco_piani)
                if messaggi_mastri:
                    for messaggio_mastro in messaggi_mastri:
                        messaggi.append(messaggio_mastro)
                print('FINE CREAZIONE MASTRI')
            if numero_livelli > 1:
                print('INIZIO CREAZIONE CONTI')
                lunghezza_codice_conto = len(codici_conto[-2])
                preferences.ImpostazioniMagister.numero_cifre_conto = lunghezza_codice_conto
                preferences.ImpostazioniMagister.save()
                messaggi_conti = crea_conti(elenco_piani)
                if messaggi_conti:
                    for messaggio_conto in messaggi_conti:
                        messaggi.append(messaggio_conto)
                print('FINE CREAZIONE CONTI')
            if numero_livelli > 0:
                print('INIZIO CREAZIONE SOTTOCONTI')
                lunghezza_codice_sottoconto = len(codici_conto[-1])
                preferences.ImpostazioniMagister.numero_cifre_sottoconto = lunghezza_codice_sottoconto
                preferences.ImpostazioniMagister.save()
                messaggi_sottoconti = crea_sottoconti(elenco_piani)
                if messaggi_sottoconti:
                    for messaggio_sottoconto in messaggi_sottoconti:
                        messaggi.append(messaggio_sottoconto)
                print('FINE CREAZIONE SOTTOCONTI')
            print('INIZIO REBUILD')
            PianoDeiConti.objects.rebuild()
            print('FINE REBUILD')
            messaggi.append('Importato piano dei conti con successo!')
    return messaggi


def importa_movimenti_prima_nota(dati_file_mdb, area, esercizio):
    messaggi = []
    if dati_file_mdb and area and esercizio:
        elenco_record = []
        elenco_movimenti = dati_file_mdb.decode('utf-8').split('\n')
        if elenco_movimenti:
            for riga_movimento in elenco_movimenti:
                if riga_movimento:
                    movimento_prima_nota = json.loads(riga_movimento)
                    print('INSERIMENTI MOVIMENTO: %s' % movimento_prima_nota)
                    nuovo_movimento = MovimentoPrimaNota()
                    nuovo_movimento.id = movimento_prima_nota.get('IDmov')
                    nuovo_movimento.area = area
                    nuovo_movimento.esercizio = esercizio
                    nuovo_movimento.numero_operazione = movimento_prima_nota.get('Progressivo')
                    nuovo_movimento.descrizione = movimento_prima_nota.get('Descrizione')
                    nuovo_movimento.data = movimento_prima_nota.get('Data')
                    nuovo_movimento.data_operazione = movimento_prima_nota.get('Data')
                    # nuovo_movimento.save()
                    elenco_record.append(nuovo_movimento)
            messaggi.append('Importati movimenti prima nota con successo!')
        if elenco_record:
            MovimentoPrimaNota.objects.bulk_create(elenco_record)
    return messaggi


def get_piano_da_codice(codice_sottoconto):
    if codice_sottoconto:
        parti_codice = codice_sottoconto.split(
            preferences.ImpostazioniMagister.separatore_codice_conti
        )
        codice_mastro = ''
        codice_gruppo = ''
        codice_conto = ''
        codice_sottoconto = ''
        numero_livelli = len(parti_codice)
        if numero_livelli > 0:
            codice_sottoconto = int(parti_codice[-1])
        if numero_livelli > 1:
            codice_conto = int(parti_codice[-2])
        if numero_livelli > 2:
            codice_mastro = int(parti_codice[-3])
        if numero_livelli > 3:
            codice_gruppo = int(parti_codice[-4])
        if numero_livelli == 4:
            try:
                piano = PianoDeiConti.objects.get(
                    codice_mastro=codice_mastro,
                    codice_conto=codice_conto,
                    codice_sottoconto=codice_sottoconto,
                    codice_gruppo=codice_gruppo,
                )
                return piano
            except PianoDeiConti.DoesNotExist:
                pass
        if numero_livelli == 3:
            try:
                piano = PianoDeiConti.objects.get(
                    codice_mastro=codice_mastro,
                    codice_conto=codice_conto,
                    codice_sottoconto=codice_sottoconto,
                )
                return piano
            except PianoDeiConti.DoesNotExist:
                pass
        if numero_livelli == 2:
            try:
                piano = PianoDeiConti.objects.get(
                    codice_conto=codice_conto,
                    codice_sottoconto=codice_sottoconto,
                )
                return piano
            except PianoDeiConti.DoesNotExist:
                pass
        if numero_livelli == 1:
            try:
                piano = PianoDeiConti.objects.get(
                    codice_sottoconto=codice_sottoconto,
                )
                return piano
            except PianoDeiConti.DoesNotExist:
                pass


def importa_dettagli_movimenti(dati_file_mdb, area, esercizio):
    messaggi = []
    if dati_file_mdb and area and esercizio:
        elenco_record = []
        elenco_dettagli_movimenti = dati_file_mdb.decode('utf-8').split('\n')
        if elenco_dettagli_movimenti:
            for riga_dettaglio_movimento in elenco_dettagli_movimenti:
                if riga_dettaglio_movimento:
                    dettaglio_movimento = json.loads(riga_dettaglio_movimento)
                    piano = None
                    movimento = None
                    sottoconto = dettaglio_movimento.get('Sottoconto')
                    if sottoconto:
                        piano = get_piano_da_codice(sottoconto)
                    id_movimento = dettaglio_movimento.get('IDmov')
                    if id_movimento:
                        try:
                            movimento = MovimentoPrimaNota.objects.get(id=id_movimento)
                        except MovimentoPrimaNota.DoesNotExist:
                            pass
                    if piano and movimento:
                        nuovo_dettaglio = DettaglioMovimentoPrimaNota()
                        nuovo_dettaglio.movimento_primanota = movimento
                        nuovo_dettaglio.piano_dei_conti = piano
                        nuovo_dettaglio.progressivo = dettaglio_movimento.get('Ordine')
                        nuovo_dettaglio.descrizione = dettaglio_movimento.get('Descrizione')
                        valuta = dettaglio_movimento.get('Valuta')
                        if not valuta:
                            valuta = settings.VALUTA_DEFAULT
                        nuovo_dettaglio.importo = Money(dettaglio_movimento.get('Importo'), valuta)
                        if dettaglio_movimento.get('Controvalore'):
                            nuovo_dettaglio.controvalore = Money(dettaglio_movimento.get('Controvalore'), settings.VALUTA_DEFAULT)
                        else:
                            nuovo_dettaglio.controvalore = Money(dettaglio_movimento.get('Importo'), valuta)
                        if dettaglio_movimento.get('Dare') == 1:
                            nuovo_dettaglio.categoria = 'dare'
                        else:
                            nuovo_dettaglio.categoria = 'avere'
                        elenco_record.append(nuovo_dettaglio)
                    else:
                        if not piano:
                            messaggi.append('ERRORE - conto codice %s NON TROVATO!' % sottoconto)
                        if not movimento:
                            messaggi.append('ERRORE - movimento con ID %s NON TROVATO!' % id_movimento)
            if elenco_record:
                DettaglioMovimentoPrimaNota.objects.bulk_create(elenco_record)
            messaggi.append('Importati dettagli movimenti prima nota con successo!')
    return messaggi


def get_provincia_da_codice(codice_provincia):
    if codice_provincia:
        try:
            provincia = models.Provincia.objects.get(codice=codice_provincia.strip())
            return provincia
        except models.Provincia.DoesNotExist:
            try:
                provincia = models.Provincia.objects.get(descrizione__iexact=codice_provincia.strip())
                return provincia
            except models.Provincia.DoesNotExist:
                if len(codice_provincia) > 2:
                    provincia = models.Provincia.objects.create(codice=codice_provincia.strip(), descrizione=codice_provincia.strip())
                    return provincia
    return None


def valida_data(data):
    if data:
        try:
            data_valida = timezone.datetime.strptime(data, '%Y-%m-%d')
            return data_valida
        except ValueError:
            return None


def get_nazione_da_nome(nome_nazione):
    if nome_nazione:
        try:
            nazione = models.Stato.objects.get(descrizione__iexact=nome_nazione.strip())
            return nazione
        except models.Stato.DoesNotExist:
            try:
                nazione = models.Stato.objects.get(codice__iexact=nome_nazione.strip())
                return nazione
            except models.Stato.DoesNotExist:
                try:
                    nazione = models.Stato.objects.get(descrizione_inglese__iexact=nome_nazione.strip())
                    return nazione
                except models.Stato.DoesNotExist:
                    pass
    return None


class ImportazioneDatiMatthaeusForm(forms.Form):
    file_database = forms.FileField(
        label='file database (mdb)',
        help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB
    )
    tipo_importazione = forms.ChoiceField(
        label=_('Tipo Importazione'), widget=forms.RadioSelect(attrs={'class': 'radiolist'}), choices=TIPI_IMPORTAZIONI_MATTHAEUS,
    )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request')
        super(ImportazioneDatiMatthaeusForm, self).__init__(*args, **kwargs)

    def save(self):
        dati_file = self.cleaned_data['file_database']
        messaggi = None
        nome_file_temporaneo = tempfile.mktemp()
        f = open(nome_file_temporaneo, 'wb')
        f.write(dati_file.read())
        f.close()
        # comando_leggi_tipo_file = ['mdb-ver', nome_file_temporaneo]
        # risultato = subprocess.check_output(comando_leggi_tipo_file)
        # print('#####################################################')
        # print('comando -> %s' % comando_leggi_tipo_file)
        # print('#####################################################')
        # print('RISULTATO: %s' % risultato)
        if self.cleaned_data['tipo_importazione'] == 'piano_dei_conti':
            elenco_piani = PianoDeiConti.objects.all()
            if elenco_piani:
                messages.error(self.request, 'ERRORE - Impossibile continuare: Piano dei conti gia\' presente!')
            else:
                comando_leggi_piano_json = ['mdb-json', nome_file_temporaneo, NOME_TABELLA_PIANO_DEI_CONTI]
                piani = subprocess.check_output(comando_leggi_piano_json)
                messaggi = importa_piano_dei_conti(piani)
                # aggiungi_messaggi(self.request, messaggi)
        elif self.cleaned_data['tipo_importazione'] == 'movimenti_prima_nota':
            area_corrente = self.request.user.area_corrente
            esercizio_corrente = self.request.user.esercizio_corrente
            if area_corrente and esercizio_corrente:
                elenco_movimenti = MovimentoPrimaNota.objects.all()
                if elenco_movimenti:
                    messages.error(self.request, 'ERRORE - Impossibile continuare: Movimenti Prima Nota gia\' presenti!')
                else:
                    # prima i movimenti
                    comando_leggi_movimenti_json = ['mdb-json', nome_file_temporaneo, NOME_TABELLA_MOVIMENTI, '--datetime-format=%Y-%m-%d']
                    movimenti_prima_nota = subprocess.check_output(comando_leggi_movimenti_json)
                    messaggi = importa_movimenti_prima_nota(movimenti_prima_nota, area_corrente, esercizio_corrente)
                    # aggiungi_messaggi(self.request, messaggi)
                    # poi i dettagli
                    comando_leggi_dettagli_json = ['mdb-json', nome_file_temporaneo, NOME_TABELLA_DETTAGLI_MOVIMENTI]
                    dettagli_movimenti = subprocess.check_output(comando_leggi_dettagli_json)
                    messaggi = importa_dettagli_movimenti(dettagli_movimenti, area_corrente, esercizio_corrente)
                    # aggiungi_messaggi(self.request, messaggi)
            else:
                messaggio = 'Selezionare un\'area ed un esercizio per procedere con l\'importazione dei movimenti!'
                messages.error(self.request, messaggio)


class ImportazioneDatiPubliusForm(forms.Form):
    file_database = forms.FileField(
        label='file database (mdb)',
        help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB
    )
    tipo_importazione = forms.ChoiceField(
        label=_('Tipo Importazione'), widget=forms.RadioSelect(attrs={'class': 'radiolist'}), choices=TIPI_IMPORTAZIONI_PUBLIUS,
    )
    aggiorna_dati = forms.BooleanField(
        label='Aggiorna dati',
        required=False,
        help_text='Se selezionato, i dati presenti verranno aggiornati con quelli del file importato'
    )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request')
        super(ImportazioneDatiPubliusForm, self).__init__(*args, **kwargs)

    def save(self):
        dati_file = self.cleaned_data['file_database']
        messaggi = None
        nuovo_log = models.LogImportazione()
        nuovo_log.data_inizio = timezone.now()
        nuovo_log.applicativo = 'publius'
        nuovo_log.tipo_importazione = self.cleaned_data['tipo_importazione']
        nuovo_log.stato_importazione = 'in corso'
        nuovo_log.file_importazione = dati_file
        nuovo_log.save()
        from publius.tasks import carica_dati_publius
        aggiorna_dati = self.cleaned_data['aggiorna_dati']
        carica_dati_publius.delay(
            nuovo_log.id,
            self.request.user.area_corrente.id,
            aggiorna_dati,
        )


class ImportazioneDatiMartinusForm(forms.Form):
    file_database = forms.FileField(
        label='file database (mdb)',
        help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB
    )
    tipo_importazione = forms.ChoiceField(
        label=_('Tipo Importazione'), widget=forms.RadioSelect(attrs={'class': 'radiolist'}), choices=TIPI_IMPORTAZIONI_MARTINUS,
    )
    aggiorna_dati = forms.BooleanField(
        label='Aggiorna dati',
        required=False,
        help_text='Se selezionato, i dati presenti verranno aggiornati con quelli del file importato'
    )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request')
        super(ImportazioneDatiMartinusForm, self).__init__(*args, **kwargs)

    def save(self):
        dati_file = self.cleaned_data['file_database']
        messaggi = None
        nuovo_log = models.LogImportazione()
        nuovo_log.data_inizio = timezone.now()
        nuovo_log.applicativo = 'martinus'
        nuovo_log.tipo_importazione = self.cleaned_data['tipo_importazione']
        nuovo_log.stato_importazione = 'in corso'
        nuovo_log.file_importazione = dati_file
        nuovo_log.save()
        from martinus.tasks import carica_dati_martinus
        aggiorna_dati = self.cleaned_data['aggiorna_dati']
        carica_dati_martinus.delay(
            nuovo_log.id,
            aggiorna_dati,
        )
        