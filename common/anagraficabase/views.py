import random

from django.utils.translation import ugettext_lazy as _
from django.utils import timezone
from django.db.models import Max
from django.views.generic.edit import FormView
from django.conf import settings

from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from .models import Esercizio, FraseMotivazionale, SantiDelGiorno
from .serializers import EsercizioSerializer, FraseMotivazionaleSerializer, SantiDelGiornoSerializer
from common.anagraficabase.forms import (
    ImportazioneDatiPubliusForm, ImportazioneDatiMatthaeusForm,
    ImportazioneDatiMartinusForm,
)


class EsercizioViewSet(viewsets.ModelViewSet):
    allowed_methods = ('GET',)

    queryset = Esercizio.objects.all()
    serializer_class = EsercizioSerializer


class ImportazioneDatiMatthaeusView(FormView):
    template_name = 'upload.html'
    form_class = ImportazioneDatiMatthaeusForm
    success_url = '/matthaeus/anagraficabase/pianodeiconti'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from matthaeus.admin import site
        context.update(site.each_context(self.request))
        return context

    def dispatch(self, request, *args, **kwargs):
        request.current_app = 'matthaeus'
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        if 'tipo_importazione' in form.data:
            if form.data['tipo_importazione'] == 'movimenti_prima_nota':
                self.success_url = '/matthaeus/movimenti/movimentoprimanota'
        form.save()
        return super(ImportazioneDatiMatthaeusView, self).form_valid(form)

    def get_form_kwargs(self):
        kwargs = super(ImportazioneDatiMatthaeusView, self).get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs


class ImportazioneDatiPubliusView(FormView):
    template_name = 'upload.html'
    form_class = ImportazioneDatiPubliusForm
    success_url = '/publius/anagraficabase/logimportazione'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from publius.admin import site
        context.update(site.each_context(self.request))
        return context

    def dispatch(self, request, *args, **kwargs):
        request.current_app = 'publius'
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.save()
        return super(ImportazioneDatiPubliusView, self).form_valid(form)

    def get_form_kwargs(self):
        kwargs = super(ImportazioneDatiPubliusView, self).get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs


class ImportazioneDatiMartinusView(FormView):
    template_name = 'upload.html'
    form_class = ImportazioneDatiMartinusForm
    success_url = '/martinus/anagraficabase/logimportazione'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from martinus.admin import site
        context.update(site.each_context(self.request))
        return context

    def dispatch(self, request, *args, **kwargs):
        request.current_app = 'martinus'
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.save()
        return super(ImportazioneDatiMartinusView, self).form_valid(form)

    def get_form_kwargs(self):
        kwargs = super(ImportazioneDatiMartinusView, self).get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs


def get_random_object(queryset):
    max_id = queryset.aggregate(max_id=Max("id"))['max_id']
    while True:
        pk = random.randint(1, max_id)
        oggetto_casuale = queryset.filter(pk=pk).first()
        if oggetto_casuale:
            return oggetto_casuale


def get_frase_del_giorno(giorno, mese):
    elenco_frasi = FraseMotivazionale.objects.all()
    if elenco_frasi:
        frasi_mese = elenco_frasi.filter(mese_calendario=mese)
        if frasi_mese:
            frasi_giorno = frasi_mese.filter(giorno_calendario=giorno)
            if frasi_giorno:
                return get_random_object(frasi_giorno)
            else:
                return get_random_object(frasi_mese)
        return get_random_object(elenco_frasi)
    

class FraseMotivazionaleView(APIView):
    allowed_methods = ['GET', ]
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        data_corrente = timezone.now()
        mese_corrente = data_corrente.month
        giorno_corrente = data_corrente.day
        frase_del_giorno = get_frase_del_giorno(giorno_corrente, mese_corrente)
        frase_motivazionale = FraseMotivazionaleSerializer(frase_del_giorno)
        return Response(frase_motivazionale.data)


class SantiDelGiornoView(APIView):
    allowed_methods = ['GET', ]
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        data_corrente = timezone.now()
        try:
            elenco_santi = SantiDelGiorno.objects.get(giorno=data_corrente.day, mese=data_corrente.month)
        except SantiDelGiorno.DoesNotExist:
            elenco_santi = None
        santi_del_giorno = SantiDelGiornoSerializer(elenco_santi)
        return Response(santi_del_giorno.data)


class LingueView(APIView):
    allowed_methods = ['GET', ]
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        elenco_lingue = settings.LANGUAGES
        dizionario_lingue = dict(elenco_lingue)
        return Response(dizionario_lingue)
