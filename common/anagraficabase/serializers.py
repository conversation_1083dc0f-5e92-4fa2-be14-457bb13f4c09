from rest_framework import serializers

from .models import <PERSON>sercizio, FraseMotivazionale, SantiDelGiorno, Stato


class EsercizioSerializer(serializers.HyperlinkedModelSerializer):
    esercizio_corrente = serializers.SerializerMethodField()

    def get_esercizio_corrente(self, esercizio):
        return self.context['request'].user.esercizio_corrente == esercizio

    class Meta:
        model = Esercizio
        fields = ['id', 'nome', 'esercizio_corrente', 'data_inizio', 'data_fine']


class FraseMotivazionaleSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = FraseMotivazionale
        fields = ['id', 'testo', 'autore']


class SantiDelGiornoSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = SantiDelGiorno
        fields = ['id', 'mese', 'giorno', 'santi']


class NazioneSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Stato
        fields = ('id', 'descrizione', 'codice',)
