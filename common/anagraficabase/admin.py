from django_mptt_admin.admin import DjangoMpttAdmin
from django.http import Http404, HttpResponseRedirect
from django.contrib import admin
from django.contrib import messages
from django.conf.urls import url
from django.conf import settings
from django.contrib.admin.utils import unquote
from django.utils.translation import ugettext_lazy as _
from django.urls import reverse
from django.utils.html import format_html
from django.contrib.admin.filters import RelatedOnlyFieldListFilter
from django.shortcuts import redirect
from django.views.decorators.csrf import csrf_protect
from django.template.defaultfilters import slugify

from preferences.admin import PreferencesAdmin
from django.utils.decorators import method_decorator
from common.anagraficabase import forms
from common.anagraficabase.resources import AreaResource
from common.anagraficabase.views import (
    ImportazioneDatiMatthaeusView, ImportazioneDatiPubliusView,
    ImportazioneDatiMartinusView,
)
from common.anagraficabase.models import ConfigurazioneDashboard, ChiusuraArea, CambioStorico, MagisterOpenExchangeRatesBackend
from matthaeus.movimenti.utils import aggiorna_statistiche_grafici
from import_export.admin import ImportMixin
from djmoney.contrib.exchange.models import Rate


csrf_protect_m = method_decorator(csrf_protect)


class ChiusuraAreaInline(admin.TabularInline):
    list_display = (
        'area', 'esercizio', 'chiuso', 'data_chiusura',
    )
    suit_classes = 'suit-tab suit-tab-chiusura'
    readonly_fields = ('data_chiusura', 'area', 'esercizio', 'chiuso')
    autocomplete_fields = ('area', 'esercizio')
    model = ChiusuraArea
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class ImpostazioniPubliusAdmin(PreferencesAdmin):
    fieldsets = (
        (
            _('IMPOSTAZIONI RELIGIOSI'), dict(
                fields=(
                    'appellativo_predefinito',
                    'sovrascrivi_residenza',
                    'creazione_automatica_trasferimento',
                    'provincia_religiosa_default',
                    'sovrascrivi_provincia_religiosa_da_casa',
                )
            )
        ),
        (
            _('IMPOSTAZIONI STAMPE'), dict(
                fields=(
                    'ordine_campi_stampa',
                    'prima_colonna_stampa',
                    'ordinamento_religiosi_stampa',
                    'nascondi_provincia_religiosa_default',
                )
            )
        ),
    )
    form = forms.ImpostazioniMagisterForm

    @csrf_protect_m
    def changelist_view(self, request, extra_context=None):
        """
        If we only have a single preference object redirect to it,
        otherwise display listing.
        """
        model = self.model
        if model.objects.all().count() > 1:
            return super(PreferencesAdmin, self).changelist_view(request)
        else:
            obj = model.singleton.get()
            return redirect(
                reverse(
                    'publius:%s_%s_change' % (
                        model._meta.app_label, model._meta.model_name
                    ),
                    args=(obj.id,)
                )
            )


class ConfigurazioneDashboardInline(admin.TabularInline):
    fields = (
        'magister_dashboard', 'widget', 'dimensione', 'ordinamento', 'attivo',
    )
    model = ConfigurazioneDashboard
    extra = 0


class ConfigurazioneDashboardAdmin(admin.ModelAdmin):
    list_display = (
        'magister_dashboard', 'widget', 'dimensione', 'ordinamento', 'attivo',
    )
    list_filter = (
        'magister_dashboard', 'widget', 'dimensione', 'attivo',
    )
   

class MagisterDashboardAdmin(admin.ModelAdmin):
    list_display = (
        'nome', 'descrizione', 'applicazione', 'nome_dashboard', 
    )
    list_filter = (
        'applicazione',
    )
    search_fields = ('nome', 'descrizione')
    inlines = [ConfigurazioneDashboardInline, ]


class ConfigurazioneWidgetAdmin(admin.ModelAdmin):
    list_display = (
        'nome', 'descrizione', 'nome_widget_1', 'nome_widget_2', 'nome_widget_3', 'nome_widget_4'
    )


class TipoAreaAdmin(admin.ModelAdmin):
    pass


class AreaAdmin(ImportMixin, DjangoMpttAdmin):
    list_display = (
        'nome', 'parent', 'valuta_default',
        'piano_dei_conti_personalizzato', 'anagrafica_personalizzata', 
        'centri_di_costo_personalizzati', 'get_link_area_default'
    )
    list_filter = (
        'tipo_area', 'valuta_default', 'piano_dei_conti_personalizzato', 'anagrafica_personalizzata',
        'centri_di_costo_personalizzati'
    )
    search_fields = ('nome', 'descrizione', 'codice', 'parent__nome')
    change_list_template = 'area_change_list.html'
    # change_tree_template = 'area_change_tree.html'
    resource_class = AreaResource

    def is_drag_and_drop_enabled(self):
        return False

    def get_link_area_default(self, obj):
        url = reverse('%s:anagraficabase_area_changelist' % self.admin_site.name)
        return format_html('<a href="%s%s/setareadefault">%s</a>' % (url, obj.id, 'Imposta Default'))
    get_link_area_default.short_description = _('Default')
    get_link_area_default.allow_tags = True

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(\d+)/setareadefault/$',
                self.admin_site.admin_view(self.set_area_default),
                name='%s_%s_setareadefault' % info
            ),
            url(
                r'^importazione_dati_publius/$',
                self.admin_site.admin_view(ImportazioneDatiPubliusView.as_view()),
                name='%s_%s_importazione_dati_publius' % info
            ),
            url(
                r'^importazione_dati_martinus/$',
                self.admin_site.admin_view(ImportazioneDatiMartinusView.as_view()),
                name='%s_%s_importazione_dati_martinus' % info
            ),
            url(
                r'^importazione_dati_matthaeus/$',
                self.admin_site.admin_view(ImportazioneDatiMatthaeusView.as_view()),
                name='%s_%s_importazione_dati_matthaeus' % info
            ),
        ]
        url_patterns += super(AreaAdmin, self).get_urls()
        return url_patterns

    def set_area_default(self, request, object_id):
        area = self.get_object(request, unquote(object_id))
        if not area:
            raise Http404()
        from common.authentication.models import UtenteMagister
        id_utente = request.user.id
        utente_corrente = UtenteMagister.objects.get(pk=id_utente)
        elenco_aree_abilitate = utente_corrente.areaabilitata_set.all()
        if elenco_aree_abilitate:
            abilitato = False
            for area_abilitata in elenco_aree_abilitate:
                if area_abilitata.area == area:
                    abilitato = True
                    break
        else:
            abilitato = True
        if abilitato:
            utente_corrente.area_corrente = area
            utente_corrente.save()
            messaggio = _('modificata area corrente -> %s') % area
            messages.success(request, messaggio)
            url = reverse('%s:anagraficabase_area_changelist' % self.admin_site.name) + 'grid'
            return HttpResponseRedirect(url)
        else:
            messaggio = _('L\' utente corrente non ha i permessi per accedere all\'area selezionata.')
            messages.warning(request, messaggio)
            url = reverse('%s:anagraficabase_area_changelist' % self.admin_site.name) + 'grid'
            return HttpResponseRedirect(url)


class EsercizioAdmin(admin.ModelAdmin):
    list_display = (
        'nome', 'descrizione', 'data_inizio', 'data_fine', 'get_link_esercizio_default'
    )
    date_hierarchy = ('data_inizio')
    search_fields = ('nome', 'descrizione')
    
    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(\d+)/seteserciziodefault/$',
                self.admin_site.admin_view(self.set_esercizio_default),
                name='%s_%s_seteserciziodefault' % info
            ),
        ]
        url_patterns += super(EsercizioAdmin, self).get_urls()
        return url_patterns

    def set_esercizio_default(self, request, object_id):
        esercizio = self.get_object(request, unquote(object_id))
        if not esercizio:
            raise Http404()
        from common.authentication.models import UtenteMagister
        id_utente = request.user.id
        utente_corrente = UtenteMagister.objects.get(pk=id_utente)
        utente_corrente.esercizio_corrente = esercizio
        utente_corrente.save()
        messages.success(request, _('Modificato esercizio corrente -> %s') % esercizio)
        return HttpResponseRedirect('/%s/anagraficabase/esercizio/' % self.admin_site.name)


class ProvinciaAdmin(admin.ModelAdmin):
    list_display = (
        'descrizione', 'nazione', 'codice'
    )
    search_fields = ('descrizione', 'nazione__descrizione', 'codice')
    autocomplete_fields = ('nazione', )
    list_filter = (
        ('nazione', RelatedOnlyFieldListFilter),
    )
    fieldsets = (
        (
            _('Dati principali'), dict(
                fields=(
                    'codice',
                    'descrizione',
                    'nazione',
                )
            )
        ),
    )


class StatoAdmin(admin.ModelAdmin):
    list_display = ('id', 'descrizione', 'descrizione_inglese', 'area', 'continente', 'codice')
    search_fields = ('descrizione', 'descrizione_inglese', 'continente', 'area', 'codice')
    list_editable = ('codice', )
    list_filter = ('continente', 'area')


class StatisticheGraficiAdmin(admin.ModelAdmin):
    list_display = (
        'area', 'esercizio', 'grafico', 'data_ultimo_aggiornamento',
    )
    date_hierarchy = ('data_ultimo_aggiornamento')
    list_filter = ('area', 'esercizio', 'grafico')

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^aggiorna/',
                self.admin_site.admin_view(self.aggiorna_grafici),
                name='%s_%s_aggiorna' % info
            ),
        ]
        url_patterns += super(StatisticheGraficiAdmin, self).get_urls()
        return url_patterns

    def aggiorna_grafici(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        aggiorna_statistiche_grafici(area=area_corrente, esercizio=esercizio_corrente)
        messaggio = _('Aggiornate statistiche grafici')
        messages.success(request, messaggio)
        url = '/matthaeus/dashboard/1/'
        return HttpResponseRedirect(url)


class FraseMotivazionaleAdmin(admin.ModelAdmin):
    list_display = ('id', 'testo', 'autore', 'giorno_calendario', 'mese_calendario')
    search_fields = ('testo', 'autore')
    list_filter = ('autore', 'mese_calendario', 'giorno_calendario')


class SantiDelGiornoAdmin(admin.ModelAdmin):
    list_display = ('mese', 'giorno',  'santi')
    search_fields = ('santi', )
    list_filter = ('mese', 'giorno')


class ApplicazioneMagisterAdmin(admin.ModelAdmin):
    list_display = (
        'app_label', 'model',
    )
    search_fields = ('app_label', 'model')
    list_filter = ('app_label', )

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class TassidiCambioInline(admin.TabularInline):
    model = Rate
    extra = 0
    readonly_fields = ('get_currency', )
    fields = ('get_currency', 'value')
    verbose_name_plural = _('Tassi di Cambio')

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_currency(self, obj):
        return obj.currency
    get_currency.short_description = _('Valuta')


class CambioStoricoAdmin(admin.ModelAdmin):
    list_display = ('get_name', 'data_inizio', 'data_fine', 'get_base_currency', 'get_last_update')
    search_fields = ('name', )
    date_hierarchy = 'data_inizio'
    readonly_fields = ('get_name', 'get_last_update', 'get_base_currency')
    fieldsets = (
        (None, {
            "fields": (
                'get_name', 'get_last_update', 'get_base_currency', 'data_inizio', 'data_fine'
            ),
        }),
    )
    inlines = [TassidiCambioInline]

    def get_name(self, obj):
        return obj.name
    get_name.short_description = _('Nome')
    
    def get_last_update(self, obj):
        return obj.last_update
    get_last_update.short_description = _('Ultimo Aggiornamento')

    def get_base_currency(self, obj):
        return obj.base_currency
    get_base_currency.short_description = _('Valuta di Riferimento')

    def has_add_permission(self, request):
        return False
    
    def get_urls(self):
        url_patterns = [
            url(
                r'^aggiorna/',
                self.admin_site.admin_view(self.aggiorna),
                name='anagraficabase_cambiostorico_aggiorna'
            ),
        ]
        url_patterns += super(CambioStoricoAdmin, self).get_urls()
        return url_patterns

    def aggiorna(self, request):
        backend = MagisterOpenExchangeRatesBackend()
        url = reverse('matthaeus:anagraficabase_cambiostorico_changelist')        
        elenco_valute = settings.CURRENCY_CHOICES
        elenco_valute_lista = []
        for valuta in elenco_valute:
            elenco_valute_lista.append(valuta[0])
        lista_valute = ','.join(elenco_valute_lista)
        backend.update_rates(symbols=lista_valute)
        messaggio = _('Tassi di cambio aggiornati correttamente!')
        messages.success(request, messaggio)
        return HttpResponseRedirect(url)


class LogImportazioneAdmin(admin.ModelAdmin):
    list_display = (
        'data_inizio', 'data_fine', 'applicativo', 'tipo_importazione', 'stato_importazione', 'numero_record'
    )
    search_fields = ('tipo_importazione',)
    date_hierarchy = ('data_inizio')
    list_filter = ('applicativo', 'stato_importazione')
    readonly_fields = (
        'tipo_importazione', 'data_inizio', 'data_fine', 'applicativo', 'numero_record', 'stato_importazione',
        'numero_record_importati', 'file_importazione',
    )

    def has_add_permission(self, request):
        return False
