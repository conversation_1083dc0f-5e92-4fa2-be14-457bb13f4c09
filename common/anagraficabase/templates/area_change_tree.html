{% extends 'admin/change_list.html' %}
{% load i18n admin_list %}
{% load admin_urls %}

{% block extrastyle %}
    {{ block.super }}
    <script type="text/javascript" src="{{ jsi18n_url }}"></script>
{% endblock %}

{% block search %}{% endblock %}

{% block result_list %}
    <div
        id="tree"
        class="block-style"
        data-auto_open="{{ tree_auto_open }}"
        data-autoescape="{{ autoescape }}"
        data-csrf-cookie-name="{{ csrf_cookie_name }}"
        data-insert_at_url="{{ insert_at_url }}"
        data-drag-and-drop="{{ drag_and_drop }}"
        {% if LANGUAGE_BIDI %}
            data-rtl
        {% endif %}
        data-save_state="{{ app_label }}_{{ model_name }}"
        {% if tree_animation_speed is not None %}
            data-tree-animation-speed="{{ tree_animation_speed }}"
        {% endif %}
        {% if tree_mouse_delay is not None %}
            data-tree-mouse-delay="{{ tree_mouse_delay }}"
        {% endif %}
        data-url="{{ tree_json_url }}"
        data-use_context_menu="{{ use_context_menu }}"
    ></div>
{% endblock %}

{% block filters %}
    {% if cl.has_filters %}
        <div id="changelist-filter">
          <h2>{% trans 'Filter' %}</h2>
          {% for spec in cl.filter_specs %}{% admin_list_filter cl spec %}{% endfor %}
        </div>
    {% endif %}
{% endblock %}

{% block pagination %}{% endblock %}

{% block object-tools-items %}
    {{ block.super }}
    <li>
        <a href="{{ grid_url }}">{% trans "Grid view" %}</a>
    </li>
{% endblock %}
