from django.conf import settings

from djmoney.money import Money

from matthaeus.movimenti.models import MovimentoPrimaNota, DettaglioMovimentoPrimaNota
from matthaeus.movimenti.models import BilancioConto
from matthaeus.movimenti.utils import get_queryset_bilancio_calcolato
from matthaeus.movimenti.utils import get_nuovo_numero

from common.anagraficabase.models import PianoDeiConti, AbilitazionePianoDeiConti, ChiusuraArea


def get_piano_conti_personalizzato(area_corrente, queryset=None):
    elenco_piani = None
    if queryset:
        elenco_piani = queryset
    else:
        elenco_piani = PianoDeiConti.objects.all()
    if area_corrente:
        if area_corrente.piano_dei_conti_personalizzato:
            elenco_piani_abilitati = AbilitazionePianoDeiConti.objects.filter(
                area_abilitata=area_corrente
            ).values('piano_dei_conti__id')
            elenco_piani = elenco_piani.filter(id__in=elenco_piani_abilitati)
    return elenco_piani


def crea_movimento_chiusura_costi(esercizio, area):
    chiusura_costi = MovimentoPrimaNota()
    chiusura_costi.numero_operazione = get_nuovo_numero(area, esercizio)
    chiusura_costi.data = esercizio.data_operazione_chiusura
    chiusura_costi.data_operazione = esercizio.data_operazione_chiusura
    chiusura_costi.descrizione = '%s - CHIUSURA COSTI' % esercizio.descrizione_operazione_chiusura
    chiusura_costi.area = area
    chiusura_costi.esercizio = esercizio
    chiusura_costi.genere_movimento = 'chiusura'
    chiusura_costi.save()
    elenco_bilanci_costi_base = BilancioConto.objects.filter(
        piano_dei_conti__tipologia__in=['economico', 'costi', 'ricavi'],
        piano_dei_conti__livello='sottoconto', area=area, esercizio=esercizio
    )
    elenco_bilanci_costi = get_queryset_bilancio_calcolato(elenco_bilanci_costi_base, area, esercizio)
    for bilancio_costo in elenco_bilanci_costi:
        saldo = bilancio_costo.get_saldo()
        if saldo:
            if saldo > Money(0.00, area.valuta_default):
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_costi
                dettaglio_dare.piano_dei_conti = esercizio.conto_profitti_perdite
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = abs(saldo)
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_costi
                dettaglio_avere.piano_dei_conti = bilancio_costo.piano_dei_conti
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = abs(saldo)
                dettaglio_avere.save()


def crea_movimento_chiusura_ricavi(esercizio, area):
    chiusura_ricavi = MovimentoPrimaNota()
    chiusura_ricavi.numero_operazione = get_nuovo_numero(area, esercizio)
    chiusura_ricavi.data = esercizio.data_operazione_chiusura
    chiusura_ricavi.data_operazione = esercizio.data_operazione_chiusura
    chiusura_ricavi.descrizione = '%s - CHIUSURA RICAVI' % esercizio.descrizione_operazione_chiusura
    chiusura_ricavi.area = area
    chiusura_ricavi.esercizio = esercizio
    chiusura_ricavi.genere_movimento = 'chiusura'
    chiusura_ricavi.save()
    elenco_bilanci_ricavi_base = BilancioConto.objects.filter(
        piano_dei_conti__tipologia__in=['economico', 'costi', 'ricavi'],
        piano_dei_conti__livello='sottoconto', area=area, esercizio=esercizio
    )
    elenco_bilanci_ricavi = get_queryset_bilancio_calcolato(elenco_bilanci_ricavi_base, area, esercizio)
    for bilancio_ricavo in elenco_bilanci_ricavi:
        saldo = bilancio_ricavo.get_saldo()
        if saldo:
            if saldo < Money(0.00, area.valuta_default):
                saldo_netto = abs(saldo)
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_ricavi
                dettaglio_avere.piano_dei_conti = esercizio.conto_profitti_perdite
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_netto
                dettaglio_avere.save()
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_ricavi
                dettaglio_dare.piano_dei_conti = bilancio_ricavo.piano_dei_conti
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_netto
                dettaglio_dare.save()


def crea_movimento_rilevazione_utile_perdita(esercizio, area):
    if esercizio.conto_profitti_perdite:
        conto_profitti_perdite = BilancioConto.objects.filter(
            area=area, esercizio=esercizio, piano_dei_conti=esercizio.conto_profitti_perdite
        )
        elenco_bilanci = get_queryset_bilancio_calcolato(conto_profitti_perdite, area, esercizio)
        if elenco_bilanci:
            bilancio_conto_profitti_perdite = elenco_bilanci[0]
            saldo_profitti_perdite = bilancio_conto_profitti_perdite.get_saldo()
            chiusura_rilevazione_utile_perdita = MovimentoPrimaNota()
            chiusura_rilevazione_utile_perdita.numero_operazione = get_nuovo_numero(area, esercizio)
            chiusura_rilevazione_utile_perdita.data = esercizio.data_operazione_chiusura
            chiusura_rilevazione_utile_perdita.data_operazione = esercizio.data_operazione_chiusura
            chiusura_rilevazione_utile_perdita.descrizione = '%s - CHIUSURA RILEVAZIONE PERDITA/UTILE' % esercizio.descrizione_operazione_chiusura
            chiusura_rilevazione_utile_perdita.area = area
            chiusura_rilevazione_utile_perdita.esercizio = esercizio
            chiusura_rilevazione_utile_perdita.genere_movimento = 'chiusura'
            chiusura_rilevazione_utile_perdita.save()
            if saldo_profitti_perdite > Money(0.00, saldo_profitti_perdite.currency):
                saldo_netto = abs(saldo_profitti_perdite)
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_rilevazione_utile_perdita
                dettaglio_dare.piano_dei_conti = esercizio.conto_risultato_esercizio
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_netto
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_rilevazione_utile_perdita
                dettaglio_avere.piano_dei_conti = esercizio.conto_profitti_perdite
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_netto
                dettaglio_avere.save()
                return 'perdita'
            else:
                saldo_netto = abs(saldo_profitti_perdite)
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_rilevazione_utile_perdita
                dettaglio_dare.piano_dei_conti = esercizio.conto_profitti_perdite
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_netto
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_rilevazione_utile_perdita
                dettaglio_avere.piano_dei_conti = esercizio.conto_risultato_esercizio
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_netto
                dettaglio_avere.save()
                return 'utile'


def crea_movimento_chiusura_attivo(esercizio, area):
    chiusura_attivo = MovimentoPrimaNota()
    chiusura_attivo.numero_operazione = get_nuovo_numero(area, esercizio)
    chiusura_attivo.data = esercizio.data_operazione_chiusura
    chiusura_attivo.data_operazione = esercizio.data_operazione_chiusura
    chiusura_attivo.descrizione = '%s - CHIUSURA ATTIVO' % esercizio.descrizione_operazione_chiusura
    chiusura_attivo.area = area
    chiusura_attivo.esercizio = esercizio
    chiusura_attivo.genere_movimento = 'chiusura'
    chiusura_attivo.save()
    elenco_bilanci_attivo_base = BilancioConto.objects.filter(
        piano_dei_conti__tipologia__in=['liquidita', 'patrimoniale'],
        piano_dei_conti__livello='sottoconto', area=area, esercizio=esercizio
    )
    elenco_bilanci_attivo = get_queryset_bilancio_calcolato(elenco_bilanci_attivo_base, area, esercizio)
    for bilancio_attivo in elenco_bilanci_attivo:
        saldo = bilancio_attivo.get_saldo()
        if saldo:
            if saldo > Money(0.00, saldo.currency):
                saldo_netto = abs(saldo)
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_attivo
                dettaglio_dare.piano_dei_conti = esercizio.conto_stato_patrimoniale_finale
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_netto
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_attivo
                dettaglio_avere.piano_dei_conti = bilancio_attivo.piano_dei_conti
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_netto
                dettaglio_avere.save()
    return chiusura_attivo


def crea_movimento_chiusura_passivo(esercizio, area):
    chiusura_passivo = MovimentoPrimaNota()
    chiusura_passivo.numero_operazione = get_nuovo_numero(area, esercizio)
    chiusura_passivo.data = esercizio.data_operazione_chiusura
    chiusura_passivo.data_operazione = esercizio.data_operazione_chiusura
    chiusura_passivo.descrizione = '%s - CHIUSURA PASSIVO' % esercizio.descrizione_operazione_chiusura
    chiusura_passivo.area = area
    chiusura_passivo.esercizio = esercizio
    chiusura_passivo.genere_movimento = 'chiusura'
    chiusura_passivo.save()
    elenco_bilanci_passivo_base = BilancioConto.objects.filter(
        piano_dei_conti__tipologia__in=['liquidita', 'patrimoniale'],
        piano_dei_conti__livello='sottoconto', area=area, esercizio=esercizio
    )
    elenco_bilanci_passivo = get_queryset_bilancio_calcolato(elenco_bilanci_passivo_base, area, esercizio)
    for bilancio_passivo in elenco_bilanci_passivo:
        saldo = bilancio_passivo.get_saldo()
        if saldo:
            if saldo < Money(0.00, saldo.currency):
                saldo_netto = abs(saldo)
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_passivo
                dettaglio_dare.piano_dei_conti = bilancio_passivo.piano_dei_conti
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_netto
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_passivo
                dettaglio_avere.piano_dei_conti = esercizio.conto_stato_patrimoniale_finale
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_netto
                dettaglio_avere.save()
    # MOVIMENTO CHIUSURA CONTO RISULTATO ESERCIZIO
    if esercizio.conto_risultato_esercizio:
        conto_risultato_esercizio = BilancioConto.objects.filter(
            area=area, esercizio=esercizio, piano_dei_conti=esercizio.conto_risultato_esercizio
        )
        elenco_bilanci = get_queryset_bilancio_calcolato(conto_risultato_esercizio, area, esercizio)
        if elenco_bilanci:
            bilancio_risultato_esercizio = elenco_bilanci[0]
            saldo_risultato = abs(bilancio_risultato_esercizio.get_saldo())
            if saldo_risultato:
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = chiusura_passivo
                dettaglio_dare.piano_dei_conti = esercizio.conto_risultato_esercizio
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = saldo_risultato
                dettaglio_dare.save()
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = chiusura_passivo
                dettaglio_avere.piano_dei_conti = esercizio.conto_stato_patrimoniale_finale
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = saldo_risultato
                dettaglio_avere.save()
            return chiusura_passivo


def crea_movimento_apertura_attivo(esercizio, area, movimento_chiusura_attivo):
    apertura_attivo = MovimentoPrimaNota()
    apertura_attivo.numero_operazione = get_nuovo_numero(area, esercizio.esercizio_successivo)
    apertura_attivo.data = esercizio.data_operazione_apertura
    apertura_attivo.data_operazione = esercizio.data_operazione_apertura
    apertura_attivo.descrizione = '%s - APERTURA ATTIVO' % esercizio.descrizione_operazione_apertura
    apertura_attivo.area = area
    apertura_attivo.esercizio = esercizio.esercizio_successivo
    apertura_attivo.genere_movimento = 'apertura'
    apertura_attivo.save()
    elenco_dettagli_avere = movimento_chiusura_attivo.dettagliomovimentoprimanota_set.filter(categoria='avere')
    for dettaglio_chiusura_attivo in elenco_dettagli_avere:
        if not dettaglio_chiusura_attivo.piano_dei_conti == esercizio.conto_risultato_esercizio:
            dettaglio_avere = DettaglioMovimentoPrimaNota()
            dettaglio_avere.movimento_primanota = apertura_attivo
            dettaglio_avere.piano_dei_conti = esercizio.conto_stato_patrimoniale_iniziale
            dettaglio_avere.categoria = 'avere'
            dettaglio_avere.importo = dettaglio_chiusura_attivo.importo
            dettaglio_avere.save()
            dettaglio_dare = DettaglioMovimentoPrimaNota()
            dettaglio_dare.movimento_primanota = apertura_attivo
            dettaglio_dare.piano_dei_conti = dettaglio_chiusura_attivo.piano_dei_conti
            dettaglio_dare.categoria = 'dare'
            dettaglio_dare.importo = dettaglio_chiusura_attivo.importo
            dettaglio_dare.save()


def crea_movimento_apertura_passivo(esercizio, area, movimento_chiusura_passivo):
    if movimento_chiusura_passivo:
        apertura_passivo = MovimentoPrimaNota()
        apertura_passivo.numero_operazione = get_nuovo_numero(area, esercizio.esercizio_successivo)
        apertura_passivo.data = esercizio.data_operazione_apertura
        apertura_passivo.data_operazione = esercizio.data_operazione_apertura
        apertura_passivo.descrizione = '%s - APERTURA PASSIVO' % esercizio.descrizione_operazione_apertura
        apertura_passivo.area = area
        apertura_passivo.esercizio = esercizio.esercizio_successivo
        apertura_passivo.genere_movimento = 'apertura'
        apertura_passivo.save()
        elenco_dettagli_dare = movimento_chiusura_passivo.dettagliomovimentoprimanota_set.exclude(
            piano_dei_conti=esercizio.conto_stato_patrimoniale_finale
        )
        for dettaglio_chiusura_passivo in elenco_dettagli_dare:
            if dettaglio_chiusura_passivo.piano_dei_conti not in [esercizio.conto_stato_patrimoniale_finale, esercizio.conto_risultato_esercizio]:
                if dettaglio_chiusura_passivo.categoria == 'dare':
                    dettaglio_dare = DettaglioMovimentoPrimaNota()
                    dettaglio_dare.movimento_primanota = apertura_passivo
                    dettaglio_dare.piano_dei_conti = esercizio.conto_stato_patrimoniale_iniziale
                    dettaglio_dare.categoria = 'dare'
                    dettaglio_dare.importo = dettaglio_chiusura_passivo.importo
                    dettaglio_dare.save()
                    dettaglio_avere = DettaglioMovimentoPrimaNota()
                    dettaglio_avere.movimento_primanota = apertura_passivo
                    dettaglio_avere.piano_dei_conti = dettaglio_chiusura_passivo.piano_dei_conti
                    dettaglio_avere.categoria = 'avere'
                    dettaglio_avere.importo = dettaglio_chiusura_passivo.importo
                    dettaglio_avere.save()
                else:
                    dettaglio_dare = DettaglioMovimentoPrimaNota()
                    dettaglio_dare.movimento_primanota = apertura_passivo
                    dettaglio_dare.piano_dei_conti = dettaglio_chiusura_passivo.piano_dei_conti
                    dettaglio_dare.categoria = 'dare'
                    dettaglio_dare.importo = dettaglio_chiusura_passivo.importo
                    dettaglio_dare.save()
                    dettaglio_avere = DettaglioMovimentoPrimaNota()
                    dettaglio_avere.movimento_primanota = apertura_passivo
                    dettaglio_avere.piano_dei_conti = esercizio.conto_stato_patrimoniale_iniziale
                    dettaglio_avere.categoria = 'avere'
                    dettaglio_avere.importo = dettaglio_chiusura_passivo.importo
                    dettaglio_avere.save()


def crea_movimento_rilevazione_utile_perdita_precedente(esercizio, area, risultato_esercizio):
    if esercizio.conto_risultato_esercizio:
        conto_risultato_esercizio = BilancioConto.objects.filter(
            area=area, esercizio=esercizio, piano_dei_conti=esercizio.conto_risultato_esercizio
        )
        elenco_bilanci = get_queryset_bilancio_calcolato(conto_risultato_esercizio, area, esercizio)
        if elenco_bilanci:
            bilancio_utile_precedente = elenco_bilanci[0]
            apertura_rilevazione_utile_perdita = MovimentoPrimaNota()
            apertura_rilevazione_utile_perdita.numero_operazione = get_nuovo_numero(area, esercizio.esercizio_successivo)
            apertura_rilevazione_utile_perdita.data = esercizio.data_operazione_apertura
            apertura_rilevazione_utile_perdita.data_operazione = esercizio.data_operazione_apertura
            apertura_rilevazione_utile_perdita.descrizione = '%s - APERTURA RILEVAZIONE PERDITA/UTILE PRECEDENTE' % esercizio.descrizione_operazione_apertura
            apertura_rilevazione_utile_perdita.area = area
            apertura_rilevazione_utile_perdita.esercizio = esercizio.esercizio_successivo
            apertura_rilevazione_utile_perdita.genere_movimento = 'apertura'
            apertura_rilevazione_utile_perdita.save()
            utile_precedente = bilancio_utile_precedente.get_totale_dare()
            if risultato_esercizio == 'utile':
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = apertura_rilevazione_utile_perdita
                dettaglio_avere.piano_dei_conti = esercizio.conto_risultati_esercizi_precedenti
                dettaglio_avere.categoria = 'avere'
                dettaglio_avere.importo = utile_precedente
                dettaglio_avere.save()
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = apertura_rilevazione_utile_perdita
                dettaglio_dare.piano_dei_conti = esercizio.conto_stato_patrimoniale_iniziale
                dettaglio_dare.categoria = 'dare'
                dettaglio_dare.importo = utile_precedente
                dettaglio_dare.save()
            elif risultato_esercizio == 'perdita':
                dettaglio_avere = DettaglioMovimentoPrimaNota()
                dettaglio_avere.movimento_primanota = apertura_rilevazione_utile_perdita
                dettaglio_avere.piano_dei_conti = esercizio.conto_risultati_esercizi_precedenti
                dettaglio_avere.categoria = 'dare'
                dettaglio_avere.importo = utile_precedente
                dettaglio_avere.save()
                dettaglio_dare = DettaglioMovimentoPrimaNota()
                dettaglio_dare.movimento_primanota = apertura_rilevazione_utile_perdita
                dettaglio_dare.piano_dei_conti = esercizio.conto_stato_patrimoniale_iniziale
                dettaglio_dare.categoria = 'avere'
                dettaglio_dare.importo = utile_precedente
                dettaglio_dare.save()


def operazioni_chiusura_esercizio(esercizio, area):
    elenco_messaggi = []
    verifica_chiusura = esercizio.verifica_preparazione_chiusura(area)
    verifica_apertura = esercizio.verifica_preparazione_apertura(area)
    if verifica_chiusura and verifica_apertura:
        # CHIUSURA ESERCIZIO
        elenco_messaggi.append('CHIUSURA ESERCIZIO CORRENTE ...')
        crea_movimento_chiusura_costi(esercizio, area)
        elenco_messaggi.append('Creato movimento chiusura costi')
        crea_movimento_chiusura_ricavi(esercizio, area)
        elenco_messaggi.append('Creato movimento chiusura ricavi')
        risultato_esercizio = crea_movimento_rilevazione_utile_perdita(esercizio, area)
        elenco_messaggi.append('Creato movimento rilevazione utile precedente - Risultato: %s' % risultato_esercizio)
        movimento_chiusura_attivo = crea_movimento_chiusura_attivo(esercizio, area)
        elenco_messaggi.append('Creato movimento chiusura attivo')
        movimento_chiusura_passivo = crea_movimento_chiusura_passivo(esercizio, area)
        elenco_messaggi.append('Creato movimento chiusura passivo')
        # APERTURA ESERCIZIO
        elenco_messaggi.append('APERTURA NUOVO ESERCIZIO ...')
        crea_movimento_apertura_attivo(esercizio, area, movimento_chiusura_attivo)
        elenco_messaggi.append('Creato movimento apertura attivo')
        crea_movimento_apertura_passivo(esercizio, area, movimento_chiusura_passivo)
        elenco_messaggi.append('Creato movimento apertura passivo')
        crea_movimento_rilevazione_utile_perdita_precedente(esercizio, area, risultato_esercizio)
        elenco_messaggi.append('Creato movimento rilevazione utile o perdita esercizio precedente')
        # DISABILITATA CHIUSURA ESERCIZIO IN AUTOMATICO PER EVITARE DI BLOCCARE TUTTE LE AREE
        # esercizio.chiuso = True
        esercizio.save()
        return elenco_messaggi


def get_nuovo_codice_conto(parente, livello):
    nuovo_codice = ''
    if parente:
        if livello == 'mastro':
            elenco_mastri_parente = PianoDeiConti.objects.filter(livello='mastro', parent=parente).order_by('-codice_mastro')
            if elenco_mastri_parente:
                ultimo_mastro = elenco_mastri_parente[0].codice_mastro
                if not ultimo_mastro:
                    elenco_mastri_parente[0].save()
                    ultimo_mastro = elenco_mastri_parente[0].codice_mastro
                try:
                    nuovo_codice = int(ultimo_mastro) + 1
                except ValueError:
                    nuovo_codice = ultimo_mastro + '-1'
        if livello == 'conto':
            elenco_conti_parente = PianoDeiConti.objects.filter(livello='conto', parent=parente).order_by('-codice_conto')
            if elenco_conti_parente:
                ultimo_conto = elenco_conti_parente[0].codice_conto
                if not ultimo_conto:
                    elenco_conti_parente[0].save()
                    ultimo_conto = elenco_conti_parente[0].codice_conto
                try:
                    nuovo_codice = int(ultimo_conto) + 1
                except ValueError:
                    nuovo_codice = ultimo_conto + '-1'
        if livello == 'sottoconto':
            elenco_sottoconti_parente = PianoDeiConti.objects.filter(livello='sottoconto', parent=parente).order_by('-codice_sottoconto')
            if elenco_sottoconti_parente:
                ultimo_sottoconto = elenco_sottoconti_parente[0].codice_sottoconto
                if not ultimo_sottoconto:
                    elenco_sottoconti_parente[0].save()
                    ultimo_sottoconto = elenco_sottoconti_parente[0].codice_sottoconto
                try:
                    nuovo_codice = int(ultimo_sottoconto) + 1
                except ValueError:
                    nuovo_codice = ultimo_sottoconto + '-1'
    return nuovo_codice


def get_dati_nuovo_conto(id_parente):
    livello = ''
    codice_gruppo = ''
    codice_mastro = ''
    codice_conto = ''
    codice_sottoconto = ''
    tipologia = ''
    gestione = ''
    if id_parente:
        parente = PianoDeiConti.objects.get(pk=id_parente)
        parente.save()
        if parente:
            if parente.livello == 'gruppo':
                livello = 'mastro'
                codice_gruppo = parente.codice_gruppo
                codice_mastro = get_nuovo_codice_conto(parente, livello)
                codice_conto = '0'
                codice_sottoconto = '0'
            elif parente.livello == 'mastro':
                livello = 'conto'
                codice_gruppo = parente.codice_gruppo
                codice_mastro = parente.codice_mastro
                codice_conto = get_nuovo_codice_conto(parente, livello)
                codice_sottoconto = '0'
            elif parente.livello == 'conto':
                livello = 'sottoconto'
                codice_gruppo = parente.codice_gruppo
                codice_mastro = parente.codice_mastro
                codice_conto = parente.codice_conto
                codice_sottoconto = get_nuovo_codice_conto(parente, livello)
            tipologia = parente.tipologia
            gestione = parente.gestione
    else:
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 4:
            livello = 'gruppo'
        elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 3:
            livello = 'mastro'
        elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
            livello = 'conto'
    return dict(
        livello=livello,
        codice_gruppo=codice_gruppo,
        codice_mastro=codice_mastro,
        codice_conto=codice_conto,
        codice_sottoconto=codice_sottoconto,
        tipologia=tipologia,
        gestione=gestione,
    )


def esercizio_area_chiuso(esercizio, area):
    if esercizio:
        if esercizio.chiuso:
            return True
        else:
            if area:
                try:
                    chiusura_area = ChiusuraArea.objects.get(esercizio=esercizio, area=area)
                    if chiusura_area.chiuso:
                        return True
                except:
                    pass
    return False
