from django.utils.translation import ugettext_lazy as _
from django.db import models
from django.utils.html import format_html
from django.conf import settings
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.template.defaultfilters import slugify

from mptt.models import MPTTModel, TreeForeignKey
from preferences.models import Preferences
from preferences import preferences

from djmoney.models.fields import MoneyField, CurrencyField
from common.utils.currency import get_elenco_valute

from djmoney.contrib.exchange.models import ExchangeBackend
from djmoney.contrib.exchange.backends import OpenExchangeRatesBackend

from django.db.transaction import atomic
from djmoney import settings as djmoney_settings
from djmoney.contrib.exchange.models import Rate

NATURA_CONTO = (
    ('patrimoniale', _('PATRIMONIALE')),
    ('liquidita', _('LIQUIDITA\'')),
    ('economico', _('ECONOMICO')),
    ('costi', _('COSTO')),
    ('ricavi', _('RICAVO')),
    ('ordine', _('D\'ORDINE')),
)

GESTIONE_CONTO = (
    ('operativa', _('OPERATIVA')),
    ('finanziaria', _('FINANZIARIA')),
    ('patrimoniale', _('PATRIMONIALE')),
)

GESTIONE_ANAGRAFICA = (
    ('clienti', _('CLIENTI')),
    ('fornitori', _('FORNITORI')),
    ('banche', _('BANCHE')),
)

LIVELLO_PIANO = (
    ('gruppo', _('gruppo')),
    ('mastro', _('mastro')),
    ('conto', _('conto')),
    ('sottoconto', _('sottoconto')),
)

LIVELLO_PIANO_2 = (
    ('conto', _('conto')),
    ('sottoconto', _('sottoconto')),
)

TIPO_BILANCIO = (
    ('competenza', _('Di competenza')),
    ('cassa', _('Di cassa')),
)

LIVELLO_PIANO_3 = (
    ('mastro', _('mastro')),
    ('conto', _('conto')),
    ('sottoconto', _('sottoconto')),
)

IMPOSTAZIONE_CONTO = (
    ('cassa', _('Cassa')),
    ('banca', _('Banca')),
    ('fatture_da_emettere', _('Fatture Da Emettere')),
    ('fatture_emesse', _('Fatture Emesse')),
)

ELENCO_GRAFICI = (
    ('matthaeus_conto_economico', 'Matthaeus - Conto Economico'),
    ('matthaeus_riepilogo_costi', 'Matthaeus - Riepilogo Costi'),
    ('matthaeus_riepilogo_ricavi', 'Matthaeus - Riepilogo Ricavi'),
    ('matthaeus_budget', 'Matthaeus - Budget'),
    ('matthaeus_liquidita', 'Matthaeus - Liquidita'),
    ('matthaeus_mol', 'Matthaeus - Margine Operativo Lordo'),
    ('matthaeus_risultato_gestione_finanziaria', 'Matthaeus - risultato gestione finanziaria'),
    ('matthaeus_risultato_gestione_patrimoniale', 'Matthaeus - risultato gestione patrimoniale'),
    ('matthaeus_risultato_esercizio', 'Matthaeus - risultato esercizio'),
    ('matthaeus_risultatofinanziario_gestione_operativa', 'Matthaeus - risultato finanziario della gestione operativa'),
    ('matthaeus_risultatofinanziario_gestione_finanziaria', 'Matthaeus - risultato finanziario della gestione finanziaria'),
    ('matthaeus_risultatofinanziario_gestione_patrimoniale', 'Matthaeus - risultato finanziario della gestione patrimoniale'),
    ('matthaeus_risultatofinanziario_esercizio', 'Matthaeus - risultato finanziario esercizio'),
    ('publius_proiezionetotalereligiosi', 'Publius - Totale Religiosi (proiezione)'),
    ('publius_etareligiosi_oggi', 'Publius - Età Religiosi (oggi)'),
    ('publius_etareligiosi_5', 'Publius - Età Religiosi (tra 5 anni)'),
    ('publius_etareligiosi_10', 'Publius - Età Religiosi (tra 10 anni)'),
    ('publius_etareligiosi_15', 'Publius - Età Religiosi (tra 15 anni)'),
    ('publius_etareligiosi_20', 'Publius - Età Religiosi (tra 20 anni)'),
)

AREE_GEOPOLITICHE = (
    ('11', 'Unione europea'),
    ('12', 'Europa centro orientale'),
    ('13', 'Altri paesi europei'),
    ('21', 'Africa settentrionale'),
    ('22', 'Africa occidentale'),
    ('23', 'Africa orientale'),
    ('24', 'Africa centro meridionale'),
    ('31', 'Asia occidentale'),
    ('32', 'Asia centro meridionale'),
    ('33', 'Asia orientale'),
    ('41', 'America settentrionale'),
    ('42', 'America centro meridionale'),
    ('50', 'Oceania'),
    ('60', 'Apolidi'),
)

CONTINENTI = (
    ('1', 'Europa'),
    ('2', 'Africa'),
    ('3', 'Asia'),
    ('4', 'America'),
    ('5', 'Oceania'),
    ('6', 'Apolidi'),
)

ORDINE_CAMPI_STAMPA = (
    ('appellativo_cognome_nome', 'Appellativo, Cognome, Nome'),
    ('appellativo_nome_cognome', 'Appellativo, Nome, Cognome'),
    ('cognome_appellativo_nome', 'Cognome, Appellativo, Nome'),
    ('cognome_nome', 'Cognome, Nome'),
    ('nome_cognome', 'Nome, Cognome'),
)

PRIMA_COLONNA_STAMPA = (
    ('matricola', 'matricola'),
    ('progressivo', 'Numero Progressivo'),
    ('niente', 'Niente'),
    ('casella', 'Casella'),
)

ORDINAMENTO_RELIGIOSI_STAMPA = (
    ('matricola', 'Matricola'),
    ('cognome__nome', 'Cognome, Nome'),
    ('casa_attuale__matricola', 'Casa, Matricola'),
    ('casa_attuale__cognome__nome', 'Casa, Cognome, Nome'),
    ('casa_attuale__status_religioso__cognome__nome', 'Casa, Status Rel., Cognome, Nome'),
)

ELENCO_DASHBOARD = [
    ('matthaeus_grafici', 'Matthaeus - Dashboard Grafici'),
    ('publius_statistiche_demografiche', 'Publius - Statistiche Demografiche'),
    ('publius_homepage', 'Publius - Home page'),
    ('cyrenaeus_homepage', 'Cyrenaeus - Home page'),
]

ELENCO_APPLICATIVI = (
    ('matthaeus', 'Matthaeus'),
    ('publius', 'Publius'),
    ('saulo', 'Saulo'),
    ('martinus', 'Martinus'),
    ('cyrenaeus', 'Cyrenaeus'),
)


# SMALL = 1    # 25%  or  [x] + [x] + [x] + [x]
# MEDIUM = 2   # 33%  or  [ x ] + [ x ] + [ x ]
# LARGE = 3    # 50%  or  [   x   ] + [   x   ]
# LARGER = 4   # 66%  or  [     x     ] + [ x ]
# LARGEST = 5  # 75%  or  [      x      ] + [x]
# FULL = 6     # 100% or  [         x         ]

DIMENSIONI_WIDGET = [
    (1, _('piccolo')),
    (2, _('medio')),
    (3, _('grande')),
    (4, _('extra large')),
    (6, _('100%')),
]


class ImpostazioniMagister(Preferences):
    ordine_campi_stampa = models.CharField(
        _('Publius - ordine campi stampa'), max_length=200, choices=ORDINE_CAMPI_STAMPA,
        null=True, blank=True,
    )
    prima_colonna_stampa = models.CharField(
        _('Publius - prima colonna stampa'), max_length=200, choices=PRIMA_COLONNA_STAMPA,
        null=True, blank=True,
    )
    ordinamento_religiosi_stampa = models.CharField(
        _('Publius - ordinamento religiosi stampa'), max_length=200, choices=ORDINAMENTO_RELIGIOSI_STAMPA,
        null=True, blank=True,
    )
    appellativo_predefinito = models.CharField(
        _('Publius - appellativo predefinito'), max_length=200, null=True, blank=True,
    )
    sovrascrivi_residenza = models.BooleanField(
        _('Publius - sovrascrivi residenza'), default=True,
        help_text=_('Se selezionato, quando non sono presenti indirizzo e luogo di residenza inserisce i corrispondenti dati presi da quelli della casa attuale')
    )
    sovrascrivi_provincia_religiosa_da_casa = models.BooleanField(
        _('Publius - sovrascrivi provincia religiosa da casa'), default=True,
        help_text=_('Se selezionato, quando viene cambiata la casa attuale di un religioso, cambia la provincia religiosa di appartenenza con quella della casa')
    )
    creazione_automatica_trasferimento = models.BooleanField(
        _('Publius - creazione automatica trasferimento'), default=True,
        help_text=_('Se selezionato, quando viene modificata la casa attuale di un religioso crea contestualmente un trasferimento in data attuale')
    )
    numero_scadenze = models.PositiveIntegerField(_('numero massimo scadenze'), default=10)
    provincia_religiosa_default = models.ForeignKey(
        'persone.ProvinciaReligiosa', verbose_name=_('Publius - Provincia Religiosa Default'),
        blank=True, null=True, on_delete=models.SET_NULL
    )
    nascondi_provincia_religiosa_default = models.BooleanField(
        _('Publius - nascondi provincia religiosa default'), default=False,
    )
    numero_cifre_mastro = models.PositiveIntegerField(
        _('Matthaeus - numero di cifre standard per il numero dei mastri'), default=1,
    )
    numero_cifre_conto = models.PositiveIntegerField(
        _('Matthaeus - numero di cifre standard per il numero dei conti'), default=2,
    )
    numero_cifre_sottoconto = models.PositiveIntegerField(
        _('Matthaeus - numero di cifre standard per il numero dei sottoconti'), default=4
    )
    separatore_codice_conti = models.CharField(
        _('Matthaeus - separatore per il codice del piano dei conti'),
        default='.', max_length=5,
    )
    gestione_bilancio = models.CharField(
        _('Matthaeus - Tipo di gestione bilancio'), max_length=200, choices=TIPO_BILANCIO,
        default='competenza',
    )
    escludi_chiusure_bilancio = models.BooleanField(
        _('Matthaeus - Escludi chiusure dalla stampa bilancio'), default=False,
        help_text=_('Se selezionato, esclude i movimenti di chiusura dalla stampa del bilancio')
    )
    abilita_gestione_anagrafiche = models.BooleanField(
        _('Matthaeus - abilita gestione anagrafiche nei movimenti'), default=False,
        help_text=_('Se selezionato, rende possibile attribuire una anagrafica ai partitari per la gestione dei clienti/fornitori')
    )
    abilita_controllo_inserimento_anagrafiche = models.BooleanField(
        _('Matthaeus - abilita il controllo per le anagrafiche nei movimenti'), default=False,
        help_text=_('Se selezionato, aggiunge un controllo per verificare che l\'anagrafica sia stata inserita per i conti che la richiedono')
    )
    intestazione_stampe = models.CharField(
        _('Magister - Intestazione per stampe'), max_length=200, null=True, blank=True,
        help_text=_('inserire il nome della congregazione per utilizzarlo nelle testate delle stampe')
    )
    gestione_controvalore_partita_semplice = models.BooleanField(
        _('Matthaeus - Gestione Controvalore nella Partita Semplice'), default=False,
        help_text=_('abilita la visualizzazione e la modifica del controvalore negli importi nella partita semplice')
    )
    gestione_controvalore_partita_avanzata = models.BooleanField(
        _('Matthaeus - Gestione Controvalore nella Partita avanzata'), default=False,
        help_text=_('abilita la visualizzazione e la modifica del controvalore negli importi nella partita avanzata')
    )
    visualizza_saldo_fornitori = models.BooleanField(
        _('Matthaeus - Visualizza saldo fornitori'), default=False,
        help_text=_('abilita la visualizzazione del saldo iniziale dei fornitori nel partitario')
    )
    

    class Meta:
        verbose_name_plural = _('Impostazioni Magister')
        app_label = 'anagraficabase'

    def __str__(self):
        return 'impostazioni magister'


class Stato(models.Model):
    descrizione = models.CharField(_('descrizione'), max_length=200)
    descrizione_inglese = models.CharField(_('descrizione (inglese)'), max_length=200)
    area = models.CharField(_('area'), max_length=200, choices=AREE_GEOPOLITICHE)
    continente = models.CharField(_('continente'), max_length=200, choices=CONTINENTI)
    codice = models.CharField(_('codice'), max_length=3, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('Nazioni')
        verbose_name = _('Nazione')
        ordering = ('descrizione', 'descrizione_inglese')
        app_label = 'anagraficabase'

    def __str__(self):
        return '%s' % self.descrizione


def get_codice_provincia():
    nuovo_codice = ''
    codici_stati = Provincia.objects.filter(codice__startswith='EE_').order_by('-codice')
    if codici_stati:
        parti_codice = codici_stati[0].codice.split('_')
        sequenza_codice = int(parti_codice[1]) + 1
        sequenza_codice = '0000%s' % sequenza_codice
        nuovo_codice = 'EE_%s' % sequenza_codice[-5:]
    else:
        nuovo_codice = 'EE_00001'
    return nuovo_codice


class Provincia(models.Model):
    codice = models.CharField(_('codice'), max_length=50, primary_key=True, db_index=True, default=get_codice_provincia)
    descrizione = models.CharField(_('descrizione'), max_length=200)
    nazione = models.ForeignKey(
        Stato, default=1, verbose_name=_('nazione'),
        on_delete=models.SET_DEFAULT
    )

    class Meta:
        verbose_name_plural = _('Province')
        ordering = ('descrizione', 'nazione')
        app_label = 'anagraficabase'

    def __str__(self):
        # if self.codice_provincia:
        #     return '%s (%s)' % (self.descrizione, self.codice_provincia)
        # else:
        return '%s' % self.descrizione


class TipoArea(models.Model):
    nome = models.CharField(_('nome'), max_length=200, unique=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('Tipi Area')
        verbose_name = _('Tipo Area')
        ordering = ('nome', 'descrizione')
        app_label = 'anagraficabase'

    def __str__(self):
        return self.nome


class Area(MPTTModel):
    nome = models.CharField(max_length=200, verbose_name=_('nome'))
    descrizione = models.CharField(verbose_name=_('descrizione'), max_length=200, null=True, blank=True)
    codice = models.CharField(max_length=200, verbose_name=_('codice'), null=True, blank=True)
    tipo_area = models.ForeignKey(TipoArea, on_delete=models.CASCADE, verbose_name=_('tipo area'), null=True, blank=True)
    valuta_default = CurrencyField(
        verbose_name=_('valuta default'),
        default=settings.VALUTA_DEFAULT, choices=get_elenco_valute()
    )
    parent = TreeForeignKey(
        'self', on_delete=models.CASCADE, null=True, blank=True, related_name='children',
        verbose_name=_('padre'),
    )
    piano_dei_conti_personalizzato = models.BooleanField(
        verbose_name=_('Piano dei conti personalizzato'), default=False
    )
    anagrafica_personalizzata = models.BooleanField(
        verbose_name=_('Anagrafica personalizzata'), default=False
    )
    centri_di_costo_personalizzati = models.BooleanField(
        verbose_name=_('Centri di costo personalizzati'), default=False
    )

    class MPTTMeta:
        order_insertion_by = ['codice', 'nome', 'descrizione']

    class Meta:
        verbose_name_plural = _('Aree')
        verbose_name = _('Area')
        ordering = ('codice', 'nome', 'descrizione')
        app_label = 'anagraficabase'

    def __str__(self):
        if self.codice:
            return '%s - %s' % (self.codice, self.nome)
        else:
            return '%s' % self.nome

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._valuta_originale = self.valuta_default

    def clean(self):
        if self.valuta_default:
            if self._valuta_originale:
                if self.valuta_default != self._valuta_originale:
                    from matthaeus.movimenti.models import MovimentoPrimaNota
                    elenco_movimenti = MovimentoPrimaNota.objects.filter(area=self)
                    if elenco_movimenti:
                        stringa_messaggio = _('Attenzione! non è possibile modificare la valuta di default di un\'area che ha già movimenti inseriti!')
                        raise ValidationError(stringa_messaggio)

    # def save(self, *args, **kwargs):
    #     if self.valuta_default:
    #         if self._valuta_originale:
    #             if self.valuta_default != self._valuta_originale:
    #                 from matthaeus.movimenti.models import MovimentoPrimaNota
    #                 elenco_movimenti = MovimentoPrimaNota.objects.filter(area=self)
    #                 if elenco_movimenti:
    #                     stringa_messaggio = _('Attenzione! non è possibile modificare la valuta di default di un\'area che ha già


class AreaAwareModel(models.Model):
    area = models.ForeignKey(Area, on_delete=models.CASCADE, verbose_name=_('area'), null=True, blank=True)

    class Meta:
        abstract = True


class CentroDiCosto(models.Model):
    nome = models.CharField(_('nome'), max_length=200, unique=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('centri di costo')
        verbose_name = _('centro di costo')
        ordering = ('nome', 'descrizione')
        app_label = 'anagraficabase'

    def __str__(self):
        return self.nome


class AbilitazioneCentroDiCosto(models.Model):
    centro_di_costo = models.ForeignKey(
        CentroDiCosto, verbose_name=_('centro di costo'), on_delete=models.CASCADE,
    )
    area_abilitata = models.ForeignKey(
        Area, on_delete=models.CASCADE, verbose_name=_('area abilitata'), null=True, blank=True
    )

    class Meta:
        verbose_name_plural = _('abilitazioni centri di costo')
        verbose_name = _('abilitazione centro di costo')
        ordering = ('centro_di_costo', 'area_abilitata')
        unique_together = ('centro_di_costo', 'area_abilitata')
        app_label = 'anagraficabase'

    def __str__(self):
        return u'%s - %s' % (self.area_abilitata, self.centro_di_costo)


class PianoDeiConti(MPTTModel):
    codice = models.CharField(
        _('codice'), max_length=200, null=True, blank=True, unique=True
    )
    descrizione = models.CharField(_('descrizione'), max_length=200)
    tipologia = models.CharField(
        _('natura conto'), max_length=200, null=True, blank=True, choices=NATURA_CONTO
    )
    conto_imputabile = models.BooleanField(_('conto imputabile'), default=False)
    conto_multivaluta = models.BooleanField(_('conto multivaluta'), default=False)
    valuta_default = CurrencyField(
        verbose_name=_('valuta default'), choices=get_elenco_valute(),
        null=True, blank=True,
    )
    preventivo_dare = MoneyField(
        _('preventivo (dare)'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    preventivo_avere = MoneyField(
        _('preventivo (avere)'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    parent = TreeForeignKey(
        'self', on_delete=models.CASCADE, null=True, blank=True, related_name='children',
        verbose_name=_('padre'),
    )
    livello = models.CharField(
        _('livello'), max_length=200, null=True, blank=True, choices=LIVELLO_PIANO
    )
    impostazione_conto = models.CharField(
        _('impostazione conto'), max_length=200, null=True, blank=True, choices=IMPOSTAZIONE_CONTO
    )
    gestione = models.CharField(
        _('gestione'), max_length=200, null=True, blank=True, choices=GESTIONE_CONTO
    )
    codice_gruppo = models.CharField(
        _('gruppo'), max_length=200, null=True, blank=True,
    )
    codice_mastro = models.CharField(
        _('mastro'), max_length=200, null=True, blank=True,
    )
    codice_conto = models.CharField(
        _('conto'), max_length=200, null=True, blank=True,
    )
    codice_sottoconto = models.CharField(
        _('sottoconto'), max_length=200, null=True, blank=True,
    )
    gestione_parziali_area = models.BooleanField(_('gestione parziali per area'), default=False)
    gestione_anagrafica = models.CharField(
        _('gestione anagrafica'), max_length=200, null=True, blank=True, choices=GESTIONE_ANAGRAFICA
    )

    class MPTTMeta:
        order_insertion_by = ['codice', 'descrizione']

    class Meta:
        verbose_name_plural = _('Conti')
        verbose_name = _('Conto')
        ordering = ('codice', 'descrizione')
        app_label = 'anagraficabase'

    def __str__(self):
        return u'%s - %s' % (self.codice, self.descrizione)

    def get_codice_assoluto(self):
        codice_assoluto = ''
        elenco_antenati = self.get_ancestors(include_self=True)
        for antenato in elenco_antenati:
            if codice_assoluto:
                codice_assoluto += '_'
            codice_assoluto += antenato.codice
        return codice_assoluto

    def clean(self):
        if not self.codice:
            codice = self.get_codice_composto()
            if codice:
                if self.id:
                    piano_doppio = PianoDeiConti.objects.filter(codice=codice).exclude(id=self.id)
                else:
                    piano_doppio = PianoDeiConti.objects.filter(codice=codice)
                if piano_doppio:
                    stringa_messaggio = _(
                        'Attenzione! Esiste un altro piano dei conti con questo codice!'
                    )
                    raise ValidationError(stringa_messaggio)
        if self.livello:
            if self.parent:
                if self.parent.livello == self.livello:
                    stringa_messaggio = _(
                        'Attenzione! Il livello corrente del conto e\' lo stesso del parente da cui deriva!'
                    )
                    raise ValidationError(stringa_messaggio)

    def is_mastro(self):
        if self.livello == 'mastro':
            return True
        return False

    def is_conto(self):
        if self.livello == 'conto':
            return True
        return False

    def is_sottoconto(self):
        if self.livello == 'sottoconto':
            return True
        return False

    def get_imputabile_display(self):
        if self.conto_imputabile:
            return 'Si'
        return 'No'

    def get_impostazione_conto_display(self):
        if self.impostazione_conto:
            return '%s' % self.impostazione_conto
        return ''

    def is_cassa(self):
        if self.impostazione_conto == 'cassa':
            return True
        else:
            elenco_antenati = self.get_ancestors(include_self=True)
            for antenato in elenco_antenati:
                if antenato.impostazione_conto == 'cassa':
                    return True
        return False

    def is_banca(self):
        if self.impostazione_conto == 'banca':
            return True
        else:
            elenco_antenati = self.get_ancestors(include_self=True)
            for antenato in elenco_antenati:
                if antenato.impostazione_conto == 'banca':
                    return True
        return False

    def get_codice_composto(self):
        codice_composto = ''
        separatore_codice = preferences.ImpostazioniMagister.separatore_codice_conti
        numero_cifre_mastro = preferences.ImpostazioniMagister.numero_cifre_mastro
        numero_cifre_conto = preferences.ImpostazioniMagister.numero_cifre_conto
        numero_cifre_sottoconto = preferences.ImpostazioniMagister.numero_cifre_sottoconto
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI > 3:
            # GRUPPO
            if self.codice_gruppo:
                codice_composto += str(self.codice_gruppo) + separatore_codice
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI > 2:
            # MASTRO
            if self.codice_mastro:
                codice_composto += str(self.codice_mastro).zfill(numero_cifre_mastro) + separatore_codice
            else:
                codice_composto += ''.zfill(numero_cifre_mastro) + separatore_codice
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI > 1:
            # CONTO
            if self.codice_conto:
                codice_composto += str(self.codice_conto).zfill(numero_cifre_conto) + separatore_codice
            else:
                codice_composto += ''.zfill(numero_cifre_conto) + separatore_codice
        # SOTTOCONTO
        if self.codice_sottoconto:
            codice_composto += str(self.codice_sottoconto).zfill(numero_cifre_sottoconto)
        else:
            codice_composto += ''.zfill(numero_cifre_sottoconto)
        return codice_composto

    def save(self, *args, **kwargs):
        if not self.livello:
            if self.parent:
                if self.parent.livello == 'conto':
                    self.livello = 'sottoconto'
                elif self.parent.livello == 'mastro':
                    self.livello = 'conto'
                elif self.parent.livello == 'gruppo':
                    self.livello = 'mastro'
            else:
                if self.is_root_node():
                    if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 4:
                        self.livello = 'gruppo'
                    elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 3:
                        self.livello = 'mastro'
                    elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
                        self.livello = 'conto'
        if self.codice:
            parti_codice = self.codice.split(preferences.ImpostazioniMagister.separatore_codice_conti)
            if not self.codice_sottoconto:
                if parti_codice[-1]:
                    try:
                        self.codice_sottoconto = int(parti_codice[-1])
                    except ValueError:
                        self.codice_sottoconto = parti_codice[-1]
            if len(parti_codice) > 1:
                if not self.codice_conto:
                    if parti_codice[-2]:
                        try:
                            self.codice_conto = int(parti_codice[-2])
                        except ValueError:
                            self.codice_conto = parti_codice[-2]
            if len(parti_codice) > 2:
                if not self.codice_mastro:
                    if parti_codice[-3]:
                        try:
                            self.codice_mastro = int(parti_codice[-3])
                        except ValueError:
                            self.codice_mastro = parti_codice[-3]
            if len(parti_codice) == 4:
                if not self.codice_gruppo:
                    if parti_codice[0]:
                        try:
                            self.codice_gruppo = int(parti_codice[0])
                        except ValueError:
                            self.codice_gruppo = parti_codice[0]
        self.codice = self.get_codice_composto()
        return super(PianoDeiConti, self).save(*args, **kwargs)


class AbilitazionePianoDeiConti(models.Model):
    piano_dei_conti = models.ForeignKey(
        PianoDeiConti, verbose_name=_('conto'), on_delete=models.CASCADE,
    )
    area_abilitata = models.ForeignKey(
        Area, on_delete=models.CASCADE, verbose_name=_('area abilitata'), null=True, blank=True
    )

    class Meta:
        verbose_name_plural = _('abilitazioni piani dei conti')
        verbose_name = _('abilitazione piano dei conti')
        ordering = ('piano_dei_conti', 'area_abilitata')
        unique_together = ('piano_dei_conti', 'area_abilitata')
        app_label = 'anagraficabase'

    def __str__(self):
        return u'%s - %s' % (self.area_abilitata, self.piano_dei_conti)


def verifica_conto_abilitato(conto, area):
    if area.piano_dei_conti_personalizzato:
        try:
            AbilitazionePianoDeiConti.objects.get(piano_dei_conti=conto, area_abilitata=area)
        except AbilitazionePianoDeiConti.DoesNotExist:
            return False
    return True


class Esercizio(models.Model):
    nome = models.CharField(_('nome'), max_length=200, unique=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, null=True, blank=True)
    data_inizio = models.DateField(_('data inizio'), null=True, blank=True)
    data_fine = models.DateField(_('data fine'), null=True, blank=True)
    chiuso = models.BooleanField(_('chiuso'), default=False)
    data_operazione_chiusura = models.DateField(_('data operazione chiusura'), null=True, blank=True)
    data_operazione_apertura = models.DateField(_('data operazione apertura'), null=True, blank=True)
    descrizione_operazione_chiusura = models.CharField(_('descrizione operazione chiusura'), max_length=200, null=True, blank=True)
    descrizione_operazione_apertura = models.CharField(_('descrizione operazione apertura'), max_length=200, null=True, blank=True)
    esercizio_successivo = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('esercizio successivo')
    )
    conto_profitti_perdite = models.ForeignKey(
        PianoDeiConti, related_name='conto_profitti_perdite_pk',
        on_delete=models.CASCADE, verbose_name=_('conto profitti e perdite'),
        null=True, blank=True
    )
    conto_risultato_esercizio = models.ForeignKey(
        PianoDeiConti, related_name='conto_risultato_esercizio_pk',
        on_delete=models.CASCADE, verbose_name=_('conto risultato d\'esercizio'),
        null=True, blank=True
    )
    conto_stato_patrimoniale_finale = models.ForeignKey(
        PianoDeiConti, related_name='conto_stato_patrimoniale_finale_pk',
        on_delete=models.CASCADE, verbose_name=_('conto stato patrimoniale finale'),
        null=True, blank=True
    )
    conto_stato_patrimoniale_iniziale = models.ForeignKey(
        PianoDeiConti, related_name='conto_stato_patrimoniale_iniziale_pk',
        on_delete=models.CASCADE, verbose_name=_('conto stato patrimoniale iniziale'),
        null=True, blank=True
    )
    conto_risultati_esercizi_precedenti = models.ForeignKey(
        PianoDeiConti, related_name='conto_risultati_esercizi_precedenti_pk',
        on_delete=models.CASCADE, verbose_name=_('conto risultati esercizi precendenti'),
        null=True, blank=True
    )

    class Meta:
        verbose_name_plural = _('Esercizi')
        verbose_name = _('Esercizio')
        ordering = ('nome', 'descrizione')
        app_label = 'anagraficabase'

    def __str__(self):
        if self.chiuso:
            return '%s (%s)' % (self.nome, _('CHIUSO'))
        else:
            return '%s' % self.nome

    def get_link_esercizio_default(self, site=None):
        if not site:
            site = 'matthaeus'
        url = reverse('%s:anagraficabase_esercizio_changelist' % site)
        return format_html('<a href="%s%s/seteserciziodefault">%s</a>' % (url, self.id, _('Imposta Default')))
    get_link_esercizio_default.short_description = _('Default')
    get_link_esercizio_default.allow_tags = True

    def verifica_preparazione_chiusura(self, area):
        # profitti e perdite
        if not self.conto_profitti_perdite:
            raise ValidationError('Attenzione! Manca il conto profitti e perdite!')
        if not verifica_conto_abilitato(self.conto_profitti_perdite, area):
            raise ValidationError('Attenzione! Il conto profitti e perdite non e\' abilitato per l\'area corrente!')
        # risultato esercizio
        if not self.conto_risultato_esercizio:
            raise ValidationError('Attenzione! Manca il conto risultato esercizio!')
        if not verifica_conto_abilitato(self.conto_risultato_esercizio, area):
            raise ValidationError('Attenzione! Il conto risultato esercizio non e\' abilitato per l\'area corrente!')
        # stato patrimoniale finale
        if not self.conto_stato_patrimoniale_finale:
            raise ValidationError('Attenzione! Manca il conto stato patrimoniale finale!')
        if not verifica_conto_abilitato(self.conto_stato_patrimoniale_finale, area):
            raise ValidationError('Attenzione! Il conto stato patrimoniale finale non e\' abilitato per l\'area corrente!')
        if not self.data_operazione_chiusura:
            raise ValidationError('Attenzione! Manca la data dell\'operazione di chiusura!')
        if not self.descrizione_operazione_chiusura:
            raise ValidationError('Attenzione! Manca la descrizione dell\'operazione di chiusura!')
        return True

    def verifica_preparazione_apertura(self, area):
        if not self.esercizio_successivo:
            raise ValidationError('Attenzione! Manca l\'esercizio successivo!')
        # risultati esercizi precendenti
        if not self.conto_risultati_esercizi_precedenti:
            raise ValidationError('Attenzione! Manca il conto risultati esercizi precendenti!')
        if not verifica_conto_abilitato(self.conto_risultati_esercizi_precedenti, area):
            raise ValidationError('Attenzione! Il conto risultati esercizi precendenti non e\' abilitato per l\'area corrente!')
        # stato patrimoniale iniziale
        if not self.conto_stato_patrimoniale_iniziale:
            raise ValidationError('Attenzione! Manca il conto stato patrimoniale iniziale!')
        if not verifica_conto_abilitato(self.conto_stato_patrimoniale_iniziale, area):
            raise ValidationError('Attenzione! Il conto stato patrimoniale iniziale non e\' abilitato per l\'area corrente!')
        if not self.data_operazione_apertura:
            raise ValidationError('Attenzione! Manca la data dell\'operazione di apertura!')
        if not self.descrizione_operazione_apertura:
            raise ValidationError('Attenzione! Manca la descrizione dell\'operazione di apertura!')
        return True


class EsercizioAwareModel(models.Model):
    esercizio = models.ForeignKey(Esercizio, on_delete=models.CASCADE, verbose_name=_('esercizio'), null=True, blank=True)

    class Meta:
        abstract = True


class ChiusuraArea(models.Model):
    esercizio = models.ForeignKey(Esercizio, on_delete=models.CASCADE, verbose_name=_('esercizio'))
    area = models.ForeignKey(Area, on_delete=models.CASCADE, verbose_name=_('area'))
    chiuso = models.BooleanField(_('chiuso'), default=False)
    data_chiusura = models.DateField(_('data chiusura'), null=True, blank=True)

    class Meta:
        verbose_name_plural = _('Chiusure Aree')
        verbose_name = _('Chiusura Area')
        ordering = ('esercizio', 'area')
        app_label = 'anagraficabase'
        unique_together = ('esercizio', 'area')
    
    def __str__(self):
        if self.chiuso:
            return '%s - %s (%s)' % (self.esercizio, self.area, _('CHIUSA'))
        else:
            return '%s - %s' % (self.esercizio, self.area)
    
    def save(self, *args, **kwargs):
        if self.chiuso:
            if not self.data_chiusura:
                self.data_chiusura = timezone.now().date()
        else:
            self.data_chiusura = None
        return super(ChiusuraArea, self).save(*args, **kwargs)


class Cambio(models.Model):
    data_inizio = models.DateField(_('data inizio'))
    data_fine = models.DateField(_('data fine'), null=True, blank=True)
    valuta_origine = CurrencyField(
        verbose_name=_('valuta origine'),
        default=settings.VALUTA_DEFAULT, choices=get_elenco_valute()
    )
    valuta_destinazione = CurrencyField(
        verbose_name=_('valuta destinazione'),
        default=settings.VALUTA_DEFAULT, choices=get_elenco_valute()
    )
    valore_destinazione = models.DecimalField(decimal_places=6, max_digits=9, verbose_name=_('valore destinazione'))

    class Meta:
        verbose_name_plural = _('Cambio Valuta')
        verbose_name = _('Cambi Valuta')
        ordering = ('-data_inizio', )
        app_label = 'anagraficabase'

    def __str__(self):
        traduzione_cambio = _('Cambio')
        traduzione_al = _('al')
        stringa_cambio = '%s %s-%s %s %s' % (traduzione_cambio, self.valuta_origine, self.valuta_destinazione, traduzione_al, self.data_inizio)
        return stringa_cambio


class PianoDeiContiImputabileManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiImputabileManager, self).get_queryset()
        return qs.filter(conto_imputabile=True)


class PianoDeiContiImputabile(PianoDeiConti):
    objects = PianoDeiContiImputabileManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti')
        verbose_name = _('Conto')


class PianoDeiContiCostiManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiCostiManager, self).get_queryset()
        return qs.filter(tipologia='costi', conto_imputabile=True)


class PianoDeiContiCosti(PianoDeiConti):
    objects = PianoDeiContiCostiManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti - COSTI')
        verbose_name = _('Conto - COSTO')


class PianoDeiContiRicaviManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiRicaviManager, self).get_queryset()
        return qs.filter(tipologia='ricavi', conto_imputabile=True)


class PianoDeiContiRicavi(PianoDeiConti):
    objects = PianoDeiContiRicaviManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti - RICAVI')
        verbose_name = _('Conto - RICAVO')


class PianoDeiContiLiquiditaManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiLiquiditaManager, self).get_queryset()
        return qs.filter(tipologia='liquidita', conto_imputabile=True)


class PianoDeiContiLiquidita(PianoDeiConti):
    objects = PianoDeiContiLiquiditaManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti - LIQUIDITA\'')
        verbose_name = _('Conto - LIQUIDITA\'')


class PianoDeiContiPatrimonialeManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiPatrimonialeManager, self).get_queryset()
        return qs.filter(tipologia='patrimoniale', conto_imputabile=True)


class PianoDeiContiPatrimoniale(PianoDeiConti):
    objects = PianoDeiContiPatrimonialeManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti - PATRIMONIALI')
        verbose_name = _('Conto - PATRIMONIALE')


class PianoDeiContiAttivoManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoDeiContiAttivoManager, self).get_queryset()
        return qs.filter(tipologia__in=['patrimoniale', 'liquidita'], conto_imputabile=True)


class PianoDeiContiAttivo(PianoDeiConti):
    objects = PianoDeiContiAttivoManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti - ATTIVO')
        verbose_name = _('Conto - ATTIVO')


class ContoFornitoriManager(models.Manager):

    def get_queryset(self):
        qs = super(ContoFornitoriManager, self).get_queryset()
        return qs.filter(gestione_anagrafica='fornitori', conto_imputabile=True)


class ContoFornitori(PianoDeiConti):
    objects = ContoFornitoriManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti Fornitori')
        verbose_name = _('Conto Fornitori')    


class ContoClientiManager(models.Manager):

    def get_queryset(self):
        qs = super(ContoClientiManager, self).get_queryset()
        return qs.filter(gestione_anagrafica='clienti', conto_imputabile=True)


class ContoClienti(PianoDeiConti):
    objects = ContoClientiManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti Clienti')
        verbose_name = _('Conto Clienti')   


class ContoClientiFornitoriManager(models.Manager):

    def get_queryset(self):
        qs = super(ContoClientiFornitoriManager, self).get_queryset()
        return qs.filter(gestione_anagrafica__in=['clienti', 'fornitori'], conto_imputabile=True)
    

class ContoClientiFornitori(PianoDeiConti):
    objects = ContoClientiFornitoriManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Conti Clienti e Fornitori')
        verbose_name = _('Conto Clienti e Fornitori')


class StatisticheGrafici(AreaAwareModel, EsercizioAwareModel):
    grafico = models.CharField(max_length=200, choices=ELENCO_GRAFICI)
    html_grafico = models.TextField(null=True, blank=True)
    data_ultimo_aggiornamento = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name_plural = _('Statistiche Grafici')
        verbose_name = _('Statistica Grafico')
        ordering = ('area', 'esercizio', 'grafico', '-data_ultimo_aggiornamento')
        app_label = 'anagraficabase'

    def __str__(self):
        return 'statistiche %s del %s' % (self.grafico, self.data_ultimo_aggiornamento)


class FraseMotivazionale(models.Model):
    autore = models.CharField(_('autore'), max_length=200, null=True, blank=True)
    testo = models.TextField(_('testo'))
    giorno_calendario = models.PositiveIntegerField(
        _('giorno calendario'), null=True, blank=True, 
        validators=[
            MaxValueValidator(31),
            MinValueValidator(1)
        ]
    )
    mese_calendario = models.PositiveIntegerField(
        _('mese calendario'), null=True, blank=True,
        validators=[
            MaxValueValidator(12),
            MinValueValidator(1)
        ]
    )

    class Meta:
        verbose_name_plural = _('Frasi Motivazionali')
        verbose_name = _('Frase Motivazionale')
        app_label = 'anagraficabase'

    def __str__(self):
        if self.autore:
            return 'frase motivazionale %s n.%s' % (self.autore, self.id)
        else:
            return 'frase motivazionale n. %s' % self.id


class MagisterDashboard(models.Model):
    nome = models.CharField(_('nome'), max_length=200)
    descrizione = models.TextField(_('descrizione'), null=True, blank=True)
    applicazione = models.CharField(_('applicazione'), max_length=200, choices=ELENCO_APPLICATIVI)
    nome_dashboard = models.CharField(_('nome dashboard'), max_length=200, choices=ELENCO_DASHBOARD, unique=True)

    class Meta:
        verbose_name_plural = _('Magister Dashboards')
        verbose_name = _('Magister Dashboard')
        app_label = 'anagraficabase'
        ordering = ('applicazione', 'nome')

    def __str__(self):
        return  '%s - %s' % (self.applicazione, self.nome)


class ConfigurazioneWidget(models.Model):
    nome = models.CharField(_('nome'), max_length=200)
    descrizione = models.TextField(_('descrizione'), null=True, blank=True)
    nome_widget_1 = models.CharField(_('nome widget 1'), max_length=200)
    nome_widget_2 = models.CharField(_('nome widget 2'), max_length=200, null=True, blank=True)
    nome_widget_3 = models.CharField(_('nome widget 3'), max_length=200, null=True, blank=True)
    nome_widget_4 = models.CharField(_('nome widget 4'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('Configurazioni Widget')
        verbose_name = _('Configurazione Widget')
        app_label = 'anagraficabase'
        ordering = ('nome', )

    def __str__(self):
        return self.nome


class ConfigurazioneDashboard(models.Model):
    magister_dashboard = models.ForeignKey(MagisterDashboard, on_delete=models.CASCADE, verbose_name=_('dashboard'))
    ordinamento = models.PositiveIntegerField(_('ordinamento'), default=1)
    attivo = models.BooleanField(_('attivo'), default=True)
    widget = models.ForeignKey(ConfigurazioneWidget, on_delete=models.CASCADE, verbose_name=_('widget'))
    dimensione = models.PositiveIntegerField(_('dimensione'), choices=DIMENSIONI_WIDGET, default=1)

    class Meta:
        verbose_name_plural = _('Configurazioni Dashboard')
        verbose_name = _('Configurazione Dashboard')
        app_label = 'anagraficabase'
        ordering = ('magister_dashboard', 'ordinamento', 'attivo')

    def __str__(self):
        return '%s - %s (%s)' % (self.magister_dashboard, self.widget, self.dimensione)


class SantiDelGiorno(models.Model):
    santi = models.TextField(_('santi'))
    giorno = models.PositiveIntegerField(
        _('giorno'),
        validators=[
            MaxValueValidator(31),
            MinValueValidator(1)
        ]
    )
    mese = models.PositiveIntegerField(
        _('mese'),
        validators=[
            MaxValueValidator(12),
            MinValueValidator(1)
        ]
    )

    class Meta:
        verbose_name_plural = _('Santi del giorno')
        verbose_name = _('Santi del giorno')
        app_label = 'anagraficabase'
        unique_together = ('giorno', 'mese')
        ordering = ('mese', 'giorno')

    def __str__(self):
        return 'santi del %s/%s' % (self.giorno, self.mese)


class ApplicazioneMagister(ContentType):

    class Meta:
        proxy = True
        verbose_name_plural = _('Applicazioni Magister')
        verbose_name = _('Applicazione Magister')
        app_label = 'anagraficabase'
        ordering = ('app_label', 'model')

    def __str__(self):
        return '%s - %s' % (self.app_label, self.model)


class CambioStorico(ExchangeBackend):
    data_inizio = models.DateField(
        verbose_name=_('Data Inizio Validità\'')
    )
    data_fine = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Data Fine Validita\'')
    )

    class Meta:
        verbose_name = _('Cambio Storico')
        verbose_name_plural = _('Cambi Storici')

    def __str__(self):
        return slugify(self.name)


class MagisterOpenExchangeRatesBackend(OpenExchangeRatesBackend):
    pass

    @atomic
    def update_rates(self, base_currency=djmoney_settings.BASE_CURRENCY, **kwargs):
        """
        Updates rates for the given backend.
        """
        data_corrente = timezone.now().date()
        elenco_cambi = CambioStorico.objects.all().order_by('-last_update')
        if elenco_cambi:
            elenco_cambi[0].data_fine = data_corrente
            elenco_cambi[0].save()
        nome = 'Cambio al %s (%s)' % (data_corrente.strftime('%d/%m/%Y'), timezone.now().strftime('%H:%M:%S'))
        backend = CambioStorico.objects.create(
            name=nome, base_currency=base_currency, data_inizio=data_corrente
        )
        params = self.get_params()
        params.update(base_currency=base_currency, **kwargs)
        Rate.objects.bulk_create(
            [
                Rate(currency=currency, value=value, backend=backend)
                for currency, value in self.get_rates(**params).items()
            ]
        )


class LogImportazione(models.Model):
    data_inizio = models.DateTimeField(_('data inizio'), auto_now_add=True)
    data_fine = models.DateTimeField(_('data fine'), null=True, blank=True)
    stato_importazione = models.CharField(_('stato_importazione'), max_length=200)
    applicativo = models.CharField(_('applicazione'), max_length=200, choices=ELENCO_APPLICATIVI)
    tipo_importazione = models.CharField(_('tipo importazione'), max_length=200)
    numero_record = models.PositiveIntegerField(_('numero record'), null=True, blank=True)
    numero_record_importati = models.PositiveIntegerField(_('numero record importati'), null=True, blank=True)
    note = models.TextField(_('note'), null=True, blank=True)
    errori = models.TextField(_('errori'), null=True, blank=True)
    file_importazione = models.FileField(
        _('file importazione'), upload_to='importazioni', null=True, blank=True
    )

    class Meta:
        verbose_name_plural = _('Log Importazioni')
        verbose_name = _('Log Importazione')
        app_label = 'anagraficabase'
        ordering = ('-data_inizio', )

    def __str__(self):
        return '%s - %s (%s)' % (self.applicativo, self.tipo_importazione, self.data_inizio)

    def aggiungi_messaggi(self, messaggi):
        if messaggi:
            for messaggio in messaggi:
                if messaggio:
                    if self.note:
                        self.note += messaggio + '\n'
                    else:
                        self.note = messaggio + '\n'
            self.save()

    def aggiungi_errore(self, errore):
        if errore:
            if self.errori:
                self.errori += errore + '\n'
            else:
                self.errori = errore + '\n'
            self.data_fine = timezone.now()
            self.stato_importazione = 'concluso con errori'
            self.save()
