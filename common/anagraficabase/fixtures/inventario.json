[{"model": "inventario.veicolo", "pk": 1, "fields": {"casa": 1, "descrizione": "FIAT PANDA", "data_acquisto": "2021-03-26", "data_alienazione": null, "targa": "DC234GT", "libretto": null, "intestatario": "pippo", "firma": null, "cavalli_fiscali": 0, "numero_telaio": null, "compagnia_assicurazione": null, "numero_polizza": null, "massimale_currency": "EUR", "massimale": null, "data_revisione": null, "scadenza_revisione": null, "scadenza_bollo": null, "scadenza_assicurazione": null, "immatricolazione": null, "fotografia": "Panda-MY21-Family-Page-Gallery-img01-Desktop-680x400.jpg", "note": ""}}, {"model": "inventario.tipobeneculturale", "pk": 1, "fields": {"nome": "Icone", "descrizione": "Icone"}}, {"model": "inventario.tipobeneculturale", "pk": 2, "fields": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "inventario.tipobeneculturale", "pk": 3, "fields": {"nome": "Sculture", "descrizione": "Sculture"}}, {"model": "inventario.tipobeneculturale", "pk": 4, "fields": {"nome": "Altari", "descrizione": "Altari"}}, {"model": "inventario.tipobeneculturale", "pk": 5, "fields": {"nome": "Architettura", "descrizione": "Architettura"}}, {"model": "inventario.beneculturale", "pk": 1, "fields": {"casa": 1, "nome": "foto simo", "descrizione": "foto simo", "tipologia": 1, "segnatura": "foto simo", "collocazione": "foto simo", "provenienza": "foto simo", "ente_proprietario": null, "fotografia": "download.jpeg", "data_stima": "2021-03-26", "valore_currency": "EUR", "valore": "100.00", "perito": null}}, {"model": "inventario.tipoattrezzatura", "pk": 1, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "inventario.tipoattrezzatura", "pk": 2, "fields": {"nome": "<PERSON><PERSON>e di cucina", "descrizione": "<PERSON><PERSON>e di cucina"}}, {"model": "inventario.tipoattrezzatura", "pk": 3, "fields": {"nome": "<PERSON><PERSON>e lavanderia", "descrizione": "<PERSON><PERSON>e lavanderia"}}, {"model": "inventario.tipoattrezzatura", "pk": 4, "fields": {"nome": "<PERSON><PERSON><PERSON> ufficio", "descrizione": "<PERSON><PERSON><PERSON> ufficio"}}, {"model": "inventario.tipoattrezzatura", "pk": 5, "fields": {"nome": "Elettromedicali", "descrizione": "Elettromedicali"}}, {"model": "inventario.tipoattrezzatura", "pk": 6, "fields": {"nome": "Ausili per disabili", "descrizione": "Ausili per disabili"}}, {"model": "inventario.tipoattrezzatura", "pk": 7, "fields": {"nome": "Tendagg<PERSON>", "descrizione": "Tendagg<PERSON>"}}, {"model": "inventario.tipoattrezzatura", "pk": 8, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "inventario.tipoattrezzatura", "pk": 9, "fields": {"nome": "Armadio", "descrizione": "Armadio"}}]