[{"model": "anagraficabase.configurazionewidget", "pk": 1, "fields": {"nome": "Età <PERSON>ligio<PERSON> (Oggi)", "descrizione": "", "nome_widget_1": "EtaReligiosiOggiWidget", "nome_widget_2": "ProiezioniStatisticheReligiosiOggiWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 2, "fields": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "descrizione": "", "nome_widget_1": "PercentualeDecessiWidget", "nome_widget_2": "ProiezioniStatisticheDecessiWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 3, "fields": {"nome": "Proiezione Statistiche a 5 anni", "descrizione": "", "nome_widget_1": "EtaReligiosi5AnniWidget", "nome_widget_2": "ProiezioniStatisticheReligiosi5AnniWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 4, "fields": {"nome": "Proiezione Statistiche a 10 anni", "descrizione": "", "nome_widget_1": "EtaReligiosi10AnniWidget", "nome_widget_2": "ProiezioniStatisticheReligiosi10AnniWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 5, "fields": {"nome": "Proiezione Statistiche a 15 anni", "descrizione": "", "nome_widget_1": "EtaReligiosi15AnniWidget", "nome_widget_2": "ProiezioniStatisticheReligiosi15AnniWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 6, "fields": {"nome": "Proiezione Statistiche a 20 anni", "descrizione": "", "nome_widget_1": "EtaReligiosi20AnniWidget", "nome_widget_2": "ProiezioniStatisticheReligiosi20AnniWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 7, "fields": {"nome": "Proiezione Totale Religiosi", "descrizione": "", "nome_widget_1": "ProiezioneTotaleReligiosiWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 8, "fields": {"nome": "Compleanni Religiosi di Oggi", "descrizione": "", "nome_widget_1": "CompleanniReligiosiOggiWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 9, "fields": {"nome": "Rico<PERSON><PERSON><PERSON>", "descrizione": "", "nome_widget_1": "RicorrenzeDefuntiOggiWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 10, "fields": {"nome": "Anniversari di Oggi", "descrizione": "", "nome_widget_1": "AnniversariOggiWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 11, "fields": {"nome": "Santi Del Giorno", "descrizione": "", "nome_widget_1": "SantiDelGiornoWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 12, "fields": {"nome": "Frase del giorno", "descrizione": "", "nome_widget_1": "FraseDelGiornoWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 13, "fields": {"nome": "Grafico Religiosi Per Provincia Religiosa", "descrizione": "", "nome_widget_1": "GraficoReligiosiPerProvinciaReligiosaWidget", "nome_widget_2": "TabellaReligiosiPerProvinciaReligiosaWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 14, "fields": {"nome": "Grafico Religiosi Per Status Religioso", "descrizione": "", "nome_widget_1": "GraficoReligiosiPerStatusReligiosoWidget", "nome_widget_2": "TabellaReligiosiPerStatusReligiosoWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 15, "fields": {"nome": "Grafico Religiosi Per Casa", "descrizione": "", "nome_widget_1": "GraficoReligiosiPerCasaWidget", "nome_widget_2": "TabellaReligiosiPerCasaWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 16, "fields": {"nome": "Grafico Religiosi Per Status", "descrizione": "", "nome_widget_1": "GraficoReligiosiPerStatusWidget", "nome_widget_2": "TabellaReligiosiPerStatusWidget", "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 17, "fields": {"nome": "Scadenze Prossime List", "descrizione": "ScadenzeProssimeList", "nome_widget_1": "ScadenzeProssimeList", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 18, "fields": {"nome": "Scadenze Passate List", "descrizione": "ScadenzePassateList", "nome_widget_1": "ScadenzePassateList", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 19, "fields": {"nome": "tipo scadenza passata", "descrizione": "", "nome_widget_1": "get_tipo_scadenza_passata_widgets", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 20, "fields": {"nome": "tipo scadenza prossima", "descrizione": "", "nome_widget_1": "get_tipo_scadenza_prossima_widgets", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}, {"model": "anagraficabase.configurazionewidget", "pk": 21, "fields": {"nome": "Stati<PERSON><PERSON>", "descrizione": "", "nome_widget_1": "StatistichePubliusWidget", "nome_widget_2": null, "nome_widget_3": null, "nome_widget_4": null}}]