[{"model": "movimenti.tipomovimento", "pk": 1, "fields": {"nome": "tipo1", "descrizione": null}}, {"model": "movimenti.tipomovimento", "pk": 2, "fields": {"nome": "tipo2", "descrizione": null}}, {"model": "movimenti.causalecontabile", "pk": 1, "fields": {"area": null, "descrizione_sintetica": "acquisti CIBO MENSA", "descrizione_registrazione": "acquisti CIBO MENSA", "operazione_partita_semplice": "uscita"}}, {"model": "movimenti.causalecontabile", "pk": 2, "fields": {"area": null, "descrizione_sintetica": "pagamento bollette elettriche", "descrizione_registrazione": "pagamento bollette elettriche", "operazione_partita_semplice": "uscita"}}, {"model": "movimenti.causalecontabile", "pk": 3, "fields": {"area": null, "descrizione_sintetica": "pagamento TASSE", "descrizione_registrazione": "pagamento TASSE", "operazione_partita_semplice": "uscita"}}, {"model": "movimenti.causalecontabile", "pk": 4, "fields": {"area": null, "descrizione_sintetica": "versamento Rette scolastiche", "descrizione_registrazione": "tasse", "operazione_partita_semplice": "entrata"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 1, "fields": {"causale_contabile": 1, "progressivo": 1, "piano_dei_conti": 19, "categoria": "dare", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 2, "fields": {"causale_contabile": 1, "progressivo": 2, "piano_dei_conti": 2, "categoria": "avere", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 3, "fields": {"causale_contabile": 2, "progressivo": 1, "piano_dei_conti": 6, "categoria": "dare", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 4, "fields": {"causale_contabile": 2, "progressivo": 2, "piano_dei_conti": 8, "categoria": "avere", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 5, "fields": {"causale_contabile": 3, "progressivo": 1, "piano_dei_conti": 16, "categoria": "dare", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 6, "fields": {"causale_contabile": 3, "progressivo": 2, "piano_dei_conti": 2, "categoria": "avere", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 8, "fields": {"causale_contabile": 4, "progressivo": 1, "piano_dei_conti": 22, "categoria": "dare", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausalecontabile", "pk": 9, "fields": {"causale_contabile": 4, "progressivo": 2, "piano_dei_conti": 2, "categoria": "avere", "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausaleanalitica", "pk": 1, "fields": {"causale_contabile": 1, "progressivo": 1, "centro_di_costo": 8, "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausaleanalitica", "pk": 2, "fields": {"causale_contabile": 2, "progressivo": 1, "centro_di_costo": 1, "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausaleanalitica", "pk": 3, "fields": {"causale_contabile": 3, "progressivo": 1, "centro_di_costo": 6, "percentuale": "100.00"}}, {"model": "movimenti.dettagliocausaleanalitica", "pk": 4, "fields": {"causale_contabile": 4, "progressivo": 1, "centro_di_costo": 7, "percentuale": "100.00"}}, {"model": "movimenti.movimentoprimanota", "pk": 25, "fields": {"area": 6, "esercizio": 3, "data": "2020-10-14", "descrizione": "acquisto cibo mensa", "tipo_movimento": null, "numero_operazione": 2, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "1455.00", "causale": 1}}, {"model": "movimenti.movimentoprimanota", "pk": 26, "fields": {"area": 6, "esercizio": 3, "data": "2020-11-20", "descrizione": "pagamento tasse Rossi 2019/20", "tipo_movimento": null, "numero_operazione": 3, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "590.45", "causale": 3}}, {"model": "movimenti.movimentoprimanota", "pk": 27, "fields": {"area": 6, "esercizio": 3, "data": "2020-12-01", "descrizione": "bolletta luce", "tipo_movimento": null, "numero_operazione": 4, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "690.31", "causale": 2}}, {"model": "movimenti.movimentoprimanota", "pk": 28, "fields": {"area": 6, "esercizio": 3, "data": "2020-12-17", "descrizione": "sponsorizzazione magliette", "tipo_movimento": null, "numero_operazione": 5, "operazione_partita_semplice": null, "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 29, "fields": {"area": 6, "esercizio": 3, "data": "2021-01-27", "descrizione": "bolletta luce", "tipo_movimento": null, "numero_operazione": 6, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "45.00", "causale": 2}}, {"model": "movimenti.movimentoprimanota", "pk": 30, "fields": {"area": 6, "esercizio": 3, "data": "2021-01-27", "descrizione": "pagamento bollette", "tipo_movimento": null, "numero_operazione": 7, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "55.00", "causale": 2}}, {"model": "movimenti.movimentoprimanota", "pk": 31, "fields": {"area": 6, "esercizio": 3, "data": "2021-02-02", "descrizione": "Rice<PERSON>ta offerta anonima", "tipo_movimento": null, "numero_operazione": 8, "operazione_partita_semplice": "entrata", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "1000.00", "causale": 4}}, {"model": "movimenti.movimentoprimanota", "pk": 32, "fields": {"area": 6, "esercizio": 3, "data": "2021-02-02", "descrizione": "Versamento in banca", "tipo_movimento": null, "numero_operazione": 9, "operazione_partita_semplice": null, "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "800.00", "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 33, "fields": {"area": 6, "esercizio": 3, "data": "2021-02-03", "descrizione": "prova", "tipo_movimento": null, "numero_operazione": 10, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "50.00", "causale": 1}}, {"model": "movimenti.movimentoprimanota", "pk": 34, "fields": {"area": 6, "esercizio": 3, "data": "2021-02-04", "descrizione": "Acquistato generi alimentari", "tipo_movimento": null, "numero_operazione": 11, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": "25.00", "causale": 1}}, {"model": "movimenti.movimentoprimanota", "pk": 35, "fields": {"area": 6, "esercizio": 3, "data": "2021-03-02", "descrizione": "Retta 2° trim. sig. Rossi", "tipo_movimento": null, "numero_operazione": 12, "operazione_partita_semplice": "entrata", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 36, "fields": {"area": 6, "esercizio": 3, "data": "2021-03-02", "descrizione": "spesa succhi di frutta", "tipo_movimento": null, "numero_operazione": 13, "operazione_partita_semplice": "uscita", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 37, "fields": {"area": 6, "esercizio": 3, "data": "2021-03-02", "descrizione": "versamento", "tipo_movimento": null, "numero_operazione": 14, "operazione_partita_semplice": "giroconto", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 38, "fields": {"area": 8, "esercizio": 3, "data": "2021-03-10", "descrizione": "Saldo A.S. famiglia Bianchi", "tipo_movimento": null, "numero_operazione": 15, "operazione_partita_semplice": "entrata", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.movimentoprimanota", "pk": 39, "fields": {"area": 8, "esercizio": 3, "data": "2021-03-10", "descrizione": "<PERSON><PERSON> famiglia <PERSON>", "tipo_movimento": null, "numero_operazione": 16, "operazione_partita_semplice": "entrata", "importo_partita_semplice_currency": "EUR", "importo_partita_semplice": null, "causale": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 56, "fields": {"movimento_primanota": 25, "progressivo": null, "piano_dei_conti": 19, "categoria": "dare", "importo_currency": "EUR", "importo": "1455.00", "controvalore_currency": "EUR", "controvalore": "1455.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 57, "fields": {"movimento_primanota": 25, "progressivo": null, "piano_dei_conti": 2, "categoria": "avere", "importo_currency": "EUR", "importo": "1455.00", "controvalore_currency": "EUR", "controvalore": "1455.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 58, "fields": {"movimento_primanota": 26, "progressivo": null, "piano_dei_conti": 16, "categoria": "dare", "importo_currency": "EUR", "importo": "590.45", "controvalore_currency": "EUR", "controvalore": "590.45", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 59, "fields": {"movimento_primanota": 26, "progressivo": null, "piano_dei_conti": 2, "categoria": "avere", "importo_currency": "EUR", "importo": "600.00", "controvalore_currency": "EUR", "controvalore": "498.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 60, "fields": {"movimento_primanota": 27, "progressivo": null, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "690.31", "controvalore_currency": "EUR", "controvalore": "690.31", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 61, "fields": {"movimento_primanota": 27, "progressivo": null, "piano_dei_conti": 8, "categoria": "avere", "importo_currency": "EUR", "importo": "690.31", "controvalore_currency": "EUR", "controvalore": "690.31", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 62, "fields": {"movimento_primanota": 28, "progressivo": null, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 63, "fields": {"movimento_primanota": 28, "progressivo": null, "piano_dei_conti": 25, "categoria": "dare", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 64, "fields": {"movimento_primanota": 29, "progressivo": null, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "45.00", "controvalore_currency": "EUR", "controvalore": "45.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 65, "fields": {"movimento_primanota": 29, "progressivo": null, "piano_dei_conti": 8, "categoria": "avere", "importo_currency": "EUR", "importo": "45.00", "controvalore_currency": "EUR", "controvalore": "45.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 66, "fields": {"movimento_primanota": 30, "progressivo": null, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "55.00", "controvalore_currency": "EUR", "controvalore": "55.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 67, "fields": {"movimento_primanota": 30, "progressivo": null, "piano_dei_conti": 8, "categoria": "avere", "importo_currency": "EUR", "importo": "55.00", "controvalore_currency": "EUR", "controvalore": "55.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 68, "fields": {"movimento_primanota": 31, "progressivo": null, "piano_dei_conti": 22, "categoria": "dare", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 69, "fields": {"movimento_primanota": 31, "progressivo": null, "piano_dei_conti": 2, "categoria": "avere", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 70, "fields": {"movimento_primanota": 32, "progressivo": null, "piano_dei_conti": 2, "categoria": "dare", "importo_currency": "EUR", "importo": "500.00", "controvalore_currency": "EUR", "controvalore": "500.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 71, "fields": {"movimento_primanota": 32, "progressivo": null, "piano_dei_conti": 34, "categoria": "dare", "importo_currency": "EUR", "importo": "300.00", "controvalore_currency": "EUR", "controvalore": "300.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 72, "fields": {"movimento_primanota": 32, "progressivo": null, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "800.00", "controvalore_currency": "EUR", "controvalore": "800.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 73, "fields": {"movimento_primanota": 33, "progressivo": null, "piano_dei_conti": 19, "categoria": "dare", "importo_currency": "EUR", "importo": "50.00", "controvalore_currency": "EUR", "controvalore": "50.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 74, "fields": {"movimento_primanota": 33, "progressivo": 2, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "50.00", "controvalore_currency": "EUR", "controvalore": "50.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 75, "fields": {"movimento_primanota": 34, "progressivo": null, "piano_dei_conti": 19, "categoria": "dare", "importo_currency": "EUR", "importo": "25.00", "controvalore_currency": "EUR", "controvalore": "25.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 76, "fields": {"movimento_primanota": 34, "progressivo": 2, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "25.00", "controvalore_currency": "EUR", "controvalore": "25.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 77, "fields": {"movimento_primanota": 35, "progressivo": 1, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 78, "fields": {"movimento_primanota": 35, "progressivo": 2, "piano_dei_conti": 28, "categoria": "avere", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 79, "fields": {"movimento_primanota": 36, "progressivo": 1, "piano_dei_conti": 19, "categoria": "dare", "importo_currency": "EUR", "importo": "12.00", "controvalore_currency": "EUR", "controvalore": "12.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 80, "fields": {"movimento_primanota": 36, "progressivo": 2, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "12.00", "controvalore_currency": "EUR", "controvalore": "12.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 81, "fields": {"movimento_primanota": 37, "progressivo": 1, "piano_dei_conti": 2, "categoria": "dare", "importo_currency": "EUR", "importo": "400.00", "controvalore_currency": "EUR", "controvalore": "400.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 82, "fields": {"movimento_primanota": 37, "progressivo": 2, "piano_dei_conti": 6, "categoria": "avere", "importo_currency": "EUR", "importo": "400.00", "controvalore_currency": "EUR", "controvalore": "400.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 83, "fields": {"movimento_primanota": 38, "progressivo": 1, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 84, "fields": {"movimento_primanota": 38, "progressivo": 2, "piano_dei_conti": 29, "categoria": "avere", "importo_currency": "EUR", "importo": "1000.00", "controvalore_currency": "EUR", "controvalore": "1000.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 85, "fields": {"movimento_primanota": 39, "progressivo": 1, "piano_dei_conti": 6, "categoria": "dare", "importo_currency": "EUR", "importo": "1200.00", "controvalore_currency": "EUR", "controvalore": "1200.00", "descrizione": null}}, {"model": "movimenti.dettagliomovimentoprimanota", "pk": 86, "fields": {"movimento_primanota": 39, "progressivo": 2, "piano_dei_conti": 31, "categoria": "avere", "importo_currency": "EUR", "importo": "1200.00", "controvalore_currency": "EUR", "controvalore": "1200.00", "descrizione": null}}]