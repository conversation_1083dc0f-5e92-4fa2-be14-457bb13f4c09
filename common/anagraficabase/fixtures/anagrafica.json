[{"model": "anagraficabase.tipoarea", "pk": 1, "fields": {"nome": "Congregazione", "descrizione": "Congregazione"}}, {"model": "anagraficabase.tipoarea", "pk": 2, "fields": {"nome": "Istituti Superiori", "descrizione": "Istituti Superiori"}}, {"model": "anagraficabase.area", "pk": 1, "fields": {"nome": "Generalato Salesiani Italiani", "descrizione": "Generalato Salesiani Italiani", "tipo_area": 1, "valuta_default": "EUR", "parent": null, "lft": 1, "rght": 20, "tree_id": 1, "level": 0}}, {"model": "anagraficabase.area", "pk": 2, "fields": {"nome": "Toscana", "descrizione": "Toscana", "tipo_area": null, "valuta_default": "EUR", "parent": 1, "lft": 16, "rght": 17, "tree_id": 1, "level": 1}}, {"model": "anagraficabase.area", "pk": 3, "fields": {"nome": "Congregazioni Emilia Romagna", "descrizione": "Congregazioni Emilia Romagna", "tipo_area": 1, "valuta_default": "EUR", "parent": 1, "lft": 2, "rght": 9, "tree_id": 1, "level": 1}}, {"model": "anagraficabase.area", "pk": 4, "fields": {"nome": "Veneto", "descrizione": "Veneto", "tipo_area": null, "valuta_default": "EUR", "parent": 1, "lft": 18, "rght": 19, "tree_id": 1, "level": 1}}, {"model": "anagraficabase.area", "pk": 5, "fields": {"nome": "Generalato Salesiani Asia", "descrizione": "Generalato Salesiani Asia", "tipo_area": null, "valuta_default": "EUR", "parent": null, "lft": 1, "rght": 2, "tree_id": 2, "level": 0}}, {"model": "anagraficabase.area", "pk": 6, "fields": {"nome": "Reggio Emilia", "descrizione": "Reggio Emilia", "tipo_area": null, "valuta_default": "EUR", "parent": 3, "lft": 7, "rght": 8, "tree_id": 1, "level": 2}}, {"model": "anagraficabase.area", "pk": 7, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>", "tipo_area": 2, "valuta_default": "EUR", "parent": 9, "lft": 13, "rght": 14, "tree_id": 1, "level": 2}}, {"model": "anagraficabase.area", "pk": 8, "fields": {"nome": "Piacenza", "descrizione": "Piacenza", "tipo_area": 1, "valuta_default": "EUR", "parent": 3, "lft": 5, "rght": 6, "tree_id": 1, "level": 2}}, {"model": "anagraficabase.area", "pk": 9, "fields": {"nome": "Scuole Emilia Romagna", "descrizione": "Scuole Emilia Romagna", "tipo_area": 2, "valuta_default": "EUR", "parent": 1, "lft": 10, "rght": 15, "tree_id": 1, "level": 1}}, {"model": "anagraficabase.area", "pk": 10, "fields": {"nome": "Scuole Area Bologna", "descrizione": "Scuole Area Bologna", "tipo_area": 2, "valuta_default": "EUR", "parent": 9, "lft": 11, "rght": 12, "tree_id": 1, "level": 2}}, {"model": "anagraficabase.area", "pk": 11, "fields": {"nome": "Bologna", "descrizione": "Bologna", "tipo_area": 2, "valuta_default": "EUR", "parent": 3, "lft": 3, "rght": 4, "tree_id": 1, "level": 2}}, {"model": "anagraficabase.esercizio", "pk": 1, "fields": {"nome": "Esercizio fiscale 2018", "descrizione": "Esercizio fiscale 2018", "data_inizio": "2018-01-01", "data_fine": "2018-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 2, "fields": {"nome": "Esercizio fiscale 2019", "descrizione": "Esercizio fiscale 2019", "data_inizio": "2019-01-01", "data_fine": "2019-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 3, "fields": {"nome": "Esercizio fiscale 2020", "descrizione": "Esercizio fiscale 2020", "data_inizio": "2020-01-01", "data_fine": "2020-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 4, "fields": {"nome": "Anno <PERSON>. 2019/2020", "descrizione": "Anno <PERSON>. 2019/2020", "data_inizio": "2019-09-15", "data_fine": "2020-07-30"}}, {"model": "anagraficabase.esercizio", "pk": 5, "fields": {"nome": "Esercizio fiscale 2021", "descrizione": "Esercizio fiscale 2021", "data_inizio": "2021-01-01", "data_fine": "2021-12-31"}}, {"model": "anagraficabase.centrodicosto", "pk": 1, "fields": {"nome": "Spese cancelleria", "descrizione": "Spese cancelleria"}}, {"model": "anagraficabase.centrodicosto", "pk": 2, "fields": {"nome": "affitti", "descrizione": "affitti"}}, {"model": "anagraficabase.centrodicosto", "pk": 3, "fields": {"nome": "canoni mensili", "descrizione": "canoni mensili"}}, {"model": "anagraficabase.centrodicosto", "pk": 4, "fields": {"nome": "spese pulizie", "descrizione": "spese pulizie"}}, {"model": "anagraficabase.centrodicosto", "pk": 5, "fields": {"nome": "stipendi", "descrizione": "stipendi"}}, {"model": "anagraficabase.centrodicosto", "pk": 6, "fields": {"nome": "tasse", "descrizione": "tasse"}}, {"model": "anagraficabase.centrodicosto", "pk": 7, "fields": {"nome": "rette", "descrizione": "rette"}}, {"model": "anagraficabase.centrodicosto", "pk": 8, "fields": {"nome": "spese MENSA", "descrizione": "spese MENSA"}}, {"model": "anagraficabase.pianodeiconti", "pk": 1, "fields": {"codice": "01.00.0000", "descrizione": "LIQUIDITA'", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": true, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 16, "tree_id": 2, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 2, "fields": {"codice": "01.02.0001", "descrizione": "conto BPM", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": "banca", "lft": 9, "rght": 10, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 4, "fields": {"codice": "55.03.0000", "descrizione": "Spese vitto/utenze/vestiario comunità", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": "0.00", "preventivo_avere_currency": "EUR", "preventivo_avere": "0.00", "parent": 18, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 11, "tree_id": 8, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 6, "fields": {"codice": "01.01.0001", "descrizione": "<PERSON><PERSON>", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 13, "livello": "sottoconto", "impostazione_conto": "cassa", "lft": 3, "rght": 4, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 7, "fields": {"codice": "55.03.0003", "descrizione": "Spese telefoniche", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "lft": 7, "rght": 8, "tree_id": 8, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 8, "fields": {"codice": "55.03.0002", "descrizione": "Spese elettricità", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "lft": 5, "rght": 6, "tree_id": 8, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 9, "fields": {"codice": "55.03.0010", "descrizione": "Spese postali", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": "1569.00", "preventivo_avere_currency": "EUR", "preventivo_avere": "345.00", "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "lft": 9, "rght": 10, "tree_id": 8, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 10, "fields": {"codice": "75.00.0000", "descrizione": "RICAVI SCUOLA E COLLEGATI", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 14, "tree_id": 10, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 11, "fields": {"codice": "75.01.0000", "descrizione": "<PERSON><PERSON>", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 10, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 13, "tree_id": 10, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 12, "fields": {"codice": "01.02.0000", "descrizione": "BANCHE", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 1, "livello": "conto", "impostazione_conto": null, "lft": 8, "rght": 15, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 13, "fields": {"codice": "01.01.0000", "descrizione": "CASSE", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 1, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 7, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 14, "fields": {"codice": "26.00.0000", "descrizione": "DEBITI DIVERSI", "tipologia": "patrimoniale", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 6, "tree_id": 6, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 15, "fields": {"codice": "26.01.0000", "descrizione": "DEBITI v/erario", "tipologia": "patrimoniale", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 14, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 5, "tree_id": 6, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 16, "fields": {"codice": "26.01.0007", "descrizione": "<PERSON>rio c/rite<PERSON>e", "tipologia": "patrimoniale", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 15, "livello": "sottoconto", "impostazione_conto": null, "lft": 3, "rght": 4, "tree_id": 6, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 17, "fields": {"codice": "56.00.0000", "descrizione": "SPESE STRAORDINARIE", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 6, "tree_id": 9, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 18, "fields": {"codice": "55.00.0000", "descrizione": "SPESE COMUNITA'", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 12, "tree_id": 8, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 19, "fields": {"codice": "55.03.0001", "descrizione": "Alimentari", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "lft": 3, "rght": 4, "tree_id": 8, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 20, "fields": {"codice": "56.02.0000", "descrizione": "Consulenze professionisti", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 17, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 5, "tree_id": 9, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 21, "fields": {"codice": "56.02.0012", "descrizione": "<PERSON><PERSON><PERSON>", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 20, "livello": "sottoconto", "impostazione_conto": null, "lft": 3, "rght": 4, "tree_id": 9, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 22, "fields": {"codice": "75.01.0005", "descrizione": "Rette Liceo Scientifico", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "lft": 11, "rght": 12, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 24, "fields": {"codice": "80.01.0000", "descrizione": "Contributi e Sponsorizzazioni da Privati", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 32, "livello": "conto", "impostazione_conto": null, "lft": 2, "rght": 7, "tree_id": 11, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 25, "fields": {"codice": "80.01.0001", "descrizione": "Beneficienze", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 24, "livello": "sottoconto", "impostazione_conto": null, "lft": 3, "rght": 4, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 26, "fields": {"codice": "01.01.0002", "descrizione": "<PERSON><PERSON>", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": true, "valuta_default": "USD", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 13, "livello": "sottoconto", "impostazione_conto": null, "lft": 5, "rght": 6, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 27, "fields": {"codice": "01.02.0002", "descrizione": "I.O.R.", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": null, "lft": 11, "rght": 12, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 28, "fields": {"codice": "75.01.0001", "descrizione": "<PERSON><PERSON> scuola infanzia", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "lft": 3, "rght": 4, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 29, "fields": {"codice": "75.01.0002", "descrizione": "<PERSON><PERSON> scuola primaria", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "lft": 5, "rght": 6, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 30, "fields": {"codice": "75.01.0003", "descrizione": "Rette scuola secondaria di 1° grado", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "lft": 7, "rght": 8, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 31, "fields": {"codice": "75.01.0004", "descrizione": "<PERSON>tte liceo classico", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "lft": 9, "rght": 10, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 32, "fields": {"codice": "80.00.0000", "descrizione": "RICAVI NON FISCALI", "tipologia": "rica<PERSON>", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "lft": 1, "rght": 8, "tree_id": 11, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 33, "fields": {"codice": "80.01.0002", "descrizione": "Eredità", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 24, "livello": "sottoconto", "impostazione_conto": null, "lft": 5, "rght": 6, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 34, "fields": {"codice": "01.02.0003", "descrizione": "Banca Intesa", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": null, "lft": 13, "rght": 14, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.cambio", "pk": 1, "fields": {"data_inizio": "2020-07-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "USD", "valore_destinazione": "1.132412"}}, {"model": "anagraficabase.cambio", "pk": 2, "fields": {"data_inizio": "2019-01-01", "data_fine": null, "valuta_origine": "USD", "valuta_destinazione": "EUR", "valore_destinazione": "0.830000"}}]