[{"model": "anagraficabase.stato", "pk": 1, "fields": {"descrizione": "Italia", "descrizione_inglese": "Italy", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 201, "fields": {"descrizione": "Albania", "descrizione_inglese": "Albania", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 202, "fields": {"descrizione": "Andorra", "descrizione_inglese": "Andorra", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 203, "fields": {"descrizione": "Austria", "descrizione_inglese": "Austria", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 206, "fields": {"descrizione": "Belgio", "descrizione_inglese": "Belgium", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 209, "fields": {"descrizione": "Bulgaria", "descrizione_inglese": "Bulgaria", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 212, "fields": {"descrizione": "Danimarca", "descrizione_inglese": "Denmark", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 214, "fields": {"descrizione": "Finlandia", "descrizione_inglese": "Finland", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 215, "fields": {"descrizione": "Francia", "descrizione_inglese": "France", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 216, "fields": {"descrizione": "Germania", "descrizione_inglese": "Germany", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 219, "fields": {"descrizione": "Regno Unito", "descrizione_inglese": "United Kingdom", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 220, "fields": {"descrizione": "Grecia", "descrizione_inglese": "Greece", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 221, "fields": {"descrizione": "Irlanda", "descrizione_inglese": "Ireland", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 223, "fields": {"descrizione": "Islanda", "descrizione_inglese": "Iceland", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 225, "fields": {"descrizione": "Liechtenstein", "descrizione_inglese": "Liechtenstein", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 226, "fields": {"descrizione": "Lussemburgo", "descrizione_inglese": "Luxembourg", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 227, "fields": {"descrizione": "Malta", "descrizione_inglese": "Malta", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 229, "fields": {"descrizione": "Monaco", "descrizione_inglese": "Monaco", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 231, "fields": {"descrizione": "Norvegia", "descrizione_inglese": "Norway", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 232, "fields": {"descrizione": "Olanda <PERSON> <PERSON><PERSON>", "descrizione_inglese": "Netherlands", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 233, "fields": {"descrizione": "Polonia", "descrizione_inglese": "Poland", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 234, "fields": {"descrizione": "Portogallo", "descrizione_inglese": "Portugal", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 235, "fields": {"descrizione": "Romania", "descrizione_inglese": "Romania", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 236, "fields": {"descrizione": "San Marino", "descrizione_inglese": "San Marino", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 239, "fields": {"descrizione": "Spagna", "descrizione_inglese": "Spain", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 240, "fields": {"descrizione": "Svezia", "descrizione_inglese": "Sweden", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 241, "fields": {"descrizione": "Svizzera", "descrizione_inglese": "Switzerland", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 243, "fields": {"descrizione": "Ucraina", "descrizione_inglese": "Ukraine", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 244, "fields": {"descrizione": "Ungheria", "descrizione_inglese": "Hungary", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 245, "fields": {"descrizione": "Russa, Federazione", "descrizione_inglese": "Russian Federation", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 246, "fields": {"descrizione": "Stato della Città del Vaticano", "descrizione_inglese": "Vatican City State", "area": "13", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 247, "fields": {"descrizione": "Estonia", "descrizione_inglese": "Estonia", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 248, "fields": {"descrizione": "<PERSON><PERSON><PERSON>", "descrizione_inglese": "Latvia", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 249, "fields": {"descrizione": "Lituania", "descrizione_inglese": "Lithuania", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 250, "fields": {"descrizione": "Croazia", "descrizione_inglese": "Croatia", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 251, "fields": {"descrizione": "Slovenia", "descrizione_inglese": "Slovenia", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 252, "fields": {"descrizione": "Bosnia-Erzegovina", "descrizione_inglese": "Bosnia and Herzegovina", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 253, "fields": {"descrizione": "Macedonia, Repubblica di", "descrizione_inglese": "Macedonia (FYROM)", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 254, "fields": {"descrizione": "Moldova", "descrizione_inglese": "Moldova, Republic of", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 255, "fields": {"descrizione": "Slovacchia", "descrizione_inglese": "Slovakia", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 256, "fields": {"descrizione": "Bielorussia", "descrizione_inglese": "Belarus", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 257, "fields": {"descrizione": "Ceca, Repubblica", "descrizione_inglese": "Czech Republic", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 270, "fields": {"descrizione": "Montenegro", "descrizione_inglese": "Montenegro", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 271, "fields": {"descrizione": "Serbia, Repubblica di", "descrizione_inglese": "Serbia", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 272, "fields": {"descrizione": "Kosovo", "descrizione_inglese": "Kosovo", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 301, "fields": {"descrizione": "Afghanistan", "descrizione_inglese": "Afghanistan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 302, "fields": {"descrizione": "Arabia Saudita", "descrizione_inglese": "Saudi Arabia", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 304, "fields": {"descrizione": "<PERSON><PERSON><PERSON>", "descrizione_inglese": "Bahrain", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 305, "fields": {"descrizione": "Bangladesh", "descrizione_inglese": "Bangladesh", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 306, "fields": {"descrizione": "Bhutan", "descrizione_inglese": "Bhutan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 307, "fields": {"descrizione": "Myanmar (ex Birmania)", "descrizione_inglese": "Myanmar", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 309, "fields": {"descrizione": "Brunei", "descrizione_inglese": "Brunei Darussalam", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 310, "fields": {"descrizione": "Cambogia", "descrizione_inglese": "Cambodia", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 311, "fields": {"descrizione": "Sri Lanka (ex Ceylon)", "descrizione_inglese": "Sri Lanka", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 314, "fields": {"descrizione": "Cinese, Repubblica Popolare", "descrizione_inglese": "China", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 315, "fields": {"descrizione": "Cipro", "descrizione_inglese": "Cyprus", "area": "11", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 319, "fields": {"descrizione": "Corea, Repubblica Popolare Democratica (Corea del Nord)", "descrizione_inglese": "Korea, Democratic People's Republic of", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 320, "fields": {"descrizione": "Corea, Repubblica (Corea del Sud)", "descrizione_inglese": "Korea, Republic of", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 322, "fields": {"descrizione": "Emirati Arabi Uniti", "descrizione_inglese": "United Arab Emirates", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 323, "fields": {"descrizione": "<PERSON><PERSON><PERSON>", "descrizione_inglese": "Philippines", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 324, "fields": {"descrizione": "Territori dell'Autonomia Palestinese", "descrizione_inglese": "Palestinian Territory, Occupied", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 326, "fields": {"descrizione": "Giappone", "descrizione_inglese": "Japan", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 327, "fields": {"descrizione": "Giordania", "descrizione_inglese": "Jordan", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 330, "fields": {"descrizione": "India", "descrizione_inglese": "India", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 331, "fields": {"descrizione": "Indonesia", "descrizione_inglese": "Indonesia", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 332, "fields": {"descrizione": "Iran, Repubblica Islamica del", "descrizione_inglese": "Iran, Islamic Republic of", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 333, "fields": {"descrizione": "Iraq", "descrizione_inglese": "Iraq", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 334, "fields": {"descrizione": "<PERSON><PERSON>", "descrizione_inglese": "Israel", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 335, "fields": {"descrizione": "Kuwait", "descrizione_inglese": "Kuwait", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 336, "fields": {"descrizione": "Laos", "descrizione_inglese": "Lao People's Democratic Republic", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 337, "fields": {"descrizione": "Libano", "descrizione_inglese": "Lebanon", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 338, "fields": {"descrizione": "Timor Orientale", "descrizione_inglese": "East Timor", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 339, "fields": {"descrizione": "Maldive", "descrizione_inglese": "Maldives", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 340, "fields": {"descrizione": "Malaysia", "descrizione_inglese": "Malaysia", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 341, "fields": {"descrizione": "Mongolia", "descrizione_inglese": "Mongolia", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 342, "fields": {"descrizione": "Nepal", "descrizione_inglese": "Nepal", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 343, "fields": {"descrizione": "Oman", "descrizione_inglese": "Oman", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 344, "fields": {"descrizione": "Pakistan", "descrizione_inglese": "Pakistan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 345, "fields": {"descrizione": "Qatar", "descrizione_inglese": "Qatar", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 346, "fields": {"descrizione": "Singapore", "descrizione_inglese": "Singapore", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 348, "fields": {"descrizione": "Siria", "descrizione_inglese": "Syrian Arab Republic", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 349, "fields": {"descrizione": "Thailandia", "descrizione_inglese": "Thailand", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 351, "fields": {"descrizione": "Tu<PERSON><PERSON>", "descrizione_inglese": "Turkey", "area": "12", "continente": "1"}}, {"model": "anagraficabase.stato", "pk": 353, "fields": {"descrizione": "Vietnam", "descrizione_inglese": "Viet Nam", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 354, "fields": {"descrizione": "Yemen", "descrizione_inglese": "Yemen", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 356, "fields": {"descrizione": "Kazakhstan", "descrizione_inglese": "Kazakhstan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 357, "fields": {"descrizione": "Uzbekistan", "descrizione_inglese": "Uzbekistan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 358, "fields": {"descrizione": "Armenia", "descrizione_inglese": "Armenia", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 359, "fields": {"descrizione": "Azerbaigian", "descrizione_inglese": "Azerbaijan", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 360, "fields": {"descrizione": "Georgia", "descrizione_inglese": "Georgia", "area": "31", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 361, "fields": {"descrizione": "Kirghizistan", "descrizione_inglese": "Kyrgyzstan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 362, "fields": {"descrizione": "Tagikistan", "descrizione_inglese": "Tajikistan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 363, "fields": {"descrizione": "Taiwan (ex Formosa)", "descrizione_inglese": "Taiwan, Province of China", "area": "33", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 364, "fields": {"descrizione": "Turkmenistan", "descrizione_inglese": "Turkmenistan", "area": "32", "continente": "3"}}, {"model": "anagraficabase.stato", "pk": 401, "fields": {"descrizione": "Algeria", "descrizione_inglese": "Algeria", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 402, "fields": {"descrizione": "Angola", "descrizione_inglese": "Angola", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 404, "fields": {"descrizione": "Costa d'Avorio", "descrizione_inglese": "Côte D'Ivoire", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 406, "fields": {"descrizione": "Benin (ex Dahomey)", "descrizione_inglese": "Benin", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 408, "fields": {"descrizione": "Botswana", "descrizione_inglese": "Botswana", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 409, "fields": {"descrizione": "Burkina Faso (ex Alto Volta)", "descrizione_inglese": "Burkina Faso", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 410, "fields": {"descrizione": "Burundi", "descrizione_inglese": "Burundi", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 411, "fields": {"descrizione": "<PERSON><PERSON>", "descrizione_inglese": "Cameroon", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 413, "fields": {"descrizione": "Capo Verde", "descrizione_inglese": "Cape Verde", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 414, "fields": {"descrizione": "Centrafricana, Repubblica", "descrizione_inglese": "Central African Republic", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 415, "fields": {"descrizione": "Ciad", "descrizione_inglese": "Chad", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 417, "fields": {"descrizione": "Comore", "descrizione_inglese": "Comoros", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 418, "fields": {"descrizione": "Congo (Repubblica del)", "descrizione_inglese": "Congo", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 419, "fields": {"descrizione": "Egitto", "descrizione_inglese": "Egypt", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 420, "fields": {"descrizione": "Etiopia", "descrizione_inglese": "Ethiopia", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 421, "fields": {"descrizione": "Gabon", "descrizione_inglese": "Gabon", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 422, "fields": {"descrizione": "Gambia", "descrizione_inglese": "Gambia", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 423, "fields": {"descrizione": "Ghana", "descrizione_inglese": "Ghana", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 424, "fields": {"descrizione": "Gibuti", "descrizione_inglese": "Djibouti", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 425, "fields": {"descrizione": "Guinea", "descrizione_inglese": "Guinea", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 426, "fields": {"descrizione": "Guinea Bissau", "descrizione_inglese": "Guinea-Bissau", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 427, "fields": {"descrizione": "Guinea Equatoriale", "descrizione_inglese": "Equatorial Guinea", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 428, "fields": {"descrizione": "Kenya", "descrizione_inglese": "Kenya", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 429, "fields": {"descrizione": "Lesotho", "descrizione_inglese": "Lesotho", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 430, "fields": {"descrizione": "Liberia", "descrizione_inglese": "Liberia", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 431, "fields": {"descrizione": "Libia", "descrizione_inglese": "Libyan Arab Jam<PERSON>riya", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 432, "fields": {"descrizione": "Madagascar", "descrizione_inglese": "Madagascar", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 434, "fields": {"descrizione": "Malawi", "descrizione_inglese": "Malawi", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 435, "fields": {"descrizione": "Mali", "descrizione_inglese": "Mali", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 436, "fields": {"descrizione": "Marocco", "descrizione_inglese": "Morocco", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 437, "fields": {"descrizione": "Mauritania", "descrizione_inglese": "Mauritania", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 438, "fields": {"descrizione": "Mauritius", "descrizione_inglese": "Mauritius", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 440, "fields": {"descrizione": "Mozambico", "descrizione_inglese": "Mozambique", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 441, "fields": {"descrizione": "Namibia", "descrizione_inglese": "Namibia", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 442, "fields": {"descrizione": "Niger", "descrizione_inglese": "Niger", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 443, "fields": {"descrizione": "Nigeria", "descrizione_inglese": "Nigeria", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 446, "fields": {"descrizione": "<PERSON><PERSON><PERSON>", "descrizione_inglese": "Rwanda", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 448, "fields": {"descrizione": "São Tomé e Principe", "descrizione_inglese": "Sao Tome and Principe", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 449, "fields": {"descrizione": "Seychelles", "descrizione_inglese": "Seychelles", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 450, "fields": {"descrizione": "Senegal", "descrizione_inglese": "Senegal", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 451, "fields": {"descrizione": "Sierra Leone", "descrizione_inglese": "Sierra Leone", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 453, "fields": {"descrizione": "Somalia", "descrizione_inglese": "Somalia", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 454, "fields": {"descrizione": "Sud Africa", "descrizione_inglese": "South Africa", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 455, "fields": {"descrizione": "Sudan", "descrizione_inglese": "Sudan", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 456, "fields": {"descrizione": "Swaziland", "descrizione_inglese": "Swaziland", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 457, "fields": {"descrizione": "Tanzania", "descrizione_inglese": "Tanzania, United Republic of", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 458, "fields": {"descrizione": "Togo", "descrizione_inglese": "Togo", "area": "22", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 460, "fields": {"descrizione": "Tunisia", "descrizione_inglese": "Tunisia", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 461, "fields": {"descrizione": "Uganda", "descrizione_inglese": "Uganda", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 463, "fields": {"descrizione": "Congo, Repubblica democratica del (ex Zaire)", "descrizione_inglese": "Congo, The Democratic Republic of The", "area": "24", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 464, "fields": {"descrizione": "Zambia", "descrizione_inglese": "Zambia", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 465, "fields": {"descrizione": "Zimbabwe (ex Rhodesia)", "descrizione_inglese": "Zimbabwe", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 466, "fields": {"descrizione": "Eritrea", "descrizione_inglese": "Eritrea", "area": "23", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 467, "fields": {"descrizione": "Sud Sudan, Repubblica del", "descrizione_inglese": "South Sudan, Republic of", "area": "21", "continente": "2"}}, {"model": "anagraficabase.stato", "pk": 503, "fields": {"descrizione": "Antigua e Barbuda", "descrizione_inglese": "Antigua and Barbuda", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 505, "fields": {"descrizione": "Bahamas", "descrizione_inglese": "Bahamas", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 506, "fields": {"descrizione": "Barbados", "descrizione_inglese": "Barbados", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 507, "fields": {"descrizione": "Belize", "descrizione_inglese": "Belize", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 509, "fields": {"descrizione": "Canada", "descrizione_inglese": "Canada", "area": "41", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 513, "fields": {"descrizione": "Costa Rica", "descrizione_inglese": "Costa Rica", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 514, "fields": {"descrizione": "Cuba", "descrizione_inglese": "Cuba", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 515, "fields": {"descrizione": "Dominica", "descrizione_inglese": "Dominica", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 516, "fields": {"descrizione": "Dominicana, Repubblica", "descrizione_inglese": "Dominican Republic", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 517, "fields": {"descrizione": "El Salvador", "descrizione_inglese": "El Salvador", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 518, "fields": {"descrizione": "Giamaic<PERSON>", "descrizione_inglese": "Jamaica", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 519, "fields": {"descrizione": "Grenada", "descrizione_inglese": "Grenada", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 523, "fields": {"descrizione": "Guatemala", "descrizione_inglese": "Guatemala", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 524, "fields": {"descrizione": "Haiti", "descrizione_inglese": "Haiti", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 525, "fields": {"descrizione": "Honduras", "descrizione_inglese": "Honduras", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 527, "fields": {"descrizione": "Messico", "descrizione_inglese": "Mexico", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 529, "fields": {"descrizione": "Nicaragua", "descrizione_inglese": "Nicaragua", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 530, "fields": {"descrizione": "Panama", "descrizione_inglese": "Panama", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 532, "fields": {"descrizione": "Saint Lucia", "descrizione_inglese": "Saint Lucia", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 533, "fields": {"descrizione": "Saint Vincent e Grenadine", "descrizione_inglese": "Saint Vincent and The Grenadines", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 534, "fields": {"descrizione": "Saint Kitts e Nevis", "descrizione_inglese": "Saint Kitts and Nevis", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 536, "fields": {"descrizione": "Stati Uniti d'America", "descrizione_inglese": "United States", "area": "41", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 602, "fields": {"descrizione": "Argentina", "descrizione_inglese": "Argentina", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 604, "fields": {"descrizione": "Bolivia", "descrizione_inglese": "Bolivia, Plurinational State of", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 605, "fields": {"descrizione": "Brasile", "descrizione_inglese": "Brazil", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 606, "fields": {"descrizione": "Cile", "descrizione_inglese": "Chile", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 608, "fields": {"descrizione": "Colombia", "descrizione_inglese": "Colombia", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 609, "fields": {"descrizione": "Ecuador", "descrizione_inglese": "Ecuador", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 612, "fields": {"descrizione": "Guyana", "descrizione_inglese": "Guyana", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 614, "fields": {"descrizione": "Paraguay", "descrizione_inglese": "Paraguay", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 615, "fields": {"descrizione": "<PERSON><PERSON>", "descrizione_inglese": "Peru", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 616, "fields": {"descrizione": "Suriname", "descrizione_inglese": "Suriname", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 617, "fields": {"descrizione": "Trinidad e Tobago", "descrizione_inglese": "Trinidad and Tobago", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 618, "fields": {"descrizione": "Uruguay", "descrizione_inglese": "Uruguay", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 619, "fields": {"descrizione": "Venezuela", "descrizione_inglese": "Venezuela, Bolivarian Republic of", "area": "42", "continente": "4"}}, {"model": "anagraficabase.stato", "pk": 701, "fields": {"descrizione": "Australia", "descrizione_inglese": "Australia", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 703, "fields": {"descrizione": "Figi", "descrizione_inglese": "Fiji", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 708, "fields": {"descrizione": "Kiribati", "descrizione_inglese": "Kiribati", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 712, "fields": {"descrizione": "Marshall, <PERSON><PERSON>", "descrizione_inglese": "Marshall Islands", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 713, "fields": {"descrizione": "Micronesia, Stati Federati", "descrizione_inglese": "Micronesia, Federated States of", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 715, "fields": {"descrizione": "Nauru", "descrizione_inglese": "Nauru", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 719, "fields": {"descrizione": "Nuova Zelanda", "descrizione_inglese": "New Zealand", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 720, "fields": {"descrizione": "<PERSON><PERSON>", "descrizione_inglese": "<PERSON><PERSON>", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 721, "fields": {"descrizione": "Papua Nuova Guinea", "descrizione_inglese": "Papua New Guinea", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 725, "fields": {"descrizione": "Salomone, Isole", "descrizione_inglese": "Solomon Islands", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 727, "fields": {"descrizione": "Samoa", "descrizione_inglese": "Samoa", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 730, "fields": {"descrizione": "Tonga", "descrizione_inglese": "Tonga", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 731, "fields": {"descrizione": "Tuvalu", "descrizione_inglese": "Tuvalu", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 732, "fields": {"descrizione": "Vanuatu", "descrizione_inglese": "Vanuatu", "area": "50", "continente": "5"}}, {"model": "anagraficabase.stato", "pk": 999, "fields": {"descrizione": "Apolide", "descrizione_inglese": "Stateless", "area": "60", "continente": "6"}}, {"model": "anagraficabase.provincia", "pk": "AG", "fields": {"descrizione": "AGRIGENTO"}}, {"model": "anagraficabase.provincia", "pk": "AL", "fields": {"descrizione": "ALESSANDRIA"}}, {"model": "anagraficabase.provincia", "pk": "AN", "fields": {"descrizione": "ANCONA"}}, {"model": "anagraficabase.provincia", "pk": "AO", "fields": {"descrizione": "AOSTA"}}, {"model": "anagraficabase.provincia", "pk": "AP", "fields": {"descrizione": "ASCOLI PICENO"}}, {"model": "anagraficabase.provincia", "pk": "AQ", "fields": {"descrizione": "L'AQUILA"}}, {"model": "anagraficabase.provincia", "pk": "AR", "fields": {"descrizione": "AREZZO"}}, {"model": "anagraficabase.provincia", "pk": "AT", "fields": {"descrizione": "ASTI"}}, {"model": "anagraficabase.provincia", "pk": "AV", "fields": {"descrizione": "AVELLINO"}}, {"model": "anagraficabase.provincia", "pk": "BA", "fields": {"descrizione": "BARI"}}, {"model": "anagraficabase.provincia", "pk": "BG", "fields": {"descrizione": "BERGAMO"}}, {"model": "anagraficabase.provincia", "pk": "BI", "fields": {"descrizione": "BIELLA"}}, {"model": "anagraficabase.provincia", "pk": "BL", "fields": {"descrizione": "BELLUNO"}}, {"model": "anagraficabase.provincia", "pk": "BN", "fields": {"descrizione": "BENEVENTO"}}, {"model": "anagraficabase.provincia", "pk": "BO", "fields": {"descrizione": "BOLOGNA"}}, {"model": "anagraficabase.provincia", "pk": "BR", "fields": {"descrizione": "BRINDISI"}}, {"model": "anagraficabase.provincia", "pk": "BS", "fields": {"descrizione": "BRESCIA"}}, {"model": "anagraficabase.provincia", "pk": "BT", "fields": {"descrizione": "BARLETTA-ANDRIA-TRANI"}}, {"model": "anagraficabase.provincia", "pk": "BZ", "fields": {"descrizione": "BOLZANO"}}, {"model": "anagraficabase.provincia", "pk": "CA", "fields": {"descrizione": "CAGLIARI"}}, {"model": "anagraficabase.provincia", "pk": "CB", "fields": {"descrizione": "CAMPOBASSO"}}, {"model": "anagraficabase.provincia", "pk": "CE", "fields": {"descrizione": "CASERTA"}}, {"model": "anagraficabase.provincia", "pk": "CH", "fields": {"descrizione": "CHIETI"}}, {"model": "anagraficabase.provincia", "pk": "CI", "fields": {"descrizione": "CARBONIA-IGLESIAS"}}, {"model": "anagraficabase.provincia", "pk": "CL", "fields": {"descrizione": "CALTANISSETTA"}}, {"model": "anagraficabase.provincia", "pk": "CN", "fields": {"descrizione": "CUNEO"}}, {"model": "anagraficabase.provincia", "pk": "CO", "fields": {"descrizione": "COMO"}}, {"model": "anagraficabase.provincia", "pk": "CR", "fields": {"descrizione": "CREMONA"}}, {"model": "anagraficabase.provincia", "pk": "CS", "fields": {"descrizione": "COSENZA"}}, {"model": "anagraficabase.provincia", "pk": "CT", "fields": {"descrizione": "CATANIA"}}, {"model": "anagraficabase.provincia", "pk": "CZ", "fields": {"descrizione": "CATANZARO"}}, {"model": "anagraficabase.provincia", "pk": "EN", "fields": {"descrizione": "ENNA"}}, {"model": "anagraficabase.provincia", "pk": "FC", "fields": {"descrizione": "FORLI' CESENA"}}, {"model": "anagraficabase.provincia", "pk": "FE", "fields": {"descrizione": "FERRARA"}}, {"model": "anagraficabase.provincia", "pk": "FG", "fields": {"descrizione": "FOGGIA"}}, {"model": "anagraficabase.provincia", "pk": "FI", "fields": {"descrizione": "FIRENZE"}}, {"model": "anagraficabase.provincia", "pk": "FM", "fields": {"descrizione": "FERMO"}}, {"model": "anagraficabase.provincia", "pk": "FR", "fields": {"descrizione": "FROSINONE"}}, {"model": "anagraficabase.provincia", "pk": "GE", "fields": {"descrizione": "GENOVA"}}, {"model": "anagraficabase.provincia", "pk": "GO", "fields": {"descrizione": "GORIZIA"}}, {"model": "anagraficabase.provincia", "pk": "GR", "fields": {"descrizione": "GROSSETO"}}, {"model": "anagraficabase.provincia", "pk": "IM", "fields": {"descrizione": "IMPERIA"}}, {"model": "anagraficabase.provincia", "pk": "IS", "fields": {"descrizione": "ISERNIA"}}, {"model": "anagraficabase.provincia", "pk": "KR", "fields": {"descrizione": "CROTONE"}}, {"model": "anagraficabase.provincia", "pk": "LC", "fields": {"descrizione": "LECCO"}}, {"model": "anagraficabase.provincia", "pk": "LE", "fields": {"descrizione": "LECCE"}}, {"model": "anagraficabase.provincia", "pk": "LI", "fields": {"descrizione": "LIVORNO"}}, {"model": "anagraficabase.provincia", "pk": "LO", "fields": {"descrizione": "LODI"}}, {"model": "anagraficabase.provincia", "pk": "LT", "fields": {"descrizione": "LATINA"}}, {"model": "anagraficabase.provincia", "pk": "LU", "fields": {"descrizione": "LUCCA"}}, {"model": "anagraficabase.provincia", "pk": "MB", "fields": {"descrizione": "MONZA-BRIANZA"}}, {"model": "anagraficabase.provincia", "pk": "MC", "fields": {"descrizione": "MACERATA"}}, {"model": "anagraficabase.provincia", "pk": "ME", "fields": {"descrizione": "MESSINA"}}, {"model": "anagraficabase.provincia", "pk": "MI", "fields": {"descrizione": "MILANO"}}, {"model": "anagraficabase.provincia", "pk": "MN", "fields": {"descrizione": "MANTOVA"}}, {"model": "anagraficabase.provincia", "pk": "MO", "fields": {"descrizione": "MODENA"}}, {"model": "anagraficabase.provincia", "pk": "MS", "fields": {"descrizione": "MASSA-CARRARA"}}, {"model": "anagraficabase.provincia", "pk": "MT", "fields": {"descrizione": "MATERA"}}, {"model": "anagraficabase.provincia", "pk": "NA", "fields": {"descrizione": "NAPOLI"}}, {"model": "anagraficabase.provincia", "pk": "NO", "fields": {"descrizione": "NOVARA"}}, {"model": "anagraficabase.provincia", "pk": "NU", "fields": {"descrizione": "NUORO"}}, {"model": "anagraficabase.provincia", "pk": "OG", "fields": {"descrizione": "OGLIASTRA"}}, {"model": "anagraficabase.provincia", "pk": "OR", "fields": {"descrizione": "ORISTANO"}}, {"model": "anagraficabase.provincia", "pk": "OT", "fields": {"descrizione": "OLBIA TEMPIO"}}, {"model": "anagraficabase.provincia", "pk": "PA", "fields": {"descrizione": "PALERMO"}}, {"model": "anagraficabase.provincia", "pk": "PC", "fields": {"descrizione": "PIACENZA"}}, {"model": "anagraficabase.provincia", "pk": "PD", "fields": {"descrizione": "PADOVA"}}, {"model": "anagraficabase.provincia", "pk": "PE", "fields": {"descrizione": "PESCARA"}}, {"model": "anagraficabase.provincia", "pk": "PG", "fields": {"descrizione": "PERUGIA"}}, {"model": "anagraficabase.provincia", "pk": "PI", "fields": {"descrizione": "PISA"}}, {"model": "anagraficabase.provincia", "pk": "PN", "fields": {"descrizione": "PORDENONE"}}, {"model": "anagraficabase.provincia", "pk": "PO", "fields": {"descrizione": "PRATO"}}, {"model": "anagraficabase.provincia", "pk": "PR", "fields": {"descrizione": "PARMA"}}, {"model": "anagraficabase.provincia", "pk": "PT", "fields": {"descrizione": "PISTOIA"}}, {"model": "anagraficabase.provincia", "pk": "PU", "fields": {"descrizione": "PESARO URBINO"}}, {"model": "anagraficabase.provincia", "pk": "PV", "fields": {"descrizione": "PAVIA"}}, {"model": "anagraficabase.provincia", "pk": "PZ", "fields": {"descrizione": "POTENZA"}}, {"model": "anagraficabase.provincia", "pk": "RA", "fields": {"descrizione": "RAVENNA"}}, {"model": "anagraficabase.provincia", "pk": "RC", "fields": {"descrizione": "REGGIO DI CALABRIA"}}, {"model": "anagraficabase.provincia", "pk": "RE", "fields": {"descrizione": "REGGIO NELL'EMILIA"}}, {"model": "anagraficabase.provincia", "pk": "RG", "fields": {"descrizione": "RAGUSA"}}, {"model": "anagraficabase.provincia", "pk": "RI", "fields": {"descrizione": "RIETI"}}, {"model": "anagraficabase.provincia", "pk": "RM", "fields": {"descrizione": "ROMA"}}, {"model": "anagraficabase.provincia", "pk": "RN", "fields": {"descrizione": "RIMINI"}}, {"model": "anagraficabase.provincia", "pk": "RO", "fields": {"descrizione": "ROVIGO"}}, {"model": "anagraficabase.provincia", "pk": "SA", "fields": {"descrizione": "SALERNO"}}, {"model": "anagraficabase.provincia", "pk": "SI", "fields": {"descrizione": "SIENA"}}, {"model": "anagraficabase.provincia", "pk": "SO", "fields": {"descrizione": "SONDRIO"}}, {"model": "anagraficabase.provincia", "pk": "SP", "fields": {"descrizione": "LA SPEZIA"}}, {"model": "anagraficabase.provincia", "pk": "SR", "fields": {"descrizione": "SIRACUSA"}}, {"model": "anagraficabase.provincia", "pk": "SS", "fields": {"descrizione": "SASSARI"}}, {"model": "anagraficabase.provincia", "pk": "SV", "fields": {"descrizione": "SAVONA"}}, {"model": "anagraficabase.provincia", "pk": "TA", "fields": {"descrizione": "TARANTO"}}, {"model": "anagraficabase.provincia", "pk": "TE", "fields": {"descrizione": "TERAMO"}}, {"model": "anagraficabase.provincia", "pk": "TN", "fields": {"descrizione": "TRENTO"}}, {"model": "anagraficabase.provincia", "pk": "TO", "fields": {"descrizione": "TORINO"}}, {"model": "anagraficabase.provincia", "pk": "TP", "fields": {"descrizione": "TRAPANI"}}, {"model": "anagraficabase.provincia", "pk": "TR", "fields": {"descrizione": "TERNI"}}, {"model": "anagraficabase.provincia", "pk": "TS", "fields": {"descrizione": "TRIESTE"}}, {"model": "anagraficabase.provincia", "pk": "TV", "fields": {"descrizione": "TREVISO"}}, {"model": "anagraficabase.provincia", "pk": "UD", "fields": {"descrizione": "UDINE"}}, {"model": "anagraficabase.provincia", "pk": "VA", "fields": {"descrizione": "VARESE"}}, {"model": "anagraficabase.provincia", "pk": "VB", "fields": {"descrizione": "VERBANO-CUSIO-OSSOLA"}}, {"model": "anagraficabase.provincia", "pk": "VC", "fields": {"descrizione": "VERCELLI"}}, {"model": "anagraficabase.provincia", "pk": "VE", "fields": {"descrizione": "VENEZIA"}}, {"model": "anagraficabase.provincia", "pk": "VI", "fields": {"descrizione": "VICENZA"}}, {"model": "anagraficabase.provincia", "pk": "VR", "fields": {"descrizione": "VERONA"}}, {"model": "anagraficabase.provincia", "pk": "VS", "fields": {"descrizione": "MEDIO CAMPIDANO"}}, {"model": "anagraficabase.provincia", "pk": "VT", "fields": {"descrizione": "VITERBO"}}, {"model": "anagraficabase.provincia", "pk": "VV", "fields": {"descrizione": "VIBO VALENTIA"}}, {"model": "anagraficabase.tipoarea", "pk": 1, "fields": {"nome": "Congregazione", "descrizione": "Congregazione"}}, {"model": "anagraficabase.tipoarea", "pk": 2, "fields": {"nome": "Istituti Superiori", "descrizione": "Istituti Superiori"}}, {"model": "anagraficabase.tipoarea", "pk": 3, "fields": {"nome": "Comunità Italia", "descrizione": null}}, {"model": "anagraficabase.tipoarea", "pk": 4, "fields": {"nome": "Comunità Estero", "descrizione": "Comunità Estero"}}, {"model": "anagraficabase.area", "pk": 1, "fields": {"nome": "Generalato Italiano", "descrizione": "Generalato Italiano", "tipo_area": 1, "valuta_default": "EUR", "parent": null, "lft": 1, "rght": 20, "tree_id": 2, "level": 0}}, {"model": "anagraficabase.area", "pk": 3, "fields": {"nome": "Province Italia", "descrizione": "Province Italia", "codice": "ITA", "tipo_area": 1, "valuta_default": "EUR", "parent": 1, "lft": 2, "rght": 11, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.area", "pk": 6, "fields": {"nome": "Reggio Emilia", "descrizione": "Reggio Emilia", "tipo_area": 3, "valuta_default": "EUR", "parent": 3, "lft": 7, "rght": 8, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 7, "fields": {"nome": "Roma", "descrizione": "<PERSON><PERSON><PERSON>", "tipo_area": 3, "valuta_default": "EUR", "parent": 3, "lft": 9, "rght": 10, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 8, "fields": {"nome": "Milano", "descrizione": "Milano", "tipo_area": 3, "valuta_default": "EUR", "parent": 3, "lft": 5, "rght": 6, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 9, "fields": {"nome": "Province Sud America", "descrizione": "Province Sud America", "tipo_area": 4, "valuta_default": "EUR", "parent": 1, "lft": 12, "rght": 19, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.area", "pk": 10, "fields": {"nome": "Buenos Aires", "descrizione": "Buenos Aires", "tipo_area": 4, "valuta_default": "ARS", "parent": 9, "lft": 13, "rght": 14, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 11, "fields": {"nome": "Bologna", "descrizione": "Bologna", "tipo_area": 2, "valuta_default": "EUR", "parent": 3, "lft": 3, "rght": 4, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 13, "fields": {"nome": "Lima", "descrizione": "Lima", "tipo_area": 3, "valuta_default": "EUR", "parent": 9, "lft": 15, "rght": 16, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.area", "pk": 14, "fields": {"nome": "Rio de Janeiro", "descrizione": "Rio de Janeiro", "tipo_area": 4, "valuta_default": "BRL", "parent": 9, "lft": 17, "rght": 18, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.esercizio", "pk": 1, "fields": {"nome": "Esercizio fiscale 2018", "descrizione": "Esercizio fiscale 2018", "data_inizio": "2018-01-01", "data_fine": "2018-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 2, "fields": {"nome": "Esercizio fiscale 2019", "descrizione": "Esercizio fiscale 2019", "data_inizio": "2019-01-01", "data_fine": "2019-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 3, "fields": {"nome": "Esercizio fiscale 2020", "descrizione": "Esercizio fiscale 2020", "data_inizio": "2020-01-01", "data_fine": "2020-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 4, "fields": {"nome": "Anno <PERSON>. 2019/2020", "descrizione": "Anno <PERSON>. 2019/2020", "data_inizio": "2019-09-15", "data_fine": "2020-07-30"}}, {"model": "anagraficabase.esercizio", "pk": 5, "fields": {"nome": "Esercizio fiscale 2021", "descrizione": "Esercizio fiscale 2021", "data_inizio": "2021-01-01", "data_fine": "2021-12-31"}}, {"model": "anagraficabase.esercizio", "pk": 6, "fields": {"nome": "Esercizio fiscale 2022", "descrizione": "Esercizio fiscale 2022", "data_inizio": "2022-01-01", "data_fine": "2022-12-31"}}, {"model": "anagraficabase.centrodicosto", "pk": 9, "fields": {"nome": "Scuola dell'infanzia", "descrizione": "Scuola dell'infanzia"}}, {"model": "anagraficabase.centrodicosto", "pk": 10, "fields": {"nome": "Scuola primaria", "descrizione": "Scuola primaria"}}, {"model": "anagraficabase.centrodicosto", "pk": 11, "fields": {"nome": "Scuola secondaria di 1° grado", "descrizione": "Scuola secondaria di 1° grado"}}, {"model": "anagraficabase.centrodicosto", "pk": 12, "fields": {"nome": "Scuola secondaria di 2° grado", "descrizione": "Scuola secondaria di 2° grado"}}, {"model": "anagraficabase.centrodicosto", "pk": 13, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "anagraficabase.centrodicosto", "pk": 14, "fields": {"nome": "Casa di cura", "descrizione": "Casa di cura"}}, {"model": "anagraficabase.centrodicosto", "pk": 15, "fields": {"nome": "R.S.A", "descrizione": "R.S.A"}}, {"model": "anagraficabase.centrodicosto", "pk": 16, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "anagraficabase.centrodicosto", "pk": 17, "fields": {"nome": "Casa per Ferie", "descrizione": "Casa per Ferie"}}, {"model": "anagraficabase.pianodeiconti", "pk": 1, "fields": {"codice": "01.00.0000", "descrizione": "LIQUIDITA'", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": true, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 16, "tree_id": 2, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 2, "fields": {"codice": "01.02.0001", "descrizione": "conto BPM", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 9, "rght": 10, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 4, "fields": {"codice": "55.03.0000", "descrizione": "Spese vitto/utenze/vestiario comunità", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": "0.00", "preventivo_avere_currency": "EUR", "preventivo_avere": "0.00", "parent": 18, "livello": "conto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 11, "tree_id": 9, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 6, "fields": {"codice": "01.01.0001", "descrizione": "Cassa Contanti €", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 13, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 7, "fields": {"codice": "55.03.0003", "descrizione": "Spese telefoniche", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 7, "rght": 8, "tree_id": 9, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 8, "fields": {"codice": "55.03.0002", "descrizione": "Spese elettricità", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 5, "rght": 6, "tree_id": 9, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 9, "fields": {"codice": "55.03.0010", "descrizione": "Spese postali", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": "1569.00", "preventivo_avere_currency": "EUR", "preventivo_avere": "345.00", "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 9, "rght": 10, "tree_id": 9, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 10, "fields": {"codice": "75.00.0000", "descrizione": "RICAVI SCUOLA E COLLEGATI", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 16, "tree_id": 11, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 11, "fields": {"codice": "75.01.0000", "descrizione": "<PERSON><PERSON>", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 10, "livello": "conto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 13, "tree_id": 11, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 12, "fields": {"codice": "01.02.0000", "descrizione": "BANCHE", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 1, "livello": "conto", "impostazione_conto": "banca", "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 8, "rght": 15, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 13, "fields": {"codice": "01.01.0000", "descrizione": "CASSE", "tipologia": "liquidita", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 1, "livello": "conto", "impostazione_conto": "cassa", "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 7, "tree_id": 2, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 14, "fields": {"codice": "26.00.0000", "descrizione": "DEBITI DIVERSI", "tipologia": "patrimoniale", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 8, "tree_id": 6, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 15, "fields": {"codice": "26.01.0000", "descrizione": "DEBITI v/erario", "tipologia": "patrimoniale", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 14, "livello": "conto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 7, "tree_id": 6, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 16, "fields": {"codice": "26.01.0007", "descrizione": "<PERSON>rio c/rite<PERSON>e", "tipologia": "patrimoniale", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 15, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 5, "rght": 6, "tree_id": 6, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 17, "fields": {"codice": "56.00.0000", "descrizione": "SPESE STRAORDINARIE", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 6, "tree_id": 10, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 18, "fields": {"codice": "55.00.0000", "descrizione": "SPESE COMUNITA'", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 12, "tree_id": 9, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 19, "fields": {"codice": "55.03.0001", "descrizione": "Alimentari", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 4, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 9, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 20, "fields": {"codice": "56.02.0000", "descrizione": "Consulenze professionisti", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 17, "livello": "conto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 5, "tree_id": 10, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 21, "fields": {"codice": "56.02.0012", "descrizione": "<PERSON><PERSON><PERSON>", "tipologia": "costi", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 20, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 10, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 22, "fields": {"codice": "75.01.0005", "descrizione": "Rette Liceo Scientifico", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 11, "rght": 12, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 24, "fields": {"codice": "80.01.0000", "descrizione": "Contributi e Sponsorizzazioni da Privati", "tipologia": "economico", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 32, "livello": "conto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 2, "rght": 7, "tree_id": 13, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 25, "fields": {"codice": "80.01.0001", "descrizione": "Beneficienze", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 24, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 13, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 26, "fields": {"codice": "01.01.0002", "descrizione": "<PERSON><PERSON>", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": true, "valuta_default": "USD", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 13, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 5, "rght": 6, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 27, "fields": {"codice": "01.02.0002", "descrizione": "I.O.R.", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 11, "rght": 12, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 28, "fields": {"codice": "75.01.0001", "descrizione": "<PERSON><PERSON> scuola infanzia", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 29, "fields": {"codice": "75.01.0002", "descrizione": "<PERSON><PERSON> scuola primaria", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 5, "rght": 6, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 30, "fields": {"codice": "75.01.0003", "descrizione": "Rette scuola secondaria di 1° grado", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 7, "rght": 8, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 31, "fields": {"codice": "75.01.0004", "descrizione": "<PERSON>tte liceo classico", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 11, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 9, "rght": 10, "tree_id": 11, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 32, "fields": {"codice": "80.00.0000", "descrizione": "RICAVI NON FISCALI", "tipologia": "rica<PERSON>", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": null, "livello": "mastro", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 1, "rght": 8, "tree_id": 13, "level": 0}}, {"model": "anagraficabase.pianodeiconti", "pk": 33, "fields": {"codice": "80.01.0002", "descrizione": "Eredità", "tipologia": "rica<PERSON>", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 24, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 5, "rght": 6, "tree_id": 13, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 34, "fields": {"codice": "01.02.0003", "descrizione": "Banca Intesa", "tipologia": "liquidita", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 12, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 13, "rght": 14, "tree_id": 2, "level": 2}}, {"model": "anagraficabase.pianodeiconti", "pk": 35, "fields": {"codice": "75.02.0000", "descrizione": "<PERSON><PERSON>", "tipologia": "rica<PERSON>", "conto_imputabile": false, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 10, "livello": "conto", "impostazione_conto": null, "codice_gruppo": "75", "codice_mastro": null, "codice_conto": "02", "codice_sottoconto": "0000", "lft": 14, "rght": 15, "tree_id": 11, "level": 1}}, {"model": "anagraficabase.pianodeiconti", "pk": 36, "fields": {"codice": "26.01.0001", "descrizione": "Erario c/IVA a debito", "tipologia": "patrimoniale", "conto_imputabile": true, "conto_multivaluta": false, "valuta_default": "EUR", "preventivo_dare_currency": "EUR", "preventivo_dare": null, "preventivo_avere_currency": "EUR", "preventivo_avere": null, "parent": 15, "livello": "sottoconto", "impostazione_conto": null, "codice_gruppo": null, "codice_mastro": null, "codice_conto": null, "codice_sottoconto": null, "lft": 3, "rght": 4, "tree_id": 6, "level": 2}}, {"model": "anagraficabase.cambio", "pk": 1, "fields": {"data_inizio": "2020-07-01", "data_fine": "2020-12-31", "valuta_origine": "EUR", "valuta_destinazione": "USD", "valore_destinazione": "1.132412"}}, {"model": "anagraficabase.cambio", "pk": 2, "fields": {"data_inizio": "2019-01-01", "data_fine": "2020-12-31", "valuta_origine": "USD", "valuta_destinazione": "EUR", "valore_destinazione": "0.830000"}}, {"model": "anagraficabase.cambio", "pk": 3, "fields": {"data_inizio": "2021-01-01", "data_fine": "2021-12-31", "valuta_origine": "EUR", "valuta_destinazione": "USD", "valore_destinazione": "1.132412"}}, {"model": "anagraficabase.cambio", "pk": 4, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "CAD", "valore_destinazione": "1.278540"}}, {"model": "anagraficabase.cambio", "pk": 5, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "USD", "valuta_destinazione": "EUR", "valore_destinazione": "0.878400"}}, {"model": "anagraficabase.cambio", "pk": 6, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "PEN", "valore_destinazione": "8.578400"}}, {"model": "anagraficabase.cambio", "pk": 7, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "ARS", "valore_destinazione": "9.456200"}}, {"model": "anagraficabase.cambio", "pk": 8, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "BRL", "valore_destinazione": "12.874500"}}, {"model": "anagraficabase.cambio", "pk": 10, "fields": {"data_inizio": "2021-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "CHF", "valore_destinazione": "0.984500"}}, {"model": "anagraficabase.cambio", "pk": 11, "fields": {"data_inizio": "2022-01-01", "data_fine": null, "valuta_origine": "EUR", "valuta_destinazione": "USD", "valore_destinazione": "1.000000"}}]