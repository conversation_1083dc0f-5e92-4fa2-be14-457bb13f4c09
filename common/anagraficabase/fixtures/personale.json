[{"model": "personale.tiporesponsabilitaattivita", "pk": 1, "fields": {"nome": "Superiora", "descrizione": "Superiora"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 2, "fields": {"nome": "Economa", "descrizione": "Economa"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 3, "fields": {"nome": "Preside", "descrizione": "Preside"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 4, "fields": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 5, "fields": {"nome": "Responsabile Qualità", "descrizione": "Responsabile Qualità"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 6, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 7, "fields": {"nome": "Tecnico", "descrizione": "Tecnico"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 8, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 9, "fields": {"nome": "Responsabile Privacy", "descrizione": "Responsabile Privacy"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 10, "fields": {"nome": "Responsabile HACCP", "descrizione": "Responsabile HACCP"}}, {"model": "personale.tiporesponsabilitaattivita", "pk": 11, "fields": {"nome": "Responsabile Amministrativo", "descrizione": "Responsabile Amministrativo"}}, {"model": "personale.tiporesponsabilita626", "pk": 1, "fields": {"nome": "RSPP", "descrizione": "RSPP"}}, {"model": "personale.tiporesponsabilita626", "pk": 2, "fields": {"nome": "ASPP", "descrizione": "ASPP"}}, {"model": "personale.tiporesponsabilita626", "pk": 3, "fields": {"nome": "RLS", "descrizione": "RLS"}}, {"model": "personale.tiporesponsabilita626", "pk": 4, "fields": {"nome": "Medico Competente", "descrizione": "Medico Competente"}}, {"model": "personale.tiporesponsabilita626", "pk": 5, "fields": {"nome": "Resp. <PERSON>", "descrizione": "Resp. <PERSON>"}}, {"model": "personale.tiporesponsabilita626", "pk": 6, "fields": {"nome": "Resp. Squadra Primo Soccorso", "descrizione": "Resp. Squadra Primo Soccorso"}}, {"model": "personale.tiporesponsabilita626", "pk": 7, "fields": {"nome": "<PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON>"}}, {"model": "personale.responsabileattivita", "pk": 2, "fields": {"casa": 1, "figura": 8, "nome": "<PERSON><PERSON>", "inizio_incarico": "2004-03-26", "fine_incarico": "2021-03-26", "data_formazione": "2021-03-26", "note": null}}, {"model": "personale.responsabileattivita", "pk": 3, "fields": {"casa": 1, "figura": 4, "nome": "<PERSON>", "inizio_incarico": "2017-01-01", "fine_incarico": null, "data_formazione": null, "note": null}}, {"model": "personale.responsabile626", "pk": 1, "fields": {"casa": 1, "figura": 1, "nome": "<PERSON>", "inizio_incarico": "2021-03-26", "fine_incarico": "2021-03-26", "data_formazione": "2021-03-26", "note": null}}]