[{"model": "case.tipocasa", "pk": 1, "fields": {"nome": "Complesso di edifici", "descrizione": "Complesso di edifici"}}, {"model": "case.tipocasa", "pk": 2, "fields": {"nome": "Edificio <PERSON>", "descrizione": "Edificio <PERSON>"}}, {"model": "case.tipocasa", "pk": 3, "fields": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "case.tipocasa", "pk": 4, "fields": {"nome": "Appartamento", "descrizione": "Appartamento"}}, {"model": "case.tipocasa", "pk": 5, "fields": {"nome": "complesso di palazzine", "descrizione": "complesso di palazzine"}}, {"model": "case.tipocasa", "pk": 6, "fields": {"nome": "Ospedale", "descrizione": "Ospedale"}}, {"model": "case.tipocasa", "pk": 7, "fields": {"nome": "Casa per ferie", "descrizione": "Casa per ferie"}}, {"model": "case.tipocasa", "pk": 8, "fields": {"nome": "Casa per Ferie e Scuola", "descrizione": "Casa per Ferie e Scuola"}}, {"model": "case.tipoente", "pk": 1, "fields": {"nome": "tipo ente 1", "descrizione": null}}, {"model": "case.tipoapertura", "pk": 1, "fields": {"nome": "Annuale", "descrizione": "Annuale"}}, {"model": "case.tipoapertura", "pk": 2, "fields": {"nome": "Stagionale", "descrizione": "Stagionale"}}, {"model": "case.tipoapertura", "pk": 3, "fields": {"nome": "Occasionale", "descrizione": "Occasionale"}}, {"model": "case.tipoapertura", "pk": 4, "fields": {"nome": "Alla bisogna", "descrizione": "Alla bisogna"}}, {"model": "case.ente", "pk": 1, "fields": {"denominazione": "Ente Di prova", "tipo_ente": 1, "indirizzo": "via pippo", "citta": "brunico", "provincia": null, "cap": "", "regione": null, "nazione": 1, "telefono": null, "fax": null, "email": null, "data_riconoscimento": null, "data_registrazione": null, "data_pubblicazione": null, "data_iscrizione": null, "partita_iva": null, "codice_fiscale": null, "note": ""}}, {"model": "case.casa", "pk": 1, "fields": {"nome": "Casa Generalizia", "descrizione": "Casa Generalizia", "tipo_casa": 1, "tipo_apertura": 1, "indirizzo": "Via della Pace, 1", "citta": "Roma", "provincia": "RM", "cap": "00123", "regione": null, "nazione": 1, "telefono": "+39 522344224", "fax": "+39 522344224", "email": "<EMAIL>", "diocesi": "Roma", "vescovo": "Santo <PERSON>", "ente_appartenenza": 1, "data_apertura": null, "data_chiusura": null, "data_erezione": null, "data_soppressione": null, "circostanze": ""}}, {"model": "case.tipoattivita", "pk": 1, "fields": {"nome": "Ospedale", "descrizione": "Ospedale"}}, {"model": "case.tipoattivita", "pk": 2, "fields": {"nome": "Casa di Cura", "descrizione": "Casa di Cura"}}, {"model": "case.tipoattivita", "pk": 3, "fields": {"nome": "RSA", "descrizione": "RSA"}}, {"model": "case.tipoattivita", "pk": 4, "fields": {"nome": "Casa per Anziani Autosuff.", "descrizione": "Casa per Anziani Autosuff."}}, {"model": "case.tipoattivita", "pk": 5, "fields": {"nome": "Casa per Anziani Non autosuff.", "descrizione": "Casa per Anziani Non autosuff."}}, {"model": "case.tipoattivita", "pk": 6, "fields": {"nome": "Casa Accoglienza", "descrizione": "Casa Accoglienza"}}, {"model": "case.tipoattivita", "pk": 7, "fields": {"nome": "Casa Famiglia", "descrizione": "Casa Famiglia"}}, {"model": "case.tipoattivita", "pk": 8, "fields": {"nome": "Istituto <PERSON>", "descrizione": "Istituto <PERSON>"}}, {"model": "case.tipoattivita", "pk": 9, "fields": {"nome": "Accoglienza minori", "descrizione": "Accoglienza minori"}}, {"model": "case.tipoattivita", "pk": 10, "fields": {"nome": "Accoglienza maggiorenni", "descrizione": "Accoglienza maggiorenni"}}, {"model": "case.tipoattivita", "pk": 11, "fields": {"nome": "Accoglienza famiglie", "descrizione": "Accoglienza famiglie"}}, {"model": "case.tipoattivita", "pk": 12, "fields": {"nome": "Accoglienza anziani", "descrizione": "Accoglienza anziani"}}, {"model": "case.tipoattivita", "pk": 13, "fields": {"nome": "Attività scolastica", "descrizione": "Attività scolastica"}}, {"model": "case.tipoattivita", "pk": 14, "fields": {"nome": "<PERSON><PERSON>", "descrizione": "<PERSON><PERSON>"}}, {"model": "case.tipoattivita", "pk": 15, "fields": {"nome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "descrizione": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"model": "case.tipoattivita", "pk": 16, "fields": {"nome": "Maternità", "descrizione": "Maternità"}}, {"model": "case.tipoattivita", "pk": 17, "fields": {"nome": "Dispensario", "descrizione": "Dispensario"}}, {"model": "case.attivita", "pk": 1, "fields": {"casa": 1, "tipo_attivita": 12, "data_attivita": "2021-03-26", "numero_utenti": 1223, "numero_operatori": 0, "numero_religiosi": 0, "numero_volontari": 0, "numero_lavoratori_autonomi": 0, "note": null}}]