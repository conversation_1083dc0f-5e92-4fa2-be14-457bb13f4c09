from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget

from common.anagraficabase.models import Area


class AreaResource(resources.ModelResource):
    parent = fields.Field(
        column_name='parent',
        attribute='parent',
        widget=ForeignKeyWidget(Area, 'id')
    )

    class Meta:
        model = Area
        fields = (
            'id', 'nome', 'descrizione', 'codice', 'parent', 'valuta_default',
        )
