# Generated by Django 2.2.28 on 2024-02-07 09:56

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0044_auto_20231222_1322'),
    ]

    operations = [
        migrations.AlterField(
            model_name='area',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_destinazione',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta destinazione'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_origine',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta origine'),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
    ]
