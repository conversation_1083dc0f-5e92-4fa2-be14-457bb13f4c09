# Generated by Django 2.2.11 on 2023-02-21 10:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0006_auto_20220222_1036'),
    ]

    operations = [
        migrations.AddField(
            model_name='esercizio',
            name='chiuso',
            field=models.BooleanField(default=False, verbose_name='nome'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='conto_profitti_perdite',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conto_profitti_perdite_pk', to='anagraficabase.PianoDeiConti', verbose_name='conto profitti e perdite'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='conto_risultati_esercizi_precedenti',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conto_risultati_esercizi_precedenti_pk', to='anagraficabase.PianoDeiConti', verbose_name='conto risultati esercizi precendenti'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='conto_risultato_esercizio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conto_risultato_esercizio_pk', to='anagraficabase.PianoDeiConti', verbose_name="conto risultato d'esercizio"),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='conto_stato_patrimoniale_finale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conto_stato_patrimoniale_finale_pk', to='anagraficabase.PianoDeiConti', verbose_name='conto stato patrimoniale finale'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='conto_stato_patrimoniale_iniziale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conto_stato_patrimoniale_iniziale_pk', to='anagraficabase.PianoDeiConti', verbose_name='conto stato patrimoniale iniziale'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='data_operazione_apertura',
            field=models.DateField(blank=True, null=True, verbose_name='data operazione apertura'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='data_operazione_chiusura',
            field=models.DateField(blank=True, null=True, verbose_name='data operazione chiusura'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='descrizione_operazione_apertura',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione operazione apertura'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='descrizione_operazione_chiusura',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione operazione chiusura'),
        ),
        migrations.AddField(
            model_name='esercizio',
            name='esercizio_successivo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagraficabase.Esercizio', verbose_name='esercizio successivo'),
        ),
    ]
