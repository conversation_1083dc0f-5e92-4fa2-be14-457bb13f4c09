# Generated by Django 2.2.28 on 2023-07-26 15:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0023_auto_20230622_1045'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='stato',
            options={'ordering': ('descrizione', 'descrizione_inglese'), 'verbose_name': 'Nazione', 'verbose_name_plural': '<PERSON><PERSON>'},
        ),
        migrations.AddField(
            model_name='provincia',
            name='nazione',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, to='anagraficabase.Stato', verbose_name='nazione'),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='area',
            field=models.CharField(blank=True, choices=[('NORD_OVEST', 'Nord-Ovest'), ('NORD_EST', 'Nord-Est'), ('CENTRO', 'Centro'), ('SUD', 'Sud'), ('ISOLE', 'Isole')], max_length=200, null=True, verbose_name='area'),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='codice',
            field=models.CharField(db_index=True, max_length=2, primary_key=True, serialize=False, verbose_name='codice'),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='descrizione',
            field=models.CharField(max_length=200, verbose_name='descrizione'),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='regione',
            field=models.CharField(blank=True, choices=[('ABRUZZO', 'Abruzzo'), ('BASILICATA', 'Basilicata'), ('CALABRIA', 'Calabria'), ('CAMPANIA', 'Campania'), ('EMILIA_ROMAGNA', 'Emilia Romagna'), ('FRIULI_VENEZIA_GIULIA', 'Friuli Venezia-Giulia'), ('LAZIO', 'Lazio'), ('LIGURIA', 'Liguria'), ('LOMBARDIA', 'Lombardia'), ('MARCHE', 'Marche'), ('MOLISE', 'Molise'), ('PIEMONTE', 'Piemonte'), ('PUGLIA', 'Puglia'), ('SARDEGNA', 'Sardegna'), ('SICILIA', 'Sicilia'), ('TOSCANA', 'Toscana'), ('TRENTINO_ALTO_ADIGE', 'Trentino-Alto Adige'), ('UMBRIA', 'Umbria'), ('VALLE_DAOSTA', "Valle D'Aosta"), ('VENETO', 'Veneto')], max_length=200, null=True, verbose_name='regione'),
        ),
    ]
