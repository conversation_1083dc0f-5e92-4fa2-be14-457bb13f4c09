# Generated by Django 2.2.28 on 2025-02-20 10:39

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0076_auto_20250205_0931'),
    ]

    operations = [
        migrations.CreateModel(
            name='PianoDeiContiAttivo',
            fields=[
            ],
            options={
                'verbose_name': 'Conto - ATTIVO',
                'verbose_name_plural': 'Conti - ATTIVO',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.AlterField(
            model_name='area',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_destinazione',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta destinazione'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_origine',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta origine'),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(blank=True, choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default=None, max_length=3, null=True, verbose_name='valuta default'),
        ),
    ]
