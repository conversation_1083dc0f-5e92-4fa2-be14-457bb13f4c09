# Generated by Django 2.2.28 on 2024-08-09 12:44

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0062_auto_20240807_1527'),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON><PERSON><PERSON><PERSON>Giorno',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('santi', models.TextField(verbose_name='santi')),
                ('giorno', models.PositiveIntegerField(validators=[django.core.validators.MaxValueValidator(31), django.core.validators.MinValueValidator(1)], verbose_name='giorno')),
                ('mese', models.PositiveIntegerField(validators=[django.core.validators.MaxValueValidator(12), django.core.validators.MinValueValidator(1)], verbose_name='mese')),
            ],
            options={
                'verbose_name': '<PERSON><PERSON> del giorno',
                'verbose_name_plural': '<PERSON><PERSON> del giorno',
                'ordering': ('mese', 'giorno'),
                'unique_together': {('giorno', 'mese')},
            },
        ),
    ]
