# Generated by Django 2.2.28 on 2023-08-24 16:24

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0024_auto_20230726_1541'),
    ]

    operations = [
        migrations.CreateModel(
            name='PianoDeiContiCosti',
            fields=[
            ],
            options={
                'verbose_name': 'Piano dei Conti - COSTO',
                'verbose_name_plural': 'Piani dei Conti - COSTI',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.CreateModel(
            name='PianoDeiContiLiquidita',
            fields=[
            ],
            options={
                'verbose_name': "Piano dei Conti - LIQUIDITA'",
                'verbose_name_plural': "Piani dei Conti - LIQUIDITA'",
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.CreateModel(
            name='PianoDeiContiPatrimoniale',
            fields=[
            ],
            options={
                'verbose_name': 'Piano dei Conti - PATRIMONIALE',
                'verbose_name_plural': 'Piani dei Conti - PATRIMONIALI',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.CreateModel(
            name='PianoDeiContiRicavi',
            fields=[
            ],
            options={
                'verbose_name': 'Piano dei Conti - RICAVO',
                'verbose_name_plural': 'Piani dei Conti - RICAVI',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
    ]
