# Generated by Django 2.2.28 on 2024-11-06 09:57

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0066_auto_20241003_1152'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
    ]
