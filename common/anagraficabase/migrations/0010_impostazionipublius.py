# Generated by Django 2.2.11 on 2023-04-07 11:01

from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0002_auto_20181220_0803'),
        ('anagraficabase', '0009_auto_20230301_1225'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImpostazioniPublius',
            fields=[
                ('preferences_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='preferences.Preferences')),
                ('ordine_campi_stampa', models.CharField(choices=[('appellativo_cognome_nome', 'Appellativo, Cognome, Nome'), ('appellativo_nome_cognome', 'Appellativo, Nome, Cognome'), ('appellativo_nome', 'Appellativo, Nome'), ('cognome_nome', 'Cognome, Nome')], max_length=200)),
            ],
            options={
                'verbose_name_plural': 'Impostazioni Publius',
            },
            bases=('preferences.preferences',),
            managers=[
                ('singleton', django.db.models.manager.Manager()),
            ],
        ),
    ]
