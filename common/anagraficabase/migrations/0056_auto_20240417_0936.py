# Generated by Django 2.2.28 on 2024-04-17 09:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0055_auto_20240416_1100'),
    ]

    operations = [
        migrations.AlterField(
            model_name='statistichegrafici',
            name='grafico',
            field=models.CharField(choices=[('matthaeus_conto_economico', 'Matt<PERSON>eus - Conto Economico'), ('matthaeus_riepilogo_costi', '<PERSON>haeus - Riepilogo Costi'), ('matthaeus_riepilogo_ricavi', '<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>'), ('matthaeus_budget', 'Matthaeus - Budget'), ('matthaeus_liquidita', '<PERSON>haeus - Liquidita'), ('matthaeus_mol', '<PERSON>haeus - Margine Operativo Lordo'), ('matthaeus_risultato_gestione_finanziaria', 'Matthaeus - risultato gestione finanziaria'), ('matthaeus_risultato_gestione_patrimoniale', 'Matthaeus - risultato gestione patrimoniale'), ('matthaeus_risultato_esercizio', 'Matthaeus - risultato esercizio'), ('matthaeus_risultatofinanziario_gestione_operativa', 'Matthaeus - risultato finanziario della gestione operativa'), ('matthaeus_risultatofinanziario_gestione_finanziaria', 'Matthaeus - risultato finanziario della gestione finanziaria'), ('matthaeus_risultatofinanziario_gestione_patrimoniale', 'Matthaeus - risultato finanziario della gestione patrimoniale'), ('matthaeus_risultatofinanziario_esercizio', 'Matthaeus - risultato finanziario esercizio'), ('publius_proiezionetotalereligiosi', 'Publius - Totale Religiosi (proiezione)'), ('publius_etareligiosi_oggi', 'Publius - Età Religiosi (oggi)'), ('publius_etareligiosi_5', 'Publius - Età Religiosi (tra 5 anni)'), ('publius_etareligiosi_10', 'Publius - Età Religiosi (tra 10 anni)'), ('publius_etareligiosi_15', 'Publius - Età Religiosi (tra 15 anni)'), ('publius_etareligiosi_20', 'Publius - Età Religiosi (tra 20 anni)')], max_length=200),
        ),
    ]
