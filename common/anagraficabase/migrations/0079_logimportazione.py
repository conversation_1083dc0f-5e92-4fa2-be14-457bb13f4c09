# Generated by Django 2.2.28 on 2025-03-31 12:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0078_impostazionimagister_sovrascrivi_provincia_religiosa_da_casa'),
    ]

    operations = [
        migrations.CreateModel(
            name='LogImportazione',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_inizio', models.DateTimeField(auto_now_add=True, verbose_name='data inizio')),
                ('data_fine', models.DateTimeField(blank=True, null=True, verbose_name='data fine')),
                ('stato_importazione', models.CharField(max_length=200, verbose_name='stato_importazione')),
                ('applicativo', models.CharField(choices=[('mattha<PERSON>', '<PERSON><PERSON><PERSON>'), ('publius', 'Publius'), ('saulo', '<PERSON><PERSON>'), ('martinus', '<PERSON><PERSON>'), ('cyrenaeus', '<PERSON><PERSON><PERSON>')], max_length=200, verbose_name='applicazione')),
                ('tipo_importazione', models.CharField(max_length=200, verbose_name='tipo importazione')),
                ('note', models.TextField(blank=True, null=True, verbose_name='note')),
            ],
            options={
                'verbose_name': 'Log Importazione',
                'verbose_name_plural': 'Log Importazioni',
                'ordering': ('-data_inizio',),
            },
        ),
    ]
