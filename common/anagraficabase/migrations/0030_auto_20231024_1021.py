# Generated by Django 2.2.28 on 2023-10-24 10:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0029_auto_20231003_1604'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='stato',
            name='area',
            field=models.CharField(choices=[('11', 'Unione europea'), ('12', 'Europa centro orientale'), ('13', 'Altri paesi europei'), ('21', 'Africa settentrionale'), ('22', 'Africa occidentale'), ('23', 'Africa orientale'), ('24', 'Africa centro meridionale'), ('31', 'Asia occidentale'), ('32', 'Asia centro meridionale'), ('33', 'Asia orientale'), ('41', 'America settentrionale'), ('42', 'America centro meridionale'), ('50', 'Oceania'), ('60', 'Apolidi')], max_length=200, verbose_name='area'),
        ),
        migrations.AlterField(
            model_name='stato',
            name='codice',
            field=models.CharField(blank=True, max_length=3, null=True, verbose_name='codice'),
        ),
        migrations.AlterField(
            model_name='stato',
            name='continente',
            field=models.CharField(choices=[('1', 'Europa'), ('2', 'Africa'), ('3', 'Asia'), ('4', 'America'), ('5', 'Oceania'), ('6', 'Apolidi')], max_length=200, verbose_name='continente'),
        ),
        migrations.AlterField(
            model_name='stato',
            name='descrizione',
            field=models.CharField(max_length=200, verbose_name='descrizione'),
        ),
        migrations.AlterField(
            model_name='stato',
            name='descrizione_inglese',
            field=models.CharField(max_length=200, verbose_name='descrizione (inglese)'),
        ),
    ]
