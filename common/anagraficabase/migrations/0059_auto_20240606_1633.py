# Generated by Django 2.2.28 on 2024-06-06 16:33

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0058_frasemotivazionale'),
    ]

    operations = [
        migrations.AlterField(
            model_name='frasemotivazionale',
            name='giorno_calendario',
            field=models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(31), django.core.validators.MinValueValidator(1)], verbose_name='giorno calendario'),
        ),
        migrations.AlterField(
            model_name='frasemotivazionale',
            name='mese_calendario',
            field=models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(12), django.core.validators.MinValueValidator(1)], verbose_name='mese calendario'),
        ),
    ]
