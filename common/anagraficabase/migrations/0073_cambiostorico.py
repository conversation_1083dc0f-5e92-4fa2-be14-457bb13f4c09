# Generated by Django 2.2.28 on 2025-01-22 09:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('exchange', '0001_initial'),
        ('anagraficabase', '0072_chiusuraarea'),
    ]

    operations = [
        migrations.CreateModel(
            name='CambioStorico',
            fields=[
                ('exchangebackend_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='exchange.ExchangeBackend')),
                ('data_inizio', models.DateField()),
                ('data_fine', models.DateField(blank=True, null=True)),
            ],
            bases=('exchange.exchangebackend',),
        ),
    ]
