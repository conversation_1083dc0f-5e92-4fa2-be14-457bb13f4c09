# Generated by Django 2.2.28 on 2024-09-24 09:47

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0064_applicazionemagister'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContoClienti',
            fields=[
            ],
            options={
                'verbose_name': 'Conto Clienti',
                'verbose_name_plural': 'Conti Clienti',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.CreateModel(
            name='ContoFornitori',
            fields=[
            ],
            options={
                'verbose_name': 'Conto Fornitori',
                'verbose_name_plural': '<PERSON>ti Fornitori',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
    ]
