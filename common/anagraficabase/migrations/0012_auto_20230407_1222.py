# Generated by Django 2.2.11 on 2023-04-07 12:22

from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0002_auto_20181220_0803'),
        ('anagraficabase', '0011_auto_20230407_1202'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImpostazioniMagister',
            fields=[
                ('preferences_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='preferences.Preferences')),
                ('ordine_campi_stampa', models.CharField(blank=True, choices=[('appellativo_cognome_nome', 'Appellativo, Cognome, Nome'), ('appellativo_nome_cognome', 'Appellativo, Nome, Cognome'), ('appellativo_nome', 'Appellativo, Nome'), ('cognome_nome', 'Cognome, Nome')], max_length=200, null=True, verbose_name='ordine campi stampa')),
                ('numero_scadenze', models.PositiveIntegerField(default=10, verbose_name='numero massimo scadenze')),
            ],
            options={
                'verbose_name_plural': 'Impostazioni Magister',
            },
            bases=('preferences.preferences',),
            managers=[
                ('singleton', django.db.models.manager.Manager()),
            ],
        ),
        migrations.RemoveField(
            model_name='impostazionipublius',
            name='preferences_ptr',
        ),
        migrations.DeleteModel(
            name='ImpostazioniCyrenaeus',
        ),
        migrations.DeleteModel(
            name='ImpostazioniPublius',
        ),
    ]
