# Generated by Django 2.2.28 on 2025-01-09 12:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0071_elimina_vista_movimenti'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChiusuraArea',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chiuso', models.BooleanField(default=False, verbose_name='chiuso')),
                ('data_chiusura', models.DateField(blank=True, null=True, verbose_name='data chiusura')),
                ('area', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area')),
                ('esercizio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Esercizio', verbose_name='esercizio')),
            ],
            options={
                'verbose_name': 'Chiusura Area',
                'verbose_name_plural': 'Chiusure Aree',
                'ordering': ('esercizio', 'area'),
                'unique_together': {('esercizio', 'area')},
            },
        ),
    ]
