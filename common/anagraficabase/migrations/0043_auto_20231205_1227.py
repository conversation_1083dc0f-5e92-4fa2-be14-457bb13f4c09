# Generated by Django 2.2.28 on 2023-12-05 12:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0042_auto_20231129_1149'),
    ]

    operations = [
        migrations.AlterField(
            model_name='statistichegrafici',
            name='grafico',
            field=models.CharField(choices=[('matthaeus_conto_economico', 'Matthaeus - Conto Economico'), ('matthaeus_riepilogo_costi', 'Matthaeus - Riepilogo Costi'), ('matthaeus_riepilogo_ricavi', '<PERSON><PERSON>eus - <PERSON><PERSON><PERSON><PERSON><PERSON>'), ('matthaeus_budget', 'Matthaeus - Budget'), ('matthaeus_liquidita', '<PERSON><PERSON>eus - Liquidita'), ('matthaeus_mol', '<PERSON>haeus - Margine Operativo Lordo'), ('matthaeus_risultato_gestione_finanziaria', 'Matthaeus - risultato gestione finanziaria'), ('matthaeus_risultato_gestione_patrimoniale', 'Matthaeus - risultato gestione patrimoniale'), ('matthaeus_risultato_esercizio', 'Matthaeus - risultato esercizio'), ('matthaeus_risultatofinanziario_gestione_operativa', 'Matthaeus - risultato finanziario della gestione operativa'), ('matthaeus_risultatofinanziario_gestione_finanziaria', 'Matthaeus - risultato finanziario della gestione finanziaria'), ('matthaeus_risultatofinanziario_gestione_patrimoniale', 'Matthaeus - risultato finanziario della gestione patrimoniale'), ('matthaeus_risultatofinanziario_esercizio', 'Matthaeus - risultato finanziario esercizio')], max_length=200),
        ),
    ]
