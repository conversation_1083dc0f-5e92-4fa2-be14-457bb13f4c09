# Generated by Django 2.2.11 on 2023-05-03 16:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0018_impostazionimagister_appellativo_predefinito'),
    ]

    operations = [
        migrations.AddField(
            model_name='impostazionimagister',
            name='creazione_automatica_trasferimento',
            field=models.BooleanField(default=True, help_text='Se selezionato, quando viene modificata la casa attuale di un religioso crea contestualmente un trasferimento in data attuale', verbose_name='Publius - creazione automatica trasferimento'),
        ),
        migrations.AddField(
            model_name='impostazionimagister',
            name='sovrascrivi_residenza',
            field=models.BooleanField(default=True, help_text='Se selezionato, quando non sono presenti indirizzo e luogo di residenza inserisce i corrispondenti dati presi da quelli della casa attuale', verbose_name='Publius - sovrascrivi residenza'),
        ),
    ]
