# Generated by Django 2.2.28 on 2024-08-26 09:17

import django.contrib.contenttypes.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('anagraficabase', '0063_santidelgiorno'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApplicazioneMagister',
            fields=[
            ],
            options={
                'verbose_name': 'Applicazione Magister',
                'verbose_name_plural': 'Applicazioni Magister',
                'ordering': ('app_label', 'model'),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contenttypes.contenttype',),
            managers=[
                ('objects', django.contrib.contenttypes.models.ContentTypeManager()),
            ],
        ),
    ]
