# Generated by Django 2.2.11 on 2021-03-25 12:41

from django.db import migrations, models
import django.db.models.deletion
import djmoney.models.fields
import mptt.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Cambio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_inizio', models.DateField(verbose_name='data inizio')),
                ('data_fine', models.DateField(blank=True, null=True, verbose_name='data fine')),
                ('valuta_origine', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta origine')),
                ('valuta_destinazione', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta destinazione')),
                ('valore_destinazione', models.DecimalField(decimal_places=6, max_digits=9, verbose_name='valore destinazione')),
            ],
            options={
                'verbose_name': 'Cambi Valuta',
                'verbose_name_plural': 'Cambio Valuta',
                'ordering': ('-data_inizio',),
            },
        ),
        migrations.CreateModel(
            name='CentroDiCosto',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, unique=True, verbose_name='nome')),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione')),
            ],
            options={
                'verbose_name': 'centro di costo',
                'verbose_name_plural': 'centri di costo',
                'ordering': ('nome', 'descrizione'),
            },
        ),
        migrations.CreateModel(
            name='Esercizio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, unique=True, verbose_name='nome')),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione')),
                ('data_inizio', models.DateField(blank=True, null=True, verbose_name='data inizio')),
                ('data_fine', models.DateField(blank=True, null=True, verbose_name='data fine')),
            ],
            options={
                'verbose_name': 'Esercizio',
                'verbose_name_plural': 'Esercizi',
                'ordering': ('nome', 'descrizione'),
            },
        ),
        migrations.CreateModel(
            name='Provincia',
            fields=[
                ('codice', models.CharField(max_length=2, primary_key=True, serialize=False)),
                ('descrizione', models.CharField(max_length=200)),
                ('regione', models.CharField(choices=[('ABRUZZO', 'Abruzzo'), ('BASILICATA', 'Basilicata'), ('CALABRIA', 'Calabria'), ('CAMPANIA', 'Campania'), ('EMILIA_ROMAGNA', 'Emilia Romagna'), ('FRIULI_VENEZIA_GIULIA', 'Friuli Venezia-Giulia'), ('LAZIO', 'Lazio'), ('LIGURIA', 'Liguria'), ('LOMBARDIA', 'Lombardia'), ('MARCHE', 'Marche'), ('MOLISE', 'Molise'), ('PIEMONTE', 'Piemonte'), ('PUGLIA', 'Puglia'), ('SARDEGNA', 'Sardegna'), ('SICILIA', 'Sicilia'), ('TOSCANA', 'Toscana'), ('TRENTINO_ALTO_ADIGE', 'Trentino-Alto Adige'), ('UMBRIA', 'Umbria'), ('VALLE_DAOSTA', "Valle D'Aosta"), ('VENETO', 'Veneto')], max_length=200)),
                ('area', models.CharField(choices=[('NORD_OVEST', 'Nord-Ovest'), ('NORD_EST', 'Nord-Est'), ('CENTRO', 'Centro'), ('SUD', 'Sud'), ('ISOLE', 'Isole')], max_length=200)),
            ],
            options={
                'verbose_name_plural': 'Province',
                'ordering': ('codice',),
            },
        ),
        migrations.CreateModel(
            name='Stato',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('descrizione', models.CharField(max_length=200)),
                ('descrizione_inglese', models.CharField(max_length=200)),
                ('area', models.CharField(choices=[('11', 'Unione europea'), ('12', 'Europa centro orientale'), ('13', 'Altri paesi europei'), ('21', 'Africa settentrionale'), ('22', 'Africa occidentale'), ('23', 'Africa orientale'), ('24', 'Africa centro meridionale'), ('31', 'Asia occidentale'), ('32', 'Asia centro meridionale'), ('33', 'Asia orientale'), ('41', 'America settentrionale'), ('42', 'America centro meridionale'), ('50', 'Oceania'), ('60', 'Apolidi')], max_length=200)),
                ('continente', models.CharField(choices=[('1', 'Europa'), ('2', 'Africa'), ('3', 'Asia'), ('4', 'America'), ('5', 'Oceania'), ('6', 'Apolidi')], max_length=200)),
                ('codice', models.CharField(blank=True, max_length=2, null=True)),
            ],
            options={
                'verbose_name_plural': 'Stati',
                'ordering': ('descrizione', 'descrizione_inglese'),
            },
        ),
        migrations.CreateModel(
            name='TipoArea',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, unique=True, verbose_name='nome')),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione')),
            ],
            options={
                'verbose_name': 'Tipo Area',
                'verbose_name_plural': 'Tipi Area',
                'ordering': ('nome', 'descrizione'),
            },
        ),
        migrations.CreateModel(
            name='PianoDeiConti',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codice', models.CharField(max_length=200, unique=True, verbose_name='codice')),
                ('descrizione', models.CharField(max_length=200, verbose_name='descrizione')),
                ('tipologia', models.CharField(blank=True, choices=[('patrimoniale', 'PATRIMONIALE'), ('liquidita', "LIQUIDITA'"), ('economico', 'ECONOMICO'), ('costi', 'COSTO'), ('ricavi', 'RICAVO'), ('ordine', "D'ORDINE")], max_length=200, null=True, verbose_name='natura conto')),
                ('saldo_iniziale_dare_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('saldo_iniziale_dare', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='saldo iniziale (dare)')),
                ('conto_imputabile', models.BooleanField(default=False, verbose_name='conto imputabile')),
                ('conto_multivaluta', models.BooleanField(default=False, verbose_name='conto multivaluta')),
                ('valuta_default', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default')),
                ('saldo_iniziale_avere_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('saldo_iniziale_avere', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='saldo iniziale (avere)')),
                ('preventivo_dare_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('preventivo_dare', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='preventivo (dare)')),
                ('preventivo_avere_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('preventivo_avere', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='preventivo (avere)')),
                ('livello', models.CharField(blank=True, choices=[('gruppo', 'gruppo'), ('mastro', 'mastro'), ('conto', 'conto'), ('sottoconto', 'sottoconto')], max_length=200, null=True, verbose_name='livello')),
                ('impostazione_conto', models.CharField(blank=True, choices=[('cassa', 'Cassa'), ('banca', 'Banca')], max_length=200, null=True, unique=True, verbose_name='impostazione conto')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent', mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='anagraficabase.PianoDeiConti', verbose_name='padre')),
            ],
            options={
                'verbose_name': 'Piano dei Conti',
                'verbose_name_plural': 'Piani dei Conti',
                'ordering': ('codice', 'descrizione'),
            },
        ),
        migrations.CreateModel(
            name='Area',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, unique=True, verbose_name='nome')),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True, verbose_name='descrizione')),
                ('valuta_default', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent', mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='anagraficabase.Area', verbose_name='padre')),
                ('tipo_area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.TipoArea', verbose_name='tipo area')),
            ],
            options={
                'verbose_name': 'Area',
                'verbose_name_plural': 'Aree',
                'ordering': ('nome', 'descrizione'),
            },
        ),
    ]
