# Generated by Django 2.2.28 on 2023-11-08 10:20

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0036_remove_provincia_codice_provincia'),
    ]

    operations = [
        migrations.CreateModel(
            name='PianoDeiContiImputabili',
            fields=[
            ],
            options={
                'verbose_name': 'Conto',
                'verbose_name_plural': '<PERSON>ti',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagraficabase.pianodeiconti',),
        ),
        migrations.AlterModelOptions(
            name='pianodeiconti',
            options={'ordering': ('codice', 'descrizione'), 'verbose_name': 'Conto', 'verbose_name_plural': '<PERSON>ti'},
        ),
        migrations.AlterModelOptions(
            name='pianodeiconticosti',
            options={'verbose_name': 'Conto - COSTO', 'verbose_name_plural': 'Conti - COSTI'},
        ),
        migrations.AlterModelOptions(
            name='pianodeicontiliquidita',
            options={'verbose_name': "Conto - LIQUIDITA'", 'verbose_name_plural': "Conti - LIQUIDITA'"},
        ),
        migrations.AlterModelOptions(
            name='pianodeicontipatrimoniale',
            options={'verbose_name': 'Conto - PATRIMONIALE', 'verbose_name_plural': 'Conti - PATRIMONIALI'},
        ),
        migrations.AlterModelOptions(
            name='pianodeicontiricavi',
            options={'verbose_name': 'Conto - RICAVO', 'verbose_name_plural': 'Conti - RICAVI'},
        ),
    ]
