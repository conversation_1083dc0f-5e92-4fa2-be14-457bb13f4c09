# Generated by Django 2.2.28 on 2023-09-12 12:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0025_pianodeiconticosti_pianodeicontiliquidita_pianodeicontipatrimoniale_pianodeicontiricavi'),
    ]

    operations = [
        migrations.CreateModel(
            name='StatisticheGrafici',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grafico', models.CharField(choices=[('matthaeus_conto_economico', 'Matthaeus - Conto Economico'), ('matthaeus_riepilogo_costi', '<PERSON><PERSON>eus - Riepilogo Costi'), ('matthaeus_riepilogo_ricavi', 'Matthaeus - Riepilogo Ricavi'), ('matthaeus_budget', 'Matthaeus - Budget'), ('matthaeus_liquidita', '<PERSON>haeus - Liquidita')], max_length=200)),
                ('html_grafico', models.TextField()),
                ('data_ultimo_aggiornamento', models.DateTimeField()),
                ('area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area')),
                ('esercizio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Esercizio', verbose_name='esercizio')),
            ],
            options={
                'verbose_name': 'Statistica Grafico',
                'verbose_name_plural': 'Statistiche Grafici',
                'ordering': ('area', 'esercizio', 'grafico', '-data_ultimo_aggiornamento'),
            },
        ),
    ]
