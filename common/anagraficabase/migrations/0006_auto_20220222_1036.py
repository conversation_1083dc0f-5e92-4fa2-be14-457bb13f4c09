# Generated by Django 2.2.11 on 2022-02-22 10:36

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0005_auto_20211221_1202'),
    ]

    operations = [
        migrations.AlterField(
            model_name='area',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_destinazione',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta destinazione'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_origine',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta origine'),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
    ]
