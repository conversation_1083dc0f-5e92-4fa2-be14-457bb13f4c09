# Generated by Django 2.2.28 on 2023-12-22 13:22

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0043_auto_20231205_1227'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('ARS', 'ARS'), ('CHF', 'CHF'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('BRL', 'R$'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
    ]
