# Generated by Django 2.2.28 on 2023-11-16 12:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0038_auto_20231108_1031'),
    ]

    operations = [
        migrations.AddField(
            model_name='impostazionimagister',
            name='numero_cifre_conto',
            field=models.PositiveIntegerField(default=2, verbose_name='Matthaeus - numero di cifre standard per il numero dei conti'),
        ),
        migrations.AddField(
            model_name='impostazionimagister',
            name='numero_cifre_mastro',
            field=models.PositiveIntegerField(default=1, verbose_name='Matthaeus - numero di cifre standard per il numero dei mastri'),
        ),
        migrations.AddField(
            model_name='impostazionimagister',
            name='numero_cifre_sottoconto',
            field=models.PositiveIntegerField(default=4, verbose_name='Matthaeus - numero di cifre standard per il numero dei sottoconti'),
        ),
        migrations.AddField(
            model_name='impostazionimagister',
            name='separatore_codice_conti',
            field=models.Char<PERSON>ield(default='.', max_length=5, verbose_name='Matthaeus - separatore per il codice del piano dei conti'),
        ),
    ]
