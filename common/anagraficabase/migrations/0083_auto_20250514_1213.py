# Generated by Django 2.2.28 on 2025-05-14 12:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafica', '0018_auto_20250326_1102'),
        ('anagraficabase', '0082_auto_20250403_1611'),
    ]

    operations = [
        migrations.AddField(
            model_name='area',
            name='anagrafica_personalizzata',
            field=models.BooleanField(default=False, verbose_name='Anagrafica personalizzata'),
        ),
        migrations.CreateModel(
            name='AbilitazioneAnagrafica',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('anagrafica', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagrafica.Anagrafica', verbose_name='anagrafica')),
                ('area_abilitata', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area abilitata')),
            ],
            options={
                'verbose_name': 'abilitazione piano dei conti',
                'verbose_name_plural': 'abilitazioni piani dei conti',
                'ordering': ('anagrafica', 'area_abilitata'),
                'unique_together': {('anagrafica', 'area_abilitata')},
            },
        ),
    ]
