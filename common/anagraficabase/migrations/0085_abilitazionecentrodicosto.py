# Generated by Django 2.2.28 on 2025-05-20 10:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0084_delete_abilitazioneanagrafica'),
    ]

    operations = [
        migrations.CreateModel(
            name='AbilitazioneCentroDiCosto',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area_abilitata', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area abilitata')),
                ('centro_di_costo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.CentroDiCosto', verbose_name='centro di costo')),
            ],
            options={
                'verbose_name': 'abilitazione centro di costo',
                'verbose_name_plural': 'abilitazioni centri di costo',
                'ordering': ('centro_di_costo', 'area_abilitata'),
                'unique_together': {('centro_di_costo', 'area_abilitata')},
            },
        ),
    ]
