# Generated by Django 2.2.11 on 2021-12-21 12:02

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0004_auto_20210816_1018'),
    ]

    operations = [
        migrations.AlterField(
            model_name='area',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_destinazione',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta destinazione'),
        ),
        migrations.AlterField(
            model_name='cambio',
            name='valuta_origine',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta origine'),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='preventivo_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_avere_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='saldo_iniziale_dare_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='pianodeiconti',
            name='valuta_default',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('CHF', 'CHF'), ('EUR', '€')], default='EUR', max_length=3, verbose_name='valuta default'),
        ),
    ]
