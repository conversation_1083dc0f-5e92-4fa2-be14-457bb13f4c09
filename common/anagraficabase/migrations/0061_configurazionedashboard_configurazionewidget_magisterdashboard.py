# Generated by Django 2.2.28 on 2024-08-07 15:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagraficabase', '0060_auto_20240611_1231'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConfigurazioneWidget',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, verbose_name='nome')),
                ('descrizione', models.TextField(blank=True, null=True, verbose_name='descrizione')),
                ('nome_widget_1', models.Char<PERSON>ield(max_length=200, verbose_name='nome widget 1')),
                ('nome_widget_2', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='nome widget 2')),
                ('nome_widget_3', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='nome widget 3')),
                ('nome_widget_4', models.CharField(blank=True, max_length=200, null=True, verbose_name='nome widget 4')),
            ],
            options={
                'verbose_name': 'Configurazione Widget',
                'verbose_name_plural': 'Configurazioni Widget',
                'ordering': ('nome',),
            },
        ),
        migrations.CreateModel(
            name='MagisterDashboard',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200, verbose_name='nome')),
                ('descrizione', models.TextField(blank=True, null=True, verbose_name='descrizione')),
                ('applicazione', models.CharField(choices=[('matthaeus', 'Matthaeus'), ('publius', 'Publius'), ('saulo', 'Saulo'), ('martinus', 'Martinus'), ('cyrenaeus', 'Cyrenaeus')], max_length=200, verbose_name='applicazione')),
                ('nome_dashboard', models.CharField(choices=[('matthaeus_grafici', 'Matthaeus - Dashboard Grafici'), ('publius_statistiche_demografiche', 'Publius - Statistiche Demografiche'), ('publius_homepage', 'Publius - Home page')], max_length=200, unique=True, verbose_name='nome dashboard')),
            ],
            options={
                'verbose_name': 'Magister Dashboard',
                'verbose_name_plural': 'Magister Dashboards',
                'ordering': ('applicazione', 'nome'),
            },
        ),
        migrations.CreateModel(
            name='ConfigurazioneDashboard',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordinamento', models.PositiveIntegerField(default=1, verbose_name='ordinamento')),
                ('attivo', models.BooleanField(default=True, verbose_name='attivo')),
                ('dimensione', models.CharField(choices=[(1, 'piccolo'), (2, 'medio'), (3, 'grande'), (4, 'extra large'), (6, '100%')], default=1, max_length=200, verbose_name='dimensione')),
                ('magister_dashboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.MagisterDashboard', verbose_name='dashboard')),
                ('widget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.ConfigurazioneWidget', verbose_name='widget')),
            ],
            options={
                'verbose_name': 'Configurazione Dashboard',
                'verbose_name_plural': 'Configurazioni Dashboard',
                'ordering': ('magister_dashboard', 'ordinamento', 'attivo'),
            },
        ),
    ]
