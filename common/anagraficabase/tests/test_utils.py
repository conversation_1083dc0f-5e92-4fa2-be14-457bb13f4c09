from django.utils import timezone

from django.test import TestCase
from django.core.exceptions import ValidationError

from . import factories
from djmoney.money import Money
from common.anagraficabase import utils


class ChiusuraEsercizioTest(TestCase):

    def test_verifica_apertura(self):
        esercizio_corrente = factories.EsercizioFactory()
        area_corrente = factories.AreaFactory()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        altro_esercizio = factories.EsercizioFactory()
        esercizio_corrente.esercizio_successivo = altro_esercizio
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        conto_1 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_stato_patrimoniale_iniziale = conto_1
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        conto_2 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_risultati_esercizi_precedenti = conto_2
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        esercizio_corrente.data_operazione_apertura = timezone.now().date()
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        esercizio_corrente.descrizione_operazione_apertura = 'operazione apertura'
        esercizio_corrente.save()
        self.assertTrue(esercizio_corrente.verifica_preparazione_apertura(area_corrente))

    def test_verifica_apertura_conti_abilitati(self):
        esercizio_corrente = factories.EsercizioFactory()
        area_corrente = factories.AreaFactory(piano_dei_conti_personalizzato=True)
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        altro_esercizio = factories.EsercizioFactory()
        esercizio_corrente.esercizio_successivo = altro_esercizio
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        conto_1 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_stato_patrimoniale_iniziale = conto_1
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        conto_2 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_risultati_esercizi_precedenti = conto_2
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        esercizio_corrente.data_operazione_apertura = timezone.now().date()
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        esercizio_corrente.descrizione_operazione_apertura = 'operazione apertura'
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        factories.AbilitazionePianoDeiContiFactory(piano_dei_conti=conto_1, area_abilitata=area_corrente)
        factories.AbilitazionePianoDeiContiFactory(piano_dei_conti=conto_2, area_abilitata=area_corrente)
        self.assertTrue(esercizio_corrente.verifica_preparazione_apertura(area_corrente))

    def test_verifica_chiusura(self):
        esercizio_corrente = factories.EsercizioFactory()
        area_corrente = factories.AreaFactory()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_1 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_profitti_perdite = conto_1
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_2 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_risultato_esercizio = conto_2
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_3 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_stato_patrimoniale_finale = conto_3
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        esercizio_corrente.data_operazione_chiusura = timezone.now().date()
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        esercizio_corrente.descrizione_operazione_chiusura = 'operazione chiusura'
        esercizio_corrente.save()
        self.assertTrue(esercizio_corrente.verifica_preparazione_chiusura(area_corrente))

    def test_verifica_chiusura_conti_abilitati(self):
        esercizio_corrente = factories.EsercizioFactory()
        area_corrente = factories.AreaFactory(piano_dei_conti_personalizzato=True)
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_1 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_profitti_perdite = conto_1
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_2 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_risultato_esercizio = conto_2
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        conto_3 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_stato_patrimoniale_finale = conto_3
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        esercizio_corrente.data_operazione_chiusura = timezone.now().date()
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_chiusura, area_corrente)
        esercizio_corrente.descrizione_operazione_chiusura = 'operazione chiusura'
        esercizio_corrente.save()
        self.assertRaises(ValidationError, esercizio_corrente.verifica_preparazione_apertura, area_corrente)
        factories.AbilitazionePianoDeiContiFactory(piano_dei_conti=conto_1, area_abilitata=area_corrente)
        factories.AbilitazionePianoDeiContiFactory(piano_dei_conti=conto_2, area_abilitata=area_corrente)
        factories.AbilitazionePianoDeiContiFactory(piano_dei_conti=conto_3, area_abilitata=area_corrente)
        self.assertTrue(esercizio_corrente.verifica_preparazione_chiusura(area_corrente))

    def test_operazioni_chiusura(self):
        esercizio_corrente = factories.EsercizioFactory()
        area_corrente = factories.AreaFactory()
        self.assertEqual(esercizio_corrente.chiuso, False)
        self.assertRaises(ValidationError, utils.operazioni_chiusura_esercizio, esercizio_corrente, area_corrente)
        self.assertEqual(esercizio_corrente.chiuso, False)
        # IMPOSTAZIONI CHIUSURA
        conto_1 = factories.PianoDeiContiFactory()
        conto_2 = factories.PianoDeiContiFactory()
        conto_3 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_profitti_perdite = conto_1
        esercizio_corrente.conto_risultato_esercizio = conto_2
        esercizio_corrente.conto_stato_patrimoniale_finale = conto_3
        esercizio_corrente.data_operazione_chiusura = timezone.now().date()
        esercizio_corrente.descrizione_operazione_chiusura = 'operazione chiusura'
        # IMPOSTAZIONI APERTURA
        altro_esercizio = factories.EsercizioFactory()
        esercizio_corrente.esercizio_successivo = altro_esercizio
        conto_4 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_stato_patrimoniale_iniziale = conto_4
        conto_5 = factories.PianoDeiContiFactory()
        esercizio_corrente.conto_risultati_esercizi_precedenti = conto_5
        esercizio_corrente.data_operazione_apertura = timezone.now().date()
        esercizio_corrente.descrizione_operazione_apertura = 'operazione apertura'
        esercizio_corrente.save()
        self.assertEqual(esercizio_corrente.chiuso, False)
        utils.operazioni_chiusura_esercizio(esercizio_corrente, area_corrente)
        # per ora la chiusura automatica e' disabilitata
        self.assertEqual(esercizio_corrente.chiuso, False)
