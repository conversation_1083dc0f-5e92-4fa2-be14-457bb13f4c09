from decimal import Decimal
from django.utils import timezone
import factory
from factory import SubFactory
from factory.django import DjangoModelFactory
from .. import models
from djmoney.contrib.exchange.models import Rate


class AreaFactory(DjangoModelFactory):
    class Meta:
        model = models.Area

    nome = factory.Faker('country')
    descrizione = factory.Sequence(lambda n: 'descrizione area {0}'.format(n))


class EsercizioFactory(DjangoModelFactory):
    class Meta:
        model = models.Esercizio

    nome = factory.Sequence(lambda n: 'esercizio {0}'.format(n))
    descrizione = factory.Sequence(lambda n: 'descrizione esercizio {0}'.format(n))
    data_inizio = timezone.now().date()
    data_fine = timezone.now().date()


class CentroDiCostoFactory(DjangoModelFactory):
    class Meta:
        model = models.CentroDiCosto

    nome = factory.Sequence(lambda n: 'centro di costo {0}'.format(n))
    descrizione = factory.Sequence(lambda n: 'descrizione centro di costo {0}'.format(n))


class PianoDeiContiFactory(DjangoModelFactory):
    class Meta:
        model = models.PianoDeiConti

    codice_gruppo = factory.Sequence(lambda n: '{0}'.format(n))
    codice_mastro = factory.Sequence(lambda n: '{0}'.format(n))
    codice_conto = factory.Sequence(lambda n: '{0}'.format(n))
    codice_sottoconto = factory.Sequence(lambda n: '{0}'.format(n))
    descrizione = factory.Sequence(lambda n: 'piano dei conti n. {0}'.format(n))
    livello = 'sottoconto'
    tipologia = 'economico'
    conto_imputabile = True
    gestione = 'finanziaria'


class AbilitazionePianoDeiContiFactory(DjangoModelFactory):
    class Meta:
        model = models.AbilitazionePianoDeiConti

    piano_dei_conti = SubFactory(PianoDeiContiFactory)
    area_abilitata = SubFactory(AreaFactory)


class StatoFactory(DjangoModelFactory):
    class Meta:
        model = models.Stato

    descrizione = factory.Faker('country')


class ProvinciaFactory(DjangoModelFactory):
    class Meta:
        model = models.Provincia

    codice = factory.Sequence(lambda n: '%s' % n)
    descrizione = factory.Faker('city')
    nazione = factory.SubFactory(StatoFactory)


class TipoAreaFactory(DjangoModelFactory):
    class Meta:
        model = models.TipoArea

    nome = factory.Sequence(lambda n: 'area %s' % n)
    descrizione = factory.Sequence(lambda n: 'tipo area %s' % n)


class CambioStoricoFactory(DjangoModelFactory):
    class Meta:
        model = models.CambioStorico

    name = 'cambio al {0}'.format(timezone.now())
    base_currency = 'USD'
    data_inizio = timezone.now().date()
    

class RateFactory(DjangoModelFactory):
    class Meta:
        model = Rate

    value = Decimal('1.1')
    currency = 'EUR'
    backend = factory.SubFactory(CambioStoricoFactory)
