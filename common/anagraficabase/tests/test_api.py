import base64 

from django.urls import reverse
from django.test import TestCase

from rest_framework.test import APIClient
from rest_framework.test import APIRequestFactory

from common.authentication.tests.factories import UtenteMagisterFactory
from . import factories


# class EsercizioAdminTest(TestCase):
#     def setUp(self):
#         self.user = UtenteMagisterFactory(username='test')
#         self.api_client = APIClient()

#         credentials = f"test:pass"
#         self.baseAuthHeader = base64.b64encode(
#             credentials.encode('utf-8')
#         ).decode('utf-8')
#         self.baseAuthHeader = f"Basic {self.baseAuthHeader}"

#     def test_list_ko_nologin(self):
#         url_list = reverse('esercizio-list')
#         response = self.api_client.get(url_list)
#         self.assertEqual(response.status_code, 401)

#     def test_create_ko_notallowed(self):
#         url_list = reverse('esercizio-list')
#         response = self.api_client.post(url_list)
#         self.assertEqual(response.status_code, 401)

#     def test_list_ok(self):
#         esercizio_corrente = factories.EsercizioFactory(nome='Esercizio corrente')
#         factories.EsercizioFactory()
#         self.user.esercizio_corrente = esercizio_corrente
#         self.user.save()
#         url_list = reverse('esercizio-list')
#         response = self.api_client.get(url_list, HTTP_AUTHORIZATION=self.baseAuthHeader)
#         self.assertEqual(response.status_code, 200)
#         esercizi = response.json()
#         self.assertEqual(len(esercizi), 3)
#         self.assertTrue('id' in esercizi[0].keys())
#         for esercizio in esercizi:
#             if esercizio['nome'] == 'Esercizio corrente':
#                 self.assertTrue(esercizio['esercizio_corrente'])
#             else:
#                 self.assertFalse(esercizio['esercizio_corrente'])
