from django.urls import reverse
from django.test import TestCase
from django import setup
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')
setup()

from common.authentication.tests.factories import UtenteMagisterFactory
from . import factories


class SiteAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_app_index(self):
        url = reverse('admin:index')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class AppAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_app_index(self):
        url = reverse('admin:app_list', args=('anagraficabase',))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class AreaAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:anagraficabase_area_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:anagraficabase_area_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.AreaFactory()
        url = reverse('admin:anagraficabase_area_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.AreaFactory()
        url = reverse('admin:anagraficabase_area_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class EsercizioAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:anagraficabase_esercizio_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:anagraficabase_esercizio_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.EsercizioFactory()
        url = reverse('matthaeus:anagraficabase_esercizio_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.EsercizioFactory()
        url = reverse('matthaeus:anagraficabase_esercizio_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class PianoDeiContiAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:anagraficabase_pianodeiconti_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:anagraficabase_pianodeiconti_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.PianoDeiContiFactory()
        url = reverse('matthaeus:anagraficabase_pianodeiconti_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.PianoDeiContiFactory()
        url = reverse('matthaeus:anagraficabase_pianodeiconti_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class CentroDiCostoAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:anagraficabase_centrodicosto_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:anagraficabase_centrodicosto_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.CentroDiCostoFactory()
        url = reverse('matthaeus:anagraficabase_centrodicosto_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.CentroDiCostoFactory()
        url = reverse('matthaeus:anagraficabase_centrodicosto_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class StatoAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('cyrenaeus:anagraficabase_stato_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('cyrenaeus:anagraficabase_stato_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.StatoFactory()
        url = reverse('cyrenaeus:anagraficabase_stato_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.StatoFactory()
        url = reverse('cyrenaeus:anagraficabase_stato_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class ProvinciaAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('cyrenaeus:anagraficabase_provincia_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('cyrenaeus:anagraficabase_provincia_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.ProvinciaFactory()
        url = reverse('cyrenaeus:anagraficabase_provincia_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.ProvinciaFactory()
        url = reverse('cyrenaeus:anagraficabase_provincia_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TipoAreaAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('cyrenaeus:anagraficabase_tipoarea_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('cyrenaeus:anagraficabase_tipoarea_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.TipoAreaFactory()
        url = reverse('cyrenaeus:anagraficabase_tipoarea_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.TipoAreaFactory()
        url = reverse('cyrenaeus:anagraficabase_tipoarea_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class AbilitazionePianoDeiContiAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:anagraficabase_abilitazionepianodeiconti_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:anagraficabase_abilitazionepianodeiconti_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.AbilitazionePianoDeiContiFactory()
        url = reverse('matthaeus:anagraficabase_abilitazionepianodeiconti_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_delete(self):
        obj = factories.AbilitazionePianoDeiContiFactory()
        url = reverse('matthaeus:anagraficabase_abilitazionepianodeiconti_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
