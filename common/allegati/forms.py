from django import forms
from common.allegati.models import DocumentoAllegato, ImmagineAllegata


class ImmagineAllegataForm(forms.ModelForm):
    get_content_link = forms.CharField(required=False)
    get_link = forms.CharField(required=False)

    class Meta:
        model = ImmagineAllegata
        fields = '__all__'


class DocumentoAllegatoForm(forms.ModelForm):
    get_content_link = forms.CharField(required=False)
    get_link = forms.CharField(required=False)

    class Meta:
        model = DocumentoAllegato
        fields = '__all__'


class DocumentoAllegatoReadonlyInlineForm(forms.ModelForm):
    get_link = forms.CharField(required=False)

    class Meta:
        model = DocumentoAllegato
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super(DocumentoAllegatoReadonlyInlineForm, self).__init__(*args, **kwargs)
