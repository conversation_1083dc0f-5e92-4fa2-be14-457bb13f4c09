# Generated by Django 2.2.11 on 2021-04-08 11:53

import common.allegati.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('allegati', '0002_auto_20210326_1010'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImmagineAllegata',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(null=True)),
                ('file_immagine', models.ImageField(upload_to=common.allegati.models.allegato_upload, verbose_name='file immagine')),
                ('data_creazione', models.DateTimeField(blank=True, null=True, verbose_name='data creazione')),
                ('data_modifica', models.DateTimeField(blank=True, null=True, verbose_name='data modifica')),
                ('descrizione', models.CharField(blank=True, max_length=200, verbose_name='descrizione')),
                ('content_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.ContentType')),
                ('proprietario', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='proprietari_immagini', to=settings.AUTH_USER_MODEL, verbose_name='proprietario')),
            ],
            options={
                'verbose_name_plural': 'immagini allegate',
                'ordering': ['-data_creazione', '-data_modifica'],
                'permissions': (('delete_foreign_attachments', 'Può eliminare allegati di altri'),),
            },
        ),
    ]
