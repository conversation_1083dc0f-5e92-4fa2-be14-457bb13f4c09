# Generated by Django 2.2.28 on 2024-10-15 15:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('archivio', '0018_statoconservazione'),
        ('allegati', '0011_documentoallegato_applicativo'),
    ]

    operations = [
        migrations.AddField(
            model_name='documentoallegato',
            name='fondo',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='fondo'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='note',
            field=models.TextField(blank=True, verbose_name='note'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='posizione',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='posizione'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='serie',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='serie'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='sintesi',
            field=models.TextField(blank=True, verbose_name='sintesi'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='sottoserie',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='sottoserie'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='stato_di_conservazione',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='archivio.StatoConservazione', verbose_name='stato di conservazione'),
        ),
        migrations.AddField(
            model_name='documentoallegato',
            name='subfondo',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='sub-fondo'),
        ),
    ]
