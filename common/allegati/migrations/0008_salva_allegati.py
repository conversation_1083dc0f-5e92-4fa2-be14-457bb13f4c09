# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2018-10-12 08:05
from __future__ import unicode_literals

from django.db import migrations


def salva_allegati(apps, schema_editor):
    DocumentoAllegato = apps.get_model('allegati', 'DocumentoAllegato')
    for allegato in DocumentoAllegato.objects.all():
        allegato.save()


class Migration(migrations.Migration):

    dependencies = [
        ('allegati', '0007_documentoallegato_descrizione_riferimento'),
    ]

    operations = [
        migrations.RunPython(salva_allegati)
    ]
