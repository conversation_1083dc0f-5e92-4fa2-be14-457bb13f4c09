#!/usr/bin/env python
# -*- coding: utf-8 -*-
from django.shortcuts import render_to_response, get_object_or_404
from django.views.decorators.http import require_POST
from django.http import HttpResponseRedirect
from django.apps import apps
from django.core.urlresolvers import reverse
from django.utils.translation import ugettext
from django.template.context import RequestContext
from django.contrib.auth.decorators import login_required

from common.allegati.models import DocumentoAllegato
from common.allegati.forms import DocumentoAllegatoForm


def add_url_for_obj(obj):
    return reverse(
        'add_documentoallegato',
        kwargs={
            'app_label': obj._meta.app_label,
            'module_name': obj._meta.module_name,
            'pk': obj.pk
        }
    )


@require_POST
@login_required
def add_documentoallegato(request, app_label, module_name, pk, template_name='allegati/add.html', extra_context={}):
    next = request.POST.get('next', '/')
    model = apps.get_model(app_label, module_name)
    if model is None:
        return HttpResponseRedirect(next)
    obj = get_object_or_404(model, pk=pk)
    form = DocumentoAllegatoForm(request.POST, request.FILES)
    if form.is_valid():
        form.save(request, obj)
        request.user.message_set.create(message=ugettext('Il documento allegato è stato caricato.'))
        return HttpResponseRedirect(next)
    else:
        template_context = {
            'form': form,
            'form_url': add_url_for_obj(obj),
            'next': next,
        }
        template_context.update(extra_context)
        return render_to_response(template_name, template_context, RequestContext(request))


@login_required
def delete_documentoallegato(request, documentoallegato_pk):
    allegato = get_object_or_404(DocumentoAllegato, pk=documentoallegato_pk)
    if request.user.has_perm('delete_foreign_attachments') or request.user == allegato.proprietario:
        allegato.delete()
        request.user.message_set.create(message=ugettext('Il documento allegato è stato eliminato.'))
    next = request.REQUEST.get('next') or '/'
    return HttpResponseRedirect(next)
