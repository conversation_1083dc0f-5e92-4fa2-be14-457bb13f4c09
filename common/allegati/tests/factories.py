import factory
from factory.django import DjangoModelFactory
from factory.django import FileField
from unittest.mock import MagicMock
from django.core.files import File

from .. import models

# FILE_MOCK = MagicMock(spec=File, name='FileMock')
# FILE_MOCK.name = 'test1.jpg'


class DocumentoAllegatoFactory(DjangoModelFactory):
    class Meta:
        model = models.DocumentoAllegato

    # file_allegato = FileField(from_file=FILE_MOCK)
    descrizione = factory.Sequence(lambda n: 'descrizione file %s' % n)


class ImmagineAllegataFactory(DjangoModelFactory):
    class Meta:
        model = models.ImmagineAllegata

    descrizione = factory.Sequence(lambda n: 'descrizione immagine %s' % n)
