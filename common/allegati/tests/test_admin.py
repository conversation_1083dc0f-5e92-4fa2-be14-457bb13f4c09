from django.urls import reverse
from django.test import TestCase
from django import setup
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')
setup()

from . import factories

from common.authentication.tests.factories import UtenteMagisterFactory


class DocumentoAllegatoAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('cyrenaeus:allegati_documentoallegato_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('cyrenaeus:allegati_documentoallegato_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        allegato = factories.DocumentoAllegatoFactory()
        url = reverse('cyrenaeus:allegati_documentoallegato_change', args=(allegato.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_delete(self):
        allegato = factories.DocumentoAllegatoFactory()
        url = reverse('cyrenaeus:allegati_documentoallegato_delete', args=(allegato.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)


# class ImmagineAllegataAdminTest(TestCase):
#     def setUp(self):
#         UtenteMagisterFactory(username='test')
#         self.assertTrue(self.client.login(username='test', password='pass'))
#         self.list = reverse('admin:allegati_immagineallegata_changelist')

#     def test_list(self):
#         response = self.client.get(self.list)
#         self.assertEqual(response.status_code, 200)

#     def test_search(self):
#         data = dict(q='text')
#         response = self.client.get(self.list, data)
#         self.assertEqual(response.status_code, 200)

#     def test_add(self):
#         url = reverse('admin:allegati_immagineallegata_add')
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, 200)

#     def test_detail(self):
#         allegato = factories.ImmagineAllegataFactory()
#         url = reverse('admin:allegati_immagineallegata_change', args=(allegato.pk,))
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, 200)

#     def test_delete(self):
#         allegato = factories.ImmagineAllegataFactory()
#         url = reverse('admin:allegati_immagineallegata_delete', args=(allegato.pk,))
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, 200)
