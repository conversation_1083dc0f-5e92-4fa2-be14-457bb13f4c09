from django.utils.translation import ugettext_lazy as _

from import_export import resources, fields

from common.allegati.models import DocumentoAllegato


class DocumentoAllegatoResource(resources.ModelResource):
    riferimento = fields.Field(column_name=_('riferimento'))
    nome_categoria = fields.Field(column_name=_('categoria'))
    nome_proprietario = fields.Field(column_name=_('proprietario'))
    nome_file_allegato = fields.Field(column_name=_('nome file'))

    def dehydrate_riferimento(self, documentoallegato):
        if documentoallegato.descrizione_riferimento:
            return u'%s' % documentoallegato.descrizione_riferimento
        return u''

    def dehydrate_nome_categoria(self, documentoallegato):
        if documentoallegato.categoria:
            return u'%s' % documentoallegato.categoria
        return u''

    def dehydrate_nome_proprietario(self, documentoallegato):
        if documentoallegato.proprietario:
            return u'%s' % documentoallegato.proprietario
        return u''

    def dehydrate_nome_file_allegato(self, documentoallegato):
        return u'%s' % documentoallegato.filename

    class Meta:
        model = DocumentoAllegato
        fields = (
            'descrizione', 'riferimento', 'nome_categoria',
            'nome_proprietario', 'data_creazione', 'data_modifica', 'nome_file_allegato'
        )
        export_order = (
            'descrizione', 'riferimento', 'nome_categoria',
            'nome_proprietario', 'data_creazione', 'data_modifica', 'nome_file_allegato'
        )
        widgets = {
            'data_creazione': {'format': '%d/%m/%Y - %H:%M'},
            'data_modifica': {'format': '%d/%m/%Y - %H:%M'},
        }
