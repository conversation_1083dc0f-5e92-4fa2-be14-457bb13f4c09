#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import mimetypes

from django.utils import timezone
from django.db import models
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils.html import format_html
from django.utils.translation import ugettext_lazy as _
from django.urls import reverse

from common.anagraficabase.models import AreaAwareModel, EsercizioAwareModel
from common.anagraficabase.models import ELENCO_APPLICATIVI
from saulo.archivio.utils import crea_struttura_documento_saulo
from saulo.archivio.models import Struttura


class StatoConservazione(models.Model):
    codice = models.CharField(_('codice'), max_length=50, null=True, blank=True)
    descrizione = models.CharField(_('descrizione'), max_length=200)

    class Meta:
        verbose_name_plural = _('Stati di conservazione')
        ordering = ('codice', 'descrizione')
        app_label = 'archivio'

    def __str__(self):
        return '%s' % self.descrizione


class Categoria(models.Model):
    nome = models.CharField(_('nome'), max_length=200)
    descrizione = models.CharField(
        _('descrizione'), max_length=200, null=True, blank=True
    )

    class Meta:
        ordering = ['nome', ]
        verbose_name_plural = _('categorie')

    def __str__(self):
        return u'%s' % self.nome


def allegato_upload(instance, filename):
    """Stores the attachment in a "per module/appname/primary key" folder"""
    if instance.content_object:
        return os.path.join(
            'allegati',
            '{app}_{model}'.format(
                app=instance.content_object._meta.app_label,
                model=instance.content_object._meta.object_name.lower(),
            ),
            str(instance.content_object.pk),
            filename,
        )
    else:
        return os.path.join('allegati', 'documentoallegato', filename)


def immagine_upload(instance, filename):
    """Stores the attachment in a "per module/appname/primary key" folder"""
    if instance.content_object:
        return os.path.join(
            'immagine',
            '{app}_{model}'.format(
                app=instance.content_object._meta.app_label,
                model=instance.content_object._meta.object_name.lower(),
            ),
            str(instance.content_object.pk),
            filename,
        )
    else:
        return os.path.join('allegati', 'immagineallegata', filename)


class DocumentoAllegatoManager(models.Manager):
    def attachments_for_object(self, obj):
        object_type = ContentType.objects.get_for_model(obj)
        return self.filter(content_type__pk=object_type.id, object_id=obj.id)


class DocumentoAllegato(AreaAwareModel, EsercizioAwareModel):
    objects = DocumentoAllegatoManager()
    content_type = models.ForeignKey(ContentType, null=True, on_delete=models.SET_NULL)
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    proprietario = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name='proprietari_allegati',
        null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('proprietario')
    )
    file_allegato = models.FileField(upload_to=allegato_upload, verbose_name=_('file allegato'))
    data_creazione = models.DateTimeField(_('data creazione'), null=True, blank=True)
    data_modifica = models.DateTimeField(_('data modifica'), null=True, blank=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, blank=True)
    categoria = models.ForeignKey(
        Categoria, null=True, blank=True, on_delete=models.CASCADE,
        verbose_name=_('categoria'),
    )
    descrizione_riferimento = models.CharField(_('descrizione riferimento'), max_length=200, blank=True)
    applicativo = models.CharField(
        _('applicativo'), max_length=200, null=True, blank=True, choices=ELENCO_APPLICATIVI
    )
    struttura = models.ForeignKey(
        Struttura, null=True, blank=True, on_delete=models.CASCADE,
        verbose_name=_('struttura Saulo'),
    )
    fondo = models.CharField(_('fondo'), max_length=200, null=True, blank=True, )
    subfondo = models.CharField(_('sub-fondo'), max_length=200, null=True, blank=True, )
    serie = models.CharField(_('serie'), max_length=200, null=True, blank=True, )
    sottoserie = models.CharField(_('sottoserie'), max_length=200, null=True, blank=True, )
    posizione = models.CharField(_('posizione'), max_length=200, null=True, blank=True, )
    sintesi = models.TextField(_('sintesi'), blank=True)
    note = models.TextField(_('note'), blank=True)
    stato_di_conservazione = models.ForeignKey(
        StatoConservazione, on_delete=models.CASCADE, verbose_name=_('stato di conservazione'), null=True, blank=True
    )
    contenuto = models.TextField(_('contenuto'), null=True, blank=True)
    autore = models.CharField(_('autore'), max_length=200, null=True, blank=True, )
    data_pubblicazione = models.DateField(_('data di pubblicazione'), blank=True, null=True)

    class Meta:
        ordering = ['-data_creazione', '-data_modifica']
        permissions = (
            ('delete_foreign_attachments', 'Può eliminare allegati di altri'),
        )
        verbose_name_plural = _('documenti allegati')
        verbose_name = _('documento allegato')
        app_label = 'allegati'

    def __str__(self):
        if self.descrizione:
            return u'%s - %s' % (self.descrizione, self.filename)
        else:
            return self.filename

    @property
    def filename(self):
        return os.path.split(self.file_allegato.name)[1]

    def get_link(self):
        if self.file_allegato:
            url_file = self.file_allegato.url
            return format_html(
                '<a href="%s" target="_blank">%s</a>' % (url_file, self.filename)
            )
        return ''
    get_link.short_description = _('Download')
    get_link.admin_order_field = 'file_allegato'
    get_link.allow_tags = True

    def get_link_struttura(self):
        if self.struttura:
            return format_html(
                '<a href="/saulo/struttura_treeview/?struttura_selezionata=%s">%s</a>' % (self.struttura.id, self.struttura)
            )
        return ''
    get_link_struttura.short_description = _('Riferimento (struttura)')
    get_link_struttura.admin_order_field = 'struttura'
    get_link_struttura.allow_tags = True

    def get_content_link(self):
        if self.content_object:
            # url_file = self.content_object.get_url()
            # return format_html(
            #     '<a href="%s" target="">%s</a>' % (url_file, self.content_object)
            # )
            return '%s' % self.content_object
        return ''
    get_content_link.short_description = _('Riferimento')
    get_content_link.allow_tags = True

    def get_link_origine(self):
        if self.applicativo and self.content_object:
            url_file = self.content_object.get_url()
            url = reverse('%s:%s_%s_change' % (self.applicativo, self.content_type.app_label, self.content_type.model), args=(self.object_id,))
            return format_html('<a href="%s" target="_blank">%s</a>' % (url, self.content_object))  
    get_link_origine.short_description = _('Riferimento (origine)')
    get_link_origine.allow_tags = True

    def display_data_creazione(self):
        return self.data_creazione.strftime('%d/%m/%Y %H:%M')
    display_data_creazione.short_description = _('Data Creaz.')
    display_data_creazione.admin_order_field = 'data_creazione'

    def display_data_modifica(self):
        return self.data_modifica.strftime('%d/%m/%Y %H:%M')
    display_data_modifica.short_description = _('Data Modif.')
    display_data_modifica.admin_order_field = 'data_modifica'

    def get_url_saulo(self):
        url = reverse('saulo:allegati_documentoallegato_change', args=(self.id,))
        return url

    def save(self, *args, **kwargs):
        if not self.id:
            self.data_creazione = timezone.now()
        self.data_modifica = timezone.now()
        if self.content_object:
            self.descrizione_riferimento = u'%s' % self.content_object
            if hasattr(self.content_object, 'area'):
                self.area = getattr(self.content_object, 'area')
            else:
                if hasattr(self.content_object, '__area__'):
                    self.area = getattr(self.content_object, '__area__')
            if hasattr(self.content_object, 'esercizio'):
                self.esercizio = getattr(self.content_object, 'esercizio')
        else:
            self.descrizione_riferimento = u''
        struttura = crea_struttura_documento_saulo(self)
        if struttura:
            self.struttura = struttura
        return super(DocumentoAllegato, self).save(*args, **kwargs)

    def get_tipo_file(self):
        if self.file_allegato:
            tipo_file = mimetypes.guess_type(self.file_allegato.file.name)
            return tipo_file[0]
        else:
            return ''
    get_tipo_file.short_description = _('Tipo File')


class ImmagineAllegataManager(models.Manager):
    def attachments_for_object(self, obj):
        object_type = ContentType.objects.get_for_model(obj)
        return self.filter(content_type__pk=object_type.id, object_id=obj.id)


class ImmagineAllegata(models.Model):
    objects = ImmagineAllegataManager()
    content_type = models.ForeignKey(ContentType, null=True, on_delete=models.SET_NULL)
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    proprietario = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name='proprietari_immagini',
        null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('proprietario')
    )
    file_immagine = models.ImageField(upload_to=immagine_upload, verbose_name=_('file immagine'))
    data_creazione = models.DateTimeField(_('data creazione'), null=True, blank=True)
    data_modifica = models.DateTimeField(_('data modifica'), null=True, blank=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, blank=True)
    categoria = models.ForeignKey(
        Categoria, null=True, blank=True, on_delete=models.CASCADE,
        verbose_name=_('categoria'),
    )

    class Meta:
        ordering = ['-data_creazione', '-data_modifica']
        permissions = (
            ('delete_foreign_attachments', 'Può eliminare allegati di altri'),
        )
        verbose_name_plural = _('immagini allegate')
        app_label = 'allegati'

    def __str__(self):
        if self.descrizione:
            return u'%s' % self.descrizione
        else:
            return self.filename

    @property
    def filename(self):
        return os.path.split(self.file_immagine.name)[1]

    def get_link(self):
        if self.file_immagine:
            url_file = self.file_immagine.url
            return format_html(
                '<a href="%s" target="_blank">%s</a>' % (url_file, self.filename)
            )
        return ''
    get_link.short_description = _('Download')
    get_link.admin_order_field = 'file_immagine'
    get_link.allow_tags = True

    def get_content_link(self):
        if self.content_object:
            url_file = self.content_object.get_url()
            return format_html(
                '<a href="%s" target="">%s</a>' % (url_file, self.content_object)
            )
        return ''
    get_content_link.short_description = _('Riferimento')
    get_content_link.allow_tags = True

    def display_data_creazione(self):
        return self.data_creazione.strftime('%d/%m/%Y %H:%M')
    display_data_creazione.short_description = _('Data Creaz.')
    display_data_creazione.admin_order_field = 'data_creazione'

    def display_data_modifica(self):
        return self.data_modifica.strftime('%d/%m/%Y %H:%M')
    display_data_modifica.short_description = _('Data Modif.')
    display_data_modifica.admin_order_field = 'data_modifica'

    def save(self, *args, **kwargs):
        if not self.id:
            self.data_creazione = timezone.now()
        self.data_modifica = timezone.now()
        return super(ImmagineAllegata, self).save(*args, **kwargs)
