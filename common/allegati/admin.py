from django.contrib.contenttypes.admin import GenericTabularInline
from django.utils.html import mark_safe
from django.utils.translation import ugettext_lazy as _
from django.contrib.admin.filters import RelatedOnlyFieldListFilter
from django.http import HttpResponse

from import_export.admin import ExportActionModelAdmin
from import_export.formats import base_formats
from secretary import Renderer

from common.allegati.models import DocumentoAllegato, ImmagineAllegata
from common.allegati import forms
from common.allegati.resources import DocumentoAllegatoResource
from common.stampe.utils import get_template_stampa


class StatoConservazioneAdmin(ExportActionModelAdmin):
    list_display = (
        'descrizione', 'codice',
    )
    search_fields = ('descrizione', 'codice', )


class ImmagineAllegataInline(GenericTabularInline):
    fields = (
        (
            'thumbnail_immagine', 'descrizione', 'file_immagine', 'categoria',
            'data_creazione', 'data_modifica'
        ),
    )
    readonly_fields = ('data_creazione', 'data_modifica', 'thumbnail_immagine')
    model = ImmagineAllegata
    extra = 1
    form = forms.ImmagineAllegataForm
    suit_classes = 'suit-tab suit-tab-immagini'

    def thumbnail_immagine(self, obj):
        if obj.file_immagine:
            return mark_safe('<a href="{}" target="_blank"><img src="{}" width="425" /></a>'.format(obj.file_immagine.url, obj.file_immagine.url))
        return ''
    thumbnail_immagine.short_description = _('Immagine')
    thumbnail_immagine.allow_tags = True


class DocumentoAllegatoInlinesNoAdd(GenericTabularInline):
    fields = (
        'get_link', 'descrizione', 'categoria', 'data_creazione', 'data_modifica'
    )
    readonly_fields = ('get_link', 'data_creazione', 'data_modifica', 'get_tipo_file')
    model = DocumentoAllegato
    autocomplete_fields = ('categoria', )
    extra = 0
    form = forms.DocumentoAllegatoForm
    suit_classes = 'suit-tab suit-tab-allegati'

    def has_add_permission(self, request):
        return False


class DocumentoAllegatoInlinesAddOnly(GenericTabularInline):
    fields = (
        'file_allegato', 'descrizione', 'categoria', 'data_creazione', 'data_modifica'
    )
    readonly_fields = ('data_creazione', 'data_modifica')
    model = DocumentoAllegato
    extra = 1
    max_num = 1
    autocomplete_fields = ('categoria', )
    verbose_name = _('Aggiungi nuovo allegato')
    verbose_name_plural = _('Aggiungi nuovo allegato')
    form = forms.DocumentoAllegatoReadonlyInlineForm
    suit_classes = 'suit-tab suit-tab-allegati'

    def get_queryset(self, request):
        qs = super(DocumentoAllegatoInlinesAddOnly, self).get_queryset(request)
        return qs.none()


class DocumentoAllegatoAdmin(ExportActionModelAdmin):
    list_display = (
        'descrizione', 'get_link', 'get_content_link',
        'proprietario', 'categoria', 'display_data_creazione', 'display_data_modifica'
    )
    readonly_fields = (
        'proprietario', 'data_creazione', 'data_modifica', 'get_content_link', 'applicativo'
    )
    search_fields = (
        'descrizione', 'descrizione_riferimento'
    )
    list_filter = (
        'categoria', ('proprietario', RelatedOnlyFieldListFilter),
        'data_creazione', 'data_modifica',)
    ordering = ('-data_creazione', )
    autocomplete_fields = ('categoria', )
    resource_class = DocumentoAllegatoResource
    actions = ['export_admin_action', 'elenco_allegati', ]
    formats = (
        base_formats.CSV,
        base_formats.XLS,
        base_formats.ODS,
    )
    fieldsets = (
        (None,
            {
                'fields':
                (
                    ('file_allegato',),
                    ('descrizione',),
                    'categoria',
                    ('get_content_link',),
                    ('proprietario',),
                    ('data_creazione', 'data_modifica',),
                )
            }),
        (
            _('Dati Area/Esercizio'), dict(
                classes=('collapse', ),
                fields=(
                    'esercizio',
                    'area',
                    'applicativo',
                )
            )
        ),
    )
    form = forms.DocumentoAllegatoForm

    def get_changeform_initial_data(self, request):
        if request.user:
            if request.user.esercizio_corrente and request.user.esercizio_corrente:
                return {
                    'area': request.user.area_corrente,
                    'esercizio': request.user.esercizio_corrente
                }

    def save_model(self, request, obj, form, change):
        if not obj.proprietario:
            obj.proprietario = request.user
        if hasattr(obj, 'area'):
            if not obj.area:
                if request.user.area_corrente:
                    obj.area = request.user.area_corrente
        if hasattr(obj, 'esercizio'):
            if not obj.esercizio:
                if request.user.esercizio_corrente:
                    obj.esercizio = request.user.esercizio_corrente
        return super(DocumentoAllegatoAdmin, self).save_model(request, obj, form, change)

    def elenco_allegati(self, request, queryset):
        elenco_allegati = queryset
        template = get_template_stampa('elenco_allegati')
        context = {
            'elenco_allegati': elenco_allegati,
        }
        engine = Renderer()
        result = engine.render(template, **context)
        response = HttpResponse(content_type='application/vnd.oasis.opendocument.text')
        response['Content-Disposition'] = 'attachment; filename=elenco_allegati.odt'
        response.write(result)
        return response
    elenco_allegati.short_description = _('Stampa Elenco Allegati sel.')

    def get_queryset(self, request):
        qs = super(DocumentoAllegatoAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
            qs = qs.filter(area__in=elenco_aree_figlie)
        return qs

    # def get_content_link_display(self, obj, request):
    #     if obj:
    #         permessi = request.user.get_user_permission(obj)
    #         if 'change' in permessi:
    #             return obj.get_content_link()
    #         else:
    #             return '%s' % obj
    #     return ''
    # get_content_link_display.short_description = _('Riferimento')
    # get_content_link_display.allow_tags = True
