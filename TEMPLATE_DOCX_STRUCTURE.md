# Struttura Template DOCX per Gerarchia Aree

## Problema Attuale
Il template `religiosi_annuario_per_aree.docx` è un file binario che deve essere modificato per supportare la nuova struttura gerarchica dei dati.

## Struttura Dati Fornita al Template

La funzione `stampa_annuario_componenti_case_per_area` fornisce al template il seguente contesto:

```python
contesto = {
    'elenco_province_case': [
        {
            'area_obj': Area,                    # Oggetto Area
            'nazione_obj': TipoArea,            # Oggetto TipoArea (per compatibilità)
            'elenco_nomi_religiosi': str,       # Lista nomi religiosi separati da \n
            'elenco_case': [CasaPublius],       # Lista oggetti CasaPublius
            'numero_case': int,                 # Numero di case
            'numero_religiosi': int,            # Numero di religiosi
            'livello': int                      # NUOVO: Livello nella gerarchia (0, 1, 2, ...)
        },
        # ... altre aree in ordine gerarchico
    ],
    'intestazione_stampe': str,
    'data_corrente': str
}
```

## Struttura Template Suggerita

### 1. Intestazione
```
{{intestazione_stampe}}
ANNUARIO COMPONENTI CASE PER AREA
Data: {{data_corrente}}
```

### 2. Loop Principale con Indentazione per Livelli
```
{%tr for area in elenco_province_case %}
{%if area.livello == 0 %}
═══════════════════════════════════════════════════════════════
{{area.area_obj.nome}} ({{area.area_obj.tipo_area.nome if area.area_obj.tipo_area else 'N/A'}})
═══════════════════════════════════════════════════════════════
{%elif area.livello == 1 %}
    ▶ {{area.area_obj.nome}} ({{area.area_obj.tipo_area.nome if area.area_obj.tipo_area else 'N/A'}})
    ───────────────────────────────────────────────────────────
{%elif area.livello == 2 %}
        ▷ {{area.area_obj.nome}} ({{area.area_obj.tipo_area.nome if area.area_obj.tipo_area else 'N/A'}})
        ─────────────────────────────────────────────────────
{%else %}
            ▸ {{area.area_obj.nome}} ({{area.area_obj.tipo_area.nome if area.area_obj.tipo_area else 'N/A'}})
{%endif %}

Totale Case: {{area.numero_case}} | Totale Religiosi: {{area.numero_religiosi}}

{%tr for casa in area.elenco_case %}
🏠 {{casa.nome}}
{{casa.get_dati_anagrafici_annuario}}
{{casa.get_dati_fondazione_annuario}}

Componenti:
{{casa.elenco_componenti_annuario}}

{%tr endfor %}

{%tr endfor %}
```

## Vantaggi della Nuova Struttura

1. **Visualizzazione Gerarchica**: Ogni livello di area è visivamente distinto
2. **Indentazione Progressiva**: I livelli più profondi sono più indentati
3. **Simboli Distintivi**: Diversi simboli per diversi livelli (═, ▶, ▷, ▸)
4. **Informazioni Complete**: Mantiene tutte le informazioni del template originale
5. **Ordine Logico**: Le aree appaiono nell'ordine gerarchico corretto

## Implementazione

Per implementare questa struttura nel template DOCX:

1. **Estrarre il contenuto XML** dal file DOCX esistente
2. **Modificare la struttura dei loop** per includere la logica di livello
3. **Aggiungere formattazione condizionale** basata sul campo `livello`
4. **Ricreare il file DOCX** con la nuova struttura

## Note Tecniche

- Il campo `livello` inizia da 0 (area radice) e incrementa per ogni livello di profondità
- La compatibilità con il template esistente è mantenuta usando `nazione_obj` per il `tipo_area`
- Le funzioni esistenti delle case (`get_dati_anagrafici_annuario`, `get_dati_fondazione_annuario`) continuano a funzionare
- L'ordine dei dati è garantito dalla logica di processamento ricorsivo

## Implementazione Completata ✅

### Template Creato
- **File**: `religiosi_annuario_per_aree_gerarchico.docx`
- **Posizione**: `common/templates/publius/persone/`
- **Stato**: ✅ Creato e testato con successo

### Modifiche Applicate
1. ✅ Estratto e analizzato il contenuto del template DOCX esistente
2. ✅ Identificati i loop e le variabili utilizzate
3. ✅ Creato nuovo template con supporto per la gerarchia
4. ✅ Testato il nuovo template con dati reali
5. ✅ Aggiornata la funzione `get_template_stampa` per utilizzare il nuovo template

### Risultato
Il template ora supporta completamente la visualizzazione gerarchica delle aree con:
- Indentazione progressiva basata sul livello
- Simboli distintivi per ogni livello
- Compatibilità completa con le funzioni esistenti
- Tutti i test passano (73/73)
