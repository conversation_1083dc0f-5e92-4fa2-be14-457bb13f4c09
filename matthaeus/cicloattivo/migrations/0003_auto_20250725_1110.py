# Generated by Django 2.2.28 on 2025-07-25 11:10

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('cicloattivo', '0002_auto_20250725_1017'),
    ]

    operations = [
        migrations.AlterField(
            model_name='datipagamentofatturacliente',
            name='importo_pagamento_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PL<PERSON>', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='arrotondamento_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='imponibile_cassa_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='imponibile_importo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='importo_bollo_virtuale_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='importo_contributo_cassa_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='importo_ritenuta_aggiuntiva_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='importo_ritenuta_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='importo_totale_documento_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='imposta_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='fatturacliente',
            name='totale_fattura_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='pagamentocliente',
            name='importo_pagamento_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3),
        ),
        migrations.AlterField(
            model_name='rigafatturacliente',
            name='prezzo_totale_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='rigafatturacliente',
            name='prezzo_unitario_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='rigariepilogofatturacliente',
            name='arrotondamento_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='rigariepilogofatturacliente',
            name='imponibile_importo_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='rigariepilogofatturacliente',
            name='imposta_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='rigariepilogofatturacliente',
            name='spese_accessorie_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('NGN', 'NGN'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('GBP', '£'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True),
        ),
    ]
