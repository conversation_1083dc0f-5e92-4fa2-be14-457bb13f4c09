# Generated by Django 2.2.28 on 2025-07-03 09:10

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import djmoney.models.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('anagrafica', '0022_auto_20250527_1050'),
        ('movimenti', '0081_auto_20250616_1003'),
        ('anagraficabase', '0090_impostazionimagister_visualizza_saldo_fornitori'),
    ]

    operations = [
        migrations.CreateModel(
            name='FatturaCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero_protocollo', models.PositiveIntegerField(blank=True, null=True, verbose_name='num. protocollo')),
                ('trasmittente_paese', models.CharField(blank=True, max_length=2, null=True)),
                ('trasmittente_codice', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('progressivo_invio', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_destinatario', models.CharField(blank=True, max_length=7, null=True)),
                ('contatti_trasmittente_telefono', models.CharField(blank=True, max_length=200, null=True)),
                ('contatti_trasmittente_email', models.CharField(blank=True, max_length=200, null=True)),
                ('pec_destinatario', models.CharField(blank=True, max_length=200, null=True)),
                ('partita_iva', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_fiscale', models.CharField(blank=True, max_length=200, null=True)),
                ('ragione_sociale', models.CharField(blank=True, max_length=200, null=True)),
                ('regime_fiscale', models.CharField(blank=True, choices=[('RF01', 'Ordinario'), ('RF02', 'Contribuenti minimi (art.1, c.96-117, L. 244/07)'), ('RF04', "Agricoltura e attivita' connesse e pesca (artt.34 e 34-bis, DPR 633/72)"), ('RF05', 'Vendita sali e tabacchi (art.74, c.1, DPR. 633/72)'), ('RF06', 'Commercio fiammiferi (art.74, c.1, DPR  633/72)'), ('RF07', 'Editoria (art.74, c.1, DPR  633/72'), ('RF08', 'Gestione servizi telefonia pubblica (art.74, c.1, DPR 633/72)'), ('RF09', 'Rivendita documenti di trasporto pubblico e di sosta (art.74, c.1, DPR  633/72)'), ('RF10', "Intrattenimenti, giochi e altre attivita' di cui alla tariffa allegata al DPR 640/72 (art.74, c.6, DPR 633/72)"), ('RF11', 'Agenzie viaggi e turismo (art.74-ter, DPR 633/72)'), ('RF12', 'Agriturismo (art.5, c.2, L. 413/91)'), ('RF13', 'Vendite a domicilio (art.25-bis, c.6, DPR  600/73)'), ('RF14', "Rivendita beni usati, oggetti d'arte, d'antiquariato o da collezione (art.36, DL 41/95)"), ('RF15', "Agenzie di vendite all'asta di oggetti d'arte, antiquariato o da collezione (art.40-bis, DL 41/95)"), ('RF16', 'IVA per cassa P.A. (art.6, c.5, DPR 633/72)'), ('RF17', 'IVA per cassa (art. 32-bis, DL 83/2012)'), ('RF18', 'Altro'), ('RF19', 'Regime forfettario (art.1, c.54-89, L. 190/2014')], max_length=200, null=True)),
                ('indirizzo', models.CharField(blank=True, max_length=60, null=True)),
                ('numero_civico', models.CharField(blank=True, max_length=8, null=True)),
                ('cap', models.CharField(blank=True, max_length=5, null=True)),
                ('comune', models.CharField(blank=True, max_length=60, null=True)),
                ('nazione', models.CharField(blank=True, max_length=5, null=True)),
                ('ufficio_iscrizione_rea', models.CharField(blank=True, max_length=2, null=True)),
                ('numero_iscrizione_rea', models.CharField(blank=True, max_length=200, null=True)),
                ('capitale_sociale', models.CharField(blank=True, max_length=200, null=True)),
                ('socio_unico', models.CharField(blank=True, choices=[('SU', 'Socio Unico'), ('SM', "piu' soci")], max_length=2, null=True)),
                ('stato_liquidazione', models.CharField(blank=True, max_length=2, null=True)),
                ('telefono', models.CharField(blank=True, max_length=200, null=True)),
                ('fax', models.CharField(blank=True, max_length=200, null=True)),
                ('email', models.CharField(blank=True, max_length=256, null=True)),
                ('riferimento_amministrazione', models.CharField(blank=True, max_length=200, null=True)),
                ('rappresentante_fiscale_partita_iva', models.CharField(blank=True, max_length=200, null=True)),
                ('rappresentante_fiscale_codice_fiscale', models.CharField(blank=True, max_length=200, null=True)),
                ('rappresentante_fiscale_denominazione', models.CharField(blank=True, max_length=200, null=True)),
                ('rappresentante_fiscale_nome', models.CharField(blank=True, max_length=200, null=True)),
                ('rappresentante_fiscale_cognome', models.CharField(blank=True, max_length=200, null=True)),
                ('cessionario_partita_iva', models.CharField(blank=True, max_length=200, null=True)),
                ('cessionario_codice_fiscale', models.CharField(blank=True, max_length=200, null=True)),
                ('cessionario_denominazione', models.CharField(blank=True, max_length=80, null=True)),
                ('cessionario_indirizzo', models.CharField(blank=True, max_length=60, null=True)),
                ('cessionario_comune', models.CharField(blank=True, max_length=60, null=True)),
                ('cessionario_numero_civico', models.CharField(blank=True, max_length=8, null=True)),
                ('cessionario_cap', models.CharField(blank=True, max_length=5, null=True)),
                ('cessionario_provincia', models.CharField(blank=True, max_length=2, null=True)),
                ('tipo_documento', models.CharField(blank=True, choices=[('TD01', 'fattura'), ('TD02', 'acconto/anticipo su fattura'), ('TD03', 'acconto/anticipo su parcella'), ('TD04', 'nota di credito'), ('TD05', 'nota di debito'), ('TD06', 'parcella'), ('TD16', 'integrazione fattura reverse charge interno'), ('TD17', "integrazione/autofattura per acquisto servizi dall'estero"), ('TD18', 'integrazione per acquisto di beni intracomunitari'), ('TD19', 'integrazione/autofattura per acquisto di beni ex art.17 c.2 DPR 633/72'), ('TD20', 'autofattura per regolarizzazione e integrazione delle fatture (ex art.6 c.8 d.lgs. 471/97  o  art.46 c.5 D.L. 331/93)'), ('TD21', 'autofattura per splafonamento'), ('TD22', 'estrazione beni da Deposito IVA'), ('TD23', "estrazione beni da Deposito IVA con versamento dell'IVA"), ('TD24', "fattura differita di cui all'art. 21, comma 4, lett. a)"), ('TD25', "fattura differita di cui all'art. 21, comma 4, terzo periodo lett. b)"), ('TD26', 'cessione di beni ammortizzabili e per passaggi interni (ex art.36 DPR 633/72)'), ('TD27', 'fattura per autoconsumo o per cessioni gratuite senza rivalsa')], max_length=4, null=True)),
                ('divisa', models.CharField(default='EUR', max_length=3)),
                ('data_documento', models.DateField()),
                ('numero_documento', models.CharField(max_length=20)),
                ('importo_totale_documento_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('importo_totale_documento', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='tot. documento')),
                ('arrotondamento_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('arrotondamento', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='arrotondamento')),
                ('tipo_ritenuta', models.CharField(blank=True, choices=[('RT01', 'ritenuta pers. fisiche'), ('RT02', 'ritenuta pers. giurid.'), ('RT03', 'contributo INPS'), ('RT04', 'contributo ENASARCO'), ('RT05', 'contributo ENPAM'), ('RT06', 'altro contributo previdenziale')], max_length=4, null=True)),
                ('importo_ritenuta_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('importo_ritenuta', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='importo ritenuta')),
                ('aliquota_ritenuta', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('causale_pagamento_ritenuta', models.CharField(blank=True, max_length=2, null=True)),
                ('tipo_ritenuta_aggiuntiva', models.CharField(blank=True, choices=[('RT01', 'ritenuta pers. fisiche'), ('RT02', 'ritenuta pers. giurid.'), ('RT03', 'contributo INPS'), ('RT04', 'contributo ENASARCO'), ('RT05', 'contributo ENPAM'), ('RT06', 'altro contributo previdenziale')], max_length=4, null=True)),
                ('importo_ritenuta_aggiuntiva_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('importo_ritenuta_aggiuntiva', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='importo ritenuta aggiuntiva')),
                ('aliquota_ritenuta_aggiuntiva', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('causale_pagamento_ritenuta_aggiuntiva', models.CharField(blank=True, max_length=2, null=True)),
                ('bollo_virtuale', models.CharField(blank=True, max_length=2, null=True)),
                ('importo_bollo_virtuale_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('importo_bollo_virtuale', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='importo bollo virtuale')),
                ('tipo_cassa_previdenziale', models.CharField(blank=True, choices=[('TC01', 'Cassa nazionale previdenza e assistenza avvocati e procuratori legali'), ('TC02', 'Cassa previdenza dottori commercialisti'), ('TC03', 'Cassa previdenza e assistenza geometri'), ('TC04', 'Cassa nazionale previdenza e assistenza ingegneri e architetti liberi professionisti'), ('TC05', 'Cassa nazionale del notariato'), ('TC06', 'Cassa nazionale previdenza e assistenza ragionieri e periti commerciali'), ('TC07', 'Ente nazionale assistenza agenti e rappresentanti di commercio (ENASARCO)'), ('TC08', 'Ente nazionale previdenza e assistenza consulenti del lavoro (ENPACL)'), ('TC09', 'Ente nazionale previdenza e assistenza medici (ENPAM)'), ('TC10', 'Ente nazionale previdenza e assistenza farmacisti (ENPAF)'), ('TC11', 'Ente nazionale previdenza e assistenza veterinari (ENPAV)'), ('TC12', "Ente nazionale previdenza e assistenza impiegati dell'agricoltura (ENPAIA)"), ('TC13', 'Fondo previdenza impiegati imprese di spedizione e agenzie marittime'), ('TC14', 'Istituto nazionale previdenza giornalisti italiani (INPGI)'), ('TC15', 'Opera nazionale assistenza orfani sanitari italiani (ONAOSI)'), ('TC16', 'Cassa autonoma assistenza integrativa giornalisti italiani (CASAGIT)'), ('TC17', 'Ente previdenza periti industriali e periti industriali laureati (EPPI)'), ('TC18', 'Ente previdenza e assistenza pluricategoriale (EPAP)'), ('TC19', 'Ente nazionale previdenza e assistenza biologi (ENPAB)'), ('TC20', 'Ente nazionale previdenza e assistenza professione infermieristica (ENPAPI)'), ('TC21', 'Ente nazionale previdenza e assistenza psicologi (ENPAP)'), ('TC22', 'INPS')], max_length=200, null=True)),
                ('aliquota_contributo_cassa', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('importo_contributo_cassa_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('importo_contributo_cassa', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='importo contributo cassa')),
                ('imponibile_cassa_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('imponibile_cassa', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='imponibile cassa')),
                ('aliquota_iva_cassa', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('ritenuta_cassa', models.CharField(blank=True, max_length=5, null=True)),
                ('natura_cassa', models.CharField(blank=True, choices=[('N1', 'N1 escluse ex art. 15'), ('N2', 'N2 non soggette'), ('N2.1', 'N2.1 non soggette ad IVA ai sensi degli artt. da 7 a 7-septies del DPR 633/72'), ('N2.2', 'N2.2 non soggette - altri casi'), ('N3', 'N3 non imponibili'), ('N3.1', 'N3.1 non imponibili - esportazioni'), ('N3.2', 'N3.2 non imponibili - cessioni intracomunitarie'), ('N3.3', 'N3.3 non imponibili - cessioni verso San Marino'), ('N3.4', "N3.4 non imponibili - operazioni assimilate alle cessioni all'esportazione"), ('N3.5', "N3.5 non imponibili - a seguito di dichiarazioni d'intento"), ('N3.6', 'N3.6 non imponibili - altre operazioni che non concorrono alla formazione del plafond'), ('N4', 'N4 esenti'), ('N5', 'N5 regime del margine / IVA non esposta in fattura'), ('N6', 'N6 inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'), ('N6.1', 'N6.1 inversione contabile - cessione di rottami e altri materiali di recupero'), ('N6.2', 'N6.2 inversione contabile - cessione di oro e argento puro'), ('N6.3', 'N6.3 inversione contabile - subappalto nel settore edile'), ('N6.4', 'N6.4 inversione contabile - cessione di fabbricati'), ('N6.5', 'N6.5 inversione contabile - cessione di telefoni cellulari'), ('N6.6', 'N6.6 inversione contabile - cessione di prodotti elettronici'), ('N6.7', 'N6.7 inversione contabile - prestazioni comparto edile e settori connessi'), ('N6.8', 'N6.8 inversione contabile - operazioni settore energetico'), ('N6.9', 'N6.9 inversione contabile - altri casi'), ('N7', 'N7 IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)')], max_length=5, null=True)),
                ('riferimento_amministrazione_cassa', models.CharField(blank=True, max_length=200, null=True)),
                ('causale', models.CharField(blank=True, max_length=200, null=True)),
                ('dati_contratto_id_documento', models.CharField(blank=True, max_length=200, null=True)),
                ('aliquota_iva', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('imponibile_importo_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('imponibile_importo', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='imponibile')),
                ('imposta_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('imposta', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='imposta')),
                ('totale_fattura_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('totale_fattura', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='totale fattura')),
                ('esigibilita_iva', models.CharField(blank=True, max_length=200, null=True)),
                ('approvata', models.BooleanField(default=False)),
                ('pagata', models.BooleanField(default=False)),
                ('importata', models.BooleanField(default=False, verbose_name='importata')),
                ('note', models.TextField(blank=True)),
                ('area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area')),
                ('causale_contabile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='movimenti.CausaleContabile', verbose_name='causale contabile')),
                ('cliente', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagrafica.Cliente', verbose_name='cliente')),
                ('esercizio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Esercizio', verbose_name='esercizio')),
                ('movimento_primanota', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='movimenti.MovimentoPrimaNota', verbose_name='Movimento Prima Nota')),
                ('provincia', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagraficabase.Provincia')),
            ],
            options={
                'verbose_name': 'fattura cliente',
                'verbose_name_plural': 'fatture clienti',
                'ordering': ('-data_documento', '-numero_protocollo'),
                'unique_together': {('numero_protocollo', 'area', 'esercizio')},
            },
        ),
        migrations.CreateModel(
            name='SaldoCliente',
            fields=[
            ],
            options={
                'verbose_name': 'Saldo Cliente',
                'verbose_name_plural': 'Saldi Clienti',
                'ordering': ('ragione_sociale',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('anagrafica.cliente',),
        ),
        migrations.CreateModel(
            name='RigaRiepilogoFatturaCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('aliquota_iva', models.DecimalField(decimal_places=2, max_digits=9)),
                ('natura', models.CharField(blank=True, choices=[('N1', 'N1 escluse ex art. 15'), ('N2', 'N2 non soggette'), ('N2.1', 'N2.1 non soggette ad IVA ai sensi degli artt. da 7 a 7-septies del DPR 633/72'), ('N2.2', 'N2.2 non soggette - altri casi'), ('N3', 'N3 non imponibili'), ('N3.1', 'N3.1 non imponibili - esportazioni'), ('N3.2', 'N3.2 non imponibili - cessioni intracomunitarie'), ('N3.3', 'N3.3 non imponibili - cessioni verso San Marino'), ('N3.4', "N3.4 non imponibili - operazioni assimilate alle cessioni all'esportazione"), ('N3.5', "N3.5 non imponibili - a seguito di dichiarazioni d'intento"), ('N3.6', 'N3.6 non imponibili - altre operazioni che non concorrono alla formazione del plafond'), ('N4', 'N4 esenti'), ('N5', 'N5 regime del margine / IVA non esposta in fattura'), ('N6', 'N6 inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'), ('N6.1', 'N6.1 inversione contabile - cessione di rottami e altri materiali di recupero'), ('N6.2', 'N6.2 inversione contabile - cessione di oro e argento puro'), ('N6.3', 'N6.3 inversione contabile - subappalto nel settore edile'), ('N6.4', 'N6.4 inversione contabile - cessione di fabbricati'), ('N6.5', 'N6.5 inversione contabile - cessione di telefoni cellulari'), ('N6.6', 'N6.6 inversione contabile - cessione di prodotti elettronici'), ('N6.7', 'N6.7 inversione contabile - prestazioni comparto edile e settori connessi'), ('N6.8', 'N6.8 inversione contabile - operazioni settore energetico'), ('N6.9', 'N6.9 inversione contabile - altri casi'), ('N7', 'N7 IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)')], max_length=5, null=True)),
                ('spese_accessorie_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('spese_accessorie', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='spese accessorie')),
                ('arrotondamento_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('arrotondamento', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='arrotondamento')),
                ('imponibile_importo_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('imponibile_importo', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='imponibile importo')),
                ('imposta_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('imposta', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='imposta')),
                ('esigibilita_iva', models.CharField(blank=True, choices=[('I', "IVA ad esigibilita' immediata"), ('D', "IVA ad esigibilit' differita"), ('S', 'Scissione dei pagamenti')], max_length=1, null=True)),
                ('riferimento_normativo', models.CharField(blank=True, max_length=200, null=True)),
                ('fattura_cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cicloattivo.FatturaCliente')),
            ],
            options={
                'verbose_name': 'Riga Riepilogo Fattura Cliente',
                'verbose_name_plural': 'Righe Riepilogo Fattura Cliente',
            },
        ),
        migrations.CreateModel(
            name='PagamentoCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_pagamento', models.DateField(default=django.utils.timezone.now, verbose_name='data pagamento')),
                ('importo_pagamento_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('importo_pagamento', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='EUR', max_digits=10, verbose_name='importo pagamento')),
                ('causale', models.CharField(blank=True, max_length=200, null=True)),
                ('note', models.TextField(blank=True)),
                ('area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Area', verbose_name='area')),
                ('cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='anagrafica.Cliente', verbose_name='cliente')),
                ('conto_liquidita', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conto_liquidita_cliente_fk', to='anagraficabase.PianoDeiContiLiquidita', verbose_name='conto liquidità')),
                ('conto_pagamento', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conto_pagamento_cliente_fk', to='anagraficabase.PianoDeiConti', verbose_name='conto pagamento')),
                ('esercizio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='anagraficabase.Esercizio', verbose_name='esercizio')),
                ('movimento_primanota', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='movimenti.MovimentoPrimaNota', verbose_name='Movimento Prima Nota')),
            ],
            options={
                'verbose_name': 'Pagamento Cliente',
                'verbose_name_plural': 'Pagamenti Clienti',
                'ordering': ('-data_pagamento', 'cliente'),
            },
        ),
        migrations.CreateModel(
            name='DatiPagamentoFatturaCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('condizioni_pagamento', models.CharField(choices=[('TP01', 'pagamento a rate'), ('TP02', 'pagamento completo'), ('TP03', 'anticipo')], max_length=200)),
                ('modalita_pagamento', models.CharField(choices=[('MP01', 'contanti'), ('MP02', 'assegno'), ('MP03', 'assegno circolare'), ('MP04', 'contanti presso Tesoreria'), ('MP05', 'bonifico'), ('MP06', 'vaglia cambiario'), ('MP07', 'bollettino bancario'), ('MP08', 'carta di pagamento'), ('MP09', 'RID'), ('MP10', 'RID utenze'), ('MP11', 'RID veloce'), ('MP12', 'RIBA'), ('MP13', 'MAV'), ('MP14', 'quietanza erario'), ('MP15', "giroconto su conti di contabilita' speciale"), ('MP16', 'domiciliazione bancaria'), ('MP17', 'domiciliazione postale'), ('MP18', 'bollettino di c/c postale'), ('MP19', 'SEPA Direct Debit'), ('MP20', 'SEPA Direct Debit CORE'), ('MP21', 'SEPA Direct Debit B2B'), ('MP22', "Trattenuta su somme gia' riscosse"), ('MP23', 'PagoPA')], max_length=200)),
                ('data_riferimento_termini_pagamento', models.DateField(blank=True, null=True)),
                ('giorni_termini_pagamento', models.PositiveIntegerField(blank=True, null=True)),
                ('data_scadenza_pagamento', models.DateField(blank=True, null=True)),
                ('importo_pagamento_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3)),
                ('importo_pagamento', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='EUR', max_digits=10, verbose_name='importo pagamento')),
                ('istituto_finanziario', models.CharField(blank=True, max_length=200, null=True)),
                ('iban', models.CharField(blank=True, max_length=200, null=True)),
                ('abi', models.CharField(blank=True, max_length=5, null=True)),
                ('cab', models.CharField(blank=True, max_length=5, null=True)),
                ('bic', models.CharField(blank=True, max_length=200, null=True)),
                ('fattura_cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cicloattivo.FatturaCliente')),
            ],
            options={
                'verbose_name': 'Dati Pagamenti Fattura Cliente',
                'verbose_name_plural': 'Dati Pagamenti Fattura Cliente',
            },
        ),
        migrations.CreateModel(
            name='AllegatoFatturaCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=200)),
                ('algoritmo_compressione', models.CharField(blank=True, max_length=200, null=True)),
                ('formato', models.CharField(blank=True, max_length=200, null=True)),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True)),
                ('file_allegato', models.FileField(blank=True, null=True, upload_to='')),
                ('fattura_cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cicloattivo.FatturaCliente')),
            ],
            options={
                'verbose_name': 'Allegato Fattura Cliente',
                'verbose_name_plural': 'Allegati Fattura Cliente',
                'ordering': ('fattura_cliente', 'nome'),
            },
        ),
        migrations.CreateModel(
            name='RigaFatturaCliente',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero_linea', models.PositiveIntegerField(blank=True, null=True)),
                ('tipo_cessione_prestazione', models.CharField(blank=True, choices=[('SC', 'Sconto'), ('PR', 'Premio'), ('AB', 'Abbuono'), ('AC', 'Spesa Accessoria')], max_length=200, null=True)),
                ('codice_articolo_tipo', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_articolo_valore', models.CharField(blank=True, max_length=200, null=True)),
                ('descrizione', models.TextField()),
                ('quantita', models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True)),
                ('unita_misura', models.CharField(blank=True, max_length=200, null=True)),
                ('prezzo_unitario_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('prezzo_unitario', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='prezzo unitario')),
                ('prezzo_totale_currency', djmoney.models.fields.CurrencyField(choices=[('USD', '$'), ('CAD', '$CAN'), ('AOA', 'AOA'), ('ARS', 'ARS'), ('BOB', 'BOB'), ('CHF', 'CHF'), ('IDR', 'IDR'), ('ILS', 'ILS'), ('INR', 'INR'), ('KRW', 'KRW'), ('MAD', 'MAD'), ('PEN', 'PEN'), ('PHP', 'PHP'), ('PLN', 'PLN'), ('BRL', 'R$'), ('VND', 'VND'), ('EUR', '€')], default='EUR', editable=False, max_length=3, null=True)),
                ('prezzo_totale', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='EUR', max_digits=10, null=True, verbose_name='prezzo totale')),
                ('aliquota_iva', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('fattura_cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cicloattivo.FatturaCliente')),
            ],
            options={
                'verbose_name': 'Riga Fattura Cliente',
                'verbose_name_plural': 'Righe Fattura Cliente',
                'ordering': ('numero_linea',),
                'unique_together': {('fattura_cliente', 'numero_linea')},
            },
        ),
    ]
