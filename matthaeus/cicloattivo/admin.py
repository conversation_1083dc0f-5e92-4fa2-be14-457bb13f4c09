from django.utils.translation import ugettext_lazy as _
from django.conf.urls import url
from django.urls import reverse
from django.utils.html import format_html
from django.contrib.admin import SimpleListFilter
from django.contrib.admin.filters import RelatedOnlyFieldListFilter

from matthaeus.admin import site, MatthaeusModelAdmin
from matthaeus.admin import MatthaeusInlineModelAdmin, MatthaeusStackedInlineModelAdmin
from matthaeus.anagrafica.admin import ClienteAdmin

from matthaeus.cicloattivo import models
from matthaeus.cicloattivo.views import UploadView
from matthaeus.cicloattivo import forms


class RigaFatturaClienteInline(MatthaeusInlineModelAdmin):
    model = models.RigaFatturaCliente
    fields = (
        'numero_linea', 'descrizione', 'prezzo_unitario', 'quantita',
        'aliquota_iva', 'prezzo_totale',
    )
    form = forms.RigaFatturaClienteForm
    suit_classes = 'suit-tab suit-tab-corpo_fattura'
    extra = 0


class RigaFatturaClienteInlineReadonly(RigaFatturaClienteInline):
    readonly_fields = (
        'numero_linea', 'descrizione', 'prezzo_unitario', 'quantita',
        'aliquota_iva', 'prezzo_totale',
    )


class RigaRiepilogoFatturaClienteInline(MatthaeusStackedInlineModelAdmin):
    model = models.RigaRiepilogoFatturaCliente
    fieldsets = (
        (
            '', dict(
                fields=(
                    'natura',
                    'aliquota_iva',
                    ('imponibile_importo', 'arrotondamento', ),
                    'spese_accessorie',
                    'imposta',
                    'esigibilita_iva',
                    'riferimento_normativo',
                )),
        ),
    )
    suit_classes = 'suit-tab suit-tab-righe_riepilogo'
    extra = 0
    form = forms.RigaRiepilogoFatturaClienteForm


class DatiPagamentoFatturaClienteInline(MatthaeusStackedInlineModelAdmin):
    model = models.DatiPagamentoFatturaCliente
    fieldsets = (
        (
            '', dict(
                fields=(
                    ('modalita_pagamento', 'condizioni_pagamento', ),
                    'importo_pagamento',
                    'data_scadenza_pagamento',
                    ('data_riferimento_termini_pagamento', 'giorni_termini_pagamento', ),
                    'istituto_finanziario',
                    'iban',
                    ('abi', 'cab', 'bic', ),
                )),
        ),
    )
    suit_classes = 'suit-tab suit-tab-dati_pagamento'
    extra = 0
    can_delete = False


class AllegatoFatturaClienteInline(MatthaeusInlineModelAdmin):
    model = models.AllegatoFatturaCliente
    fields = (
        'nome', 'file_allegato', 'descrizione', 'formato',
        'algoritmo_compressione',
    )
    readonly_fields = (
        'nome', 'file_allegato', 'descrizione', 'formato',
        'algoritmo_compressione',
    )
    suit_classes = 'suit-tab suit-tab-allegati'
    extra = 0
    can_delete = False

    def has_add_permission(self, request, obj=None):
        # Gli allegati vengono inseriti solo tramite importazione XML
        return False

    def has_delete_permission(self, request, obj=None):
        # Gli allegati vengono gestiti solo tramite importazione XML
        return False


class FattureConsolidateFilter(SimpleListFilter):
    title = 'Consolidata'
    parameter_name = 'valore'

    def value_as_list(self):
        return self.value().split(',') if self.value() else []

    def lookups(self, request, model_admin):
        return (
            (True, _('Sì')),
            (False, _('No')),
        )

    def queryset(self, request, queryset):
        if self.value() is not None:
            if self.value() == 'True':
                return queryset.filter(movimento_primanota__isnull=False)
            if self.value() == 'False':
                return queryset.filter(movimento_primanota__isnull=True)
        else:
            return queryset


class FatturaClienteAdmin(MatthaeusModelAdmin):
    list_display = (
        'numero_documento', 'data_documento', 'numero_protocollo', 'get_ragione_sociale_cliente', 'tipo_documento',
        'imponibile_importo', 'importo_totale_documento', 'get_link_cliente',
        'get_link_movimento', 'importata', 'get_consolidata'
    )
    date_hierarchy = 'data_documento'
    list_filter = (
        FattureConsolidateFilter,
        'importata',
    )
    list_select_related = ('cliente', 'provincia')
    search_fields = (
        'ragione_sociale', 'numero_documento', 'movimento_primanota__descrizione'
    )
    inlines = [
        RigaFatturaClienteInline, RigaRiepilogoFatturaClienteInline,
        DatiPagamentoFatturaClienteInline,
        AllegatoFatturaClienteInline,
    ]
    readonly_fields = (
        'totale_fattura', 'aliquota_iva', 'esigibilita_iva', 'arrotondamento',
        'get_link_movimento', 'importata', 'get_consolidata', 'divisa',
        # Campi del cedente/prestatore (noi) - solo lettura
        'ragione_sociale', 'indirizzo', 'comune', 'cap', 'provincia', 'nazione',
        'partita_iva', 'codice_fiscale', 'telefono', 'fax', 'email', 'riferimento_amministrazione',
        'ufficio_iscrizione_rea', 'numero_iscrizione_rea', 'capitale_sociale', 'socio_unico',
        'stato_liquidazione', 'regime_fiscale',
        # Campi dati trasmissione - solo lettura
        'trasmittente_paese', 'trasmittente_codice', 'progressivo_invio', 'codice_destinatario',
        'pec_destinatario', 'contatti_trasmittente_telefono', 'contatti_trasmittente_email'
    )
    autocomplete_fields = ('cliente', 'causale_contabile')
    form = forms.FatturaClienteForm
    list_max_show_all = 10000
    
    fieldsets = (
        (
            _('Dati Generali Documento'), dict(
                classes=('suit-tab suit-tab-dati_generali_documento',),
                fields=(
                    'numero_protocollo',
                    'data_documento',
                    'tipo_documento',
                    'numero_documento',
                    'importata',
                ),
            ),
        ),
        (
            _('Dati Esercizio'), dict(
                classes=('suit-tab suit-tab-dati_generali_documento', 'collapse'),
                fields=(
                    ('esercizio', 'area', ),
                )
            )
        ),
        (
            _('Cliente (Cessionario/Committente)'), dict(
                classes=('suit-tab suit-tab-dati_generali_documento', ),
                fields=(
                    'cliente',
                    'cessionario_denominazione',
                    ('cessionario_indirizzo', 'cessionario_numero_civico'),
                    'cessionario_comune',
                    ('cessionario_cap', 'cessionario_provincia'),
                    ('cessionario_partita_iva', 'cessionario_codice_fiscale', ),
                ),
            ),
        ),
        (
            _('Cedente/Prestatore - Dati Anagrafici'), dict(
                classes=('suit-tab suit-tab-cedente_prestatore', ),
                fields=(
                    'ragione_sociale',
                    'indirizzo',
                    'comune',
                    ('cap', 'provincia', 'nazione', ),
                    ('partita_iva', 'codice_fiscale', ),
                ),
            ),
        ),
        (
            _('Cedente/Prestatore - Dati Contatto'), dict(
                classes=('suit-tab suit-tab-cedente_prestatore', 'collapse'),
                fields=(
                    'telefono',
                    'fax',
                    'email',
                    'riferimento_amministrazione',
                ),
            ),
        ),
        (
            _('Cedente/Prestatore - Dati REA'), dict(
                classes=('suit-tab suit-tab-cedente_prestatore', 'collapse'),
                fields=(
                    'ufficio_iscrizione_rea',
                    'numero_iscrizione_rea',
                    'capitale_sociale',
                    'socio_unico',
                    'stato_liquidazione',
                    'regime_fiscale',
                ),
            ),
        ),
        (
            _('Dati Riepilogo'), dict(
                classes=('suit-tab suit-tab-riepilogo', ),
                fields=(
                    ('imponibile_importo', 'divisa'),
                    ('imposta', 'aliquota_iva', 'esigibilita_iva'),
                    'importo_totale_documento',
                    'arrotondamento',
                ),
            ),
        ),
        (
            _('Dati Calcolati'), dict(
                classes=('suit-tab suit-tab-riepilogo', ),
                fields=(
                    'totale_fattura',
                ),
            ),
        ),
        (
            _('Dati Ritenuta'), dict(
                classes=('suit-tab suit-tab-riepilogo', 'collapse'),
                fields=(
                    'tipo_ritenuta',
                    'importo_ritenuta',
                    'aliquota_ritenuta',
                    'causale_pagamento_ritenuta',
                ),
            ),
        ),
        (
            _('Dati Ritenuta aggiuntiva'), dict(
                classes=('suit-tab suit-tab-riepilogo', 'collapse'),
                fields=(
                    'tipo_ritenuta_aggiuntiva',
                    'importo_ritenuta_aggiuntiva',
                    'aliquota_ritenuta_aggiuntiva',
                    'causale_pagamento_ritenuta_aggiuntiva',
                ),
            ),
        ),
        (
            _('Dati Bollo'), dict(
                classes=('suit-tab suit-tab-riepilogo', 'collapse'),
                fields=(
                    'bollo_virtuale',
                    'importo_bollo_virtuale',
                ),
            ),
        ),
        (
            _('Dati Cassa Previdenziale'), dict(
                classes=('suit-tab suit-tab-riepilogo', 'collapse'),
                fields=(
                    'tipo_cassa_previdenziale',
                    'aliquota_contributo_cassa',
                    'importo_contributo_cassa',
                    'imponibile_cassa',
                    'aliquota_iva_cassa',
                    'ritenuta_cassa',
                    'natura_cassa',
                    'riferimento_amministrazione_cassa',
                ),
            ),
        ),
        (
            _('Dati Contabili'), dict(
                classes=('suit-tab suit-tab-dati_pagamento', ),
                fields=(
                    'causale_contabile',
                    'get_link_movimento',
                ),
            ),
        ),

        (
            _('Dati Trasmissione'), dict(
                classes=('suit-tab suit-tab-dati_trasmissione', ),
                fields=(
                    ('trasmittente_paese', 'trasmittente_codice', ),
                    'progressivo_invio',
                    'codice_destinatario',
                    'pec_destinatario',
                    'contatti_trasmittente_telefono',
                    'contatti_trasmittente_email',
                ),
            ),
        ),
        (
            '', dict(
                classes=('suit-tab suit-tab-note', ),
                fields=(
                    'note',
                ),
            ),
        ),
    )
    suit_form_tabs = (
        ('dati_generali_documento', _('Testata Documento')),
        ('riepilogo', _('Dati Riepilogo')),
        ('righe_riepilogo', _('Riep. Aliquote')),
        ('corpo_fattura', _('Corpo')),
        ('dati_pagamento', _('Dati Pag.')),
        ('cedente_prestatore', _('Cedente/Prestatore (Noi)')),
        ('allegati', _('Allegati')),
        ('dati_trasmissione', _('Dati Trasm.')),
        ('note', 'Note'),
    )

    def get_urls(self):
        url_patterns = [
            url(
                r'upload/$',
                self.admin_site.admin_view(UploadView.as_view()),
                name='cicloattivo_fatturacliente_upload'
            ),
        ]
        url_patterns += super(FatturaClienteAdmin, self).get_urls()
        return url_patterns

    def suit_column_attributes(self, column):
        if column in [
            'imponibile_importo', 'totale_fattura'
        ]:
            return {'class': 'text-xs-right'}

    def suit_cell_attributes(self, obj, column):
        if column in [
            'imponibile_importo', 'totale_fattura'
        ]:
            return {'class': 'text-xs-right'}

    def get_ragione_sociale_cliente(self, obj):
        """Restituisce la ragione sociale del cliente (cessionario/committente)"""
        if obj.cliente:
            return obj.cliente.ragione_sociale
        elif obj.cessionario_denominazione:
            return obj.cessionario_denominazione
        else:
            return '-'
    get_ragione_sociale_cliente.short_description = _('Cliente')
    get_ragione_sociale_cliente.admin_order_field = 'cliente__ragione_sociale'

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['upload_url'] = 'admin:cicloattivo_fatturacliente_upload'
        return super().changelist_view(request, extra_context)


class PagamentoClienteAdmin(MatthaeusModelAdmin):
    list_display = (
        'cliente', 'data_pagamento', 'importo_pagamento',
        'conto_pagamento', 'conto_liquidita', 'get_link_movimento'
    )
    list_filter = ('data_pagamento', 'area', 'esercizio')
    search_fields = ('cliente__ragione_sociale', 'causale')
    readonly_fields = ('get_link_movimento',)

    fieldsets = (
        (_('Dati Generali'), {
            'fields': (
                ('cliente', 'data_pagamento'),
                'importo_pagamento',
                ('conto_pagamento', 'conto_liquidita'),
                'causale',
                'get_link_movimento',
                'note'
            )
        }),
    )

    def get_changeform_initial_data(self, request):
        initial_data = super(PagamentoClienteAdmin, self).get_changeform_initial_data(request=request)
        if 'id_cliente' in request.GET:
            id_cliente = request.GET.get('id_cliente')
            cliente = models.Cliente.objects.get(pk=id_cliente)
            initial_data['cliente'] = cliente
        return initial_data


class SaldoClienteAdmin(ClienteAdmin):
    list_display = (
        'ragione_sociale', 'display_totale_dare', 'display_totale_avere',
        'display_saldo', 'attivo', 'display_link_partitario', 'display_paga_cliente'
    )
    list_filter = ('attivo', )

    def display_totale_dare(self, obj):
        self.totale_dare = obj.get_totale_dare(self.request.user.area_corrente, self.request.user.esercizio_corrente)
        return self.totale_dare
    display_totale_dare.short_description = 'Tot. Dare'

    def display_totale_avere(self, obj):
        self.totale_avere = obj.get_totale_avere(self.request.user.area_corrente, self.request.user.esercizio_corrente)
        return self.totale_avere
    display_totale_avere.short_description = 'Tot. Avere'

    def display_saldo(self, obj):
        return self.totale_dare - self.totale_avere
    display_saldo.short_description = 'Saldo'

    def display_link_partitario(self, obj):
        url = reverse('matthaeus:movimenti_partitarioanagrafica_changelist')
        return format_html('<a href="%s?q=&anagrafica__id__exact=%s">Vedi</a>' % (url, obj.pk))
    display_link_partitario.allow_tags = True
    display_link_partitario.short_description = 'Partitario'

    def display_paga_cliente(self, obj):
        url = reverse('matthaeus:cicloattivo_pagamentocliente_add')
        return format_html('<a href="%s?id_cliente=%s">Paga</a>' % (url, obj.pk))
    display_paga_cliente.allow_tags = True
    display_paga_cliente.short_description = 'Paga cliente'

    def get_list_display(self, request):
        self.request = request
        return self.list_display

    def suit_column_attributes(self, column):
        if column in [
            'display_totale_dare', 'display_totale_avere', 'display_saldo'
        ]:
            return {'class': 'text-xs-right'}

    def suit_cell_attributes(self, obj, column):
        if column in [
            'display_totale_dare', 'display_totale_avere', 'display_saldo'
        ]:
            return {'class': 'text-xs-right'}

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


site.register(models.FatturaCliente, FatturaClienteAdmin)
site.register(models.PagamentoCliente, PagamentoClienteAdmin)
site.register(models.SaldoCliente, SaldoClienteAdmin)
