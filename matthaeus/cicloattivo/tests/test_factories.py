from decimal import Decimal
from django.test import TestCase
from djmoney.money import Money

from matthaeus.cicloattivo.tests.factories import (
    FatturaClienteFactory, RigaFatturaClienteFactory, RigaRiepilogoFatturaClienteFactory,
    DatiPagamentoFatturaClienteFactory, AllegatoFatturaClienteFactory, PagamentoClienteFactory
)
from matthaeus.cicloattivo.models import (
    FatturaCliente, RigaFatturaCliente, RigaRiepilogoFatturaCliente,
    DatiPagamentoFatturaCliente, AllegatoFatturaCliente, PagamentoCliente
)


class FatturaClienteFactoryTest(TestCase):
    """Test per FatturaClienteFactory"""
    
    def test_fattura_cliente_factory_creation(self):
        """Test creazione di FatturaCliente tramite factory"""
        fattura = FatturaClienteFactory()
        
        self.assertIsInstance(fattura, FatturaCliente)
        self.assertIsNotNone(fattura.pk)
        self.assertIsNotNone(fattura.area)
        self.assertIsNotNone(fattura.esercizio)
        self.assertIsNotNone(fattura.cliente)
        self.assertIsNotNone(fattura.data_documento)
        self.assertIsNotNone(fattura.numero_documento)
        self.assertEqual(fattura.tipo_documento, 'TD01')
        self.assertEqual(fattura.divisa, 'EUR')
        self.assertIsInstance(fattura.importo_totale_documento, Money)
        self.assertFalse(fattura.approvata)
        self.assertFalse(fattura.pagata)
        self.assertFalse(fattura.importata)
        
    def test_fattura_cliente_factory_with_custom_values(self):
        """Test creazione con valori personalizzati"""
        fattura = FatturaClienteFactory(
            numero_documento='FC999',
            tipo_documento='TD02',
            approvata=True
        )

        self.assertEqual(fattura.numero_documento, 'FC999')
        self.assertEqual(fattura.tipo_documento, 'TD02')
        self.assertTrue(fattura.approvata)
        
    def test_fattura_cliente_factory_sequence(self):
        """Test sequenza numerica dei documenti"""
        fattura1 = FatturaClienteFactory()
        fattura2 = FatturaClienteFactory()
        
        # I numeri documento dovrebbero essere diversi
        self.assertNotEqual(fattura1.numero_documento, fattura2.numero_documento)


class RigaFatturaClienteFactoryTest(TestCase):
    """Test per RigaFatturaClienteFactory"""
    
    def test_riga_fattura_factory_creation(self):
        """Test creazione di RigaFatturaCliente tramite factory"""
        riga = RigaFatturaClienteFactory()
        
        self.assertIsInstance(riga, RigaFatturaCliente)
        self.assertIsNotNone(riga.pk)
        self.assertIsNotNone(riga.fattura_cliente)
        self.assertIsNotNone(riga.numero_linea)
        self.assertIsNotNone(riga.descrizione)
        self.assertIsInstance(riga.quantita, Decimal)
        self.assertIsInstance(riga.prezzo_unitario, Money)
        self.assertIsInstance(riga.prezzo_totale, Money)
        self.assertIsInstance(riga.aliquota_iva, Decimal)
        
    def test_riga_fattura_factory_with_fattura(self):
        """Test creazione riga con fattura specifica"""
        fattura = FatturaClienteFactory()
        riga = RigaFatturaClienteFactory(fattura_cliente=fattura)
        
        self.assertEqual(riga.fattura_cliente, fattura)
        
    def test_riga_fattura_factory_sequence(self):
        """Test sequenza numero linea"""
        fattura = FatturaClienteFactory()
        riga1 = RigaFatturaClienteFactory(fattura_cliente=fattura)
        riga2 = RigaFatturaClienteFactory(fattura_cliente=fattura)
        
        # I numeri linea dovrebbero essere sequenziali
        self.assertNotEqual(riga1.numero_linea, riga2.numero_linea)


class RigaRiepilogoFatturaClienteFactoryTest(TestCase):
    """Test per RigaRiepilogoFatturaClienteFactory"""
    
    def test_riga_riepilogo_factory_creation(self):
        """Test creazione di RigaRiepilogoFatturaCliente tramite factory"""
        riga_riepilogo = RigaRiepilogoFatturaClienteFactory()
        
        self.assertIsInstance(riga_riepilogo, RigaRiepilogoFatturaCliente)
        self.assertIsNotNone(riga_riepilogo.pk)
        self.assertIsNotNone(riga_riepilogo.fattura_cliente)
        self.assertEqual(riga_riepilogo.aliquota_iva, Decimal('22.00'))
        self.assertIsInstance(riga_riepilogo.imponibile_importo, Money)
        self.assertIsInstance(riga_riepilogo.imposta, Money)


class DatiPagamentoFatturaClienteFactoryTest(TestCase):
    """Test per DatiPagamentoFatturaClienteFactory"""
    
    def test_dati_pagamento_factory_creation(self):
        """Test creazione di DatiPagamentoFatturaCliente tramite factory"""
        dati_pagamento = DatiPagamentoFatturaClienteFactory()
        
        self.assertIsInstance(dati_pagamento, DatiPagamentoFatturaCliente)
        self.assertIsNotNone(dati_pagamento.pk)
        self.assertIsNotNone(dati_pagamento.fattura_cliente)
        self.assertEqual(dati_pagamento.condizioni_pagamento, 'TP02')
        self.assertEqual(dati_pagamento.modalita_pagamento, 'MP05')
        self.assertIsInstance(dati_pagamento.importo_pagamento, Money)
        self.assertIsNotNone(dati_pagamento.data_scadenza_pagamento)


class AllegatoFatturaClienteFactoryTest(TestCase):
    """Test per AllegatoFatturaClienteFactory"""
    
    def test_allegato_factory_creation(self):
        """Test creazione di AllegatoFatturaCliente tramite factory"""
        allegato = AllegatoFatturaClienteFactory()
        
        self.assertIsInstance(allegato, AllegatoFatturaCliente)
        self.assertIsNotNone(allegato.pk)
        self.assertIsNotNone(allegato.fattura_cliente)
        self.assertIsNotNone(allegato.nome)
        self.assertIsNotNone(allegato.descrizione)
        self.assertEqual(allegato.formato, 'PDF')
        self.assertIsNone(allegato.algoritmo_compressione)
        
    def test_allegato_factory_with_custom_values(self):
        """Test creazione allegato con valori personalizzati"""
        allegato = AllegatoFatturaClienteFactory(
            nome='documento_test.xml',
            formato='XML',
            algoritmo_compressione='ZIP'
        )
        
        self.assertEqual(allegato.nome, 'documento_test.xml')
        self.assertEqual(allegato.formato, 'XML')
        self.assertEqual(allegato.algoritmo_compressione, 'ZIP')


class PagamentoClienteFactoryTest(TestCase):
    """Test per PagamentoClienteFactory"""
    
    def test_pagamento_cliente_factory_creation(self):
        """Test creazione di PagamentoCliente tramite factory"""
        pagamento = PagamentoClienteFactory()
        
        self.assertIsInstance(pagamento, PagamentoCliente)
        self.assertIsNotNone(pagamento.pk)
        self.assertIsNotNone(pagamento.area)
        self.assertIsNotNone(pagamento.esercizio)
        self.assertIsNotNone(pagamento.cliente)
        self.assertIsNotNone(pagamento.conto_pagamento)
        self.assertIsNotNone(pagamento.conto_liquidita)
        self.assertIsNotNone(pagamento.data_pagamento)
        self.assertIsInstance(pagamento.importo_pagamento, Money)
        self.assertIsNotNone(pagamento.causale)
        
    def test_pagamento_cliente_factory_with_custom_values(self):
        """Test creazione pagamento con valori personalizzati"""
        pagamento = PagamentoClienteFactory(
            importo_pagamento=Money('750.00', 'EUR'),
            causale='Pagamento fattura specifica'
        )
        
        self.assertEqual(pagamento.importo_pagamento.amount, Decimal('750.00'))
        self.assertEqual(pagamento.causale, 'Pagamento fattura specifica')


class FactoryRelationshipsTest(TestCase):
    """Test per le relazioni tra factory"""
    
    def test_fattura_con_righe_e_allegati(self):
        """Test creazione fattura completa con righe e allegati"""
        fattura = FatturaClienteFactory()
        
        # Crea righe associate
        riga1 = RigaFatturaClienteFactory(fattura_cliente=fattura)
        riga2 = RigaFatturaClienteFactory(fattura_cliente=fattura)
        
        # Crea riepilogo
        riepilogo = RigaRiepilogoFatturaClienteFactory(fattura_cliente=fattura)
        
        # Crea dati pagamento
        dati_pagamento = DatiPagamentoFatturaClienteFactory(fattura_cliente=fattura)
        
        # Crea allegato
        allegato = AllegatoFatturaClienteFactory(fattura_cliente=fattura)
        
        # Verifica le relazioni
        self.assertEqual(fattura.rigafatturacliente_set.count(), 2)
        self.assertEqual(fattura.rigariepilogofatturacliente_set.count(), 1)
        self.assertEqual(fattura.datipagamentofatturacliente_set.count(), 1)
        self.assertEqual(fattura.allegatofatturacliente_set.count(), 1)
        
        # Verifica che le relazioni inverse funzionino
        self.assertEqual(riga1.fattura_cliente, fattura)
        self.assertEqual(riga2.fattura_cliente, fattura)
        self.assertEqual(riepilogo.fattura_cliente, fattura)
        self.assertEqual(dati_pagamento.fattura_cliente, fattura)
        self.assertEqual(allegato.fattura_cliente, fattura)
        
    def test_pagamento_con_cliente_condiviso(self):
        """Test pagamento che condivide il cliente con una fattura"""
        fattura = FatturaClienteFactory()
        pagamento = PagamentoClienteFactory(
            cliente=fattura.cliente,
            area=fattura.area,
            esercizio=fattura.esercizio
        )
        
        # Verifica che condividano lo stesso cliente
        self.assertEqual(fattura.cliente, pagamento.cliente)
        self.assertEqual(fattura.area, pagamento.area)
        self.assertEqual(fattura.esercizio, pagamento.esercizio)
