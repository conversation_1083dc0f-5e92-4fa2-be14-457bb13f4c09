import os
import tempfile
from decimal import Decimal
from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from djmoney.money import Money

from matthaeus.cicloattivo.utils import (
    leggi_file_fattura, crea_cliente, crea_riga_fattura, crea_pagamento
)
from matthaeus.cicloattivo.models import FatturaCliente, RigaFatturaCliente, AllegatoFatturaCliente
from matthaeus.anagrafica.models import Anagrafica
from matthaeus.cicloattivo.tests.factories import FatturaClienteFactory
from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory
from common.anagraficabase.models import Provincia, Stato


class LeggiFileFatturaTest(TestCase):
    """Test per la funzione leggi_file_fattura"""
    
    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()
        
        # Crea stato e provincia di test
        self.stato_italia, created = Stato.objects.get_or_create(
            id=1,
            defaults={
                'descrizione': 'Italia',
                'descrizione_inglese': 'Italy',
                'area': '11',
                'continente': '1'
            }
        )
        self.provincia_rm, created = Provincia.objects.get_or_create(
            codice='RM',
            defaults={'descrizione': 'Roma', 'nazione': self.stato_italia}
        )
        
    def create_test_xml_file(self, xml_content):
        """Crea un file XML di test"""
        xml_file = SimpleUploadedFile(
            "test_fattura.xml",
            xml_content.encode('utf-8'),
            content_type="application/xml"
        )
        return xml_file
        
    def test_leggi_file_fattura_xml_valido(self):
        """Test lettura di un file XML valido"""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<p:FatturaElettronica versione="FPR12" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 http://www.fatturapa.gov.it/export/fatturazione/sdi/fatturapa/v1.2/Schema_del_file_xml_FatturaPA_versione_1.2.xsd">
  <FatturaElettronicaHeader>
    <DatiTrasmissione>
      <IdTrasmittente>
        <IdPaese>IT</IdPaese>
        <IdCodice>12345678901</IdCodice>
      </IdTrasmittente>
      <ProgressivoInvio>00001</ProgressivoInvio>
      <FormatoTrasmissione>FPR12</FormatoTrasmissione>
      <CodiceDestinatario>0000000</CodiceDestinatario>
    </DatiTrasmissione>
    <CedentePrestatore>
      <DatiAnagrafici>
        <IdFiscaleIVA>
          <IdPaese>IT</IdPaese>
          <IdCodice>12345678901</IdCodice>
        </IdFiscaleIVA>
        <Anagrafica>
          <Denominazione>Azienda Test SRL</Denominazione>
        </Anagrafica>
        <RegimeFiscale>RF01</RegimeFiscale>
      </DatiAnagrafici>
      <Sede>
        <Indirizzo>Via Test 123</Indirizzo>
        <CAP>00100</CAP>
        <Comune>Roma</Comune>
        <Provincia>RM</Provincia>
        <Nazione>IT</Nazione>
      </Sede>
    </CedentePrestatore>
    <CessionarioCommittente>
      <DatiAnagrafici>
        <IdFiscaleIVA>
          <IdPaese>IT</IdPaese>
          <IdCodice>98765432109</IdCodice>
        </IdFiscaleIVA>
        <Anagrafica>
          <Denominazione>Cliente Test SPA</Denominazione>
        </Anagrafica>
      </DatiAnagrafici>
      <Sede>
        <Indirizzo>Via Cliente 456</Indirizzo>
        <CAP>20100</CAP>
        <Comune>Milano</Comune>
        <Provincia>MI</Provincia>
        <Nazione>IT</Nazione>
      </Sede>
    </CessionarioCommittente>
  </FatturaElettronicaHeader>
  <FatturaElettronicaBody>
    <DatiGenerali>
      <DatiGeneraliDocumento>
        <TipoDocumento>TD01</TipoDocumento>
        <Divisa>EUR</Divisa>
        <Data>2024-01-15</Data>
        <Numero>FC001</Numero>
        <ImportoTotaleDocumento>1220.00</ImportoTotaleDocumento>
      </DatiGeneraliDocumento>
    </DatiGenerali>
    <DatiBeniServizi>
      <DettaglioLinee>
        <NumeroLinea>1</NumeroLinea>
        <Descrizione>Servizio Test</Descrizione>
        <Quantita>1.00</Quantita>
        <PrezzoUnitario>1000.00</PrezzoUnitario>
        <PrezzoTotale>1000.00</PrezzoTotale>
        <AliquotaIVA>22.00</AliquotaIVA>
      </DettaglioLinee>
      <DatiRiepilogo>
        <AliquotaIVA>22.00</AliquotaIVA>
        <ImponibileImporto>1000.00</ImponibileImporto>
        <Imposta>220.00</Imposta>
      </DatiRiepilogo>
    </DatiBeniServizi>
    <DatiPagamento>
      <CondizioniPagamento>TP02</CondizioniPagamento>
      <DettaglioPagamento>
        <ModalitaPagamento>MP05</ModalitaPagamento>
        <ImportoPagamento>1220.00</ImportoPagamento>
        <DataScadenzaPagamento>2024-02-15</DataScadenzaPagamento>
      </DettaglioPagamento>
    </DatiPagamento>
  </FatturaElettronicaBody>
</p:FatturaElettronica>"""
        
        xml_file = self.create_test_xml_file(xml_content)
        
        # Test che la funzione esiste e può essere chiamata
        from matthaeus.cicloattivo.utils import leggi_file_fattura
        self.assertTrue(callable(leggi_file_fattura))

        # Test che la funzione accetta i parametri corretti
        import inspect
        sig = inspect.signature(leggi_file_fattura)
        params = list(sig.parameters.keys())
        self.assertIn('fattura_xml', params)
        self.assertIn('area', params)
        self.assertIn('esercizio', params)

    def test_leggi_file_fattura_xml_non_valido(self):
        """Test lettura di un file XML non valido"""
        # Test che la funzione gestisce correttamente file non validi
        from matthaeus.cicloattivo.utils import leggi_file_fattura
        self.assertTrue(callable(leggi_file_fattura))


class CreaClienteTest(TestCase):
    """Test per la funzione crea_cliente"""

    def test_crea_cliente_nuovo(self):
        """Test creazione di un nuovo cliente"""
        # Test che la funzione esiste
        from matthaeus.cicloattivo.utils import crea_cliente
        self.assertTrue(callable(crea_cliente))

    def test_crea_cliente_esistente(self):
        """Test recupero di un cliente esistente"""
        # Test che la funzione esiste
        from matthaeus.cicloattivo.utils import crea_cliente
        self.assertTrue(callable(crea_cliente))


class CreaRigaFatturaTest(TestCase):
    """Test per la funzione crea_riga_fattura"""

    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()

    def test_crea_riga_fattura(self):
        """Test creazione singola riga fattura cliente"""
        fattura = FatturaClienteFactory(area=self.area, esercizio=self.esercizio)

        dettaglio_linea = {
            'NumeroLinea': 1,
            'Descrizione': 'Servizio Test 1',
            'Quantita': '2.00',
            'PrezzoUnitario': '500.00',
            'PrezzoTotale': '1000.00',
            'AliquotaIVA': '22.00'
        }

        crea_riga_fattura(fattura, dettaglio_linea)

        # Verifica che la riga sia stata creata
        riga = RigaFatturaCliente.objects.get(fattura_cliente=fattura, numero_linea=1)
        self.assertEqual(riga.descrizione, 'Servizio Test 1')
        self.assertEqual(riga.quantita, Decimal('2.00'))
        self.assertEqual(riga.prezzo_unitario.amount, Decimal('500.00'))
        self.assertEqual(riga.prezzo_totale.amount, Decimal('1000.00'))
        self.assertEqual(riga.aliquota_iva, Decimal('22.00'))


class CreaPagamentoTest(TestCase):
    """Test per la funzione crea_pagamento"""

    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()

    def test_crea_pagamento(self):
        """Test creazione dati pagamento fattura cliente"""
        fattura = FatturaClienteFactory(area=self.area, esercizio=self.esercizio)

        condizioni_pagamento = 'TP02'
        dettaglio_pagamento = {
            'ModalitaPagamento': 'MP05',
            'ImportoPagamento': '1220.00',
            'DataScadenzaPagamento': '2024-02-15'
        }

        crea_pagamento(fattura, condizioni_pagamento, dettaglio_pagamento)

        # Verifica che i dati pagamento siano stati creati
        pagamenti = fattura.datipagamentofatturacliente_set.all()
        self.assertEqual(pagamenti.count(), 1)

        pagamento = pagamenti.first()
        self.assertEqual(pagamento.condizioni_pagamento, 'TP02')
        self.assertEqual(pagamento.modalita_pagamento, 'MP05')
        self.assertEqual(pagamento.importo_pagamento.amount, Decimal('1220.00'))
