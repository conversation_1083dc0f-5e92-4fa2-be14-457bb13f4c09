import factory
from decimal import Decimal
from django.utils import timezone
from djmoney.money import Money

from matthaeus.cicloattivo.models import (
    FatturaCliente, RigaFatturaCliente, DatiPagamentoFatturaCliente,
    PagamentoCliente, AllegatoFatturaCliente, RigaRiepilogoFatturaCliente
)
from matthaeus.anagrafica.tests.factories import ClienteFactory
from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory, PianoDeiContiFactory


class FatturaClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = FatturaCliente

    area = factory.SubFactory(AreaFactory)
    esercizio = factory.SubFactory(EsercizioFactory)
    cliente = factory.SubFactory(ClienteFactory)
    data_documento = factory.LazyFunction(timezone.now().date)
    numero_documento = factory.Sequence(lambda n: 'FC%04d' % n)
    tipo_documento = 'TD01'
    divisa = 'EUR'
    ragione_sociale = factory.Faker('company')
    partita_iva = factory.Sequence(lambda n: f"{12345678900 + n}")
    codice_fiscale = factory.Sequence(lambda n: f"TSTCLN{80 + (n % 20):02d}A01H501{n % 10}")
    importo_totale_documento = Money('1000.00', 'EUR')
    imponibile_importo = Money('819.67', 'EUR')
    imposta = Money('180.33', 'EUR')
    totale_fattura = Money('1000.00', 'EUR')
    approvata = False
    pagata = False
    importata = False


class RigaFatturaClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RigaFatturaCliente

    fattura_cliente = factory.SubFactory(FatturaClienteFactory)
    numero_linea = factory.Sequence(lambda n: n + 1)
    descrizione = factory.Faker('text', max_nb_chars=200)
    quantita = Decimal('1.00')
    prezzo_unitario = Money('100.00', 'EUR')
    prezzo_totale = Money('100.00', 'EUR')
    aliquota_iva = Decimal('22.00')


class DatiPagamentoFatturaClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = DatiPagamentoFatturaCliente

    fattura_cliente = factory.SubFactory(FatturaClienteFactory)
    condizioni_pagamento = 'TP02'
    modalita_pagamento = 'MP05'
    importo_pagamento = Money('1000.00', 'EUR')
    data_scadenza_pagamento = factory.LazyFunction(timezone.now().date)


class RigaRiepilogoFatturaClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RigaRiepilogoFatturaCliente

    fattura_cliente = factory.SubFactory(FatturaClienteFactory)
    aliquota_iva = Decimal('22.00')
    imponibile_importo = Money('819.67', 'EUR')
    imposta = Money('180.33', 'EUR')


class AllegatoFatturaClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AllegatoFatturaCliente

    fattura_cliente = factory.SubFactory(FatturaClienteFactory)
    nome = factory.Faker('file_name', extension='pdf')
    descrizione = factory.Faker('text', max_nb_chars=200)
    formato = 'PDF'
    algoritmo_compressione = None


class PagamentoClienteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = PagamentoCliente

    area = factory.SubFactory(AreaFactory)
    esercizio = factory.SubFactory(EsercizioFactory)
    cliente = factory.SubFactory(ClienteFactory)
    conto_pagamento = factory.SubFactory(PianoDeiContiFactory)
    conto_liquidita = factory.SubFactory(PianoDeiContiFactory)
    data_pagamento = factory.LazyFunction(timezone.now().date)
    importo_pagamento = Money('1000.00', 'EUR')
    causale = factory.Faker('text', max_nb_chars=200)
