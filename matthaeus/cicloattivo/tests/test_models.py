from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from djmoney.money import Money

from matthaeus.cicloattivo.models import (
    FatturaCliente, RigaFatturaCliente, RigaRiepilogoFatturaCliente,
    DatiPagamentoFatturaCliente, AllegatoFatturaCliente, PagamentoCliente
)
from matthaeus.cicloattivo.tests.factories import (
    FatturaClienteFactory, RigaFatturaClienteFactory, DatiPagamentoFatturaClienteFactory,
    PagamentoClienteFactory
)
from matthaeus.anagrafica.tests.factories import ClienteFactory
from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory, PianoDeiContiFactory


class FatturaClienteModelTest(TestCase):
    """Test per il modello FatturaCliente"""
    
    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()
        self.cliente = ClienteFactory()
        
    def test_fattura_cliente_creation(self):
        """Test creazione di una fattura cliente"""
        fattura = FatturaClienteFactory(
            area=self.area,
            esercizio=self.esercizio,
            cliente=self.cliente
        )
        
        self.assertIsNotNone(fattura.pk)
        self.assertEqual(fattura.area, self.area)
        self.assertEqual(fattura.esercizio, self.esercizio)
        self.assertEqual(fattura.cliente, self.cliente)
        self.assertEqual(fattura.divisa, 'EUR')
        self.assertFalse(fattura.approvata)
        self.assertFalse(fattura.pagata)
        self.assertFalse(fattura.importata)
        
    def test_fattura_cliente_str(self):
        """Test rappresentazione stringa della fattura"""
        fattura = FatturaClienteFactory(
            numero_documento='FC001',
            data_documento='2024-01-15'
        )

        # Il metodo __str__ usa il formato: "cliente - fatt. n. numero del data"
        expected = f"{fattura.cliente} - fatt. n. FC001 del 2024-01-15"
        self.assertEqual(str(fattura), expected)
        
    def test_fattura_cliente_required_fields(self):
        """Test campi obbligatori"""
        fattura = FatturaCliente()
        
        # Questi campi sono obbligatori
        with self.assertRaises(ValidationError):
            fattura.full_clean()
            
    def test_fattura_cliente_money_fields(self):
        """Test campi monetari"""
        fattura = FatturaClienteFactory()

        # Verifica che i campi monetari siano Money objects
        self.assertIsInstance(fattura.importo_totale_documento, Money)
        self.assertIsInstance(fattura.imponibile_importo, Money)
        self.assertIsInstance(fattura.imposta, Money)
        self.assertEqual(str(fattura.importo_totale_documento.currency), 'EUR')
        
    def test_fattura_cliente_cessionario_fields(self):
        """Test campi del cessionario/committente"""
        fattura = FatturaClienteFactory(
            cessionario_denominazione="Cliente Test SPA",
            cessionario_partita_iva="12345678901",
            cessionario_codice_fiscale="****************",
            cessionario_indirizzo="Via Test 123",
            cessionario_comune="Roma",
            cessionario_cap="00100",
            cessionario_provincia="RM"
        )
        
        self.assertEqual(fattura.cessionario_denominazione, "Cliente Test SPA")
        self.assertEqual(fattura.cessionario_partita_iva, "12345678901")
        self.assertEqual(fattura.cessionario_codice_fiscale, "****************")
        self.assertEqual(fattura.cessionario_indirizzo, "Via Test 123")
        self.assertEqual(fattura.cessionario_comune, "Roma")
        self.assertEqual(fattura.cessionario_cap, "00100")
        self.assertEqual(fattura.cessionario_provincia, "RM")


class RigaFatturaClienteModelTest(TestCase):
    """Test per il modello RigaFatturaCliente"""
    
    def setUp(self):
        self.fattura = FatturaClienteFactory()
        
    def test_riga_fattura_creation(self):
        """Test creazione di una riga fattura"""
        riga = RigaFatturaClienteFactory(
            fattura_cliente=self.fattura,
            numero_linea=1,
            descrizione="Prodotto Test",
            quantita=Decimal('2.00'),
            prezzo_unitario=Money('50.00', 'EUR'),
            prezzo_totale=Money('100.00', 'EUR'),
            aliquota_iva=Decimal('22.00')
        )
        
        self.assertIsNotNone(riga.pk)
        self.assertEqual(riga.fattura_cliente, self.fattura)
        self.assertEqual(riga.numero_linea, 1)
        self.assertEqual(riga.descrizione, "Prodotto Test")
        self.assertEqual(riga.quantita, Decimal('2.00'))
        self.assertEqual(riga.prezzo_unitario.amount, Decimal('50.00'))
        self.assertEqual(riga.prezzo_totale.amount, Decimal('100.00'))
        self.assertEqual(riga.aliquota_iva, Decimal('22.00'))
        
    def test_riga_fattura_str(self):
        """Test rappresentazione stringa della riga"""
        riga = RigaFatturaClienteFactory(
            numero_linea=1,
            descrizione="Prodotto Test"
        )

        # Il metodo __str__ usa il formato: "riga numero - fattura"
        expected = f"riga 1 - {riga.fattura_cliente}"
        self.assertEqual(str(riga), expected)


class DatiPagamentoFatturaClienteModelTest(TestCase):
    """Test per il modello DatiPagamentoFatturaCliente"""
    
    def setUp(self):
        self.fattura = FatturaClienteFactory()
        
    def test_dati_pagamento_creation(self):
        """Test creazione dati pagamento"""
        dati_pagamento = DatiPagamentoFatturaClienteFactory(
            fattura_cliente=self.fattura,
            condizioni_pagamento='TP02',
            modalita_pagamento='MP05',
            importo_pagamento=Money('1000.00', 'EUR')
        )
        
        self.assertIsNotNone(dati_pagamento.pk)
        self.assertEqual(dati_pagamento.fattura_cliente, self.fattura)
        self.assertEqual(dati_pagamento.condizioni_pagamento, 'TP02')
        self.assertEqual(dati_pagamento.modalita_pagamento, 'MP05')
        self.assertEqual(dati_pagamento.importo_pagamento.amount, Decimal('1000.00'))


class PagamentoClienteModelTest(TestCase):
    """Test per il modello PagamentoCliente"""
    
    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()
        self.cliente = ClienteFactory()
        self.conto_pagamento = PianoDeiContiFactory()
        self.conto_liquidita = PianoDeiContiFactory()
        
    def test_pagamento_cliente_creation(self):
        """Test creazione pagamento cliente"""
        pagamento = PagamentoClienteFactory(
            area=self.area,
            esercizio=self.esercizio,
            cliente=self.cliente,
            conto_pagamento=self.conto_pagamento,
            conto_liquidita=self.conto_liquidita,
            importo_pagamento=Money('500.00', 'EUR'),
            causale="Pagamento fattura FC001"
        )
        
        self.assertIsNotNone(pagamento.pk)
        self.assertEqual(pagamento.area, self.area)
        self.assertEqual(pagamento.esercizio, self.esercizio)
        self.assertEqual(pagamento.cliente, self.cliente)
        self.assertEqual(pagamento.conto_pagamento, self.conto_pagamento)
        self.assertEqual(pagamento.conto_liquidita, self.conto_liquidita)
        self.assertEqual(pagamento.importo_pagamento.amount, Decimal('500.00'))
        self.assertEqual(pagamento.causale, "Pagamento fattura FC001")
        
    def test_pagamento_cliente_str(self):
        """Test rappresentazione stringa del pagamento"""
        pagamento = PagamentoClienteFactory(
            cliente=self.cliente,
            data_pagamento='2024-01-15',
            importo_pagamento=Money('500.00', 'EUR')
        )

        # Il metodo __str__ usa il formato: "cliente - data"
        expected = f"{pagamento.cliente} - 2024-01-15"
        self.assertEqual(str(pagamento), expected)


class AllegatoFatturaClienteModelTest(TestCase):
    """Test per il modello AllegatoFatturaCliente"""
    
    def setUp(self):
        self.fattura = FatturaClienteFactory()
        
    def test_allegato_creation(self):
        """Test creazione allegato"""
        allegato = AllegatoFatturaCliente(
            fattura_cliente=self.fattura,
            nome="documento.pdf",
            descrizione="Documento allegato",
            formato="PDF"
        )
        allegato.save()
        
        self.assertIsNotNone(allegato.pk)
        self.assertEqual(allegato.fattura_cliente, self.fattura)
        self.assertEqual(allegato.nome, "documento.pdf")
        self.assertEqual(allegato.descrizione, "Documento allegato")
        self.assertEqual(allegato.formato, "PDF")
        
    def test_allegato_str(self):
        """Test rappresentazione stringa dell'allegato"""
        allegato = AllegatoFatturaCliente(
            nome="documento.pdf",
            descrizione="Documento allegato"
        )

        # Il metodo __str__ restituisce solo il nome
        expected = "documento.pdf"
        self.assertEqual(str(allegato), expected)
