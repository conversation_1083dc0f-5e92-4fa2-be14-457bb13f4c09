from django.test import TestCase
from django.urls import reverse
from django.contrib.admin.sites import AdminSite
from django.http import HttpRequest
from django.contrib.auth import get_user_model

from matthaeus.cicloattivo.tests.factories import (
    FatturaClienteFactory, PagamentoClienteFactory, RigaFatturaClienteFactory,
    DatiPagamentoFatturaClienteFactory
)
from matthaeus.cicloattivo.admin import (
    FatturaClienteAdmin, PagamentoClienteAdmin, AllegatoFatturaClienteInline
)
from matthaeus.cicloattivo.models import FatturaCliente, AllegatoFatturaCliente
from matthaeus.anagrafica.tests.factories import ClienteFactory
from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory
from common.authentication.tests.factories import UtenteMagisterFactory

User = get_user_model()


class FatturaClienteAdminTest(TestCase):
    def setUp(self):
        self.area_corrente = AreaFactory()
        self.esercizio_corrente = EsercizioFactory()
        self.utente_corrente = UtenteMagisterFactory(
            username='test', area_corrente=self.area_corrente,
            esercizio_corrente=self.esercizio_corrente,
        )
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:cicloattivo_fatturacliente_changelist')

    def test_fattura_cliente_changelist(self):
        fattura = FatturaClienteFactory(area=self.area_corrente, esercizio=self.esercizio_corrente)
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_fattura_cliente_add(self):
        url = reverse('matthaeus:cicloattivo_fatturacliente_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_fattura_cliente_change(self):
        fattura = FatturaClienteFactory(area=self.area_corrente, esercizio=self.esercizio_corrente)
        url = reverse('matthaeus:cicloattivo_fatturacliente_change', args=[fattura.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class PagamentoClienteAdminTest(TestCase):
    def setUp(self):
        self.area_corrente = AreaFactory()
        self.esercizio_corrente = EsercizioFactory()
        self.utente_corrente = UtenteMagisterFactory(
            username='test', area_corrente=self.area_corrente,
            esercizio_corrente=self.esercizio_corrente,
        )
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:cicloattivo_pagamentocliente_changelist')

    def test_pagamento_cliente_changelist(self):
        pagamento = PagamentoClienteFactory(area=self.area_corrente, esercizio=self.esercizio_corrente)
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_pagamento_cliente_add(self):
        url = reverse('matthaeus:cicloattivo_pagamentocliente_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_pagamento_cliente_change(self):
        pagamento = PagamentoClienteFactory(area=self.area_corrente, esercizio=self.esercizio_corrente)
        url = reverse('matthaeus:cicloattivo_pagamentocliente_change', args=[pagamento.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class FatturaClienteAdminMethodsTest(TestCase):
    """Test per i metodi specifici dell'admin di FatturaCliente"""

    def setUp(self):
        self.area = AreaFactory()
        self.esercizio = EsercizioFactory()
        self.cliente = ClienteFactory()
        self.site = AdminSite()
        self.admin = FatturaClienteAdmin(FatturaCliente, self.site)

    def test_get_ragione_sociale_cliente_con_cliente_associato(self):
        """Test che verifica il metodo get_ragione_sociale_cliente con cliente associato"""
        fattura = FatturaClienteFactory(
            area=self.area,
            esercizio=self.esercizio,
            cliente=self.cliente,
            cessionario_denominazione="Cliente Test SPA"
        )

        ragione_sociale = self.admin.get_ragione_sociale_cliente(fattura)
        self.assertEqual(ragione_sociale, self.cliente.ragione_sociale)

    def test_get_ragione_sociale_cliente_senza_cliente_associato(self):
        """Test che verifica il metodo get_ragione_sociale_cliente senza cliente associato"""
        # Crea fattura senza cliente ma con dati cessionario
        fattura = FatturaClienteFactory(
            area=self.area,
            esercizio=self.esercizio,
            cessionario_denominazione="Cliente Test SPA",
            cessionario_partita_iva="12345678901"
        )
        fattura.cliente = None
        fattura.save()

        ragione_sociale = self.admin.get_ragione_sociale_cliente(fattura)
        self.assertEqual(ragione_sociale, "Cliente Test SPA")

    def test_get_ragione_sociale_cliente_senza_dati(self):
        """Test che verifica il metodo get_ragione_sociale_cliente senza dati"""
        # Crea fattura senza cliente e senza dati cessionario
        fattura = FatturaClienteFactory(
            area=self.area,
            esercizio=self.esercizio,
            cessionario_denominazione=None,
            cessionario_partita_iva="12345678901"
        )
        fattura.cliente = None
        fattura.save()

        ragione_sociale = self.admin.get_ragione_sociale_cliente(fattura)
        self.assertEqual(ragione_sociale, "-")


class AllegatoFatturaClienteInlineTest(TestCase):
    """Test per l'inline degli allegati"""

    def setUp(self):
        self.site = AdminSite()
        self.inline = AllegatoFatturaClienteInline(AllegatoFatturaCliente, self.site)
        self.request = HttpRequest()

    def test_has_add_permission_false(self):
        """Test che verifica che non è possibile aggiungere allegati"""
        self.assertFalse(self.inline.has_add_permission(self.request))

    def test_has_delete_permission_false(self):
        """Test che verifica che non è possibile eliminare allegati"""
        self.assertFalse(self.inline.has_delete_permission(self.request))

    def test_can_delete_false(self):
        """Test che verifica che can_delete è False"""
        self.assertFalse(self.inline.can_delete)
