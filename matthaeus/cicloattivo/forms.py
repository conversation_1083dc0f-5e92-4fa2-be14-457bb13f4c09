import os

from zipfile import ZipFile

from django import forms
from django.conf import settings
from django.core.files.temp import tempfile

from matthaeus.cicloattivo.utils import leggi_file_fattura
from matthaeus.cicloattivo import models
from common.utils.widgets import MasterMoneyWidget, ContiClientiWidget, MasterMoneyDefaultWidget


class UploadForm(forms.Form):
    xml_file = forms.FileField(
        label='XML file',
        help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB,
        required=False
    )
    zip_file = forms.FileField(
        label='ZIP file',
        help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB,
        required=False
    )

    def save(self, area, esercizio):
        cartella_temporanea = tempfile.mkdtemp()
        data = self.cleaned_data['xml_file']
        elenco_messaggi = []
        if data:
            nome_file_xml = os.path.join(cartella_temporanea, 'file_fattura.xml')
            file_fattura_xml = open(nome_file_xml, 'wb')
            file_fattura_xml.write(data.read())
            file_fattura_xml.close()
            messaggio = leggi_file_fattura(nome_file_xml, area, esercizio)
            if type(messaggio) is str:
                elenco_messaggi.append(messaggio)
        else:
            data = self.cleaned_data['zip_file']
            archivio = ZipFile(data)
            nome_file_zippato = ''
            for file_zippato in archivio.filelist:
                nome_file_zippato = file_zippato.filename
                if not nome_file_zippato.endswith('metaDato.xml'):
                    if nome_file_zippato.endswith('.xml'):
                        archivio.extract(file_zippato, path=cartella_temporanea)
                        file_xml = os.path.join(cartella_temporanea, nome_file_zippato)
                        messaggio = leggi_file_fattura(file_xml, area, esercizio)
                        if type(messaggio) is str:
                            elenco_messaggi.append(messaggio)
        return elenco_messaggi


class RigaFatturaClienteForm(forms.ModelForm):

    class Meta:
        model = models.RigaFatturaCliente
        fields = '__all__'
        widgets = dict(
            descrizione=forms.TextInput(attrs={'style': 'width:400px;'}),
            numero_linea=forms.TextInput(attrs={'style': 'width:65px;'}),
            quantita=forms.TextInput(attrs={'style': 'width:65px;'}),
        )


class RigaRiepilogoFatturaClienteForm(forms.ModelForm):

    class Meta:
        model = models.RigaRiepilogoFatturaCliente
        fields = '__all__'
        widgets = dict(
            natura=forms.Select(attrs={'style': 'width:500px;'}),
            riferimento_normativo=forms.Select(attrs={'style': 'width:400px;'}),
        )


class FatturaClienteForm(forms.ModelForm):

    class Meta:
        model = models.FatturaCliente
        fields = '__all__'
        localized_fields = '__all__'
        widgets = dict(
            numero_documento=forms.TextInput(attrs={'style': 'width:400px;'}),
            causale_pagamento_ritenuta=forms.TextInput(attrs={'style': 'width:400px;'}),
            causale_pagamento_ritenuta_aggiuntiva=forms.TextInput(attrs={'style': 'width:400px;'}),
            bollo_virtuale=forms.TextInput(attrs={'style': 'width:400px;'}),
            ritenuta_cassa=forms.TextInput(attrs={'style': 'width:400px;'}),
            riferimento_amministrazione_cassa=forms.TextInput(attrs={'style': 'width:400px;'}),
            natura_cassa=forms.Select(attrs={'style': 'width:500px;'}),
            tipo_documento=forms.Select(attrs={'style': 'width:500px;'}),
            imponibile_importo=MasterMoneyWidget(),
            imposta=MasterMoneyWidget(),
            importo_totale_documento=MasterMoneyWidget(),
            arrotondamento=MasterMoneyWidget(),
            importo_ritenuta=MasterMoneyWidget(),
            importo_ritenuta_aggiuntiva=MasterMoneyWidget(),
            importo_bollo_virtuale=MasterMoneyWidget(),
            importo_contributo_cassa=MasterMoneyWidget(),
            imponibile_cassa=MasterMoneyWidget(),
            aliquota_ritenuta=forms.TextInput(attrs={'style': 'width:400px;'}),
            aliquota_ritenuta_aggiuntiva=forms.TextInput(attrs={'style': 'width:400px;'}),
            aliquota_contributo_cassa=forms.TextInput(attrs={'style': 'width:400px;'}),
            aliquota_iva_cassa=forms.TextInput(attrs={'style': 'width:400px;'}),
        )


class PagamentoClienteForm(forms.ModelForm):

    class Meta:
        model = models.PagamentoCliente
        fields = '__all__'
        localized_fields = '__all__'
        widgets = dict(
            causale=forms.TextInput(attrs={'style': 'width:400px;'}),
            conto_pagamento=ContiClientiWidget(),
            importo_pagamento=MasterMoneyDefaultWidget(),
        )
