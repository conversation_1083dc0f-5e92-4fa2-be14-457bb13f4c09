from decimal import Decimal
from django.db import models
from django.utils.translation import ugettext_lazy as _
from django.utils.html import format_html
from django.conf import settings
from django.db.models import Max
from django.utils import timezone

from djmoney.models.fields import MoneyField
from djmoney.money import Money

from common.anagraficabase.models import PianoDeiConti, PianoDeiContiLiquidita
from common.anagraficabase.models import Provincia
from matthaeus.anagrafica.models import Anagrafica, Cliente, CausaleContabile
from matthaeus.movimenti.models import MovimentoPrimaNota, DettaglioMovimentoPrimaNota
from matthaeus.cicloattivo.constants import *
from common.anagraficabase.models import AreaAwareModel, EsercizioAwareModel
from common.utils.currency import get_controvalore
from matthaeus.movimenti.utils import get_nuovo_numero


def get_nuovo_numero_protocollo_fattura_cliente(area, esercizio):
    if area and esercizio:
        qs = FatturaCliente.objects.filter(area=area, esercizio=esercizio)
        aggregate = qs.aggregate(Max('numero_protocollo'))
        max = int(aggregate.get('numero_protocollo__max', 0) or 0)
        return max + 1


class FatturaCliente(AreaAwareModel, EsercizioAwareModel):
    numero_protocollo = models.PositiveIntegerField(_('num. protocollo'), null=True, blank=True)
    # ------------------- HEADER ------------------------------------------- #
    # ##### Dati Trasmissione #######
    trasmittente_paese = models.CharField(max_length=2, null=True, blank=True)
    trasmittente_codice = models.CharField(max_length=50, null=True, blank=True)
    progressivo_invio = models.CharField(max_length=200, null=True, blank=True)
    codice_destinatario = models.CharField(max_length=7, null=True, blank=True)
    contatti_trasmittente_telefono = models.CharField(
        max_length=200, null=True, blank=True
    )
    contatti_trasmittente_email = models.CharField(
        max_length=200, null=True, blank=True
    )
    pec_destinatario = models.CharField(max_length=200, null=True, blank=True)
    # ##### Cedente Prestatore #######
    partita_iva = models.CharField(max_length=200, null=True, blank=True)
    codice_fiscale = models.CharField(max_length=200, null=True, blank=True)
    ragione_sociale = models.CharField(max_length=200, null=True, blank=True)
    regime_fiscale = models.CharField(
        max_length=200, choices=REGIME_FISCALE, null=True, blank=True
    )
    indirizzo = models.CharField(max_length=60, null=True, blank=True)
    numero_civico = models.CharField(max_length=8, null=True, blank=True)
    cap = models.CharField(max_length=5, null=True, blank=True)
    comune = models.CharField(max_length=60, null=True, blank=True)
    provincia = models.ForeignKey(
        Provincia, null=True, blank=True, on_delete=models.PROTECT
    )
    nazione = models.CharField(max_length=5, null=True, blank=True)
    ufficio_iscrizione_rea = models.CharField(max_length=2, null=True, blank=True)
    numero_iscrizione_rea = models.CharField(max_length=200, null=True, blank=True)
    capitale_sociale = models.CharField(max_length=200, null=True, blank=True)
    socio_unico = models.CharField(
        max_length=2, choices=SOCIO_UNICO, null=True, blank=True
    )
    stato_liquidazione = models.CharField(max_length=2, null=True, blank=True)
    telefono = models.CharField(max_length=200, null=True, blank=True)
    fax = models.CharField(max_length=200, null=True, blank=True)
    email = models.CharField(max_length=256, null=True, blank=True)
    riferimento_amministrazione = models.CharField(max_length=200, null=True, blank=True)
    # ##### Rappresentante Fiscale #######
    rappresentante_fiscale_partita_iva = models.CharField(max_length=200, null=True, blank=True)
    rappresentante_fiscale_codice_fiscale = models.CharField(max_length=200, null=True, blank=True)
    rappresentante_fiscale_denominazione = models.CharField(max_length=200, null=True, blank=True)
    rappresentante_fiscale_nome = models.CharField(max_length=200, null=True, blank=True)
    rappresentante_fiscale_cognome = models.CharField(max_length=200, null=True, blank=True)
    # ##### Cessionario Committente #######
    cessionario_partita_iva = models.CharField(max_length=200, null=True, blank=True)
    cessionario_codice_fiscale = models.CharField(max_length=200, null=True, blank=True)
    cessionario_denominazione = models.CharField(max_length=80, null=True, blank=True)
    cessionario_indirizzo = models.CharField(max_length=60, null=True, blank=True)
    cessionario_comune = models.CharField(max_length=60, null=True, blank=True)
    cessionario_numero_civico = models.CharField(max_length=8, null=True, blank=True)
    cessionario_cap = models.CharField(max_length=5, null=True, blank=True)
    cessionario_provincia = models.CharField(max_length=2, null=True, blank=True)
    # ------------------- BODY ------------------------------------------- #
    # ##### Dati Generali Documento #######
    tipo_documento = models.CharField(max_length=4, choices=TIPO_DOCUMENTO_SDI, null=True, blank=True)
    divisa = models.CharField(max_length=3, default=settings.VALUTA_DEFAULT)
    data_documento = models.DateField()
    numero_documento = models.CharField(max_length=20)
    importo_totale_documento = MoneyField(
        _('tot. documento'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    arrotondamento = MoneyField(
        _('arrotondamento'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    # ##### Dati Ritenuta #######
    tipo_ritenuta = models.CharField(
        max_length=4, choices=TIPO_RITENUTA, null=True, blank=True
    )
    importo_ritenuta = MoneyField(
        _('importo ritenuta'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    aliquota_ritenuta = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    causale_pagamento_ritenuta = models.CharField(max_length=2, null=True, blank=True)
    # ##### Dati Ritenuta aggiuntiva #######
    tipo_ritenuta_aggiuntiva = models.CharField(
        max_length=4, choices=TIPO_RITENUTA, null=True, blank=True
    )
    importo_ritenuta_aggiuntiva = MoneyField(
        _('importo ritenuta aggiuntiva'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    aliquota_ritenuta_aggiuntiva = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    causale_pagamento_ritenuta_aggiuntiva = models.CharField(max_length=2, null=True, blank=True)
    # ##### Dati Bollo #######
    bollo_virtuale = models.CharField(max_length=2, null=True, blank=True)
    importo_bollo_virtuale = MoneyField(
        _('importo bollo virtuale'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    # ##### Dati Cassa Previdenziale #######
    tipo_cassa_previdenziale = models.CharField(
        max_length=200, choices=TIPO_CASSA_PREVIDENZIALE, null=True, blank=True
    )
    aliquota_contributo_cassa = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    importo_contributo_cassa = MoneyField(
        _('importo contributo cassa'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    imponibile_cassa = MoneyField(
        _('imponibile cassa'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    aliquota_iva_cassa = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    ritenuta_cassa = models.CharField(max_length=5, null=True, blank=True)
    natura_cassa = models.CharField(
        max_length=5, choices=NATURA_CASSA_PREVIDENZIALE, null=True, blank=True
    )
    riferimento_amministrazione_cassa = models.CharField(max_length=200, null=True, blank=True)
    causale = models.CharField(max_length=200, null=True, blank=True)
    dati_contratto_id_documento = models.CharField(max_length=200, null=True, blank=True)
    # ##### Dati Beni Servizi - Dati riepilogo #######
    aliquota_iva = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    imponibile_importo = MoneyField(
        _('imponibile'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    imposta = MoneyField(
        _('imposta'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    totale_fattura = MoneyField(
        _('totale fattura'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    esigibilita_iva = models.CharField(max_length=200, null=True, blank=True)
    approvata = models.BooleanField(default=False)
    pagata = models.BooleanField(default=False)
    cliente = models.ForeignKey(
        Cliente, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('cliente')
    )
    causale_contabile = models.ForeignKey(
        CausaleContabile, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('causale contabile')
    )
    movimento_primanota = models.ForeignKey(
        MovimentoPrimaNota, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('Movimento Prima Nota')
    )
    importata = models.BooleanField(verbose_name=_('importata'), default=False)
    note = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = _('fatture clienti')
        verbose_name = _('fattura cliente')
        ordering = ('-data_documento', '-numero_protocollo')
        unique_together = ('numero_protocollo', 'area', 'esercizio')

    def __str__(self):
        if self.cliente:
            return '%s - fatt. n. %s del %s' % (
                self.cliente, self.numero_documento, self.data_documento
            )
        else:
            return '%s - fatt. n. %s del %s' % (
                self.ragione_sociale, self.numero_documento, self.data_documento
            )

    def get_link_movimento(self):
        if self.movimento_primanota:
            url_file = self.movimento_primanota.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.movimento_primanota))
        else:
            return ''
    get_link_movimento.short_description = _('Mov. Prima Nota')
    get_link_movimento.admin_order_field = 'movimento_primanota'

    def get_consolidata(self):
        if self.movimento_primanota:
            return True
        return False
    get_consolidata.short_description = _('Cons.')
    get_consolidata.boolean = True

    def get_link_cliente(self):
        if self.cliente:
            url_file = self.cliente.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.cliente))
        else:
            return ''
    get_link_cliente.short_description = _('Cliente')
    get_link_cliente.admin_order_field = 'cliente'

    def trova_cliente(self):
        if not self.cliente:
            if self.partita_iva:
                try:
                    cliente = Anagrafica.objects.get(partita_iva=self.partita_iva.strip())
                    return cliente
                except Anagrafica.DoesNotExist:
                    pass
                if len(self.partita_iva) == 13:
                    try:
                        cliente = Anagrafica.objects.get(partita_iva=self.partita_iva[2:])
                        return cliente
                    except Anagrafica.DoesNotExist:
                        pass
            if self.codice_fiscale:
                try:
                    cliente = Anagrafica.objects.get(codice_fiscale=self.codice_fiscale.strip())
                    return cliente
                except Anagrafica.DoesNotExist:
                    pass

    def crea_movimento_primanota(self):
        if self.causale_contabile:
            if self.movimento_primanota:
                if not self.movimento_primanota.consolidato:
                    self.movimento_primanota.delete()
                else:
                    return self.movimento_primanota
            movimento = MovimentoPrimaNota()
            movimento.causale = self.causale_contabile
            movimento.descrizione = self.__str__()
            movimento.data_operazione = self.data_documento
            movimento.data = self.data_documento
            movimento.area = self.area
            movimento.esercizio = self.esercizio
            controvalore = get_controvalore(self.importo_totale_documento, self.area.valuta_default)
            movimento.importo_partita_semplice = controvalore
            movimento.consolidato = False
            movimento.save()
            if self.cliente:
                elenco_dettagli = movimento.dettagliomovimentoprimanota_set.filter(piano_dei_conti__gestione_anagrafica='clienti')
                if elenco_dettagli:
                    for dettaglio in elenco_dettagli:
                        dettaglio.anagrafica = self.cliente
                        dettaglio.save()
            return movimento

    def save(self, *args, **kwargs):
        if not self.numero_protocollo:
            self.numero_protocollo = get_nuovo_numero_protocollo_fattura_cliente(self.area, self.esercizio)
        if not self.cliente:
            self.cliente = self.trova_cliente()
        if self.cliente:
            if not self.ragione_sociale:
                self.ragione_sociale = self.cliente.ragione_sociale
                self.indirizzo = self.cliente.indirizzo
                self.comune = self.cliente.citta
                self.cap = self.cliente.cap
                if self.cliente.stato:
                    self.nazione = self.cliente.stato.codice
                self.telefono = self.cliente.telefono
                self.fax = self.cliente.fax
                self.email = self.cliente.email
                self.partita_iva = self.cliente.partita_iva
                self.codice_fiscale = self.cliente.codice_fiscale
                if self.cliente.provincia and not self.provincia:
                    self.provincia = self.cliente.provincia
            if not self.causale_contabile:
                self.causale_contabile = self.cliente.causale_contabile
        imponibile_totale = Money('0.00', self.divisa)
        imposta_totale = Money('0.00', self.divisa)
        if not self.arrotondamento:
            self.arrotondamento = Money('0.00', self.divisa)
        if self.rigariepilogofatturacliente_set.all():
            for riga_riepilogo in self.rigariepilogofatturacliente_set.all():
                imponibile_totale += riga_riepilogo.imponibile_importo
                imposta_totale += riga_riepilogo.imposta
        else:
            if self.rigafatturacliente_set.all():
                for riga_fattura in self.rigafatturacliente_set.all():
                    imponibile_totale += riga_fattura.prezzo_totale
                    imposta_totale += riga_fattura.prezzo_totale * riga_fattura.aliquota_iva / 100
        self.imponibile_importo = imponibile_totale
        self.imposta = imposta_totale
        self.importo_totale_documento = imponibile_totale + imposta_totale
        totale = Money('0.00', self.divisa)
        if self.datipagamentofatturacliente_set.all():
            for pagamento in self.datipagamentofatturacliente_set.all():
                totale += pagamento.importo_pagamento
        self.totale_fattura = totale
        movimento = self.crea_movimento_primanota()
        if movimento:
            self.movimento_primanota = movimento
        return super(FatturaCliente, self).save(*args, **kwargs)


class RigaRiepilogoFatturaCliente(models.Model):
    fattura_cliente = models.ForeignKey(FatturaCliente, on_delete=models.CASCADE)
    aliquota_iva = models.DecimalField(max_digits=9, decimal_places=2)
    natura = models.CharField(max_length=5, choices=NATURA_CASSA_PREVIDENZIALE, null=True, blank=True)
    spese_accessorie = MoneyField(
        _('spese accessorie'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    arrotondamento = MoneyField(
        _('arrotondamento'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    imponibile_importo = MoneyField(
        _('imponibile importo'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    imposta = MoneyField(
        _('imposta'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    esigibilita_iva = models.CharField(max_length=1, null=True, blank=True, choices=TIPO_ESIGIBILITA_IVA)
    riferimento_normativo = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name = _('Riga Riepilogo Fattura Cliente')
        verbose_name_plural = _('Righe Riepilogo Fattura Cliente')


class DatiPagamentoFatturaCliente(models.Model):
    fattura_cliente = models.ForeignKey(FatturaCliente, on_delete=models.CASCADE)
    condizioni_pagamento = models.CharField(max_length=200, choices=CONDIZIONI_PAGAMENTO)
    modalita_pagamento = models.CharField(max_length=200, choices=MODALITA_PAGAMENTO)
    data_riferimento_termini_pagamento = models.DateField(null=True, blank=True)
    giorni_termini_pagamento = models.PositiveIntegerField(null=True, blank=True)
    data_scadenza_pagamento = models.DateField(null=True, blank=True)
    importo_pagamento = MoneyField(
        _('importo pagamento'), max_digits=10, decimal_places=2, default_currency=settings.VALUTA_DEFAULT
    )
    istituto_finanziario = models.CharField(max_length=200, null=True, blank=True)
    iban = models.CharField(max_length=200, null=True, blank=True)
    abi = models.CharField(max_length=5, null=True, blank=True)
    cab = models.CharField(max_length=5, null=True, blank=True)
    bic = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name = _('Dati Pagamenti Fattura Cliente')
        verbose_name_plural = _('Dati Pagamenti Fattura Cliente')

    def __str__(self):
        return '%s - %s' % (
            self.get_modalita_pagamento_display(), self.importo_pagamento
        )


class RigaFatturaCliente(models.Model):
    fattura_cliente = models.ForeignKey(FatturaCliente, on_delete=models.CASCADE)
    numero_linea = models.PositiveIntegerField(null=True, blank=True)
    tipo_cessione_prestazione = models.CharField(
        max_length=200, choices=TIPO_CESSIONE_PRESTAZIONE, null=True, blank=True
    )
    codice_articolo_tipo = models.CharField(max_length=200, null=True, blank=True)
    codice_articolo_valore = models.CharField(max_length=200, null=True, blank=True)
    descrizione = models.TextField()
    quantita = models.DecimalField(max_digits=9, decimal_places=2, null=True, blank=True)
    unita_misura = models.CharField(max_length=200, null=True, blank=True)
    prezzo_unitario = MoneyField(
        _('prezzo unitario'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    prezzo_totale = MoneyField(
        _('prezzo totale'), max_digits=10, decimal_places=2, null=True,
        blank=True, default_currency=settings.VALUTA_DEFAULT
    )
    aliquota_iva = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    class Meta:
        verbose_name = _('Riga Fattura Cliente')
        verbose_name_plural = _('Righe Fattura Cliente')
        ordering = ('numero_linea', )
        unique_together = ('fattura_cliente', 'numero_linea')

    def __str__(self):
        return 'riga %s - %s' % (self.numero_linea, self.fattura_cliente)

    def save(self, *args, **kwargs):
        if not self.quantita:
            self.quantita = Decimal('1.00')
        if not self.aliquota_iva:
            self.aliquota_iva = ALIQUOTA_IVA_DEFAULT
        if not self.prezzo_totale:
            prezzo_totale = Decimal('0.00')
            if self.prezzo_unitario and self.quantita:
                prezzo_totale = self.prezzo_unitario * self.quantita
            self.prezzo_totale = prezzo_totale
        super(RigaFatturaCliente, self).save(*args, **kwargs)
        self.fattura_cliente.save()


class AllegatoFatturaCliente(models.Model):
    fattura_cliente = models.ForeignKey(FatturaCliente, on_delete=models.CASCADE)
    nome = models.CharField(max_length=200)
    algoritmo_compressione = models.CharField(max_length=200, null=True, blank=True)
    formato = models.CharField(max_length=200, null=True, blank=True)
    descrizione = models.CharField(max_length=200, null=True, blank=True)
    file_allegato = models.FileField(null=True, blank=True)

    class Meta:
        verbose_name = _('Allegato Fattura Cliente')
        verbose_name_plural = _('Allegati Fattura Cliente')
        ordering = ('fattura_cliente', 'nome')

    def __str__(self):
        return str(self.nome)


class PagamentoCliente(AreaAwareModel, EsercizioAwareModel):
    cliente = models.ForeignKey(Cliente, on_delete=models.CASCADE, verbose_name=_('cliente'))
    conto_pagamento = models.ForeignKey(
        PianoDeiConti, on_delete=models.CASCADE, verbose_name=_('conto pagamento'), related_name='conto_pagamento_cliente_fk'
    )
    conto_liquidita = models.ForeignKey(
        PianoDeiContiLiquidita, on_delete=models.CASCADE, verbose_name=_('conto liquidità'), related_name='conto_liquidita_cliente_fk'
    )
    data_pagamento = models.DateField('data pagamento', default=timezone.now)
    importo_pagamento = MoneyField(
        _('importo pagamento'), max_digits=10, decimal_places=2, default_currency=settings.VALUTA_DEFAULT
    )
    causale = models.CharField(max_length=200, null=True, blank=True)
    note = models.TextField(blank=True)
    movimento_primanota = models.ForeignKey(
        MovimentoPrimaNota, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('Movimento Prima Nota')
    )

    class Meta:
        verbose_name = _('Pagamento Cliente')
        verbose_name_plural = _('Pagamenti Clienti')
        ordering = ('-data_pagamento', 'cliente')

    def __str__(self):
        return '%s - %s' % (self.cliente, self.data_pagamento)

    def save(self, *args, **kwargs):
        if not self.causale:
            self.causale = 'Pagamento Cliente %s' % self.cliente
        return super(PagamentoCliente, self).save(*args, **kwargs)

    def get_link_movimento(self):
        if self.movimento_primanota:
            url_file = self.movimento_primanota.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.movimento_primanota))
        else:
            return ''
    get_link_movimento.short_description = _('Mov. Prima Nota')
    get_link_movimento.admin_order_field = 'movimento_primanota'

    def crea_movimento_primanota(self):
        movimento = MovimentoPrimaNota()
        movimento.descrizione = self.__str__()
        movimento.data_operazione = self.data_pagamento
        movimento.numero_operazione = get_nuovo_numero(self.area, self.esercizio)
        movimento.data = self.data_pagamento
        movimento.area = self.area
        movimento.esercizio = self.esercizio
        movimento.save()
        if self.cliente:
            dettaglio_avere = DettaglioMovimentoPrimaNota()
            dettaglio_avere.movimento_primanota = movimento
            dettaglio_avere.piano_dei_conti = self.conto_pagamento
            dettaglio_avere.anagrafica = self.cliente
            dettaglio_avere.importo = self.importo_pagamento
            dettaglio_avere.categoria = 'avere'
            dettaglio_avere.save()
            dettaglio_dare = DettaglioMovimentoPrimaNota()
            dettaglio_dare.movimento_primanota = movimento
            dettaglio_dare.piano_dei_conti = self.conto_liquidita
            dettaglio_dare.importo = self.importo_pagamento
            dettaglio_dare.categoria = 'dare'
            dettaglio_dare.save()
        return movimento

    def save(self, *args, **kwargs):
        if not self.movimento_primanota:
            movimento = self.crea_movimento_primanota()
            if movimento:
                self.movimento_primanota = movimento
        return super(PagamentoCliente, self).save(*args, **kwargs)


class SaldoCliente(Cliente):

    class Meta:
        verbose_name = _('Saldo Cliente')
        verbose_name_plural = _('Saldi Clienti')
        proxy = True
        ordering = ('ragione_sociale', )

    def get_totale_dare(self, area, esercizio):
        totale = Money('0.00', settings.VALUTA_DEFAULT)
        elenco_dettagli_dare = DettaglioMovimentoPrimaNota.objects.filter(
            anagrafica=self, categoria='dare', movimento_primanota__area=area, movimento_primanota__esercizio=esercizio
        )
        for dettaglio_dare in elenco_dettagli_dare:
            totale += dettaglio_dare.importo
        return totale

    def get_totale_avere(self, area, esercizio):
        totale = Money('0.00', settings.VALUTA_DEFAULT)
        elenco_dettagli_avere = DettaglioMovimentoPrimaNota.objects.filter(
            anagrafica=self, categoria='avere', movimento_primanota__area=area, movimento_primanota__esercizio=esercizio
        )
        for dettaglio_avere in elenco_dettagli_avere:
            totale += dettaglio_avere.importo
        return totale

    def get_saldo(self, area, esercizio):
        saldo = Money('0.00', settings.VALUTA_DEFAULT)
        totale_dare = self.get_totale_dare(area, esercizio)
        totale_avere = self.get_totale_avere(area, esercizio)
        saldo = totale_dare - totale_avere
        return saldo
