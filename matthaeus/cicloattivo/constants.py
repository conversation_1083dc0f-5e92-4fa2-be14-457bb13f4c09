REGIME_FISCALE = (
    ('RF01', 'Ordinario'),
    ('RF02', '<PERSON>tri<PERSON>enti minimi (art.1, c.96-117, L. 244/07)'),
    ('RF04', 'Agricoltura e attivita\' connesse e pesca (artt.34 e 34-bis, DPR 633/72)'),
    ('RF05', '<PERSON><PERSON><PERSON> sali e tabacchi (art.74, c.1, DPR. 633/72)'),
    ('RF06', '<PERSON><PERSON><PERSON> fiam<PERSON> (art.74, c.1, DPR  633/72)'),
    ('RF07', 'Editoria (art.74, c.1, DPR  633/72'),
    ('RF08', 'Gestione servizi telefonia pubblica (art.74, c.1, DPR 633/72)'),
    ('RF09', 'Rivendita documenti di trasporto pubblico e di sosta (art.74, c.1, DPR  633/72)'),
    ('RF10', '<PERSON><PERSON>ttenimenti, giochi e altre attivita\' di cui alla tariffa allegata al DPR 640/72 (art.74, c.6, DPR 633/72)'),
    ('RF11', '<PERSON><PERSON><PERSON> viagg<PERSON> e turismo (art.74-ter, DPR 633/72)'),
    ('RF12', 'Agriturismo (art.5, c.2, L. 413/91)'),
    ('RF13', 'Vendite a domicilio (art.25-bis, c.6, DPR  600/73)'),
    ('RF14', 'Rivendita beni usati, oggetti d\'arte, d\'antiquariato o da collezione (art.36, DL 41/95)'),
    ('RF15', 'Agenzie di vendite all\'asta di oggetti d\'arte, antiquariato o da collezione (art.40-bis, DL 41/95)'),
    ('RF16', 'IVA per cassa P.A. (art.6, c.5, DPR 633/72)'),
    ('RF17', 'IVA per cassa (art. 32-bis, DL 83/2012)'),
    ('RF18', 'Altro'),
    ('RF19', 'Regime forfettario (art.1, c.54-89, L. 190/2014'),
)

SOCIO_UNICO = (
    ('SU', 'Socio Unico'),
    ('SM', 'piu\' soci'),
)

STATO_LIQUIDAZIONE = (
    ('LS', 'In liquidazione'),
    ('LN', 'Non in liquidazione'),
)

TIPO_DOCUMENTO_SDI = (
    ('TD01', 'fattura'),
    ('TD02', 'acconto/anticipo su fattura'),
    ('TD03', 'acconto/anticipo su parcella'),
    ('TD04', 'nota di credito'),
    ('TD05', 'nota di debito'),
    ('TD06', 'parcella'),
    ('TD16', 'integrazione fattura reverse charge interno'),
    ('TD17', 'integrazione/autofattura per acquisto servizi dall\'estero'),
    ('TD18', 'integrazione per acquisto di beni intracomunitari'),
    ('TD19', 'integrazione/autofattura per acquisto di beni ex art.17 c.2 DPR 633/72'),
    ('TD20', 'autofattura per regolarizzazione e integrazione delle fatture (ex art.6 c.8 d.lgs. 471/97  o  art.46 c.5 D.L. 331/93)'),
    ('TD21', 'autofattura per splafonamento'),
    ('TD22', 'estrazione beni da Deposito IVA'),
    ('TD23', 'estrazione beni da Deposito IVA con versamento dell\'IVA'),
    ('TD24', 'fattura differita di cui all\'art. 21, comma 4, lett. a)'),
    ('TD25', 'fattura differita di cui all\'art. 21, comma 4, terzo periodo lett. b)'),
    ('TD26', 'cessione di beni ammortizzabili e per passaggi interni (ex art.36 DPR 633/72)'),
    ('TD27', 'fattura per autoconsumo o per cessioni gratuite senza rivalsa'),
)

TIPO_RITENUTA = (
    ('RT01', 'ritenuta pers. fisiche'),
    ('RT02', 'ritenuta pers. giurid.'),
    ('RT03', 'contributo INPS'),
    ('RT04', 'contributo ENASARCO'),
    ('RT05', 'contributo ENPAM'),
    ('RT06', 'altro contributo previdenziale'),
)

TIPO_CASSA_PREVIDENZIALE = (
    ('TC01', 'Cassa nazionale previdenza e assistenza avvocati e procuratori legali'),
    ('TC02', 'Cassa previdenza dottori commercialisti'),
    ('TC03', 'Cassa previdenza e assistenza geometri'),
    ('TC04', 'Cassa nazionale previdenza e assistenza ingegneri e architetti liberi professionisti'),
    ('TC05', 'Cassa nazionale del notariato'),
    ('TC06', 'Cassa nazionale previdenza e assistenza ragionieri e periti commerciali'),
    ('TC07', 'Ente nazionale assistenza agenti e rappresentanti di commercio (ENASARCO)'),
    ('TC08', 'Ente nazionale previdenza e assistenza consulenti del lavoro (ENPACL)'),
    ('TC09', 'Ente nazionale previdenza e assistenza medici (ENPAM)'),
    ('TC10', 'Ente nazionale previdenza e assistenza farmacisti (ENPAF)'),
    ('TC11', 'Ente nazionale previdenza e assistenza veterinari (ENPAV)'),
    ('TC12', 'Ente nazionale previdenza e assistenza impiegati dell\'agricoltura (ENPAIA)'),
    ('TC13', 'Fondo previdenza impiegati imprese di spedizione e agenzie marittime'),
    ('TC14', 'Istituto nazionale previdenza giornalisti italiani (INPGI)'),
    ('TC15', 'Opera nazionale assistenza orfani sanitari italiani (ONAOSI)'),
    ('TC16', 'Cassa autonoma assistenza integrativa giornalisti italiani (CASAGIT)'),
    ('TC17', 'Ente previdenza periti industriali e periti industriali laureati (EPPI)'),
    ('TC18', 'Ente previdenza e assistenza pluricategoriale (EPAP)'),
    ('TC19', 'Ente nazionale previdenza e assistenza biologi (ENPAB)'),
    ('TC20', 'Ente nazionale previdenza e assistenza professione infermieristica (ENPAPI)'),
    ('TC21', 'Ente nazionale previdenza e assistenza psicologi (ENPAP)'),
    ('TC22', 'INPS'),
)

NATURA_CASSA_PREVIDENZIALE = (
    ('N1', 'N1 escluse ex art. 15'),
    ('N2', 'N2 non soggette'),
    ('N2.1', 'N2.1 non soggette ad IVA ai sensi degli artt. da 7 a 7-septies del DPR 633/72'),
    ('N2.2', 'N2.2 non soggette - altri casi'),
    ('N3', 'N3 non imponibili'),
    ('N3.1', 'N3.1 non imponibili - esportazioni'),
    ('N3.2', 'N3.2 non imponibili - cessioni intracomunitarie'),
    ('N3.3', 'N3.3 non imponibili - cessioni verso San Marino'),
    ('N3.4', 'N3.4 non imponibili - operazioni assimilate alle cessioni all\'esportazione'),
    ('N3.5', 'N3.5 non imponibili - a seguito di dichiarazioni d\'intento'),
    ('N3.6', 'N3.6 non imponibili - altre operazioni che non concorrono alla formazione del plafond'),
    ('N4', 'N4 esenti'),
    ('N5', 'N5 regime del margine / IVA non esposta in fattura'),
    ('N6', 'N6 inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'),
    ('N6.1', 'N6.1 inversione contabile - cessione di rottami e altri materiali di recupero'),
    ('N6.2', 'N6.2 inversione contabile - cessione di oro e argento puro'),
    ('N6.3', 'N6.3 inversione contabile - subappalto nel settore edile'),
    ('N6.4', 'N6.4 inversione contabile - cessione di fabbricati'),
    ('N6.5', 'N6.5 inversione contabile - cessione di telefoni cellulari'),
    ('N6.6', 'N6.6 inversione contabile - cessione di prodotti elettronici'),
    ('N6.7', 'N6.7 inversione contabile - prestazioni comparto edile e settori connessi'),
    ('N6.8', 'N6.8 inversione contabile - operazioni settore energetico'),
    ('N6.9', 'N6.9 inversione contabile - altri casi'),
    ('N7', 'N7 IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)'),
)

CONDIZIONI_PAGAMENTO = (
    ('TP01', 'pagamento a rate'),
    ('TP02', 'pagamento completo'),
    ('TP03', 'anticipo'),
)

MODALITA_PAGAMENTO = (
    ('MP01', 'contanti'),
    ('MP02', 'assegno'),
    ('MP03', 'assegno circolare'),
    ('MP04', 'contanti presso Tesoreria'),
    ('MP05', 'bonifico'),
    ('MP06', 'vaglia cambiario'),
    ('MP07', 'bollettino bancario'),
    ('MP08', 'carta di pagamento'),
    ('MP09', 'RID'),
    ('MP10', 'RID utenze'),
    ('MP11', 'RID veloce'),
    ('MP12', 'RIBA'),
    ('MP13', 'MAV'),
    ('MP14', 'quietanza erario'),
    ('MP15', 'giroconto su conti di contabilita\' speciale'),
    ('MP16', 'domiciliazione bancaria'),
    ('MP17', 'domiciliazione postale'),
    ('MP18', 'bollettino di c/c postale'),
    ('MP19', 'SEPA Direct Debit'),
    ('MP20', 'SEPA Direct Debit CORE'),
    ('MP21', 'SEPA Direct Debit B2B'),
    ('MP22', 'Trattenuta su somme gia\' riscosse'),
    ('MP23', 'PagoPA'),
)

TIPO_CESSIONE_PRESTAZIONE = (
    ('SC', 'Sconto'),
    ('PR', 'Premio'),
    ('AB', 'Abbuono'),
    ('AC', 'Spesa Accessoria'),
)

CENTRO_COSTO = (
    ('MC', 'MasterCom'),
    ('MV', 'MasterVoice'),
    ('MT', 'MasterTraining'),
)

TIPO_ESIGIBILITA_IVA = (
    ('I', 'IVA ad esigibilita\' immediata'),
    ('D', 'IVA ad esigibilit\' differita'),
    ('S', 'Scissione dei pagamenti'),
)

ALIQUOTA_IVA_DEFAULT = 22
