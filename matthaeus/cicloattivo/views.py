# -*- coding: utf-8 -*-

from django.contrib import messages
from django.views.generic.edit import FormView

from matthaeus.cicloattivo import forms
from matthaeus.admin import site


class UploadView(FormView):
    template_name = 'admin/cicloattivo/upload.html'
    form_class = forms.UploadForm
    success_url = '../'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(site.each_context(self.request))
        return context

    def dispatch(self, request, *args, **kwargs):
        request.current_app = 'matthaeus'
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        if self.request.user:
            area = self.request.user.area_corrente
            esercizio = self.request.user.esercizio_corrente
        elenco_messaggi = form.save(area=area, esercizio=esercizio)
        if type(elenco_messaggi) is list:
            for msg in elenco_messaggi:
                messages.error(self.request, msg)
        else:
            msg = 'File caricati con successo!'
            messages.success(self.request, msg)
        return super(UploadView, self).form_valid(form)
