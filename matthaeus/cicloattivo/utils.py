import base64
import os
from decimal import Decimal

import xmltodict

from django.core.files.base import ContentFile


def crea_riga_fattura(fattura_cliente, dettaglio_linea):
    from matthaeus.cicloattivo.models import RigaFatturaCliente
    try:
        riga_fattura_cliente = RigaFatturaCliente.objects.get(fattura_cliente=fattura_cliente, numero_linea=dettaglio_linea['NumeroLinea'])
    except RigaFatturaCliente.DoesNotExist:
        riga_fattura_cliente = RigaFatturaCliente()
    riga_fattura_cliente.numero_linea = dettaglio_linea['NumeroLinea']
    # POTREBBE ESSERE MULTIPLO: DA GESTIRE
    # if 'CodiceArticolo' in dettaglio_linea:
    #     riga_fattura_cliente.codice_articolo_tipo = dettaglio_linea['CodiceArticolo']['CodiceTipo']
    #     riga_fattura_cliente.codice_articolo_valore = dettaglio_linea['CodiceArticolo']['CodiceValore']
    if 'TipoCessionePrestazione' in dettaglio_linea:
        riga_fattura_cliente.numero_linea = dettaglio_linea['NumeroLinea']
    riga_fattura_cliente.descrizione = dettaglio_linea['Descrizione']
    if 'Quantita' in dettaglio_linea:
        riga_fattura_cliente.quantita = dettaglio_linea['Quantita']
    if 'UnitaMisura' in dettaglio_linea:
        riga_fattura_cliente.unita_misura = dettaglio_linea['UnitaMisura']
    riga_fattura_cliente.prezzo_unitario = dettaglio_linea['PrezzoUnitario']
    riga_fattura_cliente.prezzo_totale = dettaglio_linea['PrezzoTotale']
    riga_fattura_cliente.aliquota_iva = dettaglio_linea['AliquotaIVA']
    riga_fattura_cliente.fattura_cliente = fattura_cliente
    riga_fattura_cliente.save()


def crea_pagamento(fattura_cliente, condizioni_pagamento, dettaglio_pagamento):
    from matthaeus.cicloattivo.models import DatiPagamentoFatturaCliente
    dati_pagamento_sdi = DatiPagamentoFatturaCliente()
    dati_pagamento_sdi.fattura_cliente = fattura_cliente
    dati_pagamento_sdi.condizioni_pagamento = condizioni_pagamento
    dati_pagamento_sdi.modalita_pagamento = dettaglio_pagamento['ModalitaPagamento']
    if 'DataRiferimentoTerminiPagamento' in dettaglio_pagamento:
        dati_pagamento_sdi.data_riferimento_termini_pagamento = dettaglio_pagamento['DataRiferimentoTerminiPagamento']
    if 'GiorniTerminiPagamento' in dettaglio_pagamento:
        dati_pagamento_sdi.giorni_termini_pagamento = dettaglio_pagamento['GiorniTerminiPagamento']
    if 'DataScadenzaPagamento' in dettaglio_pagamento:
        dati_pagamento_sdi.data_scadenza_pagamento = dettaglio_pagamento['DataScadenzaPagamento']
    dati_pagamento_sdi.importo_pagamento = dettaglio_pagamento['ImportoPagamento']
    if 'IstitutoFinanziario' in dettaglio_pagamento:
        dati_pagamento_sdi.istituto_finanziario = dettaglio_pagamento['IstitutoFinanziario']
    if 'IBAN' in dettaglio_pagamento:
        dati_pagamento_sdi.iban = dettaglio_pagamento['IBAN']
    if 'ABI' in dettaglio_pagamento:
        dati_pagamento_sdi.abi = dettaglio_pagamento['ABI']
    if 'CAB' in dettaglio_pagamento:
        dati_pagamento_sdi.cab = dettaglio_pagamento['CAB']
    if 'BIC' in dettaglio_pagamento:
        dati_pagamento_sdi.bic = dettaglio_pagamento['BIC']
    dati_pagamento_sdi.save()


def crea_cliente(fattura_cliente):
    from matthaeus.anagrafica.models import Anagrafica
    cliente = None
    # Cerca il cliente usando i dati del cessionario/committente (nostro cliente)
    if fattura_cliente.cessionario_partita_iva:
        try:
            cliente = Anagrafica.objects.get(partita_iva=fattura_cliente.cessionario_partita_iva)
        except Anagrafica.DoesNotExist:
            pass
    if not cliente:
        if fattura_cliente.cessionario_codice_fiscale:
            try:
                cliente = Anagrafica.objects.get(codice_fiscale=fattura_cliente.cessionario_codice_fiscale)
            except Anagrafica.DoesNotExist:
                pass
    if not cliente:
        nuovo_cliente = Anagrafica()
    else:
        nuovo_cliente = cliente

    # Mappa i campi del cessionario/committente sui campi dell'anagrafica
    mapping_campi = {
        'ragione_sociale': 'cessionario_denominazione',
        'indirizzo': 'cessionario_indirizzo',
        'partita_iva': 'cessionario_partita_iva',
        'codice_fiscale': 'cessionario_codice_fiscale',
        'cap': 'cessionario_cap',
        'citta': 'cessionario_comune',
    }

    for campo_anagrafica, campo_fattura in mapping_campi.items():
        if not getattr(nuovo_cliente, campo_anagrafica):
            valore_campo = getattr(fattura_cliente, campo_fattura)
            if valore_campo:
                setattr(nuovo_cliente, campo_anagrafica, valore_campo)

    # Gestione provincia (campo speciale)
    if not nuovo_cliente.provincia_id and fattura_cliente.cessionario_provincia:
        nuovo_cliente.provincia_id = fattura_cliente.cessionario_provincia

    nuovo_cliente.cliente = True
    nuovo_cliente.save()
    return nuovo_cliente


def leggi_file_fattura(fattura_xml, area, esercizio):
    from matthaeus.cicloattivo.models import FatturaCliente, RigaRiepilogoFatturaCliente, AllegatoFatturaCliente
    from common.anagraficabase.models import Provincia

    if fattura_xml:
        file_fattura_xml = open(fattura_xml, 'rb')
        try:
            contenuto_file = file_fattura_xml.read().decode("utf-8")
        except:
            split_nome_file = os.path.split(fattura_xml)[-1]
            return 'ERRORE! file fattura %s non importata!' % split_nome_file
        file_fattura_xml.close()
        stringa_scazzata = ' xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"'
        contenuto_file = contenuto_file.replace(stringa_scazzata, '')
        fattura = xmltodict.parse(contenuto_file)
        root = None
        if list(fattura.keys()):
            chiave_root = list(fattura.keys())[0]
            root = fattura[chiave_root]
        if root:

            fattura_elettronica_header = root['FatturaElettronicaHeader']
            fattura_elettronica_body = root['FatturaElettronicaBody']

            # DATI TRASMISSIONE
            dati_trasmissione = fattura_elettronica_header['DatiTrasmissione']

            # CEDENTE PRESTATORE
            cedente_prestatore = fattura_elettronica_header['CedentePrestatore']
            dati_anagrafici_cedente = cedente_prestatore['DatiAnagrafici']
            sede_cedente = cedente_prestatore['Sede']

            # CESSIONARIO COMMITTENTE
            cessionario_committente = fattura_elettronica_header['CessionarioCommittente']
            dati_anagrafici_cessionario = cessionario_committente['DatiAnagrafici']
            sede_cessionario = cessionario_committente['Sede']

            # DATI GENERALI DOCUMENTO
            dati_generali_documento = fattura_elettronica_body['DatiGenerali']['DatiGeneraliDocumento']

            # DATI BENI SERVIZI
            dati_beni_servizi = fattura_elettronica_body['DatiBeniServizi']

            fattura_cliente = FatturaCliente()
            fattura_cliente.area = area
            fattura_cliente.esercizio = esercizio

            # DATI TRASMISSIONE
            fattura_cliente.trasmittente_paese = dati_trasmissione['IdTrasmittente']['IdPaese']
            fattura_cliente.trasmittente_codice = dati_trasmissione['IdTrasmittente']['IdCodice']
            fattura_cliente.progressivo_invio = dati_trasmissione['ProgressivoInvio']
            fattura_cliente.codice_destinatario = dati_trasmissione['CodiceDestinatario']
            if 'ContattiTrasmittente' in dati_trasmissione:
                if 'Telefono' in dati_trasmissione['ContattiTrasmittente']:
                    fattura_cliente.contatti_trasmittente_telefono = dati_trasmissione['ContattiTrasmittente']['Telefono']
                if 'Email' in dati_trasmissione['ContattiTrasmittente']:
                    fattura_cliente.contatti_trasmittente_email = dati_trasmissione['ContattiTrasmittente']['Email']
            if 'PECDestinatario' in dati_trasmissione:
                fattura_cliente.pec_destinatario = dati_trasmissione['PECDestinatario']

            # CEDENTE PRESTATORE
            if 'IdFiscaleIVA' in dati_anagrafici_cedente:
                fattura_cliente.partita_iva = dati_anagrafici_cedente['IdFiscaleIVA']['IdCodice']
            if 'CodiceFiscale' in dati_anagrafici_cedente:
                fattura_cliente.codice_fiscale = dati_anagrafici_cedente['CodiceFiscale']
            if 'Anagrafica' in dati_anagrafici_cedente:
                if 'Denominazione' in dati_anagrafici_cedente['Anagrafica']:
                    fattura_cliente.ragione_sociale = dati_anagrafici_cedente['Anagrafica']['Denominazione']
            if 'RegimeFiscale' in dati_anagrafici_cedente:
                fattura_cliente.regime_fiscale = dati_anagrafici_cedente['RegimeFiscale']

            fattura_cliente.indirizzo = sede_cedente['Indirizzo']
            if 'NumeroCivico' in sede_cedente:
                fattura_cliente.numero_civico = sede_cedente['NumeroCivico']
            fattura_cliente.cap = sede_cedente['CAP']
            fattura_cliente.comune = sede_cedente['Comune']
            if 'Provincia' in sede_cedente:
                fattura_cliente.provincia_id = sede_cedente['Provincia']
            fattura_cliente.nazione = sede_cedente['Nazione']

            if 'Contatti' in cedente_prestatore:
                if 'Telefono' in cedente_prestatore['Contatti']:
                    fattura_cliente.telefono = cedente_prestatore['Contatti']['Telefono']
                if 'Fax' in cedente_prestatore['Contatti']:
                    fattura_cliente.fax = cedente_prestatore['Contatti']['Fax']
                if 'Email' in cedente_prestatore['Contatti']:
                    fattura_cliente.email = cedente_prestatore['Contatti']['Email']

            # CESSIONARIO COMMITTENTE
            if 'IdFiscaleIVA' in dati_anagrafici_cessionario:
                fattura_cliente.cessionario_partita_iva = dati_anagrafici_cessionario['IdFiscaleIVA']['IdCodice']
            if 'CodiceFiscale' in dati_anagrafici_cessionario:
                fattura_cliente.cessionario_codice_fiscale = dati_anagrafici_cessionario['CodiceFiscale']
            if 'Anagrafica' in dati_anagrafici_cessionario:
                if 'Denominazione' in dati_anagrafici_cessionario['Anagrafica']:
                    fattura_cliente.cessionario_denominazione = dati_anagrafici_cessionario['Anagrafica']['Denominazione']

            fattura_cliente.cessionario_indirizzo = sede_cessionario['Indirizzo']
            if 'NumeroCivico' in sede_cessionario:
                fattura_cliente.cessionario_numero_civico = sede_cessionario['NumeroCivico']
            fattura_cliente.cessionario_cap = sede_cessionario['CAP']
            fattura_cliente.cessionario_comune = sede_cessionario['Comune']
            if 'Provincia' in sede_cessionario:
                fattura_cliente.cessionario_provincia = sede_cessionario['Provincia']

            # DATI GENERALI DOCUMENTO
            fattura_cliente.tipo_documento = dati_generali_documento['TipoDocumento']
            fattura_cliente.divisa = dati_generali_documento['Divisa']
            fattura_cliente.data_documento = dati_generali_documento['Data']
            fattura_cliente.numero_documento = dati_generali_documento['Numero']
            fattura_cliente.importo_totale_documento = dati_generali_documento['ImportoTotaleDocumento']
            if 'Arrotondamento' in dati_generali_documento:
                fattura_cliente.arrotondamento = dati_generali_documento['Arrotondamento']

            # DATI RITENUTA
            if 'DatiRitenuta' in dati_generali_documento:
                dati_ritenuta = dati_generali_documento['DatiRitenuta']
                fattura_cliente.tipo_ritenuta = dati_ritenuta['TipoRitenuta']
                fattura_cliente.importo_ritenuta = dati_ritenuta['ImportoRitenuta']
                fattura_cliente.aliquota_ritenuta = dati_ritenuta['AliquotaRitenuta']
                fattura_cliente.causale_pagamento_ritenuta = dati_ritenuta['CausalePagamento']

            # DATI CASSA PREVIDENZIALE
            if 'DatiCassaPrevidenziale' in dati_generali_documento:
                dati_cassa = dati_generali_documento['DatiCassaPrevidenziale']
                fattura_cliente.tipo_cassa_previdenziale = dati_cassa['TipoCassa']
                fattura_cliente.aliquota_contributo_cassa = dati_cassa['AlCassa']
                fattura_cliente.importo_contributo_cassa = dati_cassa['ImportoContributoCassa']
                if 'ImponibileCassa' in dati_cassa:
                    fattura_cliente.imponibile_cassa = dati_cassa['ImponibileCassa']
                if 'AliquotaIVA' in dati_cassa:
                    fattura_cliente.aliquota_iva_cassa = dati_cassa['AliquotaIVA']
                if 'Ritenuta' in dati_cassa:
                    fattura_cliente.ritenuta_cassa = dati_cassa['Ritenuta']
                if 'Natura' in dati_cassa:
                    fattura_cliente.natura_cassa = dati_cassa['Natura']
                if 'RiferimentoAmministrazione' in dati_cassa:
                    fattura_cliente.riferimento_amministrazione_cassa = dati_cassa['RiferimentoAmministrazione']

            fattura_cliente.importata = True
            fattura_cliente.save()

            # DETTAGLIO LINEE
            if 'DettaglioLinee' in dati_beni_servizi:
                dettaglio_linee = dati_beni_servizi['DettaglioLinee']
                if isinstance(dettaglio_linee, list):
                    for dettaglio_linea in dettaglio_linee:
                        crea_riga_fattura(fattura_cliente, dettaglio_linea)
                else:
                    crea_riga_fattura(fattura_cliente, dettaglio_linee)

            # DATI RIEPILOGO
            if 'DatiRiepilogo' in dati_beni_servizi:
                dati_riepilogo = dati_beni_servizi['DatiRiepilogo']
                if isinstance(dati_riepilogo, list):
                    for riga_riepilogo in dati_riepilogo:
                        riga_riepilogo_fattura = RigaRiepilogoFatturaCliente()
                        riga_riepilogo_fattura.fattura_cliente = fattura_cliente
                        riga_riepilogo_fattura.aliquota_iva = riga_riepilogo['AliquotaIVA']
                        if 'Natura' in riga_riepilogo:
                            riga_riepilogo_fattura.natura = riga_riepilogo['Natura']
                        if 'SpeseAccessorie' in riga_riepilogo:
                            riga_riepilogo_fattura.spese_accessorie = riga_riepilogo['SpeseAccessorie']
                        if 'Arrotondamento' in riga_riepilogo:
                            riga_riepilogo_fattura.arrotondamento = riga_riepilogo['Arrotondamento']
                        riga_riepilogo_fattura.imponibile_importo = riga_riepilogo['ImponibileImporto']
                        riga_riepilogo_fattura.imposta = riga_riepilogo['Imposta']
                        if 'EsigibilitaIVA' in riga_riepilogo:
                            riga_riepilogo_fattura.esigibilita_iva = riga_riepilogo['EsigibilitaIVA']
                        if 'RiferimentoNormativo' in riga_riepilogo:
                            riga_riepilogo_fattura.riferimento_normativo = riga_riepilogo['RiferimentoNormativo']
                        riga_riepilogo_fattura.save()
                else:
                    riga_riepilogo_fattura = RigaRiepilogoFatturaCliente()
                    riga_riepilogo_fattura.fattura_cliente = fattura_cliente
                    riga_riepilogo_fattura.aliquota_iva = dati_riepilogo['AliquotaIVA']
                    if 'Natura' in dati_riepilogo:
                        riga_riepilogo_fattura.natura = dati_riepilogo['Natura']
                    if 'SpeseAccessorie' in dati_riepilogo:
                        riga_riepilogo_fattura.spese_accessorie = dati_riepilogo['SpeseAccessorie']
                    if 'Arrotondamento' in dati_riepilogo:
                        riga_riepilogo_fattura.arrotondamento = dati_riepilogo['Arrotondamento']
                    riga_riepilogo_fattura.imponibile_importo = dati_riepilogo['ImponibileImporto']
                    riga_riepilogo_fattura.imposta = dati_riepilogo['Imposta']
                    if 'EsigibilitaIVA' in dati_riepilogo:
                        riga_riepilogo_fattura.esigibilita_iva = dati_riepilogo['EsigibilitaIVA']
                    if 'RiferimentoNormativo' in dati_riepilogo:
                        riga_riepilogo_fattura.riferimento_normativo = dati_riepilogo['RiferimentoNormativo']
                    riga_riepilogo_fattura.save()

            # DATI PAGAMENTO
            if 'DatiPagamento' in fattura_elettronica_body:
                dati_pagamento = fattura_elettronica_body['DatiPagamento']
                condizioni_pagamento = dati_pagamento['CondizioniPagamento']
                dettaglio_pagamento = dati_pagamento['DettaglioPagamento']
                if isinstance(dettaglio_pagamento, list):
                    for pagamento in dettaglio_pagamento:
                        crea_pagamento(fattura_cliente, condizioni_pagamento, pagamento)
                else:
                    crea_pagamento(fattura_cliente, condizioni_pagamento, dettaglio_pagamento)

            # ALLEGATI
            if 'Allegati' in fattura_elettronica_body:
                allegati = fattura_elettronica_body['Allegati']
                if isinstance(allegati, list):
                    for allegato in allegati:
                        allegato_fattura = AllegatoFatturaCliente()
                        allegato_fattura.fattura_cliente = fattura_cliente
                        allegato_fattura.nome = allegato['NomeAttachment']
                        if 'AlgoritmoCompressione' in allegato:
                            allegato_fattura.algoritmo_compressione = allegato['AlgoritmoCompressione']
                        if 'FormatoAttachment' in allegato:
                            allegato_fattura.formato = allegato['FormatoAttachment']
                        if 'DescrizioneAttachment' in allegato:
                            allegato_fattura.descrizione = allegato['DescrizioneAttachment']
                        if 'Attachment' in allegato:
                            contenuto_allegato = base64.b64decode(allegato['Attachment'])
                            nome_file = allegato['NomeAttachment']
                            allegato_fattura.file_allegato.save(nome_file, ContentFile(contenuto_allegato))
                        allegato_fattura.save()
                else:
                    allegato_fattura = AllegatoFatturaCliente()
                    allegato_fattura.fattura_cliente = fattura_cliente
                    allegato_fattura.nome = allegati['NomeAttachment']
                    if 'AlgoritmoCompressione' in allegati:
                        allegato_fattura.algoritmo_compressione = allegati['AlgoritmoCompressione']
                    if 'FormatoAttachment' in allegati:
                        allegato_fattura.formato = allegati['FormatoAttachment']
                    if 'DescrizioneAttachment' in allegati:
                        allegato_fattura.descrizione = allegati['DescrizioneAttachment']
                    if 'Attachment' in allegati:
                        contenuto_allegato = base64.b64decode(allegati['Attachment'])
                        nome_file = allegati['NomeAttachment']
                        allegato_fattura.file_allegato.save(nome_file, ContentFile(contenuto_allegato))
                    allegato_fattura.save()

            # CREA CLIENTE SE NON ESISTE
            cliente = crea_cliente(fattura_cliente)
            fattura_cliente.cliente = cliente
            fattura_cliente.save()

            return fattura_cliente
