from decimal import Decimal
from django.db.models import (
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Prefetch
)
from django.db.models import DecimalField, DateField
from django.utils.translation import ugettext_lazy as _
from django.conf import settings
from django.utils import timezone
from datetime import timedel<PERSON>, date

from preferences import preferences
from graphos.sources.simple import SimpleDataSource
from graphos.renderers.highcharts import ColumnChart, <PERSON><PERSON>hart, LineChart
from djmoney.money import Money

from matthaeus.movimenti.models import (
    BilancioConto, PreventivoCentroDiCosto,
    ConsuntivoCentroDiCosto, DettaglioMovimentoPrimaNota, DettaglioMovimentoAnalitico,
    BilancioContoPreventivo, MovimentoPrimaNota, ConversioneDettaglioMovimentoPrimaNota
)
from common.utils.currency import get_elenco_valuta_default, get_controvalore

from common.anagraficabase.models import PianoDeiConti, CentroDiCosto
from common.anagraficabase.models import AbilitazionePianoDeiConti, AbilitazioneCentroDiCosto
from common.anagraficabase.models import StatisticheGrafici


def get_nuovo_numero(area, esercizio):
    if area and esercizio:
        qs = MovimentoPrimaNota.objects.filter(area=area, esercizio=esercizio)
        aggregate = qs.aggregate(Max('numero_operazione'))
        max = int(aggregate.get('numero_operazione__max', 0) or 0)
        return max + 1


def get_giorno_attuale():
    return timezone.now().strftime("%d/%m/%Y")


def get_movimenti_piano_dei_conti(piano_dei_conti, area, esercizio, data_inizio=None, data_fine=None):
    elenco_aree = area.get_descendants(include_self=True)
    elenco_movimenti = DettaglioMovimentoPrimaNota.objects.filter(
        piano_dei_conti=piano_dei_conti,
        movimento_primanota__area__in=elenco_aree,
        movimento_primanota__esercizio=esercizio,
    ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
    if data_inizio:
        elenco_movimenti = elenco_movimenti.filter(
            movimento_primanota__data_operazione__gte=data_inizio,
        ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
    if data_fine:
        elenco_movimenti = elenco_movimenti.filter(
            movimento_primanota__data_operazione__lte=data_fine,
        ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
    return elenco_movimenti


def get_centridicosto_piano_dei_conti(piano_dei_conti, area, esercizio, data_inizio=None, data_fine=None):
    elenco_aree = area.get_descendants(include_self=True)
    elenco_movimenti_analitici = DettaglioMovimentoAnalitico.objects.filter(
        piano_dei_conti=piano_dei_conti,
        movimento_primanota__area__in=elenco_aree,
        movimento_primanota__esercizio=esercizio,
    ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
    if data_inizio:
        if data_fine:
            elenco_movimenti_analitici = elenco_movimenti_analitici.filter(
                movimento_primanota__data_operazione__gte=data_inizio,
                movimento_primanota__data_operazione__lte=data_fine,
            ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
        else:
            elenco_movimenti_analitici = elenco_movimenti_analitici.filter(
                movimento_primanota__data_operazione__gte=data_inizio,
            ).order_by('movimento_primanota__data', 'movimento_primanota__numero_operazione')
    return elenco_movimenti_analitici


def get_totale_dare_avere_bilancio_conto(bilancio, data_inizio=None, data_fine=None):
    totale_dare = Money(0.00, bilancio.piano_dei_conti.valuta_default)
    totale_avere = Money(0.00, bilancio.piano_dei_conti.valuta_default)
    elenco_dettaglimovimenti = get_movimenti_piano_dei_conti(
        bilancio.piano_dei_conti, bilancio.area, bilancio.esercizio,
        data_inizio, data_fine
    )
    totale_movimenti_dare = elenco_dettaglimovimenti.filter(categoria='dare').aggregate(Sum('importo'))
    somma_movimenti_dare = totale_movimenti_dare.get('importo__sum', 0)
    if somma_movimenti_dare:
        totale_dare += Money(somma_movimenti_dare, bilancio.piano_dei_conti.valuta_default)
    totale_movimenti_avere = elenco_dettaglimovimenti.filter(categoria='avere').aggregate(Sum('importo'))
    somma_movimenti_avere = totale_movimenti_avere.get('importo__sum', 0)
    if somma_movimenti_avere:
        totale_avere += Money(somma_movimenti_avere, bilancio.piano_dei_conti.valuta_default)
    return totale_dare, totale_avere


def aggiorna_bilancio_sottoconto(bilancio, data_inizio=None, data_fine=None, elenco_centri_di_costo=None):
    if bilancio:
        if not elenco_centri_di_costo:
            elenco_centri_di_costo = CentroDiCosto.objects.all()
        # PREVENTIVO CENTRI DI COSTO
        if bilancio.piano_dei_conti.tipologia in ('costi', 'ricavi', 'economico'):
            if bilancio.piano_dei_conti.livello == 'sottoconto':
                for centro in elenco_centri_di_costo:
                    try:
                        PreventivoCentroDiCosto.objects.get_or_create(
                            bilancio_conto=bilancio, centro_di_costo=centro
                        )
                    except PreventivoCentroDiCosto.MultipleObjectsReturned:
                        pass
        # CONSUNTIVI CENTRI DI COSTO
        if bilancio.piano_dei_conti.tipologia in ('costi', 'ricavi', 'economico'):
            if bilancio.piano_dei_conti.livello == 'sottoconto':
                elenco_costi_movimenti = get_centridicosto_piano_dei_conti(
                    bilancio.piano_dei_conti, bilancio.area, bilancio.esercizio,
                    data_inizio, data_fine
                )
                ConsuntivoCentroDiCosto.objects.filter(bilancio_conto=bilancio).delete()
                for centro in elenco_centri_di_costo:
                    somma_costi = elenco_costi_movimenti.filter(centro_di_costo=centro).aggregate(Sum('importo'))
                    valore_somma_costi = somma_costi.get('importo__sum', 0) or 0
                    consuntivo_centro, created = ConsuntivoCentroDiCosto.objects.get_or_create(
                        bilancio_conto=bilancio, centro_di_costo=centro
                    )
                    valuta_default = bilancio.piano_dei_conti.valuta_default or settings.VALUTA_DEFAULT
                    consuntivo_centro.importo = Money(valore_somma_costi, valuta_default)
                    consuntivo_centro.importo.decimal_places_display = None
                    consuntivo_centro.save()


def aggiorna_bilancio(area, esercizio):
    if area and esercizio:
        elenco_piani_abilitati = None
        elenco_piani = PianoDeiConti.objects.all()
        if area.piano_dei_conti_personalizzato:
            elenco_piani_abilitati = AbilitazionePianoDeiConti.objects.filter(
                area_abilitata=area
            ).values('piano_dei_conti__id')
            if elenco_piani_abilitati:
                elenco_piani = elenco_piani.filter(id__in=elenco_piani_abilitati)
            else:
                elenco_piani = []
        for conto in elenco_piani:
            bilancio_conto, created = BilancioConto.objects.get_or_create(area=area, esercizio=esercizio, piano_dei_conti=conto)
            if created:
                # print('AGGIUNTO CONTO %s ad AREA %s - ESERCIZIO %s' % (conto, area, esercizio))
                pass
        if elenco_piani_abilitati:
            elenco_conti_disabilitati = BilancioConto.objects.filter(area=area, esercizio=esercizio).exclude(
                piano_dei_conti__id__in=elenco_piani_abilitati
            )
            for conto_disabilitato in elenco_conti_disabilitati:
                conto_disabilitato.delete()
                # print('ELIMINATO CONTO %s da AREA %s - ESERCIZIO %s' % (conto_disabilitato, area, esercizio))


def aggiorna_centri_di_costo(area, esercizio):
    if area and esercizio:
        if not area.centri_di_costo_personalizzati:
            elenco_centri_di_costo = CentroDiCosto.objects.all()
        else:
            elenco_abilitazioni_centri_di_costo = AbilitazioneCentroDiCosto.objects.filter(area_abilitata=area).values('centro_di_costo__id')
            if elenco_abilitazioni_centri_di_costo:
                elenco_centri_di_costo = CentroDiCosto.objects.filter(id__in=elenco_abilitazioni_centri_di_costo)
        elenco_bilanci = BilancioConto.objects.filter(
            area=area, esercizio=esercizio, piano_dei_conti__livello='sottoconto',
            piano_dei_conti__tipologia__in=['costi', 'ricavi', 'economico'],
        )
        for bilancio in elenco_bilanci:
            aggiorna_bilancio_sottoconto(bilancio=bilancio, elenco_centri_di_costo=elenco_centri_di_costo)


def get_queryset_bilancio_calcolato(queryset_bilancio_base, area, esercizio, data_inizio=None, data_fine=None, dettagli_movimenti=None, escludi_chiusure_bilancio=False):
    if area:
        valuta_default = area.valuta_default
    else:
        valuta_default = settings.VALUTA_DEFAULT
    if not dettagli_movimenti:
        dettagli_movimenti = DettaglioMovimentoPrimaNota.objects.select_related(
            'movimento_primanota', 'piano_dei_conti__parent'
        )
    if data_inizio:
        dettagli_movimenti = dettagli_movimenti.filter(movimento_primanota__data_operazione__gte=data_inizio)
    if data_fine:
        dettagli_movimenti = dettagli_movimenti.filter(movimento_primanota__data_operazione__lte=data_fine)
    if area:
        elenco_aree = area.get_descendants(include_self=True)
        dettagli_movimenti = dettagli_movimenti.filter(movimento_primanota__area__in=elenco_aree)
    if esercizio:
        dettagli_movimenti = dettagli_movimenti.filter(movimento_primanota__esercizio=esercizio)
    if escludi_chiusure_bilancio:
        dettagli_movimenti = dettagli_movimenti.exclude(movimento_primanota__genere_movimento='chiusura')
    # Passaggio 2: Crea il dizionario bilancio con TUTTI i conti
    piani_dei_conti = PianoDeiConti.objects.select_related('parent').all()
    dizionario_bilancio = {piano.id: {
        'conto': None,  # Riferimento al conto (se presente in queryset_bilancio_base)
        'totale_dare': Money('0.00', valuta_default),
        'totale_avere': Money('0.00', valuta_default),
        'numero_movimenti': 0,
        'ultima_data_movimenti': None
    } for piano in piani_dei_conti}
    # Associa i conti presenti in queryset_bilancio_base
    for conto in queryset_bilancio_base:
        dizionario_bilancio[conto.piano_dei_conti_id]['conto'] = conto
    # Passaggio 3: Aggrega i dettagli dei movimenti
    aggregati = dettagli_movimenti.values('piano_dei_conti_id').annotate(
        # totale_dare=Sum('importo', filter=Q(categoria='dare')),
        # totale_avere=Sum('importo', filter=Q(categoria='avere')),
        totale_dare=Sum(
            'conversionedettagliomovimentoprimanota__importo',
            filter=Q(categoria='dare', conversionedettagliomovimentoprimanota__importo_currency=valuta_default)
        ),
        totale_avere=Sum(
            'conversionedettagliomovimentoprimanota__importo',
            filter=Q(categoria='avere', conversionedettagliomovimentoprimanota__importo_currency=valuta_default)
        ),
        numero_movimenti=Count('id', distinct=True),
        ultima_data_movimenti=Max('movimento_primanota__data_operazione')
    )
     # Aggiorna i conti foglia
    for agg in aggregati:
        piano_id = agg['piano_dei_conti_id']
        if piano_id in dizionario_bilancio:
            if agg['totale_dare']:
                dizionario_bilancio[piano_id]['totale_dare'] += Money(agg['totale_dare'], valuta_default)
            if agg['totale_avere']:
                dizionario_bilancio[piano_id]['totale_avere'] += Money(agg['totale_avere'], valuta_default)
            dizionario_bilancio[piano_id]['numero_movimenti'] += agg['numero_movimenti']
            ultima_data = agg['ultima_data_movimenti']
            if not dizionario_bilancio[piano_id]['ultima_data_movimenti'] or (ultima_data and ultima_data > dizionario_bilancio[piano_id]['ultima_data_movimenti']):
                dizionario_bilancio[piano_id]['ultima_data_movimenti'] = ultima_data

    # Passaggio 4: Propaga i valori lungo la gerarchia
    piano_hierarchy = {}
    for piano in piani_dei_conti:
        piano_hierarchy.setdefault(piano.parent_id, []).append(piano)

    def aggiorna_nodo(nodo_id):
        figli = piano_hierarchy.get(nodo_id, [])
        for figlio in figli:
            aggiorna_nodo(figlio.id)
            if nodo_id in dizionario_bilancio:
                nodo = dizionario_bilancio[nodo_id]
                figlio_dati = dizionario_bilancio[figlio.id]
                nodo['totale_dare'] += figlio_dati['totale_dare']
                nodo['totale_avere'] += figlio_dati['totale_avere']
                nodo['numero_movimenti'] += figlio_dati['numero_movimenti']
                if not nodo['ultima_data_movimenti'] or (figlio_dati['ultima_data_movimenti'] and figlio_dati['ultima_data_movimenti'] > nodo['ultima_data_movimenti']):
                    nodo['ultima_data_movimenti'] = figlio_dati['ultima_data_movimenti']
    # Avvia la propagazione dalla radice
    for nodo in piani_dei_conti:
        if nodo.parent is None:
            aggiorna_nodo(nodo.id)

    # Passaggio 5: Aggiorna i conti di queryset_bilancio_base
    for conto_id, dati in dizionario_bilancio.items():
        conto = dati['conto']
        if conto:
            conto.totale_dare = dati['totale_dare']
            conto.totale_avere = dati['totale_avere']
            conto.numero_movimenti = dati['numero_movimenti']
            conto.ultima_data_movimenti = dati['ultima_data_movimenti']
    return queryset_bilancio_base


def costruisci_gerarchia_piano_dei_conti(piani_dei_conti):
    gerarchia = {}
    for piano in piani_dei_conti:
        gerarchia.setdefault(piano.parent_id, []).append(piano)
    return gerarchia


def calcola_preventivi_sui_nodi_foglia(elenco_preventivi, valuta_default):
    # Aggrega i valori per i nodi foglia
    preventivi_aggregati = dict()
    for preventivo in elenco_preventivi:
        if preventivo.piano_dei_conti_id not in preventivi_aggregati:
            preventivi_aggregati[preventivo.piano_dei_conti_id] = Money('0.00', valuta_default)
        totale_preventivo = preventivi_aggregati[preventivo.piano_dei_conti_id]
        valore_convertito = get_controvalore(preventivo.preventivo, valuta_default)
        if valore_convertito:
            preventivi_aggregati[preventivo.piano_dei_conti_id] = totale_preventivo + valore_convertito
    # Costruisce un dizionario {piano_dei_conti_id: totale_preventivo}
    # return {item['piano_dei_conti_id']: item['totale_preventivo'] for item in preventivi_aggregati}
    return preventivi_aggregati


def propaga_preventivi(dizionario_preventivi, gerarchia, nodo_id, valuta_corrente):
    figli = gerarchia.get(nodo_id, [])
    for figlio in figli:
        propaga_preventivi(dizionario_preventivi, gerarchia, figlio.id, valuta_corrente)
        # Aggiorna i valori del genitore
        if nodo_id in dizionario_preventivi:
            nodo = dizionario_preventivi[nodo_id]
            figlio_dati = dizionario_preventivi[figlio.id]
            nodo['totale_preventivo'] += figlio_dati['totale_preventivo'] or Money('0.00', valuta_corrente)


def aggiorna_preventivi(bilancio_preventivo_base, gerarchia, preventivi_calcolati):
    for conto_preventivo in bilancio_preventivo_base:
        conto_id = conto_preventivo.piano_dei_conti.id
        if conto_id in preventivi_calcolati:
            conto_preventivo.totale_preventivo = preventivi_calcolati[conto_id]['totale_preventivo']


def get_queryset_bilancio_preventivo(queryset_bilancio_base, area, esercizio, data_inizio=None, data_fine=None, dettagli_movimenti=None):
    bilanci_preventivi = get_queryset_bilancio_calcolato(
        queryset_bilancio_base, area, esercizio, data_inizio, data_fine,
        dettagli_movimenti
    )
    elenco_preventivi = BilancioContoPreventivo.objects.filter(preventivo__isnull=False)
    if area:
        elenco_aree = area.get_descendants(include_self=True)
        elenco_preventivi = elenco_preventivi.filter(area__in=elenco_aree)
    if esercizio:
        elenco_preventivi = elenco_preventivi.filter(esercizio=esercizio)
    if elenco_preventivi:
        valuta_corrente = area.valuta_default
        piani_dei_conti = PianoDeiConti.objects.select_related('parent').all()
        # Step 0: Costruisci la gerarchia
        gerarchia = costruisci_gerarchia_piano_dei_conti(piani_dei_conti)
        # Step 1: Calcola i preventivi sui nodi foglia
        preventivo_sottoconto = calcola_preventivi_sui_nodi_foglia(elenco_preventivi, valuta_corrente)
        # Step 2: Inizializza il dizionario preventivi
        dizionario_preventivi = {
            piano.id: {'totale_preventivo': Money('0.00', valuta_corrente)} for piano in piani_dei_conti
        }
        # Step 3: Aggiorna i valori sui nodi foglia
        for piano_id, totale_preventivo in preventivo_sottoconto.items():
            if piano_id in dizionario_preventivi:
                dizionario_preventivi[piano_id]['totale_preventivo'] = totale_preventivo
        # Step 4: Propaga i valori lungo la gerarchia
        for nodo in piani_dei_conti:
            if nodo.parent is None:
                propaga_preventivi(dizionario_preventivi, gerarchia, nodo.id, valuta_corrente)
        # Step 5: Aggiorna i bilanci preventivi
        aggiorna_preventivi(bilanci_preventivi, gerarchia, dizionario_preventivi)
    return bilanci_preventivi


def get_statistiche_conto_economico(area_corrente, esercizio_corrente, queryset_base=None, per_dashboard=False):
    statistiche = dict()
    elenco_bilanci_conto = None
    if queryset_base:
        elenco_bilanci_conto = queryset_base.filter(area=area_corrente, esercizio=esercizio_corrente)
    else:
        elenco_bilanci_conto = BilancioConto.objects.filter(area=area_corrente, esercizio=esercizio_corrente)
    queryset_costi = elenco_bilanci_conto.filter(
        piano_dei_conti__tipologia='costi', piano_dei_conti__livello='sottoconto',
    )
    elenco_bilanci_conto_costi = get_queryset_bilancio_calcolato(
        queryset_costi, area_corrente, esercizio_corrente,
    )
    queryset_ricavi = elenco_bilanci_conto.filter(
        piano_dei_conti__tipologia='ricavi', piano_dei_conti__livello='sottoconto',
    )
    elenco_bilanci_conto_ricavi = get_queryset_bilancio_calcolato(
        queryset_ricavi, area_corrente, esercizio_corrente,
    )
    data = [
        [_('tipo'), _('totale')]
    ]
    if elenco_bilanci_conto_costi:
        totale_dare_costi = Decimal('0.00')
        totale_avere_costi = Decimal('0.00')
        for conto in elenco_bilanci_conto_costi:
            if conto.totale_dare:
                totale_dare_costi += conto.totale_dare.amount
            if conto.totale_avere:
                totale_avere_costi += conto.totale_avere.amount
        saldo_costi = totale_dare_costi - totale_avere_costi
        dati_grafico_costi = ['COSTI', abs(saldo_costi)]
        data.append(dati_grafico_costi)
    if elenco_bilanci_conto_ricavi:
        totale_dare_ricavi = Decimal('0.00')
        totale_avere_ricavi = Decimal('0.00')
        for conto in elenco_bilanci_conto_ricavi:
            if conto.totale_dare:
                totale_dare_ricavi += conto.totale_dare.amount
            if conto.totale_avere:
                totale_avere_ricavi += conto.totale_avere.amount
        saldo_ricavi = totale_dare_ricavi - totale_avere_ricavi
        dati_grafico_ricavi = ['RICAVI', abs(saldo_ricavi)]
        data.append(dati_grafico_ricavi)
    data_source = SimpleDataSource(data=data)
    titolo = _('Conto Economico')
    sottotitolo = _('confronto costi/ricavi')
    simbolo_valuta_default = get_elenco_valuta_default()[0][1]
    options = {
        'title': {
            'text': titolo,
            'align': 'center',
        },
        'subtitle': {
            'text': sottotitolo,
            'align': 'center',
        },
        'chart': {
            'type': 'column',
            # 'inverted': True,
            'options3d': {
                'enabled': True,
                'alpha': 15,
                'beta': 15,
                'depth': 50,
                'viewDistance': 25
            }
        },
        'tooltip': {
            'pointFormat': '{point.y:.2f} %s' % simbolo_valuta_default
        },
        'exporting': {
            'showTable': False
        },
        'legend': {
            'enabled': False
        },
        'plotOptions': {
            'series': {
                'dataLabels': {
                    'enabled': True,
                    'format': '{point.y:.2f} %s' % simbolo_valuta_default
                }
            },
            'column': {
                'depth': 25
            }
        },
    }
    chart = ColumnChart(
        data_source=data_source, options=options, width='100%'
    )
    statistiche['html_grafico_conto_economico'] = chart.as_html()
    elenco_dati_tabella = data
    statistiche['elenco_dati_tabella'] = elenco_dati_tabella
    statistiche['simbolo_valuta'] = simbolo_valuta_default
    return statistiche


def get_statistiche_riepilogo_costi(area_corrente, esercizio_corrente, queryset_base=None, per_dashboard=False):
    statistiche = dict()
    elenco_bilanci_conto = None
    if queryset_base:
        elenco_bilanci_conto = queryset_base.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__tipologia='costi'
        )
    else:
        elenco_bilanci_conto = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente,
            piano_dei_conti__tipologia='costi',
        )
    elenco_bilanci_conto_costi = get_queryset_bilancio_calcolato(
        elenco_bilanci_conto, area_corrente, esercizio_corrente,
    )
    if elenco_bilanci_conto_costi:
        lista_dati_grafico = [
            [_('Conto'), _('Totale')]
        ]
        lista_dati_tabella = [
            [_('Conto'), _('Totale'), _('Percentuale')]
        ]
        totale_costi = Decimal('0.00')
        for bilancio_costo in elenco_bilanci_conto_costi:
            if bilancio_costo.piano_dei_conti.livello == 'sottoconto':
                saldo = bilancio_costo.get_saldo_abs()
                if saldo:
                    dati_grafico_costi = ['%s' % bilancio_costo.piano_dei_conti, saldo.amount]
                    dati_tabella_costi = ['%s' % bilancio_costo.piano_dei_conti, saldo.amount, Decimal('0.00')]
                    lista_dati_grafico.append(dati_grafico_costi)
                    lista_dati_tabella.append(dati_tabella_costi)
                    totale_costi += saldo.amount
        for indice in range(1, len(lista_dati_tabella)):
            dati = lista_dati_tabella[indice]
            if dati[1]:
                dati[2] = round((dati[1] / totale_costi) * 100, 2)
        data_source = SimpleDataSource(data=lista_dati_grafico)
        titolo = _('COSTI')
        sottotitolo = _('Analisi Costi per conto')
        simbolo_valuta_default = get_elenco_valuta_default()[0][1]
        # POSIZIONE LEGENDA
        allineamento_legenda = 'right'
        allineamento_verticale_legenda = 'middle'
        if per_dashboard:
            allineamento_legenda = 'center'
            allineamento_verticale_legenda = 'bottom'
        options = {
            'title': {
                'text': titolo,
                'align': 'center',
            },
            'subtitle': {
                'text': sottotitolo,
                'align': 'center',
            },
            'chart': {
                'type': 'pie',
                'options3d': {
                    'enabled': True,
                    'alpha': 45,
                    'beta': 0,
                }
            },
            'tooltip': {
                'pointFormat': '{point.percentage:.2f}%% ({point.y:.2f} %s)' % simbolo_valuta_default
            },
            'exporting': {
                'showTable': False
            },
            'legend': {
                'layout': 'vertical',
                'align': allineamento_legenda,
                'verticalAlign': allineamento_verticale_legenda,
            },
            'plotOptions': {
                'pie': {
                    'borderRadius': 5,
                    'allowPointSelect': True,
                    'depth': 35,
                    'cursor': 'pointer',
                    'dataLabels': {
                        'enabled': True,
                        'format': '{point.percentage:.2f}%'
                    }
                }
            },
        }
        # Chart object
        chart = PieChart(
            data_source=data_source, options=options, width='100%'
        )
        statistiche['html_grafico_riepilogo_costi'] = chart.as_html()
        statistiche['elenco_dati_tabella'] = lista_dati_tabella
        statistiche['simbolo_valuta'] = simbolo_valuta_default
        statistiche['totale_costi'] = totale_costi
    return statistiche


def get_statistiche_riepilogo_ricavi(area_corrente, esercizio_corrente, queryset_base=None, per_dashboard=False):
    statistiche = dict()
    elenco_bilanci_conto = None
    if queryset_base:
        elenco_bilanci_conto = queryset_base.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__tipologia='ricavi'
        )
    elif area_corrente and esercizio_corrente:
        elenco_bilanci_conto = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente,
            piano_dei_conti__tipologia='ricavi',
        )
    elenco_bilanci_conto_ricavi = get_queryset_bilancio_calcolato(
        elenco_bilanci_conto, area_corrente, esercizio_corrente,
    )
    if elenco_bilanci_conto_ricavi:
        simbolo_valuta_default = get_elenco_valuta_default()[0][1]
        lista_dati_grafico = [
            [_('Conto'), _('Totale')]
        ]
        lista_dati_tabella = [
            [_('Conto'), _('Totale'), _('Percentuale')]
        ]
        totale_ricavi = Decimal('0.00')
        for bilancio_ricavo in elenco_bilanci_conto_ricavi:
            if bilancio_ricavo.piano_dei_conti.livello == 'sottoconto':
                saldo = bilancio_ricavo.get_saldo_abs()
                if saldo:
                    dati_grafico_ricavi = ['%s' % bilancio_ricavo.piano_dei_conti, saldo.amount]
                    dati_tabella_ricavi = ['%s' % bilancio_ricavo.piano_dei_conti, saldo.amount, Decimal('0.00')]
                    lista_dati_grafico.append(dati_grafico_ricavi)
                    lista_dati_tabella.append(dati_tabella_ricavi)
                    totale_ricavi += saldo.amount
        for indice in range(1, len(lista_dati_tabella)):
            dati = lista_dati_tabella[indice]
            if dati[1]:
                dati[2] = round((dati[1] / totale_ricavi) * 100, 2)
        data_source = SimpleDataSource(data=lista_dati_grafico)
        titolo = _('RICAVI')
        sottotitolo = _('Analisi Ricavi per conto')
        # POSIZIONE LEGENDA
        allineamento_legenda = 'right'
        allineamento_verticale_legenda = 'middle'
        if per_dashboard:
            allineamento_legenda = 'center'
            allineamento_verticale_legenda = 'bottom'
        options = {
            'title': {
                'text': titolo,
                'align': 'center',
            },
            'subtitle': {
                'text': sottotitolo,
                'align': 'center',
            },
            'chart': {
                'type': 'pie',
                'options3d': {
                    'enabled': True,
                    'alpha': 45,
                    'beta': 0,
                }
            },
            'tooltip': {
                'pointFormat': '{point.percentage:.2f}%% ({point.y:.2f} %s)' % simbolo_valuta_default
            },
            'exporting': {
                'showTable': False
            },
            'legend': {
                'layout': 'vertical',
                'align': allineamento_legenda,
                'verticalAlign': allineamento_verticale_legenda,
            },
            'plotOptions': {
                'pie': {
                    'borderRadius': 5,
                    'allowPointSelect': True,
                    'cursor': 'pointer',
                    'depth': 35,
                    'dataLabels': {
                        'enabled': True,
                        'format': '{point.percentage:.2f}%'
                    }
                }
            },
        }
        # Chart object
        chart = PieChart(
            data_source=data_source, options=options, width='100%'
        )
        statistiche['html_grafico_riepilogo_ricavi'] = chart.as_html()
        statistiche['elenco_dati_tabella'] = lista_dati_tabella
        statistiche['simbolo_valuta'] = simbolo_valuta_default
        statistiche['totale_ricavi'] = totale_ricavi
    return statistiche


def get_statistiche_liquidita(area_corrente, esercizio_corrente, queryset_base=None, per_dashboard=False):
    statistiche = dict()
    elenco_bilanci_conto = None
    valuta_corrente = area_corrente.valuta_default
    if queryset_base:
        elenco_bilanci_conto = queryset_base.filter(
            piano_dei_conti__tipologia='liquidita',
        ).prefetch_related('piano_dei_conti', 'area', 'esercizio')
    else:
        elenco_bilanci_conto = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente,
            piano_dei_conti__tipologia='liquidita',
        ).prefetch_related('piano_dei_conti', 'area', 'esercizio')
    if elenco_bilanci_conto:
        elenco_bilanci_liquidita = get_queryset_bilancio_calcolato(
            elenco_bilanci_conto, area_corrente, esercizio_corrente,
        )
        if elenco_bilanci_liquidita:
            elenco_aree = area_corrente.get_descendants(include_self=True)
            elenco_conti = []
            for bilancio in elenco_bilanci_liquidita:
                if bilancio.piano_dei_conti.livello == 'sottoconto':
                    if bilancio.numero_movimenti > 0:
                        elenco_conti.append(bilancio.piano_dei_conti.id)
            elenco_movimenti_conti = DettaglioMovimentoPrimaNota.objects.filter(
                piano_dei_conti_id__in=elenco_conti,
                movimento_primanota__area__in=elenco_aree,
                movimento_primanota__esercizio=esercizio_corrente,
            ).order_by('movimento_primanota__data_operazione').prefetch_related(
                'movimento_primanota',
                Prefetch(
                    'conversionedettagliomovimentoprimanota_set',
                    queryset=ConversioneDettaglioMovimentoPrimaNota.objects.filter(
                        importo_currency=valuta_corrente
                    ),
                    to_attr='conversioni_prefetch'
                )
            )
            flusso_liquidita = dict()
            flusso_liquidita[esercizio_corrente.data_inizio] = Money('0.00', valuta_corrente)
            if elenco_movimenti_conti:
                for dettaglio in elenco_movimenti_conti:
                    if dettaglio.movimento_primanota.data_operazione not in flusso_liquidita:
                        flusso_liquidita[dettaglio.movimento_primanota.data_operazione] = Money('0.00', valuta_corrente)
                    if dettaglio.importo:
                        if dettaglio.categoria == 'dare':
                            flusso_liquidita[dettaglio.movimento_primanota.data_operazione] += dettaglio.get_importo_convertito(valuta_corrente)
                        if dettaglio.categoria == 'avere':
                            flusso_liquidita[dettaglio.movimento_primanota.data_operazione] -= dettaglio.get_importo_convertito(valuta_corrente)
            dati_grafico = [
                [_('Data'), _('Liquidità')]
            ]
            liquidita = Money('0.00', valuta_corrente)
            for key in flusso_liquidita.keys():
                if key:
                    liquidita += flusso_liquidita[key]
                    dati_grafico.append([key.strftime('%d/%m/%Y'), liquidita.amount])
            data_source = SimpleDataSource(data=dati_grafico)
            titolo = _('Liquidità')
            simbolo_valuta_default = get_elenco_valuta_default()[0][1]
            options = {
                'title': {
                    'text': titolo,
                    'align': 'center',
                },
                'subtitle': 'Variazione della liquidità nel tempo',
                'tooltip': {
                    'pointFormat': '{point.y:.2f} %s' % simbolo_valuta_default
                },
                'exporting': {
                    'showTable': False
                },
                'legend': {
                    'enabled': False
                },
                # 'xAxis': {
                #     'type': 'datetime',
                #     'labels': {
                #         'format': '{value:%d/%m/%Y}'
                #     },
                # },
                'plotOptions': {
                    'line': {
                        'dataLabels': {
                            'enabled': True,
                            'format': '{point.y:.2f} %s' % simbolo_valuta_default
                        }
                    }
                },
            }
            chart = LineChart(
                data_source=data_source, options=options, width='100%'
            )
            statistiche['html_grafico_liquidita'] = chart.as_html()
            elenco_dati_tabella = dati_grafico
            statistiche['elenco_dati_tabella'] = elenco_dati_tabella
            statistiche['simbolo_valuta'] = simbolo_valuta_default
    return statistiche


def get_statistiche_budget(area_corrente, esercizio_corrente, queryset_base=None, per_dashboard=False):
    statistiche = dict()
    elenco_bilanci_conto = None
    if queryset_base:
        elenco_bilanci_conto = queryset_base.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__tipologia__in=['costi', 'ricavi', 'economico']
        )
    else:
        elenco_bilanci_conto = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__tipologia__in=['costi', 'ricavi', 'economico']
        )
    elenco_bilanci_preventivi = get_queryset_bilancio_preventivo(
        elenco_bilanci_conto, area_corrente, esercizio_corrente,
    )
    if elenco_bilanci_preventivi:
        data = [
            [_('Conto'), _('preventivo'), _('consuntivo')]
        ]
        for bilancio_preventivo in elenco_bilanci_preventivi:
            if bilancio_preventivo.piano_dei_conti.livello == 'conto':
                preventivo = bilancio_preventivo.get_totale_preventivo()
                consuntivo = bilancio_preventivo.get_consuntivo()
                if preventivo or consuntivo:
                    dati_grafico_budget = [
                        '%s' % bilancio_preventivo.piano_dei_conti,
                        abs(preventivo.amount),
                        abs(consuntivo.amount),
                    ]
                    data.append(dati_grafico_budget)
        data_source = SimpleDataSource(data=data)
        titolo = _('Budget (per conto)')
        sottotitolo = _('confronto preventivo/consuntivo per conto')
        simbolo_valuta_default = get_elenco_valuta_default()[0][1]
        options = {
            'title': {
                'text': titolo,
                'align': 'center',
            },
            'subtitle': {
                'text': sottotitolo,
                'align': 'center',
            },
            # 'chart': {
            #     'type': 'column',
            #     'options3d': {
            #         'enabled': True,
            #         'alpha': 5,
            #         'beta': 5,
            #         'depth': 70,
            #         'viewDistance': 10
            #     }
            # },
            'tooltip': {
                'pointFormat': '{point.y:.2f} %s' % simbolo_valuta_default
            },
            'exporting': {
                'showTable': False
            },
            'legend': {
                'layout': 'vertical',
                'align': 'right',
                'verticalAlign': 'middle'
            },
            'plotOptions': {
                'column': {
                    'borderRadius': 5,
                    'allowPointSelect': True,
                    'cursor': 'pointer',
                    'depth': 35,
                    'dataLabels': {
                        'enabled': True,
                        'format': '{point.y:.2f} %s' % simbolo_valuta_default
                    }
                }
            },
        }
        chart = ColumnChart(
            data_source=data_source, options=options, width='100%'
        )
        statistiche['html_grafico_budget'] = chart.as_html()
        statistiche['elenco_dati_tabella'] = data
        statistiche['simbolo_valuta'] = simbolo_valuta_default
    return statistiche


def aggiorna_statistiche_grafici(area, esercizio):
    if area and esercizio:
        ora_corrente = timezone.now()
        statistiche_conto_economico = get_statistiche_conto_economico(
            area_corrente=area, esercizio_corrente=esercizio, per_dashboard=True
        )
        if statistiche_conto_economico:
            statistica_conto, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_conto_economico', area=area, esercizio=esercizio)
            statistica_conto.html_grafico = statistiche_conto_economico['html_grafico_conto_economico']
            statistica_conto.data_ultimo_aggiornamento = ora_corrente
            statistica_conto.save()
        statistiche_riepilogo_costi = get_statistiche_riepilogo_costi(
            area_corrente=area, esercizio_corrente=esercizio, per_dashboard=True
        )
        if statistiche_riepilogo_costi:
            statistica_costi, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_riepilogo_costi', area=area, esercizio=esercizio)
            statistica_costi.html_grafico = statistiche_riepilogo_costi['html_grafico_riepilogo_costi']
            statistica_costi.data_ultimo_aggiornamento = ora_corrente
            statistica_costi.save()
        statistiche_riepilogo_ricavi = get_statistiche_riepilogo_ricavi(
            area_corrente=area, esercizio_corrente=esercizio, per_dashboard=True
        )
        if statistiche_riepilogo_ricavi:
            statistica_ricavi, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_riepilogo_ricavi', area=area, esercizio=esercizio)
            statistica_ricavi.html_grafico = statistiche_riepilogo_ricavi['html_grafico_riepilogo_ricavi']
            statistica_ricavi.data_ultimo_aggiornamento = ora_corrente
            statistica_ricavi.save()
        statistiche_budget = get_statistiche_budget(
            area_corrente=area, esercizio_corrente=esercizio, per_dashboard=True
        )
        if statistiche_budget:
            statistica_budget, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_budget', area=area, esercizio=esercizio)
            statistica_budget.html_grafico = statistiche_budget['html_grafico_budget']
            statistica_budget.data_ultimo_aggiornamento = ora_corrente
            statistica_budget.save()
        statistiche_liquidita = get_statistiche_liquidita(
            area_corrente=area, esercizio_corrente=esercizio, per_dashboard=True
        )
        if statistiche_liquidita:
            statistica_liquidita, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_liquidita', area=area, esercizio=esercizio)
            statistica_liquidita.html_grafico = statistiche_liquidita['html_grafico_liquidita']
            statistica_liquidita.data_ultimo_aggiornamento = ora_corrente
            statistica_liquidita.save()
        gestione_bilancio = preferences.ImpostazioniMagister.gestione_bilancio
        if gestione_bilancio == 'competenza':
            # STATISTICHE BILANCIO RICLASSIFICATO
            gestione_operativa = get_bilancio_riclassificato_conti(
                tipo_gestione='operativa',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            margine_operativo_lordo = gestione_operativa['indice_rendiconto']
            if gestione_operativa:
                statistica_mol, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_mol', area=area, esercizio=esercizio)
                statistica_mol.html_grafico = gestione_operativa['indice_rendiconto']
                statistica_mol.data_ultimo_aggiornamento = ora_corrente
                statistica_mol.save()
            gestione_finanziaria = get_bilancio_riclassificato_conti(
                tipo_gestione='finanziaria',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            risultato_gestione_finanziaria = gestione_finanziaria['indice_rendiconto']
            if gestione_finanziaria:
                statistica_risultato_finanziario, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_risultato_gestione_finanziaria', area=area, esercizio=esercizio)
                statistica_risultato_finanziario.html_grafico = gestione_finanziaria['indice_rendiconto']
                statistica_risultato_finanziario.data_ultimo_aggiornamento = ora_corrente
                statistica_risultato_finanziario.save()
            gestione_patrimoniale = get_bilancio_riclassificato_conti(
                tipo_gestione='patrimoniale',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            risultato_gestione_patrimoniale = gestione_patrimoniale['indice_rendiconto']
            if gestione_patrimoniale:
                statistica_risultato_patrimoniale, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_risultato_gestione_patrimoniale', area=area, esercizio=esercizio)
                statistica_risultato_patrimoniale.html_grafico = gestione_patrimoniale['indice_rendiconto']
                statistica_risultato_patrimoniale.data_ultimo_aggiornamento = ora_corrente
                statistica_risultato_patrimoniale.save()
            risultato_esercizio = margine_operativo_lordo + risultato_gestione_finanziaria + risultato_gestione_patrimoniale
            statistica_risultato_esercizio, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_risultato_esercizio', area=area, esercizio=esercizio
            )
            statistica_risultato_esercizio.html_grafico = risultato_esercizio
            statistica_risultato_esercizio.data_ultimo_aggiornamento = ora_corrente
            statistica_risultato_esercizio.save()
        if gestione_bilancio == 'cassa':
            # STATISTICHE RENDICONTO FINANZIARIO
            # #################################################################
            # Risultato Finanziario della Gestione Operativa
            # -----------------------------------------------------------------
            gestione_operativa = get_rendiconto_finanziario_conti(
                tipo_gestione='operativa',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            risultato_finanziario_gestione_operativa = gestione_operativa['indice_rendiconto']
            if gestione_operativa:
                statistica_gestione_operativa, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_risultatofinanziario_gestione_operativa',
                    area=area, esercizio=esercizio
                )
                statistica_gestione_operativa.html_grafico = gestione_operativa['indice_rendiconto']
                statistica_gestione_operativa.data_ultimo_aggiornamento = ora_corrente
                statistica_gestione_operativa.save()
            # Risultato Finanziario della Gestione Finanziaria
            # -----------------------------------------------------------------
            gestione_finanziaria = get_rendiconto_finanziario_conti(
                tipo_gestione='finanziaria',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            risultato_finanziario_gestione_finanziaria = gestione_finanziaria['indice_rendiconto']
            if gestione_finanziaria:
                statistica_risultato_finanziario, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_risultatofinanziario_gestione_finanziaria', area=area, esercizio=esercizio)
                statistica_risultato_finanziario.html_grafico = gestione_finanziaria['indice_rendiconto']
                statistica_risultato_finanziario.data_ultimo_aggiornamento = ora_corrente
                statistica_risultato_finanziario.save()
            # Risultato Finanziario della Gestione Patrimoniale
            # -----------------------------------------------------------------
            gestione_patrimoniale = get_rendiconto_finanziario_conti(
                tipo_gestione='patrimoniale',
                area_corrente=area,
                esercizio_corrente=esercizio
            )
            risultato_finanziario_gestione_patrimoniale = gestione_patrimoniale['indice_rendiconto']
            if gestione_patrimoniale:
                statistica_risultato_patrimoniale, created = StatisticheGrafici.objects.get_or_create(
                    grafico='matthaeus_risultatofinanziario_gestione_patrimoniale', area=area, esercizio=esercizio)
                statistica_risultato_patrimoniale.html_grafico = gestione_patrimoniale['indice_rendiconto']
                statistica_risultato_patrimoniale.data_ultimo_aggiornamento = ora_corrente
                statistica_risultato_patrimoniale.save()
            # Risultato Finanziario Esercizio
            # -----------------------------------------------------------------
            risultato_finanziario_esercizio = (
                risultato_finanziario_gestione_operativa + risultato_finanziario_gestione_finanziaria + risultato_finanziario_gestione_patrimoniale
            )
            statistica_risultato_finanziario_esercizio, created = StatisticheGrafici.objects.get_or_create(
                grafico='matthaeus_risultatofinanziario_esercizio', area=area, esercizio=esercizio)
            statistica_risultato_finanziario_esercizio.html_grafico = risultato_finanziario_esercizio
            statistica_risultato_finanziario_esercizio.data_ultimo_aggiornamento = ora_corrente
            statistica_risultato_finanziario_esercizio.save()
        return ora_corrente


def get_movimenti_rendiconto_finanziario(area=None, esercizio=None, data_inizio=None, data_fine=None):
    dettagli_movimenti_liquidita = DettaglioMovimentoPrimaNota.objects.filter(
        piano_dei_conti__tipologia='liquidita'
    )
    if data_inizio:
        dettagli_movimenti_liquidita = dettagli_movimenti_liquidita.filter(movimento_primanota__data_operazione__gte=data_inizio)
    if data_fine:
        dettagli_movimenti_liquidita = dettagli_movimenti_liquidita.filter(movimento_primanota__data_operazione__lte=data_fine)
    if area:
        elenco_aree = area.get_descendants(include_self=True)
        dettagli_movimenti_liquidita = dettagli_movimenti_liquidita.filter(movimento_primanota__area__in=elenco_aree)
    if esercizio:
        dettagli_movimenti_liquidita = dettagli_movimenti_liquidita.filter(movimento_primanota__esercizio=esercizio)
    dettagli_movimenti_liquidita = dettagli_movimenti_liquidita.values('movimento_primanota__id').distinct()
    dettagli_rendiconto_finanziario = DettaglioMovimentoPrimaNota.objects.filter(
        movimento_primanota__id__in=dettagli_movimenti_liquidita
    )
    return dettagli_rendiconto_finanziario


def get_rendiconto_finanziario_conti(
    tipo_gestione, elenco_bilanci=None, area_corrente=None,
    esercizio_corrente=None, data_inizio=None, data_fine=None
):
    bilanci_costi = []
    bilanci_ricavi = []
    valuta_corrente = area_corrente.valuta_default
    totale_costi = Money('0.00', valuta_corrente)
    totale_ricavi = Money('0.00', valuta_corrente)
    if not elenco_bilanci:
        elenco_bilanci_conti = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__gestione=tipo_gestione,
        )
    else:
        elenco_bilanci_conti = elenco_bilanci.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__gestione=tipo_gestione,
        )
    if not data_inizio:
        if esercizio_corrente:
            if esercizio_corrente.data_inizio:
                data_inizio = esercizio_corrente.data_inizio + timedelta(days=1)
    dettagli_movimenti = get_movimenti_rendiconto_finanziario(
        area_corrente, esercizio_corrente, data_inizio, data_fine
    )
    if elenco_bilanci_conti and tipo_gestione:
        queryset_costi = elenco_bilanci_conti.filter(
            piano_dei_conti__tipologia='costi',
        )
        elenco_costi = get_queryset_bilancio_calcolato(
            queryset_costi, area_corrente, esercizio_corrente, 
            data_inizio, data_fine, dettagli_movimenti
        )
        for costo in elenco_costi:
            if costo.piano_dei_conti.livello == 'conto':
                saldo_costo = costo.get_saldo_abs()
                if saldo_costo:
                    totale_costi += saldo_costo
                    bilanci_costi.append(costo)
        queryset_ricavi = elenco_bilanci_conti.filter(
            piano_dei_conti__tipologia='ricavi',
        )
        elenco_ricavi = get_queryset_bilancio_calcolato(
            queryset_ricavi, area_corrente, esercizio_corrente, 
            data_inizio, data_fine, dettagli_movimenti
        )
        for ricavo in elenco_ricavi:
            if ricavo.piano_dei_conti.livello == 'conto':
                saldo_ricavo = ricavo.get_saldo_abs()
                if saldo_ricavo:
                    totale_ricavi += saldo_ricavo
                    bilanci_ricavi.append(ricavo)
    indice_rendiconto = totale_ricavi - totale_costi
    indice_rendiconto = Money(indice_rendiconto.amount, valuta_corrente)
    totale_costi = Money(totale_costi.amount, valuta_corrente)
    totale_ricavi = Money(totale_ricavi.amount, valuta_corrente)
    return {
        'bilanci_costi': bilanci_costi,
        'totale_costi': totale_costi,
        'bilanci_ricavi': bilanci_ricavi,
        'totale_ricavi': totale_ricavi,
        'indice_rendiconto': indice_rendiconto
    }


def get_bilancio_riclassificato_conti(tipo_gestione, elenco_bilanci=None, area_corrente=None, esercizio_corrente=None):
    bilanci_costi = []
    bilanci_ricavi = []
    valuta_corrente = area_corrente.valuta_default
    totale_costi = Money('0.00', valuta_corrente)
    totale_ricavi = Money('0.00', valuta_corrente)
    if not elenco_bilanci:
        elenco_bilanci = BilancioConto.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__gestione=tipo_gestione
        )
    else:
        elenco_bilanci = elenco_bilanci.filter(
            area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti__gestione=tipo_gestione
        )
    if elenco_bilanci and tipo_gestione:
        queryset_costi = elenco_bilanci.filter(
            piano_dei_conti__tipologia='costi',
        )
        elenco_costi = get_queryset_bilancio_calcolato(
            queryset_costi, area_corrente, esercizio_corrente,
        )
        for costo in elenco_costi:
            if costo.piano_dei_conti.livello == 'conto':
                saldo_costo = costo.get_saldo_abs()
                if saldo_costo:
                    totale_costi += saldo_costo
                    bilanci_costi.append(costo)
        queryset_ricavi = elenco_bilanci.filter(
            piano_dei_conti__tipologia='ricavi',
        )
        elenco_ricavi = get_queryset_bilancio_calcolato(
            queryset_ricavi, area_corrente, esercizio_corrente,
        )
        for ricavo in elenco_ricavi:
            if ricavo.piano_dei_conti.livello == 'conto':
                saldo_ricavo = ricavo.get_saldo_abs()
                if saldo_ricavo:
                    totale_ricavi += saldo_ricavo
                    bilanci_ricavi.append(ricavo)
    indice_rendiconto = totale_ricavi - totale_costi
    indice_rendiconto = Money(indice_rendiconto.amount, valuta_corrente)
    totale_costi = Money(totale_costi.amount, valuta_corrente)
    totale_ricavi = Money(totale_ricavi.amount, valuta_corrente)
    return {
        'bilanci_costi': bilanci_costi,
        'totale_costi': totale_costi,
        'bilanci_ricavi': bilanci_ricavi,
        'totale_ricavi': totale_ricavi,
        'indice_rendiconto': indice_rendiconto
    }


def pulisci_elenco_bilanci(bilanci, saldi_aggregati, nome_saldo):
    elenco_pulito = []
    for bilancio_padre in bilanci:
        if bilancio_padre.piano_dei_conti.livello == 'sottoconto':
            elenco_pulito.append(bilancio_padre)
        else:
            if bilancio_padre.piano_dei_conti.id in saldi_aggregati:
                if saldi_aggregati[bilancio_padre.piano_dei_conti.id][nome_saldo]:
                    elenco_pulito.append(bilancio_padre)
    return elenco_pulito
    

def get_dati_stato_patrimoniale_conto_economico(queryset, data_inizio, data_fine, area, esercizio):
    valuta_corrente = area.valuta_default
    dati_stato_patrimoniale_conto_economico = []
    if not (queryset and area and esercizio):
        return dati_stato_patrimoniale_conto_economico
    escludi_chiusure_bilancio = preferences.ImpostazioniMagister.escludi_chiusure_bilancio
    queryset_base = queryset.filter(piano_dei_conti__livello__in=['conto', 'sottoconto'])
    ultima_data_movimento = queryset.aggregate(Max('ultima_data_movimenti'))['ultima_data_movimenti__max'] or date.today()

    saldi_aggregati = dict()

    def calcola_saldi(queryset_bilanci):
        bilanci = get_queryset_bilancio_calcolato(
            queryset_bilancio_base=queryset_bilanci, data_inizio=data_inizio,
            data_fine=data_fine, area=area, esercizio=esercizio,
            escludi_chiusure_bilancio=escludi_chiusure_bilancio
        )
        tot_attivo, tot_passivo, tot_costi, tot_ricavi = Money(0, valuta_corrente), Money(0, valuta_corrente), Money(0, valuta_corrente), Money(0, valuta_corrente)
        elenco_attivo, elenco_passivo, elenco_costi, elenco_ricavi = [], [], [], []
        
        for bilancio in bilanci:
            saldo = bilancio.get_saldo() or Money(0, valuta_corrente)
            if bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                if not bilancio.piano_dei_conti.livello == 'sottoconto':
                    elenco_attivo.append(bilancio)
                    elenco_passivo.append(bilancio)
                if saldo > Money(0, valuta_corrente):  # Saldo positivo, attivo
                    if bilancio.piano_dei_conti.livello == 'sottoconto':
                        tot_attivo += saldo
                        elenco_attivo.append(bilancio)
                    nodo_corrente = bilancio.piano_dei_conti
                    while nodo_corrente.parent:  # Propagazione verso il nodo padre
                        nodo_corrente = nodo_corrente.parent
                        if nodo_corrente.id not in saldi_aggregati:
                            saldi_aggregati[nodo_corrente.id] = {
                                'saldo_attivo': Money(0, valuta_corrente),
                                'saldo_passivo': Money(0, valuta_corrente),
                                'saldo_costi': Money(0, valuta_corrente),
                                'saldo_ricavi': Money(0, valuta_corrente),
                            }
                        saldi_aggregati[nodo_corrente.id]['saldo_attivo'] += saldo  # Aggiungi al saldo attivo del nodo padre
                elif saldo < Money(0, valuta_corrente):  # Saldo negativo, passivo
                    if bilancio.piano_dei_conti.livello == 'sottoconto':
                        tot_passivo += abs(saldo)
                        elenco_passivo.append(bilancio)
                    nodo_corrente = bilancio.piano_dei_conti
                    while nodo_corrente.parent:  # Propagazione verso il nodo padre
                        nodo_corrente = nodo_corrente.parent
                        if nodo_corrente.id not in saldi_aggregati:
                            saldi_aggregati[nodo_corrente.id] = {
                                'saldo_attivo': Money(0, valuta_corrente),
                                'saldo_passivo': Money(0, valuta_corrente),
                                'saldo_costi': Money(0, valuta_corrente),
                                'saldo_ricavi': Money(0, valuta_corrente),
                            }
                        saldi_aggregati[nodo_corrente.id]['saldo_passivo'] += abs(saldo)  # Aggiungi al saldo passivo del nodo padre
            elif bilancio.piano_dei_conti.tipologia in ['costi', 'ricavi', 'economico']:
                if not bilancio.piano_dei_conti.livello == 'sottoconto':
                    elenco_costi.append(bilancio)
                    elenco_ricavi.append(bilancio)
                if saldo > Money(0, valuta_corrente):  # Saldo positivo, costi
                    if bilancio.piano_dei_conti.livello == 'sottoconto':
                        tot_costi += saldo
                        elenco_costi.append(bilancio)
                    nodo_corrente = bilancio.piano_dei_conti
                    while nodo_corrente.parent:  # Propagazione verso il nodo padre
                        nodo_corrente = nodo_corrente.parent
                        if nodo_corrente.id not in saldi_aggregati:
                            saldi_aggregati[nodo_corrente.id] = {
                                'saldo_attivo': Money(0, valuta_corrente),
                                'saldo_passivo': Money(0, valuta_corrente),
                                'saldo_costi': Money(0, valuta_corrente),
                                'saldo_ricavi': Money(0, valuta_corrente),
                            }
                        saldi_aggregati[nodo_corrente.id]['saldo_costi'] += saldo  # Aggiungi al saldo costi del nodo padre
                elif saldo < Money(0, valuta_corrente):  # Saldo negativo, ricavi
                    if bilancio.piano_dei_conti.livello == 'sottoconto':
                        tot_ricavi += abs(saldo)
                        elenco_ricavi.append(bilancio)
                    nodo_corrente = bilancio.piano_dei_conti
                    while nodo_corrente.parent:  # Propagazione verso il nodo padre
                        nodo_corrente = nodo_corrente.parent
                        if nodo_corrente.id not in saldi_aggregati:
                            saldi_aggregati[nodo_corrente.id] = {
                                'saldo_attivo': Money(0, valuta_corrente),
                                'saldo_passivo': Money(0, valuta_corrente),
                                'saldo_costi': Money(0, valuta_corrente),
                                'saldo_ricavi': Money(0, valuta_corrente),
                            }
                        saldi_aggregati[nodo_corrente.id]['saldo_ricavi'] += abs(saldo)  # Aggiungi al saldo ricavi del nodo padre
        elenco_attivo = pulisci_elenco_bilanci(elenco_attivo, saldi_aggregati, 'saldo_attivo')
        elenco_passivo = pulisci_elenco_bilanci(elenco_passivo, saldi_aggregati, 'saldo_passivo')
        elenco_costi = pulisci_elenco_bilanci(elenco_costi, saldi_aggregati, 'saldo_costi')
        elenco_ricavi = pulisci_elenco_bilanci(elenco_ricavi, saldi_aggregati, 'saldo_ricavi')
        return {
            'tot_attivo': tot_attivo, 
            'tot_passivo': tot_passivo,
            'tot_costi': tot_costi, 
            'tot_ricavi': tot_ricavi,
            'elenco_attivo': elenco_attivo, 
            'elenco_passivo': elenco_passivo,
            'elenco_costi': elenco_costi, 
            'elenco_ricavi': elenco_ricavi
        }
    # STATO PATRIMONIALE
    bilanci_patrimoniali = queryset_base.filter(piano_dei_conti__tipologia__in=['liquidita', 'patrimoniale'])
    saldi_patrimoniali = calcola_saldi(bilanci_patrimoniali)
    # CONTO ECONOMICO
    bilanci_economici = queryset_base.filter(piano_dei_conti__tipologia__in=['costi', 'ricavi', 'economico'])
    saldi_economici = calcola_saldi(bilanci_economici)
    utile = saldi_economici['tot_ricavi'] > saldi_economici['tot_costi']
    perdita = not utile
    totale_pareggio = saldi_patrimoniali['tot_attivo'] if utile else saldi_patrimoniali['tot_passivo']
    utile_costi_ricavi = abs(saldi_economici['tot_ricavi'] - saldi_economici['tot_costi'])
    totale_utile = abs(saldi_patrimoniali['tot_attivo'] - saldi_patrimoniali['tot_passivo'])

    if saldi_economici['tot_ricavi'] > saldi_economici['tot_costi']:
        totale_pareggio_costi_ricavi = saldi_economici['tot_ricavi']
    else:
        totale_pareggio_costi_ricavi = saldi_economici['tot_costi']

    for costi in saldi_economici['elenco_costi']:
        if costi.piano_dei_conti.id in saldi_aggregati:
            costi.totale_costi = saldi_aggregati[costi.piano_dei_conti.id]['saldo_costi']

    for ricavi in saldi_economici['elenco_ricavi']:
        if ricavi.piano_dei_conti.id in saldi_aggregati:
            ricavi.totale_ricavi = saldi_aggregati[ricavi.piano_dei_conti.id]['saldo_ricavi']

    for attivo in saldi_patrimoniali['elenco_attivo']:
        if attivo.piano_dei_conti.id in saldi_aggregati:
            attivo.totale_attivo = saldi_aggregati[attivo.piano_dei_conti.id]['saldo_attivo']

    for passivo in saldi_patrimoniali['elenco_passivo']:
        if passivo.piano_dei_conti.id in saldi_aggregati:
            passivo.totale_passivo = saldi_aggregati[passivo.piano_dei_conti.id]['saldo_passivo']
    return {
        'elenco_bilanci_attivo': saldi_patrimoniali['elenco_attivo'],
        'elenco_bilanci_passivo': saldi_patrimoniali['elenco_passivo'],
        'elenco_bilanci_costi': saldi_economici['elenco_costi'],
        'elenco_bilanci_ricavi': saldi_economici['elenco_ricavi'],
        'totale_attivo': saldi_patrimoniali['tot_attivo'],
        'totale_passivo': saldi_patrimoniali['tot_passivo'],
        'totale_utile': totale_utile,
        'totale_pareggio': totale_pareggio,
        'totale_costi': saldi_economici['tot_costi'],
        'totale_ricavi': saldi_economici['tot_ricavi'],
        'utile_costi_ricavi': utile_costi_ricavi,
        'utile': utile,
        'perdita': perdita,
        'totale_pareggio_costi_ricavi': totale_pareggio_costi_ricavi,
        'ultima_data_movimento': ultima_data_movimento,
        'area': area,
        'esercizio': esercizio
    }


def get_bilancio_parziale_area_conto(conto, area, esercizio, saldo_liquidita=False, saldo_ricavi=False, livello=1):
    bilancio_area = dict()
    bilancio_area_figlia = BilancioConto.objects.filter(
        area=area, esercizio=esercizio, piano_dei_conti=conto
    )
    bilancio_completo_area = get_queryset_bilancio_calcolato(bilancio_area_figlia, area, esercizio)
    if bilancio_completo_area:
        if saldo_liquidita:
            saldo = bilancio_completo_area[0].get_saldo()
        elif saldo_ricavi:
            saldo = -(bilancio_completo_area[0].get_saldo())
        else:
            saldo = bilancio_completo_area[0].get_saldo_abs()
        if saldo:
            if livello == 1:
                stringa_puntato = chr(187)
            if livello == 2:
                stringa_puntato = ' -- '
            bilancio_area['piano_dei_conti'] = {
                'codice': '-',
                'descrizione': ' %s %s' % (stringa_puntato, area)
            }
            bilancio_area['get_saldo_abs'] = saldo
    return bilancio_area


def aggiungi_bilancio_parziale_area(elenco_bilanci, elenco_aree_figlie, esercizio_corrente, saldo_liquidita=False, saldo_ricavi=False):
    nuovo_bilancio = []
    if elenco_bilanci and elenco_aree_figlie and esercizio_corrente:
        for conto in elenco_bilanci:
            nuovo_bilancio.append(conto)
            if conto.piano_dei_conti.gestione_parziali_area:
                for area_figlia in elenco_aree_figlie:
                    livello = 1
                    bilancio_area = get_bilancio_parziale_area_conto(
                        conto.piano_dei_conti, area_figlia, esercizio_corrente, saldo_liquidita, saldo_ricavi, livello
                    )
                    if bilancio_area:
                        nuovo_bilancio.append(bilancio_area)
                        aree_livello_successivo = area_figlia.get_children()
                        if aree_livello_successivo:
                            for area_successiva in aree_livello_successivo:
                                livello = 2
                                bilancio_area_successiva = get_bilancio_parziale_area_conto(
                                    conto.piano_dei_conti, area_successiva, esercizio_corrente, saldo_liquidita, saldo_ricavi, livello
                                )
                                if bilancio_area_successiva:
                                    nuovo_bilancio.append(bilancio_area_successiva)
    return nuovo_bilancio


def aggiungi_bilancio_parziale_cliente_fornitore(elenco_bilanci, area_corrente, esercizio_corrente, saldo_liquidita=False, saldo_ricavi=False):
    nuovo_bilancio = []
    if elenco_bilanci and esercizio_corrente:
        for conto in elenco_bilanci:
            nuovo_bilancio.append(conto)
            if conto.piano_dei_conti.gestione_anagrafica:
                elenco_aree = area_corrente.get_descendants(include_self=True)
                elenco_dettagli_clienti = DettaglioMovimentoPrimaNota.objects.filter(
                    movimento_primanota__area__in=elenco_aree,
                    movimento_primanota__esercizio=esercizio_corrente,
                    anagrafica__isnull=False,
                    piano_dei_conti=conto.piano_dei_conti,
                ).order_by('anagrafica').values('anagrafica__id', 'anagrafica__ragione_sociale').distinct()
                for cliente in elenco_dettagli_clienti:
                    dettagli_cliente = DettaglioMovimentoPrimaNota.objects.filter(
                        movimento_primanota__area__in=elenco_aree,
                        movimento_primanota__esercizio=esercizio_corrente,
                        anagrafica__id=cliente['anagrafica__id'],
                        piano_dei_conti=conto.piano_dei_conti,
                    )
                    bilancio_corrente = BilancioConto.objects.filter(
                        area=area_corrente, esercizio=esercizio_corrente, piano_dei_conti=conto.piano_dei_conti
                    )
                    bilancio_cliente = get_queryset_bilancio_calcolato(
                        bilancio_corrente, area_corrente, esercizio_corrente, None, None, dettagli_cliente
                    )
                    saldo = bilancio_cliente[0].get_saldo_abs()
                    if saldo:
                        bilancio_area = dict()
                        bilancio_area['piano_dei_conti'] = {
                            'codice': '-',
                            'descrizione': ' - %s' % (cliente['anagrafica__ragione_sociale'])
                        }
                        bilancio_area['get_saldo_abs'] = saldo
                        nuovo_bilancio.append(bilancio_area)
    return nuovo_bilancio


def aggiungi_dati_parziali_aree(dati_stato_patrimoniale_conto_economico, area_corrente, esercizio_corrente):
    if dati_stato_patrimoniale_conto_economico:
        elenco_aree_figlie = area_corrente.get_children()
        # ATTIVO
        saldo_liquidita = True
        elenco_bilanci_attivo = dati_stato_patrimoniale_conto_economico['elenco_bilanci_attivo']
        nuovo_bilancio_attivo = aggiungi_bilancio_parziale_area(elenco_bilanci_attivo, elenco_aree_figlie, esercizio_corrente, saldo_liquidita)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_attivo'] = nuovo_bilancio_attivo
        # PASSIVO
        elenco_bilanci_passivo = dati_stato_patrimoniale_conto_economico['elenco_bilanci_passivo']
        nuovo_bilancio_passivo = aggiungi_bilancio_parziale_area(elenco_bilanci_passivo, elenco_aree_figlie, esercizio_corrente)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_passivo'] = nuovo_bilancio_passivo
        # RICAVI
        saldo_ricavi = True
        elenco_bilanci_ricavi = dati_stato_patrimoniale_conto_economico['elenco_bilanci_ricavi']
        nuovo_bilancio_ricavi = aggiungi_bilancio_parziale_area(elenco_bilanci_ricavi, elenco_aree_figlie, esercizio_corrente, False, saldo_ricavi)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_ricavi'] = nuovo_bilancio_ricavi
        # COSTI
        elenco_bilanci_costi = dati_stato_patrimoniale_conto_economico['elenco_bilanci_costi']
        nuovo_bilancio_costi = aggiungi_bilancio_parziale_area(elenco_bilanci_costi, elenco_aree_figlie, esercizio_corrente)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_costi'] = nuovo_bilancio_costi
    return dati_stato_patrimoniale_conto_economico


def aggiungi_dati_clienti_fornitori(dati_stato_patrimoniale_conto_economico, area_corrente, esercizio_corrente):
    if dati_stato_patrimoniale_conto_economico:
        # ATTIVO
        saldo_liquidita = True
        elenco_bilanci_attivo = dati_stato_patrimoniale_conto_economico['elenco_bilanci_attivo']
        nuovo_bilancio_attivo = aggiungi_bilancio_parziale_cliente_fornitore(
            elenco_bilanci_attivo, area_corrente, esercizio_corrente, saldo_liquidita
        )
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_attivo'] = nuovo_bilancio_attivo
        # PASSIVO
        elenco_bilanci_passivo = dati_stato_patrimoniale_conto_economico['elenco_bilanci_passivo']
        nuovo_bilancio_passivo = aggiungi_bilancio_parziale_cliente_fornitore(elenco_bilanci_passivo, area_corrente, esercizio_corrente)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_passivo'] = nuovo_bilancio_passivo
        # RICAVI
        saldo_ricavi = True
        elenco_bilanci_ricavi = dati_stato_patrimoniale_conto_economico['elenco_bilanci_ricavi']
        nuovo_bilancio_ricavi = aggiungi_bilancio_parziale_cliente_fornitore(
            elenco_bilanci_ricavi, area_corrente, esercizio_corrente, False, saldo_ricavi
        )
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_ricavi'] = nuovo_bilancio_ricavi
        # COSTI
        elenco_bilanci_costi = dati_stato_patrimoniale_conto_economico['elenco_bilanci_costi']
        nuovo_bilancio_costi = aggiungi_bilancio_parziale_cliente_fornitore(elenco_bilanci_costi, area_corrente, esercizio_corrente)
        dati_stato_patrimoniale_conto_economico['elenco_bilanci_costi'] = nuovo_bilancio_costi
    return dati_stato_patrimoniale_conto_economico


def get_saldo_periodo_precedente(partitario, area_corrente, esercizio_corrente, anagrafica=None, escludi_chiusure_bilancio=False):
    totale_avere_precedente = Money('0.00', area_corrente.valuta_default)
    totale_dare_precedente = Money('0.00', area_corrente.valuta_default)
    if area_corrente and esercizio_corrente and partitario:
        elenco_aree_figlie = area_corrente.get_descendants(include_self=True)
        if anagrafica:
            elenco_dettagli = DettaglioMovimentoPrimaNota.objects.filter(
                movimento_primanota__data_operazione__lt=partitario.movimento_primanota.data_operazione,
                movimento_primanota__area__in=elenco_aree_figlie,
                movimento_primanota__esercizio=esercizio_corrente,
                anagrafica=anagrafica,
            )
        else:
            elenco_dettagli = DettaglioMovimentoPrimaNota.objects.filter(
                movimento_primanota__data_operazione__lt=partitario.movimento_primanota.data_operazione,
                movimento_primanota__area__in=elenco_aree_figlie,
                movimento_primanota__esercizio=esercizio_corrente,
                piano_dei_conti=partitario.piano_dei_conti,
            )
        if elenco_dettagli:
            if escludi_chiusure_bilancio:
                elenco_dettagli = elenco_dettagli.exclude(movimento_primanota__genere_movimento='chiusura')
            elenco_dettagli_dare = elenco_dettagli.filter(categoria='dare')
            elenco_valori_convertiti_dare = ConversioneDettaglioMovimentoPrimaNota.objects.filter(
                dettaglio_movimento_primanota__in=elenco_dettagli_dare,
                importo_currency=area_corrente.valuta_default
            )
            elenco_dettagli_avere = elenco_dettagli.filter(categoria='avere')
            elenco_valori_convertiti_avere = ConversioneDettaglioMovimentoPrimaNota.objects.filter(
                dettaglio_movimento_primanota__in=elenco_dettagli_avere,
                importo_currency=area_corrente.valuta_default
            )
            saldo_dare = elenco_valori_convertiti_dare.aggregate(totale=Sum('importo'))
            saldo_avere = elenco_valori_convertiti_avere.aggregate(totale=Sum('importo'))
            if saldo_dare['totale']:
                totale_dare_precedente += Money(saldo_dare['totale'], area_corrente.valuta_default)
            if saldo_avere['totale']:
                totale_avere_precedente += Money(saldo_avere['totale'], area_corrente.valuta_default)
    return {
        'totale_dare': totale_dare_precedente,
        'totale_avere': totale_avere_precedente,
        'saldo': totale_dare_precedente - totale_avere_precedente,
    }


def get_saldo_centri_di_costo_periodo_precedente(partitario, area_corrente, esercizio_corrente):
    saldo_precedente = Money('0.00', area_corrente.valuta_default)
    if area_corrente and esercizio_corrente and partitario:
        elenco_aree_figlie = area_corrente.get_descendants(include_self=True)
        elenco_dettagli = DettaglioMovimentoAnalitico.objects.filter(
            movimento_primanota__data_operazione__lt=partitario.movimento_primanota.data_operazione,
            movimento_primanota__area__in=elenco_aree_figlie,
            movimento_primanota__esercizio=esercizio_corrente,
            centro_di_costo=partitario.centro_di_costo,
        )
        if elenco_dettagli:
            saldo = elenco_dettagli.aggregate(totale=Sum('importo'))
            if saldo['totale']:
                saldo_precedente += Money(saldo['totale'], area_corrente.valuta_default)
    return {
        'saldo': saldo_precedente
    }


def get_esercizio_precedente(esercizio_corrente):
    """
    Trova l'esercizio precedente a quello corrente cercando in tutti gli esercizi
    quello che ha il campo esercizio_successivo uguale a quello corrente.
    """
    from common.anagraficabase.models import Esercizio
    try:
        esercizio_precedente = Esercizio.objects.get(esercizio_successivo=esercizio_corrente)
        return esercizio_precedente
    except Esercizio.DoesNotExist:
        return None
    except Esercizio.MultipleObjectsReturned:
        # Se ci sono più esercizi che puntano allo stesso successivo, prendi il primo
        return Esercizio.objects.filter(esercizio_successivo=esercizio_corrente).first()


def genera_lista_conti_comparata(conti_esercizio_corrente, conti_esercizio_precedente, area, nome_saldo):
    lista_conti = dict()
    for conto_esercizio_corrente in conti_esercizio_corrente:
        if conto_esercizio_corrente.piano_dei_conti.livello == 'sottoconto':
            saldo_corrente = conto_esercizio_corrente.get_saldo()
        else:
            if hasattr(conto_esercizio_corrente, nome_saldo):
                saldo_corrente = getattr(conto_esercizio_corrente, nome_saldo)
            else:
                saldo_corrente = Money(0, area.valuta_default)
        lista_conti[conto_esercizio_corrente.piano_dei_conti.codice] = {
            'codice': conto_esercizio_corrente.piano_dei_conti.codice,
            'descrizione': conto_esercizio_corrente.piano_dei_conti.descrizione,
            'livello': conto_esercizio_corrente.piano_dei_conti.livello,
            'saldo_corrente': saldo_corrente,
            'saldo_precedente': Money(0, area.valuta_default),
            'variazione': saldo_corrente,
        }
    for conto_esercizio_precedente in conti_esercizio_precedente:
        if conto_esercizio_precedente.piano_dei_conti.livello == 'sottoconto':
            saldo_precedente = conto_esercizio_precedente.get_saldo()
        else:
            if hasattr(conto_esercizio_precedente, nome_saldo):
                saldo_precedente = getattr(conto_esercizio_precedente, nome_saldo)
            else:
                saldo_precedente = Money(0, area.valuta_default)
        if conto_esercizio_precedente.piano_dei_conti.codice in lista_conti:
            conto = lista_conti[conto_esercizio_precedente.piano_dei_conti.codice]
            conto['saldo_precedente'] = saldo_precedente
            conto['variazione'] = conto['saldo_corrente'] - conto['saldo_precedente']
        else:
            lista_conti[conto_esercizio_precedente.piano_dei_conti.codice] = {
                'codice': conto_esercizio_precedente.piano_dei_conti.codice,
                'descrizione': conto_esercizio_precedente.piano_dei_conti.descrizione,
                'livello': conto_esercizio_precedente.piano_dei_conti.livello,
                'saldo_corrente': Money(0, area.valuta_default),
                'saldo_precedente': saldo_precedente,
                'variazione': -saldo_precedente,
            }
    return sorted(list(lista_conti.values()), key=lambda x: x['codice'])


def get_dati_bilancio_comparativo(queryset_attuale, data_inizio, data_fine, area, esercizio):
    """
    Genera i dati del bilancio comparativo tra l'esercizio corrente e quello precedente.
    Utilizza la funzione get_dati_stato_patrimoniale_conto_economico per entrambi gli esercizi.
    """
    # Dati esercizio corrente
    dati_corrente = get_dati_stato_patrimoniale_conto_economico(
        queryset_attuale, data_inizio, data_fine, area, esercizio
    )
    # Trova esercizio precedente
    esercizio_precedente = get_esercizio_precedente(esercizio)
    dati_precedente = None
    if esercizio_precedente:
        # Queryset per l'esercizio precedente
        elenco_piani_dei_conti = [bil.piano_dei_conti for bil in queryset_attuale]
        queryset_precedente = BilancioConto.objects.filter(
            piano_dei_conti__in=elenco_piani_dei_conti, area=area, esercizio=esercizio_precedente
        )
        data_inizio_precedente = None
        data_fine_precedente = None
        if data_inizio:
            data_inizio_precedente = date(
                year=data_inizio.year - 1, 
                month=data_inizio.month, 
                day=data_inizio.day
            )
        if data_fine:
            data_fine_precedente = date(
                year=data_fine.year - 1, 
                month=data_fine.month, 
                day=data_fine.day
            )
        dati_precedente = get_dati_stato_patrimoniale_conto_economico(
            queryset_precedente, 
            data_inizio_precedente, 
            data_fine_precedente, area, esercizio_precedente
        )
    lista_conti_attivo = []
    lista_conti_passivo = []
    lista_conti_costi = []
    lista_conti_ricavi = []
    if dati_corrente and dati_precedente:
        # Attivo
        lista_conti_attivo = genera_lista_conti_comparata(
            dati_corrente['elenco_bilanci_attivo'], 
            dati_precedente['elenco_bilanci_attivo'], 
            area, 'totale_attivo',
        )
        lista_conti_passivo = genera_lista_conti_comparata(
            dati_corrente['elenco_bilanci_passivo'], 
            dati_precedente['elenco_bilanci_passivo'], 
            area, 'totale_passivo',
        )
        lista_conti_costi = genera_lista_conti_comparata(
            dati_corrente['elenco_bilanci_costi'], 
            dati_precedente['elenco_bilanci_costi'], 
            area, 'totale_costi',
        )
        lista_conti_ricavi = genera_lista_conti_comparata(
            dati_corrente['elenco_bilanci_ricavi'], 
            dati_precedente['elenco_bilanci_ricavi'], 
            area, 'totale_ricavi',
        )
    return {
        'lista_conti_attivo': lista_conti_attivo,
        'lista_conti_passivo': lista_conti_passivo,
        'lista_conti_costi': lista_conti_costi,
        'lista_conti_ricavi': lista_conti_ricavi,
        'esercizio_precedente': esercizio_precedente,
        'totale_attivo_corrente': dati_corrente['totale_attivo'],
        'totale_passivo_corrente': dati_corrente['totale_passivo'],
        'totale_costi_corrente': dati_corrente['totale_costi'],
        'totale_ricavi_corrente': dati_corrente['totale_ricavi'],
        'totale_attivo_precedente': dati_precedente['totale_attivo'],
        'totale_passivo_precedente': dati_precedente['totale_passivo'],
        'totale_costi_precedente': dati_precedente['totale_costi'],
        'totale_ricavi_precedente': dati_precedente['totale_ricavi'],
        'variazione_attivo': dati_corrente['totale_attivo'] - dati_precedente['totale_attivo'],
        'variazione_passivo': dati_corrente['totale_passivo'] - dati_precedente['totale_passivo'],
        'variazione_costi': dati_corrente['totale_costi'] - dati_precedente['totale_costi'],
        'variazione_ricavi': dati_corrente['totale_ricavi'] - dati_precedente['totale_ricavi'],
    }


    

