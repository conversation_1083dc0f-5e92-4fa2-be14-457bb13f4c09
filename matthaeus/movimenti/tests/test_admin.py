from decimal import Decimal
from datetime import date

from django.urls import reverse
from django.test import TestCase
from django.core.exceptions import ValidationError
from unittest import skip

from common.authentication.tests.factories import UtenteMagisterFactory
from common.anagraficabase.tests.factories import PianoDeiContiFactory
from common.anagraficabase.tests.factories import AreaFactory, EsercizioFactory
from matthaeus.movimenti.models import MovimentoPrimaNota, DettaglioMovimentoPrimaNota

from . import factories


class AppAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_app_index(self):
        url = reverse('matthaeus:app_list', args=('movimenti',))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TipoMovimentoAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_tipomovimento_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_tipomovimento_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.TipoMovimentoFactory()
        url = reverse('matthaeus:movimenti_tipomovimento_change',
                      args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        obj = factories.TipoMovimentoFactory()
        url = reverse('matthaeus:movimenti_tipomovimento_delete',
                      args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class CausaleContabileAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_causalecontabile_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_causalecontabile_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_senza_dettagli(self):
        obj = factories.CausaleContabileFactory()
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettaglio_causale_dare(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='dare')
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettaglio_causale_avere(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettagli_causale_dare_avere(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='dare')
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_2_dettagli_causale_dare_avere(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='dare')
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='avere')
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='dare')
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettaglio_causale_analitica(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleAnaliticaFactory(causale_contabile=obj)
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettagli_causale_analitica_ripartita(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleAnaliticaFactory(
            causale_contabile=obj, percentuale=Decimal('80.00'))
        factories.DettaglioCausaleAnaliticaFactory(
            causale_contabile=obj, percentuale=Decimal('20.00'))
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_con_dettagli_causale_analitica_e_non(self):
        obj = factories.CausaleContabileFactory()
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='dare')
        factories.DettaglioCausaleContabileFactory(
            causale_contabile=obj, categoria='avere')
        factories.DettaglioCausaleAnaliticaFactory(
            causale_contabile=obj, percentuale=Decimal('80'))
        factories.DettaglioCausaleAnaliticaFactory(
            causale_contabile=obj, percentuale=Decimal('20.00'))
        url = reverse(
            'matthaeus:movimenti_causalecontabile_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_delete(self):
        obj = factories.CausaleContabileFactory()
        url = reverse(
            'matthaeus:movimenti_causalecontabile_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)


class MovimentoPrimaNotaAdminTest(TestCase):
    def setUp(self):
        self.area_corrente = AreaFactory()
        self.esercizio_corrente = EsercizioFactory()
        self.utente_corrente = UtenteMagisterFactory(
            username='test', area_corrente=self.area_corrente,
            esercizio_corrente=self.esercizio_corrente,
        )
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse(
            'matthaeus:movimenti_movimentoprimanota_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_movimentoprimanota_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail_altro_esercizio(self):
        altro_esercizio = EsercizioFactory()
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=altro_esercizio,
            descrizione='PROVA_ALTRO_ESERCIZIO'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/matthaeus/')
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'PROVA_ALTRO_ESERCIZIO')

    def test_detail_altra_area(self):
        altra_area = AreaFactory()
        obj = factories.MovimentoPrimaNotaFactory(
            area=altra_area, esercizio=self.esercizio_corrente,
            descrizione='PROVA_ALTRA_AREA'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/matthaeus/')
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'PROVA_ALTRA_AREA')

    def test_detail_altro_esercizio_e_area(self):
        altra_area = AreaFactory()
        altro_esercizio = EsercizioFactory()
        obj = factories.MovimentoPrimaNotaFactory(
            area=altra_area, esercizio=altro_esercizio,
            descrizione='PROVA_ALTRA_AREA_ED_ESERCIZIO'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/matthaeus/')
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'PROVA_ALTRA_AREA_ED_ESERCIZIO')

    def test_con_dettaglio_dare(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente,
            descrizione='PROVA_DETTAGLIO_DARE'
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PROVA_DETTAGLIO_DARE')

    def test_con_dettaglio_avere(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_con_dettaglio_dare_e_avere(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente,
            descrizione='PROVA_DETTAGLIO_AVERE'
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='dare')
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere')
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PROVA_DETTAGLIO_AVERE')

    def test_con_2_dettaglio_dare_e_avere(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='dare',
            importo=Decimal('150.09')
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere',
            importo=Decimal('76900.36'),
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='dare',
            importo=Decimal('136.79'),
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere',
            importo=Decimal('0.99'),
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '286,88')
        self.assertContains(response, '76.901,35')
        self.assertContains(response, '-76.614,47')

    def test_con_dettaglio_analitico(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_con_dettaglio_dare_avere_analitico(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='dare')
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere')
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        DettaglioMovimentoPrimaNota.objects.all().delete()
        MovimentoPrimaNota.objects.all().delete()
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='dare'
        )
        factories.DettaglioMovimentoPrimaNotaFactory(
            movimento_primanota=obj, categoria='avere'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_stampa_sintetica(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse('matthaeus:movimenti_movimentoprimanota_stampasintetica')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_stampa_analitica(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse('matthaeus:movimenti_movimentoprimanota_stampaanalitica')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_libro_giornale(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse('matthaeus:movimenti_movimentoprimanota_librogiornale')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_libro_giornale_elenco(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse('matthaeus:movimenti_movimentoprimanota_librogiornaleelenco')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_rigenera_numerazione(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente
        )
        factories.DettaglioMovimentoAnaliticoFactory(movimento_primanota=obj)
        url = reverse('matthaeus:movimenti_movimentoprimanota_rigeneranumerazione')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    # crea un test per verificare la data_operazione e la data_registrazione del MovimentoPrimaNota
    def test_data_operazione(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente,
            data_operazione='2011-10-13'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_data_registrazione(self):
        obj = factories.MovimentoPrimaNotaFactory(
            area=self.area_corrente, esercizio=self.esercizio_corrente,
            data='2011-10-13'
        )
        url = reverse(
            'matthaeus:movimenti_movimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    @skip('da rivedere')
    def test_data_registrazione_dentro_periodo_esercizio(self):
        # crea un'AreaFactory con un esercizio con data_inizio e data_fine
        area = AreaFactory()
        esercizio = EsercizioFactory(data_inizio=date(2011, 1, 1), data_fine=date(2011, 12, 31))
        post_data = {
            'descrizione': 'prova',
            'data': date(2011, 10, 13),
            'data_operazione': date(2011, 10, 13),
            'descrizione': 'prova',
            'csrfmiddlewaretoken': '533074bb0e87d86387f10cf0166b6736',
            'numero_operazione': 685,
            'area': area.pk,
            'esercizio': esercizio.pk,
            '_continue': 'Salva e continua le modifiche',
            'dettagliodareavanzato_set-TOTAL_FORMS': 3,
            'dettagliodareavanzato_set-INITIAL_FORMS': 0,
            'dettagliodareavanzato_set-MIN_NUM_FORMS': 0,
            'dettagliodareavanzato_set-MAX_NUM_FORMS': 1000,
             'dettaglioavereavanzato_set-TOTAL_FORMS': 3,
            'dettaglioavereavanzato_set-INITIAL_FORMS': 0,
            'dettaglioavereavanzato_set-MIN_NUM_FORMS': 0,
            'dettaglioavereavanzato_set-MAX_NUM_FORMS': 1000,
            'partitarioclienti_set-TOTAL_FORMS': 0,
            'partitarioclienti_set-INITIAL_FORMS': 0,
            'partitarioclienti_set-MIN_NUM_FORMS': 0,
            'partitarioclienti_set-MAX_NUM_FORMS': 0,
            'partitariofornitori_set-TOTAL_FORMS': 0,
            'partitariofornitori_set-INITIAL_FORMS': 0,
            'partitariofornitori_set-MIN_NUM_FORMS': 0,
            'partitariofornitori_set-MAX_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-TOTAL_FORMS': 3,
            'dettagliomovimentoanalitico_set-INITIAL_FORMS': 0,
            'dettagliomovimentoanalitico_set-MIN_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-MAX_NUM_FORMS': 1000,            
            'allegati-documentoallegato-content_type-object_id-TOTAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MAX_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-TOTAL_FORMS': 1,
            'allegati-documentoallegato-content_type-object_id-2-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MAX_NUM_FORMS': 1,            
        }
        response = self.client.post(reverse('matthaeus:movimenti_movimentoprimanota_add'), post_data)
        self.assertEqual(response.status_code, 200)

    # crea un test per verificare l'inserimento di un MovimentoPrimaNota simile al precedente ma con data_operazione fuori dal periodo esercizio
    @skip('da rivedere')
    def test_data_operazione_fuori_periodo_esercizio(self):
        # crea un'AreaFactory con un esercizio con data_inizio e data_fine
        area = AreaFactory()
        esercizio = EsercizioFactory(data_inizio=date(2011, 1, 1), data_fine=date(2011, 12, 31))
        post_data = {
            'descrizione': 'prova',
            'data': date(2011, 10, 13),
            'data_operazione': date(2023, 10, 13),
            'descrizione': 'prova',
            'csrfmiddlewaretoken': '533074bb0e87d86387f10cf0166b6736',
            'numero_operazione': 695,
            'area': area.pk,
            'esercizio': esercizio.pk,
            '_continue': 'Salva e continua le modifiche',
            'dettagliodareavanzato_set-TOTAL_FORMS': 3,
            'dettagliodareavanzato_set-INITIAL_FORMS': 0,
            'dettagliodareavanzato_set-MIN_NUM_FORMS': 0,
            'dettagliodareavanzato_set-MAX_NUM_FORMS': 1000,
            'dettaglioavereavanzato_set-TOTAL_FORMS': 3,
            'dettaglioavereavanzato_set-INITIAL_FORMS': 0,
            'dettaglioavereavanzato_set-MIN_NUM_FORMS': 0,
            'dettaglioavereavanzato_set-MAX_NUM_FORMS': 1000,
            'partitarioclienti_set-TOTAL_FORMS': 0,
            'partitarioclienti_set-INITIAL_FORMS': 0,
            'partitarioclienti_set-MIN_NUM_FORMS': 0,
            'partitarioclienti_set-MAX_NUM_FORMS': 0,
            'partitariofornitori_set-TOTAL_FORMS': 0,
            'partitariofornitori_set-INITIAL_FORMS': 0,
            'partitariofornitori_set-MIN_NUM_FORMS': 0,
            'partitariofornitori_set-MAX_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-TOTAL_FORMS': 3,
            'dettagliomovimentoanalitico_set-INITIAL_FORMS': 0,
            'dettagliomovimentoanalitico_set-MIN_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-MAX_NUM_FORMS': 1000,
            'allegati-documentoallegato-content_type-object_id-TOTAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MAX_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-TOTAL_FORMS': 1,
            'allegati-documentoallegato-content_type-object_id-2-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MAX_NUM_FORMS': 1,
        }
        response = self.client.post(reverse('matthaeus:movimenti_movimentoprimanota_add'), post_data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Attenzione')
        self.assertContains(response, 'La data operazione inserita e')
        self.assertContains(response, 'al di fuori del periodo impostato per l')
        self.assertContains(response, 'esercizio corrente')

    @skip('da rivedere')
    def test_data_registrazione_successiva_data_operazione(self):
        # crea un'AreaFactory con un esercizio con data_inizio e data_fine
        area = AreaFactory()
        esercizio = EsercizioFactory(data_inizio=date(2011, 1, 1), data_fine=date(2011, 12, 31))
        post_data = {
            'descrizione': 'prova',
            'data': date(2011, 10, 11),
            'data_operazione': date(2011, 10, 13),
            'descrizione': 'prova',
            'csrfmiddlewaretoken': '533074bb0e87d86387f10cf0166b6736',
            'numero_operazione': 1234,
            'area': area.pk,
            'esercizio': esercizio.pk,
            '_continue': 'Salva e continua le modifiche',
            'dettagliodareavanzato_set-TOTAL_FORMS': 3,
            'dettagliodareavanzato_set-INITIAL_FORMS': 0,
            'dettagliodareavanzato_set-MIN_NUM_FORMS': 0,
            'dettagliodareavanzato_set-MAX_NUM_FORMS': 1000,
            'dettaglioavereavanzato_set-TOTAL_FORMS': 3,
            'dettaglioavereavanzato_set-INITIAL_FORMS': 0,
            'dettaglioavereavanzato_set-MIN_NUM_FORMS': 0,
            'dettaglioavereavanzato_set-MAX_NUM_FORMS': 1000,
            'partitarioclienti_set-TOTAL_FORMS': 0,
            'partitarioclienti_set-INITIAL_FORMS': 0,
            'partitarioclienti_set-MIN_NUM_FORMS': 0,
            'partitarioclienti_set-MAX_NUM_FORMS': 0,
            'partitariofornitori_set-TOTAL_FORMS': 0,
            'partitariofornitori_set-INITIAL_FORMS': 0,
            'partitariofornitori_set-MIN_NUM_FORMS': 0,
            'partitariofornitori_set-MAX_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-TOTAL_FORMS': 3,
            'dettagliomovimentoanalitico_set-INITIAL_FORMS': 0,
            'dettagliomovimentoanalitico_set-MIN_NUM_FORMS': 0,
            'dettagliomovimentoanalitico_set-MAX_NUM_FORMS': 1000,
            'allegati-documentoallegato-content_type-object_id-TOTAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-MAX_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-TOTAL_FORMS': 1,
            'allegati-documentoallegato-content_type-object_id-2-INITIAL_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MIN_NUM_FORMS': 0,
            'allegati-documentoallegato-content_type-object_id-2-MAX_NUM_FORMS': 1,
        }
        response = self.client.post(reverse('matthaeus:movimenti_movimentoprimanota_add'), post_data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Attenzione')
        self.assertContains(response, 'La data operazione inserita e')
        self.assertContains(response, 'successiva alla data registrazione')


class BilancioContoAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_bilancioconto_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_bilancioconto_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse('matthaeus:movimenti_bilancioconto_change',
                      args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete_admin(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse('matthaeus:movimenti_bilancioconto_delete',
                      args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_delete_utente_semplice(self):
        self.utente = UtenteMagisterFactory(
            username='semplice', is_superuser=False)
        self.assertTrue(self.client.login(
            username='semplice', password='pass'))
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse('matthaeus:movimenti_bilancioconto_delete',
                      args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_export_riepilogo_centri_costo(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'export_riepilogo_centri_di_costo', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_export_bilancio_xls(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'export_bilancio_xls', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_bilancio_sottoconti(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_sottoconti', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_bilancio_sottoconti_aggregazione(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_sottoconti_aggregazione', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)
        
    def test_bilancio_conti(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_conti', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_bilancio_conti_aggregazioni(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_conti_aggregazioni', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_bilancio_mastri(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_mastri', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_stato_patrimoniale(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'stato_patrimoniale', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_stato_patrimoniale_clienti_fornitori(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'stato_patrimoniale_clienti_fornitori', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_rendiconto_finanziario_conti(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'rendiconto_finanziario_conti', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data, follow=True)
        self.assertEqual(response.status_code, 200)

    def test_bilancio_riclassificato_conti(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'bilancio_riclassificato_conti', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_elenco_partitari(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'elenco_partitari', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_vedi_grafico_conto_economico(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'vedi_grafico_conto_economico', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_vedi_grafico_riepilogo_costi(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'vedi_grafico_riepilogo_costi', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_vedi_grafico_riepilogo_ricavi(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'vedi_grafico_riepilogo_ricavi', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)

    def test_vedi_grafico_budget(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'vedi_grafico_budget', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data, follow=True)
        self.assertEqual(response.status_code, 200)

    def test_vedi_grafico_liquidita(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        data = {'action': 'vedi_grafico_liquidita', '_selected_action': [obj.id, ]}
        change_url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        response = self.client.post(change_url, data)
        self.assertEqual(response.status_code, 200)


class BilancioContoPreventivoAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse(
            'matthaeus:movimenti_bilanciocontopreventivo_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_bilanciocontopreventivo_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoPreventivoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse(
            'matthaeus:movimenti_bilanciocontopreventivo_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete_admin(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoPreventivoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse(
            'matthaeus:movimenti_bilanciocontopreventivo_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_delete_utente_semplice(self):
        self.utente = UtenteMagisterFactory(
            username='semplice', is_superuser=False)
        self.assertTrue(self.client.login(
            username='semplice', password='pass'))
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        obj = factories.BilancioContoPreventivoFactory(
            piano_dei_conti=piano_dei_conti,
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        url = reverse(
            'matthaeus:movimenti_bilanciocontopreventivo_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)


class MastrinoSottocontiAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_mastrinosottoconti_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_mastrinosottoconti_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.MastrinoSottocontiFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_mastrinosottoconti_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete_admin(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.MastrinoSottocontiFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_mastrinosottoconti_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_delete_utente_semplice(self):
        self.utente = UtenteMagisterFactory(
            username='semplice', is_superuser=False)
        self.assertTrue(self.client.login(
            username='semplice', password='pass'))
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.MastrinoSottocontiFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_mastrinosottoconti_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)


class PartitarioAnagraficaAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_partitarioanagrafica_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_partitarioanagrafica_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.PartitarioAnagraficaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_partitarioanagrafica_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_esporta_partitari(self):
        from djmoney.money import Money
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.PartitarioAnagraficaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
            controvalore=Money('150.00', 'USD'),
            categoria='dare'
        )
        url = reverse('matthaeus:movimenti_partitarioanagrafica_esportapartitari')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        # Verifica che il CSV contenga le nuove colonne del controvalore
        content = response.content.decode('utf-8')
        self.assertIn('controvalore dare', content)
        self.assertIn('controvalore avere', content)
        self.assertIn('controvalore saldo', content)

    def test_delete_admin(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.PartitarioAnagraficaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_partitarioanagrafica_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_delete_utente_semplice(self):
        self.utente = UtenteMagisterFactory(
            username='semplice', is_superuser=False)
        self.assertTrue(self.client.login(
            username='semplice', password='pass'))
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.PartitarioAnagraficaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_partitarioanagrafica_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)


class PartitarioClientiAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_partitarioclienti_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_esporta_partitari(self):
        from djmoney.money import Money
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR', gestione_anagrafica='clienti')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
            controvalore=Money('200.00', 'USD'),
            categoria='dare'
        )
        url = reverse('matthaeus:movimenti_partitarioclienti_esportapartitari')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        content = response.content.decode('utf-8')
        self.assertIn('controvalore dare', content)
        self.assertIn('controvalore avere', content)
        self.assertIn('controvalore saldo', content)


class PartitarioFornitoriAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_partitariofornitori_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_esporta_partitari(self):
        from djmoney.money import Money
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR', gestione_anagrafica='fornitori')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
            controvalore=Money('300.00', 'USD'),
            categoria='avere'
        )
        url = reverse('matthaeus:movimenti_partitariofornitori_esportapartitari')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        content = response.content.decode('utf-8')
        self.assertIn('controvalore dare', content)
        self.assertIn('controvalore avere', content)
        self.assertIn('controvalore saldo', content)


class PartitarioBancheAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_partitariobanche_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_esporta_partitari(self):
        from djmoney.money import Money
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR', gestione_anagrafica='banche')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
            controvalore=Money('400.00', 'USD'),
            categoria='dare'
        )
        url = reverse('matthaeus:movimenti_partitariobanche_esportapartitari')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        content = response.content.decode('utf-8')
        self.assertIn('controvalore dare', content)
        self.assertIn('controvalore avere', content)
        self.assertIn('controvalore saldo', content)


class DettaglioMovimentoPrimaNotaAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_dettagliomovimentoprimanota_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_dettagliomovimentoprimanota_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_dettagliomovimentoprimanota_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete_admin(self):
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_dettagliomovimentoprimanota_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete_utente_semplice(self):
        self.utente = UtenteMagisterFactory(
            username='semplice', is_superuser=False)
        self.assertTrue(self.client.login(
            username='semplice', password='pass'))
        piano_dei_conti = PianoDeiContiFactory(valuta_default='EUR')
        movimento = factories.MovimentoPrimaNotaFactory(
            area=self.utente.area_corrente,
            esercizio=self.utente.esercizio_corrente,
        )
        obj = factories.DettaglioMovimentoPrimaNotaFactory(
            piano_dei_conti=piano_dei_conti,
            movimento_primanota=movimento,
        )
        url = reverse('matthaeus:movimenti_dettagliomovimentoprimanota_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)


class MastrinoCentriDiCostoAdminTest(TestCase):
    def setUp(self):
        self.utente = UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('matthaeus:movimenti_mastrinocentridicosto_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('matthaeus:movimenti_mastrinocentridicosto_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        obj = factories.MastrinoCentriDiCostoFactory()
        url = reverse('matthaeus:movimenti_mastrinocentridicosto_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_delete(self):
        obj = factories.MastrinoCentriDiCostoFactory()
        url = reverse('matthaeus:movimenti_mastrinocentridicosto_delete', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)
