from os.path import exists
import csv
from dateutil.relativedelta import relativedelta
from datetime import date, timedelta
from decimal import Decimal
from django.db.models import Prefetch
from urllib.parse import urlencode, quote, unquote
from django.db.models.functions import Coalesce
from django.db.models import Sum, F, Q, Max
from django.db.models import OuterRef, Exists

from django.contrib import messages
from django.conf.urls import url
from django.conf import settings
from django.http import HttpResponseRedirect, HttpResponse
from django.urls import reverse
from django.template import Context
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _
from django.contrib.admin.filters import RelatedOnlyFieldListFilter
from django.contrib.admin.views.main import ChangeList
from django.contrib.admin import SimpleListFilter
from django.template.defaultfilters import slugify
from django.db.models import IntegerField, Value

from django.utils.html import format_html

from djmoney.money import Money

from webodt.shortcuts import render_to_response as webodt_render_to_response
from import_export.admin import ExportActionMixin
from import_export.formats import base_formats
from preferences import preferences
from rangefilter.filters import NumericRangeFilterBuilder
from docxtpl import DocxTemplate

from django.contrib.contenttypes.models import ContentType
from common.allegati.admin import DocumentoAllegatoInlinesNoAdd, DocumentoAllegatoInlinesAddOnly
from common.anagraficabase.models import LIVELLO_PIANO, LIVELLO_PIANO_2, LIVELLO_PIANO_3
from common.datefilter.filters import DateRangeFilter
from common.stampe.utils import get_template_stampa
from matthaeus.admin import site, MatthaeusModelAdmin, MatthaeusInlineModelAdmin
from matthaeus.movimenti import forms
from matthaeus.movimenti.models import (
    TipoMovimento, MovimentoPrimaNota, DettaglioMovimentoPrimaNota,
    CausaleContabile, DettaglioCausaleContabile, 
    DettaglioCausaleAnalitica, PreventivoCentroDiCosto,
    ConsuntivoCentroDiCosto, DettaglioMovimentoAnalitico,
    BilancioConto, BilancioContoPreventivo, PartitarioAnagrafica,
    PartitarioClienti, PartitarioFornitori, MastrinoSottoconti,
    MovimentoPrimaNotaDaConsolidare, ConversioneDettaglioMovimentoPrimaNota, 
    DettaglioMovimentoPrimaNotaAvere, DettaglioMovimentoPrimaNotaDare, PartitarioBanche, 
    MastrinoCentriDiCosto,
)
from common.allegati.models import DocumentoAllegato
from matthaeus.movimenti.utils import ( 
    aggiorna_bilancio, get_dati_stato_patrimoniale_conto_economico,
    aggiungi_dati_parziali_aree, aggiungi_dati_clienti_fornitori,
    get_queryset_bilancio_calcolato, get_queryset_bilancio_preventivo,
    get_rendiconto_finanziario_conti, get_bilancio_riclassificato_conti,
    get_saldo_periodo_precedente, get_nuovo_numero, get_saldo_centri_di_costo_periodo_precedente,
    aggiorna_centri_di_costo, get_esercizio_precedente,
)
from matthaeus.movimenti.reports import crea_riepilogo_centri_di_costo, crea_fogli_bilancio
from matthaeus.movimenti.resources import BilancioContoPreventivoResource
from matthaeus.movimenti.views import (
    AggiornaParzialeWizard, GraficoContoEconomicoView, GraficoRiepilogoCostiView,
    GraficoRiepilogoRicaviView, GraficoBudgetView, GraficoLiquiditaView,
)
from common.utils.currency import get_elenco_valuta_default
from common.anagraficabase.utils import esercizio_area_chiuso
from common.anagraficabase.models import PianoDeiConti


class LivelloContiBilancioFilter(SimpleListFilter):
    title = _('livello')
    parameter_name = 'livello'

    def lookups(self, request, model_admin):
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
            return LIVELLO_PIANO_2
        elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 3:
            return LIVELLO_PIANO_3
        else:
            return LIVELLO_PIANO

    def queryset(self, request, queryset):
        if self.value() is not None:
            valore = self.value()
            return queryset.filter(piano_dei_conti__livello=valore)
        else:
            return queryset


class TipoMovimentoAdmin(MatthaeusModelAdmin):
    list_display = (
        'nome', 'descrizione'
    )

site.register(TipoMovimento, TipoMovimentoAdmin)


class PreventivoCentroDiCostoInline(MatthaeusInlineModelAdmin):
    fields = (
        'centro_di_costo', 'importo',
    )
    extra = 0
    suit_classes = 'suit-tab suit-tab-preventivo_centro_costo'
    autocomplete_fields = ('centro_di_costo', )
    form = forms.PreventivoCentroDiCostoForm
    model = PreventivoCentroDiCosto


class ConsuntivoCentroDiCostoInline(MatthaeusInlineModelAdmin):
    fields = (
        'centro_di_costo', 'importo', 'get_importo_preventivo', 'get_importo_scostamento'
    )
    suit_classes = 'suit-tab suit-tab-consuntivo_centro_costo'
    readonly_fields = (
        'centro_di_costo', 'get_importo_preventivo', 'importo',
        'get_importo_scostamento'
    )
    extra = 0
    model = ConsuntivoCentroDiCosto

    def get_readonly_fields(self, request, obj=None):
        if esercizio_area_chiuso(request.user.esercizio_corrente, request.user.area_corrente):
            return [n.name for n in self.opts.fields] + ['get_importo_preventivo', 'get_importo_scostamento']
        return super(MatthaeusInlineModelAdmin, self).get_readonly_fields(request, obj)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class DettaglioMovimentoAnaliticoInline(MatthaeusInlineModelAdmin):
    fields = (
        'piano_dei_conti', 'centro_di_costo', 'importo',
    )
    suit_classes = 'suit-tab suit-tab-dettagli_analitici'
    autocomplete_fields = ('piano_dei_conti', 'centro_di_costo')
    form = forms.DettaglioMovimentoAnaliticoForm
    model = DettaglioMovimentoAnalitico


class MastrinoCentriDiCostoChangeListTotals(ChangeList):

    def get_results(self, request, *args, **kwargs):
        super(MastrinoCentriDiCostoChangeListTotals, self).get_results(request, *args, **kwargs)
        if request.user.area_corrente:
            valuta_corrente = request.user.area_corrente.valuta_default
            # Prefetch delle conversioni per ottimizzare il calcolo
           
            nuova_lista = []
            saldo_corrente = Money('0.00', valuta_corrente)
            saldo_periodo_precedente = None
            if self.result_list:
                saldo_periodo_precedente = get_saldo_centri_di_costo_periodo_precedente(self.result_list[0], request.user.area_corrente, request.user.esercizio_corrente)
                saldo_corrente = saldo_periodo_precedente['saldo']
                self.dati_iniziali = []
                for field in self.list_display:
                    if field == 'get_saldo_partitario':
                        if self.result_list:
                            self.dati_iniziali.append(saldo_corrente)
                    elif field == 'get_descrizione':
                        self.dati_iniziali.append(_('Saldi periodo precedente:'))
                    else:
                        self.dati_iniziali.append('')
            for res in self.result_list:
                saldo_res = res.importo
                if saldo_res:
                    saldo_corrente += saldo_res
                res.get_saldo_partitario = saldo_corrente
                nuova_lista.append(res)
            self.aggregations = []
            risultato = self.result_list
            risultato.query.clear_limits()
            self.result_list = nuova_lista
            for field in self.list_display:
                if field == 'get_saldo_partitario':
                    if self.result_list:
                        self.aggregations.append(getattr(self.result_list[-1], 'get_saldo_partitario'))
                elif field == 'get_descrizione':
                    self.aggregations.append(_('TOTALI:'))
                else:
                    self.aggregations.append('')


class MastrinoCentriDiCostoAdmin(MatthaeusModelAdmin):
    list_display = (
        'get_numero_operazione_link', 'get_data_operazione', 'piano_dei_conti', 
        'get_descrizione','importo',
        'get_saldo_partitario', 'get_area',
    )
    list_filter = (
        DateRangeFilter,
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('centro_di_costo', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    autocomplete_fields = ('movimento_primanota', 'centro_di_costo', 'piano_dei_conti')
    readonly_fields = ('get_numero_operazione_link', )
    suit_list_filter_horizontal = (
        'centro_di_costo',
    )
    list_display_links = None
    search_fields = (
        'piano_dei_conti__codice', 'piano_dei_conti__descrizione',
        'importo', 'movimento_primanota__numero_operazione',
        'movimento_primanota__descrizione',
    )
    list_select_related = (
        'piano_dei_conti', 'movimento_primanota', 'movimento_primanota__area', 'movimento_primanota__esercizio'
    )
    list_per_page = 10000
    ordering = ['movimento_primanota__data_operazione', 'id']
    change_list_template = 'mastrino_centridicosto_change_list_totals.html'

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportamastrino/$',
                self.admin_site.admin_view(self.esporta_mastrino),
                name='%s_%s_esportamastrino' % info
            ),
            url(
                r'^stampamastrino/$',
                self.admin_site.admin_view(self.stampa_mastrino),
                name='%s_%s_stampamastrino' % info
            ),
        ]
        url_patterns += super(MastrinoCentriDiCostoAdmin, self).get_urls()
        return url_patterns

    def get_actions(self, request):
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            actions.pop('delete_selected')
        return actions

    def changelist_view(self, request, *args, **kwargs):
        self.request = request
        return super().changelist_view(request, *args, **kwargs)

    def get_changelist(self, request, **kwargs):
        return MastrinoCentriDiCostoChangeListTotals

    def suit_cell_attributes(self, obj, column):
        if column in ['get_saldo_partitario', 'importo']:
            css_dict = {'class': 'text-xs-right'}
            saldo = getattr(obj, 'get_saldo_partitario')
            if saldo:
                if saldo < Money(0.00, saldo.currency):
                    css_dict = {'class': 'text-xs-right', 'style': 'color: red;'}
                else:
                    css_dict = {'class': 'text-xs-right'}
            return css_dict

    def get_queryset(self, request):
        if not request.user.area_corrente and request.user.esercizio_corrente:
            messages.error(request, _('Non è stata selezionata un\'area o esercizio corrente.'))
            return self.model.objects.none()
        qs = super(MastrinoCentriDiCostoAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
            qs = qs.filter(movimento_primanota__area__in=elenco_aree_figlie)
        if request.user.esercizio_corrente:
            qs = qs.filter(movimento_primanota__esercizio=request.user.esercizio_corrente)
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
            if data_inizio:
                qs = qs.filter(movimento_primanota__data_operazione__gte=data_inizio)
            if data_fine:
                qs = qs.filter(movimento_primanota__data_operazione__lte=data_fine)
        return qs

    def esporta_mastrino(self, request):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="mastrino_%s_%s.csv"' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        valuta_default = request.user.area_corrente.valuta_default
        writer = csv.writer(response, delimiter=';')
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        elenco_totali = cl.aggregations
        elenco_mastrini = cl.result_list
        saldi_iniziali = get_saldo_centri_di_costo_periodo_precedente(elenco_mastrini[0], request.user.area_corrente, request.user.esercizio_corrente)
        saldo_iniziale_saldo = str(saldi_iniziali['saldo'].amount).replace('.', ',')
        saldo_periodo = Decimal('0.00')
        if elenco_mastrini:
            for mastrino in elenco_mastrini:
                if mastrino.importo:
                    saldo_periodo += mastrino.importo.amount
        elenco_totali[5] = saldi_iniziali['saldo'].amount + saldo_periodo
        writer.writerow([
            'numero operazione',
            'data operazione',
            'conto',
            'descrizione movimento',
            'importo (%s)' % valuta_default,
            'saldo (%s)' % valuta_default,
            'area',
        ])
        writer.writerow([
            '',
            '',
            '',
            '',
            'Saldi periodo precedente:',
            '',
            saldo_iniziale_saldo,
            '',
        ])
        for mastrino in elenco_mastrini:
            saldo_partitario = ''
            importo_saldo_mastrino = mastrino.get_saldo_partitario
            if importo_saldo_mastrino:
                saldo_partitario = str(importo_saldo_mastrino.amount).replace('.', ',')
            writer.writerow([
                mastrino.get_numero_operazione(),
                mastrino.get_data_operazione(),
                mastrino.piano_dei_conti,
                mastrino.get_descrizione(),
                str(mastrino.importo.amount).replace('.', ','),
                saldo_partitario,
                mastrino.get_area(),
            ])
        if elenco_totali:
            riepilogo_saldo = ''
            importo_riepilogo_saldo = elenco_totali[5]
            if importo_riepilogo_saldo:
                riepilogo_saldo = str(importo_riepilogo_saldo).replace('.', ',')
            else:
                riepilogo_saldo = '0,00'
            writer.writerow([
                '',
                '',
                '',
                'TOTALI:',
                '',
                riepilogo_saldo,
                '',
            ])
        writer.writerow([
            '',
            '',
            '',
            'Saldi periodo:',
            '',
            str(saldo_periodo).replace('.', ','),
            '',
        ])
        return response

    def stampa_mastrino(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        valuta_corrente = request.user.area_corrente.valuta_default
        elenco_totali = cl.aggregations
        elenco_mastrini = cl.result_list
        if elenco_mastrini:
            primo_mastrino = elenco_mastrini[0]
        else:
            primo_mastrino = None
        saldi_iniziali = get_saldo_centri_di_costo_periodo_precedente(primo_mastrino, request.user.area_corrente, request.user.esercizio_corrente)
        saldo_iniziale_saldo = saldi_iniziali['saldo']
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        centro_di_costo = None
        saldo_periodo = Money('0.00', valuta_corrente)
        if elenco_mastrini:
            centro_di_costo = elenco_mastrini[0].centro_di_costo
            for mastrino in elenco_mastrini:
                if mastrino.importo:
                    saldo_periodo += mastrino.importo
        elenco_totali[5] = saldo_iniziale_saldo + saldo_periodo
        template = get_template_stampa('elenco_mastrini_centri_di_costo', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_mastrini_centri_di_costo')
        contesto = dict(
            elenco_totali=elenco_totali, elenco_mastrini=elenco_mastrini,
            centro_di_costo=centro_di_costo, intestazione_stampe=intestazione_stampe,
            area_corrente=request.user.area_corrente,
            esercizio_corrente=request.user.esercizio_corrente,
            saldo_iniziale_saldo=saldo_iniziale_saldo,
            saldo_periodo_saldo=saldo_periodo,
        )
        filename = 'mastrino_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
        
site.register(MastrinoCentriDiCosto, MastrinoCentriDiCostoAdmin)


class DettaglioMovimentoPrimaNotaInline(MatthaeusInlineModelAdmin):
    fields = [
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    suit_classes = 'suit-tab suit-tab-dati_principali'
    autocomplete_fields = ('piano_dei_conti', )
    extra = 3
    readonly_fields = ('anagrafica', )
    model = DettaglioMovimentoPrimaNota
    form = forms.DettaglioMovimentoPrimaNotaForm

    def get_fields(self, request, obj=None):
        vedi_anagrafica = preferences.ImpostazioniMagister.abilita_gestione_anagrafiche
        if not vedi_anagrafica:
            if 'anagrafica' in self.fields:
                self.fields.remove('anagrafica')
        else:
            if 'anagrafica' not in self.fields:
                self.fields.append('anagrafica')
        return super(DettaglioMovimentoPrimaNotaInline, self).get_fields(request, obj)

    def get_formset(self, request, obj=None, **kwargs):
        valuta_corrente = request.user.area_corrente.valuta_default
        if obj:
            if obj.area != request.user.area_corrente:
                valuta_corrente = obj.area.valuta_default
        self.form = forms.get_form_dettaglio_movimento_prima_nota(valuta_corrente)
        return super(DettaglioMovimentoPrimaNotaInline, self).get_formset(request, obj, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        area_corrente = request.user.area_corrente
        if obj:
            if obj.area.valuta_default != area_corrente.valuta_default:
                self.extra = 0
                return ('importo', 'anagrafica', 'categoria', 'piano_dei_conti', 'progressivo', 'controvalore', 'descrizione')
        return super(DettaglioMovimentoPrimaNotaInline, self).get_readonly_fields(request, obj)


class PartitarioClientiInline(DettaglioMovimentoPrimaNotaInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    readonly_fields = (
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione',
    )
    model = PartitarioClienti
    form = forms.PartitarioClientiForm
    suit_classes = 'suit-tab suit-tab-dettagli_aggiuntivi'
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_formset(self, request, obj=None, **kwargs):
        """ Ritorna il formset standard senza personalizzazioni di valuta. """
        return super(DettaglioMovimentoPrimaNotaInline, self).get_formset(request, obj, **kwargs)


class PartitarioFornitoriInline(DettaglioMovimentoPrimaNotaInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    readonly_fields = (
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione',
    )
    model = PartitarioFornitori
    form = forms.PartitarioFornitoriForm
    suit_classes = 'suit-tab suit-tab-dettagli_aggiuntivi'
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_formset(self, request, obj=None, **kwargs):
        """ Ritorna il formset standard senza personalizzazioni di valuta. """
        return super(DettaglioMovimentoPrimaNotaInline, self).get_formset(request, obj, **kwargs)


class PartitarioBancheInline(DettaglioMovimentoPrimaNotaInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    readonly_fields = (
        'progressivo', 'piano_dei_conti', 'categoria', 'importo',
        'controvalore', 'descrizione',
    )
    model = PartitarioBanche
    form = forms.PartitarioBancheForm
    suit_classes = 'suit-tab suit-tab-dettagli_aggiuntivi'
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_formset(self, request, obj=None, **kwargs):
        """ Ritorna il formset standard senza personalizzazioni di valuta. """
        return super(DettaglioMovimentoPrimaNotaInline, self).get_formset(request, obj, **kwargs)


class DettaglioMovimentoDareInline(DettaglioMovimentoPrimaNotaInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    model = DettaglioMovimentoPrimaNotaDare


class DettaglioMovimentoDareReadonlyInline(DettaglioMovimentoDareInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    readonly_fields = (
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica'
    )
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class DettaglioMovimentoAvereInline(DettaglioMovimentoPrimaNotaInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    model = DettaglioMovimentoPrimaNotaAvere


class DettaglioMovimentoAvereReadonlyInline(DettaglioMovimentoAvereInline):
    fields = [
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica',
    ]
    readonly_fields = (
        'progressivo', 'piano_dei_conti', 'importo',
        'controvalore', 'descrizione', 'anagrafica'
    )
    extra = 0

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class MastrinoChangeListTotals(ChangeList):

    def get_results(self, request, *args, **kwargs):
        super(MastrinoChangeListTotals, self).get_results(request, *args, **kwargs)
        if request.user.area_corrente:
            valuta_corrente = request.user.area_corrente.valuta_default
            # Prefetch delle conversioni per ottimizzare il calcolo
            result_list_prefetch = self.result_list.prefetch_related(
                Prefetch(
                    'conversionedettagliomovimentoprimanota_set',
                    queryset=ConversioneDettaglioMovimentoPrimaNota.objects.filter(
                        importo_currency=valuta_corrente
                    ),
                    to_attr='conversioni_prefetch'
                )
            )
            nuova_lista = []
            saldo_corrente = Money('0.00', valuta_corrente)
            saldo_periodo_precedente = None
            if self.result_list:
                saldo_periodo_precedente = get_saldo_periodo_precedente(self.result_list[0], request.user.area_corrente, request.user.esercizio_corrente)
                saldo_corrente = saldo_periodo_precedente['saldo']
                self.dati_iniziali = []
                for field in self.list_display:
                    if field == 'get_dare_display':
                        self.dati_iniziali.append(saldo_periodo_precedente['totale_dare'])
                    elif field == 'get_avere_display':
                        self.dati_iniziali.append(saldo_periodo_precedente['totale_avere'])
                    elif field == 'get_saldo_partitario':
                        if self.result_list:
                            self.dati_iniziali.append(saldo_corrente)
                    elif field == 'get_descrizione':
                        self.dati_iniziali.append(_('Saldi periodo precedente:'))
                    else:
                        self.dati_iniziali.append('')
            for res in result_list_prefetch:
                saldo_res = res.get_saldo_convertito(valuta_corrente)
                if saldo_res:
                    saldo_corrente += saldo_res
                res.get_saldo_partitario = saldo_corrente
                nuova_lista.append(res)
            self.aggregations = []
            risultato = self.result_list
            risultato.query.clear_limits()
            self.result_list = nuova_lista
            dettagli_dare = risultato.filter(categoria='dare')
            dettagli_avere = risultato.filter(categoria='avere')
            totale_dare = Money('0.00', valuta_corrente)
            totale_avere = Money('0.00', valuta_corrente)
            if saldo_periodo_precedente:
                totale_dare += saldo_periodo_precedente['totale_dare']
                totale_avere += saldo_periodo_precedente['totale_avere']
            for dare in dettagli_dare:
                totale_dare += dare.get_importo_convertito(valuta_corrente)
            for avere in dettagli_avere:
                totale_avere += avere.get_importo_convertito(valuta_corrente)
            for field in self.list_display:
                if field == 'get_dare_display':
                    self.aggregations.append(totale_dare)
                elif field == 'get_avere_display':
                    self.aggregations.append(totale_avere)
                elif field == 'get_saldo_partitario':
                    if self.result_list:
                        self.aggregations.append(getattr(self.result_list[-1], 'get_saldo_partitario'))
                elif field == 'get_descrizione':
                    self.aggregations.append(_('TOTALI:'))
                else:
                    self.aggregations.append('')


class DettaglioMovimentoPrimaNotaAdmin(MatthaeusModelAdmin):
    list_display = [
        'get_link_movimento', 'get_data_operazione', 'piano_dei_conti', 'anagrafica', 'categoria',
        'importo', 'controvalore',  'descrizione', 'get_area',
    ]
    list_display_links = None
    search_fields = (
        'piano_dei_conti__codice', 'piano_dei_conti__descrizione', 'descrizione',
        'importo', 'movimento_primanota__numero_operazione',
        'movimento_primanota__descrizione',
    )
    readonly_fields = ('get_link_movimento', )
    form = forms.DettaglioMovimentoPrimaNotaForm
    autocomplete_fields = ('movimento_primanota', 'piano_dei_conti')
    suit_list_filter_horizontal = ('piano_dei_conti', )
    list_select_related = ('piano_dei_conti', 'movimento_primanota', 'movimento_primanota__area', 'movimento_primanota__esercizio')
    list_filter = [
        DateRangeFilter,
        'categoria',
        'piano_dei_conti__gestione_anagrafica',
        ('anagrafica', RelatedOnlyFieldListFilter),
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('piano_dei_conti', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    ]
    fieldsets = (
        (
            None, dict(
                fields=(
                    ('movimento_primanota', 'get_link_movimento'),
                    'progressivo',
                    'piano_dei_conti',
                    'descrizione',
                    'categoria',
                    'importo',
                    'controvalore',
                )
            )
        ),
    )

    def suit_cell_attributes(self, obj, column):
        if column in [
            'importo', 'controvalore'
        ]:
            return {'class': 'text-xs-right'}

    def get_list_display(self, request):
        vedi_anagrafica = preferences.ImpostazioniMagister.abilita_gestione_anagrafiche
        if not vedi_anagrafica:
            if 'anagrafica' in self.list_display:
                self.list_display.remove('anagrafica')
            if 'piano_dei_conti__gestione_anagrafica' in self.list_filter:
                self.list_filter.remove('piano_dei_conti__gestione_anagrafica')
        else:
            if 'anagrafica' not in self.list_display:
                self.list_display.insert(3, 'anagrafica')
            if 'piano_dei_conti__gestione_anagrafica' not in self.list_filter:
                self.list_display.insert(3, piano_dei_conti__gestione_anagrafica)
        return super(DettaglioMovimentoPrimaNotaAdmin, self).get_list_display(request)
        
    def get_queryset(self, request):
        qs = super(DettaglioMovimentoPrimaNotaAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
            qs = qs.filter(movimento_primanota__area__in=elenco_aree_figlie)
        if request.user.esercizio_corrente:
            qs = qs.filter(movimento_primanota__esercizio=request.user.esercizio_corrente)
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
            if data_inizio:
                qs = qs.filter(movimento_primanota__data_operazione__gte=data_inizio)
            if data_fine:
                qs = qs.filter(movimento_primanota__data_operazione__lte=data_fine)
        return qs

site.register(DettaglioMovimentoPrimaNota, DettaglioMovimentoPrimaNotaAdmin)


class MastrinoSottocontiAdmin(MatthaeusModelAdmin):
    list_display = [
        'get_numero_operazione_link', 'get_data_operazione', 'get_descrizione',
        'get_dare_display', 'get_avere_display', 'get_saldo_partitario',
        'get_area', 'descrizione',
    ]
    list_display_links = None
    search_fields = (
        'piano_dei_conti__codice', 'piano_dei_conti__descrizione', 'descrizione',
        'importo', 'movimento_primanota__numero_operazione',
        'movimento_primanota__descrizione',
    )
    readonly_fields = ('get_link_movimento', )
    form = forms.DettaglioMovimentoPrimaNotaForm
    autocomplete_fields = ('movimento_primanota', 'piano_dei_conti')
    list_select_related = (
        'piano_dei_conti', 'movimento_primanota', 'movimento_primanota__area', 'movimento_primanota__esercizio'
    )
    list_filter = (
        DateRangeFilter,
        'categoria',
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('piano_dei_conti', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    list_per_page = 10000
    ordering = ['movimento_primanota__data_operazione', 'id']
    suit_list_filter_horizontal = (
        'piano_dei_conti',
    )
    fieldsets = (
        (
            None, dict(
                fields=(
                    ('movimento_primanota', 'get_link_movimento'),
                    'progressivo',
                    'piano_dei_conti',
                    'descrizione',
                    'categoria',
                    'importo',
                    'controvalore',
                )
            )
        ),
    )
    change_list_template = 'mastrino_change_list_totals.html'

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportamastrino/$',
                self.admin_site.admin_view(self.esporta_mastrino),
                name='%s_%s_esportamastrino' % info
            ),
            url(
                r'^stampamastrino/$',
                self.admin_site.admin_view(self.stampa_mastrino),
                name='%s_%s_stampamastrino' % info
            ),
        ]
        url_patterns += super(MastrinoSottocontiAdmin, self).get_urls()
        return url_patterns

    def get_actions(self, request):
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            actions.pop('delete_selected')
        return actions

    def changelist_view(self, request, *args, **kwargs):
        self.request = request
        return super().changelist_view(request, *args, **kwargs)

    def get_dare_display(self, obj):
        if obj.categoria == 'dare':
            return obj.get_importo_convertito(self.request.user.area_corrente.valuta_default)
        return ''        
    get_dare_display.short_description = 'Dare'

    def get_avere_display(self, obj):
        if obj.categoria == 'avere':
            return obj.get_importo_convertito(self.request.user.area_corrente.valuta_default)
        return ''
    get_avere_display.short_description = 'Avere'

    def get_numero_operazione_link(self, obj):
        base_url = reverse('matthaeus:movimenti_movimentoprimanota_change', args=[obj.movimento_primanota.id])
        # Recupera i filtri correnti dalla richiesta
        filters = self.request.GET.dict()
        # Costruisce il parametro 'next' con i filtri correnti
        next_url = reverse('matthaeus:movimenti_mastrinosottoconti_changelist')
        query_string = urlencode(filters) if filters else ''
        full_next_url = f'{next_url}?{query_string}' if query_string else next_url
        # Codifica l'intero parametro next
        encoded_next = quote(full_next_url)
        return format_html(
            '<a href="{}?next={}">{}</a>',
            base_url,
            encoded_next,
            obj.movimento_primanota.numero_operazione
        )
    get_numero_operazione_link.short_description = _('Num. Operazione')
    get_numero_operazione_link.admin_order_field = 'movimento_primanota__numero_operazione'

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_changelist(self, request, **kwargs):
        return MastrinoChangeListTotals

    def suit_cell_attributes(self, obj, column):
        if column in [
            'get_totale_dare', 'get_totale_avere',
            'get_dare_display', 'get_avere_display',
        ]:
            return {'class': 'text-xs-right'}
        if column == 'get_saldo_partitario':
            css_dict = {'class': 'text-xs-right'}
            saldo = getattr(obj, 'get_saldo_partitario')
            if saldo:
                if saldo < Money(0.00, saldo.currency):
                    css_dict = {'class': 'text-xs-right', 'style': 'color: red;'}
                else:
                    css_dict = {'class': 'text-xs-right'}
            return css_dict

    def get_queryset(self, request):
        if not request.user.area_corrente and request.user.esercizio_corrente:
            messages.error(request, _('Non è stata selezionata un\'area o esercizio corrente.'))
            return self.model.objects.none()
        qs = super(MastrinoSottocontiAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
            qs = qs.filter(movimento_primanota__area__in=elenco_aree_figlie)
        if request.user.esercizio_corrente:
            qs = qs.filter(movimento_primanota__esercizio=request.user.esercizio_corrente)
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
            if data_inizio:
                qs = qs.filter(movimento_primanota__data_operazione__gte=data_inizio)
            if data_fine:
                qs = qs.filter(movimento_primanota__data_operazione__lte=data_fine)
        return qs

    def esporta_mastrino(self, request):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="mastrino_%s_%s.csv"' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        valuta_default = request.user.area_corrente.valuta_default
        writer = csv.writer(response, delimiter=';')
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        elenco_totali = cl.aggregations
        elenco_mastrini = cl.result_list
        saldi_iniziali = get_saldo_periodo_precedente(elenco_mastrini[0], request.user.area_corrente, request.user.esercizio_corrente)
        saldo_iniziale_dare = str(saldi_iniziali['totale_dare'].amount).replace('.', ',')
        saldo_iniziale_avere = str(saldi_iniziali['totale_avere'].amount).replace('.', ',')
        saldo_iniziale_saldo = str(saldi_iniziali['saldo'].amount).replace('.', ',')
        saldo_periodo_dare = Decimal('0.00')
        saldo_periodo_avere = Decimal('0.00')
        if elenco_mastrini:
            for mastrino in elenco_mastrini:
                if mastrino.importo:
                    if mastrino.categoria == 'dare':
                        saldo_periodo_dare += mastrino.get_importo_convertito(valuta_default).amount
                    if mastrino.categoria == 'avere':
                        saldo_periodo_avere += mastrino.get_importo_convertito(valuta_default).amount
        saldo_periodo = saldo_periodo_dare - saldo_periodo_avere
        elenco_totali[3] = saldi_iniziali['totale_dare'].amount + saldo_periodo_dare
        elenco_totali[4] = saldi_iniziali['totale_avere'].amount + saldo_periodo_avere
        elenco_totali[5] = saldi_iniziali['saldo'].amount + saldo_periodo
        writer.writerow([
                        'numero operazione',
                        'data operazione',
                        'conto',
                        'descrizione movimento',
                        'dare (%s)' % valuta_default,
                        'avere (%s)' % valuta_default,
                        'saldo (%s)' % valuta_default,
                        'area',
                        'descrizione',
                        ])
        writer.writerow([
                '',
                '',
                '',
                'Saldi periodo precedente:',
                saldo_iniziale_dare,
                saldo_iniziale_avere,
                saldo_iniziale_saldo,
                '',
                '',
            ])
        for mastrino in elenco_mastrini:
            totale_dare = ''
            totale_avere = ''
            saldo_partitario = ''
            if mastrino.categoria == 'dare':
                importo_totale_dare = mastrino.get_importo_convertito(valuta_default)
                if importo_totale_dare:
                    totale_dare = str(importo_totale_dare.amount).replace('.', ',')
            if mastrino.categoria == 'avere':
                importo_totale_avere = mastrino.get_importo_convertito(valuta_default)
                if importo_totale_avere:
                    totale_avere = str(importo_totale_avere.amount).replace('.', ',')
            importo_saldo_mastrino = mastrino.get_saldo_partitario
            if importo_saldo_mastrino:
                saldo_partitario = str(importo_saldo_mastrino.amount).replace('.', ',')
            writer.writerow([
                mastrino.get_numero_operazione(),
                mastrino.get_data_operazione(),
                mastrino.piano_dei_conti,
                mastrino.get_descrizione(),
                totale_dare,
                totale_avere,
                saldo_partitario,
                mastrino.get_area(),
                mastrino.descrizione,
            ])
        if elenco_totali:
            riepilogo_totale_dare = ''
            importo_riepilogo_totale_dare = elenco_totali[3]
            if importo_riepilogo_totale_dare:
                riepilogo_totale_dare = str(importo_riepilogo_totale_dare).replace('.', ',')
            riepilogo_totale_avere = ''
            importo_riepilogo_totale_avere = elenco_totali[4]
            if importo_riepilogo_totale_avere:
                riepilogo_totale_avere = str(importo_riepilogo_totale_avere).replace('.', ',')
            riepilogo_saldo = ''
            importo_riepilogo_saldo = elenco_totali[5]
            if importo_riepilogo_saldo:
                riepilogo_saldo = str(importo_riepilogo_saldo).replace('.', ',')
            else:
                riepilogo_saldo = '0,00'
            writer.writerow([
                '',
                '',
                '',
                'TOTALI:',
                riepilogo_totale_dare,
                riepilogo_totale_avere,
                riepilogo_saldo,
                '',
                '',
            ])
        writer.writerow([
                '',
                '',
                '',
                'Saldi periodo:',
                str(saldo_periodo_dare).replace('.', ','),
                str(saldo_periodo_avere).replace('.', ','),
                str(saldo_periodo).replace('.', ','),
                '',
                '',
            ])
        return response

    def stampa_mastrino(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        valuta_corrente = request.user.area_corrente.valuta_default
        elenco_totali = cl.aggregations
        elenco_mastrini = cl.result_list
        if elenco_mastrini:
            primo_mastrino = elenco_mastrini[0]
        else:
            primo_mastrino = None
        saldi_iniziali = get_saldo_periodo_precedente(primo_mastrino, request.user.area_corrente, request.user.esercizio_corrente)
        saldo_iniziale_dare = saldi_iniziali['totale_dare']
        saldo_iniziale_avere = saldi_iniziali['totale_avere']
        saldo_iniziale_saldo = saldi_iniziali['saldo']
        saldo_periodo_dare = Decimal('0.00')
        saldo_periodo_avere = Decimal('0.00')
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        conto = None
        if elenco_mastrini:
            conto = elenco_mastrini[0].piano_dei_conti
            for mastrino in elenco_mastrini:
                if mastrino.importo:
                    if mastrino.categoria == 'dare':
                        saldo_periodo_dare += mastrino.importo
                    if mastrino.categoria == 'avere':
                        saldo_periodo_avere += mastrino.importo
        saldo_periodo = saldo_periodo_dare - saldo_periodo_avere
        elenco_totali[3] = saldo_iniziale_dare + saldo_periodo_dare
        elenco_totali[4] = saldo_iniziale_avere + saldo_periodo_avere
        elenco_totali[5] = saldo_iniziale_saldo + saldo_periodo
        template = get_template_stampa('elenco_mastrini', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_mastrini')
        contesto = dict(
            elenco_totali=elenco_totali, elenco_mastrini=elenco_mastrini,
            conto=conto, intestazione_stampe=intestazione_stampe,
            area_corrente=request.user.area_corrente,
            esercizio_corrente=request.user.esercizio_corrente,
            saldo_iniziale_dare=saldo_iniziale_dare,
            saldo_iniziale_avere=saldo_iniziale_avere,
            saldo_iniziale_saldo=saldo_iniziale_saldo,
            saldo_periodo_dare=saldo_periodo_dare,
            saldo_periodo_avere=saldo_periodo_avere,
            saldo_periodo_saldo=saldo_periodo,
        )
        filename = 'mastrino_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    def get_list_display(self, request):
        vedi_anagrafica = False
        params = dict(request.GET.items()).copy()
        if 'piano_dei_conti__id__exact' in params:
            id_piano_dei_conti = params['piano_dei_conti__id__exact']
            if id_piano_dei_conti != '0':
                piano_dei_conti = PianoDeiConti.objects.get(id=id_piano_dei_conti)
                if piano_dei_conti.gestione_anagrafica:
                    vedi_anagrafica = True
        if not vedi_anagrafica:
            if 'anagrafica' in self.list_display:
                self.list_display.remove('anagrafica')
        else:
            if 'anagrafica' not in self.list_display:
                self.list_display.insert(6, 'anagrafica')
                self.list_display_links = None
        return super(MastrinoSottocontiAdmin, self).get_list_display(request)

site.register(MastrinoSottoconti, MastrinoSottocontiAdmin)


class PartitariAnagraficaChangeListTotals(ChangeList):

    def get_results(self, request, *args, **kwargs):
        super(PartitariAnagraficaChangeListTotals, self).get_results(request, *args, **kwargs)
        if request.user.area_corrente:
            valuta_corrente = request.user.area_corrente.valuta_default
            # Prefetch delle conversioni per ottimizzare il calcolo
            result_list_prefetch = self.result_list.prefetch_related(
                Prefetch(
                    'conversionedettagliomovimentoprimanota_set',
                    queryset=ConversioneDettaglioMovimentoPrimaNota.objects.filter(
                        importo_currency=valuta_corrente
                    ),
                    to_attr='conversioni_prefetch'
                )
            )
            nuova_lista = []
            saldo_corrente = Money('0.00', valuta_corrente)
            saldo_periodo_precedente = None
            if self.result_list:
                anagrafica = self.result_list[0].anagrafica
                saldo_periodo_precedente = get_saldo_periodo_precedente(self.result_list[0], request.user.area_corrente, request.user.esercizio_corrente, anagrafica)
                visualizza_saldo_fornitori = preferences.ImpostazioniMagister.visualizza_saldo_fornitori
                if visualizza_saldo_fornitori:
                    esercizio_precedente = get_esercizio_precedente(request.user.esercizio_corrente)
                    if esercizio_precedente:
                        saldo_esercizio_precedente = get_saldo_periodo_precedente(
                            self.result_list[0], request.user.area_corrente, esercizio_precedente, anagrafica, True
                        )
                        if saldo_esercizio_precedente:
                            saldo_periodo_precedente['saldo'] += saldo_esercizio_precedente['saldo']
                            saldo_periodo_precedente['totale_dare'] += saldo_esercizio_precedente['totale_dare']
                            saldo_periodo_precedente['totale_avere'] += saldo_esercizio_precedente['totale_avere']
                saldo_corrente = saldo_periodo_precedente['saldo']
                self.dati_iniziali = []
                for field in self.list_display:
                    if field == 'get_dare_display':
                        self.dati_iniziali.append(saldo_periodo_precedente['totale_dare'])
                    elif field == 'get_avere_display':
                        self.dati_iniziali.append(saldo_periodo_precedente['totale_avere'])
                    elif field == 'get_saldo_partitario':
                        if self.result_list:
                            self.dati_iniziali.append(saldo_corrente)
                    elif field == 'get_descrizione':
                        if visualizza_saldo_fornitori:
                            self.dati_iniziali.append(_('Saldi precedenti (incluso es.precedente):'))
                        else:
                            self.dati_iniziali.append(_('Saldi periodo precedente:'))
                    else:
                        self.dati_iniziali.append('')
            for res in result_list_prefetch:
                saldo_res = res.get_saldo_convertito(valuta_corrente)
                if saldo_res:
                    saldo_corrente += saldo_res
                res.get_saldo_partitario = saldo_corrente
                nuova_lista.append(res)
            self.aggregations = []
            risultato = self.result_list
            risultato.query.clear_limits()
            self.result_list = nuova_lista
            dettagli_dare = risultato.filter(categoria='dare')
            dettagli_avere = risultato.filter(categoria='avere')
            totale_dare = Money('0.00', valuta_corrente)
            totale_avere = Money('0.00', valuta_corrente)
            if saldo_periodo_precedente:
                totale_dare += saldo_periodo_precedente['totale_dare']
                totale_avere += saldo_periodo_precedente['totale_avere']
            for dare in dettagli_dare:
                totale_dare += dare.get_importo_convertito(valuta_corrente)
            for avere in dettagli_avere:
                totale_avere += avere.get_importo_convertito(valuta_corrente)
            for field in self.list_display:
                if field == 'get_dare_display':
                    self.aggregations.append(totale_dare)
                elif field == 'get_avere_display':
                    self.aggregations.append(totale_avere)
                elif field == 'get_saldo_partitario':
                    if self.result_list:
                        self.aggregations.append(getattr(self.result_list[-1], 'get_saldo_partitario'))
                elif field == 'get_descrizione':
                    self.aggregations.append(_('TOTALI:'))
                else:
                    self.aggregations.append('')


class PartitarioAnagraficaAdmin(MastrinoSottocontiAdmin):
    list_display = (
        'get_numero_operazione_link', 'get_data_operazione', 'piano_dei_conti',
        'get_descrizione', 'get_dare_display', 'get_avere_display',
        'get_saldo_partitario', 'get_area', 'descrizione',
    )
    list_filter = (
        DateRangeFilter,
        'categoria',
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('anagrafica', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = (
        'anagrafica',
    )
    change_list_template = 'partitari_change_list_totals.html'

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportapartitari/$',
                self.admin_site.admin_view(self.esporta_partitari),
                name='%s_%s_esportapartitari' % info
            ),
            url(
                r'^stampapartitari/$',
                self.admin_site.admin_view(self.stampa_partitari),
                name='%s_%s_stampapartitari' % info
            ),
        ]
        url_patterns += super(PartitarioAnagraficaAdmin, self).get_urls()
        return url_patterns

    def get_changelist(self, request, **kwargs):
        return PartitariAnagraficaChangeListTotals

    def esporta_partitari(self, request):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="partitari_%s_%s.csv"' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        valuta_default = request.user.area_corrente.valuta_default
        writer = csv.writer(response, delimiter=';')
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        elenco_totali = cl.aggregations
        elenco_partitari = cl.get_queryset(request)
        writer.writerow([
                        'numero operazione',
                        'data operazione',
                        'conto',
                        'descrizione movimento',
                        'dare (%s)' % valuta_default,
                        'avere (%s)' % valuta_default,
                        'saldo (%s)' % valuta_default,
                        'controvalore dare',
                        'controvalore avere',
                        'controvalore saldo',
                        'area',
                        'descrizione',
                        ])
        for partitario in elenco_partitari:
            totale_dare = ''
            totale_avere = ''
            saldo_partitario = ''
            controvalore_dare = ''
            controvalore_avere = ''
            controvalore_saldo = ''
            if partitario.categoria == 'dare':
                importo_totale_dare = partitario.get_importo_convertito(valuta_default)
                if importo_totale_dare:
                    totale_dare = str(importo_totale_dare.amount).replace('.', ',')
                if partitario.controvalore:
                    controvalore_dare = str(partitario.controvalore.amount).replace('.', ',')
            if partitario.categoria == 'avere':
                importo_totale_avere = partitario.get_importo_convertito(valuta_default)
                if importo_totale_avere:
                    totale_avere = str(importo_totale_avere.amount).replace('.', ',')
                if partitario.controvalore:
                    controvalore_avere = str(partitario.controvalore.amount).replace('.', ',')
            importo_saldo_partitario = partitario.get_saldo_partitario()
            if importo_saldo_partitario:
                saldo_partitario = str(importo_saldo_partitario.amount).replace('.', ',')
            # Il controvalore saldo sarà calcolato cumulativamente come per il saldo normale
            # Per ora lasciamo vuoto, verrà calcolato nella logica del ChangeList se necessario
            writer.writerow([
                partitario.get_numero_operazione(),
                partitario.get_data_operazione(),
                partitario.piano_dei_conti,
                partitario.get_descrizione(),
                totale_dare,
                totale_avere,
                saldo_partitario,
                controvalore_dare,
                controvalore_avere,
                controvalore_saldo,
                partitario.get_area(),
                partitario.descrizione,
            ])
        if elenco_totali:
            riepilogo_totale_dare = ''
            importo_riepilogo_totale_dare = elenco_totali[4]
            if importo_riepilogo_totale_dare:
                riepilogo_totale_dare = str(importo_riepilogo_totale_dare.amount).replace('.', ',')
            riepilogo_totale_avere = ''
            importo_riepilogo_totale_avere = elenco_totali[5]
            if importo_riepilogo_totale_avere:
                riepilogo_totale_avere = str(importo_riepilogo_totale_avere.amount).replace('.', ',')
            riepilogo_saldo = ''
            importo_riepilogo_saldo = elenco_totali[6]
            if importo_riepilogo_saldo:
                riepilogo_saldo = str(importo_riepilogo_saldo.amount).replace('.', ',')
            writer.writerow([
                elenco_totali[0],
                elenco_totali[1],
                elenco_totali[2],
                elenco_totali[3],
                riepilogo_totale_dare,
                riepilogo_totale_avere,
                riepilogo_saldo,
                '',  # controvalore dare totale (vuoto per ora)
                '',  # controvalore avere totale (vuoto per ora)
                '',  # controvalore saldo totale (vuoto per ora)
                elenco_totali[7],
                elenco_totali[8],
            ])
        return response

    def stampa_partitari(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        elenco_totali = cl.aggregations
        elenco_partitari = cl.get_queryset(request)
        dati_iniziali = cl.dati_iniziali
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        anagrafica = None
        if elenco_partitari:
            anagrafica = elenco_partitari[0].anagrafica
        template = get_template_stampa('elenco_partitari_anagrafica', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_partitari_anagrafica')
        contesto = dict(
            elenco_totali=elenco_totali, 
            elenco_partitari=elenco_partitari,
            dati_iniziali=dati_iniziali,
            anagrafica=anagrafica, intestazione_stampe=intestazione_stampe,
            area_corrente=request.user.area_corrente,
            esercizio_corrente=request.user.esercizio_corrente,
        )
        filename = 'partitari_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

site.register(PartitarioAnagrafica, PartitarioAnagraficaAdmin)


class PartitarioClientiAdmin(PartitarioAnagraficaAdmin):
    list_display = (
        'get_numero_operazione_link', 'get_data_operazione', 'piano_dei_conti',
        'get_descrizione', 'get_dare_display', 'get_avere_display',
        'get_saldo_partitario', 'get_area', 'descrizione',
    )
    list_filter = (
        DateRangeFilter,
        'categoria',
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('anagrafica', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = (
        'anagrafica',
    )
    change_list_template = 'partitari_change_list_totals.html'

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportapartitari/$',
                self.admin_site.admin_view(self.esporta_partitari),
                name='%s_%s_esportapartitari' % info
            ),
            url(
                r'^stampapartitari/$',
                self.admin_site.admin_view(self.stampa_partitari),
                name='%s_%s_stampapartitari' % info
            ),
        ]
        url_patterns += super(PartitarioAnagraficaAdmin, self).get_urls()
        return url_patterns

site.register(PartitarioClienti, PartitarioClientiAdmin)


class PartitarioFornitoriAdmin(PartitarioAnagraficaAdmin):
    list_display = (
        'get_numero_operazione_link', 'get_data_operazione', 'piano_dei_conti',
        'get_descrizione', 'get_dare_display', 'get_avere_display',
        'get_saldo_partitario', 'get_area', 'descrizione',
    )
    list_filter = (
        DateRangeFilter,
        'categoria',
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('anagrafica', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = (
        'anagrafica',
    )
    change_list_template = 'partitari_change_list_totals.html'

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportapartitari/$',
                self.admin_site.admin_view(self.esporta_partitari),
                name='%s_%s_esportapartitari' % info
            ),
            url(
                r'^stampapartitari/$',
                self.admin_site.admin_view(self.stampa_partitari),
                name='%s_%s_stampapartitari' % info
            ),
        ]
        url_patterns += super(PartitarioAnagraficaAdmin, self).get_urls()
        return url_patterns

site.register(PartitarioFornitori, PartitarioFornitoriAdmin)


class PartitarioBancheAdmin(PartitarioAnagraficaAdmin):
    list_display = (
        'get_numero_operazione_link', 'get_data_operazione', 'piano_dei_conti',
        'get_descrizione', 'get_dare_display', 'get_avere_display',
        'get_saldo_partitario', 'get_area', 'descrizione',
    )
    list_filter = (
        DateRangeFilter,
        'categoria',
        (
            'importo', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('anagrafica', RelatedOnlyFieldListFilter),
        ('movimento_primanota__area', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = (
        'anagrafica',
    )
    change_list_template = 'partitari_change_list_totals.html'

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^esportapartitari/$',
                self.admin_site.admin_view(self.esporta_partitari),
                name='%s_%s_esportapartitari' % info
            ),
            url(
                r'^stampapartitari/$',
                self.admin_site.admin_view(self.stampa_partitari),
                name='%s_%s_stampapartitari' % info
            ),
        ]
        url_patterns += super(PartitarioAnagraficaAdmin, self).get_urls()
        return url_patterns

site.register(PartitarioBanche, PartitarioBancheAdmin)


class DettaglioCausaleContabileInline(MatthaeusInlineModelAdmin):
    fields = (
        'progressivo', 'piano_dei_conti', 'categoria', 'percentuale', 'descrizione',
    )
    extra = 0
    autocomplete_fields = ('piano_dei_conti', )
    form = forms.DettaglioCausaleContabileForm
    model = DettaglioCausaleContabile


class DettaglioCausaleAnaliticaInline(MatthaeusInlineModelAdmin):
    fields = (
        'progressivo', 'centro_di_costo', 'percentuale',
    )
    extra = 0
    autocomplete_fields = ('centro_di_costo', )
    form = forms.DettaglioCausaleAnaliticaForm
    model = DettaglioCausaleAnalitica


class CausaleContabileAdmin(MatthaeusModelAdmin):
    list_display = ('descrizione_sintetica', 'descrizione_registrazione', 'operazione_partita_semplice', 'codice', 'area')
    list_filter = ('operazione_partita_semplice', )
    search_fields = ('descrizione_sintetica', 'descrizione_registrazione', 'codice')
    inlines = [DettaglioCausaleContabileInline, DettaglioCausaleAnaliticaInline]
    fieldsets = (
        (
            None, dict(
                fields=(
                    'codice',
                    'descrizione_sintetica',
                    'descrizione_registrazione',
                    'operazione_partita_semplice',
                    'area',
                )
            ),
        ),
    )

site.register(CausaleContabile, CausaleContabileAdmin)


class MovimentoPrimaNotaAdmin(MatthaeusModelAdmin):
    list_display = (
        'numero_operazione', 'numero_documento',
        'data', 'data_operazione', 'descrizione', 'get_totale_dare_display',
        'get_totale_avere_display', 'data_scadenza', 'area', 'esercizio',
        'get_squadratura_display', 'ha_allegati_display'
    )
    date_hierarchy = 'data'
    list_filter = (
        'squadratura',
        (
            'totale_movimento', NumericRangeFilterBuilder(
                title=_('Importo Dare/Avere')
            ),
        ),
        ('area', RelatedOnlyFieldListFilter),
        ('esercizio', RelatedOnlyFieldListFilter),
        'tipo_movimento', 'data', 'data_operazione', 'data_scadenza'
    )
    list_select_related = ('area', 'esercizio')
    search_fields = ('descrizione', 'numero_operazione', 'numero_documento')
    readonly_fields = (
        'operazione_partita_semplice', 'modalita_causale', 'partita_semplice',
        'totale_movimento', 'consolidato',
    )
    inlines = [
        DettaglioMovimentoDareInline, DettaglioMovimentoAvereInline,
        PartitarioClientiInline, PartitarioFornitoriInline, PartitarioBancheInline,
        DettaglioMovimentoAnaliticoInline,
        DocumentoAllegatoInlinesNoAdd, DocumentoAllegatoInlinesAddOnly,
    ]
    suit_form_tabs = [
        ('dati_principali', _('Dati Principali')),
        ('dettagli_aggiuntivi', _('Dettagli clienti/fornitori')),
        ('dettagli_analitici', _('Dettagli Analitici')),
        ('allegati', _('Allegati')),
    ]
    form = forms.MovimentoPrimaNotaForm
    fieldsets = (
        (
            _('Testata'), dict(
                classes=('suit-tab suit-tab-dati_principali suit-tab-dettagli_dare suit-tab-dettagli_avere', ),
                fields=(
                    'numero_operazione',
                    'data',
                    'data_operazione',
                    'descrizione',
                )
            )
        ),
        (
            _('Informazioni aggiuntive'), dict(
                classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                fields=(
                    'numero_documento',
                    'data_scadenza',
                    'data_inizio_competenza',
                    'data_fine_competenza',
                    'tipo_movimento',
                    'totale_movimento',
                    'genere_movimento',
                    'consolidato',
                )
            )
        ),
        (
            _('Causale Contabile'), dict(
                classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                fields=(
                    ('causale', 'modalita_causale'),
                    'importo_partita_semplice',
                    'operazione_partita_semplice',
                    'partita_semplice',
                )
            )
        ),
        (
            _('Dati Esercizio'), dict(
                # classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    ('esercizio', 'area', ),
                )
            )
        ),
    )

    def get_queryset(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        if area_corrente and esercizio_corrente:
            pass
        else:
            messages.warning(request, 'selezionare un\' area e un esercizio per mostrare i movimenti prima nota')
            return MovimentoPrimaNota.objects.none()
        qs = super().get_queryset(request)
         # Annotazione dei totali dare e avere
        qs = qs.annotate(
            totale_dare=Coalesce(
                Sum(
                    'dettagliomovimentoprimanota__importo',
                    filter=Q(dettagliomovimentoprimanota__categoria='dare')
                ),
                0
            ),
            totale_avere=Coalesce(
                Sum(
                    'dettagliomovimentoprimanota__importo',
                    filter=Q(dettagliomovimentoprimanota__categoria='avere')
                ),
                0
            )
        )
        allegati_subquery = DocumentoAllegato.objects.filter(
            content_type=ContentType.objects.get_for_model(MovimentoPrimaNota),
            object_id=OuterRef('pk'),
        )
        qs = qs.annotate(ha_allegati=Exists(allegati_subquery))
        return qs
    
    def get_readonly_fields(self, request, obj=None):
        abilita_gestione_anagrafiche = preferences.ImpostazioniMagister.abilita_gestione_anagrafiche
        if not abilita_gestione_anagrafiche:
            self.suit_form_tabs = [
                ('dati_principali', _('Dati Principali')),
                ('dettagli_analitici', _('Dettagli Analitici')),
                ('allegati', _('Allegati')),
            ]
        else:
            self.suit_form_tabs = [
                ('dati_principali', _('Dati Principali')),
                ('dettagli_aggiuntivi', _('Dettagli clienti/fornitori')),
                ('dettagli_analitici', _('Dettagli Analitici')),
                ('allegati', _('Allegati')),
            ]
        if esercizio_area_chiuso(request.user.esercizio_corrente, request.user.area_corrente):
            self.inlines = [
                DettaglioMovimentoDareInline, DettaglioMovimentoAvereInline,
                PartitarioClientiInline, PartitarioFornitoriInline, PartitarioBancheInline,
                DettaglioMovimentoAnaliticoInline,
                DocumentoAllegatoInlinesNoAdd,
            ]
            return [n.name for n in self.opts.fields]
        return super(MovimentoPrimaNotaAdmin, self).get_readonly_fields(request, obj)

    def suit_column_attributes(self, column):
        if column in [
            'get_totale_dare', 'get_totale_avere', 'importo_partita_semplice'
        ]:
            return {'class': 'text-xs-right'}

    def suit_cell_attributes(self, obj, column):
        if column in [
            'get_totale_dare', 'get_totale_avere', 'importo_partita_semplice'
        ]:
            return {'class': 'text-xs-right'}

    def get_changeform_initial_data(self, request):
        if request.user:
            if request.user.esercizio_corrente and request.user.esercizio_corrente:
                return {
                    'area': request.user.area_corrente,
                    'esercizio': request.user.esercizio_corrente
                }

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^librogiornale/$',
                self.admin_site.admin_view(self.libro_giornale),
                name='%s_%s_librogiornale' % info
            ),
            url(
                r'^librogiornaleelenco/$',
                self.admin_site.admin_view(self.libro_giornale_elenco),
                name='%s_%s_librogiornaleelenco' % info
            ),
            url(
                r'^stampasintetica/$',
                self.admin_site.admin_view(self.stampa_sintetica),
                name='%s_%s_stampasintetica' % info
            ),
            url(
                r'^stampaanalitica/$',
                self.admin_site.admin_view(self.stampa_analitica),
                name='%s_%s_stampaanalitica' % info
            ),
            url(
                r'^rigeneranumerazione/$',
                self.admin_site.admin_view(self.rigenera_numerazione),
                name='%s_%s_rigeneranumerazione' % info
            ),
        ]
        url_patterns += super(MovimentoPrimaNotaAdmin, self).get_urls()
        return url_patterns

    def stampa_sintetica(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        operazioni = cl.get_queryset(request)
        operazioni = operazioni.order_by('numero_operazione')
        template = get_template_stampa('primanota_sintetica', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('primanota_sintetica')
        context = Context(dict(operazioni=operazioni))
        filename = 'stampa_sintetica_operazioni_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )

    def stampa_analitica(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        operazioni = cl.get_queryset(request)
        operazioni = operazioni.order_by('numero_operazione')
        template = get_template_stampa('primanota_analitica', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('primanota_analitica')
        context = Context(dict(operazioni=operazioni))
        filename = 'stampa_analitica_operazioni_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )

    def libro_giornale(self, request):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        valuta_corrente = request.user.area_corrente.valuta_default
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        movimenti = cl.get_queryset(request)
        movimenti = movimenti.order_by('numero_operazione')
        giorni_movimenti = movimenti.values('data_operazione').order_by('data_operazione').distinct()
        elenco_giorni_movimenti = []
        for giorno in giorni_movimenti:
            movimenti_giorno = movimenti.filter(data_operazione=giorno['data_operazione'])
            elenco_giorno = dict()
            elenco_righe_giorno = []
            totale_movimenti_dare = Money(0.0, valuta_corrente)
            totale_movimenti_avere = Money(0.0, valuta_corrente)
            for movimento in movimenti_giorno:
                riga_testata = {
                    'numero': movimento.numero_operazione,
                    'data': movimento.data_operazione,
                    'descrizione': movimento.descrizione,
                    'dare': '',
                    'avere': '',
                    'totale': movimento.get_importo_totale()
                }
                elenco_righe_giorno.append(riga_testata)
                for dettaglio in movimento.get_dettaglio_dare():
                    riga_dettaglio = {
                        'numero': '',
                        'data': '',
                        'descrizione': '%s' % dettaglio.piano_dei_conti,
                        'dare': dettaglio.importo,
                        'avere': '',
                        'totale': ''
                    }
                    elenco_righe_giorno.append(riga_dettaglio)
                    if dettaglio.importo:
                        totale_movimenti_dare += dettaglio.importo
                for dettaglio in movimento.get_dettaglio_avere():
                    riga_dettaglio = {
                        'numero': '',
                        'data': '',
                        'descrizione': '%s' % dettaglio.piano_dei_conti,
                        'dare': '',
                        'avere': dettaglio.importo,
                        'totale': ''
                    }
                    elenco_righe_giorno.append(riga_dettaglio)
                    if dettaglio.importo:
                        totale_movimenti_avere += dettaglio.importo
            elenco_giorno = {
                'giorno': giorno['data_operazione'], 'movimenti': elenco_righe_giorno,
                'totale_dare': totale_movimenti_dare, 'totale_avere': totale_movimenti_avere
            }
            elenco_giorni_movimenti.append(elenco_giorno)
        template = get_template_stampa('libro_giornale', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('libro_giornale')
        context = Context(dict(elenco_giorni_movimenti=elenco_giorni_movimenti))
        filename = 'libro_giornale_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )

    def libro_giornale_elenco(self, request):
        list_display = list(self.list_display)
        valuta_corrente = request.user.area_corrente.valuta_default
        saldo_partitario = Money('0.00', valuta_corrente)
        totale_dare = Decimal('0.00')
        totale_avere = Decimal('0.00')
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        elenco_movimenti = cl.get_queryset(request)
        elenco_movimenti = elenco_movimenti.order_by('numero_operazione')
        elenco_dettagli_movimenti = []
        elenco_dettagli = DettaglioMovimentoPrimaNota.objects.filter(
            movimento_primanota__in=elenco_movimenti
        ).order_by('movimento_primanota__data_operazione', 'movimento_primanota__numero_operazione')
        for dettaglio in elenco_dettagli:
            saldo = dettaglio.get_saldo_convertito(valuta_corrente)
            if saldo:
                saldo_partitario += saldo
            dettaglio.saldo_partitario = saldo_partitario
            elenco_dettagli_movimenti.append(dettaglio)
        dettagli_dare = elenco_dettagli.filter(categoria='dare')
        elenco_dettagli_dare = elenco_dettagli.filter(categoria='dare')
        elenco_valori_convertiti_dare = ConversioneDettaglioMovimentoPrimaNota.objects.filter(
            dettaglio_movimento_primanota__in=elenco_dettagli_dare,
            importo_currency=valuta_corrente
        )
        saldo_dare = elenco_valori_convertiti_dare.aggregate(totale=Sum('importo'))
        if saldo_dare['totale']:
            totale_dare = saldo_dare['totale']
        elenco_dettagli_avere = elenco_dettagli.filter(categoria='avere')
        elenco_valori_convertiti_avere = ConversioneDettaglioMovimentoPrimaNota.objects.filter(
            dettaglio_movimento_primanota__in=elenco_dettagli_avere,
            importo_currency=valuta_corrente
        )
        saldo_avere = elenco_valori_convertiti_avere.aggregate(totale=Sum('importo'))
        if saldo_avere['totale']:
            totale_avere = saldo_avere['totale']
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        contesto = dict(
            elenco_dettagli_movimenti=elenco_dettagli_movimenti,
            totale_dare=Money(totale_dare, valuta_corrente),
            totale_avere=Money(totale_avere, valuta_corrente),
            intestazione_stampe=intestazione_stampe,
            area=request.user.area_corrente,
            esercizio=request.user.esercizio_corrente,
        )
        template = get_template_stampa('libro_giornale_elenco', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('libro_giornale_elenco')
        filename = 'libro_giornale_elenco_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    def rigenera_numerazione(self, request):
        area_corrente = request.user.area_corrente
        esercizio_corrente = request.user.esercizio_corrente
        elenco_movimenti = MovimentoPrimaNota.objects.filter(
            area=area_corrente, esercizio=esercizio_corrente
        ).order_by('data', 'data_operazione', 'numero_operazione')
        progressivo = 1
        for movimento in elenco_movimenti:
            movimento.numero_operazione = progressivo
            movimento.save()
            progressivo += 1
        messages.add_message(request, messages.SUCCESS, _('Numerazione movimenti rigenerata con successo'))
        url = reverse('matthaeus:movimenti_movimentoprimanota_changelist')
        return HttpResponseRedirect(url)

    def response_change(self, request, obj):
        next_url = request.GET.get('next')
        if next_url:
            # Decodifica il parametro next
            decoded_next_url = unquote(next_url)
            return HttpResponseRedirect(decoded_next_url)
        # Comportamento standard se 'next' non è presente
        return super().response_change(request, obj)

    def get_inline_instances(self, request, obj=None):
        elenco_inlines = [
            DettaglioMovimentoDareInline, DettaglioMovimentoAvereInline,
            PartitarioClientiInline, PartitarioFornitoriInline, PartitarioBancheInline,
            DettaglioMovimentoAnaliticoInline,
            DocumentoAllegatoInlinesNoAdd, DocumentoAllegatoInlinesAddOnly,
        ]
        if obj:
            if obj.area:
                area_corrente = request.user.area_corrente
                if obj.area != area_corrente:
                    if obj.area.valuta_default != area_corrente.valuta_default:
                        if DettaglioMovimentoDareReadonlyInline not in elenco_inlines:
                            elenco_inlines += [DettaglioMovimentoDareReadonlyInline]
                        if DettaglioMovimentoAvereReadonlyInline not in elenco_inlines:
                            elenco_inlines += [DettaglioMovimentoAvereReadonlyInline]
                        if DettaglioMovimentoDareInline in elenco_inlines:
                            elenco_inlines.remove(DettaglioMovimentoDareInline)
                        if DettaglioMovimentoAvereInline in elenco_inlines:
                            elenco_inlines.remove(DettaglioMovimentoAvereInline)
        self.inlines = elenco_inlines
        return super(MovimentoPrimaNotaAdmin, self).get_inline_instances(request, obj)

site.register(MovimentoPrimaNota, MovimentoPrimaNotaAdmin)


class ContiMovimentatiFilter(SimpleListFilter):
    title = 'Conti movimentati'
    parameter_name = 'conti_movimentati'

    def value_as_list(self):
        return self.value().split(',') if self.value() else []

    def lookups(self, request, model_admin):
        return (
            (True, _('Sì')),
            (False, _('No')),
        )

    def queryset(self, request, queryset):
        return queryset


class BilancioContoChangeList(ChangeList):

    def get_results(self, request, *args, **kwargs):
        super(BilancioContoChangeList, self).get_results(request, *args, **kwargs)
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        else:
            if 'mesi_preventivo' in params:
                data_inizio = request.user.esercizio_corrente.data_inizio
                data_mese = data_inizio + relativedelta(months=int(params['mesi_preventivo']))
                data_fine = date(year=data_mese.year, month=data_mese.month, day=1) - relativedelta(days=1)
        elenco_bilanci = get_queryset_bilancio_calcolato(
            self.queryset, request.user.area_corrente,
            request.user.esercizio_corrente, data_inizio, data_fine
        )
        dizionario_bilancio = dict()
        for bilancio in elenco_bilanci:
            dizionario_bilancio[bilancio.id] = bilancio
        for res in self.result_list:
            if res.id in dizionario_bilancio:
                bilancio = dizionario_bilancio[res.id]
                res.totale_dare = bilancio.totale_dare
                res.totale_avere = bilancio.totale_avere
                res.ultima_data_movimenti = bilancio.ultima_data_movimenti
                res.numero_movimenti = bilancio.numero_movimenti
        if 'conti_movimentati' in params:
            nuova_lista = []
            if params['conti_movimentati'] == 'True':
                nuova_lista = [conto for conto in self.result_list if conto.numero_movimenti > 0]
            else:
                conti_non_movimentati = []
                for conto in  self.result_list:
                    if conto.numero_movimenti == 0 or conto.numero_movimenti == '':
                        conti_non_movimentati.append(conto)
                nuova_lista = conti_non_movimentati
            self.result_list = nuova_lista


class BilancioContoAdmin(MatthaeusModelAdmin):
    list_display = (
        'get_codice', 'get_descrizione', 'get_natura_conto',
        'get_totale_dare', 'get_totale_avere', 'get_saldo',
        'get_ultima_data_movimenti', 'get_link_movimenti',
    )
    search_fields = ('piano_dei_conti__codice', 'piano_dei_conti__descrizione', )
    list_filter = (
        DateRangeFilter, LivelloContiBilancioFilter, ContiMovimentatiFilter,
        'piano_dei_conti__tipologia', 'piano_dei_conti__gestione',
    )
    readonly_fields = (
        'area', 'esercizio', 'get_totale_dare', 'get_totale_avere', 'piano_dei_conti',
        'get_ultima_data_movimenti', 'get_saldo', 'get_link_movimenti',
        'get_totale_preventivo', 'get_preventivo_conto'
    )
    list_per_page = 100
    form = forms.BilancioContoForm
    inlines = [PreventivoCentroDiCostoInline, ConsuntivoCentroDiCostoInline]
    list_select_related = ('piano_dei_conti', 'area', 'esercizio')
    fieldsets = (
        (
            _('Dati principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'piano_dei_conti',
                    'area',
                    'esercizio',
                )
            )
        ),
        (
            _('TOTALI'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'get_totale_dare',
                    'get_totale_avere',
                    'get_saldo',
                )
            ),
        ),
        (
            _('PREVENTIVO'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'get_preventivo_conto',
                    'get_totale_preventivo',
                )
            )
        ),
    )
    actions = [
        'elenco_partitari', 'bilancio_mastri',
        'bilancio_conti', 'bilancio_conti_aggregazioni',
        'bilancio_sottoconti', 'bilancio_sottoconti_aggregazione',
        'rendiconto_finanziario_conti', 'bilancio_riclassificato_conti',
        'stato_patrimoniale', 'stato_patrimoniale_parziali',
        'stato_patrimoniale_clienti_fornitori', 'bilancio_comparativo',
        'export_bilancio_xls', 'export_riepilogo_centri_di_costo',
        'vedi_grafico_conto_economico', 'vedi_grafico_riepilogo_costi',
        'vedi_grafico_riepilogo_ricavi', 'vedi_grafico_liquidita',
    ]
    suit_form_tabs = (
        ('dati_principali', _('Dati Principali')),
        ('preventivo_centro_costo', _('Preventivi Centri di Costo')),
        ('consuntivo_centro_costo', _('Consuntivi Centri di Costo')),
    )

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        if esercizio_area_chiuso(request.user.esercizio_corrente, request.user.area_corrente):
            elenco_campi_base = [n.name for n in self.opts.fields]
            elenco_campi_calcolati = [
                'get_totale_dare', 'get_totale_avere', 'piano_dei_conti',
                'get_ultima_data_movimenti', 'get_saldo', 'get_link_movimenti',
                'get_totale_preventivo', 'get_preventivo_conto'
            ]
            return elenco_campi_base + elenco_campi_calcolati
        return super(BilancioContoAdmin, self).get_readonly_fields(request, obj)

    def get_actions(self, request):
        actions = super().get_actions(request)
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
            if 'bilancio_mastri' in actions:
                actions.pop('bilancio_mastri')
        return actions

    def get_changelist(self, request, **kwargs):
        return BilancioContoChangeList

    def export_bilancio_xls(self, request, queryset):
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        foglio_excel = crea_fogli_bilancio(
            queryset, data_inizio, data_fine, request.user.area_corrente, request.user.esercizio_corrente
        )
        response = HttpResponse(content_type='application/vnd.ms-excel')
        filename = 'stato_patrimoniale_conto_economico_%s_%s.xls' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        response['Content-Disposition'] = 'attachment; filename="%s"' % filename
        foglio_excel.save(response)
        return response
    export_bilancio_xls.short_description = 'Stato Patrimoniale/Conto Economico (XLS)'

    def bilancio_sottoconti(self, request, queryset):
        elenco_bilanci = queryset.order_by('piano_dei_conti', ).filter(piano_dei_conti__livello='sottoconto')
        valuta_corrente = request.user.area_corrente.valuta_default
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_bilanci = get_queryset_bilancio_calcolato(
            elenco_bilanci, request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        totale_attivo = Money(0.00, valuta_corrente)
        totale_passivo = Money(0.00, valuta_corrente)
        totale_costi = Money(0.00, valuta_corrente)
        totale_ricavi = Money(0.00, valuta_corrente)
        for conto_bilancio in elenco_bilanci:
            if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                if conto_bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                    saldo_patrimoniale_dare = conto_bilancio.get_totale_dare()
                    saldo_patrimoniale_avere = conto_bilancio.get_totale_avere()
                    if saldo_patrimoniale_dare > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                            totale_attivo = totale_attivo + abs(saldo_patrimoniale_dare)
                    if saldo_patrimoniale_avere > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                            totale_passivo = totale_passivo + abs(saldo_patrimoniale_avere)
                elif conto_bilancio.piano_dei_conti.tipologia == 'costi':
                    saldo_costi = conto_bilancio.get_saldo()
                    totale_costi = totale_costi + saldo_costi
                elif conto_bilancio.piano_dei_conti.tipologia == 'ricavi':
                    saldo_ricavi = conto_bilancio.get_saldo_abs()
                    totale_ricavi = totale_ricavi + saldo_ricavi
        differenza_attivo_passivo = totale_attivo - totale_passivo
        differenza_ricavi_costi = totale_ricavi - totale_costi
        template = get_template_stampa('bilancio_sottoconti', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_sottoconti')
        context = Context(
            dict(
                elenco_bilanci=elenco_bilanci,
                titolo=_('Bilancio ai sottoconti (senza aggregazioni)'),
                totale_attivo=Money(totale_attivo.amount, valuta_corrente),
                totale_passivo=Money(totale_passivo.amount, valuta_corrente),
                differenza_attivo_passivo=Money(differenza_attivo_passivo.amount, valuta_corrente),
                totale_costi=Money(totale_costi.amount, valuta_corrente),
                totale_ricavi=Money(totale_ricavi.amount, valuta_corrente),
                differenza_ricavi_costi=Money(differenza_ricavi_costi.amount, valuta_corrente),
            )
        )
        filename = 'bilancio_sottoconti_senza_aggregazione_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_sottoconti.short_description = _('Bilancio ai sottoconti (senza aggregazioni)')

    def bilancio_sottoconti_aggregazione(self, request, queryset):
        elenco_bilanci = queryset.order_by('piano_dei_conti', )
        valuta_corrente = request.user.area_corrente.valuta_default
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_bilanci = get_queryset_bilancio_calcolato(
            elenco_bilanci,
            request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        totale_attivo = Money(0.00, valuta_corrente)
        totale_passivo = Money(0.00, valuta_corrente)
        totale_costi = Money(0.00, valuta_corrente)
        totale_ricavi = Money(0.00, valuta_corrente)
        for conto_bilancio in elenco_bilanci:
            if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                if conto_bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                    saldo_patrimoniale_dare = conto_bilancio.get_totale_dare()
                    saldo_patrimoniale_avere = conto_bilancio.get_totale_avere()
                    if saldo_patrimoniale_dare > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                            totale_attivo = totale_attivo + abs(saldo_patrimoniale_dare)
                    if saldo_patrimoniale_avere > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'sottoconto':
                            totale_passivo = totale_passivo + abs(saldo_patrimoniale_avere)
                elif conto_bilancio.piano_dei_conti.tipologia == 'costi':
                    saldo_costi = conto_bilancio.get_saldo()
                    totale_costi = totale_costi + saldo_costi
                elif conto_bilancio.piano_dei_conti.tipologia == 'ricavi':
                    saldo_ricavi = conto_bilancio.get_saldo_abs()
                    totale_ricavi = totale_ricavi + saldo_ricavi
        differenza_attivo_passivo = totale_attivo - totale_passivo
        differenza_ricavi_costi = totale_ricavi - totale_costi
        template = get_template_stampa('bilancio_sottoconti_aggregazioni', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_sottoconti_aggregazioni')
        context = Context(
            dict(
                elenco_bilanci=elenco_bilanci,
                titolo=_('Bilancio ai sottoconti (con aggregazioni)'),
                totale_attivo=Money(totale_attivo.amount, valuta_corrente),
                totale_passivo=Money(totale_passivo.amount, valuta_corrente),
                differenza_attivo_passivo=Money(differenza_attivo_passivo.amount, valuta_corrente),
                totale_costi=Money(totale_costi.amount, valuta_corrente),
                totale_ricavi=Money(totale_ricavi.amount, valuta_corrente),
                differenza_ricavi_costi=Money(differenza_ricavi_costi.amount, valuta_corrente),
            )
        )
        filename = 'bilancio_sottoconti_aggregazione_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_sottoconti_aggregazione.short_description = _('Bilancio ai sottoconti (con aggregazioni)')

    def bilancio_conti(self, request, queryset):
        # elenco_bilanci = queryset.order_by('piano_dei_conti', ).filter(piano_dei_conti__livello='conto')
        elenco_bilanci = queryset.order_by('piano_dei_conti', )
        valuta_corrente = request.user.area_corrente.valuta_default
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_bilanci = get_queryset_bilancio_calcolato(
            elenco_bilanci, request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        totale_attivo = Money(0.00, valuta_corrente)
        totale_passivo = Money(0.00, valuta_corrente)
        totale_costi = Money(0.00, valuta_corrente)
        totale_ricavi = Money(0.00, valuta_corrente)
        bilanci_mastri_conti = [bilancio for bilancio in elenco_bilanci if bilancio.piano_dei_conti.livello == 'conto']
        for conto_bilancio in bilanci_mastri_conti:
            if conto_bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                saldo_patrimoniale_dare = conto_bilancio.get_totale_dare()
                saldo_patrimoniale_avere = conto_bilancio.get_totale_avere()
                if saldo_patrimoniale_dare > Money(0.00, valuta_corrente):
                    if conto_bilancio.piano_dei_conti.livello == 'conto':
                        totale_attivo = totale_attivo + abs(saldo_patrimoniale_dare)
                if saldo_patrimoniale_avere > Money(0.00, valuta_corrente):
                    if conto_bilancio.piano_dei_conti.livello == 'conto':
                        totale_passivo = totale_passivo + abs(saldo_patrimoniale_avere)
            elif conto_bilancio.piano_dei_conti.tipologia == 'costi':
                saldo_costi = conto_bilancio.get_saldo()
                totale_costi = totale_costi + saldo_costi
            elif conto_bilancio.piano_dei_conti.tipologia == 'ricavi':
                saldo_ricavi = conto_bilancio.get_saldo_abs()
                totale_ricavi = totale_ricavi + saldo_ricavi
        differenza_attivo_passivo = totale_attivo - totale_passivo
        differenza_ricavi_costi = totale_ricavi - totale_costi
        template = get_template_stampa('bilancio_conti', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_conti')
        context = Context(
            dict(
                elenco_bilanci=bilanci_mastri_conti,
                titolo=_('Bilancio ai conti'),
                totale_attivo=Money(totale_attivo.amount, valuta_corrente),
                totale_passivo=Money(totale_passivo.amount, valuta_corrente),
                differenza_attivo_passivo=Money(differenza_attivo_passivo.amount, valuta_corrente),
                totale_costi=Money(totale_costi.amount, valuta_corrente),
                totale_ricavi=Money(totale_ricavi.amount, valuta_corrente),
                differenza_ricavi_costi=Money(differenza_ricavi_costi.amount, valuta_corrente),
            )
        )
        filename = 'bilancio_conti_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_conti.short_description = _('Bilancio ai conti')

    def bilancio_conti_aggregazioni(self, request, queryset):
        #elenco_bilanci = queryset.order_by('piano_dei_conti', ).filter(piano_dei_conti__livello__in=['gruppo', 'mastro', 'conto'])
        elenco_bilanci = queryset.order_by('piano_dei_conti', )
        valuta_corrente = request.user.area_corrente.valuta_default
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_bilanci = get_queryset_bilancio_calcolato(
            elenco_bilanci,
            request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        totale_attivo = Money(0.00, valuta_corrente)
        totale_passivo = Money(0.00, valuta_corrente)
        totale_costi = Money(0.00, valuta_corrente)
        totale_ricavi = Money(0.00, valuta_corrente)
        bilanci_mastri_conti = [bilancio for bilancio in elenco_bilanci if bilancio.piano_dei_conti.livello in ['gruppo', 'mastro', 'conto']]
        for conto_bilancio in bilanci_mastri_conti:
            if conto_bilancio.piano_dei_conti.livello == 'conto':
                if conto_bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                    saldo_patrimoniale_dare = conto_bilancio.get_totale_dare()
                    saldo_patrimoniale_avere = conto_bilancio.get_totale_avere()
                    if saldo_patrimoniale_dare > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'conto':
                            totale_attivo = totale_attivo + abs(saldo_patrimoniale_dare)
                    if saldo_patrimoniale_avere > Money(0.00, valuta_corrente):
                        if conto_bilancio.piano_dei_conti.livello == 'conto':
                            totale_passivo = totale_passivo + abs(saldo_patrimoniale_avere)
                elif conto_bilancio.piano_dei_conti.tipologia == 'costi':
                    saldo_costi = conto_bilancio.get_saldo()
                    totale_costi = totale_costi + saldo_costi
                elif conto_bilancio.piano_dei_conti.tipologia == 'ricavi':
                    saldo_ricavi = conto_bilancio.get_saldo_abs()
                    totale_ricavi = totale_ricavi + saldo_ricavi
        differenza_attivo_passivo = totale_attivo - totale_passivo
        differenza_ricavi_costi = totale_ricavi - totale_costi
        template = get_template_stampa('bilancio_conti_aggregazioni', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_conti_aggregazioni')
        context = Context(
            dict(
                elenco_bilanci=bilanci_mastri_conti,
                titolo=_('Bilancio ai conti (con aggregazioni)'),
                totale_attivo=Money(totale_attivo.amount, valuta_corrente),
                totale_passivo=Money(totale_passivo.amount, valuta_corrente),
                differenza_attivo_passivo=Money(differenza_attivo_passivo.amount, valuta_corrente),
                totale_costi=Money(totale_costi.amount, valuta_corrente),
                totale_ricavi=Money(totale_ricavi.amount, valuta_corrente),
                differenza_ricavi_costi=Money(differenza_ricavi_costi.amount, valuta_corrente),
            )
        )
        filename = 'bilancio_conti_aggregazioni_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_conti_aggregazioni.short_description = _('Bilancio ai conti (con aggregazioni)')

    def bilancio_mastri(self, request, queryset):
        # elenco_bilanci = queryset.order_by('piano_dei_conti', ).filter(piano_dei_conti__livello='mastro')
        elenco_bilanci = queryset.order_by('piano_dei_conti', )
        valuta_corrente = request.user.area_corrente.valuta_default
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_bilanci = get_queryset_bilancio_calcolato(
            elenco_bilanci, request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        totale_attivo = Money(0.00, valuta_corrente)
        totale_passivo = Money(0.00, valuta_corrente)
        totale_costi = Money(0.00, valuta_corrente)
        totale_ricavi = Money(0.00, valuta_corrente)
        bilanci_mastri_conti = [bilancio for bilancio in elenco_bilanci if bilancio.piano_dei_conti.livello == 'mastro']
        for conto_bilancio in bilanci_mastri_conti:
            if conto_bilancio.piano_dei_conti.tipologia in ['liquidita', 'patrimoniale']:
                saldo_patrimoniale_dare = conto_bilancio.get_totale_dare()
                saldo_patrimoniale_avere = conto_bilancio.get_totale_avere()
                if saldo_patrimoniale_dare > Money(0.00, valuta_corrente):
                    if conto_bilancio.piano_dei_conti.livello == 'mastro':
                        totale_attivo = totale_attivo + abs(saldo_patrimoniale_dare)
                if saldo_patrimoniale_avere > Money(0.00, valuta_corrente):
                    if conto_bilancio.piano_dei_conti.livello == 'mastro':
                        totale_passivo = totale_passivo + abs(saldo_patrimoniale_avere)
            elif conto_bilancio.piano_dei_conti.tipologia == 'costi':
                saldo_costi = conto_bilancio.get_saldo()
                totale_costi = totale_costi + saldo_costi
            elif conto_bilancio.piano_dei_conti.tipologia == 'ricavi':
                saldo_ricavi = conto_bilancio.get_saldo_abs()
                totale_ricavi = totale_ricavi + saldo_ricavi
        differenza_attivo_passivo = totale_attivo - totale_passivo
        differenza_ricavi_costi = totale_ricavi - totale_costi
        template = get_template_stampa('bilancio_sottoconti_aggregazioni', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_sottoconti_aggregazioni')
        context = Context(
            dict(
                elenco_bilanci=bilanci_mastri_conti,
                titolo=_('Bilancio ai mastri'),
                totale_attivo=Money(totale_attivo.amount, valuta_corrente),
                totale_passivo=Money(totale_passivo.amount, valuta_corrente),
                differenza_attivo_passivo=Money(differenza_attivo_passivo.amount, valuta_corrente),
                totale_costi=Money(totale_costi.amount, valuta_corrente),
                totale_ricavi=Money(totale_ricavi.amount, valuta_corrente),
                differenza_ricavi_costi=Money(differenza_ricavi_costi.amount, valuta_corrente),
            )
        )
        filename = 'bilancio_mastri_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_mastri.short_description = _('Bilancio ai mastri')

    def stato_patrimoniale(self, request, queryset):
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        dati_stato_patrimoniale_conto_economico = get_dati_stato_patrimoniale_conto_economico(
            queryset, data_inizio, data_fine, request.user.area_corrente,
            request.user.esercizio_corrente
        )
        template = get_template_stampa('stato_patrimoniale', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('stato_patrimoniale')
        context = Context(dati_stato_patrimoniale_conto_economico)
        filename = 'stato_patrimoniale_conto_economico_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stato_patrimoniale.short_description = _('Stato patr./Conto Economico')

    def stato_patrimoniale_parziali(self, request, queryset):
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        dati_stato_patrimoniale_conto_economico = get_dati_stato_patrimoniale_conto_economico(
            queryset, data_inizio, data_fine, request.user.area_corrente,
            request.user.esercizio_corrente
        )
        dati_stato_patrimoniale_con_parziali = aggiungi_dati_parziali_aree(
            dati_stato_patrimoniale_conto_economico,
            request.user.area_corrente,
            request.user.esercizio_corrente,
        )
        template = get_template_stampa('stato_patrimoniale_parziali', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('stato_patrimoniale_parziali')
        context = Context(dati_stato_patrimoniale_con_parziali)
        filename = 'stato_patr_conto_eco_espl_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stato_patrimoniale_parziali.short_description = _('Stato patr./Conto Economico (con parz.)')

    def stato_patrimoniale_clienti_fornitori(self, request, queryset):
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        dati_stato_patrimoniale_conto_economico = get_dati_stato_patrimoniale_conto_economico(
            queryset, data_inizio, data_fine, request.user.area_corrente,
            request.user.esercizio_corrente
        )
        dati_stato_patrimoniale_clienti_fornitori = aggiungi_dati_clienti_fornitori(
            dati_stato_patrimoniale_conto_economico,
            request.user.area_corrente,
            request.user.esercizio_corrente,
        )
        template = get_template_stampa('stato_patrimoniale_clienti_fornitori', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('stato_patrimoniale_clienti_fornitori')
        context = Context(dati_stato_patrimoniale_clienti_fornitori)
        filename = 'stato_patr_conto_eco_clienti_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stato_patrimoniale_clienti_fornitori.short_description = _('Stato patr./Conto Economico (dett.clienti/forn.)')

    def bilancio_comparativo(self, request, queryset):
        """
        Genera un bilancio comparativo tra l'esercizio corrente e quello precedente.
        """
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]

        # Importa la funzione dalla utils
        from .utils import get_dati_bilancio_comparativo

        # Genera i dati comparativi
        dati_comparativi = get_dati_bilancio_comparativo(
            queryset, data_inizio, data_fine, request.user.area_corrente,
            request.user.esercizio_corrente
        )

        # Prepara il contesto per il template comparativo DOCX
        template = get_template_stampa('bilancio_comparativo', request.user.language)
        if not exists(template):
            template = get_template_stampa('bilancio_comparativo')

        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        contesto = dict(
            dati_comparativi=dati_comparativi,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().strftime('%d/%m/%Y %H:%M'),
            data_inizio=data_inizio,
            data_fine=data_fine,   
            area_corrente=request.user.area_corrente,
            esercizio_corrente=request.user.esercizio_corrente,
            esercizio_precedente=dati_comparativi['esercizio_precedente'],
        )
        filename = 'bilancio_comparativo_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(),
            slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    bilancio_comparativo.short_description = _('Bilancio Comparativo (esercizio corrente vs precedente)')

    def rendiconto_finanziario_conti(self, request, queryset):
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        valuta_corrente = request.user.area_corrente.valuta_default
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        area_corrente = request.user.area_corrente
        esercizio_corrente = request.user.esercizio_corrente
        if queryset:
            gestione_operativa = get_rendiconto_finanziario_conti(
                'operativa', queryset, area_corrente, esercizio_corrente,
                data_inizio, data_fine,
            )
            gestione_finanziaria = get_rendiconto_finanziario_conti(
                'finanziaria', queryset, area_corrente, esercizio_corrente,
                data_inizio, data_fine,
            )
            gestione_patrimoniale = get_rendiconto_finanziario_conti(
                'patrimoniale', queryset, area_corrente, esercizio_corrente,
                data_inizio, data_fine,
            )
        margine_operativo_lordo = gestione_operativa['indice_rendiconto']
        risultato_gestione_finanziaria = gestione_finanziaria['indice_rendiconto']
        risultato_mol_gestione_finanziaria = margine_operativo_lordo + risultato_gestione_finanziaria
        risultato_gestione_patrimoniale = gestione_patrimoniale['indice_rendiconto']
        risultato_esercizio = margine_operativo_lordo + risultato_gestione_finanziaria + risultato_gestione_patrimoniale
        conti_liquidita = queryset.filter(piano_dei_conti__livello='conto', piano_dei_conti__tipologia='liquidita')
        conti_liquidita_iniziale = None
        if data_inizio:
            inizio_esercizio_corrente = esercizio_corrente.data_inizio
            if not inizio_esercizio_corrente == data_inizio:
                conti_liquidita_iniziale = get_queryset_bilancio_calcolato(
                    conti_liquidita, area_corrente, esercizio_corrente,
                    data_inizio=inizio_esercizio_corrente,
                    data_fine=(data_inizio - timedelta(days=1))
                )
        else:
            inizio_esercizio_corrente = esercizio_corrente.data_inizio
            data_inizio = esercizio_corrente.data_inizio
            conti_liquidita_iniziale = get_queryset_bilancio_calcolato(
                conti_liquidita, area_corrente, esercizio_corrente,
                data_inizio=inizio_esercizio_corrente,
                data_fine=inizio_esercizio_corrente
            )
        if not data_fine:
            data_fine = esercizio_corrente.data_fine
        liquidita_iniziale = Money(0.00, valuta_corrente)
        if conti_liquidita_iniziale:
            for conto_liquidita in conti_liquidita_iniziale:
                liquidita_iniziale += conto_liquidita.get_saldo_abs()
        liquidita_finale = liquidita_iniziale + risultato_esercizio
        context = Context(
            dict(
                # OPERATIVA
                gestione_operativa_conti_ricavi=gestione_operativa['bilanci_ricavi'],
                gestione_operativa_totale_ricavi=gestione_operativa['totale_ricavi'],
                gestione_operativa_conti_costi=gestione_operativa['bilanci_costi'],
                gestione_operativa_totale_costi=gestione_operativa['totale_costi'],
                margine_operativo_lordo=margine_operativo_lordo,
                # FINANZIARI
                gestione_finanziaria_conti_ricavi=gestione_finanziaria['bilanci_ricavi'],
                gestione_finanziaria_totale_ricavi=gestione_finanziaria['totale_ricavi'],
                gestione_finanziaria_conti_costi=gestione_finanziaria['bilanci_costi'],
                gestione_finanziaria_totale_costi=gestione_finanziaria['totale_costi'],
                risultato_gestione_finanziaria=risultato_gestione_finanziaria,
                risultato_mol_gestione_finanziaria=Money(risultato_mol_gestione_finanziaria.amount, valuta_corrente),
                # PATRIMONIALE
                gestione_patrimoniale_conti_ricavi=gestione_patrimoniale['bilanci_ricavi'],
                gestione_patrimoniale_totale_ricavi=gestione_patrimoniale['totale_ricavi'],
                gestione_patrimoniale_conti_costi=gestione_patrimoniale['bilanci_costi'],
                gestione_patrimoniale_totale_costi=gestione_patrimoniale['totale_costi'],
                risultato_gestione_patrimoniale=risultato_gestione_patrimoniale,
                risultato_esercizio=Money(risultato_esercizio.amount, valuta_corrente),
                liquidita_iniziale=Money(liquidita_iniziale.amount, valuta_corrente),
                liquidita_finale=Money(liquidita_finale.amount, valuta_corrente),
                data_inizio=data_inizio.strftime('%d/%m/%Y'),
                data_fine=data_fine.strftime('%d/%m/%Y'),
            )
        )
        template = get_template_stampa('rendiconto_finanziario', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('rendiconto_finanziario')
        filename = 'rendiconto_finanziario_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    rendiconto_finanziario_conti.short_description = _('Rendiconto Finanziario (ai conti)')

    def bilancio_riclassificato_conti(self, request, queryset):
        area_corrente = request.user.area_corrente
        esercizio_corrente = request.user.esercizio_corrente
        valuta_corrente = area_corrente.valuta_default
        if queryset:
            gestione_operativa = get_bilancio_riclassificato_conti('operativa', queryset, area_corrente, esercizio_corrente)
            gestione_finanziaria = get_bilancio_riclassificato_conti('finanziaria', queryset, area_corrente, esercizio_corrente)
            gestione_patrimoniale = get_bilancio_riclassificato_conti('patrimoniale', queryset, area_corrente, esercizio_corrente)
        margine_operativo_lordo = gestione_operativa['indice_rendiconto']
        risultato_gestione_finanziaria = gestione_finanziaria['indice_rendiconto']
        risultato_mol_gestione_finanziaria = margine_operativo_lordo + risultato_gestione_finanziaria
        risultato_gestione_patrimoniale = gestione_patrimoniale['indice_rendiconto']
        risultato_esercizio = margine_operativo_lordo + risultato_gestione_finanziaria + risultato_gestione_patrimoniale
        context = Context(
            dict(
                # OPERATIVA
                gestione_operativa_conti_ricavi=gestione_operativa['bilanci_ricavi'],
                gestione_operativa_totale_ricavi=gestione_operativa['totale_ricavi'],
                gestione_operativa_conti_costi=gestione_operativa['bilanci_costi'],
                gestione_operativa_totale_costi=gestione_operativa['totale_costi'],
                margine_operativo_lordo=margine_operativo_lordo,
                # FINANZIARI
                gestione_finanziaria_conti_ricavi=gestione_finanziaria['bilanci_ricavi'],
                gestione_finanziaria_totale_ricavi=gestione_finanziaria['totale_ricavi'],
                gestione_finanziaria_conti_costi=gestione_finanziaria['bilanci_costi'],
                gestione_finanziaria_totale_costi=gestione_finanziaria['totale_costi'],
                risultato_gestione_finanziaria=risultato_gestione_finanziaria,
                risultato_mol_gestione_finanziaria=Money(risultato_mol_gestione_finanziaria.amount, valuta_corrente),
                # PATRIMONIALE
                gestione_patrimoniale_conti_ricavi=gestione_patrimoniale['bilanci_ricavi'],
                gestione_patrimoniale_totale_ricavi=gestione_patrimoniale['totale_ricavi'],
                gestione_patrimoniale_conti_costi=gestione_patrimoniale['bilanci_costi'],
                gestione_patrimoniale_totale_costi=gestione_patrimoniale['totale_costi'],
                risultato_gestione_patrimoniale=risultato_gestione_patrimoniale,
                risultato_esercizio=Money(risultato_esercizio.amount, valuta_corrente),
            )
        )
        template = get_template_stampa('bilancio_riclassificato', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('bilancio_riclassificato')
        filename = 'bilancio_riclassificato_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    bilancio_riclassificato_conti.short_description = _('Bilancio riclassificato (ai conti)')

    def elenco_partitari(self, request, queryset):
        elenco_bilanci = queryset.order_by('piano_dei_conti', )
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        elenco_partitari = get_queryset_bilancio_calcolato(
            elenco_bilanci,
            request.user.area_corrente, request.user.esercizio_corrente,
            data_inizio, data_fine
        )
        template = get_template_stampa('elenco_partitari', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_partitari')
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        contesto = dict(
            elenco_partitari=elenco_partitari,
            area_corrente=request.user.area_corrente,
            esercizio_corrente=request.user.esercizio_corrente,
            intestazione_stampe=intestazione_stampe,
        )
        filename = 'elenco_partitari_%s_%s.docx' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    elenco_partitari.short_description = _('Elenco Partitari (conti selezionati)')

    def suit_row_attributes(self, obj, request):
        if obj.piano_dei_conti.is_mastro():
            return {'class': 'table-info font-italic'}
        if obj.piano_dei_conti.is_sottoconto():
            return {'class': 'table-warning font-weight-normal'}
        return {'class': 'table-success font-weight-bold'}

    def suit_column_attributes(self, column):
        if column in [
            'totale_dare', 'totale_avere', 'get_saldo',
            'get_totale_dare', 'get_totale_avere',
        ]:
            return {'class': 'text-xs-right'}

    def suit_cell_attributes(self, obj, column):
        if column in [
            'totale_dare', 'totale_avere', 'get_totale_dare', 'get_totale_avere',
            'get_totale_dare_display', 'get_totale_avere_display',
        ]:
            css_dict = {'class': 'text-xs-right'}
            return css_dict
        if column == 'get_saldo':
            css_dict = {'class': 'text-xs-right'}
            saldo = obj.get_saldo()
            if saldo < Money(0.00, saldo.currency):
                css_dict['style'] = 'color:red;'
            return css_dict

    def get_urls(self):
        url_patterns = [
            url(
                r'^aggiorna/',
                self.admin_site.admin_view(self.aggiorna),
                name='movimenti_bilancioconto_aggiorna'
            ),
            url(
                r'^aggiornaconsuntivi/$',
                self.admin_site.admin_view(self.aggiorna_consuntivi_centri_di_costo),
                name='movimenti_bilancioconto_aggiornaconsuntivi'
            ),
            url(
                r'^aggiornaparziale/$',
                self.admin_site.admin_view(AggiornaParzialeWizard.as_view()),
                name='movimenti_bilancioconto_aggiornaparziale'
            ),
        ]
        url_patterns += super(BilancioContoAdmin, self).get_urls()
        return url_patterns

    def vedi_grafico_conto_economico(self, request, queryset):
        return GraficoContoEconomicoView.as_view()(request, queryset=queryset)
    vedi_grafico_conto_economico.short_description = _('Vedi Grafico Conto Economico')

    def vedi_grafico_riepilogo_costi(self, request, queryset):
        return GraficoRiepilogoCostiView.as_view()(request, queryset=queryset)
    vedi_grafico_riepilogo_costi.short_description = _('Vedi Grafico Riepilogo Costi (per conto)')

    def vedi_grafico_riepilogo_ricavi(self, request, queryset):
        return GraficoRiepilogoRicaviView.as_view()(request, queryset=queryset)
    vedi_grafico_riepilogo_ricavi.short_description = _('Vedi Grafico Riepilogo Ricavi (per conto)')

    def vedi_grafico_budget(self, request, queryset):
        return GraficoBudgetView.as_view()(request, queryset=queryset)
    vedi_grafico_budget.short_description = _('Vedi Grafico Budget (per conto)')

    def vedi_grafico_liquidita(self, request, queryset):
        return GraficoLiquiditaView.as_view()(request, queryset=queryset)
    vedi_grafico_liquidita.short_description = _('Vedi Grafico Liquidita\'')

    def export_riepilogo_centri_di_costo(self, request, queryset):
        foglio_excel = crea_riepilogo_centri_di_costo(queryset)
        response = HttpResponse(content_type='application/vnd.ms-excel')
        filename = 'riepilogo_centri_di_costo_%s_%s.xls' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        response['Content-Disposition'] = 'attachment; filename="%s"' % filename
        foglio_excel.save(response)
        return response
    export_riepilogo_centri_di_costo.short_description = _('Esporta Riepilogo Centri di costo (xls)')

    def aggiorna(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        aggiorna_bilancio(area_corrente, esercizio_corrente)
        url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        return HttpResponseRedirect(url)

    def aggiorna_consuntivi_centri_di_costo(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        aggiorna_centri_di_costo(area_corrente, esercizio_corrente)
        url = reverse('matthaeus:movimenti_bilancioconto_changelist')
        return HttpResponseRedirect(url)

    def get_queryset(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        if area_corrente and esercizio_corrente:
            pass
        else:
            messages.warning(request, 'selezionare un\' area e un esercizio per mostrare il bilancio')
            return BilancioConto.objects.none()
        queryset_base = super(BilancioContoAdmin, self).get_queryset(request)
        queryset_base = queryset_base.filter(area=area_corrente, esercizio=esercizio_corrente)
        return queryset_base

    def get_ultima_data_movimenti(self, obj):
        if obj.ultima_data_movimenti:
            return obj.ultima_data_movimenti
        return ''
    get_ultima_data_movimenti.short_description = _('Data ultimo mov.')

    def get_numero_movimenti(self, obj):
        if obj.numero_movimenti:
            return obj.numero_movimenti
        return ''
    get_numero_movimenti.short_description = _('Num. Movimenti')

    def get_link_movimenti(self, obj):
        if obj.numero_movimenti:
            if obj.piano_dei_conti.livello == 'sottoconto':
                return format_html(
                    '<a href="/matthaeus/movimenti/mastrinosottoconti/?q=&piano_dei_conti_id__exact=%s" target="">%s</a>' % (
                        obj.piano_dei_conti.id, obj.numero_movimenti
                    )
                )
            else:
                return obj.numero_movimenti
        else:
            return ''
    get_link_movimenti.short_description = _('Num. Movimenti')
    get_link_movimenti.admin_order_field = 'piano_dei_conti'

site.register(BilancioConto, BilancioContoAdmin)


class MesiPreventivoFilter(SimpleListFilter):
    title = 'Mesi preventivo'
    parameter_name = 'mesi_preventivo'

    def lookups(self, request, model_admin):
        return (
            (1, 1),
            (2, 2),
            (3, 3),
            (4, 4),
            (5, 5),
            (6, 6),
            (7, 7),
            (8, 8),
            (9, 9),
            (10, 10),
            (11, 11),
            (12, 12),
        )

    def queryset(self, request, queryset):
        if self.value() is not None:
            return queryset.annotate(mesi_preventivo=Value(self.value(), output_field=IntegerField()))
        else:
            return queryset


class BilancioContoPreventivoChangeList(ChangeList):

    def get_results(self, request, *args, **kwargs):
        super(BilancioContoPreventivoChangeList, self).get_results(request, *args, **kwargs)
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
        else:
            if 'mesi_preventivo' in params:
                data_inizio = request.user.esercizio_corrente.data_inizio
                data_mese = data_inizio + relativedelta(months=int(params['mesi_preventivo']))
                data_fine = date(year=data_mese.year, month=data_mese.month, day=1) - relativedelta(days=1)
        elenco_bilanci = get_queryset_bilancio_preventivo(
            self.queryset, request.user.area_corrente,
            request.user.esercizio_corrente, data_inizio, data_fine
        )
        dizionario_bilancio = dict()
        for bilancio in elenco_bilanci:
            dizionario_bilancio[bilancio.id] = bilancio
        for res in self.result_list:
            if res.id in dizionario_bilancio:
                bilancio = dizionario_bilancio[res.id]
                res.totale_dare = bilancio.totale_dare
                res.totale_avere = bilancio.totale_avere
                res.totale_preventivo = bilancio.totale_preventivo
                res.ultima_data_movimenti = bilancio.ultima_data_movimenti
                res.numero_movimenti = bilancio.numero_movimenti
        if 'conti_movimentati' in params:
            nuova_lista = []
            if params['conti_movimentati'] == 'True':
                nuova_lista = [conto for conto in self.result_list if conto.numero_movimenti > 0]
            else:
                conti_non_movimentati = []
                for conto in  self.result_list:
                    if conto.numero_movimenti == 0 or conto.numero_movimenti == '':
                        conti_non_movimentati.append(conto)
                nuova_lista = conti_non_movimentati
            self.result_list = nuova_lista


class BilancioContoPreventivoAdmin(ExportActionMixin, BilancioContoAdmin):
    list_display = (
        'get_codice', 'get_descrizione', 'get_natura_conto',
        'get_totale_preventivo', 'get_consuntivo', 'get_scostamento',
        'get_ultima_data_movimenti', 'get_numero_movimenti',
    )
    list_filter = (
        MesiPreventivoFilter,
        'piano_dei_conti__livello',
        ContiMovimentatiFilter,
        'piano_dei_conti__tipologia',
    )
    readonly_fields = (
        'area', 'esercizio', 'get_totale_dare', 'get_totale_avere', 'piano_dei_conti',
        'get_ultima_data_movimenti', 'get_saldo', 'get_numero_movimenti',
        'get_totale_preventivo', 'get_valore_preventivo_aree',
    )
    fieldsets = (
        (
            _('Dati principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'piano_dei_conti',
                    'area',
                    'esercizio',
                )
            )
        ),
        (
            _('PREVENTIVO'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'preventivo',
                    'get_valore_preventivo_aree',
                )
            )
        ),
    )
    formats = (
        base_formats.CSV,
        base_formats.XLS,
        base_formats.ODS,
    )
    actions = [
        'export_riepilogo_centri_di_costo', 'vedi_grafico_budget',
    ]
    resource_class = BilancioContoPreventivoResource

    def get_actions(self, request):
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            actions.move_to_end('delete_selected')
        actions.update(
            export_admin_action=(
                BilancioContoPreventivoAdmin.export_admin_action,
                "export_admin_action",
                _("Esporta %(verbose_name_plural)s (selezionati)"),
            )
        )
        return actions

    def suit_cell_attributes(self, obj, column):
        if column in [
            'get_totale_preventivo', 'get_consuntivo',
        ]:
            css_dict = {'class': 'text-xs-right'}
            return css_dict
        if column == 'get_scostamento':
            css_dict = {'class': 'text-xs-right'}
            scostamento = obj.get_scostamento()
            if scostamento < Money(0.00, scostamento.currency):
                css_dict['style'] = 'color:red;'
            return css_dict

    def export_admin_action(self, request, queryset):
        """
        Exports the selected rows using file_format.
        """
        export_format = request.POST.get('file_format')
        if not export_format:
            lista_export_format = request.POST.getlist('file_format')
            if lista_export_format:
                for selezione in lista_export_format:
                    if selezione:
                        export_format = selezione
                        break
        if not export_format:
            messages.warning(request, _('You must select an export format.'))
        else:
            formats = self.get_export_formats()
            file_format = formats[int(export_format)]()
            export_data = self.get_export_data(file_format, queryset, request=request, encoding=self.to_encoding)
            content_type = file_format.get_content_type()
            response = HttpResponse(export_data, content_type=content_type)
            filename = 'bilancio_preventivo_%s_%s.%s' % (
                slugify(request.user.area_corrente).upper(),
                slugify(timezone.now().date()),
                file_format.get_extension(),
            )
            # vecchio_nome = self.get_export_filename(request, queryset, file_format)
            response['Content-Disposition'] = 'attachment; filename="%s"' % (
                filename,
            )
            return response

    def get_changelist(self, request, **kwargs):
        return BilancioContoPreventivoChangeList

    def get_valore_preventivo_aree(self, obj):
        elenco_aree_figlie = obj.area.get_descendants(include_self=False)
        conti_preventivi_figli = BilancioContoPreventivo.objects.filter(
            area__in=elenco_aree_figlie, esercizio=obj.esercizio, piano_dei_conti=obj.piano_dei_conti
        ).select_related('area').order_by('area')
        stringa_preventivi = ''
        for conto_preventivo in conti_preventivi_figli:
            if conto_preventivo.preventivo:
                stringa_preventivi += '%s - %s<br>' % (conto_preventivo.area, conto_preventivo.preventivo)
        return format_html(stringa_preventivi)
    get_valore_preventivo_aree.short_description = _('Tot. Preventivo per area')
    get_valore_preventivo_aree.allow_tags = True

    def get_queryset(self, request):
        area_corrente = None
        esercizio_corrente = None
        if request.user.area_corrente:
            area_corrente = request.user.area_corrente
        if request.user.esercizio_corrente:
            esercizio_corrente = request.user.esercizio_corrente
        if area_corrente and esercizio_corrente:
            pass
        else:
            messages.warning(request, 'selezionare un\' area e un esercizio per mostrare il bilancio preventivo')
            return BilancioContoPreventivo.objects.none()
        queryset_base = super(BilancioContoPreventivoAdmin, self).get_queryset(request)
        queryset_base = queryset_base.filter(area=area_corrente, esercizio=esercizio_corrente)
        return queryset_base

site.register(BilancioContoPreventivo, BilancioContoPreventivoAdmin)


class MovimentoPrimaNotaDaConsolidareAdmin(MovimentoPrimaNotaAdmin):
    list_display = (
        'data', 'numero_documento', 'data_operazione', 
        'descrizione', 'get_totale_dare_display',
        'get_totale_avere_display', 'data_scadenza', 'area', 'esercizio',
        'get_squadratura_display',
    )
    readonly_fields = (
        'operazione_partita_semplice', 'modalita_causale', 'partita_semplice',
        'totale_movimento',
    )
    fieldsets = (
        (
            _('Testata'), dict(
                classes=('suit-tab suit-tab-dati_principali suit-tab-dettagli_dare suit-tab-dettagli_avere', ),
                fields=(
                    'data',
                    'data_operazione',
                    'descrizione',
                    'consolidato',
                )
            )
        ),
        (
            _('Informazioni aggiuntive'), dict(
                classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                fields=(
                    'numero_documento',
                    'data_scadenza',
                    'data_inizio_competenza',
                    'data_fine_competenza',
                    'tipo_movimento',
                    'totale_movimento',
                    'genere_movimento',
                )
            )
        ),
        (
            _('Causale Contabile'), dict(
                classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                fields=(
                    ('causale', 'modalita_causale'),
                    'importo_partita_semplice',
                    'operazione_partita_semplice',
                    'partita_semplice',
                )
            )
        ),
        (
            _('Dati Esercizio'), dict(
                classes=('suit-tab suit-tab-dati_principali', 'collapse'),
                fields=(
                    ('esercizio', 'area', ),
                )
            )
        ),
    )
    
    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_save_and_continue'] = False # Here
        return super(MovimentoPrimaNotaDaConsolidareAdmin, self).changeform_view(request, object_id, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        obj.consolidato = True
        obj.numero_operazione = get_nuovo_numero(obj.area, obj.esercizio)
        return super(MovimentoPrimaNotaDaConsolidareAdmin, self).save_model(request, obj, form, change)

    def has_add_permission(self, request):
        return False

    def response_change(self, request, obj):
        if '_save' in request.POST:
            url = reverse(
                'matthaeus:movimenti_movimentoprimanota_changelist',
            )
            return HttpResponseRedirect(url)
        return super(MovimentoPrimaNotaDaConsolidareAdmin, self).response_change(request, obj)

site.register(MovimentoPrimaNotaDaConsolidare, MovimentoPrimaNotaDaConsolidareAdmin)


class ConversioneDettaglioMovimentoPrimaNotaAdmin(MatthaeusModelAdmin):
    list_display = ('get_movimento_link', 'get_piano_dei_conti', 'get_categoria', 'get_importo_originale', 'importo', 'ora_creazione')
    list_filter = (
        'importo_currency',
        'dettaglio_movimento_primanota__categoria',
        ('dettaglio_movimento_primanota__movimento_primanota__area', RelatedOnlyFieldListFilter),
        ('dettaglio_movimento_primanota__piano_dei_conti', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = ('dettaglio_movimento_primanota__piano_dei_conti', )
    autocomplete_fields = ('dettaglio_movimento_primanota', )
    date_hierarchy = 'dettaglio_movimento_primanota__movimento_primanota__data'
    search_fields = (
        'dettaglio_movimento_primanota__descrizione', 'dettaglio_movimento_primanota__movimento_primanota__descrizione',
        'dettaglio_movimento_primanota__numero',
    )
    list_select_related = (
        'dettaglio_movimento_primanota', 
        'dettaglio_movimento_primanota__piano_dei_conti', 
        'dettaglio_movimento_primanota__movimento_primanota', 
        'dettaglio_movimento_primanota__movimento_primanota__area', 
        'dettaglio_movimento_primanota__movimento_primanota__esercizio'
    )
    readonly_fields = ('dettaglio_movimento_primanota', 'ora_creazione')

    def get_queryset(self, request):
        qs = super(ConversioneDettaglioMovimentoPrimaNotaAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
            qs = qs.filter(dettaglio_movimento_primanota__movimento_primanota__area__in=elenco_aree_figlie)
        if request.user.esercizio_corrente:
            qs = qs.filter(dettaglio_movimento_primanota__movimento_primanota__esercizio=request.user.esercizio_corrente)
        return qs

    def get_movimento_link(self, obj):
        base_url = reverse('matthaeus:movimenti_movimentoprimanota_change', args=[obj.dettaglio_movimento_primanota.movimento_primanota.id])
        return format_html(
            '<a href="{}">{}</a>',
            base_url,
            obj.dettaglio_movimento_primanota.movimento_primanota
        )
    get_movimento_link.short_description = _('Movimento')

site.register(ConversioneDettaglioMovimentoPrimaNota, ConversioneDettaglioMovimentoPrimaNotaAdmin)
