import os
import json
from os.path import join
from datetime import <PERSON><PERSON><PERSON>

from celery.schedules import crontab

from django.conf.locale.it import formats as it_formats
from django.utils.translation import ugettext_lazy as _
from settings.celery_conf import app as celery_app


def getenv_bool(name, default=False):
    value = os.getenv(name)
    if value is None:
        return default
    if value.lower() in ('yes', 'true'):
        return True
    return False


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_PATH = os.path.realpath(os.path.dirname(__file__))
STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
MEDIA_ROOT = os.path.join(BASE_DIR, 'media/')

LOCALE_PATHS = [
    # os.path.join(MEDIA_ROOT, 'locale'),
    os.path.join(BASE_DIR, 'locale')
]

SECRET_KEY = 'ize-(jw0pheg$x^3-v+$2h5@q3x=1q6pi5(va4qzgevr(1io4i'

ALLOWED_HOSTS = ['*']

BASE_ADMIN_ALLOWED_NETWORKS = ['0.0.0.0/0']

# IMPOSTAZIONI DI ROSETTA
ROSETTA_MESSAGES_PER_PAGE = 30
DEEPL_AUTH_KEY = os.getenv('DEEPL_AUTH_KEY', 'c0efac74-0330-4d6f-81de-14a5e180fe2e:fx')
ROSETTA_ENABLE_TRANSLATION_SUGGESTIONS = True

DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000
DATA_UPLOAD_MAX_MEMORY_SIZE = 9999999

INSTALLED_APPS = [
    'common.baseweb',
    'modeltranslation',
    'preferences',
    'controlcenter',
    'rangefilter',
    'docxtpl',
    'openpyxl',
    'jquery',
    'mptt',
    'djmoney',
    'djmoney.contrib.exchange',
    'formtools',
    'graphos',
    'django_mptt_admin',
    'admin_adv_search_builder',
    'django_admin_multiple_choice_list_filter',
    'import_export',
    'webodt',
    'secretary',
    'rosetta',
    'xmltodict',
    'advanced_filters',
    'rest_framework',
    'rest_framework.authtoken',
    'location_field.apps.DefaultConfig',
    'common.health',
    'common.metrics',
    'common.allegati',
    'common.authentication',
    'common.anagraficabase',
    'common.immobilizzazioni',
    'common.scadenziario',
    'common.stampe',
    'common.datefilter',
    'jsi18n',
    # CYRENAEUS
    'cyrenaeus.apps.CyrenaeusConfig',
    'cyrenaeus.case',
    'cyrenaeus.inventario',
    'cyrenaeus.immobili',
    'cyrenaeus.contratti',
    'cyrenaeus.personale',
    # MATTHAEUS
    'matthaeus.apps.MatthaeusConfig',
    'matthaeus.anagrafica',
    'matthaeus.bilanci',
    'matthaeus.movimenti',
    'matthaeus.partitasemplice',
    'matthaeus.ciclopassivo',
    'matthaeus.cicloattivo',
    # SAULO
    'saulo.apps.SauloConfig',
    'saulo.archivio',
    # MARTINUS
    'martinus.apps.MartinusConfig',
    'martinus.benefattori',
    'martinus.destinazioni',
    'martinus.adesioni',
    'martinus.donazioni',
    # PETRUS
    'petrus.apps.PetrusConfig',
    'petrus.spese',
    # PUBLIUS
    'publius.apps.PubliusConfig',
    'publius.case_publius',
    'publius.persone',
    'suit',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'django.contrib.admin',
]

# Application definition

ROOT_URLCONF = 'settings.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.i18n',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

AUTH_USER_MODEL = 'authentication.UtenteMagister'

WSGI_APPLICATION = 'settings.wsgi.application'

X_FRAME_OPTIONS = 'SAMEORIGIN'

# Database
# https://docs.djangoproject.com/en/1.9/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': os.getenv('POSTGRES_DB', 'postgres'),
        'USER': os.getenv('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD', 'postgres'),
        'HOST': os.getenv('POSTGRES_HOST', 'db'),
        'PORT': os.getenv('POSTGRES_PORT', '5432'),
    },
}

WEBODT_CONVERTER = 'webodt.converters.openoffice.OpenOfficeODFConverter'
WEBODT_TEMPLATE_PATH = join(BASE_DIR, 'common/templates')

LOCATION_FIELD = {
    'map.provider': 'openstreetmap',
    'search.provider': 'nominatim',
    'map.zoom': 18,
}

CONTROLCENTER_DASHBOARDS = (
    'cyrenaeus.dashboards.CyrenaeusHomepageDashboard',
    'matthaeus.dashboards.MatthaeusDashboard',
    'publius.dashboards.StatisticheDemograficheDashboard',
    'publius.dashboards.PubliusHomepageDashboard',
    
)
CONTROLCENTER_CHARTIST_COLORS = 'material'

# Password validation
# https://docs.djangoproject.com/en/1.9/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/1.9/topics/i18n/

LANGUAGES = [
    ('it', _('Italiano')),
    ('en', _('Inglese')),
    ('es', _('Spagnolo')),
    ('fr', _('Francese')),
    ('pt', _('Portoghese')),
]

CURRENCIES = (
    'EUR', 'ARS', 'AOA', 'BOB', 'BRL', 'CAD', 'CHF', 'GBP', 'IDR', 'ILS', 'INR', 'KRW', 'MAD', 'NGN',
    'PEN', 'PHP', 'PLN', 'USD', 'VND',
)

CURRENCY_CHOICES = [
    ('ARS', 'ARS'),
    ('AOA', 'AOA'),
    ('BOB', 'BOB'),
    ('BRL', 'R$'),
    ('CAD', '$CAN'),
    ('CHF', 'CHF'),
    ('EUR', '€'),
    ('GBP', '£'),
    ('IDR', 'IDR'),
    ('ILS', 'ILS'),
    ('INR', 'INR'),
    ('KRW', 'KRW'),
    ('MAD', 'MAD'),
    ('NGN', 'NGN'),
    ('PEN', 'PEN'),
    ('PHP', 'PHP'),
    ('PLN', 'PLN'),
    ('USD', '$'),
    ('VND', 'VND'),
]

VALUTA_DEFAULT = os.getenv('VALUTA_DEFAULT', 'EUR')

OPEN_EXCHANGE_RATES_APP_ID = '35f55763b2174996a31f546468088631'

prefix_default_language = False
LANGUAGE_CODE = 'it'

TIME_ZONE = 'Europe/Rome'
USE_TZ = False

it_formats.DATE_FORMAT = 'd/m/Y'
it_formats.DATETIME_FORMAT = 'd/m/Y - H:i'

TIME_INPUT_FORMATS = [
    '%H:%M:%S',     # '14:30:59'
    '%H:%M:%S.%f',  # '14:30:59.000200'
    '%H:%M',        # '14:30'
    # CUSTOM
    '%H.%M',
    '%H',
    '%H %M',
]

USE_I18N = True
USE_L10N = True
DECIMAL_SEPARATOR = ','
NUMBER_GROUPING = 3
THOUSAND_SEPARATOR = '.'
USE_THOUSAND_SEPARATOR = True

MEDIA_URL = '/media/'
STATIC_URL = '/static/'

FILE_UPLOAD_MAX_MEMORY_SIZE_MB = 100
FILE_UPLOAD_MAX_MEMORY_SIZE = FILE_UPLOAD_MAX_MEMORY_SIZE_MB * 1024 * 1024

SITE_ID = 1

# DJANGO SCHEDULE
FIRST_DAY_OF_WEEK = 1

# S3 Media
AWS_S3_FILE_OVERWRITE = False
AWS_AUTO_CREATE_BUCKET = True

AWS_ACCESS_KEY_ID = os.getenv('MINIO_ACCESS_KEY', 'admin')
AWS_S3_ENDPOINT_URL = os.getenv('MINIO_URL', 'http://localhost:9000')
AWS_SECRET_ACCESS_KEY = os.getenv('MINIO_SECRET_KEY', 'admin123')
# AWS_S3_CUSTOM_DOMAIN = os.getenv('MINIO_DOMAIN', 'localhost')

AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'media')
AWS_QUERYSTRING_EXPIRE = int(os.getenv('AWS_QUERYSTRING_EXPIRE', '3600'))

DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

# Applications
VERSION = os.getenv('VERSION', 'dev')

MATTHAEUS_ENABLED = getenv_bool('MATTHAEUS_ENABLED', False)
CYRENAEUS_ENABLED = getenv_bool('CYRENAEUS_ENABLED', False)
SAULO_ENABLED = getenv_bool('SAULO_ENABLED', False)
PUBLIUS_ENABLED = getenv_bool('PUBLIUS_ENABLED', False)
MARTINUS_ENABLED = getenv_bool('MARTINUS_ENABLED', False)
PETRUS_ENABLED = getenv_bool('PETRUS_ENABLED', False)
EGO_ENABLED = getenv_bool('EGO_ENABLED', False)
DONUM_ENABLED = getenv_bool('DONUM_ENABLED', False)

CYRENAEUS_SITE_HEADER = os.getenv(
    'CYRENAEUS_SITE_HEADER',
    'MAGISTER - Gestione Contabile - Ver. {}'.format(VERSION)
)
MATTHAEUS_SITE_HEADER = os.getenv(
    'MATTHAEUS_SITE_HEADER',
    'MAGISTER - Gestione Contabile - Ver. {}'.format(VERSION)
)
SAULO_SITE_HEADER = os.getenv(
    'SAULO_SITE_HEADER',
    'MAGISTER - Gestione Documentale - Ver. {}'.format(VERSION)
)
PUBLIUS_SITE_HEADER = os.getenv(
    'PUBLIUS_SITE_HEADER',
    'MAGISTER - Gestione Personale - Ver. {}'.format(VERSION)
)
MARTINUS_SITE_HEADER = os.getenv(
    'MARTINUS_SITE_HEADER',
    'MAGISTER - Gestione Donazioni - Ver. {}'.format(VERSION)
)
PETRUS_SITE_HEADER = os.getenv(
    'PETRUS_SITE_HEADER',
    'MAGISTER - Gestione Risorse Umane - Ver. {}'.format(VERSION)
)

# Valori possibili: m/f
GENERE_CONGREGAZIONE = os.getenv('CONGREGATION_GENDER', 'm')

if GENERE_CONGREGAZIONE == 'm':
    CONGREGAZIONE_MASCHILE = True
else:
    CONGREGAZIONE_MASCHILE = False
CONGREGAZIONE_FEMMINILE = not CONGREGAZIONE_MASCHILE

# Valori possibili: 2/3/4
NUMERO_LIVELLI_PIANO_DEI_CONTI = int(os.getenv('MATTHAEUS_CHART_OF_ACCOUNTS_LEVELS', '4'))

REST_FRAMEWORK = {
    'DATETIME_FORMAT': "%Y-%m-%d %H:%M:%S",
    'DATE_FORMAT': "%Y-%m-%d",
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES':(
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 
        'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
}

AUTHENTICATION_BACKENDS = (
    'common.authentication.backends.MagisterBackend',
)

# Celery
def get_celery_broker_url():
    password = os.environ.get('REDIS_PASSWORD', 'changeme')
    if password:
        password = ':{}@'.format(password)
    return 'redis://{password}{host}:{port}/{db}'.format(
        password=password,
        host=os.environ.get('REDIS_HOST', 'localhost'),
        port=os.environ.get('REDIS_PORT', 6379),
        db=os.environ.get('REDIS_DB', 0),
    )

# ######################    Celery   #################################
CELERY_BROKER_URL = get_celery_broker_url()
result_backend = CELERY_BROKER_URL
# imports = (
#     'common.authentication.tasks',
# )

CELERY_IMPORTS = (
    'common.authentication.tasks',
    'matthaeus.tasks',
    'publius.tasks',
    'martinus.tasks',
)

CELERY_RESULT_EXPIRES = timedelta(days=10)
CELERY_TIMEZONE = 'Europe/Rome'

CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_EVENT_SERIALIZER = 'json'
CELERY_TASK_SOFT_TIME_LIMIT = 600

DAILY_CRONTAB = json.loads(os.environ.get('DAILY_CRONTAB', '{"minute": 10, "hour": 2}'))
FIVE_MIN_CRONTAB = json.loads(os.environ.get('FIVE_MIN_CRONTAB', '{"minute": "*/5", "hour": "8-20"}'))

AGGIORNAMENTO_STATISTICHE_CRONTAB = json.loads(
    os.environ.get('MATTHAEUS_STATS_UPDATE_CRONTAB', '{"minute": 0, "hour": "6,22"}')
)
AGGIORNAMENTO_BILANCIO_CRONTAB = json.loads(
    # os.environ.get('MATTHAEUS_BALANCE_UPDATE_CRONTAB', '{"minute": "*/15", "hour": "8-20"}')
    os.environ.get('MATTHAEUS_BALANCE_UPDATE_CRONTAB', '{"minute": 0, "hour": "4,21"}')
)
AGGIORNAMENTO_CDC_CRONTAB = json.loads(
    os.environ.get('MATTHAEUS_CDC_UPDATE_CRONTAB', '{"minute": 0, "hour": "5,23"}')
)

CELERY_BEAT_SCHEDULE = dict(
    aggiorna_statistiche_matthaeus=dict(
        task='matthaeus.tasks.aggiorna_statistiche_matthaeus',
        schedule=crontab(**AGGIORNAMENTO_STATISTICHE_CRONTAB),
    ),
    aggiorna_bilancio_matthaeus=dict(
        task='matthaeus.tasks.aggiorna_bilancio_matthaeus',
        schedule=crontab(**AGGIORNAMENTO_BILANCIO_CRONTAB),
    ),
    aggiorna_centri_di_costo_matthaeus=dict(
        task='matthaeus.tasks.aggiorna_centri_di_costo_matthaeus',
        schedule=crontab(**AGGIORNAMENTO_CDC_CRONTAB),
    ),
)

# TAB E MENU NASCOSTI - indipendentemente dai permessi

# ES: PUBLIUS_TAB_NASCOSTI = os.getenv('PUBLIUS_TAB_NASCOSTI', 'assemblee,consigli_generali')
PUBLIUS_TAB_NASCOSTI = os.getenv('PUBLIUS_HIDDEN_TABS', '')
PUBLIUS_ELENCO_TAB_NASCOSTI = PUBLIUS_TAB_NASCOSTI.split(',')

# ES: PUBLIUS_MENU_NASCOSTI = os.getenv('PUBLIUS_MENU_NASCOSTI', 'persone.familiare')
PUBLIUS_MENU_NASCOSTI = os.getenv('PUBLIUS_HIDDEN_MENUS', '')
PUBLIUS_ELENCO_MENU_NASCOSTI = PUBLIUS_MENU_NASCOSTI.split(',')
