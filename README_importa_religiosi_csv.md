# Command per Importazione Religiosi da CSV

## Descrizione
Il command `importa_religiosi_csv` permette di importare o aggiornare i dati del modello `Religioso` da un file CSV con intestazioni di colonna.

## Utilizzo

### Sintassi base
```bash
python manage.py importa_religiosi_csv percorso/del/file.csv
```

### Opzioni disponibili

- `--dry-run`: Esegue una simulazione senza salvare i dati nel database
- `--encoding`: Specifica l'encoding del file CSV (default: utf-8)
- `--delimiter`: Specifica il delimitatore del CSV (default: ,)

### Esempi di utilizzo

```bash
# Importazione normale
python manage.py importa_religiosi_csv religiosi.csv

# Simulazione senza salvare
python manage.py importa_religiosi_csv religiosi.csv --dry-run

# File con encoding diverso
python manage.py importa_religiosi_csv religiosi.csv --encoding=iso-8859-1

# File con delimitatore diverso (punto e virgola)
python manage.py importa_religiosi_csv religiosi.csv --delimiter=";"
```

## Formato del file CSV

### Campi obbligatori
- `id`: ID del religioso (per aggiornamento) o nuovo ID (per inserimento)
- `casa_attuale`: Nome della casa attuale (obbligatorio solo per nuovi religiosi, deve esistere nel database)

### Campi supportati

#### Campi di testo
- `cognome`, `nome`, `iniziali`, `appellativo`
- `matricola`, `numero_camera`, `marca`
- `luogo_nascita`, `regione_nascita`, `diocesi_nascita`
- `nome_battesimo`, `nome_civile`
- `luogo_battesimo`, `diocesi_battesimo`
- `luogo_cresima`, `diocesi_cresima`
- `padre`, `madre`, `stato_famiglia`, `vescovo_ordinante`
- `indirizzo_residenza`, `cap_residenza`, `luogo_residenza`, `regione_residenza`, `diocesi_residenza`
- `indirizzo_domicilio`, `luogo_domicilio`
- `telefono_1`, `telefono_2`, `fax`, `cellulare`
- `email`, `email_2`, `contatti`, `patente`
- `carattere`, `attitudini`, `destinazioni_incarichi`, `annotazioni`
- `codice_fiscale`, `notaio`
- `ora_morte`, `luogo_morte`, `regione_morte`, `causa_morte`, `luogo_sepoltura`
- `necrologio`, `testimonianze`, `vita`
- `medico_curante`, `tessera_sanitaria`, `gruppo_sanguigno`
- `esenzione`, `percentuale`, `codice_esenzione`, `status_pensione`, `codice_inps`
- `luogo_uscita`, `causa_uscita`, `motivazione_uscita`
- `notizie_dopo_uscita`, `note_uscita`, `indirizzo_uscita`, `note_riservate`
- `elettorato`

#### Campi numerici
- `numero_congregazione`, `numero_decesso`, `numero_uscita`

#### Campi booleani (true/false, 1/0, si/no)
- `pensione`, `in_vita`

#### Campi data (formati supportati: YYYY-MM-DD, DD/MM/YYYY, DD-MM-YYYY, YYYY/MM/DD)
- `data_nascita`, `data_battesimo`, `data_cresima`, `data_residenza`
- `data_domicilio`, `data_testamento`, `data_morte`
- `data_entrata`, `data_noviziato`, `data_professione_temporanea`
- `data_professione_permanente`, `data_25`, `data_50`, `data_uscita`

#### Campi ForeignKey (devono corrispondere ai nomi nel database)
- `status`: Nome dello status
- `status_religioso`: Nome della voce curriculum
- `posizione_canonica`: Nome della posizione canonica
- `gruppo_appartenenza`: Nome del gruppo di appartenenza
- `provincia_nascita`, `provincia_residenza`, `provincia_morte`: Nome della provincia
- `nazione_nascita`, `cittadinanza`, `nazionalita`, `nazione_battesimo`, `nazione_residenza`, `nazione_morte`: Nome dello stato/nazione
- `provincia_religiosa`, `provincia_giuridica`: Nome della provincia religiosa
- `regione_religiosa`: Nome della regione religiosa
- `tipo_domicilio`: Nome del tipo domicilio
- `comunita_morte`, `casa_residenza`: Nome della casa
- `incarico`: Nome dell'incarico
- `conto`: Codice del piano dei conti

## Comportamento del command

1. **Aggiornamento vs Inserimento**: Se l'ID esiste già nel database, il record viene aggiornato. Altrimenti viene creato un nuovo record.

2. **Gestione errori**: Gli errori vengono registrati ma non interrompono l'elaborazione degli altri record.

3. **Validazione**: I campi ForeignKey vengono validati contro i dati esistenti nel database.

4. **Performance**: Il command utilizza cache per ottimizzare le query sui campi ForeignKey.

5. **Transazioni**: Ogni record viene elaborato in una transazione separata per evitare rollback completi.

## Output del command

Il command fornisce un output dettagliato che include:
- Stato di elaborazione per ogni riga
- Avvisi per valori non trovati o formati non validi
- Riepilogo finale con conteggi di record creati, aggiornati ed errori

## Note importanti

- Assicurarsi che tutti i record referenziati nei campi ForeignKey esistano già nel database
- Utilizzare sempre l'opzione `--dry-run` per testare l'importazione prima di eseguirla realmente
- I campi obbligatori del modello devono essere presenti nel CSV o già esistenti nel record da aggiornare
- Le date devono essere in uno dei formati supportati
- I valori booleani accettano vari formati (true/false, 1/0, si/no, yes/no)

## Esempio pratico

### File CSV di esempio (esempio_religiosi.csv)
```csv
id,cognome,nome,telefono_1,email,data_nascita,luogo_nascita,codice_fiscale,in_vita
1,Rossi,Mario,06-12345678,<EMAIL>,1980-05-15,Roma,****************,true
2,Bianchi,Giuseppe,06-87654321,<EMAIL>,1975-03-20,Milano,****************,true
```

### Comando di test
```bash
# Prima esegui sempre un test in modalità dry-run
python manage.py importa_religiosi_csv esempio_religiosi.csv --dry-run

# Se tutto è corretto, esegui l'importazione reale
python manage.py importa_religiosi_csv esempio_religiosi.csv
```

### Output di esempio
```
MODALITÀ DRY-RUN: Nessun dato verrà salvato
Riga 2: Religioso 1 creato - Rossi Mario
Riga 3: Religioso 2 creato - Bianchi Giuseppe

==================================================
RIEPILOGO IMPORTAZIONE:
Religiosi creati: 2
Religiosi aggiornati: 0
Errori: 0
Totale processati: 2

NOTA: Modalità dry-run attiva - nessun dato è stato salvato
```
