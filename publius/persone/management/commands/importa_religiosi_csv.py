import csv
import os
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.core.exceptions import ValidationError

from publius.persone.models import (
    <PERSON>li<PERSON>so, CasaPublius, Status, VoceCurriculum, PosizioneCanonica,
    GruppoAppartenenza, Provincia, Stato, ProvinciaReligiosa, RegioneReligiosa,
    TipoDomicilio, Incarico
)
from common.anagraficabase.models import PianoDeiConti


class Command(BaseCommand):
    help = 'Importa dati del modello Religiosi da un file CSV'

    def add_arguments(self, parser):
        parser.add_argument(
            'csv_file',
            type=str,
            help='Percorso del file CSV da importare'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Esegue una simulazione senza salvare i dati'
        )
        parser.add_argument(
            '--encoding',
            type=str,
            default='utf-8',
            help='Encoding del file CSV (default: utf-8)'
        )
        parser.add_argument(
            '--delimiter',
            type=str,
            default=',',
            help='Delimitatore del CSV (default: ,)'
        )

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        dry_run = options['dry_run']
        encoding = options['encoding']
        delimiter = options['delimiter']

        if not os.path.exists(csv_file):
            raise CommandError(f'Il file {csv_file} non esiste')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('MODALITÀ DRY-RUN: Nessun dato verrà salvato')
            )

        try:
            with open(csv_file, 'r', encoding=encoding) as file:
                reader = csv.DictReader(file, delimiter=delimiter)
                self.importa_religiosi(reader, dry_run)
        except Exception as e:
            raise CommandError(f'Errore durante la lettura del file: {e}')

    def importa_religiosi(self, reader, dry_run):
        """Importa i dati dei religiosi dal CSV"""
        religiosi_creati = 0
        religiosi_aggiornati = 0
        errori = 0

        # Mappatura dei campi ForeignKey per ottimizzare le query
        self.cache_foreign_keys()

        for row_num, row in enumerate(reader, start=2):  # Start=2 perché la riga 1 è l'header
            try:
                with transaction.atomic():
                    if dry_run:
                        # In modalità dry-run, usa un savepoint per rollback
                        sid = transaction.savepoint()

                    religioso_id = row.get('id')
                    if not religioso_id:
                        self.stdout.write(
                            self.style.ERROR(f'Riga {row_num}: ID mancante, saltata')
                        )
                        errori += 1
                        continue

                    # Cerca se il religioso esiste già
                    try:
                        religioso = Religioso.objects.get(id=religioso_id)
                        azione = 'aggiornato'
                        religiosi_aggiornati += 1
                    except Religioso.DoesNotExist:
                        religioso = Religioso()
                        religioso.id = religioso_id
                        azione = 'creato'
                        religiosi_creati += 1

                    # Aggiorna i campi del religioso
                    self.aggiorna_campi_religioso(religioso, row, row_num)

                    if not dry_run:
                        religioso.save()
                    else:
                        # Rollback in modalità dry-run
                        transaction.savepoint_rollback(sid)

                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Riga {row_num}: Religioso {religioso_id} {azione} - '
                            f'{religioso.cognome} {religioso.nome}'
                        )
                    )

            except Exception as e:
                errori += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'Riga {row_num}: Errore durante l\'importazione: {e}'
                    )
                )

        # Riepilogo finale
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'RIEPILOGO IMPORTAZIONE:')
        self.stdout.write(f'Religiosi creati: {religiosi_creati}')
        self.stdout.write(f'Religiosi aggiornati: {religiosi_aggiornati}')
        self.stdout.write(f'Errori: {errori}')
        self.stdout.write(f'Totale processati: {religiosi_creati + religiosi_aggiornati}')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nNOTA: Modalità dry-run attiva - nessun dato è stato salvato')
            )

    def cache_foreign_keys(self):
        """Crea cache per i campi ForeignKey per ottimizzare le performance"""
        self.cache_case = {casa.nome: casa for casa in CasaPublius.objects.all()}
        self.cache_status = {status.nome: status for status in Status.objects.all()}
        self.cache_voce_curriculum = {voce.nome: voce for voce in VoceCurriculum.objects.all()}
        self.cache_posizione_canonica = {pos.nome: pos for pos in PosizioneCanonica.objects.all()}
        self.cache_gruppo_appartenenza = {gruppo.nome: gruppo for gruppo in GruppoAppartenenza.objects.all()}
        self.cache_province = {prov.descrizione: prov for prov in Provincia.objects.all()}
        self.cache_stati = {stato.descrizione: stato for stato in Stato.objects.all()}
        self.cache_province_religiose = {prov.nome: prov for prov in ProvinciaReligiosa.objects.all()}
        self.cache_regioni_religiose = {reg.nome: reg for reg in RegioneReligiosa.objects.all()}
        self.cache_tipo_domicilio = {tipo.nome: tipo for tipo in TipoDomicilio.objects.all()}
        self.cache_incarichi = {inc.nome: inc for inc in Incarico.objects.all()}
        self.cache_conti = {conto.codice: conto for conto in PianoDeiConti.objects.all()}

    def aggiorna_campi_religioso(self, religioso, row, row_num):
        """Aggiorna i campi del religioso con i dati dal CSV"""

        # Campi obbligatori
        if 'casa_attuale' in row and row['casa_attuale']:
            casa = self.cache_case.get(row['casa_attuale'])
            if casa:
                religioso.casa_attuale = casa
            else:
                raise ValueError(f"Casa '{row['casa_attuale']}' non trovata")
        elif not religioso.casa_attuale_id:
            # Per nuovi religiosi, il campo casa_attuale è obbligatorio
            if not religioso.id:
                raise ValueError("Campo 'casa_attuale' obbligatorio mancante per nuovo religioso")

        # Campi di testo semplici
        campi_testo = [
            'cognome', 'nome', 'iniziali', 'appellativo', 'matricola', 'numero_camera',
            'marca', 'luogo_nascita', 'regione_nascita', 'diocesi_nascita', 'nome_battesimo',
            'nome_civile', 'luogo_battesimo', 'diocesi_battesimo', 'luogo_cresima',
            'diocesi_cresima', 'padre', 'madre', 'stato_famiglia', 'vescovo_ordinante',
            'indirizzo_residenza', 'cap_residenza', 'luogo_residenza', 'regione_residenza',
            'diocesi_residenza', 'indirizzo_domicilio', 'luogo_domicilio', 'telefono_1',
            'telefono_2', 'fax', 'cellulare', 'email', 'email_2', 'contatti', 'patente',
            'carattere', 'attitudini', 'destinazioni_incarichi', 'annotazioni',
            'codice_fiscale', 'ora_morte', 'luogo_morte', 'regione_morte', 'causa_morte',
            'luogo_sepoltura', 'necrologio', 'testimonianze', 'vita', 'medico_curante',
            'tessera_sanitaria', 'gruppo_sanguigno', 'esenzione', 'percentuale',
            'codice_esenzione', 'status_pensione', 'codice_inps', 'luogo_uscita',
            'causa_uscita', 'motivazione_uscita', 'notizie_dopo_uscita', 'note_uscita',
            'indirizzo_uscita', 'note_riservate', 'elettorato'
        ]

        for campo in campi_testo:
            if campo in row and row[campo]:
                setattr(religioso, campo, row[campo])

        # Campi numerici
        campi_numerici = ['numero_congregazione', 'numero_decesso', 'numero_uscita']
        for campo in campi_numerici:
            if campo in row and row[campo]:
                try:
                    setattr(religioso, campo, int(row[campo]))
                except ValueError:
                    self.stdout.write(
                        self.style.WARNING(f'Riga {row_num}: Valore non numerico per {campo}: {row[campo]}')
                    )

        # Campi booleani
        if 'pensione' in row and row['pensione']:
            religioso.pensione = row['pensione'].lower() in ['true', '1', 'si', 'sì', 'yes']
        if 'in_vita' in row and row['in_vita']:
            religioso.in_vita = row['in_vita'].lower() in ['true', '1', 'si', 'sì', 'yes']

        # Campi data
        campi_data = [
            'data_nascita', 'data_battesimo', 'data_cresima', 'data_residenza',
            'data_domicilio', 'data_testamento', 'data_morte', 'data_entrata',
            'data_noviziato', 'data_professione_temporanea', 'data_professione_permanente',
            'data_25', 'data_50', 'data_uscita'
        ]

        for campo in campi_data:
            if campo in row and row[campo]:
                try:
                    # Prova diversi formati di data
                    data_str = row[campo].strip()
                    if data_str:
                        data = self.parse_data(data_str)
                        setattr(religioso, campo, data)
                except ValueError as e:
                    self.stdout.write(
                        self.style.WARNING(f'Riga {row_num}: Formato data non valido per {campo}: {row[campo]} - {e}')
                    )

        # Campi ForeignKey
        self.aggiorna_foreign_keys(religioso, row, row_num)

    def parse_data(self, data_str):
        """Prova a parsare una data in diversi formati"""
        formati = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d']

        for formato in formati:
            try:
                return datetime.strptime(data_str, formato).date()
            except ValueError:
                continue

        raise ValueError(f"Formato data non riconosciuto: {data_str}")

    def aggiorna_foreign_keys(self, religioso, row, row_num):
        """Aggiorna i campi ForeignKey del religioso"""

        # Status
        if 'status' in row and row['status']:
            status = self.cache_status.get(row['status'])
            if status:
                religioso.status = status
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Status "{row["status"]}" non trovato')
                )

        # Status religioso (VoceCurriculum)
        if 'status_religioso' in row and row['status_religioso']:
            voce = self.cache_voce_curriculum.get(row['status_religioso'])
            if voce:
                religioso.status_religioso = voce
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Status religioso "{row["status_religioso"]}" non trovato')
                )

        # Posizione canonica
        if 'posizione_canonica' in row and row['posizione_canonica']:
            posizione = self.cache_posizione_canonica.get(row['posizione_canonica'])
            if posizione:
                religioso.posizione_canonica = posizione
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Posizione canonica "{row["posizione_canonica"]}" non trovata')
                )

        # Gruppo appartenenza
        if 'gruppo_appartenenza' in row and row['gruppo_appartenenza']:
            gruppo = self.cache_gruppo_appartenenza.get(row['gruppo_appartenenza'])
            if gruppo:
                religioso.gruppo_appartenenza = gruppo
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Gruppo appartenenza "{row["gruppo_appartenenza"]}" non trovato')
                )

        # Province
        province_fields = [
            ('provincia_nascita', 'cache_province'),
            ('provincia_residenza', 'cache_province'),
            ('provincia_morte', 'cache_province')
        ]

        for field_name, cache_name in province_fields:
            if field_name in row and row[field_name]:
                provincia = getattr(self, cache_name).get(row[field_name])
                if provincia:
                    setattr(religioso, field_name, provincia)
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Riga {row_num}: Provincia "{row[field_name]}" non trovata per {field_name}')
                    )

        # Stati/Nazioni
        stati_fields = [
            ('nazione_nascita', 'cache_stati'),
            ('cittadinanza', 'cache_stati'),
            ('nazionalita', 'cache_stati'),
            ('nazione_battesimo', 'cache_stati'),
            ('nazione_residenza', 'cache_stati'),
            ('nazione_morte', 'cache_stati')
        ]

        for field_name, cache_name in stati_fields:
            if field_name in row and row[field_name]:
                stato = getattr(self, cache_name).get(row[field_name])
                if stato:
                    setattr(religioso, field_name, stato)
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Riga {row_num}: Stato "{row[field_name]}" non trovato per {field_name}')
                    )

        # Provincia religiosa
        if 'provincia_religiosa' in row and row['provincia_religiosa']:
            prov_rel = self.cache_province_religiose.get(row['provincia_religiosa'])
            if prov_rel:
                religioso.provincia_religiosa = prov_rel
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Provincia religiosa "{row["provincia_religiosa"]}" non trovata')
                )

        # Regione religiosa
        if 'regione_religiosa' in row and row['regione_religiosa']:
            reg_rel = self.cache_regioni_religiose.get(row['regione_religiosa'])
            if reg_rel:
                religioso.regione_religiosa = reg_rel
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Regione religiosa "{row["regione_religiosa"]}" non trovata')
                )

        # Provincia giuridica (usa la stessa cache delle province religiose)
        if 'provincia_giuridica' in row and row['provincia_giuridica']:
            prov_giur = self.cache_province_religiose.get(row['provincia_giuridica'])
            if prov_giur:
                religioso.provincia_giuridica = prov_giur
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Provincia giuridica "{row["provincia_giuridica"]}" non trovata')
                )

        # Tipo domicilio
        if 'tipo_domicilio' in row and row['tipo_domicilio']:
            tipo_dom = self.cache_tipo_domicilio.get(row['tipo_domicilio'])
            if tipo_dom:
                religioso.tipo_domicilio = tipo_dom
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Tipo domicilio "{row["tipo_domicilio"]}" non trovato')
                )

        # Comunità morte (usa la stessa cache delle case)
        if 'comunita_morte' in row and row['comunita_morte']:
            comunita = self.cache_case.get(row['comunita_morte'])
            if comunita:
                religioso.comunita_morte = comunita
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Comunità morte "{row["comunita_morte"]}" non trovata')
                )

        # Casa residenza (usa la stessa cache delle case)
        if 'casa_residenza' in row and row['casa_residenza']:
            casa_res = self.cache_case.get(row['casa_residenza'])
            if casa_res:
                religioso.casa_residenza = casa_res
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Casa residenza "{row["casa_residenza"]}" non trovata')
                )

        # Incarico
        if 'incarico' in row and row['incarico']:
            incarico = self.cache_incarichi.get(row['incarico'])
            if incarico:
                religioso.incarico = incarico
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Incarico "{row["incarico"]}" non trovato')
                )

        # Conto (Piano dei Conti)
        if 'conto' in row and row['conto']:
            conto = self.cache_conti.get(row['conto'])
            if conto:
                religioso.conto = conto
            else:
                self.stdout.write(
                    self.style.WARNING(f'Riga {row_num}: Conto "{row["conto"]}" non trovato')
                )
